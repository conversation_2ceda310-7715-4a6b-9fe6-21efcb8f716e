<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <parent>
        <groupId>com.ruijing.order</groupId>
        <artifactId>order-base-dependency</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modules>
        <module>store-order-api</module>
        <module>store-order-server</module>
        <module>order-base-client</module>
        <module>order-base-dependency</module>
    </modules>

    <groupId>com.ruijing.store</groupId>
    <artifactId>store-order-service</artifactId>
    <version>1.0.6-SNAPSHOT</version>
    <name>store-order-service</name>
    <description>store-order-service project for Spring Boot</description>

    <properties>
        <testable.version>0.6.6</testable.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-api-info-bom</artifactId>
                <version>${msharp.version}</version>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.ruijing.plugin</groupId>
                <artifactId>api-package-plugin</artifactId>
                <version>1.1.0-SNAPSHOT</version>
                <configuration>
                    <!--项目名称，必填，需要唯一，未在数据库中的不允许使用-->
                    <appkey>store-order-service</appkey>
                    <!--项目版本号，不填默认"1.0.0"-->
                    <version>1.0.6-SNAPSHOT</version>
                    <!--是否跳过swagger生成，不填默认false-->
                    <skipSwaggerGeneration>true</skipSwaggerGeneration>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--单测覆盖 maven插件-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <configuration>
                    <excludes>
                        <exclude>com/ruijing/store/**/*DTO.class</exclude>
                        <exclude>com/ruijing/store/**/*DO.class</exclude>
                        <exclude>com/ruijing/store/**/*BO.class</exclude>
                        <exclude>com/ruijing/store/**/*VO.class</exclude>
                        <exclude>com/ruijing/store/**/*Test.class</exclude>
                        <exclude>com/ruijing/store/**/*Request.class</exclude>
                        <exclude>com/ruijing/store/**/*Enum.class</exclude>
                        <exclude>com/ruijing/store/**/*Controller.class</exclude>
                        <exclude>com/ruijing/store/**/*Mapper.class</exclude>
                        <exclude>com/ruijing/store/**/*Client.class</exclude>
                        <exclude>com/ruijing/store/order/constant/*Constant.class</exclude>
                        <exclude>com/ruijing/store/warehouse/message/bean/OrderBean.class</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <argLine>@{argLine} -javaagent:${settings.localRepository}/com/alibaba/testable/testable-agent/${testable.version}/testable-agent-${testable.version}.jar</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>maven-releases</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
        </repository>

    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>

        </snapshotRepository>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
