package com.ruijing.order.exception;

import com.ruijing.order.enums.IBaseTemplateEnum;
import com.ruijing.order.utils.LocaleUtils;
import org.assertj.core.util.Arrays;

import java.util.List;

/**
 * @author: li<PERSON><PERSON>
 * @createTime: 2023-05-15 17:28
 * @description: 业务拦截异常，用于表示例如校验输入、状态异常等接口调用前置校验抛出的异常，不需要提醒
 **/
public class BusinessInterceptException extends RuntimeException implements ILocaleException {

    private static final long serialVersionUID = -266343027805889887L;

    private IBaseTemplateEnum templateEnum;

    private List<Object> templateParams;

    public BusinessInterceptException() {
        super();
    }

    public BusinessInterceptException(String message) {
        super(message);
    }

    public BusinessInterceptException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessInterceptException(Throwable cause) {
        super(cause);
    }

    public BusinessInterceptException(IBaseTemplateEnum templateEnum, Object... templateParams) {
        super(LocaleUtils.format(templateEnum.getTemplateContent(), Arrays.asList(templateParams)));
        this.templateEnum = templateEnum;
        this.templateParams = Arrays.asList(templateParams);
    }

    @Override
    public List<Object> getTemplateParams() {
        return templateParams;
    }

    @Override
    public IBaseTemplateEnum getTemplateEnum() {
        return templateEnum;
    }

}
