package com.ruijing.order.exception;

/**
 * @description: 调用Rpc返回不正常时的异常
 * @author: zhuk
 * @create: 2019-09-26 14:34
 **/
public class CallRpcException extends RuntimeException {

    /**
     *  无参构造函数
     * */
    public CallRpcException(){
        super();
    }

    /**
     * 用详细信息指定一个异常
     */
    public CallRpcException(String message){
        super(message);
    }

    /**
     * 用指定的详细信息和原因构造一个新的异常
     */
    public CallRpcException(String message, Throwable cause){
        super(message,cause);
    }

    /**
     * 用指定原因构造一个新的异常
     */
    public CallRpcException(Throwable cause) {
        super(cause);
    }

}
