package com.ruijing.order.aop;

import com.ruijing.base.logger.LogMonitor;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.exception.ICodeException;
import com.ruijing.pearl.annotation.PearlValue;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 记录db操作的日志的 切面
 * @author: zhongyulei
 * @create: 2019/9/30 16:14
 **/
@Aspect
@Component
@Order(10)
@ConditionalOnProperty(name = "order.extension.aop.serviceLog")
public class LoggerAopAspect {

    @PearlValue(key = "order.log.allInfoLog",defaultValue = "false")
    private boolean isAllInfoLog;

    /**
     * 需要打日志的simple类名
     */
    @PearlValue(key = "order.log.className",defaultValue = "[]")
    List<String> logClassNameList;

    /**
     * 需要打日志的方法名
     */
    @PearlValue(key = "order.log.methodName",defaultValue = "[]")
    List<String> logMethodNameList;

    @Pointcut("@annotation(com.ruijing.order.annotation.ServiceLog) || @within(com.ruijing.order.annotation.ServiceLog)")
    private void serviceLogOperation(){}

    /**
     * 日志切面。  枚举配置、或者配置中心配置类名和方法名的 会打印INFO日志
     * @param joinPoint
     * @return
     */
    @Around("serviceLogOperation()")
    private Object rpcServiceLoggerAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        ServiceLog annotation = signature.getMethod().getAnnotation(ServiceLog.class);

        if (annotation == null) {
            annotation = joinPoint.getTarget().getClass().getAnnotation(ServiceLog.class);
        }

        ServiceType logServiceEnum = annotation.serviceType();
        OperationType operationEnum = annotation.operationType();
        // 方法描述
        String methodDescription = annotation.description();

        boolean isAllowInfoLog = logClassNameList.contains(className) || logMethodNameList.contains(methodName) || isAllInfoLog
                || OperationType.WRITE.equals(operationEnum) || Environment.isDevEnv();

        //打印日志 入参
        LogMonitor.Monitor monitor = LogMonitor.openLogMonitor(className,methodName);
        monitor.setCategory(methodDescription);
        Transaction transaction = Cat.newTransaction(className, methodName);
        Object result = null;
        try {
            // 执行业务操作
            monitor.setReq(JsonUtils.toJsonIgnoreNull(joinPoint.getArgs()));
            result = joinPoint.proceed();
            transaction.setSuccessStatus();
            monitor.setSuccessStatus();
        } catch (BusinessInterceptException e){
            transaction.setSuccessStatus();
            // 业务异常，只打logger不打cat
            isAllowInfoLog = true;
            monitor.setStatus(e);
            result = exceptionProcess( logServiceEnum, result, e);
        } catch (Throwable e) {
            catErrorProcess(methodDescription, transaction, e);
            // 其余的异常logger和cat都打
            isAllowInfoLog = true;
            monitor.setStatus(e);
            result = exceptionProcess( logServiceEnum, result, e);
        } finally {
            transaction.complete();
            // 如果允许打印才进行日志打印
            if (isAllowInfoLog) {
                monitor.setResp(JsonUtils.toJsonIgnoreNull(result));
                monitor.complete();
            }
        }
        return result;
    }

    /**
     * 异常捕获后统一处理
     * @param logServiceEnum    业务类型
     * @param result            业务处理结果
     * @param e                 异常
     * @return                  处理结果
     */
    private Object exceptionProcess(ServiceType logServiceEnum, Object result, Throwable e) throws Throwable {
        if(e instanceof ICodeException && ((ICodeException) e).fastFail()){
            throw e;
        }
        if (ServiceType.RPC_SERVICE.equals(logServiceEnum)) {
            result = RemoteResponse.custom().setFailure(e.getMessage()).build();
        } else if (ServiceType.RPC_CLIENT.equals(logServiceEnum) || ServiceType.COMMON_SERVICE.equals(logServiceEnum)) {
            throw e;
        } else if (ServiceType.RPC_SERVICE_PAGE_RES.equals(logServiceEnum)) {
            result = PageableResponse.custom().setFailure(e.getMessage()).build();
        }
        return result;
    }

    private void catErrorProcess(String methodDescription, Transaction transaction, Throwable e){
        transaction.addData(CatUtils.buildStackInfo(methodDescription + "异常", e));
        transaction.setStatus(e);
    }
}
