package com.ruijing.order.filter;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.filter.FilterChain;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.fundamental.remoting.msharp.provider.context.FilterContext;
import com.ruijing.fundamental.remoting.msharp.provider.filter.ProviderFilter;
import com.ruijing.order.enums.CommonExecptionMessageEnum;
import com.ruijing.order.enums.IBaseTemplateEnum;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.exception.ICodeException;
import com.ruijing.order.exception.ILocaleException;
import com.ruijing.pearl.annotation.PearlValue;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/12/4 0004 14:16
 * @Version 1.0
 * @Desc: Order设置为10。这样框架初始化时，大于设定值的Filter会先于该Filter执行，小于的会后于该Filter执行
 */
@Service
@Order(10)
@ConditionalOnProperty(name = "order.extension.simpleFilter")
public class LogFilter implements ProviderFilter {

    /**
     * 是否开启所有日志信息
     */
    @PearlValue(key = "order.log.allInfoLog",defaultValue = "false")
    private boolean isAllInfoLog;

    /**
     * 需要打日志的simple类名
     */
    @PearlValue( key = "order.log.className",defaultValue = "[]")
    private List<String> logClassNameList;

    /**
     * 需要打日志的方法名
     */
    @PearlValue( key = "order.log.methodName",defaultValue = "[]")
    private List<String> logMethodNameList;

    /**
     * 是否走新国际化逻辑  TODO 临时开关
     */
    @Value("${log.filter.locale.enable:false}")
    private Boolean localeEnable;

    @Override
    public void doFilter(FilterContext filterContext, FilterChain filterChain) throws Throwable {
        Method method = filterContext.getMethod();
        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        Class<?> returnType = method.getReturnType();
        Object[] args = filterContext.getArgs();

        Logger logger = LoggerFactory.getLogger(filterContext.getTarget().getClass());
        Transaction transaction = Cat.newTransaction(className, methodName);

        boolean isAllowInfoLog = false;
        if (logClassNameList.contains(className) || logMethodNameList.contains(methodName) || isAllInfoLog
                || Environment.isDevEnv()) {
            isAllowInfoLog = true;
        }

        //打印所有日志信息
        if (isAllowInfoLog) {
            logger.info("进入{}.{} 方法, 入参:{}", className, methodName, JsonUtils.toJson(args));
        }

        try {
            filterChain.doFilter(filterContext);

            //若配置了全日志打印， 则打印结果日志信息
            if (isAllowInfoLog) {
                logger.info("调用后结果{}", JsonUtils.toJson(filterContext.getRetValue()));
            }
            transaction.setSuccess();
        }catch (BusinessInterceptException e){
            // cat设置为成功，为了不打在cat
            transaction.setSuccess();
            // 日志处理为失败
            logger.error("操作失败:" + methodName + "异常，" + "参数{}", JsonUtils.toJson(args), e);
            this.handleError(filterContext, returnType, e);
        } catch (IllegalArgumentException | IllegalStateException | CallRpcException e) {
            //断言异常返回RemoteResponse对象
            transaction.addData(CatUtils.buildStackInfo(methodName + "异常, 参数：" + JsonUtils.toJson(args), e));
            transaction.setStatus(e);
            logger.error("操作失败:" + methodName + "异常，" + "参数{}", JsonUtils.toJson(args), e);
            this.handleError(filterContext, returnType, e);
        }catch (Throwable throwable) {
            //其他异常抛出去
            transaction.addData(CatUtils.buildStackInfo(methodName + "异常, 参数：" + JsonUtils.toJson(args), throwable));
            transaction.setStatus(throwable);
            logger.error("操作失败:" + methodName + "异常，" + "参数{}", JsonUtils.toJson(args), throwable);
            this.handleError(filterContext, returnType, throwable);
        } finally {
            transaction.complete();
        }
    }

    private void handleError(FilterContext filterContext, Class<?> returnType, Throwable e) {
        String localeTemplateCode = StringUtils.EMPTY;
        Map<String, String> localeTemplateParams = New.emptyMap();
        String message = e.getMessage();
        Integer code = RemoteResponse.FAILURE;
        Object data = null;

        final String defautLang = "cnzh";
        String language = RpcContext.getProviderContext().getHeader("v-site-language", defautLang);

        // 非中文才国际化,临时开关 TODO
        if (BooleanUtils.isTrue(localeEnable) && !Objects.equals(language, defautLang) && !(e instanceof ICodeException)) {
            // 默认服务器异常
            localeTemplateCode = getTemplateCode(CommonExecptionMessageEnum.SERVER_ERROR);
            message = CommonExecptionMessageEnum.SERVER_ERROR.getTemplateContent();
            // 国际化异常出处理参数填充
            if (e instanceof ILocaleException) {
                ILocaleException localeException = (ILocaleException) e;
                String fullTemplateCode = getTemplateCode(localeException.getTemplateEnum());
                localeTemplateCode = StringUtils.isNotBlank(fullTemplateCode) ? fullTemplateCode : localeTemplateCode;
                localeTemplateParams = MapUtils.isNotEmpty(getLocaleParams(localeException)) ? getLocaleParams(localeException) : localeTemplateParams;
                message = e.getMessage();
            }
        }
        // ICodeException 手动翻译 不区分是否国际化
        if(e instanceof ICodeException){
            code = ((ICodeException) e).getCode();
            data = ((ICodeException) e).getData();
        }

        if (RemoteResponse.class.equals(returnType)) {
            RemoteResponse<Object> remoteResponse = RemoteResponse.custom()
                    .setFailure(message)
                    .setLocaleTemplateCode(localeTemplateCode)
                    .setLocaleTemplateParams(localeTemplateParams)
                    .setCode(code)
                    .setData(data);
            filterContext.setRetValue(remoteResponse);
        }
        if (PageableResponse.class.equals(returnType)) {
            PageableResponse<Object> pageableResponse = PageableResponse.custom()
                    .setFailure(message)
                    .setLocaleTemplateCode(localeTemplateCode)
                    .setLocaleTemplateParams(localeTemplateParams)
                    .setCode(code)
                    .setData(data);
            filterContext.setRetValue(pageableResponse);
        }
    }

    /**
     * 获取模板Code ,拼接前缀
     *
     * @param templateEnum 模板枚举
     */
    private String getTemplateCode(IBaseTemplateEnum templateEnum) {
        if (Objects.isNull(templateEnum)) {
            return StringUtils.EMPTY;
        }
        return templateEnum.getTemplateCodePrefix() + templateEnum.getTemplateCode();
    }

    /**
     * 获取LocaleException的模板参数
     *
     * @param e 异常对象
     * @return 模板参数Map
     * 定义参数模板的时候约定 参数占位符规定成递增数字形式， {{0}} {{1}}  {{2}}...
     */
    private Map<String, String> getLocaleParams(ILocaleException e) {
        List<Object> params = e.getTemplateParams();
        if (Objects.isNull(params)) {
            return New.emptyMap();
        }
        // 转成Map形式
        Map<String, String> localeParams = New.mapWithCapacity(params.size());
        for (int i = 0; i < params.size(); i++) {
            localeParams.put(String.valueOf(i), String.valueOf(params.get(i)));
        }
        return localeParams;
    }
}
