package com.ruijing.order.enums;

import com.ruijing.order.constant.CommonConstant;

/**
 * <AUTHOR>
 * @Description 通用异常信息枚举
 * @Date: 2024/10/15 17:39
 * templateCode 前缀 : store.common
 * 定义参数模板的时候约定 参数占位符规定成递增数字形式， {{0}} {{1}}  {{2}}...
 **/
public enum CommonExecptionMessageEnum implements IBaseTemplateEnum {

    // 订单不存在
    ORDER_NOT_EXIST("order.not.exist", "订单不存在"),

    // 订单不存在：{订单号}
    ORDER_NO_NOT_EXIST("order.not.exist.with.order.no", "订单不存在：{0}"),

    // 服务器异常
    SERVER_ERROR("server.error", "服务器异常"),

    ;

    /**
     * 模板code
     */
    private final String templateCode;

    /**
     * 模板模板
     */
    private final String templateContent;

    @Override
    public String getTemplateCode() {
        return templateCode;
    }

    /**
     * 获取模板Code前缀
     */
    @Override
    public String getTemplateCodePrefix() {
        return CommonConstant.LOCALE_PREFIX_ORDER_COMMON;
    }

    @Override
    public String getTemplateContent() {
        return templateContent;
    }

    CommonExecptionMessageEnum(String templateCode, String templateContent) {
        this.templateCode = templateCode;
        this.templateContent = templateContent;
    }

    public static CommonExecptionMessageEnum getByTemplateCode(String templateCode) {
        for (CommonExecptionMessageEnum e : CommonExecptionMessageEnum.values()) {
            if (e.getTemplateCode().equals(templateCode)) {
                return e;
            }
        }
        return null;
    }
}
