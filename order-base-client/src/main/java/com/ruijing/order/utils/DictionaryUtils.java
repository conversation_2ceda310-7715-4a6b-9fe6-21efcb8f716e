package com.ruijing.order.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 字典工具类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/15 12:48
 **/
public class DictionaryUtils {

    /**
     * 使用函数从源对象获取指定字典, 当key重复默认覆盖value
     * @param source       元数组对象
     * @param keyFunction   字典 key 函数
     * @param valueFunction 字典 value 函数
     * @param <T>          元对象类型
     * @param <K>          字典 key 类型
     * @param <V>          字典 value 类型
     * @return             字典
     */
    public static <T, K, V> Map<K, V> toMap(List<T> source,
                                            Function<? super T, ? extends K> keyFunction,
                                            Function<? super T, ? extends V> valueFunction) {

        return toMap(source, keyFunction, valueFunction, (oldValue, newValue) -> newValue);
    }

    /**
     * 使用函数从数组对象获取指定字典, 可自定义覆盖策略
     * @param source        元数组对象
     * @param keyFunction    字典 key 函数
     * @param valueFunction  字典 value 函数
     * @param mergeFunction 覆盖策略
     * @param <T>           元对象类型
     * @param <K>           字典 key 类型
     * @param <V>           字典 value 类型
     * @return              字典
     */
    public static <T, K, V> Map<K, V> toMap(List<T> source,
                                            Function<? super T, ? extends K> keyFunction,
                                            Function<? super T, ? extends V> valueFunction,
                                            BinaryOperator<V> mergeFunction) {

        return source.stream().collect(Collectors.toMap(keyFunction, valueFunction, mergeFunction));
    }

    /**
     * 根据数组字段获取字典对象，不允许重复 key
     * @return  字典
     */
    public static <T, K, V> Map<K, V> toMapNonDuplicate(List<T> source,
                                                        Function<? super T, ? extends K> keyFunction,
                                                        Function<? super T, ? extends V> valueFunction) {

        Map<K, V> dictionary = new HashMap<>(source.size());
        for (T t : source) {
            if (dictionary.put(keyFunction.apply(t), valueFunction.apply(t)) != null) {
                throw new IllegalStateException(String.format("Duplicate key %s", keyFunction.apply(t)));
            }
        }
        return dictionary;
    }

    /**
     * 枚举转换字典
     * @param enums         枚举对象
     * @param keyFunction    字典 key 函数
     * @param valueFunction  字典 value 函数
     * @param <T>           元对象类型
     * @param <K>           字典 key 类型
     * @param <V>           字典 value 类型
     * @return              字典
     */
    public static <T extends Enum, K, V> Map<K, V> enumToMap(Class<T> enums,
                                                             Function<? super T, ? extends K> keyFunction,
                                                             Function<? super T, ? extends V> valueFunction) {
        if (enums == null) {
            throw new IllegalStateException("specified enum is empty");
        }
        // 类型检查
        if (!enums.isEnum()) {
            throw new ClassCastException("type error");
        }

        T[] enumConstants = enums.getEnumConstants();
        if (enumConstants.length == 0) {
            throw new IllegalStateException("specified enum is empty");
        }

        List<T> enumList = Arrays.asList(enumConstants);
        // 默认枚举获取字典不允许key重复
        return toMapNonDuplicate(enumList, keyFunction, valueFunction);
    }

    /**
     * 根据数组字段 分组 获取字典对象
     * @param source        源对象
     * @param keyFunction    key函数
     * @param <T>           元对象类型
     * @param <K>           key类型
     * @return              字典
     */
    public static <T, K> Map<K, List<T>> groupBy(List<T> source,
                                                 Function<? super T, ? extends K> keyFunction) {

        return source.stream().collect(Collectors.groupingBy(keyFunction));
    }

    /**
     * 对对象数组形成为对象的属性1与属性2的分组映射
     * @param source 源对象数组
     * @param keyFunction 从获取源对象获取key的函数
     * @param valueFunction 从获取源对象获取value的函数
     * @return 分组后的映射
     * @param <K> key的class
     * @param <V> value的class
     * @param <T> 源对象class
     */
    public static <K, V, T> Map<K, List<V>> groupFieldByKey(List<T> source,
                                                            Function<? super T, ? extends K> keyFunction,
                                                            Function<? super T, ? extends V> valueFunction) {
        return source.stream().collect(Collectors.groupingBy(keyFunction, Collectors.mapping(valueFunction, Collectors.toList())));
    }
}
