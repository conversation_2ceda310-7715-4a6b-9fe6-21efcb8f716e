package com.ruijing.order.utils;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * @description: 时间处理工具
 * @author: zhuk
 * @create: 2019-09-10 14:36
 **/
public class TimeUtils {

    private TimeUtils() {
        throw new IllegalArgumentException(
                "工具类，不支持构造函数创建！");
    }

    private static final Long DAY_MILLISECONDS = 24 * 60 * 60 * 1000L;

    /**
     * 计算两个时间戳相差的天数
     * @param startTime 起始时间
     * @param endTime   截止时间
     * @return apartDay 相差天数
     */
    public static double timeApartDay(long startTime,long endTime){
        long apartTime = endTime - startTime;
        return div(apartTime, DAY_MILLISECONDS, 2);
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入。
     *
     * @param v1
     *            被除数
     * @param v2
     *            除数
     * @param scale
     *            表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
    private static double div(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(String.valueOf(v1));
        BigDecimal b2 = new BigDecimal(String.valueOf(v2));
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取指定日期
     * @param days 正数往后, 负数往前获取
     * @return
     */
    public static Date getSettingDate(long days) {
        LocalDate startLocal = LocalDate.now().plusDays(days);
        Instant instant = startLocal.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取指定日期，精确到时分秒
     * @param days 正数往后, 负数往前获取
     * @return     指定日期，精确到时分秒
     */
    public static Date getSettingTime(long days) {
        LocalDateTime startLocal = LocalDateTime.now().plusDays(days);
        Instant instant = startLocal.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * 获取当天 0点0分的时间
     * @return date
     */
    public static Date getTodayMinTime() {
        LocalDateTime now = LocalDateTime.now();
        return Date.from(LocalDateTime.of(now.toLocalDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }


    public static Date getTodayMaxTime() {
        LocalDateTime now = LocalDateTime.now();
        return Date.from(LocalDateTime.of(now.toLocalDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取本月的第一天的00：00：00
     * @return
     */
    public static Date getFirstDayMinOfMouth(){
        LocalDateTime firstDay = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth())), LocalTime.MIN);
        return Date.from(firstDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
