package com.ruijing.order.utils;

import com.ruijing.fundamental.common.collections.New;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * @description: 集合批处理工具类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/4 15:28
 **/
public class ListUtils {
    // SQL语句in操作的阈值
    private static final int BATCH_SQL_CAPACITY = 500;

    public static int getBatchSQLCapacity() {
        return BATCH_SQL_CAPACITY;
    }

    /**
     * 集合分割, 返回集合的集合, 默认按500个元素一组
     * @param collect 待分割的集合
     * @param <T>
     * @return
     */
    public static <T extends List> List<T> splitCollection(T collect) {
        return splitCollection(BATCH_SQL_CAPACITY, collect);
    }

    /**
     * 指定容量集合分割, 返回集合的集合
     * @param capacity 容量大小
     * @param collect  待分割的集合
     * @param <T>
     * @return
     */
    public static <T extends List> List<T> splitCollection(int capacity, T collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return New.list();
        }

        // 计算数组分割数量
        int count = (collect.size() / capacity) + 1;
        List<T> splitResult = new ArrayList<>(count);

        if (count > 1) {
            int startIndex = 0;
            int endIndex = capacity;
            for (int i = 1; i <= count; i++) {
                if (i == count) {
                    // 最后一次遍历, 令尾指针指向数组末尾
                    endIndex = collect.size();
                }
                // 截取的数组对象
                List subList = collect.subList(startIndex, endIndex);
                if (subList.size() > 0) {
                    splitResult.add((T) subList);
                }
                // 截取完成, 移动下标
                startIndex = capacity * i;
                endIndex += capacity;
            }
        } else {
            return Arrays.asList(collect);
        }
        return splitResult;
    }

    /**
     * 通过函数获得指定返回值的数组
     * @param source 源对象
     * @param function 函数
     * @param <T> 源对象类型
     * @param <R> 指定类型
     * @return
     */
    public static <T, R> List<R> toList(List<T> source, Function<T, R> function) {
        List<R> target = new ArrayList<>(source.size());
        for (T t : source) {
            R r = function.apply(t);
            target.add(r);
        }

        return target;
    }
}
