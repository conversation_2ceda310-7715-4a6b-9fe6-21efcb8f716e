package com.ruijing.order.utils;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @createTime: 2023-07-21 11:07
 * @description: rpc调用通用封装
 **/
public class RpcCallUtils {

    /**
     * 分批执行
     * @param source 数据源
     * @param partitionSize 大小
     * @param function rpc调用的具体方法
     * @param <T> 请求体
     * @param <R> 返回值
     */
    public static <T, R> void partitionWrite(List<T> source, int partitionSize, Function<List<T>, RemoteResponse<R>> function){
        RpcCallUtils.partitionWrite(source, partitionSize, function, response -> Preconditions.isTrue(response.isSuccess(), response.getMsg()));
    }

    /**
     * 分批执行
     * @param source 数据源
     * @param partitionSize 大小
     * @param function rpc调用的具体方法
     * @param <T> 请求体
     * @param <R> 返回值
     */
    public static <T, R> void partitionWrite(List<T> source, int partitionSize, Function<List<T>, RemoteResponse<R>> function, Consumer<RemoteResponse<R>> errorHandler){
        if(CollectionUtils.isEmpty(source)){
            return;
        }
        if(source.size() < partitionSize){
            RpcCallUtils.getDataFromRemoteResponse(function.apply(source), errorHandler);
            return;
        }
        List<List<T>> partitionList = Lists.partition(source, partitionSize);
        for(List<T> part : partitionList){
            RpcCallUtils.getDataFromRemoteResponse(function.apply(New.list(part)), errorHandler);
        }
    }

    /**
     * 分批执行
     * @param source 数据源
     * @param partitionSize 大小
     * @param function rpc调用的具体方法
     * @return 整合后的数据集合
     * @param <T> 请求体
     * @param <R> 返回值
     */
    public static <T, R> List<R> partitionExec(List<T> source, int partitionSize, Function<List<T>, RemoteResponse<List<R>>> function){
        return RpcCallUtils.partitionExec(source, partitionSize, function, response -> Preconditions.isTrue(response.isSuccess(), response.getMsg()));
    }

    /**
     * 分批执行
     * @param source 数据源
     * @param partitionSize 大小
     * @param function rpc调用的具体方法
     * @return 整合后的数据集合
     * @param <T> 请求体
     * @param <R> 返回值
     */
    public static <T, R> List<R> partitionExec(List<T> source, int partitionSize, Function<List<T>, RemoteResponse<List<R>>> function, Consumer<RemoteResponse<List<R>>> errorHandler){
        if(CollectionUtils.isEmpty(source)){
            return New.list();
        }
        if(source.size() < partitionSize){
            return RpcCallUtils.getDataFromRemoteResponse(function.apply(source), errorHandler);
        }
        List<List<T>> partitionList = Lists.partition(source, partitionSize);
        List<R> resultList = new ArrayList<>(source.size());
        for(List<T> part : partitionList){
            resultList.addAll(RpcCallUtils.getDataFromRemoteResponse(function.apply(New.list(part)), errorHandler));
        }
        return resultList;
    }

    private static <T> T getDataFromRemoteResponse(RemoteResponse<T> response, Consumer<RemoteResponse<T>> errorHandler){
        if(response == null){
            return null;
        }
        if(!Boolean.TRUE.equals(response.isSuccess())){
            errorHandler.accept(response);
        }
        return response.getData();
    }
}
