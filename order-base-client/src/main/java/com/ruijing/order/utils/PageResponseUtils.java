package com.ruijing.order.utils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.order.params.BasePageParamDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 分页查询工具类
 * @author: zhong<PERSON><PERSON>i
 * @create: 2019/11/7 15:49
 **/
public class PageResponseUtils {

    /**
     * 分页工具
     * @param supplier      分页查询函数
     * @param pageNumber    当前分页参数
     * @param pageSize      页大小
     * @return              分页结果
     */
    public static <T> PageableResponse<List<T>> pageInvoke(Supplier<List<T>> supplier, Integer pageNumber , Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<T> resultList = supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);

        return PageableResponse
                .<List<T>>custom()
                .setSuccess()
                .setData(resultList)
                .setPageNo(pageInfo.getPageNum())
                .setPageSize(pageInfo.getPageSize())
                .setTotal(pageInfo.getTotal());
    }

    /**
     * 分页异构转换函数
     * @param supplier      分页查询结果生产函数
     * @param function      异构转换函数
     * @param pageNumber    页数
     * @param pageSize      每页结果数
     * @param <T>           mybatis DO对象类型
     * @param <R>           DTO对象类型
     * @return              分页结果
     */
    public static <T, R> PageableResponse<List<R>> pageInvoke(Supplier<List<T>> supplier,
                                                              Function<? super T, ? extends R> function,
                                                              Integer pageNumber,
                                                              Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        List<T> resultList = supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);

        List<R> pageList = resultList.stream().map(function).collect(Collectors.toList());
        return PageableResponse
                .<List<R>>custom()
                .setSuccess()
                .setData(pageList)
                .setPageNo(pageInfo.getPageNum())
                .setPageSize(pageInfo.getPageSize())
                .setTotal(pageInfo.getTotal());
    }

    /**
     * 分页参数 ，得到搜索起始
     * @param basePageParamDTO
     * @return
     */
    public static Integer getRequestStart(BasePageParamDTO basePageParamDTO){
        Integer pageNo = basePageParamDTO.getPageNo();
        Integer pageSize = basePageParamDTO.getPageSize();
        return getRequestStart(pageNo, pageSize);
    }

    /**
     * 分页参数 ，得到搜索起始
     * @param pageNo
     * @param pageSize
     * @return start
     */
    public static Integer getRequestStart(Integer pageNo, Integer pageSize) {
        Integer start = (pageNo - 1) * pageSize;
        return start;
    }
}
