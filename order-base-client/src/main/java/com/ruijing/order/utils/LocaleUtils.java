package com.ruijing.order.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.ruijing.base.translator.api.dto.TemplateRenderDTO;
import com.ruijing.base.translator.api.dto.TemplateRenderParam;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.client.TranslatorClient;
import com.ruijing.order.enums.IBaseTemplateEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 国际化翻译工具类
 * @Date: 2024/10/23 16:45
 **/
public class LocaleUtils {

    private static final Logger log = LoggerFactory.getLogger(LocaleUtils.class);

    private final static String RJ_WEBSITE_LANGUAGE_HEADER = "v-site-language";

    /**
     * 手动翻译
     */
    public static String translate(IBaseTemplateEnum iBaseTemplateEnum, Object... params) {
        String message = StringUtils.EMPTY;
        if (Objects.isNull(iBaseTemplateEnum)) {
            return message;
        }
        try {
            // 从请求头拿参数
            String language = RpcContext.getProviderContext().getHeader(RJ_WEBSITE_LANGUAGE_HEADER, "cnzh");
            TemplateRenderParam renderParam = new TemplateRenderParam();
            renderParam.setAppkey(Environment.getAppKey());
            renderParam.setLanguage(language);
            renderParam.setLanguageMsgTemplateCode(iBaseTemplateEnum.getTemplateCodePrefix() + iBaseTemplateEnum.getTemplateCode());
            renderParam.setLanguageMsgTemplateParam(getLocaleParams(Arrays.asList(params)));
            // 调用RPC翻译
            TranslatorClient translatorClient = SpringUtil.getBean(TranslatorClient.class);
            TemplateRenderDTO renderDTO = translatorClient.render(renderParam);
            message = renderDTO.getMsg();
        } catch (Exception e) {
            // 翻译失败用中文枚举的模板
            log.error("LocaleUtils翻译失败:code{}", iBaseTemplateEnum.getTemplateCode(), e);
            message = format(iBaseTemplateEnum.getTemplateContent(), Arrays.asList(params));
        }
        return message;

    }

    /**
     * 转换参数成Map形式
     */
    private static Map<String, String> getLocaleParams(List<Object> params) {
        if (Objects.isNull(params)) {
            return New.emptyMap();
        }
        // 转成Map形式
        Map<String, String> localeParams = New.mapWithCapacity(params.size());
        for (int i = 0; i < params.size(); i++) {
            localeParams.put(String.valueOf(i), String.valueOf(params.get(i)));
        }
        return localeParams;
    }

    /**
     * 格式化参数到模板
     */
    public static String format(String template, List<Object> params) {
        for (int i = 0; i < params.size(); i++) {
            String key = "{{" + i + "}}";
            template = StringUtils.replace(template, key, String.valueOf(params.get(i)));
        }
        return template;
    }
}
