package com.ruijing.order.params;

import java.io.Serializable;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/13 16:13
 **/
public class BasePageParamDTO implements Serializable {

    private static final long serialVersionUID = -4001512070512099030L;

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 20;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BasePageParamDTO{");
        sb.append("pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
