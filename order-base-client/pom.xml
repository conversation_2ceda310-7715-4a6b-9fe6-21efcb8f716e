<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>store-order-service</artifactId>
        <groupId>com.ruijing.store</groupId>
        <version>1.0.6-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ruijing.order</groupId>
    <artifactId>order-base-client</artifactId>
    <version>1.0.3-SNAPSHOT</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api-info-bom</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-spring-boot-autoconfigure</artifactId>
            <version>${msharp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-cat-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-pearl-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-cache-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>inf-bom-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <version>1.0.0-SNAPSHOT</version>
            <artifactId>base-biz-logger-logback</artifactId>
        </dependency>
        <!-- 多语言翻译  -->
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-biz-translator-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>