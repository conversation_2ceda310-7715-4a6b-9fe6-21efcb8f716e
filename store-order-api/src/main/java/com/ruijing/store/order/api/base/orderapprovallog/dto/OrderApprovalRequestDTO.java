package com.ruijing.store.order.api.base.orderapprovallog.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * 订单审批日志查询入参
 * <AUTHOR>
 * @Date 2020/8/3 2:27 下午
 */
public class OrderApprovalRequestDTO implements Serializable {

    private static final long serialVersionUID = -5259379211572556073L;

    /**
     * 订单id数组，必填
     */
    @ModelProperty("订单id数组，必填")
    private List<Integer> orderIdList;

    @ModelProperty(value = "订单日志类型", enumClass = OrderApprovalEnum.class)
    private List<Integer> typeList;

    @Deprecated
    private List<OrderApprovalEnum> orderApproveStatusList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public OrderApprovalRequestDTO setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
        return this;
    }

    public List<OrderApprovalEnum> getOrderApproveStatusList() {
        return orderApproveStatusList;
    }

    public void setOrderApproveStatusList(List<OrderApprovalEnum> orderApproveStatusList) {
        this.orderApproveStatusList = orderApproveStatusList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderApprovalRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderIdList=" + orderIdList)
                .add("typeList=" + typeList)
                .add("orderApproveStatusList=" + orderApproveStatusList)
                .toString();
    }
}
