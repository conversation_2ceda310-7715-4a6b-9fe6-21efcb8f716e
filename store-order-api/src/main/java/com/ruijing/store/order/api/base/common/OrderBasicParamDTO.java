package com.ruijing.store.order.api.base.common;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @author: zhukai
 * @date : 2020/1/14 3:41 下午
 * @description: 订单基础入参
 */
@RpcModel("订单-基本请求信息")
public class OrderBasicParamDTO implements Serializable {

    private static final long serialVersionUID = 6338346775643656408L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号或竞价单号")
    private String orderNo;

    /**
     * 订单id列表
     */
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;

    /**
     * 订单号列表
     */
    @RpcModelProperty("订单号或竞价单号列表")
    private List<String> orderNoList;

    /**
     * 订单详情id
     */
    @RpcModelProperty("订单详情id")
    private Integer orderDetailId;

    /**
     * 订单机构编码
     */
    @RpcModelProperty("机构编码")
    private String orgCode;

    /**
     * 采购单id
     */
    @RpcModelProperty("采购单id")
    private Integer appId;

    /**
     * 退货单号
     */
    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("程序员内部的黑话密钥")
    private String slang;

    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("供应商id")
    private Integer suppId;

    @RpcModelProperty("备注")
    private String remark;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public OrderBasicParamDTO setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
        return this;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public OrderBasicParamDTO setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public OrderBasicParamDTO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public OrderBasicParamDTO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public String getSlang() {
        return slang;
    }

    public void setSlang(String slang) {
        this.slang = slang;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderBasicParamDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public OrderBasicParamDTO setSuppId(Integer suppId) {
        this.suppId = suppId;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public OrderBasicParamDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
