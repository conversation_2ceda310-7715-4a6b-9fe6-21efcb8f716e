package com.ruijing.store.order.api.base.refinvoiceorder.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 订单关联发票请求入参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/2 15:36
 **/
public class RefInvoiceOrderRequestDTO implements Serializable {

    private static final long serialVersionUID = -8494316447297380069L;

    /**
     * 发票关联信息id 集合, 集合数不能超100
     */
    @RpcModelProperty("id集合, 集合数不能超100")
    private List<String> idList;

    /**
     * 订单id集合, 集合数不能超100
     */
    @RpcModelProperty("订单id集合, 集合数不能超100")
    private List<String> orderIdList;

    /**
     * 订单id集合, 集合数不能超100
     */
    @RpcModelProperty("发票id集合, 集合数不能超100")
    private List<Integer> invoiceIdList;

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public List<String> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<String> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getInvoiceIdList() {
        return invoiceIdList;
    }

    public void setInvoiceIdList(List<Integer> invoiceIdList) {
        this.invoiceIdList = invoiceIdList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RefInvoiceOrderRequestDTO{");
        sb.append("idList=").append(idList);
        sb.append(", orderIdList=").append(orderIdList);
        sb.append(", invoiceIdList=").append(invoiceIdList);
        sb.append('}');
        return sb.toString();
    }
}
