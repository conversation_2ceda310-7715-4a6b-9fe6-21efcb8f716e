package com.ruijing.store.order.api.general.enums;

/**
 * <AUTHOR>
 * Order 搜索 的 nested类型对象
 */

public enum OrderNestedEnum {
    /**
     * 订单详情
     */
    NESTED_TABLE_DETAIL("order_detail"),

    /**
     * 经费卡
     */
    NESTED_TABLE_CARD ("card"),

    /**
     * 日志
     */
    NESTED_TABLE_LOG  ("log"),

    /**
     * 订单额外信息
     */
    ORDER_EXTRA("order_extra");

    private String name ;

    private OrderNestedEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
