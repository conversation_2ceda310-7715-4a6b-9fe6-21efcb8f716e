package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;

/**
 * @description: 申请退货入参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/26 18:30
 **/
public class GoodsReturnParamDTO implements Serializable {
    private static final long serialVersionUID = 5935787110875293565L;
    /**
     * 订单 id
     */
    private Integer orderMasterId;
    /**
     * 机构 code
     */
    private String orgCode;
    /**
     * 机构 id
     */
    private Integer organizationId;
    /**
     * 课题组/部门 id
     */
    private Integer departmentId;

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnParamDTO{");
        sb.append("orderMasterId=").append(orderMasterId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", organizationId=").append(organizationId);
        sb.append(", departmentId=").append(departmentId);
        sb.append('}');
        return sb.toString();
    }
}
