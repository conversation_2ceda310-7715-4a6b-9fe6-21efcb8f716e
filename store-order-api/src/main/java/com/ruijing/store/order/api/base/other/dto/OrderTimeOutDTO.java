package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/22 15:40
 **/
public class OrderTimeOutDTO implements Serializable {
    private static final long serialVersionUID = 2993410556147966158L;

    /**
     * 订单id
     */
    private Integer id;
    /**
     * 用户机构id
     */
    private Integer fuserId;
    /**
     * 采购部门id
     */
    private Integer fbuyDepartmentId;
    /**
     * 超时订单数量
     */
    private Integer amount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 经费状态
     */
    private Integer fundStatus;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 库房状态
     */
    private Integer inventoryStatus;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFuserId() {
        return fuserId;
    }

    public void setFuserId(Integer fuserId) {
        this.fuserId = fuserId;
    }

    public Integer getFbuyDepartmentId() {
        return fbuyDepartmentId;
    }

    public void setFbuyDepartmentId(Integer fbuyDepartmentId) {
        this.fbuyDepartmentId = fbuyDepartmentId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    @Override
    public String toString() {
        return "OrderTimeOutDTO{" +
                "id=" + id +
                ", fuserId=" + fuserId +
                ", fbuyDepartmentId=" + fbuyDepartmentId +
                ", amount=" + amount +
                ", orderNo='" + orderNo + '\'' +
                ", fundStatus=" + fundStatus +
                ", status=" + status +
                ", inventoryStatus=" + inventoryStatus +
                '}';
    }
}
