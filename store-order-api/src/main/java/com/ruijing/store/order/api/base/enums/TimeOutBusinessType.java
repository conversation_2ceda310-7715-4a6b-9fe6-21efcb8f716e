package com.ruijing.store.order.api.base.enums;


/**
 * <AUTHOR>
 */
public enum TimeOutBusinessType // 超时验收/结算
{

    BALANCE(1, "结算","待结算状态:[6]"),

    ACCEPTANCE(2, "验收","待验收状态:[5]"),

    ACCEPT_APPROVE_OR_WAREHOUSE(3, "验收审批或入库", "待验收审批状态:[20]或待结算+库房状态[0,1,2]"),

    BALANCE_APPROVE(4, "结算单审批", "结算单审批，调用结算接口获取")
    ;

    private final Integer value;
    private final String name;
    private final String desc;

    TimeOutBusinessType(Integer value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static final TimeOutBusinessType getByName(String name) {
        for (TimeOutBusinessType timeOutBusinessType : TimeOutBusinessType.values()) {
            if (timeOutBusinessType.name.equals(name)){
                return timeOutBusinessType;
            }
        }
        return null;
    }

    public static final TimeOutBusinessType getByValue(Integer value) {
        for (TimeOutBusinessType timeOutBusinessType : TimeOutBusinessType.values()) {
            if (timeOutBusinessType.value.equals(value)){
                return timeOutBusinessType;
            }
        }
        return null;
    }
}
