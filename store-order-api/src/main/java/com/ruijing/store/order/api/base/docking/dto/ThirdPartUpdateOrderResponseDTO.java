package com.ruijing.store.order.api.base.docking.dto;

import java.io.Serializable;

/**
 * @description: 第三方单位管理平台订单
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/23 16:10
 **/
public class ThirdPartUpdateOrderResponseDTO implements Serializable {

    private static final long serialVersionUID = -3910086025063951982L;

    /**
     * 对接单号
     */
    private String particularNo;


    public String getParticularNo() {
        return particularNo;
    }

    public void setParticularNo(String particularNo) {
        this.particularNo = particularNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartOrderResponseDTO{");
        sb.append(", particularNo='").append(particularNo).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
