package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 入库商品打印信息
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/17 10:33
 **/
public class OrderEntryWarehouseProductPrintDTO implements Serializable {

    private static final long serialVersionUID = 9200321047622396646L;

    /**
     * 商品规格
     */
    @RpcModelProperty("商品规格")
    private String specifications;

    /**
     * 商品名称
     */
    @RpcModelProperty("商品名称")
    private String productName;

    /**
     * 分类标签
     */
    @RpcModelProperty("分类标签")
    private String categoryName;

    /**
     * 品牌名称
     */
    @RpcModelProperty("品牌名称")
    private String brandName;

    /**
     * 商品货号
     */
    @RpcModelProperty("商品货号")
    private String productCode;

    /**
     * cas号
     */
    @RpcModelProperty("cas号")
    private String casNo;

    /**
     * 商品单价
     */
    @RpcModelProperty("商品单价")
    private BigDecimal price;

    /**
     * 应收数量
     */
    @RpcModelProperty("应收数量")
    private Integer receivableNum;

    /**
     * 入库单位
     */
    @RpcModelProperty("入库单位")
    private String receivedUnit;

    /**
     * 实收数量
     */
    @RpcModelProperty("实收数量")
    private Integer receivedNum;

    /**
     * 计量单位
     */
    @RpcModelProperty("计量单位")
    private String measurementUnit;

    /**
     * 计量数量（实收数量）
     */
    @RpcModelProperty("计量数量（实收数量）")
    private BigDecimal measurementNum;

    /**
     * 商品图片
     */
    @RpcModelProperty("商品图片")
    private String fpicpath;

    /**
     * 商品形态: 1固体 2液体 3气体
     */
    @RpcModelProperty("商品形态: 1固体 2液体 3气体")
    private Integer form;

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getReceivableNum() {
        return receivableNum;
    }

    public void setReceivableNum(Integer receivableNum) {
        this.receivableNum = receivableNum;
    }

    public String getReceivedUnit() {
        return receivedUnit;
    }

    public void setReceivedUnit(String receivedUnit) {
        this.receivedUnit = receivedUnit;
    }

    public Integer getReceivedNum() {
        return receivedNum;
    }

    public void setReceivedNum(Integer receivedNum) {
        this.receivedNum = receivedNum;
    }

    public String getMeasurementUnit() {
        return measurementUnit;
    }

    public void setMeasurementUnit(String measurementUnit) {
        this.measurementUnit = measurementUnit;
    }

    public BigDecimal getMeasurementNum() {
        return measurementNum;
    }

    public void setMeasurementNum(BigDecimal measurementNum) {
        this.measurementNum = measurementNum;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public void setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderEntryWarehouseProductPrintDTO{");
        sb.append("specifications='").append(specifications).append('\'');
        sb.append(", productName='").append(productName).append('\'');
        sb.append(", firstCategoryName='").append(categoryName).append('\'');
        sb.append(", brandName='").append(brandName).append('\'');
        sb.append(", productCode='").append(productCode).append('\'');
        sb.append(", casNo='").append(casNo).append('\'');
        sb.append(", price=").append(price);
        sb.append(", receivableNum=").append(receivableNum);
        sb.append(", receivedUnit='").append(receivedUnit).append('\'');
        sb.append(", receivedNum=").append(receivedNum);
        sb.append(", measurementUnit='").append(measurementUnit).append('\'');
        sb.append(", measurementNum=").append(measurementNum);
        sb.append(", fpicpath='").append(fpicpath).append('\'');
        sb.append(", form=").append(form);
        sb.append('}');
        return sb.toString();
    }
}
