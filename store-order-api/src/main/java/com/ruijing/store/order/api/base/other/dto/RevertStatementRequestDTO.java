package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.StatementOperatingEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 订单结算复原入参
 * <AUTHOR>
 */
public class RevertStatementRequestDTO implements Serializable {
    private static final long serialVersionUID = -8519677885215394646L;
    /**
     * 医院编码
     */
    private String orgCode;

    /**
     * 订单id数组
     */
    private List<Integer> orderIdList;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 操作类型
     */
    private OrderApprovalEnum operatingStatus;

    /**
     * 操作人分类
     */
    private StatementOperatingEnum operatingType;

    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;

    /**
     * 原因&备注
     */
    private String reason;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public OrderApprovalEnum getOperatingStatus() {
        return operatingStatus;
    }

    public void setOperatingStatus(OrderApprovalEnum operatingStatus) {
        this.operatingStatus = operatingStatus;
    }

    public StatementOperatingEnum getOperatingType() {
        return operatingType;
    }

    public void setOperatingType(StatementOperatingEnum operatingType) {
        this.operatingType = operatingType;
    }

    public OrderStatusEnum getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(OrderStatusEnum orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getReason() {
        return reason;
    }

    public RevertStatementRequestDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RevertStatementRequestDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append(", orderIdList=").append(orderIdList);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", operatingStatus=").append(operatingStatus);
        sb.append(", operatingType=").append(operatingType);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", reason=").append(reason);
        sb.append('}');
        return sb.toString();
    }
}

