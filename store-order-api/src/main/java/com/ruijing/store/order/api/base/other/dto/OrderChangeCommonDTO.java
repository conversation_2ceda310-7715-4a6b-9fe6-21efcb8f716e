package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 订单换卡 通用dto
 */
public class OrderChangeCommonDTO implements Serializable {

    private static final long serialVersionUID = 2907636690770360200L;

    /**
     * 订单绑卡对象
     */
    private List<RefFundcardOrderDTO> refFundCardOrderList;

    /**
     * 操作人信息
     */
    private OrderOperatorDTO orderOperatorDTO;

    public List<RefFundcardOrderDTO> getRefFundCardOrderList() {
        return refFundCardOrderList;
    }

    public void setRefFundCardOrderList(List<RefFundcardOrderDTO> refFundCardOrderList) {
        this.refFundCardOrderList = refFundCardOrderList;
    }

    public OrderOperatorDTO getOrderOperatorDTO() {
        return orderOperatorDTO;
    }

    public void setOrderOperatorDTO(OrderOperatorDTO orderOperatorDTO) {
        this.orderOperatorDTO = orderOperatorDTO;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderChangeCommonDTO{");
        sb.append("refFundCardOrderList=").append(refFundCardOrderList);
        sb.append(", orderOperatorDTO=").append(orderOperatorDTO);
        sb.append('}');
        return sb.toString();
    }
}
