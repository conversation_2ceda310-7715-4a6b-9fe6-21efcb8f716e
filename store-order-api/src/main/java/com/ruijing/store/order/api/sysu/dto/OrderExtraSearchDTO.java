package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 扩展字段搜索
 * <AUTHOR>
 * @date 2024年01月21日
 */
public class OrderExtraSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 必填，单位个性化操作类型，0无意义，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}
     */
    @RpcModelProperty("必填，单位个性化操作类型，0无意义，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}")
    private Integer extraKey;

    /**
     * 必填，单位个性化描述字串，即具体存的值是什么
     */
    @RpcModelProperty("必填，单位个性化描述字串，即具体存的值是什么")
    private List<String> extraValueList;

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public List<String> getExtraValueList() {
        return extraValueList;
    }

    public void setExtraValueList(List<String> extraValueList) {
        this.extraValueList = extraValueList;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("OrderExtraSearchDTO{");
        sb.append("extraKey=").append(extraKey);
        sb.append(", extraValueList='").append(extraValueList).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
