package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-11-22 10:44
 * @description:
 **/
public class OrderBankDataDTO implements Serializable {

    private static final long serialVersionUID = -500464129272158659L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("bankId,默认0")
    private Integer bankId;

    @RpcModelProperty("银行开户名")
    private String bankAccountName;

    @RpcModelProperty("所属银行")
    private String bankName;

    @RpcModelProperty("开户行名称")
    private String bankBranch;

    @RpcModelProperty("支行联行号")
    private String bankCode;

    @RpcModelProperty("省份代码")
    private String provinceCode;

    @RpcModelProperty("城市代码")
    private String cityCode;

    @RpcModelProperty("银行卡号")
    private String bankCardNumber;

    @RpcModelProperty("账户类型")
    private Integer accountType;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderBankDataDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderBankDataDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderBankDataDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getBankId() {
        return bankId;
    }

    public OrderBankDataDTO setBankId(Integer bankId) {
        this.bankId = bankId;
        return this;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public OrderBankDataDTO setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
        return this;
    }

    public String getBankName() {
        return bankName;
    }

    public OrderBankDataDTO setBankName(String bankName) {
        this.bankName = bankName;
        return this;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public OrderBankDataDTO setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
        return this;
    }

    public String getBankCode() {
        return bankCode;
    }

    public OrderBankDataDTO setBankCode(String bankCode) {
        this.bankCode = bankCode;
        return this;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public OrderBankDataDTO setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;
    }

    public String getCityCode() {
        return cityCode;
    }

    public OrderBankDataDTO setCityCode(String cityCode) {
        this.cityCode = cityCode;
        return this;
    }

    public String getBankCardNumber() {
        return bankCardNumber;
    }

    public OrderBankDataDTO setBankCardNumber(String bankCardNumber) {
        this.bankCardNumber = bankCardNumber;
        return this;
    }


    public Integer getAccountType() {
        return accountType;
    }

    public OrderBankDataDTO setAccountType(Integer accountType) {
        this.accountType = accountType;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderBankDataDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderNo='" + orderNo + "'")
                .add("orgId=" + orgId)
                .add("bankId=" + bankId)
                .add("bankAccountName='" + bankAccountName + "'")
                .add("bankName='" + bankName + "'")
                .add("bankBranch='" + bankBranch + "'")
                .add("bankCode='" + bankCode + "'")
                .add("provinceCode='" + provinceCode + "'")
                .add("cityCode='" + cityCode + "'")
                .add("bankCardNumber='" + bankCardNumber + "'")
                .add("accountType=" + accountType)
                .toString();
    }
}
