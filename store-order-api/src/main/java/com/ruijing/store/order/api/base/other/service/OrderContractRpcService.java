package com.ruijing.store.order.api.base.other.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.OrderContractDTO;
import com.ruijing.store.order.api.base.other.dto.OrderContractQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:41
 * @description
 */
@RpcApi(value = "提供订单合同相关的RPC接口")
public interface OrderContractRpcService {


    /**
     * 查询订单合同信息
     * @param orderContractQueryDTO 查询条件
     * @return 合同信息
     */
    @RpcMethod("查询订单合同")
    RemoteResponse<List<OrderContractDTO>> listOrderContract(OrderContractQueryDTO orderContractQueryDTO);

    /**
     * 保存订单合同信息
     * @param orderContractDTOList 订单合同信息
     * @return 是否成功
     */
    @RpcMethod("保存订单合同--相关订单所有关联的合同删除再写入，上限100条/次")
    RemoteResponse<Boolean> saveOrderContract(List<OrderContractDTO> orderContractDTOList);
}
