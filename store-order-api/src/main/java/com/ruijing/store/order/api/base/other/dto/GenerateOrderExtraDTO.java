package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/13 16:41
 * @description
 */
public class GenerateOrderExtraDTO implements Serializable {

    private static final long serialVersionUID = 2382811157185339438L;
    
    /**
     * 属性域
     */
    private OrderExtraEnum field;
    
    /**
     * 属性值
     */
    private String value;

    
    public OrderExtraEnum getField() {
        return field;
    }

    public void setField(OrderExtraEnum field) {
        this.field = field;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GenerateOrderExtraDTO{");
        sb.append("field=").append(field);
        sb.append(", value='").append(value).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public GenerateOrderExtraDTO(OrderExtraEnum field, String value) {
        this.field = field;
        this.value = value;
    }

    public GenerateOrderExtraDTO() {
    }
}
