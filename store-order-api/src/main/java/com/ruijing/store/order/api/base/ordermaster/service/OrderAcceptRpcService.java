package com.ruijing.store.order.api.base.ordermaster.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;

/**
 * <AUTHOR>
 * hms订单验收业务
 */
@RpcApi("订单验收RPC")
public interface OrderAcceptRpcService {

    /**
     * 用户手动验收订单
     * @param orderReceiptParamDTO
     * @return      收货出参
     */
    @RpcMethod("用户手动验收订单")
    RemoteResponse<ReceiptOrderResponseDO> userAcceptOrder(OrderReceiptParamDTO orderReceiptParamDTO);

    /**
     * 三院临床一物一码入库完成后调用
     * 自动验收订单, 这个目前只有先入库完成后收货会调, 别的业务别用, 出问题别找我
     * @param request
     * @return      收货出参
     */
    @RpcMethod("用户手动验收订单")
    RemoteResponse<ReceiptOrderResponseDO> autoAcceptOrder(OrderReceiptParamDTO request);
}
