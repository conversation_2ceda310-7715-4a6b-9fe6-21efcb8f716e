package com.ruijing.store.order.api.base.orderdetail.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 超时订单详情DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/1 11:11
 **/
public class OrderDetailTimeOutDTO implements Serializable {

    private static final long serialVersionUID = -4846393413854403060L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 商品名
     */
    private String goodName;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 商品编号
     */
    private String goodCode;
    /**
     * 商品规格
     */
    private String spec;
    /**
     * 商品单价
     */
    private BigDecimal price;
    /**
     * 商品数量
     */
    private BigDecimal quantity;
    /**
     * 危化品标签
     */
    private String dangerousTag;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getGoodName() {
        return goodName;
    }

    public void setGoodName(String goodName) {
        this.goodName = goodName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderDetailTimeOutDTO{");
        sb.append("id=").append(id);
        sb.append(", goodName='").append(goodName).append('\'');
        sb.append(", brand='").append(brand).append('\'');
        sb.append(", goodCode='").append(goodCode).append('\'');
        sb.append(", spec='").append(spec).append('\'');
        sb.append(", price=").append(price);
        sb.append(", quantity=").append(quantity);
        sb.append(", dangerousTag='").append(dangerousTag).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
