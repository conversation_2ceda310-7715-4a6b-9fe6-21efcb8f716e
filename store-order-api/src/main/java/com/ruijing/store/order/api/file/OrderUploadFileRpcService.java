package com.ruijing.store.order.api.file;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.file.request.OrderDeleteUploadFileDTO;
import com.ruijing.store.order.api.file.request.OrderFileInfoParamDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.api.file.vo.UploadFileInfoDTO;

import java.util.List;

/**
 * Name: OrderUploadFileRPCService
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/8/21
 */
public interface OrderUploadFileRpcService {

     /**
      * 获取已上传的文件
      * @param request 获取用参数
      * @return 文件信息
      */
     RemoteResponse<List<UploadFileInfoDTO>> getUploadFileInfo(OrderFileInfoParamDTO request);

     /**
      * 上传文件
      * @param request 上传文件的参数
      * @return 是否成功
      */
     Boolean saveUploadFileInfo(OrderUploadFileDataRequestDTO request);


     @RpcMethod("根据文件ID删除文件")
     RemoteResponse<Boolean> deleteOrderFileByFileId(OrderDeleteUploadFileDTO orderDeleteUploadFileDTO);

}
