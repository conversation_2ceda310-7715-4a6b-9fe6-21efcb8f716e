package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 根据id 修改订单状态 参数对象
 * @author: zhuk
 * @create: 2019-07-09 16:02
 **/
public class UpdateOrderStatusReqDTO implements Serializable {

    private static final long serialVersionUID = -1842857131362287865L;
    /**
     * 订单Id
     */
    private Integer orderMasterId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单id 数组
     */
    private List<Integer> orderMasterIdList;

    /**
     * 收货日期
     */
    private Date lastReceiveDate;

    /**
     * 结算子状态
     * 参考 statement-api StatementStatusEnum
     */
    private Integer statementStatus;

    private String reason;

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<Integer> getOrderMasterIdList() {
        return orderMasterIdList;
    }

    public void setOrderMasterIdList(List<Integer> orderMasterIdList) {
        this.orderMasterIdList = orderMasterIdList;
    }

    public Date getLastReceiveDate() {
        return lastReceiveDate;
    }

    public void setLastReceiveDate(Date lastReceiveDate) {
        this.lastReceiveDate = lastReceiveDate;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", UpdateOrderStatusReqDTO.class.getSimpleName() + "[", "]")
                .add("orderMasterId=" + orderMasterId)
                .add("orderStatus=" + orderStatus)
                .add("orderMasterIdList=" + orderMasterIdList)
                .add("lastReceiveDate=" + lastReceiveDate)
                .add("statementStatus=" + statementStatus)
                .add("reason='" + reason + "'")
                .toString();
    }
}
