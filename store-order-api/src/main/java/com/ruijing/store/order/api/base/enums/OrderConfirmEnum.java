package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/17 17:35
 * @Description
 **/
public enum OrderConfirmEnum {

    /**
     * -1 从store来的魔法值，不知
     */
    UNKNOWN(-1, "无需确认"),
    /**
     * 0 不需确认
     */
    NO_CONFIRM(0,"不需确认"),
    /**
     * 1 需确认并上传图片
     */
    NEED_CONFIRM_AND_PICS(1,"需确认并上传图片"),
    /**
     * 2 需确认不上传图片
     */
    NEED_CONFIRM_AND_NO_PICS(2,"需确认不上传图片"),
    /**
     * 3 不需确认可再次上传图片
     */
    NO_CONFIRM_AND_CAN_PICS(3,"不需确认可再次上传图片");

    private Integer value;
    private String desc;


    OrderConfirmEnum(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderConfirmEnum getByName(String name) {
        for (OrderConfirmEnum orderConfirmEnum : OrderConfirmEnum.values()) {
            if (orderConfirmEnum.desc.equals(name)){
                return orderConfirmEnum;
            }
        }
        return null;
    }

    public static final OrderConfirmEnum getByValue(Integer value) {
        for (OrderConfirmEnum orderConfirmEnum : OrderConfirmEnum.values()) {
            if (orderConfirmEnum.value.equals(value)){
                return orderConfirmEnum;
            }
        }
        return null;
    }
}
