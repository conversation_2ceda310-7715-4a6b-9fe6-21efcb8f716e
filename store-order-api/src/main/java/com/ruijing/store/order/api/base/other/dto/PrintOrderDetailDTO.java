package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import java.util.StringJoiner;

/**
 * @title: PrintOrderDetailDTO
 * @projectName research-statement-web
 * @description: 验收单打印订单详情信息
 * @author：zhongyulei
 * @date 2019-12-06 11:35
 */
public class PrintOrderDetailDTO implements Serializable {
    private static final long serialVersionUID = 7383102556057248782L;

    /**
     * 商品名称
     */
    private String fgoodname;

    /**
     * 商品货号
     */
    private String goodCode;

    /**
     * 品牌名称
     */
    private String fbrand;

    /**
     * 商品数量
     */
    private Integer fquantity;

    /**
     * 单位
     */
    private String funit;

    /**
     * 规格
     */
    private String fspec;

    /**
     * 类别
     */
    private String categoryType;

    /**
     * 费用类别
     */
    private Set<String> categoryTagSet;

    /**
     * 单价（原价）
     */
    private BigDecimal originalPrice;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 商品总价
     */
    private BigDecimal goodTotalPrice;

    public String getFgoodname() {
        return fgoodname;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getFbrand() {
        return fbrand;
    }

    public void setFbrand(String fbrand) {
        this.fbrand = fbrand;
    }

    public Integer getFquantity() {
        return fquantity;
    }

    public void setFquantity(Integer fquantity) {
        this.fquantity = fquantity;
    }

    public String getFunit() {
        return funit;
    }

    public void setFunit(String funit) {
        this.funit = funit;
    }

    public String getFspec() {
        return fspec;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public String getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType;
    }

    public Set<String> getCategoryTagSet() {
        return categoryTagSet;
    }

    public void setCategoryTagSet(Set<String> categoryTagSet) {
        this.categoryTagSet = categoryTagSet;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getGoodTotalPrice() {
        return goodTotalPrice;
    }

    public void setGoodTotalPrice(BigDecimal goodTotalPrice) {
        this.goodTotalPrice = goodTotalPrice;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", PrintOrderDetailDTO.class.getSimpleName() + "[", "]")
                .add("fgoodname='" + fgoodname + "'")
                .add("goodCode='" + goodCode + "'")
                .add("fbrand='" + fbrand + "'")
                .add("fquantity=" + fquantity)
                .add("funit='" + funit + "'")
                .add("fspec='" + fspec + "'")
                .add("categoryType='" + categoryType + "'")
                .add("categoryTagSet=" + categoryTagSet)
                .add("originalPrice=" + originalPrice)
                .add("price=" + price)
                .add("goodTotalPrice=" + goodTotalPrice)
                .toString();
    }
}
