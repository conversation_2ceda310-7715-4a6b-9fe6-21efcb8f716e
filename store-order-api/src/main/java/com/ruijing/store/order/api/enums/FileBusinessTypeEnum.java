package com.ruijing.store.order.api.enums;

/**
 * <AUTHOR>
 * @date 2022/6/24 12:26
 * @description 上传文件的类型
 *
 * 齐鲁工业大学用到（结算）
 *
 */
public enum FileBusinessTypeEnum {

    /**
     * 服务报告
     */
    SERVICE_REPORT(1,"服务技术合同书"),

    SORTED(2, "分拣图片"),

    DELIVERY(3, "配送图片"),

    WAREHOUSE(4, "入库单"),

    ORDER_CONTRACT(5, "订单合同"),

    ACCEPTANCE_ATTACHMENT(6, "验收附件"),

    /**
     * 验收单据，港科大（管理平台传过来）、广西中医药（验收完成后自动上传）、宁波大学附属第一医院(验收前前端生成并上传）
     * 使用whitehole上的ACCEPTANCE_FORM
     */
    ACCEPTANCE_FORM(7, "验收单据"),

    OUT_WAREHOUSE(8, "出库单"),

    PURCHASE_FILE(9, "采购文件"),

    DELIVERY_NOTE(10, "送货单"),

    ACCEPTANCE_VIDEO(11, "验收视频"),

    CUSTOM_DOCUMENT(12, "自定义单据"),

    PAYMENT_RECORD(13, "付款记录"),

    ;

    /**
     * 代码值
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    FileBusinessTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 通过枚举值获取枚举
     * @param code 代码值
     * @return 枚举
     */
    public static FileBusinessTypeEnum getByValue(Integer code) {
        for (FileBusinessTypeEnum fileBusinessTypeEnum : FileBusinessTypeEnum.values()) {
            if (fileBusinessTypeEnum.getCode().equals(code)) {
                return fileBusinessTypeEnum;
            }
        }
        return null;
    }
}
