package com.ruijing.store.order.api.base.docking.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ThirdPartQueryOrderResponseDTO implements Serializable {

    private static final long serialVersionUID = 3504232225191446275L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("汇总单号")
    private String summaryNo;

    @RpcModelProperty("采购部门")
    private String departmentName;

    @RpcModelProperty("采购人")
    private String buyerName;

    @RpcModelProperty("订单日期")
    private Date orderDate;

    @RpcModelProperty("订单状态")
    private Integer status;

    @RpcModelProperty("收货人")
    private String receiveMan;

    @RpcModelProperty("收货人手机")
    private String receiveManMobile;

    @RpcModelProperty("订单金额")
    private BigDecimal price;

    @RpcModelProperty("审批人")
    private String approval;

    @RpcModelProperty("审批时间")
    private Date approvalDate;

    @RpcModelProperty("是否竞价")
    private Boolean isBid;

    @RpcModelProperty("项目code")
    private String fundProjectCode;

    @RpcModelProperty("预算code")
    private String fundCardCode;

    @RpcModelProperty("科目code")
    private String fundSubjectCode;

    @RpcModelProperty("用款类型")
    private Integer type;

    @RpcModelProperty("报销事由")
    private String remarks;

    @RpcModelProperty("供应商编码")
    private String supplierCode;

    @RpcModelProperty("供应商")
    private String supplierName;

    @RpcModelProperty("供应商联系人")
    private String supplierContactMan;

    @RpcModelProperty("供应商电话")
    private String supplierTelephone;

    @RpcModelProperty("采购人工号")
    private String buyerJobNumber;

    @RpcModelProperty("收货人工号")
    private String acceptorJobNumber;

    @RpcModelProperty("开户行")
    private String bank;

    @RpcModelProperty("开户行支行")
    private String bankChild;

    @RpcModelProperty("开户名")
    private String bankName;

    @RpcModelProperty("开户账户, 银行卡号")
    private String bankAccount;

    @RpcModelProperty("银行联行号")
    private String bankCode;

    @RpcModelProperty("订单验收图片")
    private List<String> orderAcceptPhotoList;

    @RpcModelProperty("订单明细数组")
    private List<ThirdPartQueryOrderDetailResponseDTO> orderDetailList;

    @RpcModelProperty("发票")
    private List<ThirdPartQueryOrderInvoiceResponseDTO> invoiceList;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSummaryNo() {
        return summaryNo;
    }

    public void setSummaryNo(String summaryNo) {
        this.summaryNo = summaryNo;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReceiveMan() {
        return receiveMan;
    }

    public void setReceiveMan(String receiveMan) {
        this.receiveMan = receiveMan;
    }

    public String getReceiveManMobile() {
        return receiveManMobile;
    }

    public void setReceiveManMobile(String receiveManMobile) {
        this.receiveManMobile = receiveManMobile;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getApproval() {
        return approval;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<ThirdPartQueryOrderInvoiceResponseDTO> getInvoiceList() {
        return invoiceList;
    }

    public void setInvoiceList(List<ThirdPartQueryOrderInvoiceResponseDTO> invoiceList) {
        this.invoiceList = invoiceList;
    }

    public Boolean getBid() {
        return isBid;
    }

    public void setBid(Boolean bid) {
        isBid = bid;
    }

    public String getFundProjectCode() {
        return fundProjectCode;
    }

    public void setFundProjectCode(String fundProjectCode) {
        this.fundProjectCode = fundProjectCode;
    }

    public String getFundCardCode() {
        return fundCardCode;
    }

    public void setFundCardCode(String fundCardCode) {
        this.fundCardCode = fundCardCode;
    }

    public String getFundSubjectCode() {
        return fundSubjectCode;
    }

    public void setFundSubjectCode(String fundSubjectCode) {
        this.fundSubjectCode = fundSubjectCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierContactMan() {
        return supplierContactMan;
    }

    public void setSupplierContactMan(String supplierContactMan) {
        this.supplierContactMan = supplierContactMan;
    }

    public String getSupplierTelephone() {
        return supplierTelephone;
    }

    public void setSupplierTelephone(String supplierTelephone) {
        this.supplierTelephone = supplierTelephone;
    }

    public List<ThirdPartQueryOrderDetailResponseDTO> getOrderDetailList() {
        return orderDetailList;
    }

    public void setOrderDetailList(List<ThirdPartQueryOrderDetailResponseDTO> orderDetailList) {
        this.orderDetailList = orderDetailList;
    }

    public String getBuyerJobNumber() {
        return buyerJobNumber;
    }

    public void setBuyerJobNumber(String buyerJobNumber) {
        this.buyerJobNumber = buyerJobNumber;
    }

    public List<String> getOrderAcceptPhotoList() {
        return orderAcceptPhotoList;
    }

    public void setOrderAcceptPhotoList(List<String> orderAcceptPhotoList) {
        this.orderAcceptPhotoList = orderAcceptPhotoList;
    }

    public String getBank() {
        return bank;
    }

    public String getBankChild() {
        return bankChild;
    }

    public void setBankChild(String bankChild) {
        this.bankChild = bankChild;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getAcceptorJobNumber() {
        return acceptorJobNumber;
    }

    public void setAcceptorJobNumber(String acceptorJobNumber) {
        this.acceptorJobNumber = acceptorJobNumber;
    }

    @Override
    public String toString() {
        return "ThirdPartQueryOrderResponseDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", summaryNo='" + summaryNo + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", orderDate=" + orderDate +
                ", status=" + status +
                ", receiveMan='" + receiveMan + '\'' +
                ", receiveManMobile='" + receiveManMobile + '\'' +
                ", price=" + price +
                ", approval='" + approval + '\'' +
                ", approvalDate=" + approvalDate +
                ", isBid=" + isBid +
                ", fundProjectCode='" + fundProjectCode + '\'' +
                ", fundCardCode='" + fundCardCode + '\'' +
                ", fundSubjectCode='" + fundSubjectCode + '\'' +
                ", type=" + type +
                ", remarks='" + remarks + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", supplierContactMan='" + supplierContactMan + '\'' +
                ", supplierTelephone='" + supplierTelephone + '\'' +
                ", orderDetailList=" + orderDetailList +
                ", invoiceList=" + invoiceList +
                '}';
    }
}
