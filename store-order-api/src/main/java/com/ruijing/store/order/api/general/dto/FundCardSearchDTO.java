package com.ruijing.store.order.api.general.dto;

import java.io.Serializable;

/**
 * @program: store-order-service
 * @description: 订单经费卡
 * @author: zhuk
 * @create: 2019-06-01 11:00
 **/
public class FundCardSearchDTO implements Serializable {
    private static final long serialVersionUID = 7724078403839127749L;

    /**
     * 整型的经费卡id（用不到）
     */
    @Deprecated
    private Integer cardId;

    /**
     * 经费卡编号
     */
    private String cardNo;

    /**
     * 真正的经费卡id
     */
    private String fundCardId;

    /**
     * 中科大附一特殊，经费卡院区名称
     */
    private String campusName;

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getFundCardId() {
        return fundCardId;
    }

    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("FundCardSearchDTO{");
        sb.append("cardId=").append(cardId);
        sb.append(", cardNo='").append(cardNo).append('\'');
        sb.append(", fundCardId='").append(fundCardId).append('\'');
        sb.append(", campusName='").append(campusName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
