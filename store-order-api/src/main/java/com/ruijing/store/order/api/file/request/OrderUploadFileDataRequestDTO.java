package com.ruijing.store.order.api.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 14:28
 * @description 订单文件上传请求体
 */
@RpcModel("订单文件上传请求体")
public class OrderUploadFileDataRequestDTO implements Serializable {
    
    private static final long serialVersionUID = -5617726485425487012L;
    
    @RpcModelProperty("订单上传文件信息")
    private List<OrderUploadFileDataDTO> orderUploadFileDataDTOList;

    public List<OrderUploadFileDataDTO> getOrderUploadFileDataDTOList() {
        return orderUploadFileDataDTOList;
    }

    public void setOrderUploadFileDataDTOList(List<OrderUploadFileDataDTO> orderUploadFileDataDTOList) {
        this.orderUploadFileDataDTOList = orderUploadFileDataDTOList;
    }

    @Override
    public String toString() {
        return "OrderUploadFileDataRequestDTO{" +
                "orderUploadFileDataDTOList=" + orderUploadFileDataDTOList +
                '}';
    }
}
