package com.ruijing.store.order.api.base.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.goodsreturn.dto.*;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;

import java.util.List;

/**
 * 退货单rpc接口
 * <AUTHOR>
 * @Date 2020/7/20 3:46 下午
 */
@RpcApi("退货单RPC接口")
public interface GoodsReturnRpcService {

    /**
     * 新增退货记录
     * @param goodsReturnDTO
     * @return
     */
    @RpcMethod("新增退货记录")
    RemoteResponse<Integer> insertList(List<GoodsReturnDTO> goodsReturnDTO);

    /**
     * 通过detailId数组查询退货数据，集合不可超过100个
     * @param request
     * @return
     */
    @RpcMethod("通过detailId数组查询退货数据，集合不可超过100个")
    RemoteResponse<List<GoodsReturnDTO>> findByDetailIdList(GoodsReturnRequestDTO request);

    /**
     * 通过Id数组查询退货数据，集合不可超过100个
     * @param request
     * @return
     */
    @RpcMethod("通过id数组查询退货数据，集合不可超过100个")
    RemoteResponse<List<GoodsReturnDTO>> findByIdList(GoodsReturnRequestDTO request);

    /**
     * 通过orderId数组查询退货数据，集合不可超过100个
     * @param request
     * @return
     */
    @RpcMethod("通过orderId数组查询退货数据，集合不可超过100个")
    RemoteResponse<List<GoodsReturnDTO>> findByOrderIdList(GoodsReturnRequestDTO request);

    @RpcMethod("通过returnNo数组查询退货数据，集合不可超过100个")
    RemoteResponse<List<GoodsReturnDTO>> findByReturnNoList(GoodsReturnRequestDTO request);

    /**
     * 通过订单id撤销退货单
     * @param request
     * @return
     */
    @RpcMethod("通过订单id撤销退货单，集合不可超过100个")
    RemoteResponse<Integer> cancelGoodsReturn(GoodsReturnRequestDTO request);

    /**
     * 通过订单id更新，退货完成回调，集合不可超过100个
     * @param request
     * @return
     */
    @RpcMethod("通过订单id更新，集合不可超过100个，退货完成回调，目前只有中大异步解冻回调用")
    RemoteResponse<Integer> goodsReturnSuccessCallBack(GoodsReturnRequestDTO request);

    /**
     * 查询退货统计信息（统计时间范围内金额等功能）
     * @param request
     * @return
     */
    @RpcMethod("查询退货统计信息（统计时间范围内金额等功能）")
    RemoteResponse<GoodsReturnStatResultDTO> goodsReturnStats(GoodsReturnStatRequestDTO request);

    /**
     * 查询退货统计信息
     * @param request
     * @return
     */
    @RpcMethod("查询退货统计信息")
    RemoteResponse<List<GoodsReturnDTO>> goodsReturnInfoStats(GoodsReturnStatRequestDTO request);

    /**
     * 查询退货日志
     * @param goodsReturnLogRequestDTO
     * @return
     */
    @RpcMethod("查询退货日志")
    RemoteResponse<List<GoodsReturnLogDTO>> listGoodsReturnLog(GoodsReturnLogRequestDTO goodsReturnLogRequestDTO);

    /**
     * 取消订单退货申请
     * @param goodsReturnRequestDTO 退货单列表
     * @return Boolean 是否成功
     */
    RemoteResponse<Boolean> cancelGoodsReturnByThunder(GoodsReturnRequestDTO goodsReturnRequestDTO);

    /**
     * 强制取消订单退货申请
     * @param goodsReturnCancelRequstDTO 退货取消参数
     * @return 是否成功
     */
    @RpcMethod("强制取消退货接口")
    RemoteResponse<Boolean> forceCancelGoodsReturn(GoodsReturnCancelRequstDTO goodsReturnCancelRequstDTO);
}
