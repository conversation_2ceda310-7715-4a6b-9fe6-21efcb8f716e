package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2024-10-23 14:55
 * @description:
 */
public class OrderDetailModifyRequestDTO implements Serializable {

    private static final long serialVersionUID = 5144764341433724445L;

    private Integer detailId;

    /**
     * 商品名称
     */
    private String fgoodname;

    /**
     * 图片位置
     */
    @ModelProperty("图片URL")
    private String fpicpath;

    @ModelProperty("供应商名称")
    private String suppName;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderDetailModifyRequestDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public OrderDetailModifyRequestDTO setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
        return this;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public OrderDetailModifyRequestDTO setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
        return this;
    }

    public String getSuppName() {
        return suppName;
    }

    public OrderDetailModifyRequestDTO setSuppName(String suppName) {
        this.suppName = suppName;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailModifyRequestDTO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("fgoodname='" + fgoodname + "'")
                .add("fpicpath='" + fpicpath + "'")
                .add("suppName='" + suppName + "'")
                .toString();
    }
}
