package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;

/**
 * @author: zhu<PERSON>
 * @date : 2020/10/21 1:42 下午
 * @description: 订单基础信息结算单idDTO
 */
public class OrderStatementRefDTO implements Serializable {

    /**
     * 结算单id
     */
    private Integer statementId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单id
     */
    private Integer orderId;


    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
}
