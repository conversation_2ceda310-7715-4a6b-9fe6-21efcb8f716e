package com.ruijing.store.order.api.base.goodsreturn.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-08-08 17:17
 */
public class GoodsReturnLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 退货单id
     */
    private Integer returnId;

    /**
     * 退货操作人id
     */
    private Integer operatorId;

    /**
     * 退货操作人
     */
    private String operatorName;

    /**
     * 操作人类型
     */
    private Integer operatorType;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 凭证url
     */
    private String imagesURL;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getImagesURL() {
        return imagesURL;
    }

    public void setImagesURL(String imagesURL) {
        this.imagesURL = imagesURL;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
