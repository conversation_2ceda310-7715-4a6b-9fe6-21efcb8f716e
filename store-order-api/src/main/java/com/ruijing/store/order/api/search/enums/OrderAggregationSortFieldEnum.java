package com.ruijing.store.order.api.search.enums;

/**
 * @description: 订单统计管理入参
 * @author: zhuk
 * @create: 2019-09-10 19:36
 **/
public enum OrderAggregationSortFieldEnum {

    /**
     * 金额 降序
     */
    AMOUNT_DESC(1,"amountItem","金额降序",2),

    /**
     * 数量 降序
     */
    QUANTITY_DESC(2,"quantityIem","数量降序",2),

    /**
     * 金额 升序
     */
    AMOUNT_ASC(3,"amountItem","金额升序",1),

    /**
     * 金额 升序
     */
    QUANTITY_ASC(4,"quantityIem","数量升序",1);


    OrderAggregationSortFieldEnum(Integer itemValue,String itemCode, String itemName, Integer sort) {
        this.itemValue = itemValue;
        this.itemCode = itemCode;
        this.itemName = itemName;
        this.sort = sort;
    }


    private Integer itemValue;

    private String itemCode;

    private String itemName;

    private Integer sort;

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getItemValue() {
        return itemValue;
    }

    public void setItemValue(Integer itemValue) {
        this.itemValue = itemValue;
    }

    public static OrderAggregationSortFieldEnum getSortFieldByValue(Integer itemValue) {
        switch (itemValue) {
            case 2:
                return QUANTITY_DESC;
            case 3:
                return AMOUNT_ASC;
            case 4:
                return QUANTITY_ASC;
            default:
                return AMOUNT_DESC;
        }
    }
}
