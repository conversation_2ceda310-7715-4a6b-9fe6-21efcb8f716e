package com.ruijing.store.order.api.gateway.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 同步订单数据用的入参 DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/29 10:36
 **/
public class SyncOrderParamDTO implements Serializable {

    private static final long serialVersionUID = -6746532364819728735L;

    /**
     * 同步最近{days}天数的订单数据
     */
    private Integer days;

    /**
     * 需要同步的订单数组
     */
    private List<String> numberList;

    /**
     * 同步服务对应的key
     * 0-订单，1-采购单，2-经费卡，3-结算单，4-汇总单，5-竞价单
     */
    private Integer key;

    /**
     * 自定义token，为安全使用
     */
    private String token;

    /**
     * 同步最近{hours}小时的订单数据
     */
    private Integer hours;

    /**
     * 同步最近{minutes}分钟的订单数据
     */
    private Integer minutes;

    public List<String> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<String> numberList) {
        this.numberList = numberList;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public Integer getHours() {
        return hours;
    }

    public void setHours(Integer hours) {
        this.hours = hours;
    }

    public Integer getMinutes() {
        return minutes;
    }

    public void setMinutes(Integer minutes) {
        this.minutes = minutes;
    }
}
