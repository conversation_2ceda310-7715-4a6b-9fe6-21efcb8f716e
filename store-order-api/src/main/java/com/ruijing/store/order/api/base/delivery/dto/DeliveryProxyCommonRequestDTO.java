package com.ruijing.store.order.api.base.delivery.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:12
 * @description
 */
@RpcModel("代配送通用请求体")
public class DeliveryProxyCommonRequestDTO implements Serializable {

    private static final long serialVersionUID = 3100040969016197359L;
    
    @RpcModelProperty("订单id")
    private Integer orderId;
    
    @RpcModelProperty("原因")
    private String reason;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryProxyCommonRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("reason='" + reason + "'")
                .toString();
    }
}
