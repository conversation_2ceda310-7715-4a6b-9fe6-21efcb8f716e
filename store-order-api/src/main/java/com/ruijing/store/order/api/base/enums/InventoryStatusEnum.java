package com.ruijing.store.order.api.base.enums;

/**
 * 订单入库状态枚举
 */
public enum InventoryStatusEnum {
    WAITING_FOR_STORE(0, "待入库"),
    WAITING_FOR_REVIEW(1, "待审核"),
    FAILED(2, "驳回"),
    COMPLETE(9, "已入库"),
    NOT_INBOUND(8, "无需入库"),
    /**
     * 已经推送到TPI,等待入库完成的回调
     */
    WAITING_FOR_CALLBACK(10,"入库推送中"),
    /**
     * 推送到THUNDER或者是推送到TPI失败
     */
    FAILED_TO_PUSH(11,"推送入库失败"),

    CANCELLING_RECEIPT(12, "撤销中"),

    CANCELLED_RECEIPT(13, "已撤销"),

    /**
     * 申领单已申领，只用重庆医科大学附属第一医院定制需求才用到。
     */
    CLAIMED(14, "已领用"),
    ;

    private Integer code;
    private String description;

    InventoryStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static InventoryStatusEnum getByCode(Integer code) {
        for (InventoryStatusEnum value : InventoryStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
