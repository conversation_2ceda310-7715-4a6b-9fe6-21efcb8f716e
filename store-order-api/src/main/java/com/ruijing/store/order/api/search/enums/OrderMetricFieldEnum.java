package com.ruijing.store.order.api.search.enums;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2021/7/30 15:42
 */
public enum OrderMetricFieldEnum {

    DETAIL_QUANTITY("fquantity", "订单详情数量", 2),

    DETAIL_RETURN_QUANTITY("fcancelquantity", "订单详情退货数量", 2),

    DETAIL_ORIGINAL_AMOUNT("original_amount", "订单详情原价", 2),

    DETAIL_BID_AMOUNT("fbidamount", "订单详情成交价", 2),

    DETAIL_RETURN_AMOUNT("fcancelamount", "订单详情退货金额", 2);

    /**
     * 字段名
     */
    private String field;

    /**
     * 字段注释
     */
    private String name;

    /**
     * 所属表 1= orderMaster  2= orderDetail
     */
    private Integer table;

    OrderMetricFieldEnum(String field, String name, Integer table) {
        this.field = field;
        this.name = name;
        this.table =table;
    }

    public String getField() {
        return field;
    }

    public String getName() {
        return name;
    }

    public Integer getTable() {
        return table;
    }
}
