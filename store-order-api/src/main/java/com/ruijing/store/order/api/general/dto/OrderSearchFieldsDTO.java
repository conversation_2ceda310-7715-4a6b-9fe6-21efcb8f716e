package com.ruijing.store.order.api.general.dto;


import com.ruijing.store.order.api.general.enums.OrderNestedEnum;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */

public class OrderSearchFieldsDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = -1022551092247098604L;

    /**
     * 全文检索关键字
     */
    private String searchKey;
    private Set<String> fullTextMasterFields = new HashSet<>();
    private Set<String> fullTextDetailFields = new HashSet<>();

    /**
     * 初始化全文检索字段
     */
    private void initFullTextFields(){
        //orderMaster的查询条件字段
        //fullTextMasterFields.add("forderno");         //订单编号
        //采购部门
        fullTextMasterFields.add("fbuydepartment");
        //采购人
        fullTextMasterFields.add("fbuyername");

        //orderDetail的查询条件字段
        //商品名
        fullTextDetailFields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName() +"."+"fgoodname");
        //货号
        fullTextDetailFields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+"."+"fgoodcode");
    }


    public OrderSearchFieldsDTO(String searchKey ) {
        this.searchKey = searchKey;
        initFullTextFields();
    }

    public OrderSearchFieldsDTO() {
        super();
    }


    /**
     * id
     */
    private Integer id;
    //订单号
    private String forderno;
    //申请id
    private Integer ftbuyappid;
    //结束时间
    private String upperOrderdate;
    //起始时间
    private String lowerOrderdate;
    //订单日期
    private String forderdate;
    //部门名称
    private String fbuydepartment;
    //部门id
    private List<Integer> fbuydepartmentid;
    //单位id
    private List<Integer> fuserids;
    //采购人id
    private Integer fbuyerid;
    //供应商名称
    private String fsuppname;
    //供应商id
    private List<Integer> fsuppids;
    //采购人姓名
    private String fbuyername;
    //货品名称
    private String fgoodname;
    //货号
    private String fgoodcode;
    /**
     * 订单状态列表
     */
    private List<Integer> statusList;

    private Integer species;
    /**
     *
     * 降序排序 字段，安顺设置
     */
    private List<String> descFields;

    /**
     * 升序排序 字段，安顺设置
     */
    private List<String> ascFields;

    /**
     * 是否需要根据 特定订单状态排序
     */
    private boolean scoreByStatus;

    public boolean getScoreByStatus() {
        return scoreByStatus;
    }

    public void setScoreByStatus(boolean scoreByStatus) {
        this.scoreByStatus = scoreByStatus;
    }

    /**
     * 新增全文检索字段
     * @return
     */
    public void addFullTextMasterFields(String field){
        fullTextMasterFields.add(field);
    }

    public void setFullTextMasterFields(Set<String> fields){
        fullTextMasterFields = fields;
    }
    public String getSearchKey() {
        return searchKey;
    }

    public Set<String> getFullTextMasterFields(){
        return  this.fullTextMasterFields;
    }

    public Set<String> getFullTextDetailFields() {
        return fullTextDetailFields;
    }

    public void setFullTextDetailFields(Set<String> fullTextDetailFields) {
        this.fullTextDetailFields = fullTextDetailFields;
    }

    public void addFullTextDetailFields(String fullTextDetailField) {
        this.fullTextDetailFields.add(fullTextDetailField);
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFbuydepartment() {
        return fbuydepartment;
    }

    public void setFbuydepartment(String fbuydepartment) {
        this.fbuydepartment = fbuydepartment;
    }

    public List<Integer> getFbuydepartmentid() {
        return fbuydepartmentid;
    }

    public void setFbuydepartmentid(List<Integer> fbuydepartmentid) {
        this.fbuydepartmentid = fbuydepartmentid;
    }

    public String getForderno() {
        return forderno;
    }

    public void setForderno(String forderno) {
        this.forderno = forderno;
    }

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public String getUpperOrderdate() {
        return upperOrderdate;
    }

    public void setUpperOrderdate(String upperOrderdate) {
        this.upperOrderdate = upperOrderdate;
    }

    public String getLowerOrderdate() {
        return lowerOrderdate;
    }

    public void setLowerOrderdate(String lowerOrderdate) {
        this.lowerOrderdate = lowerOrderdate;
    }

    public String getForderdate() {
        return forderdate;
    }

    public void setForderdate(String forderdate) {
        this.forderdate = forderdate;
    }

    public List<Integer> getFuserids() {
        return fuserids;
    }

    public void setFuserids(List<Integer> fuserids) {
        this.fuserids = fuserids;
    }

    public List<Integer> getFsuppids() {
        return fsuppids;
    }

    public void setFsuppids(List<Integer> fsuppids) {
        this.fsuppids = fsuppids;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    public String getFbuyername() {
        return fbuyername;
    }

    public void setFbuyername(String fbuyername) {
        this.fbuyername = fbuyername;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public void setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public List<String> getDescFields() {
        return descFields;
    }

    public void setDescFields(List<String> descFields) {
        this.descFields = descFields;
    }

    public List<String> getAscFields() {
        return ascFields;
    }

    public void setAscFields(List<String> ascFields) {
        this.ascFields = ascFields;
    }

    public Integer getFbuyerid() {
        return fbuyerid;
    }

    public void setFbuyerid(Integer fbuyerid) {
        this.fbuyerid = fbuyerid;
    }

    @Override
    public String toString() {
        return "OrderSearchFieldsDTO{" +
                "searchKey='" + searchKey + '\'' +
                ", fullTextMasterFields=" + fullTextMasterFields +
                ", fullTextDetailFields=" + fullTextDetailFields +
                ", id=" + id +
                ", forderno='" + forderno + '\'' +
                ", ftbuyappid=" + ftbuyappid +
                ", upperOrderdate='" + upperOrderdate + '\'' +
                ", lowerOrderdate='" + lowerOrderdate + '\'' +
                ", forderdate='" + forderdate + '\'' +
                ", fbuydepartment='" + fbuydepartment + '\'' +
                ", fbuydepartmentid=" + fbuydepartmentid +
                ", fuserids=" + fuserids +
                ", fbuyerid=" + fbuyerid +
                ", fsuppname='" + fsuppname + '\'' +
                ", fsuppids=" + fsuppids +
                ", fbuyername='" + fbuyername + '\'' +
                ", fgoodname='" + fgoodname + '\'' +
                ", fgoodcode='" + fgoodcode + '\'' +
                ", statusList=" + statusList +
                ", species=" + species +
                ", descFields=" + descFields +
                ", ascFields=" + ascFields +
                ", scoreByStatus=" + scoreByStatus +
                '}';
    }
}
