package com.ruijing.store.order.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/6/20 16:09
 */
@RpcModel("订单——额外信息查询请求体")
public class OrderExtraInfoParamDTO implements Serializable {

    private static final long serialVersionUID = 2051435639475569243L;

    @RpcModelProperty("订单额外信息 枚举")
    private OrderExtraEnum orderExtraEnum;

    @RpcModelProperty("订单额外信息 值")
    private String orderExtraValue;

    @RpcModelProperty("订单额外信息 是包含还是不包含此枚举，bool，true正选，false反选，空则判断查询值")
    private Boolean includeThisExtra;

    public OrderExtraEnum getOrderExtraEnum() {
        return orderExtraEnum;
    }

    public void setOrderExtraEnum(OrderExtraEnum orderExtraEnum) {
        this.orderExtraEnum = orderExtraEnum;
    }

    public String getOrderExtraValue() {
        return orderExtraValue;
    }

    public void setOrderExtraValue(String orderExtraValue) {
        this.orderExtraValue = orderExtraValue;
    }

    public Boolean getIncludeThisExtra() {
        return includeThisExtra;
    }

    public void setIncludeThisExtra(Boolean includeThisExtra) {
        this.includeThisExtra = includeThisExtra;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExtraInfoParamDTO{");
        sb.append("orderExtraEnum=").append(orderExtraEnum);
        sb.append(", orderExtraValue='").append(orderExtraValue).append('\'');
        sb.append(", includeThisExtra=").append(includeThisExtra);
        sb.append('}');
        return sb.toString();
    }
}
