package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 价格更新请求类
 * @date 2023/10/31 28
 */
public class UpdateOrderPriceRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单详情价格")
    private List<OrderDetailPriceDTO> orderDetailPriceDTOList;

    public List<OrderDetailPriceDTO> getOrderDetailPriceDTOList() {
        return orderDetailPriceDTOList;
    }

    public void setOrderDetailPriceDTOList(List<OrderDetailPriceDTO> orderDetailPriceDTOList) {
        this.orderDetailPriceDTOList = orderDetailPriceDTOList;
    }
}
