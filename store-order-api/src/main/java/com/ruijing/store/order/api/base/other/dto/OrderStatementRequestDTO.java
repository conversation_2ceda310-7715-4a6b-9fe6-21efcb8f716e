package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 订单发起结算请求的dto
 */
public class OrderStatementRequestDTO implements Serializable {

    private static final long serialVersionUID = 8465790002456552518L;
    /**
     * 结算单id
     */
    private Integer statementId;

    /**
     * 订单id数组
     */
    private List<Integer> orderIdList;

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderStatementRequestDTO{");
        sb.append("statementId=").append(statementId);
        sb.append(", orderIdList=").append(orderIdList);
        sb.append('}');
        return sb.toString();
    }
}
