package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderExplosivesGoodsDTO implements Serializable {

    private static final long serialVersionUID = 3714147658428999118L;

    /**
     * 订单商品id
     */
    private Integer id;

    /**
     * 商品计量单位
     */
    private String unit;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 商品损耗量
     */
    private BigDecimal attrition;

    /**
     * 合法用途说明
     */
    private String legalUse;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAttrition() {
        return attrition;
    }

    public void setAttrition(BigDecimal attrition) {
        this.attrition = attrition;
    }

    public String getLegalUse() {
        return legalUse;
    }

    public void setLegalUse(String legalUse) {
        this.legalUse = legalUse;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExplosivesGoodsDTO{");
        sb.append("id=").append(id);
        sb.append(", unit='").append(unit).append('\'');
        sb.append(", quantity=").append(quantity);
        sb.append(", attrition=").append(attrition);
        sb.append(", legalUse='").append(legalUse).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
