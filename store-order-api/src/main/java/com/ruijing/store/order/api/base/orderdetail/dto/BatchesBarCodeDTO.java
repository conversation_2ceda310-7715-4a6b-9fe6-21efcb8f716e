package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

@RpcModel("批次条形码信息")
public class BatchesBarCodeDTO implements Serializable {

    private static final long serialVersionUID = 7299004134947246942L;

    /**
     * 条形码, 长整型
     */
    @RpcModelProperty("条形码")
    private String barCode;

    /**
     * 只有部分接口的中爆条形码返回，确保效率
     */
    @RpcModelProperty("条形码图片")
    private String barCodeImg;

    /**
     * 需要的才返回
     */
    @RpcModelProperty("条形码的二维码版本")
    private String barCodeQrImg;

    @RpcModelProperty(value = "条形码类型", enumLink = "com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum")
    private Integer type;

    /**
     * 批号
     */
    @RpcModelProperty("批号")
    private String batches;

    /**
     * 有效期
     */
    @RpcModelProperty("有效期")
    private String expiration;

    @RpcModelProperty("生产厂家")
    private String manufacturer;

    @RpcModelProperty("生产日期")
    private String productionDate;

    @RpcModelProperty("气瓶码")
    private String gasBottleBarcode;

    @RpcModelProperty("数量")
    private Integer quantity;

    /**
     * 耐久度(外观) 0正常1破损
     */
    @RpcModelProperty("耐久度 0正常1破损")
    private Integer exterior;

    /**
     * 0：待录入批次
     * 1：待发货
     * 2：待收货
     * 3：待入库审批
     * 4：已入库
     * --入库驳回
     * 5：待出库审批
     * 6：已出库
     * 7：退货待确认
     * 8：取消退货
     * 9：同意退货
     * 10：退还货物
     * 11：已退货
     * 12: 拒绝退货
     */
    @RpcModelProperty("码的状态")
    private Integer status;

    @RpcModelProperty(value = "批次状态")
    private Integer batchesStatus;

    @RpcModelProperty(value = "交易状态")
    private Integer transactionStatus;

    @RpcModelProperty(value = "库房状态")
    private Integer inventoryStatus;

    /**
     * 打印状态, 0未1已打印
     */
    @RpcModelProperty("打印状态, 0未1已打印")
    private Integer printed;

    @ModelProperty("退货原因")
    private String returnReason;

    /**
     * 退货说明
     */
    @ModelProperty("退货说明")
    private String returnDescription;

    public String getBarCode() {
        return barCode;
    }

    public BatchesBarCodeDTO setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public String getBarCodeImg() {
        return barCodeImg;
    }

    public BatchesBarCodeDTO setBarCodeImg(String barCodeImg) {
        this.barCodeImg = barCodeImg;
        return this;
    }

    public String getBarCodeQrImg() {
        return barCodeQrImg;
    }

    public BatchesBarCodeDTO setBarCodeQrImg(String barCodeQrImg) {
        this.barCodeQrImg = barCodeQrImg;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public BatchesBarCodeDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public BatchesBarCodeDTO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public BatchesBarCodeDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public BatchesBarCodeDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public BatchesBarCodeDTO setQuantity(Integer quantity) {
        this.quantity = quantity;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public BatchesBarCodeDTO setProductionDate(String productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public BatchesBarCodeDTO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public BatchesBarCodeDTO setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public BatchesBarCodeDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getBatchesStatus() {
        return batchesStatus;
    }

    public BatchesBarCodeDTO setBatchesStatus(Integer batchesStatus) {
        this.batchesStatus = batchesStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public BatchesBarCodeDTO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public BatchesBarCodeDTO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getPrinted() {
        return printed;
    }

    public BatchesBarCodeDTO setPrinted(Integer printed) {
        this.printed = printed;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public BatchesBarCodeDTO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public BatchesBarCodeDTO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BatchesBarCodeDTO.class.getSimpleName() + "[", "]")
                .add("barCode='" + barCode + "'")
                .add("barCodeImg='" + barCodeImg + "'")
                .add("barCodeQrImg='" + barCodeQrImg + "'")
                .add("type=" + type)
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("productionDate='" + productionDate + "'")
                .add("gasBottleBarcode='" + gasBottleBarcode + "'")
                .add("quantity=" + quantity)
                .add("exterior=" + exterior)
                .add("status=" + status)
                .add("batchesStatus=" + batchesStatus)
                .add("transactionStatus=" + transactionStatus)
                .add("inventoryStatus=" + inventoryStatus)
                .add("printed=" + printed)
                .add("returnReason='" + returnReason + "'")
                .add("returnDescription='" + returnDescription + "'")
                .toString();
    }

}
