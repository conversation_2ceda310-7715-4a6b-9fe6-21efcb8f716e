package com.ruijing.store.order.api.advertisement.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 广告投放订单信息查询请求类
 * @date 2023/9/4 16:05
 */
public class AdvertisementOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("页码")
    private Integer pageNo;

    @RpcModelProperty("页大小")
    private Integer pageSize;

    @RpcModelProperty("供应商id")
    private List<Integer> suppIdList;

    @RpcModelProperty("订单开始时间")
    private Date startTime;

    @RpcModelProperty("订单结束时间")
    private Date endTime;

    @RpcModelProperty("广告id")
    private List<String> advertisementIdList;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getSuppIdList() {
        return suppIdList;
    }

    public void setSuppIdList(List<Integer> suppIdList) {
        this.suppIdList = suppIdList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<String> getAdvertisementIdList() {
        return advertisementIdList;
    }

    public void setAdvertisementIdList(List<String> advertisementIdList) {
        this.advertisementIdList = advertisementIdList;
    }
}
