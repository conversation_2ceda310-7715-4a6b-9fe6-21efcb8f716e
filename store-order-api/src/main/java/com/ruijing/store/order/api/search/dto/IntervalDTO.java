package com.ruijing.store.order.api.search.dto;

import com.ruijing.store.order.api.search.enums.DateUnitEnum;

import java.io.Serializable;

/**
 * @author: zhukai
 * @date : 2020/2/17 3:21 下午
 * @description: 间隔周期DTO
 */
public class IntervalDTO implements Serializable {

    private static final long serialVersionUID = -8809883561369144813L;

    /**
     * 值 默认1
     */
    private Integer value = 1;

    /**
     * 单位 默认月
     */
    private DateUnitEnum dateUnit = DateUnitEnum.MONTH;

    /**
     * 时间格式 默认：yyyy-MM-dd
     */
    private String dateFormat = "yyyy-MM-dd";

    public IntervalDTO() {
        super();
    }

    public IntervalDTO(Integer value, DateUnitEnum dateUnit, String dateFormat) {
        this.value = value;
        this.dateUnit = dateUnit;
        this.dateFormat = dateFormat;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public DateUnitEnum getDateUnit() {
        return dateUnit;
    }

    public void setDateUnit(DateUnitEnum dateUnit) {
        this.dateUnit = dateUnit;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    @Override
    public String toString() {
        return "IntervalDTO{" +
                "value=" + value +
                ", dateUnit=" + dateUnit +
                ", dateFormat='" + dateFormat + '\'' +
                '}';
    }
}
