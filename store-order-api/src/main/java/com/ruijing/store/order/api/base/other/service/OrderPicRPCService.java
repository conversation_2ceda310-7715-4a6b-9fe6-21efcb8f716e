package com.ruijing.store.order.api.base.other.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.OrderPicDTO;

import java.util.List;

/**
 * 提供t_order_pic表的相关RPC接口
 */
@RpcApi(value = "提供t_order_pic表的相关RPC接口")
public interface OrderPicRPCService {

    /**
     * 按订单号，获取订单图片链接的列表
     *
     * @param orderNoList
     * @return 订单图片链接的列表
     */
    @RpcMethod("按订单号，查询获取订单图片链接的列表，限制100条内")
    RemoteResponse<List<OrderPicDTO>> findPicByOrderNo(List<String> orderNoList);

    /**
     * 按id列表，批量删除订单图片链接
     *
     * @param idList
     * @return
     */
    @RpcMethod("按order pic表主键id列表，批量删除，限制50条内")
    RemoteResponse deletePicById(List<Integer> idList);

    /**
     * 按订单列表，批量插入
     *
     * @param orderPicList
     * @return
     */
    @RpcMethod("按订单列表，批量插入，限制50条内")
    RemoteResponse batchInsertOrderPic(List<OrderPicDTO> orderPicList);
}
