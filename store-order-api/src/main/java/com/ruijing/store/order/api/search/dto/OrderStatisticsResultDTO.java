package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;

/**
 * @description: 首页订单状态统计 结果
 * @author: zhuk
 * @create: 2019-08-22 15:14
 **/
public class OrderStatisticsResultDTO implements Serializable {

    private static final long serialVersionUID = 6734055702517063766L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 状态
     */
    private Integer status;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "OrderStatisticsResultDTO{" +
                "orderId=" + orderId +
                ", orderNumber='" + orderNumber + '\'' +
                ", status=" + status +
                '}';
    }
}
