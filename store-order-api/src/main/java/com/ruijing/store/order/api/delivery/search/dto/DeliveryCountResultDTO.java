package com.ruijing.store.order.api.delivery.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-10-08 16:30
 * @description:
 **/
@RpcModel("代配送数量结果")
public class DeliveryCountResultDTO implements Serializable {

    private static final long serialVersionUID = -8473340895807074730L;

    /**
     * 总代配送数量
     */
    @RpcModelProperty("总代配送订单数量")
    private Integer totalCount;

    public Integer getTotalCount() {
        return totalCount;
    }

    public DeliveryCountResultDTO setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryCountResultDTO.class.getSimpleName() + "[", "]")
                .add("totalCount=" + totalCount)
                .toString();
    }
}
