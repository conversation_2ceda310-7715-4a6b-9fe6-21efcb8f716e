package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 首页订单状态统计入参
 * @author: zhuk
 * @create: 2019-08-22 15:11
 **/
public class OrderStatisticsParamDTO implements Serializable{

    private static final long serialVersionUID = 2968094860334532463L;

    /**
     * 采购人id
     */
    private List<Integer> buyerIds;

    /**
     * 组织id
     */
    private  List<Integer> orgIds;

    /**
     * 供应商Id
     */
    private List<Integer> suppIds;

    /**
     * 课题组id列表
     */
    private List<Integer> deptIds;

    /**
     * 需要的线上下单的集合{@link com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum}
     */
    private List<Integer> speciesList;

    /**
     * 需要关联的采购审批流id
     */
    private List<Integer> flowIdList;

    /**
     * 是否在hms
     */
    private Boolean inHms;

    /**
     * 是否展示我的订单列表内的状态
     */
    private Boolean myOrderCheck;

    public List<Integer> getBuyerIds() {
        return buyerIds;
    }

    public void setBuyerIds(List<Integer> buyerIds) {
        this.buyerIds = buyerIds;
    }

    public List<Integer> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Integer> orgIds) {
        this.orgIds = orgIds;
    }

    public List<Integer> getSuppIds() {
        return suppIds;
    }

    public void setSuppIds(List<Integer> suppIds) {
        this.suppIds = suppIds;
    }

    public List<Integer> getDeptIds() {
        return deptIds;
    }

    public OrderStatisticsParamDTO setDeptIds(List<Integer> deptIds) {
        this.deptIds = deptIds;
        return this;
    }

    public List<Integer> getSpeciesList() {
        return speciesList;
    }

    public void setSpeciesList(List<Integer> speciesList) {
        this.speciesList = speciesList;
    }

    public List<Integer> getFlowIdList() {
        return flowIdList;
    }

    public OrderStatisticsParamDTO setFlowIdList(List<Integer> flowIdList) {
        this.flowIdList = flowIdList;
        return this;
    }

    public Boolean getInHms() {
        return inHms;
    }

    public void setInHms(Boolean inHms) {
        this.inHms = inHms;
    }

    public Boolean getMyOrderCheck() {
        return myOrderCheck;
    }

    public OrderStatisticsParamDTO setMyOrderCheck(Boolean myOrderCheck) {
        this.myOrderCheck = myOrderCheck;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderStatisticsParamDTO.class.getSimpleName() + "[", "]")
                .add("buyerIds=" + buyerIds)
                .add("orgIds=" + orgIds)
                .add("suppIds=" + suppIds)
                .add("deptIds=" + deptIds)
                .add("speciesList=" + speciesList)
                .add("flowIdList=" + flowIdList)
                .add("inHms=" + inHms)
                .add("myOrderCheck=" + myOrderCheck)
                .toString();
    }
}
