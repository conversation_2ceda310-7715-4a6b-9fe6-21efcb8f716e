package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 统计商品销量DTO
 * @author: zhuk
 * @create: 2019-09-09 15:26
 **/
public class ProductSalesParamDTO implements Serializable {

    private static final long serialVersionUID = 4020706542966888390L;

    /**
     * 产品Id
     */
    private Long productId;

    /**
     * 起始时间
     */
    private String startDate;

    /**
     * 截止时间
     */
    private String endDate;

    /**
     * 采购课题组id
     */
    private List<Integer> departmentId;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<Integer> getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(List<Integer> departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    public String toString() {
        return "ProductSalesParamDTO{" +
                "productId=" + productId +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", departmentId=" + departmentId +
                '}';
    }
}
