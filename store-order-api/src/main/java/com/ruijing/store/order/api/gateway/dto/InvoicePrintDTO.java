package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

public class InvoicePrintDTO implements Serializable {

    private static final long serialVersionUID = 2562060462781693407L;

    @RpcModelProperty("发票号")
    private String invoiceNo;

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }
}
