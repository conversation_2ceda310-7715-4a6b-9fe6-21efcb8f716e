package com.ruijing.store.order.api.base.enums;

/**
 * Author: zhukai
 * CreateTime : 2019/12/5 3:12 下午
 * Description: 线上单。先下单类型枚举
 */
public enum ProcessSpeciesEnum {

    NORMAL(0,"线上单"),
    OFFLINE(1,"线下单");

    private Integer value;

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    ProcessSpeciesEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static final ProcessSpeciesEnum getByName(String name) {
        for (ProcessSpeciesEnum processSpeciesEnum : ProcessSpeciesEnum.values()) {
            if (processSpeciesEnum.name.equals(name)){
                return processSpeciesEnum;
            }
        }
        return null;
    }

    public static final ProcessSpeciesEnum getByValue(Integer value) {
        for (ProcessSpeciesEnum processSpeciesEnum : ProcessSpeciesEnum.values()) {
            if (processSpeciesEnum.value.equals(value)){
                return processSpeciesEnum;
            }
        }
        return null;
    }

}
