package com.ruijing.store.order.api.base.docking.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

public class ThirdPartQueryRequestDTO implements Serializable {
    private static final long serialVersionUID = -2536562192803437468L;

    /**
     * 对接单位编码
     */
    @RpcModelProperty("机构编码")
    private String orgCode;

    /**
     * 锐竞商城订单号
     */
    @RpcModelProperty("订单号数组")
    private List<String> orderNoList;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    @Override
    public String toString() {
        return "ThirdPartQueryRequestDTO{" +
                "orgCode='" + orgCode + '\'' +
                ", orderNoList=" + orderNoList +
                '}';
    }
}
