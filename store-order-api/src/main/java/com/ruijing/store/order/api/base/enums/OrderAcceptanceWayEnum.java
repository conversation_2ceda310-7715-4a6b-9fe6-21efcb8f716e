package com.ruijing.store.order.api.base.enums;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-25 09:56
 * @description: 验收方式
 */
public enum OrderAcceptanceWayEnum {

    /**
     * 在订单管理页面做验收
     */
    NORMAL(0, "正常验收"),

    /**
     * 在库房界面做扫码验收
     */
    SCAN_BARCODE(1, "扫码验收"),
    ;

    public final Integer value;

    public final String name;

    OrderAcceptanceWayEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
