package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: chenzhanliang
 * @createTime: 2024-04-24 14:25
 * @description:
 **/
@RpcModel("附件")
public class AttachmentDTO implements Serializable {

    private static final long serialVersionUID = -8268528759998600392L;

    @RpcModelProperty("附件url")
    private String url;

    @RpcModelProperty("附件名字")
    private String fileName;

    public String getUrl() {
        return url;
    }

    public AttachmentDTO setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public AttachmentDTO setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public AttachmentDTO(String url, String fileName) {
        this.url = url;
        this.fileName = fileName;
    }

    public AttachmentDTO() {
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", AttachmentDTO.class.getSimpleName() + "[", "]")
                .add("url='" + url + "'")
                .add("fileName='" + fileName + "'")
                .toString();
    }
}
