package com.ruijing.store.order.api.general.dto;

import com.ruijing.store.order.api.general.enums.OrderNestedEnum;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class OrderSearchExtraResultDTO implements Serializable {

    private static final long serialVersionUID = -2796610196668183243L;
    private Set<String> fields = new HashSet<>();

    public OrderSearchExtraResultDTO(){
        fields.add("id");
        //订单号
        fields.add("forderno");
        //订单时间
        fields.add("forderdate");
        //部门名称
        fields.add("fbuydepartment");
        //部门名称id
        fields.add("fbuydepartmentid");
        //采购人名称
        fields.add("fbuyername");
        //采购人id
        fields.add("fbuyerid");
        //供应商id
        fields.add("fsuppid");
        //供应商名称
        fields.add("fsuppname");
        //订单总金额
        fields.add("forderamounttotal");
        //单位id
        fields.add("fuserid");
        //单位名称
        fields.add("fusername");
        //状态
        fields.add("status");
        //结算单id
        fields.add("statement_id");

        //订单详情ID
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "detail_id");
        //货物名称0
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fgoodname");
        //货号
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fgoodcode");
        //品牌
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fbrand");
        //规格
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fspec");
        //数量
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fquantity");
        //商品单价
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "original_price");
        //商品项 原价
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "original_amount");
        //招标价格
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fbidprice");
        //图片路径
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "fpicpath");
        //危险品标识
        fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." + "dangerousTag");
    }

    public void setTorderMasterFields(String field){
        if(isNotBlank(field)){
            fields.add(field);
        }
    }

    public void setTorderDetailFields(String field){
        if(isNotBlank(field)){
            fields.add(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+ "." +field);
        }
    }

    public void setTorderLogFields(String field){
        if(isNotBlank(field)){
            fields.add(OrderNestedEnum.NESTED_TABLE_LOG.getName()+ "." +field);
        }
    }

    public void setTorderCardFields(String field){
        if(isNotBlank(field)){
            fields.add(OrderNestedEnum.NESTED_TABLE_CARD.getName()+ "." +field);
        }
    }

    public Set<String> getFields(){
        return fields;
    }

    /**
     * 因为API包没有 commons-lang3 包，自己写一个判空白的方法
     * @param cs 字符串入参
     * @return   字符是否串是否不为空的结果
     */
    public boolean isNotBlank(CharSequence cs) {
        return !isBlank(cs);
    }

    /**
     * 因为API包没有 commons-lang3 包，自己写一个判空白的方法
     * @param cs 字符串入参
     * @return   字符是否串是否不为空的结果
     */
    public boolean isBlank(CharSequence cs) {
        int strLen;
        if (cs != null && (strLen = cs.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(cs.charAt(i))) {
                    return false;
                }
            }

        }
        return true;
    }
}
