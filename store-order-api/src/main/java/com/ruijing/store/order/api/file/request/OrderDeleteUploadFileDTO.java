package com.ruijing.store.order.api.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.enums.FileBusinessTypeEnum;

import java.io.Serializable;
import java.util.List;

@RpcModel("订单——删除文件入参")
public class OrderDeleteUploadFileDTO implements Serializable {

    private static final long serialVersionUID = -2331678641748032464L;

    @RpcModelProperty(value = "订单ID",description = "必传")
    private Integer orderId;

    @RpcModelProperty(value = "文件ID集合", description = "必传")
    private List<Long> fileIdList;

    @RpcModelProperty(value = "文件类型", enumClass = FileBusinessTypeEnum.class, description = "必传")
    private Integer fileBusinessType;

    @RpcModelProperty(value = "文件名",description = "有就传")
    private String fileName;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<Long> getFileIdList() {
        return fileIdList;
    }

    public void setFileIdList(List<Long> fileIdList) {
        this.fileIdList = fileIdList;
    }

    public Integer getFileBusinessType() {
        return fileBusinessType;
    }

    public void setFileBusinessType(Integer fileBusinessType) {
        this.fileBusinessType = fileBusinessType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "OrderDeleteUploadFileDTO{" +
                "orderId=" + orderId +
                ", fileIdList=" + fileIdList +
                ", fileBusinessType=" + fileBusinessType +
                ", fileName='" + fileName + '\'' +
                '}';
    }
}
