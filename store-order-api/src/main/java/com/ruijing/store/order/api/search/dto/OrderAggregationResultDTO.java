package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: zhukai
 * @date : 2020/2/17 1:50 上午
 * @description: 订单聚合结果DTO
 */
public class OrderAggregationResultDTO implements Serializable {

    private static final long serialVersionUID = 7345131316683885212L;

    /**
     * 聚合字段的Id，当聚合字段为非数字时不返回
     */
    private Long aggFieldId;

    /**
     * 聚合字段的字符串，部分接口有
     */
    private String aggFieldStr;

    /**
     * 金额
     */
    private Double amount;

    /**
     * 数量
     */
    private Double quantity;

    public Long getAggFieldId() {
        return aggFieldId;
    }

    public void setAggFieldId(Long aggFieldId) {
        this.aggFieldId = aggFieldId;
    }

    public String getAggFieldStr() {
        return aggFieldStr;
    }

    public OrderAggregationResultDTO setAggFieldStr(String aggFieldStr) {
        this.aggFieldStr = aggFieldStr;
        return this;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderAggregationResultDTO.class.getSimpleName() + "[", "]")
                .add("aggFieldId=" + aggFieldId)
                .add("aggFieldStr='" + aggFieldStr + "'")
                .add("amount=" + amount)
                .add("quantity=" + quantity)
                .toString();
    }
}
