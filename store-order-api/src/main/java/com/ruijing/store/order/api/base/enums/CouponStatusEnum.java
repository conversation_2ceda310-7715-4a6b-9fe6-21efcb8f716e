package com.ruijing.store.order.api.base.enums;

public enum CouponStatusEnum{
    Got(0, "已领取"),
    Resume(5, "恢复使用"),
    Used(10, "已使用"),
    Expired(20, "已过期");

    public final int value;
    public final String name;

    private CouponStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static final CouponStatusEnum get(int type) {
        for (CouponStatusEnum couponStatusEnum : CouponStatusEnum.values()) {
            if (couponStatusEnum.value == type) {
                return couponStatusEnum;
            }
        }
        return null;
    }

    public static final boolean canUse(CouponStatusEnum couponStatusEnum) {
        return couponStatusEnum == Got || couponStatusEnum == Resume;
    }

    public Integer getValue() {
        return this.value;
    }
}
