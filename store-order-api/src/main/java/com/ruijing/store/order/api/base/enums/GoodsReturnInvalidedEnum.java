package com.ruijing.store.order.api.base.enums;

/**
 * 退货单是否作废枚举
 */
public enum GoodsReturnInvalidedEnum {
    NORMAL(0, "正常"),
    INVALIDED(1, "已作废"),
    ;

    private Integer code;
    private String description;

    GoodsReturnInvalidedEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
