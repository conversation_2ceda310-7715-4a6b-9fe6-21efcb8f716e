package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: OrderApproLogDTO
 * @author: zhuk
 * @create: 2019-07-05 16:54
 **/
public class OrderApprovalLogDTO implements Serializable {

    private static final long serialVersionUID = 6605635159026587951L;

    private Integer id;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 收货照片,多张用分号分隔
     */
    private String photo;

    /**
     * 审批原因
     */
    private String reason;

    /**
     * 审批结果状态0驳回1通过
     */
    private Integer approveStatus;

    /**
     * 验收审批等级
     */
    private Integer approveLevel;

    /**
     * 审批结果描述
     */
    private String approveDescription;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 签名url
     */
    private String signUrl;

    /**
     * 操作时间
     */
    private Date creationTime;

    public Integer getId() {
        return id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public String getPhoto() {
        return photo;
    }

    public String getReason() {
        return reason;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getApproveDescription() {
        return approveDescription;
    }

    public void setApproveDescription(String approveDescription) {
        this.approveDescription = approveDescription;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getSignUrl() {
        return signUrl;
    }

    public void setSignUrl(String signUrl) {
        this.signUrl = signUrl;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderApprovalLogDTO{");
        sb.append("id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", photo='").append(photo).append('\'');
        sb.append(", reason='").append(reason).append('\'');
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", approveLevel=").append(approveLevel);
        sb.append(", approveDescription='").append(approveDescription).append('\'');
        sb.append(", operatorId=").append(operatorId);
        sb.append(", operatorName='").append(operatorName).append('\'');
        sb.append(", signUrl='").append(signUrl).append('\'');
        sb.append(", creationTime=").append(creationTime);
        sb.append('}');
        return sb.toString();
    }
}
