package com.ruijing.store.order.api.base.docking.dto;

import java.io.Serializable;
import java.util.List;

/**
 * docking 第三方财务对接信息查询入参实体
 */
public class DockingRequestDTO implements Serializable {

    private static final long serialVersionUID = -7400981005691381009L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 主键id 集合，不可超过500
     */
    private List<Integer> idList;

    /**
     * 单号，0-订单号
     */
    private String info;

    /**
     * 单号数组，不可超过500
     */
    private List<String> infoList;

    /**
     * 对接单号数组，不可超过100
     */
    private List<String> extraInfoList;

    /**
     * 对接类型
     */
    private Integer type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public List<String> getInfoList() {
        return infoList;
    }

    public void setInfoList(List<String> infoList) {
        this.infoList = infoList;
    }

    public List<String> getExtraInfoList() {
        return extraInfoList;
    }

    public void setExtraInfoList(List<String> extraInfoList) {
        this.extraInfoList = extraInfoList;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DockingRequestDTO{");
        sb.append("id=").append(id);
        sb.append(", idList=").append(idList);
        sb.append(", info='").append(info).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", infoList=").append(infoList);
        sb.append(", extraInfoList=").append(extraInfoList);
        sb.append('}');
        return sb.toString();
    }
}
