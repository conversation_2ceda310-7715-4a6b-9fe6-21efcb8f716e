package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

public class GoodsReturnDetailPageDTO implements Serializable {

    private static final long serialVersionUID = 4828553996840691277L;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("订单明细商品id")
    private String detailId;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("商品单价")
    private String price;

    @RpcModelProperty("商品数量")
    private String quantity;

    @RpcModelProperty("商品总价")
    private String amount;

    @RpcModelProperty("商品图片")
    private String goodsPicturePath;

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnDetailPageVO{");
        sb.append("productId='").append(productId).append('\'');
        sb.append("detailId='").append(detailId).append('\'');
        sb.append("goodsName='").append(goodsName).append('\'');
        sb.append(", goodsCode='").append(goodsCode).append('\'');
        sb.append(", specification='").append(specification).append('\'');
        sb.append(", brand='").append(brand).append('\'');
        sb.append(", price=").append(price);
        sb.append(", quantity=").append(quantity);
        sb.append(", amount=").append(amount);
        sb.append('}');
        return sb.toString();
    }
}
