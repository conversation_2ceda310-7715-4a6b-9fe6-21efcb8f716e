package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * 存储仓库dto
 */
@RpcModel("订单管理-单位个性-库房信息")
public class OrderStoreHouseDTO implements Serializable {

    private static final long serialVersionUID = -2322712397926066124L;
    /**
     * 仓库id
     */
    @RpcModelProperty("仓库id")
    private String storeHouseId;

    /**
     * 仓库名
     */
    @RpcModelProperty("仓库名")
    private String storeHouseName;

    /**
     * 仓库编号
     */
    @RpcModelProperty("仓库编号")
    private String storeHouseNumber;

    public String getStoreHouseId() {
        return storeHouseId;
    }

    public void setStoreHouseId(String storeHouseId) {
        this.storeHouseId = storeHouseId;
    }

    public String getStoreHouseName() {
        return storeHouseName;
    }

    public void setStoreHouseName(String storeHouseName) {
        this.storeHouseName = storeHouseName;
    }

    public String getStoreHouseNumber() {
        return storeHouseNumber;
    }

    public void setStoreHouseNumber(String storeHouseNumber) {
        this.storeHouseNumber = storeHouseNumber;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderStoreHouseDTO{");
        sb.append("storeHouseId='").append(storeHouseId).append('\'');
        sb.append(", storeHouseName='").append(storeHouseName).append('\'');
        sb.append(", storeHouseNumber='").append(storeHouseNumber).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
