package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 换卡接口请求入参
 * @date 2023/10/31 40
 */
public class ChangeRefFundCardOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("新的经费卡信息")
    private List<RefFundcardOrderDTO> refFundcardOrderDTOList;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<RefFundcardOrderDTO> getRefFundcardOrderDTOList() {
        return refFundcardOrderDTOList;
    }

    public void setRefFundcardOrderDTOList(List<RefFundcardOrderDTO> refFundcardOrderDTOList) {
        this.refFundcardOrderDTOList = refFundcardOrderDTOList;
    }
}
