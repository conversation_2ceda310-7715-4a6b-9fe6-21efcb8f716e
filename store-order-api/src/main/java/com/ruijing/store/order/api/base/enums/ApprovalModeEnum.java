package com.ruijing.store.order.api.base.enums;

/**
 * 验收审批方式
 */
public enum ApprovalModeEnum {
    MANUAL(0, "手动审批"),
    TIMEOUT_AUTO_APPROVAL(1, "自动审批"),
    ;

    public final Integer code;

    public final String desc;

    ApprovalModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
