package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Date 2020/5/13 0013 19:11
 * @Version 1.0
 * @Desc:描述
 */
public enum  RefFundCardOrderTypeEnum {
    /**
     * 订单
     */
    ORDER_TYPE_ENUM(1,"订单"),

    /**
     * 采购单的
     */
    PURCHASE_TYPE_ENUM(2,"采购订单"),

    /**
     * 竞价单的
     */
    Bid_TYPE_ENUM(3, "竞价订单")
    ;
    RefFundCardOrderTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String  description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
