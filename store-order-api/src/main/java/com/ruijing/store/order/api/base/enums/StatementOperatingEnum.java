package com.ruijing.store.order.api.base.enums;

/**
 * 结算操作人类型
 */
public enum StatementOperatingEnum {
    PURCHASER(1, "采购人操作"),
    SUPPLIER(2, "供应商操作"),

    /**
     * 系统操作，只有OMS数据异常处理用
     */
    SYSTEM(3, "系统操作");

    private Integer code;
    private String description;

    StatementOperatingEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public Integer getCode() {
        return code;
    }
}
