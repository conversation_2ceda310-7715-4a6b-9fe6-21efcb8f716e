package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @author: zhu<PERSON>
 * @date : 2020/2/20 10:29 上午
 * @description: 搜索分页结果DTO
 */
public class SearchPageResultDTO<T> implements Serializable {

    private static final long serialVersionUID = 6768298327860152247L;

    private List<T> recordList;

    private Long totalHits;

    public Long getTotalHits() {
        return totalHits;
    }

    public void setTotalHits(Long totalHits) {
        this.totalHits = totalHits;
    }

    public List<T> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<T> recordList) {
        this.recordList = recordList;
    }

    @Override
    public String toString() {
        return "SearchPageResultDTO{" +
                "recordList=" + recordList +
                ", totalHits=" + totalHits +
                '}';
    }
}
