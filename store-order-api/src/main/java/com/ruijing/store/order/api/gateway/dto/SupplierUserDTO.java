package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2020/9/7 10:16 上午
 * @description: 供应商用户信息
 */
public class SupplierUserDTO implements Serializable {

    private static final long serialVersionUID = -4876409999549132192L;

    @RpcModelProperty("用户名称")
    private String name;

    @RpcModelProperty("用户电话")
    private String phone;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
