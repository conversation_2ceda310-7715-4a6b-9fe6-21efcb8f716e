package com.ruijing.store.order.api.base.docking.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

public class ThirdPartQueryOrderInvoiceResponseDTO implements Serializable {

    private static final long serialVersionUID = 8014859463587388641L;

    @RpcModelProperty("发票号")
    private String invoiceNo;

    @RpcModelProperty("发票图片url")
    private String invoiceUrl;

    @RpcModelProperty("开票日期")
    private Date invoiceDate;

    @RpcModelProperty("发票摘要")
    private String invoiceRemark;

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
    }
}
