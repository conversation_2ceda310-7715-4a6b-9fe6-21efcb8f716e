package com.ruijing.store.order.api.base.enums;


/**
 * @description: 订单状态枚举
 * @author: zhuk
 * @create: 2019-07-10 15:25
 **/
public enum OrderStatusEnum {

    /**
     * 订单状态
     */
    WaitingForDockingConfirm(0,"待对接方确认"),
    DeckingFail(-1,"对接失效"),
    Close(3, "订单关闭"),
    WaitingForDelivery(4, "待发货"),
    WaitingForReceive(5, "待收货"),
    WaitingForStatement_1(6, "待结算"),
    Assess(7, "已评价"),
    WaitingForConfirm(8, "待确认"),
    PurchaseApplyToCancel(9, "采购人申请取消"),
    Statementing_1(10, "结算中"),
    Finish(11, "完成"),

    /**
     * 南方医 验收审批 异步 更改经费卡中 的状态
     *
     */
    changeingFundCar(16,"待结算经费"),

    /**
     * 南方医 验收审批 异步更改经费卡失败 的状态
     */
    changeFundCarFailed(17,"结算经费失败"),

    /**
     *     新增状态 供应商申请取消
     */
    SupplierApplyToCancel(19, "供应商申请取消"),
    OrderReceiveApproval(20, "订单验收审批"),

    /**
     * 中大状态，结算驳回后 订单就会回到这个状态
     */
    OrderRejectForStatement(21, "统一报帐人驳回"),

    /**
     * 中大状态，二级验收审批
     */
    OrderReceiveApprovalTwo(22, "待职能部门复核"),

    /**
     *  中大状态，二级验收审批（审批驳回状态）
     */
    ORDER_RECEIVE_APPROVAL_LEVEL_TWO_REJECT(23, "职能部门驳回"),

    /**
     * 平台运营商核实中，中大专用。给我们平台运营审批
     */
    PLATFORM_OPERATOR_APPROVAL(24, "运营商核实中"),

    /**
     * 运营商核实不通过，只能退货
     */
    PLATFORM_OPERATOR_APPROVAL_REJECT(25, "运营商核实不通过"),

    ORDER_SPLIT_UP(30, "订单已拆分");

    public final Integer value;
    public final String name;

    OrderStatusEnum(int value, String msg) {
        this.value = value;
        this.name = msg;
    }

    public Integer getValue()
    {
        return this.value;
    }

    public String getName()
    {
        return this.name;
    }

    public static OrderStatusEnum get(Integer type) {
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getValue().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
