package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2019/12/21 2:54 下午
 * @description:
 */
public class OrderCategoryAmountDTO implements Serializable {

    private static final long serialVersionUID = -47627567509344022L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 测试分析费
     */
    private BigDecimal analyzeAmount = BigDecimal.ZERO;

    /**
     * 试剂耗材费
     */
    private BigDecimal reagentAmount = BigDecimal.ZERO;

    /**
     * 服务 金额
     */
    private BigDecimal serviceAmount = BigDecimal.ZERO;

    /**
     * 耗材 金额
     */
    private BigDecimal materialAmount = BigDecimal.ZERO;

    /**
     * 试剂 金额
     */
    private BigDecimal potionsAmount = BigDecimal.ZERO;

    /**
     * 动物 金额
     */
    private BigDecimal animalAmount = BigDecimal.ZERO;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAnalyzeAmount() {
        return analyzeAmount;
    }

    public void setAnalyzeAmount(BigDecimal analyzeAmount) {
        this.analyzeAmount = analyzeAmount;
    }

    public BigDecimal getReagentAmount() {
        return reagentAmount;
    }

    public void setReagentAmount(BigDecimal reagentAmount) {
        this.reagentAmount = reagentAmount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public BigDecimal getMaterialAmount() {
        return materialAmount;
    }

    public void setMaterialAmount(BigDecimal materialAmount) {
        this.materialAmount = materialAmount;
    }

    public BigDecimal getPotionsAmount() {
        return potionsAmount;
    }

    public void setPotionsAmount(BigDecimal potionsAmount) {
        this.potionsAmount = potionsAmount;
    }

    public BigDecimal getAnimalAmount() {
        return animalAmount;
    }

    public void setAnimalAmount(BigDecimal animalAmount) {
        this.animalAmount = animalAmount;
    }
}
