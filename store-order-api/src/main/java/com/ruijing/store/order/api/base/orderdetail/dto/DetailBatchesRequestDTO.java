package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

@Deprecated
public class DetailBatchesRequestDTO implements Serializable {

    private static final long serialVersionUID = -2982414982505438591L;

    @RpcModelProperty("订单明细批次")
    List<DetailBatchesDTO> orderDetailBatchesList;

    public List<DetailBatchesDTO> getOrderDetailBatchesList() {
        return orderDetailBatchesList;
    }

    public void setOrderDetailBatchesList(List<DetailBatchesDTO> orderDetailBatchesList) {
        this.orderDetailBatchesList = orderDetailBatchesList;
    }
}

