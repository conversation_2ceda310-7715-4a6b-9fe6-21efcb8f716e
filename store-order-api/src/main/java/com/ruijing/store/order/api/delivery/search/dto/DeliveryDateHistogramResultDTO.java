package com.ruijing.store.order.api.delivery.search.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-03-29 18:52
 * @description:
 **/
public class DeliveryDateHistogramResultDTO implements Serializable {

    private static final long serialVersionUID = -6021648230288675189L;

    /**
     * 需要先聚合的字段
     */
    private String aggFieldName;

    /**
     * 时间字符串
     */
    private String dateString;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 金额
     */
    private Double amount;

    /**
     * 订单数量
     */
    private Double count;

    public String getAggFieldName() {
        return aggFieldName;
    }

    public void setAggFieldName(String aggFieldName) {
        this.aggFieldName = aggFieldName;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryDateHistogramResultDTO.class.getSimpleName() + "[", "]")
                .add("aggFieldName='" + aggFieldName + "'")
                .add("dateString='" + dateString + "'")
                .add("timestamp=" + timestamp)
                .add("amount=" + amount)
                .add("count=" + count)
                .toString();
    }
}
