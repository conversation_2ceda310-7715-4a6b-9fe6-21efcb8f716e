package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:zhouxin
 * @Date:2021/5/31
 * @Version:
 * @Desc:订单图片DTO
 */
public class OrderPicDTO implements Serializable {

    private static final long serialVersionUID = -4524623463464459699L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 验收图片
     */
    private String picUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderPicDTO{");
        sb.append("id=").append(id);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", picUrl='").append(picUrl).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
