package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @description: 订单收货后的出参
 * @author: zhong<PERSON><PERSON><PERSON>
 * @create: 2021/3/23 15:51
 **/
public class ReceiptOrderResponseDO implements Serializable {
    private static final long serialVersionUID = -9100839301015957547L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 入库结果
     */
    private WarehouseResultDTO warehouseResultDTO;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public WarehouseResultDTO getWarehouseResultDTO() {
        return warehouseResultDTO;
    }

    public void setWarehouseResultDTO(WarehouseResultDTO warehouseResultDTO) {
        this.warehouseResultDTO = warehouseResultDTO;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ReceiptOrderResponseDO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("warehouseResultDTO=" + warehouseResultDTO)
                .toString();
    }
}
