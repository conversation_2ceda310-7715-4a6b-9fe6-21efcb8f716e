package com.ruijing.store.order.api.base.enums;

/**
 * 原先在store-gaea-service的枚举
 */
public enum CategoryTagTypeEnum {
    FEE_TAG_TYPE(0, "测试分析费，实验耗材费"),
    CATEGORY_TAG_TYPE(1, "服务，耗材，试剂，动物");

    private final Integer value;
    private final String desc;

    private CategoryTagTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static final CategoryTagTypeEnum getByType(Integer type) {
        switch (type) {
            case 0:
                return FEE_TAG_TYPE;
            case 1:
                return CATEGORY_TAG_TYPE;
            default:
                return null;
        }
    }

    public Integer getValue() {
        return this.value;
    }
}