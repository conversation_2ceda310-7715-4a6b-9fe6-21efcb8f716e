package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/16 17:42
 * @Description 采购人中心出库单打印数组，仅限即入即出的
 **/
public class OrderOutWarehousePrintDTO implements Serializable {

    private static final long serialVersionUID = 6816298039645363896L;

    @RpcModelProperty(value = "出库单Id")
    private Integer outWarehouseApplicationId;

    @RpcModelProperty(value = "出库单号")
    private String outWarehouseApplicationNo;

    @RpcModelProperty(value = "出库申请时间")
    private Long outWarehouseApplicationTime;

    @RpcModelProperty(value = "出库申请人")
    private String outWarehouseApplicant;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "出库单状态（状态0未出库，1已出库）")
    private  Integer status;

    @RpcModelProperty(value = "申请出库商品状态名称（未出库、已出库）")
    private String statusName;

    @RpcModelProperty(value = "出库日期，时间戳格式")
    private Long outWarehouseTime;

    @RpcModelProperty(value = "订单号对应条形码(base64编码后的字符串)")
    private String orderNoBarcode;

    @RpcModelProperty(value = "出库单号对应条形码")
    private String exitNoBarcode;

    @RpcModelProperty(value = "出库商品信息列表")
    private List<OrderOutWarehouseProductInfoDTO> orderOutWarehouseProductInfoDTOList;

    public Integer getOutWarehouseApplicationId() {
        return outWarehouseApplicationId;
    }

    public OrderOutWarehousePrintDTO setOutWarehouseApplicationId(Integer outWarehouseApplicationId) {
        this.outWarehouseApplicationId = outWarehouseApplicationId;
        return this;
    }

    public String getOutWarehouseApplicationNo() {
        return outWarehouseApplicationNo;
    }

    public OrderOutWarehousePrintDTO setOutWarehouseApplicationNo(String outWarehouseApplicationNo) {
        this.outWarehouseApplicationNo = outWarehouseApplicationNo;
        return this;
    }

    public Long getOutWarehouseApplicationTime() {
        return outWarehouseApplicationTime;
    }

    public OrderOutWarehousePrintDTO setOutWarehouseApplicationTime(Long outWarehouseApplicationTime) {
        this.outWarehouseApplicationTime = outWarehouseApplicationTime;
        return this;
    }

    public String getOutWarehouseApplicant() {
        return outWarehouseApplicant;
    }

    public OrderOutWarehousePrintDTO setOutWarehouseApplicant(String outWarehouseApplicant) {
        this.outWarehouseApplicant = outWarehouseApplicant;
        return this;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public OrderOutWarehousePrintDTO setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public OrderOutWarehousePrintDTO setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public OrderOutWarehousePrintDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getStatusName() {
        return statusName;
    }

    public OrderOutWarehousePrintDTO setStatusName(String statusName) {
        this.statusName = statusName;
        return this;
    }

    public Long getOutWarehouseTime() {
        return outWarehouseTime;
    }

    public OrderOutWarehousePrintDTO setOutWarehouseTime(Long outWarehouseTime) {
        this.outWarehouseTime = outWarehouseTime;
        return this;
    }

    public String getOrderNoBarcode() {
        return orderNoBarcode;
    }

    public OrderOutWarehousePrintDTO setOrderNoBarcode(String orderNoBarcode) {
        this.orderNoBarcode = orderNoBarcode;
        return this;
    }

    public String getExitNoBarcode() {
        return exitNoBarcode;
    }

    public OrderOutWarehousePrintDTO setExitNoBarcode(String exitNoBarcode) {
        this.exitNoBarcode = exitNoBarcode;
        return this;
    }

    public List<OrderOutWarehouseProductInfoDTO> getOrderOutWarehouseProductInfoVOList() {
        return orderOutWarehouseProductInfoDTOList;
    }

    public OrderOutWarehousePrintDTO setOrderOutWarehouseProductInfoVOList(List<OrderOutWarehouseProductInfoDTO> orderOutWarehouseProductInfoDTOList) {
        this.orderOutWarehouseProductInfoDTOList = orderOutWarehouseProductInfoDTOList;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOutWarehousePrintDTO{");
        sb.append("outWarehouseApplicationId=").append(outWarehouseApplicationId);
        sb.append(", outWarehouseApplicationNo='").append(outWarehouseApplicationNo).append('\'');
        sb.append(", outWarehouseApplicationTime=").append(outWarehouseApplicationTime);
        sb.append(", outWarehouseApplicant='").append(outWarehouseApplicant).append('\'');
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName='").append(warehouseName).append('\'');
        sb.append(", status=").append(status);
        sb.append(", statusName='").append(statusName).append('\'');
        sb.append(", outWarehouseTime=").append(outWarehouseTime);
        sb.append(", orderNoBarcode='").append(orderNoBarcode).append('\'');
        sb.append(", exitNoBarcode='").append(exitNoBarcode).append('\'');
        sb.append(", orderOutWarehouseProductInfoDTOList=").append(orderOutWarehouseProductInfoDTOList);
        sb.append('}');
        return sb.toString();
    }
}
