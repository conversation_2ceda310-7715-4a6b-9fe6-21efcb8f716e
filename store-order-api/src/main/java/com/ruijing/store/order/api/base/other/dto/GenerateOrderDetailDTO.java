package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2025-04-14 10:36
 * @description:
 */
public class GenerateOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 875734285507133729L;

    /**
     *订单详情id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer fmasterid;

    /**
     * 招标日期
     */
    private Date fbiddate;

    /**
     * 订单b编号
     */
    private String fdetailno;

    /**
     *分类id
     */
    private Integer categoryid;

    /**
     * 商品分类
     */
    private String fclassification;

    /**
     * SPU，即商品+规格组合下的唯一编码
     */
    @ModelProperty("SPU")
    private String fgoodcode;

    /**
     * 原货号，平台编码。即商品纬度下的唯一编码（同规格时仍相同）
     */
    @ModelProperty("平台编码")
    private String productCode;

    /**
     * 商品名称
     */
    private String fgoodname;

    /**
     * 参考品牌
     */
    private String fbrand;

    /**
     * 规格
     */
    private String fspec;

    /**
     * 单位
     */
    private String funit;

    /**
     * 数量
     */
    private BigDecimal fquantity;

    /**
     * 招标价格
     */
    private BigDecimal fbidprice;

    /**
     * 招标总价格
     */
    private BigDecimal fbidamount;

    /**
     * 图片位置
     */
    private String fpicpath;

    private BigDecimal fremainquantity;

    private Integer fbrandid;

    private Integer tsuppmerpassid;

    private BigDecimal fcancelquantity;

    private BigDecimal fcancelamount;

    /**
     *产品id
     */
    private Long productSn;

    /**
     * 退货状态
     */
    private Integer returnStatus;

    /**
     * 退货状态
     */
    private Double returnAmount;

    /**
     * 商品项 原价
     */
    private BigDecimal originalAmount;

    /**
     * 商品单价
     */
    private BigDecimal originalPrice;

    /**
     * 是否修改价格
     */
    private Boolean modifyPrice;

    /**
     * 货期
     */
    private Integer deliveryTime;

    /**
     * 优惠券  商品项余额
     */
    private BigDecimal remainderPrice;

    /**
     * 协议价
     */
    private BigDecimal negotiatedPrice;

    /**
     * 中大二级分类id
     */
    private Integer sysuCategoryId;

    /**
     * 采购目录ID
     */
    private Integer categoryDirectoryId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private BigDecimal carryFee;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    private String firstCategoryName;

    /**
     * 二级分类id
     */
    private Integer secondCategoryId;

    /**
     * 二级分类名称
     */
    private String secondCategoryName;

    /**
     * 报账类型
     *
     * tag_type = 0;
     * tag_value  com.reagent.tags.global.enums.FeeType
     */
    private String feeTypeTag;

    /**
     * 分类标签
     * tag_type = 1;
     * com.reagent.tags.global.enums.FirstTierCategory
     */
    private String categoryTag;

    /**
     * 危化品标签Id
     */
    private Integer dangerousTypeId;

    /**
     * 危化品标签名称
     */
    private String dangerousTypeName;

    /**
     * 管制品类型Id
     */
    private Integer regulatoryTypeId;

    /**
     *管制品类型名称
     */
    private String regulatoryTypeName;

    /**
     *Cas号
     */
    private String casno;

    /**
     *供应商Id
     */
    private Integer suppId;

    /**
     *供应商名称
     */
    private String suppName;

    /**
     * 供应商编号
     */
    private String suppCode;

    /**
     * 品牌英文名
     */
    private String brandEname;

    /**
     * 订单详情额外信息
     */
    private List<OrderDetailExtraDTO> orderDetailExtraDTOList;

    @ModelProperty("商品多规格")
    private String attribute;

    @ModelProperty
    private String dockingNumber;


    public Integer getId() {
        return id;
    }

    public GenerateOrderDetailDTO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getFmasterid() {
        return fmasterid;
    }

    public GenerateOrderDetailDTO setFmasterid(Integer fmasterid) {
        this.fmasterid = fmasterid;
        return this;
    }

    public Date getFbiddate() {
        return fbiddate;
    }

    public GenerateOrderDetailDTO setFbiddate(Date fbiddate) {
        this.fbiddate = fbiddate;
        return this;
    }

    public String getFdetailno() {
        return fdetailno;
    }

    public GenerateOrderDetailDTO setFdetailno(String fdetailno) {
        this.fdetailno = fdetailno;
        return this;
    }

    public Integer getCategoryid() {
        return categoryid;
    }

    public GenerateOrderDetailDTO setCategoryid(Integer categoryid) {
        this.categoryid = categoryid;
        return this;
    }

    public String getFclassification() {
        return fclassification;
    }

    public GenerateOrderDetailDTO setFclassification(String fclassification) {
        this.fclassification = fclassification;
        return this;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public GenerateOrderDetailDTO setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public GenerateOrderDetailDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public GenerateOrderDetailDTO setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
        return this;
    }

    public String getFbrand() {
        return fbrand;
    }

    public GenerateOrderDetailDTO setFbrand(String fbrand) {
        this.fbrand = fbrand;
        return this;
    }

    public String getFspec() {
        return fspec;
    }

    public GenerateOrderDetailDTO setFspec(String fspec) {
        this.fspec = fspec;
        return this;
    }

    public String getFunit() {
        return funit;
    }

    public GenerateOrderDetailDTO setFunit(String funit) {
        this.funit = funit;
        return this;
    }

    public BigDecimal getFquantity() {
        return fquantity;
    }

    public GenerateOrderDetailDTO setFquantity(BigDecimal fquantity) {
        this.fquantity = fquantity;
        return this;
    }

    public BigDecimal getFbidprice() {
        return fbidprice;
    }

    public GenerateOrderDetailDTO setFbidprice(BigDecimal fbidprice) {
        this.fbidprice = fbidprice;
        return this;
    }

    public BigDecimal getFbidamount() {
        return fbidamount;
    }

    public GenerateOrderDetailDTO setFbidamount(BigDecimal fbidamount) {
        this.fbidamount = fbidamount;
        return this;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public GenerateOrderDetailDTO setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
        return this;
    }

    public BigDecimal getFremainquantity() {
        return fremainquantity;
    }

    public GenerateOrderDetailDTO setFremainquantity(BigDecimal fremainquantity) {
        this.fremainquantity = fremainquantity;
        return this;
    }

    public Integer getFbrandid() {
        return fbrandid;
    }

    public GenerateOrderDetailDTO setFbrandid(Integer fbrandid) {
        this.fbrandid = fbrandid;
        return this;
    }

    public Integer getTsuppmerpassid() {
        return tsuppmerpassid;
    }

    public GenerateOrderDetailDTO setTsuppmerpassid(Integer tsuppmerpassid) {
        this.tsuppmerpassid = tsuppmerpassid;
        return this;
    }

    public BigDecimal getFcancelquantity() {
        return fcancelquantity;
    }

    public GenerateOrderDetailDTO setFcancelquantity(BigDecimal fcancelquantity) {
        this.fcancelquantity = fcancelquantity;
        return this;
    }

    public BigDecimal getFcancelamount() {
        return fcancelamount;
    }

    public GenerateOrderDetailDTO setFcancelamount(BigDecimal fcancelamount) {
        this.fcancelamount = fcancelamount;
        return this;
    }

    public Long getProductSn() {
        return productSn;
    }

    public GenerateOrderDetailDTO setProductSn(Long productSn) {
        this.productSn = productSn;
        return this;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public GenerateOrderDetailDTO setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
        return this;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public GenerateOrderDetailDTO setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
        return this;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public GenerateOrderDetailDTO setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
        return this;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public GenerateOrderDetailDTO setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
        return this;
    }

    public Boolean getModifyPrice() {
        return modifyPrice;
    }

    public GenerateOrderDetailDTO setModifyPrice(Boolean modifyPrice) {
        this.modifyPrice = modifyPrice;
        return this;
    }

    public Integer getDeliveryTime() {
        return deliveryTime;
    }

    public GenerateOrderDetailDTO setDeliveryTime(Integer deliveryTime) {
        this.deliveryTime = deliveryTime;
        return this;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public GenerateOrderDetailDTO setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
        return this;
    }

    public BigDecimal getNegotiatedPrice() {
        return negotiatedPrice;
    }

    public GenerateOrderDetailDTO setNegotiatedPrice(BigDecimal negotiatedPrice) {
        this.negotiatedPrice = negotiatedPrice;
        return this;
    }

    public Integer getSysuCategoryId() {
        return sysuCategoryId;
    }

    public GenerateOrderDetailDTO setSysuCategoryId(Integer sysuCategoryId) {
        this.sysuCategoryId = sysuCategoryId;
        return this;
    }

    public Integer getCategoryDirectoryId() {
        return categoryDirectoryId;
    }

    public GenerateOrderDetailDTO setCategoryDirectoryId(Integer categoryDirectoryId) {
        this.categoryDirectoryId = categoryDirectoryId;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public GenerateOrderDetailDTO setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public GenerateOrderDetailDTO setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
        return this;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public GenerateOrderDetailDTO setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public GenerateOrderDetailDTO setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
        return this;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public GenerateOrderDetailDTO setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
        return this;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public GenerateOrderDetailDTO setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public GenerateOrderDetailDTO setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
        return this;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public GenerateOrderDetailDTO setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
        return this;
    }

    public Integer getDangerousTypeId() {
        return dangerousTypeId;
    }

    public GenerateOrderDetailDTO setDangerousTypeId(Integer dangerousTypeId) {
        this.dangerousTypeId = dangerousTypeId;
        return this;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public GenerateOrderDetailDTO setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
        return this;
    }

    public Integer getRegulatoryTypeId() {
        return regulatoryTypeId;
    }

    public GenerateOrderDetailDTO setRegulatoryTypeId(Integer regulatoryTypeId) {
        this.regulatoryTypeId = regulatoryTypeId;
        return this;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public GenerateOrderDetailDTO setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
        return this;
    }

    public String getCasno() {
        return casno;
    }

    public GenerateOrderDetailDTO setCasno(String casno) {
        this.casno = casno;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public GenerateOrderDetailDTO setSuppId(Integer suppId) {
        this.suppId = suppId;
        return this;
    }

    public String getSuppName() {
        return suppName;
    }

    public GenerateOrderDetailDTO setSuppName(String suppName) {
        this.suppName = suppName;
        return this;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public GenerateOrderDetailDTO setSuppCode(String suppCode) {
        this.suppCode = suppCode;
        return this;
    }

    public String getBrandEname() {
        return brandEname;
    }

    public GenerateOrderDetailDTO setBrandEname(String brandEname) {
        this.brandEname = brandEname;
        return this;
    }

    public List<OrderDetailExtraDTO> getOrderDetailExtraDTOList() {
        return orderDetailExtraDTOList;
    }

    public GenerateOrderDetailDTO setOrderDetailExtraDTOList(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        this.orderDetailExtraDTOList = orderDetailExtraDTOList;
        return this;
    }

    public String getAttribute() {
        return attribute;
    }

    public GenerateOrderDetailDTO setAttribute(String attribute) {
        this.attribute = attribute;
        return this;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public GenerateOrderDetailDTO setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
        return this;
    }
}
