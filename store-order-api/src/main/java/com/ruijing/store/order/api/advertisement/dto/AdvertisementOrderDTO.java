package com.ruijing.store.order.api.advertisement.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 广告投放订单信息类
 * @date 2023/9/4 16:03
 */
public class AdvertisementOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("生成订单时间")
    private Date orderDate;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单状态", enumLink = "com.ruijing.store.order.api.base.enums.OrderStatusEnum")
    private Integer orderStatus;

    @RpcModelProperty("订单金额")
    private BigDecimal orderAmount;

    @RpcModelProperty("组织id")
    private Integer orgId;

    @RpcModelProperty("组织名称")
    private String orgName;

    @RpcModelProperty("课题组id")
    private Integer departmentId;

    @RpcModelProperty("课题组")
    private String departmentName;

    @RpcModelProperty("用户名")
    private String buyerName;

    @RpcModelProperty("用户id")
    private Integer buyerId;

    @RpcModelProperty("用户邮箱")
    private String buyerEmail;

    @RpcModelProperty("用户手机")
    private String buyerMobile;

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerEmail() {
        return buyerEmail;
    }

    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }

    public String getBuyerMobile() {
        return buyerMobile;
    }

    public void setBuyerMobile(String buyerMobile) {
        this.buyerMobile = buyerMobile;
    }
}
