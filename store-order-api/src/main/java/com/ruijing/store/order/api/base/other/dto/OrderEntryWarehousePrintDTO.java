package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 入库单打印模型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 18:36
 **/
public class OrderEntryWarehousePrintDTO implements Serializable {

    private static final long serialVersionUID = -2732592676048011788L;

    /**
     * 关联订单号
     */
    @RpcModelProperty("关联订单号")
    private String orderNo;

    /**
     * 入库单号
     */
    @RpcModelProperty("入库单号")
    private String entryNo;

    /**
     * 申请入库的库房名称
     */
    @RpcModelProperty("申请入库的库房名称")
    private String roomName;

    /**
     * 入库申请时间
     */
    @RpcModelProperty("入库申请时间")
    private Date applyDate;

    /**
     * 入库日志
     */
    @RpcModelProperty("入库日志，废弃")
    @Deprecated
    private OrderEntryWarehousePrintLogDTO orderEntryWarehousePrintLog;

    /**
     * 入库日志
     */
    @RpcModelProperty("入库日志")
    private List<OrderWarehousePrintEntryLogDTO> orderWarehousePrintEntryLogDTOList;

    /**
     * 入库商品详情
     */
    @RpcModelProperty("入库商品详情")
    private List<OrderEntryWarehouseProductPrintDTO> orderEntryWarehouseProductPrintList;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public void setEntryNo(String entryNo) {
        this.entryNo = entryNo;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public List<OrderEntryWarehouseProductPrintDTO> getOrderEntryWarehouseProductPrintList() {
        return orderEntryWarehouseProductPrintList;
    }

    public void setOrderEntryWarehouseProductPrintList(List<OrderEntryWarehouseProductPrintDTO> orderEntryWarehouseProductPrintList) {
        this.orderEntryWarehouseProductPrintList = orderEntryWarehouseProductPrintList;
    }

    public OrderEntryWarehousePrintLogDTO getOrderEntryWarehousePrintLog() {
        return orderEntryWarehousePrintLog;
    }

    public void setOrderEntryWarehousePrintLog(OrderEntryWarehousePrintLogDTO orderEntryWarehousePrintLog) {
        this.orderEntryWarehousePrintLog = orderEntryWarehousePrintLog;
    }

    public List<OrderWarehousePrintEntryLogDTO> getOrderWarehousePrintEntryLogDTOList() {
        return orderWarehousePrintEntryLogDTOList;
    }

    public void setOrderWarehousePrintEntryLogDTOList(List<OrderWarehousePrintEntryLogDTO> orderWarehousePrintEntryLogDTOList) {
        this.orderWarehousePrintEntryLogDTOList = orderWarehousePrintEntryLogDTOList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderEntryWarehousePrintDTO{");
        sb.append("orderNo='").append(orderNo).append('\'');
        sb.append(", entryNo='").append(entryNo).append('\'');
        sb.append(", roomName='").append(roomName).append('\'');
        sb.append(", applyDate=").append(applyDate);
        sb.append(", orderEntryWarehousePrintLog=").append(orderEntryWarehousePrintLog);
        sb.append(", orderWarehousePrintEntryLogDTOList=").append(orderWarehousePrintEntryLogDTOList);
        sb.append(", orderEntryWarehouseProductPrintList=").append(orderEntryWarehouseProductPrintList);
        sb.append('}');
        return sb.toString();
    }
}
