package com.ruijing.store.order.api.base.enums;

import java.util.Objects;

/**
 * 订单标签枚举,支持搜索筛选
 */
public enum OrderTagEnum {

    /**
     * 华西定制
     */
    ORG_AGREEMENT(1, "医院遴选"),
    NON_ORG_AGREEMENT(2, "非遴选采购"),
    SCIENCE_SERVICE(3, "科研服务"),
    SELF_PURCHASE_RECORD(4, "自购备案"),
    PURCHASE_RECORD(5, "招采备案"),
    PLATFORM_BIDDING(6, "平台竞价"),

    ;

    private final Integer value;

    private final String desc;

    public static OrderTagEnum getByValue(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (OrderTagEnum orderTagEnum : OrderTagEnum.values()) {
            if (Objects.equals(orderTagEnum.getValue(), value)) {
                return orderTagEnum;
            }
        }
        return null;
    }

    OrderTagEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "OrderTagEnum{" +
                "value=" + value +
                ", desc='" + desc + '\'' +
                '}';
    }
}
