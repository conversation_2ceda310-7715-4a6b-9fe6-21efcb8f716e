package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

@RpcModel("图片DTO")
public class PictureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("图片地址")
    private String pictureUrl;

    @RpcModelProperty("图片名称")
    private String pictureName;

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public String getPictureName() {
        return pictureName;
    }

    public void setPictureName(String pictureName) {
        this.pictureName = pictureName;
    }

    @Override
    public String toString() {
        return "PictureDTO{" +
                "pictureUrl='" + pictureUrl + '\'' +
                ", pictureName='" + pictureName + '\'' +
                '}';
    }

    public PictureDTO(String pictureUrl, String pictureName) {
        this.pictureUrl = pictureUrl;
        this.pictureName = pictureName;
    }

    public PictureDTO() {
    }
}