package com.ruijing.store.order.api.base.orderapprovallog.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderAllRelateLogDTO;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;

import java.util.List;

@RpcApi("订单审批日志RPC服务")
public interface OrderApprovalLogRPCService {
    /**
     * 新增订单 审批日志
     * @param request   日志入参
     * @return
     */
    @RpcMethod("新增订单 审批日志, orderId, approveStatus, operatorId必填")
    RemoteResponse<Boolean> insertOrderApproval(OrderApprovalLogDTO request);

    /**
     * 批量新增 订单审批日志
     * @param request   日志入参
     */
    @RpcMethod("批量新增订单 审批日志, orderId, approveStatus, operatorId必填")
    RemoteResponse<Integer> insertOrderApprovalLogList(List<OrderApprovalLogDTO> request);

    @RpcMethod("保存订单操作日志, orderId, approveStatus, operatorId必填")
    RemoteResponse<Boolean> saveOrUpdateApprovalLog(OrderApprovalLogDTO request);

    /**
     * 批量查询 订单审批日志 上限500
     * @param request   查询参数
     */
    @RpcMethod("批量查询订单审批日志 最多一次查500个订单")
    RemoteResponse<List<OrderApprovalLogDTO>> listOrderApprovalLog(OrderApprovalRequestDTO request);

    @RpcMethod(value = "批量查询采购人中心的日志", notes = "orderIdList单次最多200条")
    RemoteResponse<List<OrderAllRelateLogDTO>> listOrderLogInPurchaseCenter(List<Integer> orderIdList);
} 
