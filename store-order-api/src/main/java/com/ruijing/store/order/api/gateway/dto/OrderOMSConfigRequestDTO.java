package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Set;

/**
 * @description: 查询订单OMS配置的 入参
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/23 17:10
 **/
public class OrderOMSConfigRequestDTO implements Serializable {

    private static final long serialVersionUID = 7564409343049262893L;

    @RpcModelProperty("配置编码")
    private Set<String> configCode;

    public Set<String> getConfigCode() {
        return configCode;
    }

    public void setConfigCode(Set<String> configCode) {
        this.configCode = configCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOMSConfigRequestDTO{");
        sb.append(", configCode=").append(configCode);
        sb.append('}');
        return sb.toString();
    }
}
