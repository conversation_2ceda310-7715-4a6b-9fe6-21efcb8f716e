package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 供应商拉取的订单数据DTo
 * @author: zhuk
 * @create: 2019-11-13 18:38
 **/
public class SuppOrderPullDTO implements Serializable{

    private static final long serialVersionUID = 205804897572272746L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 供应商Id
     */
    private Integer suppId;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 是否退货  0未退货 1 退货
     */
    private Integer hasReturn;

    /**
     * 订单金额
     */
    private double orderAmount;

    /**
     * 订单确认时间
     */
    private Date confirmTime;

    /**
     * 订单生成时间
     */
    private Date orderGenerateTime;

    /**
     * 订单发货时间
     */
    private Date deliveryTime;

    /**
     * 订单申请退货时间
     */
    private Date returnApplyTime;

    /**
     * 供应商确认退货时间
     */
    private Date returnConfirmTime;

    /**
     * 退货单号
     */
    private String returnOrderNo;

    /**
     * 退货金额
     */
    private Double returnAmount;

    /**
     * 订单更新时间
     */
    private Date orderUpdateTime;

    /**
     * 退货单更新时间
     */
    private Date returnOrderUpdateTime;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getHasReturn() {
        return hasReturn;
    }

    public void setHasReturn(Integer hasReturn) {
        this.hasReturn = hasReturn;
    }

    public double getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(double orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Date getOrderGenerateTime() {
        return orderGenerateTime;
    }

    public void setOrderGenerateTime(Date orderGenerateTime) {
        this.orderGenerateTime = orderGenerateTime;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Date getReturnApplyTime() {
        return returnApplyTime;
    }

    public void setReturnApplyTime(Date returnApplyTime) {
        this.returnApplyTime = returnApplyTime;
    }

    public Date getReturnConfirmTime() {
        return returnConfirmTime;
    }

    public void setReturnConfirmTime(Date returnConfirmTime) {
        this.returnConfirmTime = returnConfirmTime;
    }

    public String getReturnOrderNo() {
        return returnOrderNo;
    }

    public void setReturnOrderNo(String returnOrderNo) {
        this.returnOrderNo = returnOrderNo;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Date getOrderUpdateTime() {
        return orderUpdateTime;
    }

    public void setOrderUpdateTime(Date orderUpdateTime) {
        this.orderUpdateTime = orderUpdateTime;
    }

    public Date getReturnOrderUpdateTime() {
        return returnOrderUpdateTime;
    }

    public void setReturnOrderUpdateTime(Date returnOrderUpdateTime) {
        this.returnOrderUpdateTime = returnOrderUpdateTime;
    }

    @Override
    public String toString() {
        return "SuppOrderPullDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", suppId=" + suppId +
                ", status=" + status +
                ", hasReturn=" + hasReturn +
                ", orderAmount=" + orderAmount +
                ", confirmTime=" + confirmTime +
                ", orderGenerateTime=" + orderGenerateTime +
                ", deliveryTime=" + deliveryTime +
                ", returnApplyTime=" + returnApplyTime +
                ", returnConfirmTime=" + returnConfirmTime +
                ", returnOrderNo='" + returnOrderNo + '\'' +
                ", returnAmount=" + returnAmount +
                ", orderUpdateTime=" + orderUpdateTime +
                ", returnOrderUpdateTime=" + returnOrderUpdateTime +
                '}';
    }
}
