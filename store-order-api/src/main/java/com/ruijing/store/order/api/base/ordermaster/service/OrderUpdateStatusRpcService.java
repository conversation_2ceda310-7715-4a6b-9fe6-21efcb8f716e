package com.ruijing.store.order.api.base.ordermaster.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.annotation.Appkey;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderCancelDeliveryDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderConfirmDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderDeliveryDTO;

/**
 * @author: liwenyu
 * @createTime: 2023-10-27 16:49
 * @description:
 **/
@RpcApi("订单状态流转服务")
@Appkey(name = "store-order-service")
public interface OrderUpdateStatusRpcService {

    /**
     * 订单发货
     * @param orderDeliveryDTO 发货参数
     * @return 是否成功
     */
    @RpcMethod("订单发货")
    RemoteResponse<Boolean> orderDelivery(OrderDeliveryDTO orderDeliveryDTO);


    /**
     * 供应商确认订单
     * @param orderDeliveryDTO 确认订单入参
     * @return 是否成功
     */
    @RpcMethod("供应商确认订单")
    RemoteResponse<Boolean> suppConfirmOrder(OrderConfirmDTO orderDeliveryDTO);

    @RpcMethod("供应商取消发货")
    RemoteResponse<Boolean> suppCancelDelivery(OrderCancelDeliveryDTO orderCancelDeliveryDTO);

}
