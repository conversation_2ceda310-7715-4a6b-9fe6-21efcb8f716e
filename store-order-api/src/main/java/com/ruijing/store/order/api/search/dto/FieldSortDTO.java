package com.ruijing.store.order.api.search.dto;

import com.ruijing.store.order.api.enums.SortOrderEnum;

import java.io.Serializable;

/**
 * @author: zhukai
 * @date : 2020/2/19 11:07 上午
 * @description: 搜索 排序字段 对象
 */
public class FieldSortDTO implements Serializable {

    private static final long serialVersionUID = -3032154260099143328L;

    /**
     * 需要排序的 字段名称
     */
    private String sortField;

    /**
     * 排序类型 升序、降序  默认升序
     */
    private SortOrderEnum sortOrder = SortOrderEnum.ASC;

    public FieldSortDTO(String sortField, SortOrderEnum sortOrder) {
        this.sortField = sortField;
        this.sortOrder = sortOrder;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public SortOrderEnum getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(SortOrderEnum sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "SortFieldDTO{" +
                "sortField='" + sortField + '\'' +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
