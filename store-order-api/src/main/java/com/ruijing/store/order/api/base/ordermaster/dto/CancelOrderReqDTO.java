package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2020/4/1 0001 17:31
 * @Version 1.0
 * @Desc:描述
 */
@RpcModel("订单管理-拒绝供应商取消订单")
public class CancelOrderReqDTO implements Serializable {
    private static final long serialVersionUID = -8268528759998600392L;
    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderMasterId;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 取消原因
     */
    @RpcModelProperty("取消原因")
    private String cancelReason;

    /**
     * 拒绝取消的原因
     */
    @RpcModelProperty("拒绝取消的原因")
    private String refuseReason;

    /**
     * 职业编号
     */
    @RpcModelProperty("职业编号")
    private String jobNumber;

    /**
     * 组织名称
     */
    @RpcModelProperty("组织id")
    private Integer orgId;

    /**
     * 组织名称
     */
    @RpcModelProperty("组织名称")
    private String orgCode;

    /**
     * 取消人id
     */
    @RpcModelProperty("取消人id")
    private String cancelManId;

    /**
     * 取消人
     */
    @RpcModelProperty("取消人")
    private String cancelMan;

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getCancelManId() {
        return cancelManId;
    }

    public void setCancelManId(String cancelManId) {
        this.cancelManId = cancelManId;
    }

    public String getCancelMan() {
        return cancelMan;
    }

    public void setCancelMan(String cancelMan) {
        this.cancelMan = cancelMan;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CancelOrderReqDTO{");
        sb.append("orderMasterId=").append(orderMasterId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", cancelReason='").append(cancelReason).append('\'');
        sb.append(", refuseReason='").append(refuseReason).append('\'');
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", cancelManId='").append(cancelManId).append('\'');
        sb.append(", cancelMan='").append(cancelMan).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
