package com.ruijing.store.order.api.base.ordermaster.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.ordermaster.dto.*;

import java.util.List;

/**
 * @description: 订单服务
 * @author: zhuk
 * @create: 2019-07-04 14:20
 **/
@RpcApi
public interface OrderMasterCommonService {

    /**
     * 根据orderMaster的 id列表 查询到订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findOrderListByIds(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据主键 更新orderMaster 值不为空 的字段
     * @param orderMasterDTO
     * @return
     */
    RemoteResponse<Integer> updateByPrimaryKeySelective(OrderMasterDTO orderMasterDTO);

    /**
     * 根据主键 修改订单状态
     * @param updateOrderStatusReqDTO
     * @return
     */
    RemoteResponse<Integer> updateStatusById(UpdateOrderStatusReqDTO updateOrderStatusReqDTO);

    /**
     * 根据订单号 修改订单经费状态
     * @param updateFundStatusReqDTO
     * @return
     */
    RemoteResponse<Integer> updateFundStatusByOrderNo(UpdateFundStatusReqDTO updateFundStatusReqDTO);

    /**
     * 供应商申请取消订单
     * @param applyCancelOrderReqDTO
     * @return
     */
    RemoteResponse<Integer> applyCancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * 根据orderMaster的 id查询订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<OrderMasterDTO> findOrderMasterById(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据订单号集合 查询 订单信息。
     * @param orderMasterCommonReqDTO
     *        orderMasterCommonReqDTO.orderMasterNoList 字段必填不能为空,且size不能超过300。
     * @return List<OrderMasterDTO>
     */
    RemoteResponse<List<OrderMasterDTO>> findOrderMasterByOrderNoList(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据订单号查询订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<OrderMasterDTO> findOrderMasterByOrderNo(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 查寻超时取消订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findByStatusAndFcanceldateLessThan(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 查寻超时取消订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findByStatusAndForderdateLessThan(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据orderMaster的 orgid 和 订单状态 查询订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findByStatusAndOrgId(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据 采购人 和订单状态 查询订单
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findOrderMasterByBuyerAndStatus(OrderMasterForCmsReq orderMasterForCmsReq);

    /**
     * 根据 ordermasterId 查询 对应的订单详情
     * @param orderDetailReq
     * @return
     */
    RemoteResponse<List<OrderDetailDTO>> findOrderDetailsByMasterId(OrderDetailReq orderDetailReq);

    /**
     * 根据id 更新订单入库状态（不执行回调等操作，仅更新状态）
     * @param updateOrderParamDTO
     * @return
     */
    RemoteResponse<Integer> updateInventoryStatusById(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 根据id 更新订单信息
     * @param updateOrderParamDTO
     * @return
     */
    RemoteResponse<Integer> updateOrderById(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 根据订单号 更新订单信息
     * @param updateOrderParamDTO
     * @return
     */
    RemoteResponse<UpdateOrderParamDTO> updateOrderByOrderNo(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 批量更新订单状态，根据订单id
     * @param requestParam
     * @return
     */
    RemoteResponse updateStatusByIdList(UpdateOrderStatusReqDTO requestParam);

    /**
     * 批量更正订单状态，根据订单id--对应OMS异常数据处理功能
     * @param requestParam 请求参数
     * @return 是否成功
     */
    RemoteResponse<Boolean> fixStatusByIdList(UpdateOrderStatusReqDTO requestParam);

    /**
     * 根据结算单id集合查询 结算订单关联关系
     * @param statementIds 结算单id结合  size< 100
     * @return
     */
    RemoteResponse<List<OrderStatementRefDTO>> findOrderBaseByStatementIds(List<Integer> statementIds);

    /**
     * 根据采购单id查询订单集合
     * @param applicationIdList 采购单id size < 100
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findOrderByApplicationIdList(OrderMasterCommonReqDTO applicationIdList);

    /**
     * 查询最大订单id接口
     * @return
     */
    @RpcMethod("查询最大订单id接口")
    RemoteResponse<Integer> findMaxOrderId();

    /**
     * 根据id分页查询订单数据, left > 0, right > 0, right - left <= 500
     * @param request
     * @return
     */
    @RpcMethod("根据id分页查询订单数据, left > 0, right > 0, right - left <= 500")
    RemoteResponse<List<OrderMasterDTO>> rangeById(OrderMasterCommonReqDTO request);

    /**
     * 根据更新时间查询最近发生更新的订单的id
     * @param request
     * @return
     */
    @RpcMethod("根据更新时间查询最近发生更新的订单的id")
    RemoteResponse<List<Integer>> findIdByUpdateTimeAfter(OrderMasterCommonReqDTO request);

    /**
     * 根据订单号集合修改竞价单id
     * @param updateBidIdParamDTO
     * @return RemoteResponse<Boolean>
     */
    @RpcMethod("根据订单号集合修改竞价单id")
    RemoteResponse<Boolean> updateBidIdByOrderNoSet(UpdateBidIdParamDTO updateBidIdParamDTO);

    /**
     * 批量更新订单结算信息接口, 需要传参： orderId， status， statementId, 单次调用最大支持100个更新
     * @param request 订单结算入参
     * @return 更新成功数量
     */
    @RpcMethod("批量更新订单结算信息接口, 需要传参： orderId， status， statementId, 单次调用最大支持100个更新")
    RemoteResponse<Integer> batchUpdateOrderStatement(List<UpdateOrderParamDTO> request);

    /**
     * 根据订单号集合修改竞价单id
     * @param request
     * @return RemoteResponse<Integer>
     */
    @RpcMethod("批量更新订单数据，数组对象里的订单orderNo必填, 批量更新单次最多支持100单")
    @Deprecated
    RemoteResponse<Integer> batchUpdateByOrderNo(List<UpdateOrderParamDTO> request);

    /**
     * 根据订单号 批量更新订单的其他字段
     *
     * @param request
     * @return
     */
    @RpcMethod("批量更新订单其他字段")
    RemoteResponse<Integer> updateFieldByOrderNo(List<UpdateOrderParamDTO> request);

    /**
     * 发送待确认邮件给供应商
     *
     * @param orderMasterDTO
     * @return
     */
    @RpcMethod("发送待确认邮件给供应商")
    RemoteResponse<Boolean> waitingConfirmMessageToSupp(OrderMasterDTO orderMasterDTO);
}
