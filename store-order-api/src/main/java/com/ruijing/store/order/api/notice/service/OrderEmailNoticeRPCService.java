package com.ruijing.store.order.api.notice.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;

/**
 * 订单邮件通知RPC服务
 */
@RpcApi("订单邮件通知RPC服务")
public interface OrderEmailNoticeRPCService {

    /**
     * 订单拆单邮件通知采购人
     * @param request
     * @return  是否成功
     */
    @RpcMethod("订单拆单邮件通知采购人, orderId必填")
    RemoteResponse<Boolean> orderSplitNotice(OrderBasicParamDTO request);
}
