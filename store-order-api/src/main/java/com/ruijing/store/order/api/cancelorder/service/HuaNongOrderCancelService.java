package com.ruijing.store.order.api.cancelorder.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.cancelorder.dto.HuaNongCancelOrderReqDTO;

/**
 * @description: 调用华农 取消订单的接口
 * @author: zhuk
 * @create: 2019-07-10 16:01
 **/

public interface HuaNongOrderCancelService {

    /**
     * 调用华农 取消订单的接口
     * @param huaNongCancelOrderReqDTO
     * @return
     */
    RemoteResponse<Boolean> cancelOrder(HuaNongCancelOrderReqDTO huaNongCancelOrderReqDTO);

}
