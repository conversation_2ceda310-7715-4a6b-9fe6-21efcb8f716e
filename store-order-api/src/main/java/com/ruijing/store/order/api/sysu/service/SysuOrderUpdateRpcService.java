package com.ruijing.store.order.api.sysu.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.sysu.dto.ChangeRefFundCardOrderRequest;
import com.ruijing.store.order.api.sysu.dto.UpdateOrderPriceRequest;

/**
 * <AUTHOR>
 * @description 中大订单相关信息更新接口
 * @date 2023/10/31 40
 */
public interface SysuOrderUpdateRpcService {

    /**
     * 订单详情价格更新接口，会关联更新订单主表总价，数量限制200条
     *
     */
    RemoteResponse<Boolean> updateOrderPrice(UpdateOrderPriceRequest request);

    /**
     * 换卡接口，根据订单id删除原关联经费卡，再插入入参中的经费卡信息
     *
     */
    RemoteResponse<Boolean> changeRefFundCardOrder(ChangeRefFundCardOrderRequest request);
}
