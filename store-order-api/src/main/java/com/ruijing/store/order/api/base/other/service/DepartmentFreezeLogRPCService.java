package com.ruijing.store.order.api.base.other.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.DepartmentFreezeLogDTO;

import java.util.List;

/**
 * 冻结课题组相关RPC接口
 */
@RpcApi(value = "冻结课题组相关RPC接口")
public interface DepartmentFreezeLogRPCService {

    /**
     * 按医院id，获取由被冻结的部门
     * @param orgId
     * @return 被冻结部门的信息
     */
    @RpcMethod("按医院id，获取由被冻结的部门")
    RemoteResponse<List<DepartmentFreezeLogDTO>> findFreezeByOrgId(Integer orgId);
}