package com.ruijing.store.order.api.sysu.service;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.api.sysu.dto.OrderRequestDTO;

import java.util.List;

/**
 * @description: 中大订单搜索服务
 * @author: zhengzhendong
 * @create: 2020-10-15 00:50
 **/
public interface SysuOrderSearchService {

    /**
     * 验收超时订单搜索
     *
     * @param orderRequestDto
     * @return
     */
    RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> overTimeOrderSearch(OrderRequestDTO orderRequestDto);

    /**
     * 结算超时订单搜索
     * @param orderRequestDTO 搜索条件
     * @return 结算超时订单
     */
    RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> statementOverTimeOverSearch(OrderRequestDTO orderRequestDTO);


    /**
     * 订单列表搜索
     * @param orderRequestDto 订单列表搜索参数
     * @return SearchPageResultDTO<OrderMasterSearchDTO>
     */
    RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> orderListSearch(OrderRequestDTO orderRequestDto);

    /**
     * 我审批的风险订单搜索
     * @param orderRequestDTO 搜索参数
     * @return 已审批的订单
     */
    PageableResponse<List<OrderMasterSearchDTO>> myApprovedRiskOrderSearch(OrderRequestDTO orderRequestDTO);
}
