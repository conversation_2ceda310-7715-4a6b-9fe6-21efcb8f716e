package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 冻结课题组记录DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/31 15:43
 **/
public class DepartmentFreezeLogDTO implements Serializable {

    private static final long serialVersionUID = 1495668867042629676L;

    /**
     * id
     */
    @RpcModelProperty("id")
    private Integer id;

    /**
     * 机构id
     */
    @RpcModelProperty("机构id")
    private Integer orgId;

    /**
     * 部门id
     */
    @RpcModelProperty("机构id")
    private Integer departmentId;

    /**
     * 冻结类型 1-结算超时冻结 2-验收超时冻结
     */
    @RpcModelProperty("冻结类型 1-结算超时冻结 2-验收超时冻结")
    private Integer type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
