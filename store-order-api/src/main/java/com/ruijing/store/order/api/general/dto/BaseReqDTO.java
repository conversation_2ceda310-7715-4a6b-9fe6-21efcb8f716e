package com.ruijing.store.order.api.general.dto;
/**
 * Created by <PERSON><PERSON><PERSON> on 2019/5/31.
 */

import java.io.Serializable;

/**
 * @program: store-order-service
 *
 * @description: 搜索请求基础对象
 *
 * @author: zhuk
 *
 * @create: 2019-05-31 09:59
 **/

public class BaseReqDTO implements Serializable {
    private Integer startHit = 0;
    private Integer pageSize = 20;

    public Integer getStartHit() {
        return startHit;
    }

    public void setStartHit(Integer startHit) {
        this.startHit = startHit;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
