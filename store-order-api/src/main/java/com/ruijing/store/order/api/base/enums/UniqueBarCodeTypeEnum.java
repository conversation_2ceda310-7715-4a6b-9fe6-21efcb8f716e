package com.ruijing.store.order.api.base.enums;

/**
 * @author: li<PERSON><PERSON>
 * @createTime: 2023-08-07 09:46
 * @description: 一物一码类型
 **/
public enum UniqueBarCodeTypeEnum {

    /**
     * 单位一物一码
     */
    ORG(1, "单位一物一码"),

    /**
     * 中爆条形码
     */
    CHINA_BLAST(2, "中爆条形码"),

    /**
     * 库房导入商品的条形码，与订单无关
     */
    WAREHOUSE(3, "库房导入条形码"),
    ;

    private final Integer code;
    private final String description;

    UniqueBarCodeTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static UniqueBarCodeTypeEnum getByCode(Integer code) {
        for (UniqueBarCodeTypeEnum value : UniqueBarCodeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
