package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Date 2020/4/2 0002 18:46
 * @Version 1.0
 * @Desc:描述
 */
public enum BusinessType {
    DiscountActivity(10, "折扣促销"),
    Coupon(50, "优惠券"),
    NegotiatedPrice(40, "协议价"),
    ChangePrice(30, "商家改价"),
    GroupBuyingPrice(60, "团购");
    public final Integer value;
    public final String name;

    private BusinessType(int value, String msg) {
        this.value = value;
        this.name = msg;
    }
    public Integer getValue()
    {
        return this.value;
    }

    public String getName()
    {
        return this.name;
    }

    public static final BusinessType get(final int type) {
        for (BusinessType businessType : BusinessType.values()) {
            if (businessType.value == type){
                return businessType;
            }
        }
        return null;
    }
}
