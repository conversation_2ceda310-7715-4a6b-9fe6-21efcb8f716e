package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

@RpcModel("拆单入参")
public class OrderSplitUpRequestDTO implements Serializable {

    private static final long serialVersionUID = -5014899076132420286L;

    @RpcModelProperty("母订单号")
    private String orderNo;

    @RpcModelProperty("子订单集合")
    private List<OrderChildRequestDTO> children;

    public String getOrderNo() {
        return orderNo;
    }

    public OrderSplitUpRequestDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public List<OrderChildRequestDTO> getChildren() {
        return children;
    }

    public OrderSplitUpRequestDTO setChildren(List<OrderChildRequestDTO> children) {
        this.children = children;
        return this;
    }
}
