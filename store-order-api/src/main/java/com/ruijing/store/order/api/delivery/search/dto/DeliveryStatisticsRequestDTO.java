package com.ruijing.store.order.api.delivery.search.dto;

import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.IntervalDTO;
import com.ruijing.store.order.api.search.enums.OrderAggregationSortFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-03-30 11:39
 * @description:
 **/
public class DeliveryStatisticsRequestDTO implements Serializable {

    private static final long serialVersionUID = 4709135438426095873L;

    /**
     * 范围查询集合
     */
    private List<FieldRangeDTO> fieldRangeList;

    /**
     * 流程种类 0:正常, 1:线下
     */
    private Integer species;

    /**
     * 订单来源列表
     * {@link com.ruijing.store.order.api.base.enums.OrderTypeEnum}
     */
    private List<Integer> orderTypeList;

    /**
     * 排除的订单状态
     */
    private List<Integer> notStatusList;

    /**
     * 聚合排序时的前 多少名 默认50
     */
    private Integer topSize = 50;

    /**
     * 聚合字段
     */
    private OrderSearchFieldEnum aggField;

    /**
     * 排序条目
     */
    private OrderAggregationSortFieldEnum sortItem;

    /**
     * 聚合间隔时间
     */
    private IntervalDTO intervalDate;

    /**
     * 是否跳过没有数据的时间段，不传则默认false
     */
    private Boolean skipEmptyValueInterval;

    /**
     * 聚合排序规则
     */
    private SortOrderEnum sortOrderEnum;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条目数
     */
    private Integer pageSize;

    /**
     * 去除的单位id列表
     */
    private List<Integer> excludeOrgIdList;

    /**
     * 去除的供应商id列表
     */
    private List<Integer> excludeSuppIdList;

    public List<FieldRangeDTO> getFieldRangeList() {
        return fieldRangeList;
    }

    public void setFieldRangeList(List<FieldRangeDTO> fieldRangeList) {
        this.fieldRangeList = fieldRangeList;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public List<Integer> getOrderTypeList() {
        return orderTypeList;
    }

    public void setOrderTypeList(List<Integer> orderTypeList) {
        this.orderTypeList = orderTypeList;
    }

    public List<Integer> getNotStatusList() {
        return notStatusList;
    }

    public void setNotStatusList(List<Integer> notStatusList) {
        this.notStatusList = notStatusList;
    }

    public Integer getTopSize() {
        return topSize;
    }

    public void setTopSize(Integer topSize) {
        this.topSize = topSize;
    }

    public OrderSearchFieldEnum getAggField() {
        return aggField;
    }

    public void setAggField(OrderSearchFieldEnum aggField) {
        this.aggField = aggField;
    }

    public OrderAggregationSortFieldEnum getSortItem() {
        return sortItem;
    }

    public void setSortItem(OrderAggregationSortFieldEnum sortItem) {
        this.sortItem = sortItem;
    }

    public IntervalDTO getIntervalDate() {
        return intervalDate;
    }

    public void setIntervalDate(IntervalDTO intervalDate) {
        this.intervalDate = intervalDate;
    }

    public Boolean getSkipEmptyValueInterval() {
        return skipEmptyValueInterval;
    }

    public void setSkipEmptyValueInterval(Boolean skipEmptyValueInterval) {
        this.skipEmptyValueInterval = skipEmptyValueInterval;
    }

    public SortOrderEnum getSortOrderEnum() {
        return sortOrderEnum;
    }

    public void setSortOrderEnum(SortOrderEnum sortOrderEnum) {
        this.sortOrderEnum = sortOrderEnum;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getExcludeOrgIdList() {
        return excludeOrgIdList;
    }

    public void setExcludeOrgIdList(List<Integer> excludeOrgIdList) {
        this.excludeOrgIdList = excludeOrgIdList;
    }

    public List<Integer> getExcludeSuppIdList() {
        return excludeSuppIdList;
    }

    public void setExcludeSuppIdList(List<Integer> excludeSuppIdList) {
        this.excludeSuppIdList = excludeSuppIdList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryStatisticsRequestDTO.class.getSimpleName() + "[", "]")
                .add("fieldRangeList=" + fieldRangeList)
                .add("species=" + species)
                .add("orderTypeList=" + orderTypeList)
                .add("notStatusList=" + notStatusList)
                .add("topSize=" + topSize)
                .add("aggField=" + aggField)
                .add("sortItem=" + sortItem)
                .add("intervalDate=" + intervalDate)
                .add("skipEmptyValueInterval=" + skipEmptyValueInterval)
                .add("sortOrderEnum=" + sortOrderEnum)
                .add("pageNo=" + pageNo)
                .add("pageSize=" + pageSize)
                .add("excludeOrgIdList=" + excludeOrgIdList)
                .add("excludeSuppIdList=" + excludeSuppIdList)
                .toString();
    }
}
