package com.ruijing.store.order.api.base.delivery.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:06
 * @description
 */
@RpcModel("代配送数据修改请求体")
public class DeliveryProxyChangeDTO implements Serializable {

    private static final long serialVersionUID = 6305586509067892708L;
    
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;
    
    @RpcModelProperty("代配送来源类型")
    private DeliveryProxySourceTypeEnum deliveryProxySourceTypeEnum;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public DeliveryProxySourceTypeEnum getDeliveryProxySourceTypeEnum() {
        return deliveryProxySourceTypeEnum;
    }

    public void setDeliveryProxySourceTypeEnum(DeliveryProxySourceTypeEnum deliveryProxySourceTypeEnum) {
        this.deliveryProxySourceTypeEnum = deliveryProxySourceTypeEnum;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DeliveryProxyChangeDTO.class.getSimpleName() + "[", "]")
                .add("orderIdList=" + orderIdList)
                .add("deliveryProxySourceTypeEnum=" + deliveryProxySourceTypeEnum)
                .toString();
    }
}
