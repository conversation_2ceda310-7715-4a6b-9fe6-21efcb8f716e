package com.ruijing.store.order.api.base.orderapprovallog.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单关联的所有业务日志DTO
 */
@RpcModel("订单关联的所有业务日志")
public class OrderAllRelateLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("日志ID")
    private Integer id;

    @RpcModelProperty("订单ID")
    private Integer orderId;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("采购单号/竞价单号")
    private String purchaseBidNo;

    @RpcModelProperty("结算单号")
    private String settlementNo;

    @RpcModelProperty("操作类型")
    private String operationType;

    @RpcModelProperty("操作人")
    private String operatorName;

    @RpcModelProperty("操作时间")
    private Date createTime;

    @RpcModelProperty("备注")
    private String remark;

    public Integer getId() {
        return id;
    }

    public OrderAllRelateLogDTO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderAllRelateLogDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderAllRelateLogDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getPurchaseBidNo() {
        return purchaseBidNo;
    }

    public OrderAllRelateLogDTO setPurchaseBidNo(String purchaseBidNo) {
        this.purchaseBidNo = purchaseBidNo;
        return this;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public OrderAllRelateLogDTO setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
        return this;
    }

    public String getOperationType() {
        return operationType;
    }

    public OrderAllRelateLogDTO setOperationType(String operationType) {
        this.operationType = operationType;
        return this;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public OrderAllRelateLogDTO setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OrderAllRelateLogDTO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public OrderAllRelateLogDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    @Override
    public String toString() {
        return "OrderAllRelateLogDTO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", purchaseBidNo='" + purchaseBidNo + '\'' +
                ", settlementNo='" + settlementNo + '\'' +
                ", operationType='" + operationType + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", createTime=" + createTime +
                ", remark='" + remark + '\'' +
                '}';
    }
} 