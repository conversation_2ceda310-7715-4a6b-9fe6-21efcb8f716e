package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

/**
 * @description: 退货单统计DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/17 15:25
 **/
public class OrderReturnStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -4438405985018712282L;
    /**
     * 用户id
     */
    private Integer fuserid;
    /**
     * 采购部门id
     */
    private Integer fbuydepartmentid;
    /**
     * 退货订单数量计数
     */
    private Integer orderReturningCount;

    public Integer getFuserid() {
        return fuserid;
    }

    public void setFuserid(Integer fuserid) {
        this.fuserid = fuserid;
    }

    public Integer getFbuydepartmentid() {
        return fbuydepartmentid;
    }

    public void setFbuydepartmentid(Integer fbuydepartmentid) {
        this.fbuydepartmentid = fbuydepartmentid;
    }

    public Integer getOrderReturningCount() {
        return orderReturningCount;
    }

    public void setOrderReturningCount(Integer orderReturningCount) {
        this.orderReturningCount = orderReturningCount;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderReturnStatisticsDTO{");
        sb.append("fuserid=").append(fuserid);
        sb.append(", fbuydepartmentid=").append(fbuydepartmentid);
        sb.append(", orderReturningCount=").append(orderReturningCount);
        sb.append('}');
        return sb.toString();
    }
}
