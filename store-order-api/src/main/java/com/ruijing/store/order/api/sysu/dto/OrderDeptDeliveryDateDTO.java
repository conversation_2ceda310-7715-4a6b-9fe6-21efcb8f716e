package com.ruijing.store.order.api.sysu.dto;

import java.io.Serializable;

/**
 * @Author:zhengzhendong
 * @Date:2020/06/08 11:22
 * @Desc:订单部门与发货时间请求实体
 */
public class OrderDeptDeliveryDateDTO implements Serializable {
    private static final long serialVersionUID = -3015184349675507012L;

    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 发货开始时间
     */
    private String deliveryStartDate;

    /**
     * 发货结束时间
     */
    private String deliveryEndDate;

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDeliveryStartDate() {
        return deliveryStartDate;
    }

    public void setDeliveryStartDate(String deliveryStartDate) {
        this.deliveryStartDate = deliveryStartDate;
    }

    public String getDeliveryEndDate() {
        return deliveryEndDate;
    }

    public void setDeliveryEndDate(String deliveryEndDate) {
        this.deliveryEndDate = deliveryEndDate;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderDeptDeliveryDateDTO{");
        sb.append("departmentId=").append(departmentId);
        sb.append(", deliveryStartDate='").append(deliveryStartDate).append('\'');
        sb.append(", deliveryEndDate='").append(deliveryEndDate).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
