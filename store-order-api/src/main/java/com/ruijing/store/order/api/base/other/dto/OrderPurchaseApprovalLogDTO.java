package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/7/14 6:42 下午
 */
public class OrderPurchaseApprovalLogDTO implements Serializable {

    private static final long serialVersionUID = 3562959513090416145L;

    /**
     * 审批时间
     */
    private String date;

    /**
     * 审批时间时间戳
     */
    private Long dateTimeStamp;

    /**
     * 审批人用户id
     */
    private Integer approverUserId;

    /**
     * 审批人名称
     */
    private String approver;

    /**
     * 操作类型
     */
    private String operate;

    /**
     * 操作内容
     */
    private String operateComment;

    /**
     * 审批等级 默认0
     */
    private Integer approveLevel;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 电子签名url
     */
    private String electronicSignUrl;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Long getDateTimeStamp() {
        return dateTimeStamp;
    }

    public void setDateTimeStamp(Long dateTimeStamp) {
        this.dateTimeStamp = dateTimeStamp;
    }

    public Integer getApproverUserId() {
        return approverUserId;
    }

    public OrderPurchaseApprovalLogDTO setApproverUserId(Integer approverUserId) {
        this.approverUserId = approverUserId;
        return this;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getOperateComment() {
        return operateComment;
    }

    public void setOperateComment(String operateComment) {
        this.operateComment = operateComment;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getElectronicSignUrl() {
        return electronicSignUrl;
    }

    public void setElectronicSignUrl(String electronicSignUrl) {
        this.electronicSignUrl = electronicSignUrl;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderPurchaseApprovalLogDTO.class.getSimpleName() + "[", "]")
                .add("date='" + date + "'")
                .add("dateTimeStamp=" + dateTimeStamp)
                .add("approverUserId=" + approverUserId)
                .add("approver='" + approver + "'")
                .add("operate='" + operate + "'")
                .add("operateComment='" + operateComment + "'")
                .add("approveLevel=" + approveLevel)
                .add("result='" + result + "'")
                .add("electronicSignUrl='" + electronicSignUrl + "'")
                .toString();
    }
}
