package com.ruijing.store.order.api.general.dto;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public class OrderSearchResultDTO implements Serializable {

    private static final long serialVersionUID = 2358705167165371024L;
    private Long totalHits;
    private Integer pageSize;
    private List<OrderMasterSearchDTO> orderMasterSearchDTOS;

    public List<OrderMasterSearchDTO> getOrderMasterSearchDTOS() {
        return orderMasterSearchDTOS;
    }

    public void setOrderMasterSearchDTOS(List<OrderMasterSearchDTO> orderMasterSearchDTOS) {
        this.orderMasterSearchDTOS = orderMasterSearchDTOS;
    }

    public Long getTotalHits() {
        return totalHits;
    }

    public void setTotalHits(Long totalHits) {
        this.totalHits = totalHits;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderSearchResultDTO.class.getSimpleName() + "[", "]")
                .add("totalHits=" + totalHits)
                .add("pageSize=" + pageSize)
                .add("orderMasterSearchDTOS=" + orderMasterSearchDTOS)
                .toString();
    }
}
