package com.ruijing.store.order.api.base.enums;

/**
 * @description: 新退货单退货状态枚举
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/28 11:23
 **/
public enum GoodsReturnStatusEnum {
    WAITING_FOR_CONFIRM(0, "待确认"),
    AGREE_TO_RETURN(1, "同意退货"),
    REFUSED_TO_RETURN(2, "拒绝退货"),
    //3是供应商未确认之前采购人撤销退货
    CANCEL_REQUEST(3, "取消申请"),
    RETURNED_GOODS(4, "退还货物"),
    SUCCESS(5, "成功"),
    ;
    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String description;

    GoodsReturnStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * @description: 通过枚举值获取枚举
     * @param code  枚举编码
     * @return      枚举描述
     */
    public static String getDescriptionByCode(Integer code) {
        for (GoodsReturnStatusEnum goodsReturnEnum : GoodsReturnStatusEnum.values()) {
            if (goodsReturnEnum.getCode().equals(code)) {
                return goodsReturnEnum.getDescription();
            }
        }
        return "";
    }
}
