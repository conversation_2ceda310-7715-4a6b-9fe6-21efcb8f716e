package com.ruijing.store.order.api.base.enums;

/**
 * 订单种类枚举
 */
public enum OrderTypeEnum {
    
    /**
     * 普通采购单
     */
    PURCHASE_ORDER(0, "采购单"),

    /**
     * 普通竞价单
     */
    BID_ORDER(1, "竞价单"),

    /**
     * 临床采购单
     */
    CLINICAL_PURCHASE_ORDER(2, "临床采购单"),

    /**
     * 生猪采购单，用于生猪采购平台
     */
    PIG_PURCHASE_ORDER(3,"生猪采购单")
    ;

    OrderTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String  description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * @description: 通过枚举值获取枚举
     * @date: 2021/3/3 14:46
     * @author: zeng<PERSON><PERSON>
     * @param code
     * @return com.ruijing.store.order.api.base.enums.OrderTypeEnum
     */
    public final static OrderTypeEnum getByCode(Integer code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
