package com.ruijing.store.order.api.base.enums;

/**
 * 操作日志 业务类型枚举
 */
public enum OperateLogBussinessTypeEnum {

    PURCHASE(1, "采购单"),

    BID(2, "竞价单"),

    ORDER(3, "订单"),

    SETTLEMENT(4, "结算单"),

    ;

    private final Integer code;

    private final String desc;

    OperateLogBussinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
