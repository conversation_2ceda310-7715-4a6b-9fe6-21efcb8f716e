package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/9/16 16:50
 * @description
 */
@RpcModel("退货通知DTO")
public class GoodsReturnNoticeDTO implements Serializable {

    private static final long serialVersionUID = 2717668822174346862L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("退货单号列表")
    private List<String> returnNoList;
    
    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("操作人用户id")
    private Long oprUserId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<String> getReturnNoList() {
        return returnNoList;
    }

    public void setReturnNoList(List<String> returnNoList) {
        this.returnNoList = returnNoList;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Long getOprUserId() {
        return oprUserId;
    }

    public void setOprUserId(Long oprUserId) {
        this.oprUserId = oprUserId;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnNoticeDTO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("returnNoList=" + returnNoList)
                .add("orgId=" + orgId)
                .add("oprUserId=" + oprUserId)
                .toString();
    }
}
