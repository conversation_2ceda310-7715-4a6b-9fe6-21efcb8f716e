package com.ruijing.store.order.api.scheduled.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.store.order.api.search.dto.OrderOrgStatParamDTO;

/**
 * @description: 订单超时相关业务
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/21 11:27
 **/
public interface OrderScheduledService {
    /**
     * 自动验收
     * @return
     * @throws Exception
     */
    RemoteResponse autoReceiptOrder();

    /**
     * 订单超时统计数据, 插入到 t_timeout_statistics
     * @return
     */
    RemoteResponse orderTimeOutStatistics();

    /**
     * 定时冻结课题组
     * @return
     */
    RemoteResponse departmentTimeOutFreeze();

    /**
     * 催单邮件，订单超时未验收/结算发送邮件通知
     */
    RemoteResponse departmentFreezeNotice();

    /**
     * 自动取消订单
     * @return
     */
    RemoteResponse autoCancelOrder();

    /**
     * 供应商 退货单自动验收，异步
     * @return
     */
    default ListenableFuture<RemoteResponse<Integer>> asyncAutoReceiveReturn() {
        return ListenableFutures.forValue(this.returnAutoReceive());
    }

    /**
     * 供应商 退货单自动验收
     * @return
     */
    RemoteResponse<Integer> returnAutoReceive();

    /**
     * 待结算数据自动发起结算
     * @return 是否成功
     */
    RemoteResponse<Boolean> autoCreateStatement();

}
