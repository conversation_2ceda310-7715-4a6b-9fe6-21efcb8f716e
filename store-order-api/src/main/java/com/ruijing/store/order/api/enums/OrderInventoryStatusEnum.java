package com.ruijing.store.order.api.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/18 15:45
 * @Description
 **/
public enum OrderInventoryStatusEnum {
    UNDONE(0, "未入库"),
    DONE(1, "已入库"),
    NOT(2, "无需入库")
    ;
    private Integer code;
    private String description;

    OrderInventoryStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }
}
