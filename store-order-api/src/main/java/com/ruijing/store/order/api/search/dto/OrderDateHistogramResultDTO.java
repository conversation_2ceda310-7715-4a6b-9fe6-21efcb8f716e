package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: zhukai
 * @date : 2020/2/17 4:17 下午
 * @description: 订单时间柱状图对象DTO
 */
public class OrderDateHistogramResultDTO implements Serializable {

    private static final long serialVersionUID = 111414288004098282L;

    /**
     * 需要先聚合的字段值
     */
    private String aggFieldValue;

    /**
     * 时间字符串
     */
    private String dateString;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 金额
     */
    private Double amount;

    /**
     * 订单数量
     */
    private Double count;

    public String getAggFieldValue() {
        return aggFieldValue;
    }

    public OrderDateHistogramResultDTO setAggFieldValue(String aggFieldValue) {
        this.aggFieldValue = aggFieldValue;
        return this;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDateHistogramResultDTO.class.getSimpleName() + "[", "]")
                .add("aggFieldValue='" + aggFieldValue + "'")
                .add("dateString='" + dateString + "'")
                .add("timestamp=" + timestamp)
                .add("amount=" + amount)
                .add("count=" + count)
                .toString();
    }
}
