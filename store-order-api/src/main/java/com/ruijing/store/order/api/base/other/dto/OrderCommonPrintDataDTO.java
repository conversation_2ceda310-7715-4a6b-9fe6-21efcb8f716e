package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 订单打印通用出参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 15:53
 **/
public class OrderCommonPrintDataDTO implements Serializable {

    private static final long serialVersionUID = 7944843269181146086L;

    /**
     * 订单打印模型数组
     */
    @RpcModelProperty("订单打印数组")
    private List<OrderPrintDTO> orderMasterList;

    public List<OrderPrintDTO> getOrderMasterList() {
        return orderMasterList;
    }

    public void setOrderMasterList(List<OrderPrintDTO> orderMasterList) {
        this.orderMasterList = orderMasterList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderPrintCommonDataDTO{");
        sb.append("orderMasterList=").append(orderMasterList);
        sb.append('}');
        return sb.toString();
    }
}
