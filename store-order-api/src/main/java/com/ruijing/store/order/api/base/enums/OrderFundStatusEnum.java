package com.ruijing.store.order.api.base.enums;

/**
 * 订单 经费状态
 */

public enum OrderFundStatusEnum {

    /**
     * 未对接财务
     */
    UN_FREEZE(0, "未对接财务"),

    /**
     * 冻结成功
     */
    Freezed(1, "冻结成功"),

    /**
     * 经费 释放中
     */
    Thrawing(2, "释放中"),

    /**
     * 释放失败
     */
    ThrawFailed(3, "释放失败"),

    /**
     * 释放成功
     */
    ThrawSuccessed(4, "释放成功"),

    /**
     * 扣减成功
     */
    Deducted(5, "扣减成功"),

    /**
     * 冻结失败
     */
    FreezedFail(6, "冻结失败"),

    ChangingCard(7,"换卡中"),

    ChangedCardSuccess(8,"换卡成功"),

    ChangedCardFail(9,"换卡失败"),

    FROZE(10,"冻结中（扣减中）"),

    DEDUCT_FAILED(11, "扣减失败"),

    DISTRIBUTE_STATEMENT(90,"分散结算（自结算）"),

    DISTRIBUTE_STATEMENT_NO_TAG(91,"分散结算（自结算，不打标签）"),
    ;


    public final Integer value;
    public final String name;

    OrderFundStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static OrderFundStatusEnum get(Integer value) {
        for (OrderFundStatusEnum item : OrderFundStatusEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }

        return null;
    }

}
