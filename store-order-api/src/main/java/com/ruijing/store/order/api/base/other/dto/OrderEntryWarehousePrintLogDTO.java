package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 入库单日志打印模型
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 19:00
 **/
public class OrderEntryWarehousePrintLogDTO implements Serializable {

    private static final long serialVersionUID = 772691240081877093L;

    /**
     * 一级审批人
     */
    @RpcModelProperty("一级审批人")
    private String firstApprovalOperator;

    public String getFirstApprovalOperator() {
        return firstApprovalOperator;
    }

    public void setFirstApprovalOperator(String firstApprovalOperator) {
        this.firstApprovalOperator = firstApprovalOperator;
    }
}
