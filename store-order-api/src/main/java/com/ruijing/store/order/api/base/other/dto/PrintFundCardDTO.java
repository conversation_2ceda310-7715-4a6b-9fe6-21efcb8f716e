package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

/**
 * @title: FundCardDTO
 * @projectName research-statement-web
 * @description: 经费卡基本信息
 * @author：z<PERSON><PERSON><PERSON><PERSON>
 * @date 2020-02-09 19:35
 */
public class PrintFundCardDTO implements Serializable {

    private static final long serialVersionUID = 1263409585414386375L;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目编号
     */
    private String code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("FundCardDTO{");
        sb.append("name='").append(name).append('\'');
        sb.append(", code='").append(code).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
