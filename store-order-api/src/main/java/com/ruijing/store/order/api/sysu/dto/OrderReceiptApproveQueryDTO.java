package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 订单验收查询条件业务DTO
 * 此DTO用于设置订单验收审核查询时的多状态查询条件
 * 一级验收审核 + 一级验收审核范围的课题组集合
 * 二级验收审核 + 二级验收审核范围的课题组集合
 * <AUTHOR> chenxiaodong
 * @date : 2023/10/13 下午3:44
 */
@RpcModel("订单验收查询条件业务DTO")
public class OrderReceiptApproveQueryDTO implements Serializable {

    private static final long serialVersionUID = 2377601345518277040L;

    /**
     * {@link com.ruijing.store.order.api.base.enums.OrderStatusEnum}
     */
    @RpcModelProperty("订单状态")
    private Integer orderStatus;

    @RpcModelProperty("用户具备验收权限的课题组范围")
    private List<Integer> departmentIds;

    public OrderReceiptApproveQueryDTO() {
    }

    public OrderReceiptApproveQueryDTO(Integer orderStatus, List<Integer> departmentIds) {
        this.orderStatus = orderStatus;
        this.departmentIds = departmentIds;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<Integer> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Integer> departmentIds) {
        this.departmentIds = departmentIds;
    }

    @Override
    public String toString() {
        return "OrderReceiptApproveQueryDTO{" +
                "orderStatus=" + orderStatus +
                ", departmentIds=" + departmentIds +
                '}';
    }
}
