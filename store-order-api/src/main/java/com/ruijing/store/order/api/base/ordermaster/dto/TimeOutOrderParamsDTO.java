package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.store.order.api.base.common.BasePageParamDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 超时订单列表查询的入参
 * @author: zhong<PERSON><PERSON>i
 * @create: 2019/11/5 15:56
 **/
public class TimeOutOrderParamsDTO extends BasePageParamDTO implements Serializable {
    private static final long serialVersionUID = 7537809755713412804L;
    /**
     * 医院/机构id
     */
    private Integer userId;
    /**
     * 医院/机构编号
     */
    private String orgCode;

    private Integer orgId;

    /**
     * 课题组/部门id数组
     */
    private List<Integer> departmentIds;
    /**
     * 超时类型
     */
    private int overTimeType;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 商品信息
     */
    private String goodInfo;
    /**
     * 搜索关键字
     */
    private String searchKey;

    /**
     * 采购人id
     */
    private Integer buyerUserId;

    public Integer getOrgId() {
        return orgId;
    }

    public TimeOutOrderParamsDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public int getOverTimeType() {
        return overTimeType;
    }

    public void setOverTimeType(int overTimeType) {
        this.overTimeType = overTimeType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<Integer> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Integer> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public String getGoodInfo() {
        return goodInfo;
    }

    public void setGoodInfo(String goodInfo) {
        this.goodInfo = goodInfo;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Integer buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TimeOutOrderParamsDTO.class.getSimpleName() + "[", "]")
                .add("userId=" + userId)
                .add("orgCode='" + orgCode + "'")
                .add("departmentIds=" + departmentIds)
                .add("overTimeType=" + overTimeType)
                .add("startDate=" + startDate)
                .add("endDate=" + endDate)
                .add("goodInfo='" + goodInfo + "'")
                .add("searchKey='" + searchKey + "'")
                .add("buyerUserId=" + buyerUserId)
                .toString();
    }
}
