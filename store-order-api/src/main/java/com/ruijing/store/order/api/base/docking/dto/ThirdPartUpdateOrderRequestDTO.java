package com.ruijing.store.order.api.base.docking.dto;

import java.io.Serializable;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/18 18:38
 **/
public class ThirdPartUpdateOrderRequestDTO implements Serializable {

    private static final long serialVersionUID = -755171765228685380L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态
     * {@link com.ruijing.store.order.api.base.enums.OrderStatusEnum}
     */
    private Integer status;

    /**
     * 更新原因
     */
    private String updateReason;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUpdateReason() {
        return updateReason;
    }

    public void setUpdateReason(String updateReason) {
        this.updateReason = updateReason;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartUpdateOrderRequestDTO{");
        sb.append("orderNo='").append(orderNo).append('\'');
        sb.append(", status=").append(status);
        sb.append(", updateReason=").append(updateReason);
        sb.append('}');
        return sb.toString();
    }
}
