package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;
/**
 * @description: dto
 * @author: 曾文聪
 * @create: 2019-09-17 15:11
 **/
public class OrderOrgSuppStatParamDTO implements Serializable {

    private static final long serialVersionUID = 3660746920522580801L;
    /**
     * 医院id
     */
    private Integer orgId;
    /**
     * 合作商id
     */
    private List<Integer> suppIds;
    /**
     * 类型
     */
    private Integer species;

    /**
     * 统计需要过滤的特定订单状态
     */
    private List<Integer> excludeStatus;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getSuppIds() {
        return suppIds;
    }

    public void setSuppIds(List<Integer> suppIds) {
        this.suppIds = suppIds;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public List<Integer> getExcludeStatus() {
        return excludeStatus;
    }

    public void setExcludeStatus(List<Integer> excludeStatus) {
        this.excludeStatus = excludeStatus;
    }
}
