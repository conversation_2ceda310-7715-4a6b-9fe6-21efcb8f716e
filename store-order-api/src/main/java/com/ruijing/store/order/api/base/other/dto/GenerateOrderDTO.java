package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 16:38
 * @Version 1.0
 * @Desc:描述 生成订单DTO
 */
@RpcModel("生成订单DTO")
public class GenerateOrderDTO implements Serializable {

    private static final long serialVersionUID = 2085616644518502016L;
    /**
     * 订单主表DTO
     */
    @RpcModelProperty("订单主表DTO")
    private OrderMasterDTO orderMasterDTO;

    /**
     * 订单详情DTO集合
     */
    @RpcModelProperty("订单详情DTO集合，请改用generateOrderDetailDTOList")
    @Deprecated
    private List<OrderDetailDTO> orderDetailDTOS;

    @ModelProperty("生成订单详情集合")
    private List<GenerateOrderDetailDTO> generateOrderDetailDTOList;

    /**
     * 订单确认备案信息DTO
     */
    @RpcModelProperty("订单确认备案信息DTO")
    private OrderConfirmForTheRecordDTO orderConfirmForTheRecordDTO;

    /**
     * 订单绑定的经费卡信息集合
     */
    @RpcModelProperty("订单绑定的经费卡信息集合")
    private List<RefFundcardOrderDTO> refFundcardOrderDTOS;

    /**
     * 订单配送地址信息
     */
    @RpcModelProperty("订单配送地址信息")
    private GenerateOrderAddressDTO generateOrderAddressDTO;

    /**
     * 对应的审批流类型
     */
    @RpcModelProperty("对应的审批流类型")
    private String flowFundType;

    /**
     * 订单拓展字段
     */
    @RpcModelProperty("订单拓展字段")
    private List<GenerateOrderExtraDTO> generateOrderExtraDTOList;

    @RpcModelProperty("管理平台对接单号")
    private String dockingNumber;

    @RpcModelProperty("外部供应商对接单号")
    private String suppDockingNumber;

    @ModelProperty("管理平台送货单号，暂未用到")
    private String outerBuyerDeliveryNumber;

    @ModelProperty("管理平台需要的发货二维码，广州实验室用")
    private String outerBuyerDeliveryQrCode;

    @RpcModelProperty("订单银行信息")
    private OrderBankDataDTO orderBankDataDTO;

    @ModelProperty("订单个人收款信息快照")
    private OrderPayeeUserDTO orderPayeeUserDTO;

    @ModelProperty("财务对接拓展数据")
    private String financialDockingExtraData;

    public OrderMasterDTO getOrderMasterDTO() {
        return orderMasterDTO;
    }

    public void setOrderMasterDTO(OrderMasterDTO orderMasterDTO) {
        this.orderMasterDTO = orderMasterDTO;
    }

    public List<OrderDetailDTO> getOrderDetailDTOS() {
        return orderDetailDTOS;
    }

    public void setOrderDetailDTOS(List<OrderDetailDTO> orderDetailDTOS) {
        this.orderDetailDTOS = orderDetailDTOS;
    }

    public List<GenerateOrderDetailDTO> getGenerateOrderDetailDTOList() {
        return generateOrderDetailDTOList;
    }

    public GenerateOrderDTO setGenerateOrderDetailDTOList(List<GenerateOrderDetailDTO> generateOrderDetailDTOList) {
        this.generateOrderDetailDTOList = generateOrderDetailDTOList;
        return this;
    }

    public OrderConfirmForTheRecordDTO getOrderConfirmForTheRecordDTO() {
        return orderConfirmForTheRecordDTO;
    }

    public void setOrderConfirmForTheRecordDTO(OrderConfirmForTheRecordDTO orderConfirmForTheRecordDTO) {
        this.orderConfirmForTheRecordDTO = orderConfirmForTheRecordDTO;
    }
    public List<RefFundcardOrderDTO> getRefFundcardOrderDTOS() {
        return refFundcardOrderDTOS;
    }

    public void setRefFundcardOrderDTOS(List<RefFundcardOrderDTO> refFundcardOrderDTOS) {
        this.refFundcardOrderDTOS = refFundcardOrderDTOS;
    }

    public GenerateOrderAddressDTO getGenerateOrderAddressDTO() {
        return generateOrderAddressDTO;
    }

    public void setGenerateOrderAddressDTO(GenerateOrderAddressDTO generateOrderAddressDTO) {
        this.generateOrderAddressDTO = generateOrderAddressDTO;
    }

    public String getFlowFundType() {
        return flowFundType;
    }

    public void setFlowFundType(String flowFundType) {
        this.flowFundType = flowFundType;
    }

    public List<GenerateOrderExtraDTO> getGenerateOrderExtraDTOList() {
        return generateOrderExtraDTOList;
    }

    public void setGenerateOrderExtraDTOList(List<GenerateOrderExtraDTO> generateOrderExtraDTOList) {
        this.generateOrderExtraDTOList = generateOrderExtraDTOList;
    }

    public OrderBankDataDTO getOrderBankDataDTO() {
        return orderBankDataDTO;
    }

    public GenerateOrderDTO setOrderBankDataDTO(OrderBankDataDTO orderBankDataDTO) {
        this.orderBankDataDTO = orderBankDataDTO;
        return this;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public GenerateOrderDTO setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
        return this;
    }

    public String getSuppDockingNumber() {
        return suppDockingNumber;
    }

    public GenerateOrderDTO setSuppDockingNumber(String suppDockingNumber) {
        this.suppDockingNumber = suppDockingNumber;
        return this;
    }

    public String getOuterBuyerDeliveryNumber() {
        return outerBuyerDeliveryNumber;
    }

    public GenerateOrderDTO setOuterBuyerDeliveryNumber(String outerBuyerDeliveryNumber) {
        this.outerBuyerDeliveryNumber = outerBuyerDeliveryNumber;
        return this;
    }

    public String getOuterBuyerDeliveryQrCode() {
        return outerBuyerDeliveryQrCode;
    }

    public GenerateOrderDTO setOuterBuyerDeliveryQrCode(String outerBuyerDeliveryQrCode) {
        this.outerBuyerDeliveryQrCode = outerBuyerDeliveryQrCode;
        return this;
    }

    public OrderPayeeUserDTO getOrderPayeeUserDTO() {
        return orderPayeeUserDTO;
    }

    public void setOrderPayeeUserDTO(OrderPayeeUserDTO orderPayeeUserDTO) {
        this.orderPayeeUserDTO = orderPayeeUserDTO;
    }

    public String getFinancialDockingExtraData() {
        return financialDockingExtraData;
    }

    public GenerateOrderDTO setFinancialDockingExtraData(String financialDockingExtraData) {
        this.financialDockingExtraData = financialDockingExtraData;
        return this;
    }

    @Override
    public String toString() {
        return "GenerateOrderDTO{" +
                "orderMasterDTO=" + orderMasterDTO +
                ", orderDetailDTOS=" + orderDetailDTOS +
                ", generateOrderDetailDTOList=" + generateOrderDetailDTOList +
                ", orderConfirmForTheRecordDTO=" + orderConfirmForTheRecordDTO +
                ", refFundcardOrderDTOS=" + refFundcardOrderDTOS +
                ", generateOrderAddressDTO=" + generateOrderAddressDTO +
                ", flowFundType='" + flowFundType + '\'' +
                ", generateOrderExtraDTOList=" + generateOrderExtraDTOList +
                ", dockingNumber='" + dockingNumber + '\'' +
                ", suppDockingNumber='" + suppDockingNumber + '\'' +
                ", outerBuyerDeliveryNumber='" + outerBuyerDeliveryNumber + '\'' +
                ", outerBuyerDeliveryQrCode='" + outerBuyerDeliveryQrCode + '\'' +
                ", orderBankDataDTO=" + orderBankDataDTO +
                ", orderPayeeUserDTO=" + orderPayeeUserDTO +
                ", financialDockingExtraData='" + financialDockingExtraData + '\'' +
                '}';
    }
}
