package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 2019-09-17
 */
public class OrderOrgStatResultDTO implements Serializable {

    private static final long serialVersionUID = -2623779855659136988L;

    /**
     *id
     */
    private Long id;

    /**
     *数量
     */
    private BigDecimal quantity;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOrgStatResultDTO{");
        sb.append("id=").append(id);
        sb.append(", quantity=").append(quantity);
        sb.append('}');
        return sb.toString();
    }
}
