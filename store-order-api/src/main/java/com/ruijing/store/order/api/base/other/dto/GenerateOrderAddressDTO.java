package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * 订单生成，配送地址入参
 */
public class GenerateOrderAddressDTO implements Serializable {

    private static final long serialVersionUID = 8996300610618001269L;

    /**
     * 地址标签
     */
    private String label;

    /**
     * 采购联系人，对应torder_master.fbuyercontactman
     */
    private String receiverName;

    /**
     * 采购联系电话，对应torder_master.fbuyertelephone
     */
    private String receiverPhone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 市辖区域/县
     */
    private String region;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 配送类型，0普通配送，1代配送
     */
    private Integer deliveryType;

    /**
     * 代配送来源类型
     * {@link DeliveryProxySourceTypeEnum}
     */
    private Integer proxySourceType;

    /**
     * 代收人
     */
    private String receiverNameProxy;

    /**
     * 代收人电话
     */
    private String receiverPhoneProxy;

    /**
     * 代配送省份
     */
    private String provinceProxy;

    /**
     * 代配送城市
     */
    private String cityProxy;

    /**
     * 代配送市辖区/县
     */
    private String regionProxy;

    /**
     * 代配送详细地址
     */
    private String addressProxy;

    /**
     * 配送坐标
     */
    private String geo;

    /**
     * 代配送坐标
     */
    private String geoProxy;


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getReceiverName() {
        return this.receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return this.receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getProvince() {
        return this.province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return this.region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getReceiverNameProxy() {
        return this.receiverNameProxy;
    }

    public void setReceiverNameProxy(String receiverNameProxy) {
        this.receiverNameProxy = receiverNameProxy;
    }

    public String getReceiverPhoneProxy() {
        return this.receiverPhoneProxy;
    }

    public void setReceiverPhoneProxy(String receiverPhoneProxy) {
        this.receiverPhoneProxy = receiverPhoneProxy;
    }

    public String getProvinceProxy() {
        return this.provinceProxy;
    }

    public void setProvinceProxy(String provinceProxy) {
        this.provinceProxy = provinceProxy;
    }

    public String getCityProxy() {
        return this.cityProxy;
    }

    public void setCityProxy(String cityProxy) {
        this.cityProxy = cityProxy;
    }

    public String getRegionProxy() {
        return this.regionProxy;
    }

    public void setRegionProxy(String regionProxy) {
        this.regionProxy = regionProxy;
    }

    public String getAddressProxy() {
        return this.addressProxy;
    }

    public void setAddressProxy(String addressProxy) {
        this.addressProxy = addressProxy;
    }

    public Integer getDeliveryType() {
        return this.deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getProxySourceType() {
        return proxySourceType;
    }

    public void setProxySourceType(Integer proxySourceType) {
        this.proxySourceType = proxySourceType;
    }

    public String getGeo() {
        return geo;
    }

    public void setGeo(String geo) {
        this.geo = geo;
    }

    public String getGeoProxy() {
        return geoProxy;
    }

    public void setGeoProxy(String geoProxy) {
        this.geoProxy = geoProxy;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GenerateOrderAddressDTO.class.getSimpleName() + "[", "]")
                .add("label='" + label + "'")
                .add("receiverName='" + receiverName + "'")
                .add("receiverPhone='" + receiverPhone + "'")
                .add("province='" + province + "'")
                .add("city='" + city + "'")
                .add("region='" + region + "'")
                .add("address='" + address + "'")
                .add("deliveryType=" + deliveryType)
                .add("proxySourceType=" + proxySourceType)
                .add("receiverNameProxy='" + receiverNameProxy + "'")
                .add("receiverPhoneProxy='" + receiverPhoneProxy + "'")
                .add("provinceProxy='" + provinceProxy + "'")
                .add("cityProxy='" + cityProxy + "'")
                .add("regionProxy='" + regionProxy + "'")
                .add("addressProxy='" + addressProxy + "'")
                .add("geo='" + geo + "'")
                .add("geoProxy='" + geoProxy + "'")
                .toString();
    }
}
