package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

@RpcModel("订单详情关联-验收图片DTO")
public class AcceptPictureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("验收图片信息")
    private PictureDTO pictureDTO;

    @RpcModelProperty("关联的订单详情ID集合")
    private List<Integer> orderDetailIdList;

    public PictureDTO getPictureDTO() {
        return pictureDTO;
    }

    public void setPictureDTO(PictureDTO pictureDTO) {
        this.pictureDTO = pictureDTO;
    }

    public List<Integer> getOrderDetailIdList() {
        return orderDetailIdList;
    }

    public void setOrderDetailIdList(List<Integer> orderDetailIdList) {
        this.orderDetailIdList = orderDetailIdList;
    }

    @Override
    public String toString() {
        return "AcceptPictureDTO{" +
                "pictureDTO=" + pictureDTO +
                ", orderDetailIdList=" + orderDetailIdList +
                '}';
    }
}