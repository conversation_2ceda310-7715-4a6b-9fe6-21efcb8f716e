package com.ruijing.store.order.api.base.enums;

/**
 * 备案类型枚举
 */
public enum OrderConfirmTypeEnum {
    NOXIOUS(1,"易制毒类"),
    EXPLOSIVES(2,"易制爆类"),
    ;

    public final Integer code;

    public final String name;

    OrderConfirmTypeEnum(int code, String name){
        this.code = code;
        this.name = name;
    }

    public final Integer getCode() {
        return code;
    }

    public final String getDesc() {
        return name;
    }

    public static OrderConfirmTypeEnum getByName(String name) {
        for (OrderConfirmTypeEnum orderConfirmTypeEnum : OrderConfirmTypeEnum.values()) {
            if (orderConfirmTypeEnum.name.equals(name)){
                return orderConfirmTypeEnum;
            }
        }
        return null;
    }

    public static OrderConfirmTypeEnum getByCode(Integer code) {
        for (OrderConfirmTypeEnum orderConfirmTypeEnum : OrderConfirmTypeEnum.values()) {
            if (orderConfirmTypeEnum.code.equals(code)){
                return orderConfirmTypeEnum;
            }
        }
        return null;
    }
}
