package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.gateway.dto.SupplierUserDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 订单打印模型
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 18:35
 **/
public class OrderPrintDTO implements Serializable {
    private static final long serialVersionUID = -9214548862260795238L;

    /**
     * 供应商名字
     */
    @RpcModelProperty("供应商名字")
    private String suppName;

    /**
     * 供应商编码
     */
    @RpcModelProperty("供应商编码")
    private String suppCode;

    @RpcModelProperty("供应商用户信息列表")
    private List<SupplierUserDTO> suppUsers;

    @RpcModelProperty(value = "供应商地址")
    private String suppAddress;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 采购单id
     */
    @RpcModelProperty("采购单id")
    private Integer applicationId;

    /**
     * 采购单单号
     */
    @RpcModelProperty("采购单单号")
    private String applicationNo;

    /**
     * 采购人名称
     */
    @RpcModelProperty("采购人名称")
    private String buyerName;

    /**
     * 采购审核人
     */
    @RpcModelProperty("采购审核人, 默认是一级审批人")
    private String orderApprovalName;

    /**
     * 二级采购审核人
     */
    @RpcModelProperty("二级采购审核人")
    private String orderSecondApprovalName;

    /**
     * 三级采购审核人
     */
    @RpcModelProperty("三级采购审核人")
    private String orderThirdApprovalName;

    @RpcModelProperty("采购单生成时间")
    private Long applicationCreateTime;

    /**
     * 订单发票抬头id
     */
    @RpcModelProperty("订单发票抬头id")
    private Integer invoiceTitleId;

    /**
     * 条形码
     */
    @RpcModelProperty("条形码")
    private String barCode;

    /**
     * 采购人联系方式
     */
    @RpcModelProperty("采购人联系方式")
    private String buyerTelephone;

    /**
     * 订单验收日期
     */
    @RpcModelProperty("订单验收日期")
    private Date orderReceiptDate;

    /**
     * 验收人名称
     */
    @RpcModelProperty("验收人名称")
    private String receiverName;

    /**
     * 收货人（注意与验收人的区分，此处与供应商命名一致）
     */
    @RpcModelProperty("收货人（注意与验收人的区分）")
    private String buyerContactMan;

    /**
     * 收货人手机号
     */
    @RpcModelProperty("收货人手机号")
    private String receiverPhone;

    /**
     * 收货地址
     */
    @RpcModelProperty("收货地址")
    private String receiverAddress;

    /**
     * 线下供应商统一信用编码
     */
    @RpcModelProperty("线下供应商统一信用编码")
    private String unifyCode;

    /**
     * 二维码
     */
    @RpcModelProperty("二维码")
    private String orderNoQrCode;
    
    @RpcModelProperty("微信订单详情二维码")
    private String wechatOrderDetailQrCode;

    /**
     * 验收审批人
     */
    @RpcModelProperty("验收审批人")
    private String acceptApprovalName;

    /**
     * 课题组名称
     */
    @RpcModelProperty("课题组名称")
    private String departmentName;

    /**
     * 当前课题组父级课题组名称
     */
    @RpcModelProperty("当前课题组父级课题组名称")
    private String departmentParentName;

    /**
     * 课题组负责人
     */
    @RpcModelProperty("课题组负责人")
    private String departmentManagerName;

    /**
     * 医院名称
     */
    @RpcModelProperty("单位/医院名称")
    private String orgName;

    /**
     * 订单验收日期
     */
    @RpcModelProperty("订单生成日期")
    private Date orderDate;

    /**
     * 采购申请说明
     */
    @RpcModelProperty("采购申请说明")
    private String purchaseNote;

    @RpcModelProperty("采购用途")
    private String purchasePurpose;

    /**
     * 线上-0，线下-1单
     */
    @RpcModelProperty("线上-0，线下-1单")
    private Integer orderSpecies;

    /**
     * 线下采购渠道
     */
    @RpcModelProperty("线下采购渠道")
    private String offlineProcurementChannel;

    @RpcModelProperty("自定义的订单关联分类")
    private String selfDefCategory;

    @RpcModelProperty("减去成功退货后的订单总额")
    private BigDecimal totalPriceAfterReturn;

    /**
     * 订单商品详情
     */
    @RpcModelProperty("订单商品详情")
    private List<OrderDetailPrintDTO> orderDetailPrintList;

    /**
     * 订单经费卡信息
     */
    @RpcModelProperty("订单经费卡信息")
    private List<OrderFundCardPrintDTO> orderFundCardPrintList;

    @RpcModelProperty("经费卡列表")
    private List<FundCardPrintDTO> fundCardPrintDTOList;

    /**
     * 订单入库单打印模型数组
     */
    @RpcModelProperty("订单入库单打印模型数组")
    private List<OrderEntryWarehousePrintDTO> orderEntryWarehousePrintList;

    /**
     * 订单出库单打印模型数组——仅限即入即出配置
     */
    @RpcModelProperty("订单出库单打印模型数组——仅限即入即出")
    private List<OrderOutWarehousePrintDTO> orderOutWarehousePrintList;

    /**
     * 订单审批日志打印模型数组
     */
    @RpcModelProperty("订单审批日志打印模型数组")
    private List<OrderPurchaseApprovalLogPrintDTO> orderPurchaseApprovalLogPrintList;

    /**
     * 订单验收评价列表-字串
     */
    @RpcModelProperty("订单验收评价列表-评价id")
    private List<Integer> acceptCommentIdList;

    /**
     * 订单退货单打印模型数组
     */
    @RpcModelProperty("订单退货单打印模型数组")
    private List<OrderGoodsReturnPrintDTO> orderGoodsReturnPrintList;

    /**
     * 发票信息打印模组
     */
    @RpcModelProperty("发票信息")
    private List<OrderInvoiceInfoDTO> orderInvoiceInfoList;

    /**
     * 验收时图片
     */
    @RpcModelProperty("验收时图片")
    private List<String> receivePicUrls;

    @RpcModelProperty("旧单标识")
    private Boolean oldFlag;


    public String getSuppAddress() {
        return suppAddress;
    }

    public OrderPrintDTO setSuppAddress(String suppAddress) {
        this.suppAddress = suppAddress;
        return this;
    }

    public List<OrderDetailPrintDTO> getOrderDetailPrintList() {
        return orderDetailPrintList;
    }

    public void setOrderDetailPrintList(List<OrderDetailPrintDTO> orderDetailPrintList) {
        this.orderDetailPrintList = orderDetailPrintList;
    }

    public List<OrderFundCardPrintDTO> getOrderFundCardPrintList() {
        return orderFundCardPrintList;
    }

    public void setOrderFundCardPrintList(List<OrderFundCardPrintDTO> orderFundCardPrintList) {
        this.orderFundCardPrintList = orderFundCardPrintList;
    }

    public List<FundCardPrintDTO> getFundCardPrintDTOList() {
        return fundCardPrintDTOList;
    }

    public OrderPrintDTO setFundCardPrintDTOList(List<FundCardPrintDTO> fundCardPrintDTOList) {
        this.fundCardPrintDTOList = fundCardPrintDTOList;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getOrderApprovalName() {
        return orderApprovalName;
    }

    public void setOrderApprovalName(String orderApprovalName) {
        this.orderApprovalName = orderApprovalName;
    }

    public List<OrderEntryWarehousePrintDTO> getOrderEntryWarehousePrintList() {
        return orderEntryWarehousePrintList;
    }

    public void setOrderEntryWarehousePrintList(List<OrderEntryWarehousePrintDTO> orderEntryWarehousePrintList) {
        this.orderEntryWarehousePrintList = orderEntryWarehousePrintList;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public Integer getInvoiceTitleId() {
        return invoiceTitleId;
    }

    public void setInvoiceTitleId(Integer invoiceTitleId) {
        this.invoiceTitleId = invoiceTitleId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone;
    }

    public Date getOrderReceiptDate() {
        return orderReceiptDate;
    }

    public void setOrderReceiptDate(Date orderReceiptDate) {
        this.orderReceiptDate = orderReceiptDate;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<OrderPurchaseApprovalLogPrintDTO> getOrderPurchaseApprovalLogPrintList() {
        return orderPurchaseApprovalLogPrintList;
    }

    public void setOrderPurchaseApprovalLogPrintList(List<OrderPurchaseApprovalLogPrintDTO> orderPurchaseApprovalLogPrintList) {
        this.orderPurchaseApprovalLogPrintList = orderPurchaseApprovalLogPrintList;
    }

    public List<Integer> getAcceptCommentIdList() {
        return acceptCommentIdList;
    }

    public OrderPrintDTO setAcceptCommentIdList(List<Integer> acceptCommentIdList) {
        this.acceptCommentIdList = acceptCommentIdList;
        return this;
    }

    public List<OrderGoodsReturnPrintDTO> getOrderGoodsReturnPrintList() {
        return orderGoodsReturnPrintList;
    }

    public void setOrderGoodsReturnPrintList(List<OrderGoodsReturnPrintDTO> orderGoodsReturnPrintList) {
        this.orderGoodsReturnPrintList = orderGoodsReturnPrintList;
    }

    public String getDepartmentManagerName() {
        return departmentManagerName;
    }

    public void setDepartmentManagerName(String departmentManagerName) {
        this.departmentManagerName = departmentManagerName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public List<OrderInvoiceInfoDTO> getOrderInvoiceInfoList() {
        return orderInvoiceInfoList;
    }

    public OrderPrintDTO setOrderInvoiceInfoList(List<OrderInvoiceInfoDTO> orderInvoiceInfoList) {
        this.orderInvoiceInfoList = orderInvoiceInfoList;
        return this;
    }

    public List<OrderOutWarehousePrintDTO> getOrderOutWarehousePrintList() {
        return orderOutWarehousePrintList;
    }

    public OrderPrintDTO setOrderOutWarehousePrintList(List<OrderOutWarehousePrintDTO> orderOutWarehousePrintList) {
        this.orderOutWarehousePrintList = orderOutWarehousePrintList;
        return this;
    }

    public String getPurchaseNote() {
        return purchaseNote;
    }

    public OrderPrintDTO setPurchaseNote(String purchaseNote) {
        this.purchaseNote = purchaseNote;
        return this;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public OrderPrintDTO setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderPrintDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getUnifyCode() {
        return unifyCode;
    }

    public void setUnifyCode(String unifyCode) {
        this.unifyCode = unifyCode;
    }

    public String getOrderNoQrCode() {
        return orderNoQrCode;
    }

    public void setOrderNoQrCode(String orderNoQrCode) {
        this.orderNoQrCode = orderNoQrCode;
    }

    public String getWechatOrderDetailQrCode() {
        return wechatOrderDetailQrCode;
    }

    public void setWechatOrderDetailQrCode(String wechatOrderDetailQrCode) {
        this.wechatOrderDetailQrCode = wechatOrderDetailQrCode;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public void setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer appId) {
        this.applicationId = appId;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getOfflineProcurementChannel() {
        return offlineProcurementChannel;
    }

    public void setOfflineProcurementChannel(String offlineProcurementChannel) {
        this.offlineProcurementChannel = offlineProcurementChannel;
    }

    public String getOrderSecondApprovalName() {
        return orderSecondApprovalName;
    }

    public void setOrderSecondApprovalName(String orderSecondApprovalName) {
        this.orderSecondApprovalName = orderSecondApprovalName;
    }

    public String getOrderThirdApprovalName() {
        return orderThirdApprovalName;
    }

    public void setOrderThirdApprovalName(String orderThirdApprovalName) {
        this.orderThirdApprovalName = orderThirdApprovalName;
    }

    public List<SupplierUserDTO> getSuppUsers() {
        return suppUsers;
    }

    public void setSuppUsers(List<SupplierUserDTO> suppUsers) {
        this.suppUsers = suppUsers;
    }

    public String getAcceptApprovalName() {
        return acceptApprovalName;
    }

    public OrderPrintDTO setAcceptApprovalName(String acceptApprovalName) {
        this.acceptApprovalName = acceptApprovalName;
        return this;
    }

    public String getSelfDefCategory() {
        return selfDefCategory;
    }

    public void setSelfDefCategory(String selfDefCategory) {
        this.selfDefCategory = selfDefCategory;
    }

    public Long getApplicationCreateTime() {
        return applicationCreateTime;
    }

    public void setApplicationCreateTime(Long applicationCreateTime) {
        this.applicationCreateTime = applicationCreateTime;
    }

    public String getPurchasePurpose() {
        return purchasePurpose;
    }

    public void setPurchasePurpose(String purchasePurpose) {
        this.purchasePurpose = purchasePurpose;
    }

    public List<String> getReceivePicUrls() {
        return receivePicUrls;
    }

    public void setReceivePicUrls(List<String> receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public BigDecimal getTotalPriceAfterReturn() {
        return totalPriceAfterReturn;
    }

    public void setTotalPriceAfterReturn(BigDecimal totalPriceAfterReturn) {
        this.totalPriceAfterReturn = totalPriceAfterReturn;
    }

    public Boolean getOldFlag() {
        return oldFlag;
    }

    public OrderPrintDTO setOldFlag(Boolean oldFlag) {
        this.oldFlag = oldFlag;
        return this;
    }

    @Override
    public String toString() {
        return "OrderPrintDTO{" +
                "suppName='" + suppName + '\'' +
                ", suppCode='" + suppCode + '\'' +
                ", suppUsers=" + suppUsers +
                ", orderNo='" + orderNo + '\'' +
                ", orderId=" + orderId +
                ", applicationId=" + applicationId +
                ", applicationNo='" + applicationNo + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", orderApprovalName='" + orderApprovalName + '\'' +
                ", orderSecondApprovalName='" + orderSecondApprovalName + '\'' +
                ", orderThirdApprovalName='" + orderThirdApprovalName + '\'' +
                ", applicationCreateTime=" + applicationCreateTime +
                ", invoiceTitleId=" + invoiceTitleId +
                ", barCode='" + barCode + '\'' +
                ", buyerTelephone='" + buyerTelephone + '\'' +
                ", orderReceiptDate=" + orderReceiptDate +
                ", receiverName='" + receiverName + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", receiverPhone='" + receiverPhone + '\'' +
                ", receiverAddress='" + receiverAddress + '\'' +
                ", unifyCode='" + unifyCode + '\'' +
                ", orderNoQrCode='" + orderNoQrCode + '\'' +
                ", wechatOrderDetailQrCode='" + wechatOrderDetailQrCode + '\'' +
                ", acceptApprovalName='" + acceptApprovalName + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", departmentParentName='" + departmentParentName + '\'' +
                ", departmentManagerName='" + departmentManagerName + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orderDate=" + orderDate +
                ", purchaseNote='" + purchaseNote + '\'' +
                ", purchasePurpose='" + purchasePurpose + '\'' +
                ", orderSpecies=" + orderSpecies +
                ", offlineProcurementChannel='" + offlineProcurementChannel + '\'' +
                ", selfDefCategory='" + selfDefCategory + '\'' +
                ", totalPriceAfterReturn=" + totalPriceAfterReturn +
                ", orderDetailPrintList=" + orderDetailPrintList +
                ", orderFundCardPrintList=" + orderFundCardPrintList +
                ", fundCardPrintDTOList=" + fundCardPrintDTOList +
                ", orderEntryWarehousePrintList=" + orderEntryWarehousePrintList +
                ", orderOutWarehousePrintList=" + orderOutWarehousePrintList +
                ", orderPurchaseApprovalLogPrintList=" + orderPurchaseApprovalLogPrintList +
                ", acceptCommentIdList=" + acceptCommentIdList +
                ", orderGoodsReturnPrintList=" + orderGoodsReturnPrintList +
                ", orderInvoiceInfoList=" + orderInvoiceInfoList +
                ", receivePicUrls=" + receivePicUrls +
                ", oldFlag=" + oldFlag +
                ", suppAddress='" + suppAddress + '\'' +
                '}';
    }
}
