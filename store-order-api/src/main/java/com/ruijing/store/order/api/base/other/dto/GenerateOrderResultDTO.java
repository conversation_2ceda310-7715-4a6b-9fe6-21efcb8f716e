package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

public class GenerateOrderResultDTO implements Serializable {
    private static final long serialVersionUID = -7758627766950307628L;

    /**
     * 订单主表id
     */
    private Integer orderMasterId;

    /**
     * 订单号
     */
    private String orderMasterNumber;

    /**
     * 生成订单的配送地址
     */
    private GenerateOrderAddressDTO orderAddressDTO;

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public String getOrderMasterNumber() {
        return orderMasterNumber;
    }

    public void setOrderMasterNumber(String orderMasterNumber) {
        this.orderMasterNumber = orderMasterNumber;
    }

    public GenerateOrderAddressDTO getOrderAddressDTO() {
        return orderAddressDTO;
    }

    public void setOrderAddressDTO(GenerateOrderAddressDTO orderAddressDTO) {
        this.orderAddressDTO = orderAddressDTO;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GenerateOrderResultDTO{");
        sb.append("orderMasterId=").append(orderMasterId);
        sb.append(", orderMasterNumber='").append(orderMasterNumber).append('\'');
        sb.append(", orderAddressDTO='").append(orderAddressDTO).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
