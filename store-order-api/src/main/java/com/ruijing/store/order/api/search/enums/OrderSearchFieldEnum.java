package com.ruijing.store.order.api.search.enums;

/**
 * @description: 订单统计管理入参
 * @author: zhuk
 * @create: 2019-09-10 19:36
 **/
public enum OrderSearchFieldEnum {

    /**
     * 组织单位id
     */
    ORG_ID("fuserid", "组织Id",1),

    /**
     * 课题组Id
     */
    DEPARTMENT_ID("fbuydepartmentid", "课题组Id",1),

    /**
     * 订单状态
     */
    STATUS("status", "订单状态",1),

    /**
     * 订单类型或订单来源
     */
    ORDER_TYPE("order_type", "订单来源", 1),

    /**
     * 商品id
     */
    PRODUCT_ID("product_id","商品Id",2),

    /**
     * 分类id
     */
    CATEGORY_ID("categoryID","分类Id",2),

    /**
     * 一级分类id
     */
    FIRST_CATEGORY_ID("first_category_id", "一级分类id", 2),

    /**
     * 二级分类id
     */
    SECOND_CATEGORY_ID("second_category_id", "二级分类id", 2),

    /**
     * 品牌Id
     */
    BRAND_ID("fbrandid","品牌Id",2),

    /**
     * 供应商Id
     */
    SUPPLIER_ID("fsuppid", "供应商Id", 1),

    /**
     * 订单详情供应商Id
     */
    DETAIL_SUPPLIER_ID("supp_id", "订单详情供应商Id", 2),

    DELIVERY_SORTED_TIME("delivery_sorted_time", "代配送分拣时间", 1, false),

    DELIVERY_DELIVERED_TIME("delivered_time", "代配送配送时间", 1, false),

    DELIVERY_SORTED_MAN("sorted_user.keyword", "代配送分拣人", 1, false),

    DELIVERY_DELIVERED_MAN("delivery_user.keyword", "代配送配送人", 1, false),

    BUYER_ID("fbuyerid", "采购人id", 1),

    IS_REGULAR_CUSTOMER("regular_customer_purchase", "是否老客户", 1),

    IS_CUSTOMER_SUBSCRIBE_SUPP("customer_subscribe_supp", "是否采购用户订阅了商家", 1),

    PROVINCE("province", "省份", 1, false),

    ;


    OrderSearchFieldEnum(String field, String name,Integer table) {
        this.field = field;
        this.name = name;
        this.table =table;
        this.fieldNumberType = true;
    }

    OrderSearchFieldEnum(String field, String name, Integer table, boolean fieldNumberType) {
        this.field = field;
        this.name = name;
        this.table =table;
        this.fieldNumberType = fieldNumberType;
    }

    /**
     * 字段名
     */
    private final String field;

    /**
     * 字段注释
     */
    private final String name;

    /**
     * 所属表 1= orderMaster  2= orderDetail
     */
    private final Integer table;

    /**
     * 是否聚合维度键为数字
     */
    private final Boolean fieldNumberType;


    public String getField() {
        return field;
    }

    public String getName() {
        return name;
    }

    public Integer getTable() {
        return table;
    }

    public Boolean getFieldNumberType() {
        return fieldNumberType;
    }
}
