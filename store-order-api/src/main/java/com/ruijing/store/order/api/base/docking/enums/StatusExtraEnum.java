package com.ruijing.store.order.api.base.docking.enums;

/**
 * Created with IntelliJ IDEA.
 * User: zht
 * Date: 2018/8/8
 * Time: 14:28
 * To change this template use File | Settings | File Templates.
 * Description:
 */
public enum StatusExtraEnum {

    /**
     * 外部订单对接状态
     */
    Fail(-2, "订单", "失效"),
    WaitForConfirmed(-1,"订单","待确认"),
    NoStatus(0, "订单", "默认"),
    Submitted(202, "结算单", "已提交"),
    FinancialProcessing(203, "结算单", "财务处理中"),
    End(203, "结算单", "结束"),
    Purchase(101,"采购单", "采购单");
    private Integer value;
    private String type;
    private String desc;

    StatusExtraEnum(Integer value, String type, String desc) {
        this.value = value;
        this.type = type;
        this.desc = desc;
    }

    public static StatusExtraEnum getStatusExtraByValue(Integer value) {
        for (StatusExtraEnum statusExtra : StatusExtraEnum.values()) {
            if (statusExtra.getValue().equals(value)) {
                return statusExtra;
            }
        }
        throw new RuntimeException("Unknow enum value: " + value);
    }

    public Integer getValue() {
        return value;
    }


    public String getType() {
        return type;
    }


    public String getDesc() {
        return desc;
    }

}
