package com.ruijing.store.order.api.base.goodsreturn.dto;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 16:57
 * @description
 */
@RpcModel("取消退货请求体")
public class GoodsReturnCancelRequstDTO implements Serializable {

    /**
     * orderId 数组，长度不可超100
     */
    @RpcModelProperty("orderId 数组，长度不可超100")
    private List<Integer> orderIdList;
    
    @RpcModelProperty("取消退货原因")
    private String reason;
    
    @RpcModelProperty("取消退货操作人")
    private Integer userId;
    
    @RpcModelProperty("单位id")
    private Integer orgId;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    @Override
    public String toString() {
        return "GoodsReturnCancelRequstDTO{" +
                "orderIdList=" + orderIdList +
                ", reason='" + reason + '\'' +
                ", userId=" + userId +
                ", orgId=" + orgId +
                '}';
    }
}
