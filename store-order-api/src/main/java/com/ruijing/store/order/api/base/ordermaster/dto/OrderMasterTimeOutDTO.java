package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 超时订单DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/1 10:50
 **/
public class OrderMasterTimeOutDTO implements Serializable {

    private static final long serialVersionUID = -7697473558104265086L;
    /**
     * 主订单id
     */
    private Integer id;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 采购部门Id
     */
    private Integer buyDepartmentId;
    /**
     * 采购部门
     */
    private String buyDepartment;
    /**
     * 采购人
     */
    private String buyerName;

    /**
     * 经费卡id 集合
     */
    private List<String> fundCardIdList;

    /**
     * 订单日期(时间戳)
     */
    private Long orderDate;
    /**
     * 待验收时长
     */
    private Integer waitingExamineDays;
    /**
     * 待结算时长
     */
    private Integer waitingBalanceDays;
    /**
     * 供应商
     */
    private String suppName;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 结算单id
     */
    private Integer statementId;
    /**
     * 结算状态,参考{@StatementStatusEnum}
     */
    private Integer statementStatus;
    /**
     * 订单金额
     */
    private BigDecimal totalPrice;
    /**
     * 超时类型
     */
    private Integer timeOutType;

    /**
     * 库房状态
     */
    private Integer inventoryStatus;

    /**
     * 订单详情
     */
    private List<OrderDetailTimeOutDTO> orderDetails;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public Integer getWaitingExamineDays() {
        return waitingExamineDays;
    }

    public void setWaitingExamineDays(Integer waitingExamineDays) {
        this.waitingExamineDays = waitingExamineDays;
    }

    public Integer getWaitingBalanceDays() {
        return waitingBalanceDays;
    }

    public void setWaitingBalanceDays(Integer waitingBalanceDays) {
        this.waitingBalanceDays = waitingBalanceDays;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public Integer getBuyDepartmentId() {
        return buyDepartmentId;
    }

    public void setBuyDepartmentId(Integer buyDepartmentId) {
        this.buyDepartmentId = buyDepartmentId;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuyDepartment() {
        return buyDepartment;
    }

    public void setBuyDepartment(String buyDepartment) {
        this.buyDepartment = buyDepartment;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public List<String> getFundCardIdList() {
        return fundCardIdList;
    }

    public void setFundCardIdList(List<String> fundCardIdList) {
        this.fundCardIdList = fundCardIdList;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Long getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Long orderDate) {
        this.orderDate = orderDate;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public List<OrderDetailTimeOutDTO> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<OrderDetailTimeOutDTO> orderDetails) {
        this.orderDetails = orderDetails;
    }

    public Integer getTimeOutType() {
        return timeOutType;
    }

    public void setTimeOutType(Integer timeOutType) {
        this.timeOutType = timeOutType;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }


    @Override
    public String toString() {
        return "OrderMasterTimeOutDTO{" +
                "id=" + id +
                ", orderNo='" + orderNo + '\'' +
                ", buyDepartmentId=" + buyDepartmentId +
                ", buyDepartment='" + buyDepartment + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", fundCardIdList=" + fundCardIdList +
                ", orderDate=" + orderDate +
                ", waitingExamineDays=" + waitingExamineDays +
                ", waitingBalanceDays=" + waitingBalanceDays +
                ", suppName='" + suppName + '\'' +
                ", status=" + status +
                ", statementId=" + statementId +
                ", statementStatus=" + statementStatus +
                ", totalPrice=" + totalPrice +
                ", timeOutType=" + timeOutType +
                ", inventoryStatus=" + inventoryStatus +
                ", orderDetails=" + orderDetails +
                '}';
    }
}
