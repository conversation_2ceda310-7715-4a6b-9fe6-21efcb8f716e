package com.ruijing.store.order.api.warehouse.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.warehouse.dto.FinishWarehouseDataDTO;

/**
 * <AUTHOR>
 * @date 2022/8/5 10:22
 * @description
 */
@RpcApi("订单入库服务")
public interface OrderWareHouseRpcService {

    /**
     * 完成入库后处理
     * 
     * @param finishWarehouseDataDTO 更新订单入库数据参数
     * @return 是否成功
     */
    @RpcMethod("完成入库后处理")
    RemoteResponse<Boolean> finishWarehouse(FinishWarehouseDataDTO finishWarehouseDataDTO);
}
