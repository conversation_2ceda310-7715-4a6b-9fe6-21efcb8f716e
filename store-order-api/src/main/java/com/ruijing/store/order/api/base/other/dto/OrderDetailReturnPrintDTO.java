package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrderDetailReturnPrintDTO implements Serializable {

    private static final long serialVersionUID = -1431728634657605826L;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("商品货号")
    private String goodsCode;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("单位")
    private String unit;

    @RpcModelProperty("退货数量")
    private BigDecimal quantity;

    @RpcModelProperty("退货原因")
    private String returnReason;

    @RpcModelProperty("单价")
    private BigDecimal price;

    @RpcModelProperty("总金额")
    private BigDecimal amount;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "OrderDetailReturnDetailDTO{" +
                "goodsName='" + goodsName + '\'' +
                ", goodsCode='" + goodsCode + '\'' +
                ", brand='" + brand + '\'' +
                ", specification='" + specification + '\'' +
                ", unit='" + unit + '\'' +
                ", quantity=" + quantity +
                ", returnReason='" + returnReason + '\'' +
                ", price=" + price +
                ", amount=" + amount +
                '}';
    }
}

