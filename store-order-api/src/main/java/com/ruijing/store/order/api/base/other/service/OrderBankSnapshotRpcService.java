package com.ruijing.store.order.api.base.other.service;

import com.ruijing.fundamental.api.annotation.Appkey;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.OrderBankDataDTO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-11-30 11:36
 * @description:
 **/
@Appkey(name = "store-order-service")
public interface OrderBankSnapshotRpcService {

    /**
     * 仅更新银行信息，orderId必填，更新不为null的字段
     * @param orderBankDataDTOList 快照数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> updateOrderBankData(List<OrderBankDataDTO> orderBankDataDTOList);

    /**
     * 保存订单银行信息,OrderBankDataDTO所有字段必填。有则插入，无则更新
     * @param orderBankDataDTOList 银行卡数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> saveOrderBankData(List<OrderBankDataDTO> orderBankDataDTOList);

    /**
     * 获取订单银行信息
     * @param orderIdList 订单id
     * @return 银行数据
     */
    RemoteResponse<List<OrderBankDataDTO>> getOrderBankData(List<Integer> orderIdList);

    /**
     * 删除订单银行信息
     * @param orderIdList 订单id
     * @return 是否成功
     */
    RemoteResponse<Boolean> deleteOrderBankData(List<Integer> orderIdList);
}
