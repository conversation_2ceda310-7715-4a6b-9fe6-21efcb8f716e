package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @title: FundCardManagerDTO
 * @projectName research-statement-web
 * @description: 经费卡负责人实体
 * @author：z<PERSON><PERSON><PERSON><PERSON>
 * @date 2020-01-03 15:37
 */
public class PrintFundCardManagerDTO implements Serializable {

    private static final long serialVersionUID = 1149371816819663189L;

    /**
     * 经费卡号
     */
    private List<String> fundCardNoList;

    /**
     * 项目编码
     */
    private String fundCardProjectNo;

    /**
     * 项目名称
     */
    private String fundCardProjectName;

    /**
     * 经费卡负责人
     */
    private List<String> managerList;

    public List<String> getFundCardNoList() {
        return fundCardNoList;
    }

    public void setFundCardNoList(List<String> fundCardNoList) {
        this.fundCardNoList = fundCardNoList;
    }

    public String getFundCardProjectNo() {
        return fundCardProjectNo;
    }

    public void setFundCardProjectNo(String fundCardProjectNo) {
        this.fundCardProjectNo = fundCardProjectNo;
    }

    public String getFundCardProjectName() {
        return fundCardProjectName;
    }

    public void setFundCardProjectName(String fundCardProjectName) {
        this.fundCardProjectName = fundCardProjectName;
    }

    public List<String> getManagerList() {
        return managerList;
    }

    public void setManagerList(List<String> managerList) {
        this.managerList = managerList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("FundCardManagerDTO{");
        sb.append("fundCardNoList=").append(fundCardNoList);
        sb.append(", fundCardProjectNo='").append(fundCardProjectNo).append('\'');
        sb.append(", fundCardProjectName='").append(fundCardProjectName).append('\'');
        sb.append(", managerList=").append(managerList);
        sb.append('}');
        return sb.toString();
    }
}