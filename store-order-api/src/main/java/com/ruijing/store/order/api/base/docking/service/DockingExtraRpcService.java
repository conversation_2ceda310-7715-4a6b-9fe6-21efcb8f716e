package com.ruijing.store.order.api.base.docking.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingRequestDTO;

import java.rmi.Remote;
import java.util.List;

/**
 * @description: 第三方对接表rpc 服务
 * @author: zhuk
 * @create: 2019-09-25 14:23
 **/
public interface DockingExtraRpcService {


    /**
     * 批量插入
     * @param dockingExtraDTOList
     * @return
     */
    RemoteResponse<Integer> insertList(List<DockingExtraDTO> dockingExtraDTOList);

    /**
     * 新增dockingExtra
     * @param dockingExtraDTO
     * @return
     */
    RemoteResponse insertDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 查询 DockingExtra
     * @param dockingExtraDTO 入参
     * @return  List<DockingExtraDTO>
     */
    RemoteResponse<List<DockingExtraDTO>> findDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 查询 DockingExtra
     * @param dockingExtraDTO 入参
     */
    RemoteResponse updateDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 根据info 修改 extraInfo
     * @param dockingExtraDTO
     * @return
     */
    RemoteResponse updateExtraInfoByInfo(DockingExtraDTO dockingExtraDTO);

    /**
     * 查询 DockingExtra 第三方对接订单信息
     * @param request 入参，infoList，不可为空，数量不可超过500
     * @return  List<DockingExtraDTO>
     */
    RemoteResponse<List<DockingExtraDTO>> findDockingExtraByInfoList(DockingRequestDTO request);

    /**
     * 通过 DockingExtra 对接单号信息和 type 查询对接单信息
     * @param request 入参，extraInfoList, 不可为空，数量不可超过100, type不可为空
     * @return
     */
    RemoteResponse<List<DockingExtraDTO>> findDockingByExtraInfoAndType(DockingRequestDTO request);

    /**
     * 通过 info 订单号更新记录
     * @param request 入参，info, 不可为空
     * @return
     */
    RemoteResponse<Integer> saveOrUpdateByInfo(DockingExtraDTO request);
}
