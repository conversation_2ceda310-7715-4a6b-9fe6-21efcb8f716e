package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/20 18:53
 * @description
 */
@RpcMethod("入库日志")
public class OrderWarehousePrintEntryLogDTO implements Serializable {

    private static final long serialVersionUID = -2732592676048011790L;

    @RpcModelProperty("日志id")
    private Integer id;
    
    /**
     * 入库单id
     */
    @RpcModelProperty("入库单id")
    private Integer entryId;
    
    /**
     * 操作用户guid
     */
    @RpcModelProperty("操作用户guid")
    private String userGuid;
    
    /**
     * 操作用户名字
     */
    @RpcModelProperty("操作用户名字")
    private String userName;
    /**
     * 业务类型0：申请入库，1入库审批
     */
    @RpcModelProperty("业务类型0：申请入库，1入库审批")
    private Integer businessType;
    /**
     * 业务描述
     */
    @RpcModelProperty("业务描述")
    private String businessDesc;

    @RpcModelProperty("创建时间")
    private Date createTime;

    @RpcModelProperty("备注")
    private String remark;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEntryId() {
        return entryId;
    }

    public void setEntryId(Integer entryId) {
        this.entryId = entryId;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessDesc() {
        return businessDesc;
    }

    public void setBusinessDesc(String businessDesc) {
        this.businessDesc = businessDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderWarehousePrintEntryLogDTO{");
        sb.append("id=").append(id);
        sb.append(", entryId=").append(entryId);
        sb.append(", userGuid='").append(userGuid).append('\'');
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", businessType=").append(businessType);
        sb.append(", businessDesc='").append(businessDesc).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", remark='").append(remark).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
