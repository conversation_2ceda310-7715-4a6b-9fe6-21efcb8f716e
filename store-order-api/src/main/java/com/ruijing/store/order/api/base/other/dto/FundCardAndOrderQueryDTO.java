package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.enums.RefFundCardOrderTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/5/13 0013 10:53
 * @Version 1.0
 * @Desc:描述
 */
public class FundCardAndOrderQueryDTO implements Serializable {

    private static final long serialVersionUID = 5287834713902236783L;

    /**
     * 采购单或者订单id或竞价单id
     */
    private Integer businessId;

    /**
     * 订单类型
     */
    private RefFundCardOrderTypeEnum refFundCardOrderTypeEnum;

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer orderId) {
        this.businessId = orderId;
    }

    public RefFundCardOrderTypeEnum getRefFundCardOrderTypeEnum() {
        return refFundCardOrderTypeEnum;
    }

    public void setRefFundCardOrderTypeEnum(RefFundCardOrderTypeEnum refFundCardOrderTypeEnum) {
        this.refFundCardOrderTypeEnum = refFundCardOrderTypeEnum;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("FundCardAndOrderQueryDTO{");
        sb.append("businessId=").append(businessId);
        sb.append(", refFundCardOrderTypeEnum=").append(refFundCardOrderTypeEnum);
        sb.append('}');
        return sb.toString();
    }
}
