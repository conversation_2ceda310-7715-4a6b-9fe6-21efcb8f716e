package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@RpcModel("退货日志DTO")
public class GoodsReturnLogDTO implements Serializable {

    private static final long serialVersionUID = 1617875464185917950L;

    @RpcModelProperty("id")
    private Integer id;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("退货单id")
    private Integer returnId;

    @RpcModelProperty("操作人")
    private String operatorName;

    /**
     * 参考枚举
     * {@link com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum}
     */
    @RpcModelProperty("操作内容")
    private Integer operationType;

    @RpcModelProperty("时间")
    private Date createTime;

    @RpcModelProperty("备注")
    private String remark;

    /**
     * 退货图片
     */
    @RpcModelProperty("凭证数组")
    private List<String> imagesURL;

    public Integer getId() {
        return id;
    }

    public GoodsReturnLogDTO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public GoodsReturnLogDTO setReturnId(Integer returnId) {
        this.returnId = returnId;
        return this;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public GoodsReturnLogDTO setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public GoodsReturnLogDTO setOperationType(Integer operationType) {
        this.operationType = operationType;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public GoodsReturnLogDTO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public GoodsReturnLogDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public List<String> getImagesURL() {
        return imagesURL;
    }

    public GoodsReturnLogDTO setImagesURL(List<String> imagesURL) {
        this.imagesURL = imagesURL;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public GoodsReturnLogDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    @Override
    public String toString() {
        return "GoodsReturnLogDTO{" +
                "id=" + id +
                "orderId=" + orderId +
                ", returnId=" + returnId +
                ", operatorName='" + operatorName + '\'' +
                ", operationType=" + operationType +
                ", createTime=" + createTime +
                ", remark='" + remark + '\'' +
                ", imagesURL=" + imagesURL +
                '}';
    }
}
