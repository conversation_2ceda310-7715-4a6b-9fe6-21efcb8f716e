package com.ruijing.store.order.api.search.enums;

/**
 * 订单source字段
 */
public enum OrderSourceEnum {

    /**
     * id
     */
    id("id"),

    /**
     * 订单号
     */
    forderno("forderno"),

    /**
     * 部门名称
     */
    fbuydepartment("fbuydepartment"),

    /**
     * 结算单id
     */
    statement_id("statement_id"),

    /**
     * 经费卡
     */
    card("card");

    public String field;

    OrderSourceEnum(String field) {
        this.field = field;
    }
}
