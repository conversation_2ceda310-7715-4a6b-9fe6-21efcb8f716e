package com.ruijing.store.order.api.base.orderextra.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2021/6/29 9:40
 */
@RpcModel("订单额外信息")
public class OrderExtraDTO implements Serializable {

    private static final long serialVersionUID = 2288806853625090224L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 必填，订单主表id，0无意义
     */
    @RpcModelProperty("必填，订单主表id，0无意义，")
    private Integer orderId;

    /**
     * 必填，订单号，没有就找订单
     */
    @RpcModelProperty("必填，订单号，没有就找订单")
    private String orderNo;

    /**
     * 单位id，0无意义，除非其他地方已经有，否则必填
     */
    @RpcModelProperty("单位id，0无意义，除非其他地方已经有，否则必填")
    private Integer orgId;

    /**
     * 必填，单位个性化操作类型，0无意义，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}
     */
    @RpcModelProperty("必填，单位个性化操作类型，0无意义，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}")
    private Integer extraKey;

    /**
     * 必填, 拓展表key描述，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}
     */
    @RpcModelProperty("必填, 拓展表key描述，由枚举维护{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}")
    private String extraKeyDesc;

    /**
     * 必填，单位个性化描述字串，即具体存的值是什么
     */
    @RpcModelProperty("必填，单位个性化描述字串，即具体存的值是什么")
    private String extraValue;

    /**
     * 生成时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public String getExtraKeyDesc() {
        return extraKeyDesc;
    }

    public void setExtraKeyDesc(String extraKeyDesc) {
        this.extraKeyDesc = extraKeyDesc;
    }

    public String getExtraValue() {
        return extraValue;
    }

    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExtraDTO{");
        sb.append("id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orgId=").append(orgId);
        sb.append(", extraKey=").append(extraKey);
        sb.append(", extraKeyDesc='").append(extraKeyDesc).append('\'');
        sb.append(", extraValue='").append(extraValue).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
