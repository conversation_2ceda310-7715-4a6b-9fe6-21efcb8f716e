package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

/**
 * 机构配置响应参数
 */
public class OrgConfigResponseDTO implements Serializable {
    private static final long serialVersionUID = 9176076303719563970L;
    private String orgCode;
    private String configCode;
    private String configValue;

    public OrgConfigResponseDTO() {
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getConfigCode() {
        return this.configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigValue() {
        return this.configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("BaseConfigDTO{");
        sb.append("orgCode='").append(this.orgCode).append('\'');
        sb.append(", configCode='").append(this.configCode).append('\'');
        sb.append(", configValue='").append(this.configValue).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
