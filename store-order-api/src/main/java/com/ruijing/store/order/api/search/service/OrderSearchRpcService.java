package com.ruijing.store.order.api.search.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description: 订单搜索RPC接口
 * @author: zhuk
 * @create: 2019-08-22 14:47
 **/
public interface OrderSearchRpcService {

    /**
     * 订单搜索通用查询
     * @param orderSearchParamDTO
     * pageSize 默认 20
     * pageNo 默认 0
     * @return
     */
    RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> commonSearch(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 根据结算单id 查询订单信息
     * @param orderSearchParamDTO
     * @return
     */
    RemoteResponse<List<OrderMasterSearchDTO>> searchOrderByStatementIdList(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 供应商获取退货单数据
     * @param orderPullParamDTO 入参
     * @return RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>>
     */
    RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>> getReturnOrderForSupp(OrderPullParamDTO orderPullParamDTO);

    /**
     * 按时间范围 推送数据给供应商
     * @param orderPullParamDTO
     * @return
     */
    RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>> getBaseOrderForSupp(OrderPullParamDTO orderPullParamDTO);

    /**
     * 根据订单id 搜索订单
     * @param orderSearchParamDTO   订单id
     * @return  List<OrderMasterSearchDTO>
     */
    RemoteResponse<List<OrderMasterSearchDTO>> searchOrderById(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 根据订单id 搜索订单
     * @param orderSearchParamDTO   订单idList 大小不能超过500
     * @return  List<OrderMasterSearchDTO>
     */
    RemoteResponse<List<OrderMasterSearchDTO>> searchOrderByIdList(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 根据订单idList 搜索订单.并计算返回 现在实际的金额
     * @param orderSearchParamDTO  订单idList 大小不能超过500
     * @return  List<OrderMasterSearchDTO>
     */
    RemoteResponse<List<OrderMasterSearchDTO>> searchActualOrderByIdList(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 统计某段时间活跃供应商数量（排除演示供应商）
     * @param omsStatisticsParamDTO 时间范围，状态list， getNoSuppIdList
     * @return 供应商数量
     */
    RemoteResponse<Double> countSuppForOms(OmsStatisticsParamDTO omsStatisticsParamDTO);

    /**
     * 统计一段时间内总买家量（排除演示单位）
     * @param omsStatisticsParamDTO  fuserid列表（不包含）
     * @return 买家数量
     */
    RemoteResponse<Double> countBuyersForOms(OmsStatisticsParamDTO omsStatisticsParamDTO);

    /**
     * 统计 订单金额，课题组数量，供应商数量
     * @param paramDTO 入参
     * @return 订单金额，课题组数量，供应商数量
     */
    RemoteResponse<StatisticsManagerResultDTO> countStatisticsOrder(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计供应商数量
     * @param paramDTO 入参
     * @return 供应商数量
     */
    RemoteResponse<StatisticsManagerResultDTO> countSupplierQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计课题组数量
     * @param paramDTO 入参
     * @return 课题组数量
     */
    RemoteResponse<StatisticsManagerResultDTO> countDepartmentQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单管理 统计订单金额
     * @param paramDTO 入参
     * @return 订单金额
     */
    RemoteResponse<StatisticsManagerResultDTO> countOrderTotalAmount(StatisticsManagerParamDTO paramDTO);

    /**
     * 查询用户个状态的 订单数量
     * @param orderStatisticsParamDTO 入参 用户id 组织id
     * @return <status, count> status -> order status; count -> count of order status
     */
    RemoteResponse<Map<Integer,Integer>> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO);

    /**
     * 统计一段时间 某个产品的销量
     * @param productSalesParamDTO 入参
     * @return ProductSalesResultDTO
     */
    RemoteResponse<ProductSalesResultDTO> countProductSales(ProductSalesParamDTO productSalesParamDTO);

    /**
     * 查询医院购买量top N的商品
     * @param dto 入参
     * @return List<OrderOrgStatResultDTO>
     */
    RemoteResponse<List<OrderOrgStatResultDTO>> countOrgTopProduct(OrderOrgStatParamDTO dto);

    /**
     * 查询统计相关合作商在该医院的销量
     * @param dto 入参
     * @return BigDecimal
     */
    RemoteResponse<BigDecimal> sumSuppOrgSale(OrderOrgSuppStatParamDTO dto);

    /**
     * 订单属性 聚合 订单金额和数量
     * @param paramDTO
     * @return
     */
    RemoteResponse<List<OrderAggregationResultDTO>> aggOrderAmountAndCount(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单商品属性 聚合  金额 和 数量
     * @param paramDTO
     * @return
     */
    RemoteResponse<List<OrderAggregationResultDTO>> aggProductAmountAndCount(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单商品属性 聚合  金额 和 数量
     * @param paramDTO
     * @return
     */
    RemoteResponse<List<OrderDateHistogramResultDTO>> aggOrderAmountDateHistogram(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计商品的 数量 和金额
     * @param paramDTO
     * @return
     */
    RemoteResponse<OrderAggregationResultDTO> sumProductAmountAndQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计订单各个状态的数量
     * @param request   入参
     * @return          <OrderStatus, count>
     */
    RemoteResponse<Map<OrderStatusEnum, Long>> countOrderStatusStatistics(OrderStatisticsParamDTO request);
}
