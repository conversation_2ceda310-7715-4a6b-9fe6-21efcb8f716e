package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

public class OrderAcceptDetailDTO implements Serializable {
    private static final long serialVersionUID = 2285613697613305270L;

    /**
     * 订单明细id
     */
    @RpcModelProperty("订单明细id")
    private Integer detailId;

    /**
     * 外观, 磨损度
     */
    @RpcModelProperty("外观, 磨损度")
    private Integer exterior;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getExterior() {
        return exterior;
    }

    public void setExterior(Integer exterior) {
        this.exterior = exterior;
    }

    @Override
    public String toString() {
        return "orderAcceptDetailDTO{" +
                "detailId=" + detailId +
                ", exterior=" + exterior +
                '}';
    }
}