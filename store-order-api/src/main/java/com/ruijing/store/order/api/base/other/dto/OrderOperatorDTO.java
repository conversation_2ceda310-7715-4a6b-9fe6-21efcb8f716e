package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *  操作人/提交人 通用dto
 */
public class OrderOperatorDTO implements Serializable {

    private static final long serialVersionUID = 8677041053064963890L;

    /**
     * 提交人所属机构编码
     */
    private String orgCode;

    /**
     * 提交人userId
     */
    private Integer userId;

    /**
     * 提交人工号
     */
    private String jobNumber;

    /**
     * 提交人用户名
     */
    private String userName;

    /**
     * 提交日期
     */
    private Date operateDate;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 经费负责人
     */
    private String fundManagerName;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getTelephone() {
        return telephone;
    }

    public OrderOperatorDTO setTelephone(String telephone) {
        this.telephone = telephone;
        return this;
    }

    public String getFundManagerName() {
        return fundManagerName;
    }

    public void setFundManagerName(String fundManagerName) {
        this.fundManagerName = fundManagerName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOperatorDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append(", userId=").append(userId);
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append(", userName=").append(userName);
        sb.append(", operateDate=").append(operateDate);
        sb.append('}');
        return sb.toString();
    }
}
