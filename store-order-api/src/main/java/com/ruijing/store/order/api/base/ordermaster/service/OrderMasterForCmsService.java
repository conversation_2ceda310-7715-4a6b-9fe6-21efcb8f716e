package com.ruijing.store.order.api.base.ordermaster.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterForCmsReq;

import java.util.List;

/**
 * @description: 提供给cms的订单接口
 * @author: zhuk
 * @create: 2019-07-02 09:57
 **/
public interface OrderMasterForCmsService {

    /**
     * 根据 采购人 和订单状态 查询订单
     * @return
     */
    RemoteResponse<List<OrderMasterDTO>> findOrderMasterByBuyerAndStatus(OrderMasterForCmsReq orderMasterForCmsReq);

}
