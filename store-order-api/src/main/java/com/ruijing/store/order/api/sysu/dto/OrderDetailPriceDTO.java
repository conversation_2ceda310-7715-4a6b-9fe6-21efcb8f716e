package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 订单价格dto类
 * @date 2023/10/31 16
 */
public class OrderDetailPriceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单详情id")
    private Integer orderDetailId;

    @RpcModelProperty("单价")
    private BigDecimal bidPrice;

    @RpcModelProperty("总价")
    private BigDecimal bidAmount;

    @RpcModelProperty("余额")
    private BigDecimal remainderPrice;

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public BigDecimal getBidPrice() {
        return bidPrice;
    }

    public void setBidPrice(BigDecimal bidPrice) {
        this.bidPrice = bidPrice;
    }

    public BigDecimal getBidAmount() {
        return bidAmount;
    }

    public void setBidAmount(BigDecimal bidAmount) {
        this.bidAmount = bidAmount;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }
}
