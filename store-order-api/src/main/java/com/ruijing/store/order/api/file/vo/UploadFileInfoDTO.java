package com.ruijing.store.order.api.file.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:22
 * @description
 */
@RpcModel("文件上传信息返回体")
public class UploadFileInfoDTO implements Serializable {

    private static final long serialVersionUID = 1798189980890674791L;
    
    @RpcModelProperty("订单ID")
    private Integer orderId;

    @RpcModelProperty("订单号")
    private String orderNo;
    
    @RpcModelProperty("文件类型")
    private Integer fileBusinessType;

    @RpcModelProperty(value = "文件ID")
    private Long fileId;

    @RpcModelProperty("文件路径")
    private String url;

    @RpcModelProperty("文件名")
    private String fileName;

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getFileBusinessType() {
        return fileBusinessType;
    }

    public void setFileBusinessType(Integer fileBusinessType) {
        this.fileBusinessType = fileBusinessType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "UploadFileInfoDTO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", fileBusinessType=" + fileBusinessType +
                ", fileId=" + fileId +
                ", url='" + url + '\'' +
                ", fileName='" + fileName + '\'' +
                '}';
    }
}
