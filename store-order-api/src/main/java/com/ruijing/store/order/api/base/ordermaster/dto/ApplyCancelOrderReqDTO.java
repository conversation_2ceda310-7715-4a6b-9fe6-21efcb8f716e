package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 申请取消订单接口
 * @author: zhuk
 * @create: 2019-07-08 13:49
 **/
@RpcModel("订单管理-取消订单请求")
public class ApplyCancelOrderReqDTO implements Serializable {

    private static final long serialVersionUID = -6820838984612487639L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 采购单id
     */
    @RpcModelProperty("采购单id")
    private Integer appId;

    /**
     * 取消时间
     */
    @RpcModelProperty("取消时间")
    private Date fcanceldate;

    /**
     * 取消人id
     */
    @RpcModelProperty("取消人id")
    private String fcancelmanid;

    /**
     * 取消人username
     */
    @RpcModelProperty("取消人username")
    private String fcancelman;

    /**
     * 取消原因
     */
    @RpcModelProperty("取消原因")
    private String fcancelreason;

    /**
     * 订单状态
     */
    @RpcModelProperty("订单状态")
    private Integer status;

    /**
     * 组织名
     */
    @RpcModelProperty("组织名")
    private String orgCode;

    @RpcModelProperty("组织id")
    private Integer orgId;

    /**
     * 职业编号
     */
    @RpcModelProperty("职业编号")
    private String jobNumber;

    /**
     * 申请取消订单操作人类型
     */
    @RpcModelProperty(value = "申请取消订单操作人类型", hidden = true)
    private Integer opUserType;

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Date getFcanceldate() {
        return fcanceldate;
    }

    public void setFcanceldate(Date fcanceldate) {
        this.fcanceldate = fcanceldate;
    }

    public String getFcancelmanid() {
        return fcancelmanid;
    }

    public void setFcancelmanid(String fcancelmanid) {
        this.fcancelmanid = fcancelmanid;
    }

    public String getFcancelman() {
        return fcancelman;
    }

    public void setFcancelman(String fcancelman) {
        this.fcancelman = fcancelman;
    }

    public String getFcancelreason() {
        return fcancelreason;
    }

    public void setFcancelreason(String fcancelreason) {
        this.fcancelreason = fcancelreason;
    }

    public Integer getOpUserType() {
        return opUserType;
    }

    public void setOpUserType(Integer opUserType) {
        this.opUserType = opUserType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ApplyCancelOrderReqDTO{");
        sb.append("orderId=").append(orderId);
        sb.append("appId=").append(appId);
        sb.append(", fcanceldate=").append(fcanceldate);
        sb.append(", fcancelmanid='").append(fcancelmanid).append('\'');
        sb.append(", fcancelman='").append(fcancelman).append('\'');
        sb.append(", fcancelreason='").append(fcancelreason).append('\'');
        sb.append(", status=").append(status);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", orgId='").append(orgId).append('\'');
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
