package com.ruijing.store.order.api.advertisement.service;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderDTO;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @description oms广告投放订单相关接口
 * @date 2023/9/4 16:02
 */
public interface AdvertisementOrderRpcService {

    PageableResponse<List<AdvertisementOrderDTO>> listAdvertisementOrder(AdvertisementOrderRequest request);
}
