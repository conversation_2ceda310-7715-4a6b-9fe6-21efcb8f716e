package com.ruijing.store.order.api.general.dto;/**
 * Created by <PERSON><PERSON><PERSON> on 2019/5/31.
 */

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: store-order-service
 *
 * @description: 订单提供给结算接口的请求参数
 *
 * @author: zhuk
 *
 * @create: 2019-05-31 10:13
 **/

public class OrderStatementReqDTO extends BaseReqDTO implements Serializable {

    private static final long serialVersionUID = 2591968340241613782L;

    private List<String> statementIds = new ArrayList<>();

    public OrderStatementReqDTO() {
        super();
    }

    public OrderStatementReqDTO(List<String> statementIds) {
        this.statementIds = statementIds;
    }

    public List<String> getStatementIds() {
        return statementIds;
    }

    public void setStatementIds(List<String> statementIds) {
        this.statementIds = statementIds;
    }

    @Override
    public String toString() {
        return "OrderStatementReqDTO{" +
                "statementIds=" + statementIds +
                '}';
    }
}
