package com.ruijing.store.order.api.base.enums;

/**
 * 订单打印数据的类型
 */
public class OrderPrintDataConstant {

    /**
     * 采购单类型
     */
    public static final int PURCHASE = 1;

    /**
     * 入库单类型
     */
    public static final int ENTRY_WAREHOUSE = 2;

    /**
     * 出库单类型
     */
    public static final int EXIT_WAREHOUSE = 3;

    /**
     * 退货单类型
     */
    public static final int GOODS_RETURN = 4;

    /**
     * 发票相关信息
     */
    public static final int INVOICE_INFO = 5;

    /**
     * 送货单相关信息
     */
    public static final int DELIVERY_INFO = 6;
}
