package com.ruijing.store.order.api.delivery.search.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryAggResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryCountResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryDateHistogramResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryStatisticsRequestDTO;
import com.ruijing.store.order.api.search.dto.OrderStatisticsParamDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerResultDTO;

import java.util.List;

/**
 * @author: li<PERSON>yu
 * @createTime: 2023-03-28 17:15
 * @description:
 **/
@RpcApi("代配送订单搜索相关服务")
public interface DeliveryProxySearchRpcService {

    /**
     * 统计代配送数量
     * @param orderStatisticsParamDTO 统计参数
     * @return 数量
     */
    @RpcMethod("统计代配送订单数量")
    RemoteResponse<DeliveryCountResultDTO> countOrder(OrderStatisticsParamDTO orderStatisticsParamDTO);

    /**
     * 统计代配送订单总金额、总数量--按指定纬度聚合
     * @param paramDTO 入参
     * @return 订单金额，课题组数量，供应商数量
     */
    @RpcMethod("统计代配送订单总金额、总数量--按指定纬度聚合")
    RemoteResponse<StatisticsManagerResultDTO> aggForTotal(DeliveryStatisticsRequestDTO paramDTO);

    /**
     * 根据聚合字段，聚合订单数量和总金额--按指定纬度聚合
     * @param paramDTO 聚合参数
     * @return 聚合结果
     */
    @RpcMethod("根据聚合字段，聚合订单数量和总金额--按指定纬度聚合")
    PageableResponse<List<DeliveryAggResultDTO>> aggOrderAmountAndCountByAggField(DeliveryStatisticsRequestDTO paramDTO);

    /**
     * 按照时间段聚合订单数量和金额（直方图）。如果传入aggField，会先按aggField聚合，再根据时间聚合（用于导出excel）
     * @param paramDTO 入参
     * @return 聚合结果
     */
    @RpcMethod("按照时间段聚合订单数量和金额（直方图）")
    PageableResponse<List<DeliveryDateHistogramResultDTO>> aggOrderAmountAndCountDateHistogram(DeliveryStatisticsRequestDTO paramDTO);
}
