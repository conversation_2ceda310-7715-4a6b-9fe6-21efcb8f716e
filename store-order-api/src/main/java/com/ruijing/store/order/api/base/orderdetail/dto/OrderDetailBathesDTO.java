package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

@RpcModel("订单明细批次条形码信息")
public class OrderDetailBathesDTO implements Serializable {

    private static final long serialVersionUID = -3520737043893304184L;

    @RpcModelProperty("订单明细id")
    private Integer detailId;

    @RpcModelProperty("商品名")
    private String productName;

    @RpcModelProperty("货号")
    private String productCode;

    @RpcModelProperty("规格")
    private String spec;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("cas号")
    private String casNo;

    @RpcModelProperty("二级分类id")
    private Integer secondCategoryId;

    @RpcModelProperty("二级分类名")
    private String secondCategoryName;

    @RpcModelProperty("批次条形码")
    private List<BatchesBarCodeDTO> batches;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderDetailBathesDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public OrderDetailBathesDTO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderDetailBathesDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getSpec() {
        return spec;
    }

    public OrderDetailBathesDTO setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderDetailBathesDTO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public OrderDetailBathesDTO setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public OrderDetailBathesDTO setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
        return this;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public OrderDetailBathesDTO setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public List<BatchesBarCodeDTO> getBatches() {
        return batches;
    }

    public OrderDetailBathesDTO setBatches(List<BatchesBarCodeDTO> batches) {
        this.batches = batches;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailBathesDTO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("productName='" + productName + "'")
                .add("productCode='" + productCode + "'")
                .add("spec='" + spec + "'")
                .add("brand='" + brand + "'")
                .add("casNo='" + casNo + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("batches=" + batches)
                .toString();
    }
}
