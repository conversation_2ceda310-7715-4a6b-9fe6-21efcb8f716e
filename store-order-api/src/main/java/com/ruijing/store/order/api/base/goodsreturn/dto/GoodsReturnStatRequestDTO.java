package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2021/7/2 16:51
 */
@RpcModel("退货——统计请求体")
public class GoodsReturnStatRequestDTO implements Serializable {

    private static final long serialVersionUID = -5556634533636091342L;

    /**
     * 单位id列表
     */
    @RpcModelProperty(value = "单位id列表")
    private List<Integer> orgIdList;

    /**
     * 统计开始时间，非空
     */
    @RpcModelProperty(value = "统计开始时间戳，非空")
    private Long startTime;

    /**
     * 统计结束时间，非空
     */
    @RpcModelProperty(value = "统计结束时间戳，非空")
    private Long endTime;

    /**
     * 需要的订单状态列表，空则全部{@link com.ruijing.store.order.api.base.enums.OrderStatusEnum}
     */
    @RpcModelProperty(value = "需要的订单状态列表，空则全部", description = "需要的订单状态列表，空则全部.com.ruijing.store.order.api.base.enums.OrderStatusEnum维护")
    private List<Integer> filterOrderStatusList;

    /**
     * 需要的退货单状态列表，默认为完成退货，空则返回全部{@link com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum}
     */
    @RpcModelProperty(value = "需要的退货操作状态列表", description = "需要的退货操作状态列表。com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum维护")
    private List<Integer> filterReturnOperationList;

    /**
     * 入库状态{@link com.ruijing.store.order.api.base.enums.InventoryStatusEnum}
     */
    @RpcModelProperty(value = "入库状态", description = "com.ruijing.store.order.api.base.enums.InventoryStatusEnum维护")
    private Integer inventoryStatus;

    public List<Integer> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Integer> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getFilterOrderStatusList() {
        return filterOrderStatusList;
    }

    public void setFilterOrderStatusList(List<Integer> filterOrderStatusList) {
        this.filterOrderStatusList = filterOrderStatusList;
    }

    public List<Integer> getFilterReturnOperationList() {
        return filterReturnOperationList;
    }

    public void setFilterReturnOperationList(List<Integer> filterReturnOperationList) {
        this.filterReturnOperationList = filterReturnOperationList;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnStatRequestDTO{");
        sb.append("orgIdList=").append(orgIdList);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", filterOrderStatusList=").append(filterOrderStatusList);
        sb.append(", filterReturnOperationList=").append(filterReturnOperationList);
        sb.append(", inventoryStatus=").append(inventoryStatus);
        sb.append('}');
        return sb.toString();
    }
}
