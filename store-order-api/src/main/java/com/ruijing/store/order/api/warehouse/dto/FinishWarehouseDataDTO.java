package com.ruijing.store.order.api.warehouse.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/5 10:25
 * @description
 */
@RpcModel("完成入库参数")
public class FinishWarehouseDataDTO implements Serializable {
    
    private static final long serialVersionUID = -3610066909138050273L;
    
    /**
     * 订单id
     */
    @RpcModelProperty("订单主键")
    private Integer orderId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Override
    public String toString() {
        return "FinishWarehouseDataDTO{" +
                "orderId=" + orderId +
                '}';
    }
}
