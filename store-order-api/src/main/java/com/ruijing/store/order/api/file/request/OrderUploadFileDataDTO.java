package com.ruijing.store.order.api.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.enums.FileBusinessTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/29 11:30
 * @description 订单上传文件信息
 */
@RpcModel("订单上传文件信息")
public class OrderUploadFileDataDTO implements Serializable {

    private static final long serialVersionUID = -3803938378547719188L;

    @RpcModelProperty("订单ID")
    private Integer orderId;

    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * FileBusinessTypeEnum
     */
    @RpcModelProperty(value = "文件类型", enumClass = FileBusinessTypeEnum.class)
    private Integer fileBusinessType;

    @RpcModelProperty("文件路径")
    private String url;

    @RpcModelProperty("文件名")
    private String fileName;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getFileBusinessType() {
        return fileBusinessType;
    }

    public void setFileBusinessType(Integer fileBusinessType) {
        this.fileBusinessType = fileBusinessType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "OrderUploadFileDataDTO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", fileBusinessType=" + fileBusinessType +
                ", url='" + url + '\'' +
                ", fileName='" + fileName + '\'' +
                '}';
    }
}
