package com.ruijing.store.order.api.base.enums;


/**
 * Author: zhukai
 * CreateTime : 2019/12/5 2:06 下午
 * Description: 订单日志 枚举 按照store的OrderApprovalEnum 慢慢迁移
 */
public enum OrderApprovalEnum {

    /**
     * 验收审批驳回
     */
    REJECT(0, "验收审批驳回"),

    /**
     * 验收审批通过
     */
    PASS(1, "验收审批通过"),

    /**
     * 已更新驳回订单
     */
    REJECT_FOR_STATEMENT(2, "结算单驳回订单"),

    /**
     * 重新上传图片
     */
    REPEAT_UPLOAD_IMAGE(3, "重新上传图片"),

    /**
     * 更新订单备注
     */
    UPDATE_REMARK(4, "更新订单备注"),

    /**
     * 取消订单
     */
    CANCEL(5, "取消订单"),

    /**
     * 订单确认收货
     */
    RECEIPT(6, "确认收货"),

    /**
     * 订单退货
     */
    GOODS_RETURN(7, "订单退货"),

    /**
     * 验收审批驳回
     */
    REJECT_DURING_RECEIVE_BY_BUYER(12, "验收审批驳回"),

    /**
     * 设备处验收审核驳回
     */
    REJECT_DURING_RECEIVE_BY_FACILITY(13, "验收审核驳回"),

    /**
     * 确认订单，待发货
     */
    SUPPLIER_CONFIRM_ORDER(8, "供应商确认订单，待发货"),

    /**
     * 14 解冻经费中
     */
    UNFROZEN_FUNDS(14, "解冻经费中"),

    /**
     * 结算撤销
     */
    SETTLEMENT_REVOKE(15, "结算撤销"),

    /**
     * 结算驳回
     */
    SETTLEMENT_REJECT(16, "结算驳回"),

    /**
     * 结算作废
     */
    SETTLEMENT_INVALID(17, "财务作废结算单"),

    /**
     * 解冻成功
     */
    UNFROZEN_SUCCESS(18, "解冻成功"),

    /**
     * 解冻失败
     */
    UNFROZEN_FAILURE(19, "解冻失败"),

    /**
     * 追加验收图片
     */
    ADDITIONAL_ACCEPTANCE_PICTURE(20, "追加验收图片"),

    /**
     * 拒绝取消
     */
    REFUSE_CANCEL(21, "拒绝取消"),

    /**
     * 定制化枚举，通知第三方供应商已发货
     */
    NOTICE_JILI_DISPATCH(22, "通知第三方供应商已发货"),

    /**
     * 定制化枚举，通知基里供应商同意退货
     */
    NOTICE_JILI_APPROVE_RETURN(23, "通知基里供应商同意退货"),

    /**
     * 定制化枚举，通知基里供应商拒绝退货
     */
    NOTICE_JILI_REJECT_RETURN(24, "通知基里供应商拒绝退货"),

    /**
     * 入库回调操作
     */
    IN_BOUND_CALLBACK(25, "入库回调操作"),

    /**
     * 验收审批换卡
     */
    APPROVAL_CHANGE_FUND_CARD(26, "验收审批换卡"),

    /**
     * 验收审批换卡成功
     */
    APPROVAL_CHANGE_FUND_CARD_SUCCESS(27, "验收审批换卡成功"),

    /**
     * 验收审批换卡失败
     */
    APPROVAL_CHANGE_FUND_CARD_FAILURE(28, "验收审批换卡失败"),

    /**
     * 超时自动验收审批
     */
    TIMEOUT_AUTO_APPROVAL_ORDER(29, "超时自动验收审批"),

    /**
     * 超时自动验收审批失败
     */
    TIMEOUT_AUTO_APPROVAL_ORDER_FAILURE(30, "超时自动验收审批失败"),

    MODIFIED_DELIVERY_ADDRESS(31, "修改订单收货地址"),

    /**
     * 拆分订单
     */
    ORDER_SPLIT_UP(32, "拆分订单"),

    PUSH_ORDER_TO_THIRD_FAILURE(33, "推送管理平台失败"),

    PUSH_ORDER_TO_THIRD_SUCCESS(34, "推送管理平台成功"),

    DELIVERY_PROXY_TURN_OFF(35, "取消代配送"),

    DELIVERY_PROXY_TURN_ON(36, "开启代配送"),

    /**
     * 完成订单
     */
    FINISH_ORDER(37, "完成订单"),

    /**
     * 推送发票
     */
    PUSH_INVOICE(38,"推送发票"),

    SUPPLIER_AGREE_CANCEL_ORDER(40, "商家同意取消订单"),

    /**
     * 采购人同意取消订单
     */
    BUYER_AGREE_CANCEL_ORDER(41, "同意取消订单"),

    SUPPLIER_REFUSE_CANCEL_ORDER(42, "商家拒绝取消订单"),

    /**
     * 采购人拒绝取消订单
     */
    BUYER_REFUSE_CANCEL_ORDER(43, "拒绝取消订单"),

    /**
     * 执行验收审批过程发生异常（只有产品指定的种类才记录：商品未完成退货/经费卡不可用）
     */
    ACCEPT_APPROVE_ERROR(44,"验收审批失败"),

    /**
     * 撤销 无需入库 的订单操作枚举
     */
    CANCEL_RECEIPT(45, "撤销入库"),
/*********************************************************************/
    //todo 以下枚举为oms修改订单数据对应枚举
    //目前使用 com.ruijing.store.order.business.enums.OmsFixDataEnum 替换
    /**
     * 修改订单状态
     */
    FIX_ORDER_STATUS(46, "修改订单状态"),

    /**
     * 修改经费状态
     */
    FIX_FUND_STATUS(47 , "修改经费状态"),

    /**
     * 重新冻结经费
     */
    FUND_RE_FREEZE(48, "重新冻结经费"),

    /**
     * 重新冻结经费
     */
    FUND_RE_UNFREEZE(49, "重新解冻经费"),

    /**
     * 解冻经费
     */
    FUND_UNFREEZE(50, "解冻经费"),
/*********************************************************************/

    /**
     * 系统作废汇总结算单，用于OMS异常数据处理
     */
    SYSTEM_INVALID_SETTLEMENT(51 ,"作废汇总结算单"),

    /**
     * 商家申请取消代配送
     */
    SUPP_APPLY_CANCEL_DELIVERY_PROXY(52, "取消锐竞送货上门服务"),

    /**
     * 采购人拒绝取消代配送
     */
    BUYER_REJECT_CANCEL_DELIVERY_PROXY(53, "已拒绝"),

    /**
     * 采购人同意取消代配送
     */
    BUYER_AGREE_CANCEL_DELIVERY_PROXY(54, "已同意"),

    /**
     * 入库后推送管理平台中
     */
    PUSHING_WAREHOUSE_TO_THIRD(55, "同步订单验收信息"),

    /**
     * 入库后推送管理平台成功
     */
    PUSH_WAREHOUSE_TO_THIRD_SUCCESS(56, "订单验收信息同步成功"),

    /**
     * 入库后推送管理平台失败
     */
    PUSH_WAREHOUSE_TO_THIRD_FAILURE(57, "订单验收信息同步失败"),

    /**
     * 推送管理平台订单状态中
     */
    PUSHING_ORDER_STATUS_TO_THIRD(58, "同步订单状态"),

    /**
     * 推送管理平台订单状态成功
     */
    PUSH_ORDER_STATUS_TO_THIRD_SUCCESS(59, "订单状态同步成功"),

    /**
     * 推送管理平台订单状态失败
     */
    PUSH_ORDER_STATUS_TO_THIRD_FAILURE(60, "订单状态同步失败"),

    /**
     * 订单关闭
     */
    ORDER_CLOSE(61, "订单关闭"),

    /**
     * 修改订单信息
     */
    CHANGE_ORDER_INFO(62, "修改订单信息"),

    /**
     * 中大二级验收审批通过
     */
    SECOND_LEVEL_ACCEPT_APPROVE_PASS(63, "验收复核通过"),

    /**
     * 中大二级验收审批驳回
     */
    SECOND_LEVEL_ACCEPT_APPROVE_REJECT(64, "验收复核驳回"),

    /**
     * 推送发票 成功/失败
     */
    PUSH_INVOICE_SUCCESS(65, "推送发票成功"),
    PUSH_INVOICE_FAIL(66, "推送发票失败"),

    MODIFY_INVOICE(67, "修改发票"),

    DELETE_AND_UPDATE_ACCEPTANCE_PHOTO(68, "删除后更新验收照片"),

    /**
     * 风险单日志
     */
    MODIFY_RISK_ORDER_TO_NORMAL(69, "风险订单恢复正常"),

    CLOSE_RISK_ORDER(70, "关闭风险订单"),

    APPLY_MODIFY_RISK_ORDER_TO_NORMAL(71, "申请恢复风险订单为正常单"),

    REJECT_RISK_ORDER_MODIFY_APPLY(72, "驳回恢复风险单的申请"),

    /**
     * 中大验收复核后需运营商审批相关日志
     */
    APPLY_PLATFORM_OPERATOR_APPROVAL(73, "验收复核需运营商核实"),

    PLATFORM_OPERATOR_APPROVE_PASS(74, "运营商核实通过"),

    PLATFORM_OPERATOR_APPROVE_REJECT(75, "运营商核实不通过"),

    CANCEL_ACCEPT_APPROVE(76, "取消验收审批"),

    SUPP_MODIFY_BATCHES(77, "供应商修改批次信息"),

    WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS(78, "待结算换卡成功"),

    WATING_STATEMENT_CHANGE_FUND_CARD_FAILURE(79, "待结算换卡失败"),

    SETTLEMENT_CHANGE_FUND_CARD_SUCCESS(80, "结算阶段换卡成功"),

    SETTLEMENT_CHANGE_FUND_CARD_FAILURE(81, "结算阶段换卡失败"),

    DELETE_ACCEPTANCE_PHOTO(82, "删除验收照片"),

    DELETE_ACCEPTANCE_ATTACHMENT(83, "删除验收附件"),

    ADDITIONAL_ACCEPTANCE_ATTACHMENT(84, "追加验收附件"),

    /**
     * 当前级别无需验收审批
     */
    NO_NEED_ACCEPT_APPROVAL(85, "无需审批"),

    CANCEL_DELIVERY(86, "取消发货"),

    /**
     * 暨大完成报账
     */
    COMPLETE_REPORT_EXPENSE(87, "确认报账"),

    /**
     * 流转验收审批
     */
    TRANSFER_ACCEPT_APPROVAL(88, "流转审批"),
    ;

    private Integer value;

    private String name;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    OrderApprovalEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static OrderApprovalEnum getByValue(Integer code) {
        for (OrderApprovalEnum item : OrderApprovalEnum.values()) {
            if (item.getValue().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameByValue(Integer code) {
        for (OrderApprovalEnum item : OrderApprovalEnum.values()) {
            if (item.getValue().equals(code)) {
                return item.getName();
            }
        }
        return "";
    }
}
