package com.ruijing.store.statistic.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.statistic.api.search.enums.AdvertisementOrderAggTermTypeEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 广告投放订单信息聚合请求类
 * @date 2023/9/4 14:49
 */
public class AdvertisementOrderAggRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("供应商id")
    private List<Integer> suppIdList;

    @RpcModelProperty("订单开始时间")
    private Date startTime;

    @RpcModelProperty("订单结束时间")
    private Date endTime;

    @RpcModelProperty("广告id")
    private List<Integer> advertisementIdList;

    @RpcModelProperty(value = "分组类型默认按供应商分组")
    private AdvertisementOrderAggTermTypeEnum termType;

    public List<Integer> getSuppIdList() {
        return suppIdList;
    }

    public void setSuppIdList(List<Integer> suppIdList) {
        this.suppIdList = suppIdList;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getAdvertisementIdList() {
        return advertisementIdList;
    }

    public void setAdvertisementIdList(List<Integer> advertisementIdList) {
        this.advertisementIdList = advertisementIdList;
    }

    public AdvertisementOrderAggTermTypeEnum getTermType() {
        return termType;
    }

    public void setTermType(AdvertisementOrderAggTermTypeEnum termType) {
        this.termType = termType;
    }
}
