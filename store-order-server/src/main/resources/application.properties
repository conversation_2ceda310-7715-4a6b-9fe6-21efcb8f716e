appkey=store-order-service
deployenv=uat

logging.config=classpath:logback-spring.xml

pagehelper.reasonable=false
pagehelper.row-bounds-with-count=true
pagehelper.offset-as-page-num=true

order.extension.aop.serviceLog=true
order.extension.simpleFilter=true
order.extension.simpleRedisClient=true

msharp.rpc.service.debug.enable=true
msharp.rpc.long.service.timeout.threshold=3000
msharp.gateway.long.service.timeout.threshold=3000
msharp.cluster.data.sync.enabled=true
msharp.dingtalk.alarm.user.phone.list=18924554293,19925760209
msharp.commons.async.core.poolSize=16
msharp.commons.async.core.maximum.poolSize=32

spring.main.allow-circular-references=true
# 国际化开关
log.filter.locale.enable=true