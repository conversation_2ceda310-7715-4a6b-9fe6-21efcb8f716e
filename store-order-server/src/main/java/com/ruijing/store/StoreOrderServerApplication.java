package com.ruijing.store;

import com.msharp.sharding.jdbc.springboot.autoconfigure.annotation.EnableMSharpDataSource;
import com.ruijing.cat.springboot.autoconfigure.annotation.EnableCat;
import com.ruijing.fundamental.springboot.starter.ServiceBootApplication;
import com.ruijing.pearl.annotation.EnablePearl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 服务启动类
 *
 * <AUTHOR>
 * @date 2019-05-27
 */
@MapperScan(basePackages = {"com.ruijing.store.order.base.*.mapper","com.ruijing.store.usertag.mapper"})
@SpringBootApplication
@EnablePearl
@EnableMSharpDataSource
@EnableCat
public class StoreOrderServerApplication {

    public static void main(String[] args) {
        ServiceBootApplication.main(StoreOrderServerApplication.class, args);
        System.out.printf("多服务测试1~！");
    }
}

