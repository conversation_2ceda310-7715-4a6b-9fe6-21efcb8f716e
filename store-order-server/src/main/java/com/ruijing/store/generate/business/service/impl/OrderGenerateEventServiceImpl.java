package com.ruijing.store.generate.business.service.impl;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.generate.business.service.OrderGenerateEventService;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.PurchaseApprovalLogClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-05-23 16:02
 */
@Service
public class OrderGenerateEventServiceImpl implements OrderGenerateEventService {

    /**
     * 系统用户id
     */
    private static final int SYSTEM_OPERATOR_ID = -1;

    /**
     * 系统用户名称
     */
    private static final String SYSTEM_OPERATOR_NAME = "系统";

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private final static String CAT_TYPE = "DefaultOuterBuyerRequestServiceImpl";

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Override
    public void approveSuccess(OrderMasterDO orderMasterDO, String reason) {
        // 记录日志
        orderApprovalLogService.saveApprovalLog(orderMasterDO.getId(), OrderApprovalEnum.UPDATE_REMARK.getValue(), SYSTEM_OPERATOR_ID, StringUtils.truncate(reason, DockingConstant.ORDER_APPROVAL_LOG_REASON_MAX_LENGTH));
        // 更新订单状态
        orderMasterMapper.updateStatusById(OrderStatusEnum.WaitingForConfirm.getValue(), orderMasterDO.getId());

        AsyncExecutor.listenableRunAsync(() -> {
            //待供应商确认状态，发送生成订单邮件邮件给供应商
            orderEmailHandler.sendOrderGenerateEmailToSupp(orderMasterDO);
            weChatMessageHandler.waitingConfirmToSupp(orderMasterDO);
        }).addFailureCallback(throwable -> {
            LOGGER.error("发送生成订单邮件邮件给供应商失败：" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "发送待确认订单邮件邮件给供应商失败：", throwable);
            throw new IllegalStateException(throwable);
        });
    }

    @Override
    public void approveFail(OrderMasterDO orderMasterDO, String reason) {
        // 记录日志
        orderApprovalLogService.saveApprovalLog(orderMasterDO.getId(), OrderApprovalEnum.CANCEL.getValue(), SYSTEM_OPERATOR_ID, StringUtils.truncate(reason, DockingConstant.ORDER_APPROVAL_LOG_REASON_MAX_LENGTH));
        // 更新订单状态
        orderMasterMapper.updateStatusById(OrderStatusEnum.Close.getValue(), orderMasterDO.getId());
    }
}
