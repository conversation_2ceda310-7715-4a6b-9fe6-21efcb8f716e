package com.ruijing.store.generate.callback.service.impl;

import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.generate.callback.service.OrderGenerateCallBackService;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date ：Created in 2022-05-25 11:46
 */
@Service
public class OrderGenerateCallBackServiceImpl implements OrderGenerateCallBackService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;


    /**
     * 系统用户id
     */
    private static final int SYSTEM_OPERATOR_ID = -1;

    /**
     * 系统用户名称
     */
    private static final String SYSTEM_OPERATOR_NAME = "系统";

    @Override
    public void pushOrderToBuyerCallBack(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderEventPushResultResponseDTO.getOrderNo());
        Integer currentStatus = orderMaster.getStatus();
        Integer orderId = orderMaster.getId();
        // 推送失败
        if (orderEventPushResultResponseDTO.getOrderEventStatusEnum().equals(OrderEventStatusEnum.FAILED)) {
            // 更新订单状态
            if (OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(currentStatus)) {
                OrderMasterDO updated = new OrderMasterDO();
                updated.setStatus(OrderStatusEnum.DeckingFail.getValue());
                orderMasterMapper.updateStatusByIdIn(updated, New.list(orderMaster.getId()));
            } else {
                logger.info(orderId + ":订单状态为" + currentStatus + ",生成订单推送失败,不进行订单状态更新");
            }
            // 记录订单推送失败日志
            orderApprovalLogService.saveApprovalLog(orderMaster.getId(), OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_FAILURE.getValue(), SYSTEM_OPERATOR_ID, orderEventPushResultResponseDTO.getFailReason());
        }
        // 推送成功
        else if (orderEventPushResultResponseDTO.getOrderEventStatusEnum().equals(OrderEventStatusEnum.COMPLETE)) {
            // 更新订单状态
            if (OrderStatusEnum.DeckingFail.getValue().equals(currentStatus)) {
                OrderMasterDO updated = new OrderMasterDO();
                updated.setStatus(OrderStatusEnum.WaitingForDockingConfirm.getValue());
                orderMasterMapper.updateStatusByIdIn(updated, New.list(orderMaster.getId()));
            } else {
                logger.info(orderId + ":订单状态为" + currentStatus + ",生成订单推送成功,不进行订单状态更新");
            }
            // 记录订单推送成功日志
            orderApprovalLogService.saveApprovalLog(orderMaster.getId(), OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_SUCCESS.getValue(), SYSTEM_OPERATOR_ID, null);
        }
    }
}
