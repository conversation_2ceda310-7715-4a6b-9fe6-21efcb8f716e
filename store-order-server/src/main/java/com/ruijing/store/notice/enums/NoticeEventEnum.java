package com.ruijing.store.notice.enums;

import com.ruijing.store.order.api.base.goodsreturn.GoodsReturnNoticeService;

/**
 * <AUTHOR>
 * @date 2022/9/16 18:31
 * @description 需要进行通知相关的事件枚举
 */
public enum NoticeEventEnum {
    
    /**
     * 采购人申请退货完成
     */
    BUYER_APPLY_RETURN_ORDER_COMPLETE(GoodsReturnNoticeService.class.getName(), "applyGoodsReturnComplete"),

    /**
     * 取消退货完成
     */
    CANCEL_GOODS_RETURN_COMPLETE(GoodsReturnNoticeService.class.getName(), "goodsReturnCancelComplete"),

    /**
     * 采购人申请退货前校验
     */
    VERIFY_BEFORE_BUYER_APPLY_RETURN(GoodsReturnNoticeService.class.getName(), "verifyBeforeApplyGoodsReturn");

    /**
     * 泛化调用的类名
     */
    private final String classNameToCall;

    /**
     * 泛化调用的方法名
     */
    private final String methodNameToCall;

    NoticeEventEnum(String classNameToCall, String methodNameToCall) {
        this.classNameToCall = classNameToCall;
        this.methodNameToCall = methodNameToCall;
    }

    public String getClassNameToCall() {
        return classNameToCall;
    }

    public String getMethodNameToCall() {
        return methodNameToCall;
    }
}
