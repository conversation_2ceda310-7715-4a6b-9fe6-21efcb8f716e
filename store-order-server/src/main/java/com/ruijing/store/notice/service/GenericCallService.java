package com.ruijing.store.notice.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.notice.enums.NoticeEventEnum;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2022/5/20 9:19
 * @description
 */
public interface GenericCallService {

    /**
     * 泛化调用
     *
     * @param appKey           外部服务的appKey
     * @param param            调用时的入参
     * @param classNameToCall  要进行泛化调用的类名
     * @param methodNameToCall 要进行泛化调用的方法名
     * @param timeout          超时时间
     * @param orderNo          发起调用的关联订单号
     * @param <T>              入参类型的泛型
     * @param <R>              回参的泛型
     * @return 远程返回的参数
     */
    <T, R> RemoteResponse<R> callMethod(String appKey, T param, String classNameToCall, String methodNameToCall, int timeout, String orderNo);

    /**
     * 泛化调用
     *
     * @param appKeyList       外部服务的appKey列表
     * @param param            调用时的入参
     * @param classNameToCall  要进行泛化调用的类名
     * @param methodNameToCall 要进行泛化调用的方法名
     * @param timeout          超时时间
     * @param orderNo          发起调用的关联订单号
     * @param <T>              入参类型的泛型
     * @return 远程返回的参数
     */
    <T> RemoteResponse<Boolean> callMethodToApps(List<String> appKeyList, T param, String classNameToCall, String methodNameToCall, int timeout, String orderNo) throws ExecutionException, InterruptedException;

    /**
     * 泛化调用
     *
     * @param noticeEventEnum  通知事件类型
     * @param orgCode          单位代码
     * @param param            调用时的入参
     * @param timeout          超时时间
     * @param orderNo          发起调用的关联订单号
     * @param <T>              入参类型的泛型
     * @return 远程返回的参数
     */
    <T> RemoteResponse<Boolean> callMethodToApps(NoticeEventEnum noticeEventEnum, String orgCode, T param, int timeout, String orderNo) throws ExecutionException, InterruptedException;
}
