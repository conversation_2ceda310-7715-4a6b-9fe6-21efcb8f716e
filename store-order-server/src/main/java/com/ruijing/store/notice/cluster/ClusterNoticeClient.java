package com.ruijing.store.notice.cluster;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.remoting.invoker.client.ClusterSyncClient;
import com.ruijing.fundamental.remoting.msharp.api.ClusterSyncListener;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.notice.dingtalk.DingTalkAlarmService;
import com.ruijing.store.order.other.service.ReimbursementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-10-31 18:27
 * @description:
 **/
@Component
public class ClusterNoticeClient implements ClusterSyncListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public static String VEE_CARD_CACHE_RELOAD = "VEE_CARD_CACHE_RELOAD";

    @Resource
    private ReimbursementService reimbursementService;

    @Resource
    private DingTalkAlarmService dingTalkAlarmService;

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void noticeDataChange(String key, Object value){
        ClusterSyncClient.async(new ClusterNoticeClient.NoticeKeyValue(key, value), 5000, false)
                .addFailureCallback(callback->{
                    logger.error("通知集群其他节点失败！", callback);
                    dingTalkAlarmService.sendDingMessage(DingTalkAlarmService.ORDER_GROUP_WEB_HOOK, New.list(DingTalkAlarmService.DEFAULT_ALARM_PHONE), "通知" + Environment.getAppKey() + "集群其他节点失败", false);
                });
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean dataChanged(Object message) {
        try{
            if(message instanceof NoticeKeyValue && VEE_CARD_CACHE_RELOAD.equals(((NoticeKeyValue) message).getKey())){
                reimbursementService.reloadAllConfig();
                logger.info("cluster node data reload success");
            }
        } catch (Exception e){
            logger.error("处理重载数据失败！", e);
            dingTalkAlarmService.sendDingMessage(DingTalkAlarmService.ORDER_GROUP_WEB_HOOK, New.list(DingTalkAlarmService.DEFAULT_ALARM_PHONE), "通知" + Environment.getAppKey() + "集群其他节点失败", false);
            throw e;
        }

        return true;
    }

    public static class NoticeKeyValue implements Serializable {
        private static final long serialVersionUID = 8673973059865820192L;

        private String key;

        private Object value;

        public NoticeKeyValue(String key, Object value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return new StringJoiner(", ", NoticeKeyValue.class.getSimpleName() + "[", "]")
                    .add("key='" + key + "'")
                    .add("value=" + value)
                    .toString();
        }
    }
}
