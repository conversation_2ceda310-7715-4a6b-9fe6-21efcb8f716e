package com.ruijing.store.config.filter;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.filter.FilterChain;
import com.ruijing.fundamental.remoting.msharp.provider.context.FilterContext;
import com.ruijing.fundamental.remoting.msharp.provider.filter.GateWayFilter;
import com.ruijing.store.exception.CodeException;
import org.springframework.core.annotation.Order;

/**
 * @author: liwenyu
 * @createTime: 2023-05-25 15:23
 * @description:
 **/
@Order(15)
public class GateWayFilterConfig implements GateWayFilter {

    @Override
    public void doFilter(FilterContext context, FilterChain<FilterContext, Throwable, Throwable> chain) throws Throwable {
        try{
            chain.doFilter(context);
        }catch (CodeException e){
            RemoteResponse<Object> remoteResponse = RemoteResponse.custom().setFailure(e.getMessage()).setData(e.getErrorData()).setCode(e.getErrorCode()).build();
            context.setRetValue(remoteResponse);
        }
    }
}
