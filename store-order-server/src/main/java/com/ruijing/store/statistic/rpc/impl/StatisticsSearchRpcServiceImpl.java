package com.ruijing.store.statistic.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.search.service.OrderAggRelatedService;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggDTO;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggRequest;
import com.ruijing.store.statistic.api.search.dto.ValidPurchaseCountDTO;
import com.ruijing.store.statistic.api.search.enums.AdvertisementOrderAggTermTypeEnum;
import com.ruijing.store.statistic.api.search.service.StatisticsSearchRpcService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/4/28 18:03
 */
@MSharpService
public class StatisticsSearchRpcServiceImpl implements StatisticsSearchRpcService {

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderAggRelatedService orderAggRelatedService;

    private static final Integer orderMasterTable = 1;

    private static final Integer orderDetailTable = 2;

    /**
     * 需要排序和聚合，按照单位、供应商、品牌、时间、订单状态（调用时需要保证这些聚合维度都是互斥的，筛选条件可以混合）
     * @param paramDTO
     * @return
     */
    @Override
    @ServiceLog
    public RemoteResponse<List<OrderAggregationResultDTO>> aggAmountAndQuantityByEntities(StatisticsManagerParamDTO paramDTO) {
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        List<OrderAggregationResultDTO> aggResultList = New.list();
        if (orderMasterTable.equals(aggField.getTable())) {
            aggResultList = orderSearchBoostService.aggOrderAmountAndCount(paramDTO);
        } else if (orderDetailTable.equals(aggField.getTable())) {
            aggResultList = orderSearchBoostService.aggProductAmountAndCount(paramDTO);
        }
        return RemoteResponse.<List<OrderAggregationResultDTO>>custom().setData(aggResultList).setSuccess();
    }

    /**
     * 趋势分析，按照年、月、趋势类型（还有单位、线上下单的筛选条件）， 返回对应年份月份数据(都返回两年数据)
     * @param paramDTO
     * @return
     */
    @Override
    @ServiceLog
    public RemoteResponse<Map<String, List<OrderDateHistogramResultDTO>>> aggAmountAndQuantityByTrendType(StatisticsManagerParamDTO paramDTO) {
        Preconditions.notNull(paramDTO, "查询按订单金额数量趋势信息入参不可为空");
        String startTime = paramDTO.getStartTime();
        String endTime = paramDTO.getEndTime();
        Preconditions.isTrue(startTime != null && endTime != null, "查询按订单金额数量趋势信息时间入参不可为空");
        if (paramDTO.getIntervalDate() == null) {
            paramDTO.setIntervalDate(new IntervalDTO());
        }
        Map<String, List<OrderDateHistogramResultDTO>> dateOrderInfoListMap = new HashMap<>();

        // 当年
        List<OrderDateHistogramResultDTO> curOrderInfoList = orderSearchBoostService.aggOrderAmountDateHistogram(paramDTO);
        String curYear = startTime.substring(0,4);
        dateOrderInfoListMap.put(curYear, curOrderInfoList);

        // 前一年
        Date startDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, startTime);
        Date endDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, endTime);

        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(startDate);
        calendar.add(Calendar.YEAR, -1);
        Date startDateOneYearBefore = calendar.getTime();

        calendar.setTime(endDate);
        calendar.add(Calendar.YEAR, -1);
        Date endDateOneYearBefore = calendar.getTime();

        String startTimeOneYearBefore = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDateOneYearBefore);
        String endTimeOneYearBefore = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDateOneYearBefore);

        paramDTO.setStartTime(startTimeOneYearBefore);
        paramDTO.setEndTime(endTimeOneYearBefore);
        List<OrderDateHistogramResultDTO> beforeOrderInfoList = orderSearchBoostService.aggOrderAmountDateHistogram(paramDTO);
        String beforeYear = startTimeOneYearBefore.substring(0,4);
        dateOrderInfoListMap.put(beforeYear, beforeOrderInfoList);

        return RemoteResponse.<Map<String, List<OrderDateHistogramResultDTO>>>custom().setData(dateOrderInfoListMap).setSuccess();
    }

    /**
     * 根据条件返回 成功退货金额总和 的聚合结果
     * @param paramDTO
     * @return
     */
    @Override
    @ServiceLog
    public RemoteResponse<List<GoodsReturnAggResDTO>> aggReturnAmountByEntities(StatisticsManagerParamDTO paramDTO) {
        Preconditions.isTrue(paramDTO.getOrgId() != null || CollectionUtils.isNotEmpty(paramDTO.getOrgIdList()), "单位id传入不可为空");
        Preconditions.isTrue(CollectionUtils.isEmpty(paramDTO.getSuppIdList()) || paramDTO.getSuppIdList().size() <= 100, "供应商id长度不可超过100");
        BusinessErrUtil.isTrue(paramDTO.getStartTime() != null && paramDTO.getEndTime() != null, ExecptionMessageEnum.PLEASE_PROVIDE_START_AND_END_TIMES);
        List<GoodsReturnAggResDTO> aggResList = orderSearchBoostService.aggReturnAmountByEntities(paramDTO);
        return RemoteResponse.<List<GoodsReturnAggResDTO>>custom().setData(aggResList).setSuccess();
    }
    
    /**
     * 按搜索条件，返回购买的商品数量信息
     * @param paramDTO
     * @return
     */
    @ServiceLog
    public RemoteResponse<List<ValidPurchaseCountDTO>> aggPurchaseQuantity(StatisticsManagerParamDTO paramDTO) {
        List<ValidPurchaseCountDTO> validPurchaseCountList = orderSearchBoostService.aggPurchaseQuantity(paramDTO);
        return RemoteResponse.<List<ValidPurchaseCountDTO>>custom().setData(validPurchaseCountList).setSuccess();
    }

    /**
     * 广告投放订单聚合，按照条件聚合下单买家数，下单数，下单金额，下单转化率，按广告id分组
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<List<AdvertisementOrderAggDTO>> aggAdvertisementOrder(AdvertisementOrderAggRequest request) {
        if(AdvertisementOrderAggTermTypeEnum.ADVERT_ID.equals(request.getTermType())){
            return RemoteResponse.success(orderSearchBoostService.aggAdvertisementOrderGroupByNestedField(request));
        } else if(AdvertisementOrderAggTermTypeEnum.NONE.equals(request.getTermType())){
            return RemoteResponse.success(orderSearchBoostService.aggAdvertisementOrder(request));
        } else {
            return RemoteResponse.success(orderSearchBoostService.aggAdvertisementOrderGroupByDocField(request));
        }
    }

    @Override
    public RemoteResponse<List<OrderDateHistogramResultDTO>> aggAmountAndQuantityByEntitiesDateHistogram(StatisticsManagerParamDTO paramDTO) {
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        if (orderDetailTable.equals(aggField.getTable())) {
            return RemoteResponse.<List<OrderDateHistogramResultDTO>>custom().setFailure("暂不支持订单子表纬度的聚合");
        }
        return RemoteResponse.success(orderAggRelatedService.aggAmountAndQuantityByEntitiesDateHistogram(paramDTO));
    }
}
