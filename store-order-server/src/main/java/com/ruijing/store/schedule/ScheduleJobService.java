package com.ruijing.store.schedule;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.job.core.annotation.JobExecutor;
import com.ruijing.job.core.annotation.JobHandler;
import com.ruijing.job.core.annotation.JobRegister;
import com.ruijing.store.order.api.scheduled.service.OrderScheduledService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.order.business.handler.OrderMessageHandler;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/22 17:22
 * @description
 */
@JobExecutor
public class ScheduleJobService {

    @Resource
    private OrderScheduledService orderScheduledService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "${order.timeout.scheduler.cron:0 0 0 * * ?}")
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> orderTimeOutStatistics() {
        orderScheduledService.orderTimeOutStatistics();
        return RemoteResponse.success();
    }

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "${order.receipt.scheduler.cron:0 4 0 * * ?}")
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> autoAcceptOrder(){
        orderScheduledService.autoReceiptOrder();
        return RemoteResponse.success();
    }

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "${cancelorder.scheduler.cron:0 0 22 1 * ?}")
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> autoCancelOrder(){
        orderScheduledService.autoCancelOrder();
        return RemoteResponse.success();
    }

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "${return.auto.receive.scheduler.cron:0 3 0 * * ?}")
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> scheduleForReturnAutoReceive() {
        orderScheduledService.returnAutoReceive();
        return RemoteResponse.success();
    }

    @JobHandler
    @JobRegister(phone = "***********", scheduleCron = "0 0 9 * * ?")
    public RemoteResponse<Boolean> scheduleSendMsgForChongQingYiKe(){
        orderMessageHandler.sendNoticeClaimScheduleForChongQingYiKe();
        return RemoteResponse.success();
    }
}
