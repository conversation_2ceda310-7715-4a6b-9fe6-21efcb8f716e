package com.ruijing.store.usertag.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.user.tag.api.dto.AddUserTagRequest;
import com.ruijing.user.tag.api.dto.ImportTaskRequest;
import com.ruijing.user.tag.api.service.UserTagRpcService;

import java.util.List;

/**
 * @author: zhukai
 * @date : 2023/10/25 14:39
 * @description:
 */
@ServiceClient
public class UserTagRpcServiceClient {

    @MSharpReference(remoteAppkey = "store-user-tag-service")
    private UserTagRpcService userTagRpcService;

    @ServiceLog(operationType = OperationType.WRITE,description = "批量添加用户标签",serviceType = ServiceType.RPC_CLIENT)
    public void addUserTag(List<AddUserTagRequest> addUserTagRequests){
        RemoteResponse response = userTagRpcService.addUserTag(addUserTagRequests);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE,description = "批量导入完成",serviceType = ServiceType.RPC_CLIENT)
    public void endImport(ImportTaskRequest importTaskRequest){
        RemoteResponse response = userTagRpcService.endImport(importTaskRequest);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
    }
}
