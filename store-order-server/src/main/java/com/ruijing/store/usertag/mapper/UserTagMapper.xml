<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.usertag.mapper.UserTagMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.usertag.model.UserTagDO">
    <!--@mbg.generated-->
    <result column="userId" jdbcType="INTEGER" property="userId" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="countItem" jdbcType="INTEGER" property="countItem" />

  </resultMap>
  <select id="getUserCategoryTag" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
      select b.fbuyerid userId,a.fclassification tag,count(1) countItem
      from torder_detail a,
      torder_master b
      where a.fmasterid = b.id
      and a.second_category_id != a.CategoryID
      and b.forderdate > '2022-07-01'
      and b.fbuyerid in
      <foreach item="userId" collection="userIds" open="(" close=")" separator=",">
          #{userId,jdbcType=INTEGER}
      </foreach>
      group by b.fbuyerid ,a.fclassification
      having countItem>2


  </select>
</mapper>