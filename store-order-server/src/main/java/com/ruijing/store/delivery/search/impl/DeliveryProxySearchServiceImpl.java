package com.ruijing.store.delivery.search.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.search.client.aggregation.CountItem;
import com.ruijing.search.client.aggregation.DateHistogramItem;
import com.ruijing.search.client.aggregation.SumItem;
import com.ruijing.search.client.aggregation.TermsItem;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.NestedFilter;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.query.RangeQuery;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.AggsResultItem;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.store.delivery.search.DeliveryProxySearchService;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryAggResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryDateHistogramResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryStatisticsRequestDTO;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.IntervalDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerResultDTO;
import com.ruijing.store.order.api.search.enums.DateUnitEnum;
import com.ruijing.store.order.api.search.enums.OrderAggregationSortFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @author: liwenyu
 * @createTime: 2023-03-29 17:12
 * @description:
 **/
@Service
public class DeliveryProxySearchServiceImpl implements DeliveryProxySearchService {

    private static final String ORDER_SEARCH_INDEX = "order";

    private static final String NESTED_TABLE = "order_detail";

    private static final String AMOUNT_ITEM = "amountItem";

    private static final String QUANTITY_ITEM = "quantityIem";

    private static final String DATE_AGG_ITEM = "dateAggItem";

    private static final String BUCKETS = "buckets";

    private static final String KEY = "key";
    @Resource
    private OrderSearchRPCServiceClient orderSearchRpcServiceClient;

    /**
     * 聚合指定条件下的代配送订单
     * @param paramDTO 参数
     * @return 聚合结果
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public StatisticsManagerResultDTO aggTotal(DeliveryStatisticsRequestDTO paramDTO){
        final Request request = new Request();
        // 通用填充
        fillRequestStatistics(paramDTO, request);
        // 增加聚合项
        request.addAggsItemList(New.list(new SumItem("forderamounttotal", "totalAmount")));
        Response response = orderSearchRpcServiceClient.search(request);
        List<AggsResultItem> aggResultItems = response.getAggsResult().getAggsResultItems();
        // 拼接到结果集中
        String totalAmount = "totalAmount";
        StatisticsManagerResultDTO result  = new StatisticsManagerResultDTO();
        for (AggsResultItem aggResultItem : aggResultItems) {
            String json = aggResultItem.getJson();
            if(json.contains(totalAmount)){
                result.setOriginalAmount(aggResultItem.getSum());
            }
        }
        result.setOrderQuantity(response.getTotalHits());
        return result;
    }

    /**
     * 根据聚合字段，聚合订单数量和总金额
     * @param paramDTO 聚合参数
     * @return 聚合结果
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public PageableResponse<List<DeliveryAggResultDTO>> aggOrderAmountAndCountByAggField(DeliveryStatisticsRequestDTO paramDTO){
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        Preconditions.notNull(aggField,"aggField不能为空！");

        String aggItemName = "aggItem";
        Integer topSize = paramDTO.getTopSize();
        Request request = new Request();
        this.fillRequestStatistics(paramDTO,request);
        TermsItem itemAgg = new TermsItem(aggField.getField(), aggItemName);
        itemAgg.setTop(topSize);

        FieldSortItem fieldSortItem = new FieldSortItem(AMOUNT_ITEM, SortOrder.DESC);
        OrderAggregationSortFieldEnum sortItem = paramDTO.getSortItem();
        if (sortItem != null){
            fieldSortItem = new FieldSortItem(sortItem.getItemCode(),SortOrder.getSortOrder(sortItem.getSort()));
        }
        itemAgg.addSortItem(fieldSortItem);

        request.addAggsItem(itemAgg);
        itemAgg.addAggsItem(new SumItem("forderamounttotal", AMOUNT_ITEM));
        CountItem countItem = new CountItem("id", QUANTITY_ITEM);
        itemAgg.addAggsItem(countItem);
        Response response = orderSearchRpcServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        JSONObject termsJson = JSON.parseObject(aggsResultItem.getJson());
        JSONObject aggItemJson = termsJson.getJSONObject(aggItemName);
        JSONArray bucketsArray = aggItemJson.getJSONArray("buckets");

        //封装返回对象
        List<DeliveryAggResultDTO> resultList = New.listWithCapacity(bucketsArray.size());
        for (int i = 0; i < bucketsArray.size(); i++) {
            JSONObject jsonObject = bucketsArray.getJSONObject(i);
            DeliveryAggResultDTO deliveryAggResultDTO = new DeliveryAggResultDTO();

            String aggResultKey =  jsonObject.getString("key");
            deliveryAggResultDTO.setAggResultKey(aggResultKey);

            JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
            Double orderAmount = sumJson.getDouble("value");
            deliveryAggResultDTO.setAmount(orderAmount);

            JSONObject orderQuantityJson = jsonObject.getJSONObject(QUANTITY_ITEM);
            Double orderQuantity = orderQuantityJson.getDouble("value");
            deliveryAggResultDTO.setQuantity(orderQuantity);
            resultList.add(deliveryAggResultDTO);
        }
        // 目前无法聚合后分页，先预留
        return PageableResponse.<List<DeliveryAggResultDTO>>custom().setData(resultList).setTotal(resultList.size()).setPageSize(resultList.size()).setPageNo(1).setSuccess();
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public PageableResponse<List<DeliveryDateHistogramResultDTO>> aggOrderAmountAndCountDateHistogram(DeliveryStatisticsRequestDTO paramDTO){
        Request request = new Request();
        this.fillRequestStatistics(paramDTO,request);

        List<DeliveryDateHistogramResultDTO> allResultDTOList = New.list();
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        if(aggField != null){
            // 有传聚合字段，则根据字段先聚合再根据字段聚合
            String aggItem = "aggItem";
            TermsItem itemAgg = new TermsItem(aggField.getField(), aggItem);
            itemAgg.setTop(paramDTO.getTopSize());
            request.addAggsItem(itemAgg);
            // 日期直方图聚合作为其子聚合
            itemAgg.addAggsItem(this.getDateHistogramItem(paramDTO));
            Response response = orderSearchRpcServiceClient.search(request);
            // 封装返回对象
            List<AggsResultItem> aggResultItems = response.getAggsResult().getAggsResultItems();
            if (CollectionUtils.isEmpty(aggResultItems)) {
                return PageableResponse.<List<DeliveryDateHistogramResultDTO>>custom().setData(New.emptyList()).setTotal(0).setPageSize(paramDTO.getPageSize()).setPageNo(1);
            }
            JSONObject termsJson = JSON.parseObject(aggResultItems.get(0).getJson());
            JSONObject aggFieldJson = termsJson.getJSONObject(aggItem);
            JSONArray aggFieldBucketArray = aggFieldJson.getJSONArray(BUCKETS);
            for(int i = 0; i < aggFieldBucketArray.size(); i++){
                JSONObject aggFieldBucketItem = aggFieldBucketArray.getJSONObject(i);
                String aggFieldKey = aggFieldBucketItem.getString(KEY);
                JSONObject dateAggItem = aggFieldBucketItem.getJSONObject(DATE_AGG_ITEM);
                // 获取日期直方图数据
                List<DeliveryDateHistogramResultDTO> dateHistogramResultDTOList = this.getHistogramResultList(dateAggItem);
                dateHistogramResultDTOList.forEach(item-> item.setAggFieldName(aggFieldKey));
                allResultDTOList.addAll(dateHistogramResultDTOList);
            }
        }else {
            // 无其他聚合字段，则直接按之契聚合
            request.addAggsItem(this.getDateHistogramItem(paramDTO));
            Response response = orderSearchRpcServiceClient.search(request);
            List<AggsResultItem> aggResultItems = response.getAggsResult().getAggsResultItems();
            if (CollectionUtils.isEmpty(aggResultItems)) {
                return PageableResponse.<List<DeliveryDateHistogramResultDTO>>custom().setData(New.emptyList()).setTotal(0).setPageSize(paramDTO.getPageSize()).setPageNo(1);
            }
            JSONObject termsJson = JSON.parseObject(aggResultItems.get(0).getJson());
            JSONObject dateAggItem = termsJson.getJSONObject(DATE_AGG_ITEM);
            allResultDTOList = this.getHistogramResultList(dateAggItem);
        }

        // 目前无法聚合后分页，先预留
        return PageableResponse.<List<DeliveryDateHistogramResultDTO>>custom().setData(allResultDTOList).setTotal(allResultDTOList.size()).setPageSize(allResultDTOList.size()).setPageNo(1).setSuccess();
    }

    /**
     * 从聚合结果对象获取直方图结果
     * @param dateAggItem 聚合结果对象
     * @return 直方图结果
     */
    private List<DeliveryDateHistogramResultDTO> getHistogramResultList(JSONObject dateAggItem){
        JSONArray dateAggBucket = dateAggItem.getJSONArray(BUCKETS);
        List<DeliveryDateHistogramResultDTO> dateHistogramResultDTOList = New.listWithCapacity(dateAggBucket.size());
        for(int j = 0; j < dateAggBucket.size(); j++){
            JSONObject dateObj = dateAggBucket.getJSONObject(j);
            dateHistogramResultDTOList.add(this.translateJsonToHistogramDto(dateObj));
        }
        return dateHistogramResultDTOList;
    }

    private DeliveryDateHistogramResultDTO translateJsonToHistogramDto(JSONObject jsonObject){
        Long timestamp =  jsonObject.getLong("key");
        String dateString = jsonObject.getString("key_as_string");
        JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
        Double orderAmount = sumJson.getDouble("value");
        JSONObject countJson = jsonObject.getJSONObject(QUANTITY_ITEM);
        Double orderCount = countJson.getDouble("value");
        DeliveryDateHistogramResultDTO aggregationResultDTO = new DeliveryDateHistogramResultDTO();
        aggregationResultDTO.setDateString(dateString);
        aggregationResultDTO.setTimestamp(timestamp);
        aggregationResultDTO.setAmount(orderAmount);
        aggregationResultDTO.setCount(orderCount);
        return aggregationResultDTO;
    }

    /**
     * 获取直方图聚合参数
     * @param paramDTO 请求参数
     * @return 直方图聚合参数
     */
    private DateHistogramItem getDateHistogramItem(DeliveryStatisticsRequestDTO paramDTO){
        Optional<FieldRangeDTO> deliveryTimeRange = paramDTO.getFieldRangeList().stream().filter(rangeItem ->
                OrderSearchFieldEnum.DELIVERY_DELIVERED_TIME.getField().equals(rangeItem.getField())
                        || OrderSearchFieldEnum.DELIVERY_SORTED_TIME.getField().equals(rangeItem.getField())).findFirst();
        Preconditions.isTrue(deliveryTimeRange.isPresent(), "需要传入代配送操作时间范围");
        FieldSortItem fieldSortItem = new FieldSortItem("_key",SortOrder.DESC);
        SortOrderEnum sortOrderEnum = paramDTO.getSortOrderEnum();
        if (SortOrderEnum.ASC == sortOrderEnum) {
            fieldSortItem = new FieldSortItem("_key",SortOrder.ASC);
        }
        IntervalDTO intervalDate = paramDTO.getIntervalDate();
        Assert.isTrue(intervalDate != null ,"intervalDate不能为空！");
        String dateFormat = StringUtils.isNotBlank(intervalDate.getDateFormat()) ? intervalDate.getDateFormat() : "yyyy-MM-dd";
        int value = intervalDate.getValue() != null ? intervalDate.getValue() : 1;
        DateUnitEnum dateUnitEnum = intervalDate.getDateUnit() != null ? intervalDate.getDateUnit() : DateUnitEnum.MONTH;
        // 根据过滤条件的时间进行统计
        DateHistogramItem dateHistogramItem = new DateHistogramItem(deliveryTimeRange.get().getField() ,DATE_AGG_ITEM, dateFormat, new DateHistogramItem.Interval(value,dateUnitEnum.getSymbol()));

        // 判断是否需要让桶数据完整返回所有时间分段信息，即便没有对应的单据数据。传true则为是
        if(Boolean.TRUE.equals(paramDTO.getSkipEmptyValueInterval())){
            dateHistogramItem.setMinDocCount(1);
        }else {
            dateHistogramItem.setMinDocCount(0);
        }
        DateHistogramItem.ExtendedBounds extendedBounds = new DateHistogramItem.ExtendedBounds(deliveryTimeRange.get().getLower(), deliveryTimeRange.get().getUpper());
        dateHistogramItem.setExtendedBounds(extendedBounds);

        dateHistogramItem.addAggsItem(new SumItem("forderamounttotal", AMOUNT_ITEM));
        CountItem countItem = new CountItem("id", QUANTITY_ITEM);
        dateHistogramItem.addAggsItem(countItem);
        dateHistogramItem.addSortItem(fieldSortItem);
        return dateHistogramItem;
    }

    /**
     * 拼接代配送统计通用请求参数到request中
     * @param paramDTO 代配送统计参数
     * @param request 查询请求参数
     */
    private void fillRequestStatistics(DeliveryStatisticsRequestDTO paramDTO, Request request){
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        // 只查开启了的代配送单
        request.addFilter(new TermFilter("deliveryType", DeliveryTypeEnum.PROXY.getCode()));

        //流程种类
        Integer species = paramDTO.getSpecies();
        if (species != null) {
            request.addFilter(new TermFilter("species", species));
        }

        // 订单来源筛选
        List<Integer> orderTypeList = paramDTO.getOrderTypeList();
        if (CollectionUtils.isNotEmpty(orderTypeList)) {
            request.addFilter(new TermFilter("order_type", orderTypeList.toArray()));
        }

        List<Integer> notStatusList = paramDTO.getNotStatusList();
        //去除的订单状态过滤
        if (CollectionUtils.isNotEmpty(notStatusList)) {
            request.addNotFilter(new TermFilter("status", notStatusList));
        }

        //范围查询
        List<FieldRangeDTO> fieldRangeList = paramDTO.getFieldRangeList();
        if (CollectionUtils.isNotEmpty(fieldRangeList)) {
            fieldRangeList.forEach(range ->
                    request.addQuery(new RangeQuery(range.getField(), range.getLower(), range.getUpper()
                            ,range.getIncludeLower(),range.getIncludeUpper())));
        }

        // 去除的单位id列表
        List<Integer> excludeOrgIdList = paramDTO.getExcludeOrgIdList();
        if (CollectionUtils.isNotEmpty(excludeOrgIdList)) {
            request.addNotFilter(new TermFilter("fuserid", excludeOrgIdList));
        }

        // 去除的供应商id列表，明细和主表都要控制
        List<Integer> excludeSuppIdList = paramDTO.getExcludeSuppIdList();
        if (CollectionUtils.isNotEmpty(excludeSuppIdList)) {
            request.addNotFilter(new TermFilter("fsuppid", excludeSuppIdList));
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "supp_id", excludeSuppIdList);
            request.addNotFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }
    }
}
