package com.ruijing.store.delivery.search.impl;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.delivery.search.DeliveryProxySearchService;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryAggResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryCountResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryDateHistogramResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryStatisticsRequestDTO;
import com.ruijing.store.order.api.delivery.search.service.DeliveryProxySearchRpcService;
import com.ruijing.store.order.api.search.dto.OrderStatisticsParamDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerResultDTO;
import com.ruijing.store.order.search.service.OrderSearchBoostService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: liwenyu
 * @createTime: 2023-03-28 17:20
 * @description:
 **/
@MSharpService
public class DeliveryProxySearchRpcServiceImpl implements DeliveryProxySearchRpcService {

    @Resource
    private DeliveryProxySearchService deliveryProxySearchService;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Override
    public RemoteResponse<DeliveryCountResultDTO> countOrder(OrderStatisticsParamDTO orderStatisticsParamDTO) {
        Map<Integer, Integer> deliveryCountMap = orderSearchBoostService.countOrderByDeliveryStatus(orderStatisticsParamDTO);
        // 代配送订单相关
        int deliveryProxyTotal = deliveryCountMap.values().stream().reduce(0, Integer::sum);
        return RemoteResponse.success(new DeliveryCountResultDTO().setTotalCount(deliveryProxyTotal));
    }

    @Override
    @ServiceLog(description = "统计代配送订单金额")
    public RemoteResponse<StatisticsManagerResultDTO> aggForTotal(DeliveryStatisticsRequestDTO paramDTO) {
        StatisticsManagerResultDTO resultDTO = deliveryProxySearchService.aggTotal(paramDTO);
        return RemoteResponse.success(resultDTO);
    }

    @Override
    @ServiceLog(description = "根据聚合字段，聚合订单数量和总金额")
    public PageableResponse<List<DeliveryAggResultDTO>> aggOrderAmountAndCountByAggField(DeliveryStatisticsRequestDTO paramDTO) {
        return deliveryProxySearchService.aggOrderAmountAndCountByAggField(paramDTO);
    }

    @Override
    @ServiceLog(description = "按照时间段聚合订单数量和金额（直方图）。如果传入aggField，会先按aggField聚合，再根据时间聚合（用于导出excel）")
    public PageableResponse<List<DeliveryDateHistogramResultDTO>> aggOrderAmountAndCountDateHistogram(DeliveryStatisticsRequestDTO paramDTO){
        return deliveryProxySearchService.aggOrderAmountAndCountDateHistogram(paramDTO);
    }
}
