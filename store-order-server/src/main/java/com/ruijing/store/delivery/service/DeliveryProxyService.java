package com.ruijing.store.delivery.service;

import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:19
 * @description 代配送服务
 */
public interface DeliveryProxyService {

    /**
     * 修改代配送来源类型
     * @param orderIdList 订单id数据
     * @param proxySourceType 来源类型
     */
    void changeProxySourceType(List<Integer> orderIdList, DeliveryProxySourceTypeEnum proxySourceType);

    /**
     * 买家申请取消代配送
     * @param orderId 订单id
     * @param reason 原因
     */
    void suppApplyCancel(Integer orderId, String reason);

    /**
     * 买家同意取消代配送
     * @param orderId 订单id
     * @param reason 原因
     * @param operatorId 操作人
     */
    void buyerAgreeCancel(Integer orderId, String reason, Integer operatorId);

    /**
     * 买家拒绝取消代配送
     * @param orderId 订单id
     * @param reason 原因
     * @param operatorId 操作人
     */
    void buyerRejectCancel(Integer orderId, String reason, Integer operatorId);
}
