package com.ruijing.store.delivery.service.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.delivery.service.DeliveryProxyService;
import com.ruijing.store.order.api.base.delivery.dto.DeliveryProxyChangeDTO;
import com.ruijing.store.order.api.base.delivery.dto.DeliveryProxyCommonRequestDTO;
import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;
import com.ruijing.store.order.api.base.delivery.service.DeliveryProxyRpcService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:17
 * @description 代配送RPC服务
 */
@MSharpService
public class DeliveryProxyRpcServiceImpl implements DeliveryProxyRpcService {
    
    @Resource
    private DeliveryProxyService deliveryProxyService;
    
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> changeProxySourceType(DeliveryProxyChangeDTO deliveryProxyChangeDTO) {
        DeliveryProxySourceTypeEnum proxySourceType = deliveryProxyChangeDTO.getDeliveryProxySourceTypeEnum();
        Preconditions.notNull(proxySourceType, "代配送来源类型不可空");
        Preconditions.notEmpty(deliveryProxyChangeDTO.getOrderIdList(), "订单ID不可空");
        
        deliveryProxyService.changeProxySourceType(deliveryProxyChangeDTO.getOrderIdList(), proxySourceType);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> suppApplyCancelProxy(DeliveryProxyCommonRequestDTO requestDTO) {
        Preconditions.notNull(requestDTO.getOrderId(), "需取消的订单id不可空");
        Preconditions.notNull(requestDTO.getReason(), "取消原因不可空");
        deliveryProxyService.suppApplyCancel(requestDTO.getOrderId(), requestDTO.getReason());
        return RemoteResponse.success();
    }
}
