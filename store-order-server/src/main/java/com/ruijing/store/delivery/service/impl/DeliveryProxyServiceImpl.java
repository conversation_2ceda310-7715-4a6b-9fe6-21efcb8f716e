package com.ruijing.store.delivery.service.impl;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.delivery.service.DeliveryProxyService;
import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;
import com.ruijing.store.order.api.base.enums.DeliveryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.SysLetterHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.constant.OrderCommonConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.rpc.client.OrderAddressRPCClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/29 11:41
 * @description
 */
@Service
public class DeliveryProxyServiceImpl implements DeliveryProxyService {
    
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    
    private final String CAT_TYPE = this.getClass().getSimpleName();

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderAddressRPCClient orderAddressRpcClient;
    
    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;
    
    @Resource
    private OrderEmailHandler orderEmailHandler;
    
    @Resource
    private WeChatMessageHandler weChatMessageHandler;
    
    @Resource
    private SysLetterHandler sysLetterHandler;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void changeProxySourceType(List<Integer> orderIdList, DeliveryProxySourceTypeEnum proxySourceType) {
        Preconditions.notNull(proxySourceType);
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        if(CollectionUtils.isEmpty(orderMasterDOList)){
            return;
        }
        DeliveryTypeEnum isProxyOn = proxySourceType != DeliveryProxySourceTypeEnum.NONE ? DeliveryTypeEnum.PROXY : DeliveryTypeEnum.NORMAL;
        // 更新地址表
        List<OrderAddressDTO> addressUpdateList = orderMasterDOList.stream().map(orderMasterDO -> {
            OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
            orderAddressDTO.setProxySourceType(proxySourceType.getCode());
            orderAddressDTO.setDeliveryType(isProxyOn.getCode());
            orderAddressDTO.setOrderNo(orderMasterDO.getForderno());
            return orderAddressDTO;
        }).collect(Collectors.toList());
        orderAddressRpcClient.batchUpdateByOrderNo(addressUpdateList);
        // 更新订单主表地址数据
        List<OrderAddressDTO> orderAddressDTOList = orderAddressRpcClient.findByOrderId(orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList()));
        StringBuilder stringBuilder = new StringBuilder(400);
        List<UpdateOrderParamDTO> updateOrderParamDTOList = orderAddressDTOList.stream().map(orderAddressDTO -> {
            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
            updateOrderParamDTO.setOrderId(orderAddressDTO.getId());
            updateOrderParamDTO.setDeliveryPlace(this.getDeliveryAddress(orderAddressDTO, stringBuilder));
            return updateOrderParamDTO;
        }).collect(Collectors.toList());
        orderMasterMapper.updateOrderByIdList(updateOrderParamDTOList);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void suppApplyCancel(Integer orderId, String reason) {
        OrderAddressDTO orderAddressDTO = orderAddressRpcClient.findByOrderId(orderId);
        Preconditions.notNull(orderAddressDTO, "没有找到代配送记录！");
        Preconditions.isTrue(DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType()), "该订单非代配送状态！");
        this.writeOrderApprovalLog(orderId, OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY, reason, OrderCommonConstant.PROMISE_SUPPLIER_FLAG);
        if(DeliveryProxySourceTypeEnum.BUYER.getCode().equals(orderAddressDTO.getProxySourceType())){
            // 买家发起的代配送请求，需要经过买家同意
            OrderAddressDTO updateParam = new OrderAddressDTO();
            updateParam.setDeliveryStatus(DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue());
            updateParam.setOrderNo(orderAddressDTO.getOrderNo());
            orderAddressRpcClient.batchUpdateByOrderNo(New.list(updateParam));
            return;
        }
        // 否则代配送直接取消成功
        this.cancelProxySuccess(orderAddressDTO, orderId, reason);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void buyerAgreeCancel(Integer orderId, String reason, Integer operatorId) {
        OrderAddressDTO orderAddressDTO = orderAddressRpcClient.findByOrderId(orderId);
        Preconditions.notNull(orderAddressDTO, "没有找到代配送记录！");
        Preconditions.isTrue(DeliveryProxySourceTypeEnum.BUYER.getCode().equals(orderAddressDTO.getProxySourceType()), "非采购人发起的代配送！无法同意！");
        Preconditions.isTrue(DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue().equals(orderAddressDTO.getDeliveryStatus()), "当前代配送状态已非【供应商申请取消代配送】状态！");
        // 写日志、取消成功
        this.writeOrderApprovalLog(orderId, OrderApprovalEnum.BUYER_AGREE_CANCEL_DELIVERY_PROXY, reason, operatorId);
        
        this.cancelProxySuccess(orderAddressDTO, orderId, StringUtils.EMPTY);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void buyerRejectCancel(Integer orderId, String reason, Integer operatorId) {
        OrderAddressDTO orderAddressDTO = orderAddressRpcClient.findByOrderId(orderId);
        Preconditions.notNull(orderAddressDTO, "没有找到代配送记录！");
        Preconditions.isTrue(DeliveryProxySourceTypeEnum.BUYER.getCode().equals(orderAddressDTO.getProxySourceType()), "非采购人发起的代配送！无法同意！");
        Preconditions.isTrue(DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue().equals(orderAddressDTO.getDeliveryStatus()), "当前代配送状态已非【供应商申请取消代配送】状态！");
        // 写日志、更新状态
        this.writeOrderApprovalLog(orderId, OrderApprovalEnum.BUYER_REJECT_CANCEL_DELIVERY_PROXY, reason, operatorId);
        
        OrderAddressDTO updateParam = new OrderAddressDTO();
        updateParam.setDeliveryStatus(DeliveryStatusEnum.BUYER_REJECT_CANCEL.getValue());
        updateParam.setOrderNo(orderAddressDTO.getOrderNo());
        orderAddressRpcClient.batchUpdateByOrderNo(New.list(updateParam));
        
        // 发站内信
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        this.asyncSendRejectCancelMessage(orderMasterDO, reason);
    }

    /**
     * 异步发送拒绝取消代配送信息给供应商
     * @param orderMasterDO 订单数据
     * @param reason 原因
     */
    private void asyncSendRejectCancelMessage(OrderMasterDO orderMasterDO, String reason){
        AsyncExecutor.listenableRunAsync(() -> {
            //待供应商确认状态，发送生成订单邮件邮件给供应商
            orderEmailHandler.sendRejectCancelDeliveryProxy2Supp(orderMasterDO, reason);
            weChatMessageHandler.sendRejectCancelDeliveryProxy2Supp(orderMasterDO, reason);
            sysLetterHandler.sendRejectCancelDeliveryProxy2Supp(orderMasterDO, reason);
        }).addFailureCallback(throwable -> {
            LOGGER.error("发送拒绝取消代配送信息给供应商失败：" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "发送拒绝取消代配送信息给供应商失败：", throwable);
            throw new IllegalStateException(throwable);
        });
    }

    /**
     * 取消代配送成功
     * @param orderAddressDTO 代配送地址数据
     * @param orderId 订单id
     */
    private void cancelProxySuccess(OrderAddressDTO orderAddressDTO, Integer orderId, String reason){
        // 更新地址表
        OrderAddressDTO updateParam = new OrderAddressDTO();
        updateParam.setDeliveryType(DeliveryTypeEnum.NORMAL.getCode());
        updateParam.setDeliveryStatus(DeliveryStatusEnum.SHUT_DOWN.getValue());
        updateParam.setOrderNo(orderAddressDTO.getOrderNo());
        orderAddressRpcClient.batchUpdateByOrderNo(New.list(updateParam));
        // 更新订单地址表
        orderAddressDTO.setDeliveryType(DeliveryTypeEnum.NORMAL.getCode());
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderId);
        updateOrderParamDTO.setDeliveryPlace(this.getDeliveryAddress(orderAddressDTO));
        orderMasterMapper.updateOrderById(updateOrderParamDTO);
        // 写操作日志
        this.writeOrderApprovalLog(orderId, OrderApprovalEnum.DELIVERY_PROXY_TURN_OFF, reason, OrderCommonConstant.PROMISE_SUPPLIER_FLAG);
    }
    
    private void writeOrderApprovalLog(Integer orderId, OrderApprovalEnum orderApprovalEnum, String reason, Integer operatorId){
        OrderApprovalLog record = new OrderApprovalLog();
        record.setOrderId(orderId);
        record.setApproveStatus(orderApprovalEnum.getValue());
        record.setReason(reason);
        record.setOperatorId(operatorId);
        orderApprovalLogMapper.insertSelective(record);
    }
    
    private String getDeliveryAddress(OrderAddressDTO orderAddressDTO){
        return this.getDeliveryAddress(orderAddressDTO, new StringBuilder(400));
    }

    /**
     * 封装收货人入参信息
     * 原地址： 省+市+区+地址+“-”+标签
     * 代配送地址： 代配送省市区地址 + “转送” + 原地址 + “，” + 接收人 + “，” + 接收人电话  
     *
     * @param orderAddressDTO 地址入参
     * @return 更新入参
     */
    private String getDeliveryAddress(OrderAddressDTO orderAddressDTO, StringBuilder stringBuilder) {
        stringBuilder.setLength(0);
        // 代配送地址拼接处理
        if (DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType())) {
            stringBuilder.append(orderAddressDTO.getProvinceProxy())
                    .append(orderAddressDTO.getCityProxy())
                    .append(orderAddressDTO.getRegionProxy())
                    .append(orderAddressDTO.getAddressProxy())
                    .append(" 转送 ");
        }

        stringBuilder.append(StringUtils.defaultIfBlank(orderAddressDTO.getProvince(), StringUtils.EMPTY))
                .append(StringUtils.defaultIfBlank(orderAddressDTO.getCity(), StringUtils.EMPTY))
                .append(StringUtils.defaultIfBlank(orderAddressDTO.getRegion(), StringUtils.EMPTY))
                .append(StringUtils.defaultIfBlank(orderAddressDTO.getAddress(), StringUtils.EMPTY));
        if (StringUtils.isNotBlank(orderAddressDTO.getLabel())) {
            stringBuilder.append("-").append(orderAddressDTO.getLabel());
        }

        if (DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType())) {
            stringBuilder.append("，")
                    .append(orderAddressDTO.getReceiverName())
                    .append("，")
                    .append(orderAddressDTO.getReceiverPhone());
        }

        return stringBuilder.toString();
    }
}
