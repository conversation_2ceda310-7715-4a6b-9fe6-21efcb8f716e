package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/1/21 11:17
 **/
public class GoodsReturnDetailBaseRequestDTO implements Serializable {

    private static final long serialVersionUID = 6310143046230959585L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("退货单明细id")
    private Integer detailId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnDetailBaseRequestDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", detailId=").append(detailId);
        sb.append('}');
        return sb.toString();
    }
}
