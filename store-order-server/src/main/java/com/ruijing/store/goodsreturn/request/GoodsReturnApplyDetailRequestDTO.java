package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 退货申请详情入参DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 15:25
 **/
public class GoodsReturnApplyDetailRequestDTO implements Serializable {

    private static final long serialVersionUID = -5497811577925557429L;

    @RpcModelProperty("订单商品详情id")
    private Integer detailId;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("商品图片路径")
    private String goodsPicturePath;

    @RpcModelProperty("退货商品单价")
    private BigDecimal price;

    @RpcModelProperty("退货商品数量")
    private BigDecimal quantity;

    @RpcModelProperty("退货商品总价")
    private BigDecimal amount;

    @RpcModelProperty("退货原因")
    private String returnReason;

    @RpcModelProperty("退货说明")
    private String remark;

    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("商品单位")
    private String unit;

    @RpcModelProperty("一并退货的气瓶二维码，非一物一码模式才需要")
    private List<String> returnGasBottleBarcodes;

    /**
     * 一物一码退货数据，仅有一物一码退货时赋值
     */
    private List<GoodsReturnBarcodeDataDTO> goodsReturnBarcodeDataDTOList;

    @RpcModelProperty(value = "商品平台唯一编码",hidden = true)
    private String productCode;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public List<String> getReturnGasBottleBarcodes() {
        return returnGasBottleBarcodes;
    }

    public GoodsReturnApplyDetailRequestDTO setReturnGasBottleBarcodes(List<String> returnGasBottleBarcodes) {
        this.returnGasBottleBarcodes = returnGasBottleBarcodes;
        return this;
    }

    public List<GoodsReturnBarcodeDataDTO> getGoodsReturnBarcodeDataDTOList() {
        return goodsReturnBarcodeDataDTOList;
    }

    public GoodsReturnApplyDetailRequestDTO setGoodsReturnBarcodeDataDTOList(List<GoodsReturnBarcodeDataDTO> goodsReturnBarcodeDataDTOList) {
        this.goodsReturnBarcodeDataDTOList = goodsReturnBarcodeDataDTOList;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public GoodsReturnApplyDetailRequestDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    @Override
    public String toString() {
        return "GoodsReturnApplyDetailRequestDTO{" +
                "detailId=" + detailId +
                ", goodsName='" + goodsName + '\'' +
                ", goodsCode='" + goodsCode + '\'' +
                ", specification='" + specification + '\'' +
                ", brand='" + brand + '\'' +
                ", goodsPicturePath='" + goodsPicturePath + '\'' +
                ", price=" + price +
                ", quantity=" + quantity +
                ", amount=" + amount +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                ", dangerousTag='" + dangerousTag + '\'' +
                ", productId='" + productId + '\'' +
                ", unit='" + unit + '\'' +
                ", returnGasBottleBarcodes=" + returnGasBottleBarcodes +
                ", goodsReturnBarcodeDataDTOList=" + goodsReturnBarcodeDataDTOList +
                ", productCode='" + productCode + '\'' +
                '}';
    }
}
