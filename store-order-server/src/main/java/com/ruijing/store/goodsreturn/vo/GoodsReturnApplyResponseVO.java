package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 提交申请退货后的响应出参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/1/28 13:21
 **/
public class GoodsReturnApplyResponseVO implements Serializable {

    private static final long serialVersionUID = 4839075763118035242L;

    @RpcModelProperty("退货单id")
    private Integer returnId;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("申请人")
    private String applyName;

    @RpcModelProperty("申请时间")
    private Date applyDate;

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnApplyResponseVO{");
        sb.append("returnId='").append(returnId).append('\'');
        sb.append("returnNo='").append(returnNo).append('\'');
        sb.append(", applyName='").append(applyName).append('\'');
        sb.append(", applyDate=").append(applyDate);
        sb.append('}');
        return sb.toString();
    }
}
