package com.ruijing.store.goodsreturn.constant;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.MapBuilder;

import java.util.Map;

/**
 * @description: 退货配置硬编码
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/1 18:24
 **/
public class GoodsReturnConstant {

    /**
     * 整单退货的单位编码集合
     */
    public final static Map<Integer, String> ALL_RETURN_ORG = MapBuilder.<Integer, String>custom()
            .put(37, "JI_NAN_DA_XUE")
            .put(36, "SHANG_HAI_JIU_YUAN")
            .put(10,"ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN")
            .put(46, "ZHENG_DA_FU_YI")
            .put(70,"WEN_ZHOU_YI_KE_DA_XUE")
            .put(38, "GUI_ZHOU_YI_KE_DA_XUE")
            .put(3, "ZHONG_SHAN_DA_XUE")
            .put(92, "WEN_YI_DA_REN_JI_XUE_YUAN")
            .put(95, "ZHONG_KE_YUAN_ZHONG_JI_SUO")
            .put(62, "ZHONG_SHAN_DA_XUE_SHEN_ZHEN")
            .put(111, "GUANG_ZHOU_YI_KE_DA_XUE")
            .put(OrgEnum.CHANG_ZHOU_SHI_DI_YI_REN_MIN_YI_YUAN.getValue(), OrgEnum.CHANG_ZHOU_SHI_DI_YI_REN_MIN_YI_YUAN.getCode())
            .build();

    /**
     * 退货原因粒度 订单粒度
     */
    public final static Integer RETURN_REASON_ORDER_TYPE = 1;

    /**
     * 退货原因粒度 商品粒度
     */
    public final static Integer RETURN_REASON_PRODUCT_TYPE = 0;

}
