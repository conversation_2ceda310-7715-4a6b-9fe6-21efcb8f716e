package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 退货基础request对象
 * @author: zhong<PERSON>lei
 * @create: 2020/12/31 10:21
 **/
public class GoodsReturnBaseRequestDTO implements Serializable {

    private static final long serialVersionUID = -2933327254578963082L;

    @RpcModelProperty("退货id")
    private Integer returnId;

    /**
     * 退货单id数组，只用于后端处理，前端无需传参
     */
    private List<String> returnNoList;
    
    /**
     * 退货单id数组，只用于后端处理，前端无需传参
     */
    private List<Integer> returnIdList;

    @RpcModelProperty(value = "退货状态",enumClass = GoodsReturnStatusEnum.class)
    private Integer returnStatus;

    @RpcModelProperty("拒绝/同意退货原因")
    private String reason;

    @RpcModelProperty("拒绝/同意退货图片")
    private String pic;

    @RpcModelProperty("订单id，目前只支持整单退货的单位(如中大，暨大)根据订单id查询退货单信息")
    private Integer orderId;

    @RpcModelProperty("退货明细，只更新寄回地址")
    private List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public List<Integer> getReturnIdList() {
        return returnIdList;
    }

    public void setReturnIdList(List<Integer> returnIdList) {
        this.returnIdList = returnIdList;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<String> getReturnNoList() {
        return returnNoList;
    }

    public void setReturnNoList(List<String> returnNoList) {
        this.returnNoList = returnNoList;
    }

    public List<GoodsReturnInfoDetailVO> getGoodsReturnInfoDetailVOList() {
        return goodsReturnInfoDetailVOList;
    }

    public GoodsReturnBaseRequestDTO setGoodsReturnInfoDetailVOList(List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList) {
        this.goodsReturnInfoDetailVOList = goodsReturnInfoDetailVOList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnBaseRequestDTO.class.getSimpleName() + "[", "]")
                .add("returnId=" + returnId)
                .add("returnNoList=" + returnNoList)
                .add("returnIdList=" + returnIdList)
                .add("returnStatus=" + returnStatus)
                .add("reason='" + reason + "'")
                .add("pic='" + pic + "'")
                .add("orderId=" + orderId)
                .add("goodsReturnInfoDetailVOList=" + goodsReturnInfoDetailVOList)
                .toString();
    }
}
