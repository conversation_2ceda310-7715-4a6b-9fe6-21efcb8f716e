package com.ruijing.store.goodsreturn.callback.impl;

import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.callback.OrderGoodsReturnCallbackService;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.translator.GoodsReturnLogTranslator;
import com.ruijing.store.order.constant.DockingConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2024-05-16 19:24
 * @description:
 */
@Service
public class OrderGoodsReturnCallbackServiceImpl implements OrderGoodsReturnCallbackService {

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Override
    public void pushOrderToSuppCallBack(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        String returnNo = orderEventPushResultResponseDTO.getReturnNo();
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByReturnNoIn(New.list(returnNo));
        BusinessErrUtil.notEmpty(goodsReturns, ExecptionMessageEnum.INVALID_RETURN_ORDER_NUMBER);
        Integer returnId = goodsReturns.get(0).getId();
        // 记日志
        GoodsReturnOperationTypeEnum goodsReturnOperationTypeEnum = null;
        switch (orderEventPushResultResponseDTO.getOrderEventStatusEnum()) {
            case PUSHING:
                goodsReturnOperationTypeEnum = GoodsReturnOperationTypeEnum.PUSHING_RETURN_TO_SUPP;
                break;
            case COMPLETE:
                goodsReturnOperationTypeEnum = GoodsReturnOperationTypeEnum.PUSH_RETURN_TO_SUPP_SUCCESS;
                break;
            case FAILED:
                goodsReturnOperationTypeEnum = GoodsReturnOperationTypeEnum.PUSH_RETURN_TO_SUPP_FAILURE;
                break;
            default:
                break;
        }

        if (goodsReturnOperationTypeEnum != null) {
            // 插入操作日志
            goodsReturnLogDOMapper.insertSelective(
                    GoodsReturnLogTranslator.buildDO(DockingConstant.SYSTEM_OPERATOR_ID, DockingConstant.SYSTEM_OPERATOR_NAME, returnId, GoodsReturnOperatorTypeEnum.SYSTEM_USER, goodsReturnOperationTypeEnum, StringUtils.EMPTY, null)
            );
        }
    }
}
