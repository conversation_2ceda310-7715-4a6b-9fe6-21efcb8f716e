package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 退货单详情VO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 17:32
 **/
public class GoodsReturnInfoVO implements Serializable {

    private static final long serialVersionUID = 8246528195558249073L;

    @RpcModelProperty("退货单id")
    private Integer id;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("部门/课题组名称")
    private String departmentName;

    @RpcModelProperty("采购人")
    private String buyerName;

    @RpcModelProperty("供应商名称")
    private String supplierName;

    @RpcModelProperty("供应商名称")
    private Integer supplierId;

    @RpcModelProperty("收货地址")
    private String deliveryAddress;

    @RpcModelProperty("金额")
    private BigDecimal amount;

    @RpcModelProperty("退货商品信息")
    private List<GoodsReturnInfoDetailVO> returnInfoDetailList;

    @RpcModelProperty("拒绝退货原因")
    private String refuseReason;

    @RpcModelProperty("同意退货原因")
    private String agreeReason;

    @RpcModelProperty("采购单位")
    private String orgName;

    @RpcModelProperty("采购单位Id")
    private Integer orgId;

    @RpcModelProperty("机构编码")
    private String orgCode;

    @RpcModelProperty("联系电话")
    private String phone;

    @RpcModelProperty("退货状态")
    private Integer returnStatus;

    @RpcModelProperty("收货人电话")
    private String buyerTelephone;

    @RpcModelProperty("收货人")
    private String buyerContactMan;

    @RpcModelProperty("课题组负责人")
    private String departmentManageName;

    @RpcModelProperty("供应商code")
    private String suppCode;

    @RpcModelProperty("发起退货申请时间")
    private Date creationTime;

    @RpcModelProperty("供应商联系电话")
    private String supplierContactTelephone;

    @RpcModelProperty("延迟验收次数")
    private Integer delayAcceptCount;

    @RpcModelProperty("距离自动验收天数")
    private Integer autoAcceptDays;

    @RpcModelProperty("限制只能整单退货")
    private Boolean limitOnlyWholeReturn;

    @RpcModelProperty("是否一物一码")
    private Boolean eachProductEachCode;

    @RpcModelProperty("是否需要填写批次")
    private Boolean suppNeedFillBatchesData;

    @RpcModelProperty(value = "货仓标识",enumClass = StockTypeEnum.class)
    private Integer stockWarehouseType;

    @RpcModelProperty(value = "订单类型,0:采购的,1:竞价单")
    private Integer orderType;

    @RpcModelProperty(value = "退货原因", description = "退货单粒度")
    private String returnReason;

    @RpcModelProperty(value = "退货说明", description = "退货单粒度")
    private String remark;

    @RpcModelProperty(value = "退货原因粒度类型（0:商品粒度退货原因，1:订单粒度退货原因）", description = "用于前端控制展示的退货原因粒度")
    private Integer returnReasonType;

    public Integer getReturnReasonType() {
        return returnReasonType;
    }

    public GoodsReturnInfoVO setReturnReasonType(Integer returnReasonType) {
        this.returnReasonType = returnReasonType;
        return this;
    }

    public Integer getStockWarehouseType() {
        return stockWarehouseType;
    }

    public GoodsReturnInfoVO setStockWarehouseType(Integer stockWarehouseType) {
        this.stockWarehouseType = stockWarehouseType;
        return this;
    }

    public Integer getAutoAcceptDays() {
        return autoAcceptDays;
    }

    public void setAutoAcceptDays(Integer autoAcceptDays) {
        this.autoAcceptDays = autoAcceptDays;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getDepartmentManageName() {
        return departmentManageName;
    }

    public void setDepartmentManageName(String departmentManageName) {
        this.departmentManageName = departmentManageName;
    }

    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public void setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
    }

    public String getAgreeReason() {
        return agreeReason;
    }

    public void setAgreeReason(String agreeReason) {
        this.agreeReason = agreeReason;
    }


    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public List<GoodsReturnInfoDetailVO> getReturnInfoDetailList() {
        return returnInfoDetailList;
    }

    public void setReturnInfoDetailList(List<GoodsReturnInfoDetailVO> returnInfoDetailList) {
        this.returnInfoDetailList = returnInfoDetailList;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierContactTelephone() {
        return supplierContactTelephone;
    }

    public void setSupplierContactTelephone(String supplierContactTelephone) {
        this.supplierContactTelephone = supplierContactTelephone;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public GoodsReturnInfoVO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public Integer getDelayAcceptCount() {
        return delayAcceptCount;
    }

    public GoodsReturnInfoVO setDelayAcceptCount(Integer delayAcceptCount) {
        this.delayAcceptCount = delayAcceptCount;
        return this;
    }

    public Boolean getLimitOnlyWholeReturn() {
        return limitOnlyWholeReturn;
    }

    public GoodsReturnInfoVO setLimitOnlyWholeReturn(Boolean limitOnlyWholeReturn) {
        this.limitOnlyWholeReturn = limitOnlyWholeReturn;
        return this;
    }

    public Boolean getEachProductEachCode() {
        return eachProductEachCode;
    }

    public GoodsReturnInfoVO setEachProductEachCode(Boolean eachProductEachCode) {
        this.eachProductEachCode = eachProductEachCode;
        return this;
    }

    public Boolean getSuppNeedFillBatchesData() {
        return suppNeedFillBatchesData;
    }

    public GoodsReturnInfoVO setSuppNeedFillBatchesData(Boolean suppNeedFillBatchesData) {
        this.suppNeedFillBatchesData = suppNeedFillBatchesData;
        return this;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public GoodsReturnInfoVO setOrderType(Integer orderType) {
        this.orderType = orderType;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "GoodsReturnInfoVO{" +
                "id=" + id +
                ", orderNo='" + orderNo + '\'' +
                ", orderId=" + orderId +
                ", returnNo='" + returnNo + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", supplierId=" + supplierId +
                ", deliveryAddress='" + deliveryAddress + '\'' +
                ", amount=" + amount +
                ", returnInfoDetailList=" + returnInfoDetailList +
                ", refuseReason='" + refuseReason + '\'' +
                ", agreeReason='" + agreeReason + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgId=" + orgId +
                ", orgCode='" + orgCode + '\'' +
                ", phone='" + phone + '\'' +
                ", returnStatus=" + returnStatus +
                ", buyerTelephone='" + buyerTelephone + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", departmentManageName='" + departmentManageName + '\'' +
                ", suppCode='" + suppCode + '\'' +
                ", creationTime=" + creationTime +
                ", supplierContactTelephone='" + supplierContactTelephone + '\'' +
                ", delayAcceptCount=" + delayAcceptCount +
                ", autoAcceptDays=" + autoAcceptDays +
                ", limitOnlyWholeReturn=" + limitOnlyWholeReturn +
                ", eachProductEachCode=" + eachProductEachCode +
                ", suppNeedFillBatchesData=" + suppNeedFillBatchesData +
                ", stockWarehouseType=" + stockWarehouseType +
                ", orderType=" + orderType +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                ", returnReasonType=" + returnReasonType +
                '}';
    }
}
