package com.ruijing.store.goodsreturn.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.fundamental.api.annotation.Method;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.goodsreturn.request.DelayAcceptDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnPageRequestDTO;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnLogVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnPageVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnStatisticsVO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.business.service.GoodsReturnService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zhukai
 * @date : 2020/12/23 下午4:31
 * @description: 供应商退货网关接口
 */
@MSharpService(isGateway = "true")
@RpcMapping("/suppOrderReturn")
@RpcApi(value = "供应商订单-退货")
public class SupplierGoodsReturnGWController {

    @Resource
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Resource
    private GoodsReturnService goodsReturnService;

    /**
     * 我的退货列表
     * @return 退货分页对象
     */
    @RpcMethod("退货列表")
    @RpcMapping("/getPageGoodsReturn")
    public PageableResponse<List<GoodsReturnPageVO>> getPageGoodsReturn(GoodsReturnPageRequestDTO goodsReturnPageRequestDTO) {
        BasePageResponseDTO<GoodsReturnPageVO> basePageResponseDTO = supplierGoodsReturnService.getPageGoodsReturn(goodsReturnPageRequestDTO);
        return PageableResponse.<List<GoodsReturnPageVO>>custom().setSuccess()
                .setTotal(basePageResponseDTO.getTotal())
                .setPageNo(basePageResponseDTO.getPageNo())
                .setPageSize(basePageResponseDTO.getPageSize())
                .setData(basePageResponseDTO.getData());
    }

    /**
     * 退货单数据统计
     * @return 退货统计数据
     */
    @RpcMethod("退货单数据统计")
    @RpcMapping("/getGoodsReturnStatistics")
    public RemoteResponse<GoodsReturnStatisticsVO> getGoodsReturnStatistics() {
        return supplierGoodsReturnService.getGoodsReturnStatistics();
    }

    /**
     * 退货单详情
     * @return 退货单详情
     */
    @RpcMethod("退货单详情")
    @RpcMapping("/getGoodsReturnDetail")
    public RemoteResponse<GoodsReturnInfoVO> getGoodsReturnDetail(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        return supplierGoodsReturnService.getGoodsReturnInfo(goodsReturnBaseRequestDTO);
    }


    @RpcMethod("更新退货状态")
    @RpcMapping("/updateGoodsReturnStatus")
    @ServiceLog(description = "网关-更新退货状态", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> updateGoodsReturnStatus(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        return supplierGoodsReturnService.updateGoodsReturnStatus(goodsReturnBaseRequestDTO);
    }

    @Method(value = "同意退货并确认验收", author = "黄有望")
    @RpcMapping("/agreeAndAcceptReturn")
    @ServiceLog(description = "网关-同意退货并确认验收", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> agreeAndAcceptReturn(@RpcMethodParam(includePropertyNames = { "returnId", "reason", "pic", "goodsReturnInfoDetailVOList" })
                                                        GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        return supplierGoodsReturnService.agreeAndAcceptReturn(goodsReturnBaseRequestDTO);
    }

    /**
     * 获取退货单日志
     * @return 退货日志
     */
    @RpcMethod("获取退货单日志")
    @RpcMapping("/getGoodsReturnLogs")
    public RemoteResponse<List<GoodsReturnLogVO>> getGoodsReturnLogs(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        return supplierGoodsReturnService.getGoodsReturnLog(goodsReturnBaseRequestDTO);
    }

    /**
     * 退货延期验收
     */
    @RpcMethod("退货延期验收")
    @RpcMapping("/delayAccept")
    public RemoteResponse<Boolean> delayAccept(DelayAcceptDTO delayAcceptDTO) {
        supplierGoodsReturnService.delayAccept(delayAcceptDTO);
        return RemoteResponse.success();
    }

    /**
     * 后门接口补充旧退货单的数据
     */
    @RpcMethod("补充旧退货单的数据")
    @RpcMapping("/syncReplyTime")
    public RemoteResponse<Boolean> syncReplyTime(){
        goodsReturnService.syncReplyTime();
        return RemoteResponse.success();
    }
}
