package com.ruijing.store.goodsreturn.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.goodsreturn.request.DelayAcceptDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnPageRequestDTO;
import com.ruijing.store.goodsreturn.request.SupplierAuthorityDTO;
import com.ruijing.store.goodsreturn.vo.*;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.user.api.dto.OrganizationDTO;

import java.util.List;

/**
 * @author: zhukai
 * @date : 2020/12/23 下午4:38
 * @description:
 */
public interface SupplierGoodsReturnService {

    /**
     * <AUTHOR>
     * @Param [goodsReturnPageRequestDTO]
     * @return BasePageResponseDTO<GoodsReturnPageVO>
     * @Description //分页列表
     **/
    BasePageResponseDTO<GoodsReturnPageVO> getPageGoodsReturn(GoodsReturnPageRequestDTO goodsReturnPageRequestDTO);

    /**
     * <AUTHOR>
     * @Param [supplierId, orgIds]
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<com.ruijing.store.goodsreturn.vo.GoodsReturnStatisticsVO>
     * @Description //退货单统计
     **/
    RemoteResponse<GoodsReturnStatisticsVO> getGoodsReturnStatistics();

    /**
     * <AUTHOR>
     * @Param [goodsReturnBaseRequestDTO]
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<com.ruijing.store.goodsreturn.vo.GoodsReturnInfoVO>
     * @Description //详情
     **/
    RemoteResponse<GoodsReturnInfoVO> getGoodsReturnInfo(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO);

    /**
     * 获取退货单日志
     * @return 退货日志
     */
    RemoteResponse<List<GoodsReturnLogVO>> getGoodsReturnLog(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO);

    /**
     * 更新退货状态
     * @return 是否成功
     */
    RemoteResponse<Boolean> updateGoodsReturnStatus(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO);

    /**
     * 更新退货状态（绕过供应商权限认证，直接传入，供外部供应商用）
     * @param goodsReturnBaseRequestDTO 退货参数
     * @param supplierAuthorityDTO 供应商数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> updateGoodsReturnStatus(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO, GoodsReturn goodsReturn, SupplierAuthorityDTO supplierAuthorityDTO);

    /**
     * 退货成功
     * @param goodsReturn       退货单信息
     * @param organizationDTO   机构信息
     * @param orderMasterDO     订单信息
     */
    default void returnSuccess(GoodsReturn goodsReturn, OrganizationDTO organizationDTO, OrderMasterDO orderMasterDO) {
        if (goodsReturn == null || goodsReturn.getGoodsReturnDetailJSON() == null) {
            return;
        }
        String returnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(returnDetailJSON);
        this.returnSuccess(goodsReturn, goodsReturnInfoDetailVOS, organizationDTO, orderMasterDO);
    }
    /**
     * 退货成功
     * @param goodsReturn               退货单信息
     * @param goodsReturnInfoDetailVOS  退货单明细
     * @param organizationDTO           机构信息
     * @param orderMasterDO             订单信息
     */
    void returnSuccess(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS,
                       OrganizationDTO organizationDTO, OrderMasterDO orderMasterDO);

    /**
     * 获取供应商登录用户信息
     * @return
     */
    SupplierAuthorityDTO getSupplierAuthority();

    /**
     * 退货延迟验收
     * @param delayAcceptDTO
     */
    void delayAccept(DelayAcceptDTO delayAcceptDTO);

    /**
     * 同意退货并确认验收
     * @param goodsReturnBaseRequestDTO 请求参数
     * @return 是否成功
     */
    RemoteResponse<Boolean> agreeAndAcceptReturn(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO);
}
