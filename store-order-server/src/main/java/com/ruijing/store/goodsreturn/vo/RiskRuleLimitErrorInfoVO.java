package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.util.List;

/**
 * Name: RiskRuleLimitErrorInfoVO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/5/22
 */

@RpcModel(value = "竞价限额管控扣减/返还报错信息")
public class RiskRuleLimitErrorInfoVO {

    private List<ProductInfo> disablePurchaseList;

    public static class ProductInfo {
        @RpcModelProperty("商品信息")
        private List<String> productNameList;

        @RpcModelProperty("供应商名")
        private String suppName;

        @RpcModelProperty("报错信息")
        private String item;

        public ProductInfo(){};

        public ProductInfo(List<String> productNameList, String suppName, String item) {
            this.productNameList = productNameList;
            this.suppName = suppName;
            this.item = item;
        }

        public List<String> getProductNameList() {
            return productNameList;
        }

        public void setProductNameList(List<String> productNameList) {
            this.productNameList = productNameList;
        }

        public String getSuppName() {
            return suppName;
        }

        public void setSuppName(String suppName) {
            this.suppName = suppName;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }
    }

    public List<ProductInfo> getDisablePurchaseList() {
        return disablePurchaseList;
    }

    public void setDisablePurchaseList(List<ProductInfo> disablePurchaseList) {
        this.disablePurchaseList = disablePurchaseList;
    }

    @Override
    public String toString() {
        return "RiskRuleLimitErrorInfoVO{" +
                "disablePurchaseList=" + disablePurchaseList +
                '}';
    }
}
