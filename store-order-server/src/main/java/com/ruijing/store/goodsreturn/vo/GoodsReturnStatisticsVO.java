package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 退货单统计VO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 16:36
 **/
public class GoodsReturnStatisticsVO implements Serializable {

    private static final long serialVersionUID = -8421008222778267507L;

    @RpcModelProperty("退货总数")
    private Integer total;

    @RpcModelProperty("待确认|退货申请计数")
    private Integer waitingConfirmCount;

    @RpcModelProperty("供应商同意退货单计数")
    private Integer agreeReturnCount;

    @RpcModelProperty("拒绝退货单计数")
    private Integer refusedReturnCount;

    @RpcModelProperty("取消申请退货单计数")
    private Integer cancelReturnCount;

    @RpcModelProperty("采购人已退还退货单计数")
    private Integer returnedCount;

    @RpcModelProperty("退货完成计数")
    private Integer successfulCount;

    @RpcModelProperty("退货待验收计数")
    private Integer returnAcceptanceCount;

    public int getReturnAcceptanceCount() {
        return returnAcceptanceCount;
    }

    public GoodsReturnStatisticsVO setReturnAcceptanceCount(Integer returnAcceptanceCount) {
        this.returnAcceptanceCount = returnAcceptanceCount;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public GoodsReturnStatisticsVO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public Integer getWaitingConfirmCount() {
        return waitingConfirmCount;
    }

    public GoodsReturnStatisticsVO setWaitingConfirmCount(Integer waitingConfirmCount) {
        this.waitingConfirmCount = waitingConfirmCount;
        return this;
    }

    public Integer getAgreeReturnCount() {
        return agreeReturnCount;
    }

    public GoodsReturnStatisticsVO setAgreeReturnCount(Integer agreeReturnCount) {
        this.agreeReturnCount = agreeReturnCount;
        return this;
    }

    public Integer getRefusedReturnCount() {
        return refusedReturnCount;
    }

    public GoodsReturnStatisticsVO setRefusedReturnCount(Integer refusedReturnCount) {
        this.refusedReturnCount = refusedReturnCount;
        return this;
    }

    public Integer getCancelReturnCount() {
        return cancelReturnCount;
    }

    public GoodsReturnStatisticsVO setCancelReturnCount(Integer cancelReturnCount) {
        this.cancelReturnCount = cancelReturnCount;
        return this;
    }

    public Integer getReturnedCount() {
        return returnedCount;
    }

    public GoodsReturnStatisticsVO setReturnedCount(Integer returnedCount) {
        this.returnedCount = returnedCount;
        return this;
    }

    public Integer getSuccessfulCount() {
        return successfulCount;
    }

    public GoodsReturnStatisticsVO setSuccessfulCount(Integer successfulCount) {
        this.successfulCount = successfulCount;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnStatisticsVO{");
        sb.append("total=").append(total);
        sb.append(", waitingConfirmCount=").append(waitingConfirmCount);
        sb.append(", agreeReturnCount=").append(agreeReturnCount);
        sb.append(", refusedReturnCount=").append(refusedReturnCount);
        sb.append(", cancelReturnCount=").append(cancelReturnCount);
        sb.append(", returnedCount=").append(returnedCount);
        sb.append(", successfulCount=").append(successfulCount);
        sb.append('}');
        return sb.toString();
    }
}
