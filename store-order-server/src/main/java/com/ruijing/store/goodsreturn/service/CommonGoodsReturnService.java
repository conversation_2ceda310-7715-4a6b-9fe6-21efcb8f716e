package com.ruijing.store.goodsreturn.service;

import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;
import java.util.Map;

/**
 * 公用退货接口
 */
public interface CommonGoodsReturnService {

    /**
     * 根据订单号生成退货单号
     * @param orderNo   订单号
     * @return          退货单号
     */
    String createReturnNo(String orderNo);

    /**
     * 保存订单退货操作日志
     * @param goodsReturnLogDO 日志记录
     */
    void saveReturnOperationLog(GoodsReturnLogDO goodsReturnLogDO);

    /**
     * 获取当前最优先展示的退货状态的map
     *
     * @param goodsReturnList 订单对应的所有退货单
     * @return detailId -> 最优先展示的退货状态
     */
    Map<Integer, Integer> getFirstReturnStatusMapByReturn(List<GoodsReturn> goodsReturnList);

    /**
     * 推送订单信息到第三方管理平台
     * @param goodsReturn       退货快照
     * @param orderMasterDO     订单快照
     */
    void pushReturnToThirdPlatform(GoodsReturn goodsReturn, OrderMasterDO orderMasterDO);
}
