package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * Name: RiskRuleLimitErrorInfoVO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/5/22
 */

@RpcModel(value = "竞价限额管控校验报错信息")
public class RiskRuleLimitCheckErrorInfoVO {


    /**
     * 操作类型 {@link com.ruijing.fundamental.risk.control.enums.LimitControlOperateTypeEnum}
     */
    @RpcModelProperty("操作类型")
    private Integer operateType;

    /**
     * 限额校验提醒方式，1, "提醒且限制下单", 2, "提醒不限制下单"
     * {@link com.ruijing.fundamental.risk.control.riskrule.enums.RiskRuleNotifyTypeEnum}
     */
    @RpcModelProperty(value = "限额校验提醒方式", description = "1, 提醒且限制下单, 2, 提醒不限制下单")
    private Integer validateNoticeType;

    /**
     * 采购限额类型 1累计金额 2商品平均价
     * {@link com.ruijing.fundamental.risk.control.riskrule.enums.RuleFieldAmountLimitRangeEnum}
     */
    @RpcModelProperty("采购限额类型 1累计金额 2商品平均价")
    private Integer purchaseAmountLimitType;

    @RpcModelProperty("单位")
    private Integer orgId;


    /**
     * 限购商品信息
     */
    @RpcModelProperty("限购商品信息")
    private List<ProductInfo> disablePurchaseList;


    /**
     * 校验失败的商品信息
     */
    public static class ProductInfo {

        @RpcModelProperty("命中风险规则id")
        private Integer ruleId;

        @RpcModelProperty("预订单编号")
        private String orderNo;

        @RpcModelProperty("商品id")
        private Long productId;

        @RpcModelProperty("商品名")
        private String productName;

        @RpcModelProperty("供应商名")
        private String suppName;

        @RpcModelProperty("报错信息")
        private String item;

        @RpcModelProperty(value = "平均单价")
        private BigDecimal averageProductPrice;

        @RpcModelProperty("商品分类名称")
        private String productCategoryName;

        public Integer getRuleId() {
            return ruleId;
        }

        public void setRuleId(Integer ruleId) {
            this.ruleId = ruleId;
        }


        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public Long getProductId() {
            return productId;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getSuppName() {
            return suppName;
        }

        public void setSuppName(String suppName) {
            this.suppName = suppName;
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public BigDecimal getAverageProductPrice() {
            return averageProductPrice;
        }

        public void setAverageProductPrice(BigDecimal averageProductPrice) {
            this.averageProductPrice = averageProductPrice;
        }

        public String getProductCategoryName() {
            return productCategoryName;
        }

        public void setProductCategoryName(String productCategoryName) {
            this.productCategoryName = productCategoryName;
        }
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Integer getValidateNoticeType() {
        return validateNoticeType;
    }

    public void setValidateNoticeType(Integer validateNoticeType) {
        this.validateNoticeType = validateNoticeType;
    }

    public Integer getPurchaseAmountLimitType() {
        return purchaseAmountLimitType;
    }

    public void setPurchaseAmountLimitType(Integer purchaseAmountLimitType) {
        this.purchaseAmountLimitType = purchaseAmountLimitType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<ProductInfo> getDisablePurchaseList() {
        return disablePurchaseList;
    }

    public void setDisablePurchaseList(List<ProductInfo> disablePurchaseList) {
        this.disablePurchaseList = disablePurchaseList;
    }
}
