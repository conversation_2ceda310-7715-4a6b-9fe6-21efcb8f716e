package com.ruijing.store.goodsreturn.controller;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.annotation.Method;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnDetailBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnPageRequestDTO;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.*;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.OrganizationClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: zhukai
 * @date : 2020/12/23 下午4:33
 * @description: 采购人退货网关接口
 */
@MSharpService(isGateway = "true")
@RpcMapping("/buyerOrderReturn")
@RpcApi(value = "订单-退货")
public class BuyerGoodsReturnGWController {

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private BarCodeGoodsReturnService barCodeGoodsReturnService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    /**
     * 我的退货列表
     * @return 退货分页对象
     */
    @RpcMethod("我的退货列表")
    @RpcMapping("/getPageGoodsReturn")
    public PageableResponse<List<GoodsReturnPageVO>> getPageGoodsReturn(RjSessionInfo rjSessionInfo, GoodsReturnPageRequestDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        BasePageResponseDTO<GoodsReturnPageVO> result = buyerGoodsReturnService.getPageGoodsReturn(request, rjSessionInfo);
        return PageableResponse.<List<GoodsReturnPageVO>>custom().setSuccess().setTotal(result.getTotal()).setPageNo(pageNo).setPageSize(pageSize).setData(result.getData());
    }

    /**
     * 申请退货/提交退货申请
     * @return 是否成功
     */
    @RpcMethod("申请退货/提交退货申请")
    @RpcMapping("/applyGoodsReturn")
    @ServiceLog(operationType = OperationType.WRITE,description = "提交退货申请")
    public RemoteResponse<GoodsReturnApplyResponseVO> applyGoodsReturn(GoodsReturnApplyRequestDTO request) {
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(request.getOrderNo(), "订单号不可空");

        List<BaseOrderExtraDTO> baseOrderExtraDTOList = orderExtraClient.selectByOrderNoIn(New.list(request.getOrderNo()));
        Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(baseOrderExtraDTOList, BaseOrderExtraDTO::getExtraKey, BaseOrderExtraDTO::getExtraValue);
        boolean eachProductEachCode = CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue()));
        boolean suppNeedFillBatches = CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue()));
        BusinessErrUtil.isTrue(!eachProductEachCode, ExecptionMessageEnum.USE_SCAN_CODE_RETURN_FUNCTION);
        GoodsReturnApplyResponseVO result;
        if(suppNeedFillBatches){
            // OMS配置了需要强制填批次数据的单位，随机选批号数据进行退货
            result = barCodeGoodsReturnService.applyGoodsReturnWithRandomBarCode(rjSessionInfo, request);
        } else {
            result = buyerGoodsReturnService.applyGoodsReturn(request, rjSessionInfo);
        }
        return RemoteResponse.<GoodsReturnApplyResponseVO>custom().setSuccess().setData(result);
    }

    @Method(value = "编辑退货单", description = "仅支持订单粒度退货原因页面,不支持旧的商品粒度页面", author = "黄有望")
    @RpcMapping("/editGoodsReturn")
    @ServiceLog(operationType = OperationType.WRITE, description = "编辑退货单")
    public RemoteResponse<GoodsReturnApplyResponseVO> editGoodsReturn(
            @RpcMethodParam(includePropertyNames = { "id", "returnReason", "remark", "goodsImageList" }) GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        GoodsReturnApplyResponseVO result = buyerGoodsReturnService.editGoodsReturn(request, rjSessionInfo);
        return RemoteResponse.success(result);
    }

    /**
     * 退货单数据统计
     * @return 退货统计数据
     */
    @RpcMethod("退货单数据统计")
    @RpcMapping("/getGoodsReturnStatistics")
    public RemoteResponse<GoodsReturnStatisticsVO> getGoodsReturnStatistics(RjSessionInfo rjSessionInfo) {
        GoodsReturnStatisticsVO goodsReturnStatistics = buyerGoodsReturnService.getGoodsReturnStatistics(rjSessionInfo);
        return RemoteResponse.<GoodsReturnStatisticsVO>custom().setSuccess().setData(goodsReturnStatistics);
    }

    /**
     * 退货单详情
     * @return 退货单详情
     */
    @RpcMethod("退货单详情，只需要returnId入参")
    @RpcMapping("/getGoodsReturnDetail")
    public RemoteResponse<GoodsReturnInfoVO> getGoodsReturnDetail(GoodsReturnBaseRequestDTO request) {
        GoodsReturnInfoVO goodsReturnInfo = buyerGoodsReturnService.getGoodsReturnInfo(request);
        return RemoteResponse.<GoodsReturnInfoVO>custom().setSuccess().setData(goodsReturnInfo);
    }

    /**
     * 取消退货
     * @return 是否成功
     */
    @RpcMethod("取消退货，只需要returnId入参")
    @RpcMapping("/cancelGoodsReturnDetail")
    @ServiceLog(operationType = OperationType.WRITE,description = "取消退货申请")
    public RemoteResponse<Boolean> cancelGoodsReturnDetail(RjSessionInfo rjSessionInfo, GoodsReturnBaseRequestDTO request) {
        boolean result = buyerGoodsReturnService.cancelGoodsReturn(request, rjSessionInfo);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    /**
     * 采购人更新退货状态
     * @return 是否成功
     */
    @RpcMethod("采购人更新退货状态，需要returnId，returnStatus入参")
    @RpcMapping("/updateGoodsReturnStatus")
    @ServiceLog(operationType = OperationType.WRITE,description = "采购人更新退货状态")
    public RemoteResponse<Boolean> updateGoodsReturnStatus(RjSessionInfo rjSessionInfo, GoodsReturnBaseRequestDTO request) {
        boolean result = buyerGoodsReturnService.updateGoodsReturn(request, rjSessionInfo);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    /**
     * 获取退货单日志
     * @return 退货分页日志
     */
    @RpcMethod("获取退货单日志，只需要returnId入参")
    @RpcMapping("/getGoodsReturnLogs")
    public RemoteResponse<List<GoodsReturnLogVO>> getGoodsReturnLogs(GoodsReturnBaseRequestDTO request) {
        List<GoodsReturnLogVO> result = buyerGoodsReturnService.getGoodsReturnLog(request);
        return RemoteResponse.<List<GoodsReturnLogVO>>custom().setSuccess().setData(result);
    }

    /**
     * 获取订单明细的退货数量
     * @return 获取订单明细的退货数量
     */
    @RpcMethod("获取订单明细的退货数量，需要returnId，detailId入参")
    @RpcMapping("/getGoodsReturnCountByDetailId")
    public RemoteResponse<Integer> getGoodsReturnCountByDetailId(GoodsReturnDetailBaseRequestDTO request) {
        int returnCount = buyerGoodsReturnService.getReturnCountByDetailId(request);
        return RemoteResponse.<Integer>custom().setSuccess().setData(returnCount);
    }

    /**
     * 获取退货申请单界面数据，主体还是靠orderDetail里面的订单数据，这里暂时是对退货数据的补充
     * @return 退货申请单数据
     */
    @RpcMethod("获取退货申请单界面数据")
    @RpcMapping("/getApplyGoodsReturnData")
    public RemoteResponse<ApplyGoodsReturnOrderVO> getApplyGoodsReturnData(RjSessionInfo rjSessionInfo, GoodsReturnApplyRequestDTO request){
        return RemoteResponse.success(buyerGoodsReturnService.getApplyGoodsReturnData(request.getOrderNo(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER)));
    }

    @RpcMethod(value = "查询所选退货商品是否正在入库中", notes = "true-正在入库中，false-不在入库中,用于退货前二次确认弹窗")
    @RpcMapping("/checkGoodsReturnEntry")
    public RemoteResponse<Boolean> checkGoodsReturnInStock(@RpcMethodParam(includePropertyNames = { "orderNo", "returnApplyDetailList" }) GoodsReturnApplyRequestDTO request) {
        return RemoteResponse.success(buyerGoodsReturnService.checkGoodsReturnEntry(request));
    }

    @RpcMethod(value = "撤销入库", notes = "用于发起退货时撤销入库")
    @RpcMapping("/cancelGoodsReturnInStock")
    @ServiceLog(operationType = OperationType.WRITE, description = "发起退货前撤销入库")
    public RemoteResponse<Boolean> cancelGoodsReturnInStock(RjSessionInfo rjSessionInfo,
                                                            @RpcMethodParam(includePropertyNames = { "orderNo", "returnApplyDetailList" }) GoodsReturnApplyRequestDTO request) {
        buyerGoodsReturnService.entryWithDraw(request, rjSessionInfo);
        return RemoteResponse.success();
    }

}
