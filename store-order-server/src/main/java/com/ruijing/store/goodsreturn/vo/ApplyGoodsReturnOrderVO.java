package com.ruijing.store.goodsreturn.vo;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Liwenyu
 * @create: 2024-05-06 09:48
 * @description:
 */
public class ApplyGoodsReturnOrderVO implements Serializable {

    private static final long serialVersionUID = 1867467216912214491L;

    /**
     * 订单号
     */
    @ModelProperty("订单号")
    private String orderNo;

    /**
     * 可以退货的商品
     */
    @ModelProperty("可以退货的商品信息")
    private List<ApplyGoodsReturnOrderDetailVO> canReturnOrderDetails;

    public String getOrderNo() {
        return orderNo;
    }

    public ApplyGoodsReturnOrderVO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public List<ApplyGoodsReturnOrderDetailVO> getCanReturnOrderDetails() {
        return canReturnOrderDetails;
    }

    public ApplyGoodsReturnOrderVO setCanReturnOrderDetails(List<ApplyGoodsReturnOrderDetailVO> canReturnOrderDetails) {
        this.canReturnOrderDetails = canReturnOrderDetails;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ApplyGoodsReturnOrderVO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("canReturnOrderDetails=" + canReturnOrderDetails)
                .toString();
    }
}
