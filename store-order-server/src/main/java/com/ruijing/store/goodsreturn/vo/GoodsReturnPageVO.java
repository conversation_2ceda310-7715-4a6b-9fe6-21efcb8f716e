package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 退货单VO
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 11:00
 **/
public class GoodsReturnPageVO implements Serializable {

    private static final long serialVersionUID = -8041657820424261634L;

    @RpcModelProperty("退货单id")
    private Integer id;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("申请退货时间")
    private Long applyTime;

    @RpcModelProperty("供应商id")
    private Integer supplierId;

    @RpcModelProperty("供应商名称")
    private String supplierName;

    @RpcModelProperty("采购人名称")
    private String buyerName;

    @RpcModelProperty("部门/课题组名称")
    private String departmentName;

    @RpcModelProperty("退货状态，0-待确认，1-同意退货，2-拒绝退货，3-取消申请，4-采购人已退还货物，5-退货成功")
    private Integer returnStatus;

    @RpcModelProperty("退货单商品详情")
    private List<GoodsReturnDetailPageVO> goodsReturnDetailList;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("采购单位名称")
    private String orgName;

    @RpcModelProperty("机构编码")
    private String orgCode;

    @RpcModelProperty("订单状态")
    private Integer orderStatus;

    @RpcModelProperty("退货总金额")
    private BigDecimal totalPrice;

    @RpcModelProperty("供应商QQ")
    private String suppQQ;

    @RpcModelProperty("延迟验收次数")
    private Integer delayAcceptCount;

    @RpcModelProperty("自动验收时间")
    private Date replyTime;

    @RpcModelProperty("距离自动验收天数")
    private Integer autoAcceptDays;

    @RpcModelProperty(value = "货仓标识",enumClass = StockTypeEnum.class)
    private Integer stockWarehouseType;

    @RpcModelProperty(value = "订单类型")
    private Integer orderType;

    public Integer getStockWarehouseType() {
        return stockWarehouseType;
    }

    public GoodsReturnPageVO setStockWarehouseType(Integer stockWarehouseType) {
        this.stockWarehouseType = stockWarehouseType;
        return this;
    }

    public Integer getAutoAcceptDays() {
        return autoAcceptDays;
    }

    public void setAutoAcceptDays(Integer autoAcceptDays) {
        this.autoAcceptDays = autoAcceptDays;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Long getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Long applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public List<GoodsReturnDetailPageVO> getGoodsReturnDetailList() {
        return goodsReturnDetailList;
    }

    public void setGoodsReturnDetailList(List<GoodsReturnDetailPageVO> goodsReturnDetailList) {
        this.goodsReturnDetailList = goodsReturnDetailList;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getSuppQQ() {
        return suppQQ;
    }

    public void setSuppQQ(String suppQQ) {
        this.suppQQ = suppQQ;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public GoodsReturnPageVO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public Integer getDelayAcceptCount() {
        return delayAcceptCount;
    }

    public GoodsReturnPageVO setDelayAcceptCount(int delayAcceptCount) {
        this.delayAcceptCount = delayAcceptCount;
        return this;
    }

    public void setDelayAcceptCount(Integer delayAcceptCount) {
        this.delayAcceptCount = delayAcceptCount;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public GoodsReturnPageVO setOrderType(Integer orderType) {
        this.orderType = orderType;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnPageVO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("orderId=" + orderId)
                .add("returnNo='" + returnNo + "'")
                .add("applyTime=" + applyTime)
                .add("supplierId=" + supplierId)
                .add("supplierName='" + supplierName + "'")
                .add("buyerName='" + buyerName + "'")
                .add("departmentName='" + departmentName + "'")
                .add("returnStatus=" + returnStatus)
                .add("goodsReturnDetailList=" + goodsReturnDetailList)
                .add("orderNo='" + orderNo + "'")
                .add("orgName='" + orgName + "'")
                .add("orgCode='" + orgCode + "'")
                .add("orderStatus=" + orderStatus)
                .add("totalPrice=" + totalPrice)
                .add("suppQQ='" + suppQQ + "'")
                .add("delayAcceptCount=" + delayAcceptCount)
                .add("replyTime=" + replyTime)
                .add("autoAcceptDays=" + autoAcceptDays)
                .add("stockWarehouseType=" + stockWarehouseType)
                .add("orderType=" + orderType)
                .toString();
    }
}
