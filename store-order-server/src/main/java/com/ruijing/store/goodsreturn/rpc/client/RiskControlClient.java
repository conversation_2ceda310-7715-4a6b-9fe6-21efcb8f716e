package com.ruijing.store.goodsreturn.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.risk.control.riskmessage.api.RiskMessageResultOrderRPCService;
import com.ruijing.fundamental.risk.control.riskmessage.dto.RiskOrderListRequest;
import com.ruijing.fundamental.risk.control.riskmessage.dto.RiskOrderResponse;
import com.ruijing.fundamental.risk.control.riskrule.api.RiskRuleLimitControlService;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.LimitControlOrderParamDTO;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.RiskRuleLimitControlResultDTO;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.OperationType;
import com.ruijing.store.order.log.enums.ServiceType;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

/**
 * Name: RiskControlClient
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/5/19
 */
@ServiceClient
public class RiskControlClient {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "riskControlClient";

    @MSharpReference(remoteAppkey = "msharp-risk-control-service")
    private RiskRuleLimitControlService riskRuleLimitControlService;

    @MSharpReference(remoteAppkey = "msharp-risk-control-service")
    private RiskMessageResultOrderRPCService riskMessageResultOrderRPCService;


    /**
     * 校验接口
     * @param limitControlOrderParamDTO
     * @return
     */
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public RiskRuleLimitControlResultDTO riskRuleValidateByOrder(LimitControlOrderParamDTO limitControlOrderParamDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "riskRuleLimitControlCheck");
        RiskRuleLimitControlResultDTO resultDTO = null;
        try{
            RemoteResponse<RiskRuleLimitControlResultDTO> response = riskRuleLimitControlService.riskRuleValidateByOrder(limitControlOrderParamDTO);
            if (!response.isSuccess()){
                LOGGER.error("风控规则限额管控RPC校验失败！返回信息为：{}" , JsonUtils.toJson(response));
            }
            Preconditions.isTrue(response.isSuccess(), "风控规则限额管控RPC校验失败！");
            resultDTO = response.getData();
            Preconditions.notNull(resultDTO, "风控规则限额管控RPC校验返回resultDTO为空! ");
            transaction.setSuccessStatus();
        }catch (Exception e){
            transaction.setStatus(e);
            transaction.addData(JsonUtils.toJson(limitControlOrderParamDTO));
            LOGGER.info("风控规则限额管控校验失败!", e);
            throw e;
        }finally {
            transaction.complete();
        }
        return resultDTO;
    }

    /**
     * 扣减返还接口
     * @param limitControlOrderParamDTO
     * @return
     */
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public RiskRuleLimitControlResultDTO riskRuleCostDeductByOrder(LimitControlOrderParamDTO limitControlOrderParamDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "riskRuleLimitControlCheck");
        RiskRuleLimitControlResultDTO resultDTO = null;
        try{
            RemoteResponse<RiskRuleLimitControlResultDTO> response = riskRuleLimitControlService.riskRuleCostDeductByOrder(limitControlOrderParamDTO);
            if (!response.isSuccess()){
                LOGGER.error("风控规则限额管控扣减/返还RPC调用失败！返回信息为：{}" , JsonUtils.toJson(response));
            }
            Preconditions.isTrue(response.isSuccess(), "风控规则限额管控扣减/返还RPC调用失败！");
            resultDTO = response.getData();
            Preconditions.notNull(resultDTO, "风控规则限额管控扣减/返还RPC接口返回resultDTO为空! ");
            transaction.setSuccessStatus();
        }catch (Exception e){
            transaction.setStatus(e);
            transaction.addData(JsonUtils.toJson(limitControlOrderParamDTO));
            LOGGER.info("风控规则限额管控扣减/返还失败!", e);
            throw e;
        }finally {
            transaction.complete();
        }
        return resultDTO;
    }

    @ServiceLog(description = "查询异常订单是否存在", serviceType = ServiceType.RPC_CLIENT)
    public List<RiskOrderResponse> existsRiskMessageOrder(RiskOrderListRequest riskOrderListRequest) {
        if (Objects.isNull(riskOrderListRequest) || CollectionUtils.isEmpty(riskOrderListRequest.getOrderNoList())) {
            return New.emptyList();
        }
        List<RiskOrderResponse> resultList = New.list();
        List<List<String>> partitionOrderNoList = Lists.partition(riskOrderListRequest.getOrderNoList(), 100);

        for (List<String> subOrderNoList : partitionOrderNoList) {
            RiskOrderListRequest subRequest = new RiskOrderListRequest();
            subRequest.setOrgId(riskOrderListRequest.getOrgId());
            subRequest.setRuleIdList(riskOrderListRequest.getRuleIdList());
            subRequest.setOrderNoList(New.list(subOrderNoList));

            RemoteResponse<List<RiskOrderResponse>> response = riskMessageResultOrderRPCService.existsRiskMessageOrder(subRequest);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());

            if (CollectionUtils.isNotEmpty(response.getData())) {
                resultList.addAll(response.getData());
            }
        }
        return resultList;
    }
}
