package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.ThirdPartOrderReturnDTO;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.order.enums.event.OrderReturnEventEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnImageDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnImageDO;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.ThirdPartOrderRPCClient;
import com.ruijing.store.order.util.BusinessNoUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/1/5 15:03
 **/
@Service
@CatAnnotation
public class CommonGoodsReturnServiceImpl implements CommonGoodsReturnService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private static final String CAT_TYPE = "CommonGoodsReturnServiceImpl";

    @Autowired
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Resource
    private GoodsReturnImageDOMapper goodsReturnImageDOMapper;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Override
    public String createReturnNo(String orderNo) {
        String returnNo = null;
        String suffix = null;

        GoodsReturn goodsReturnDO = null;
        String orderNoPrefix = BusinessNoUtils.OrderNumberType.R.getCode();
        if (orderNo.startsWith(orderNoPrefix)) {
            goodsReturnDO = goodsReturnMapper.selectOneByReturnNoLike(BusinessNoUtils.OrderNumberType.T.getCode() + orderNo);
        } else {
            goodsReturnDO = goodsReturnMapper.selectOneByReturnNoLike(BusinessNoUtils.OrderNumberType.T.getCode() + orderNo.substring(1, 15));
        }

        if (goodsReturnDO != null) {
            String existReturnNo = goodsReturnDO.getReturnNo();
            suffix = existReturnNo.substring(existReturnNo.length() - 2);
        }
        // 生成退货单号
        returnNo = BusinessNoUtils.getBusinessNumber(BusinessNoUtils.BusinessType.RETURN, orderNo, suffix);
        return returnNo;
    }

    /**
     * 保存退货操作日志
     * @param log
     */
    @Override
    public void saveReturnOperationLog(GoodsReturnLogDO log) {
        AsyncExecutor.listenableRunAsync(() -> {
            Integer returnId = Optional.ofNullable(log.getReturnId()).orElse(0);
            if (returnId == 0) {
                return;
            }
            cacheClient.controlRepeatOperation(returnId.toString(), 3);
            List<GoodsReturnLogDO> existLog = goodsReturnLogDOMapper.findByReturnIdInAndOperationType(Collections.singletonList(returnId), log.getOperationType());
            // 存在更新, 否则插入
            if (CollectionUtils.isNotEmpty(existLog)) {
                log.setId(existLog.get(0).getId());
                goodsReturnLogDOMapper.updateByPrimaryKeySelective(log);
            } else {
                goodsReturnLogDOMapper.insertSelective(log);
            }
            cacheClient.removeCache(returnId.toString());
        }).addFailureCallback(throwable -> {
            LOGGER.error("保存退货操作日志异常：" + throwable);
            Cat.logError(CAT_TYPE, "saveReturnOperationLog", "保存退货操作日志异常", throwable);
        });
    }

    @Override
    public Map<Integer, Integer> getFirstReturnStatusMapByReturn(List<GoodsReturn> goodsReturnList) {
        List<GoodsReturn> sort = goodsReturnList.stream()
                // 过滤正常的退货单
                .filter(it -> GoodsReturnInvalidEnum.NORMAL.getCode().equals(it.getInvalid()))
                // 过滤已退货成功的单
                .filter(it -> !GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(it.getGoodsReturnStatus()))
                // 按照 退货状态显示优先级 已完成退货5->采购人已退货4->拒绝退货2->同意退货1->待确认0 排序
                .sorted((o1, o2) -> o2.getGoodsReturnStatus() - o1.getGoodsReturnStatus())
                .collect(Collectors.toList());

        Map<Integer, Integer> result = new HashMap<>();
        for (GoodsReturn g : sort) {
            String existGoodsReturnDetailJSON = g.getGoodsReturnDetailJSON();
            List<GoodsReturnInfoDetailVO> existGoodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(existGoodsReturnDetailJSON);
            // 队尾的退货状态是展示优先级最高的退货状态
            for (GoodsReturnInfoDetailVO item : existGoodsReturnInfoDetailVOList) {
                result.put(Integer.parseInt(item.getDetailId()), g.getGoodsReturnStatus());
            }
        }
        return result;
    }

    @Override
    public void pushReturnToThirdPlatform(GoodsReturn goodsReturn, OrderMasterDO orderMasterDO) {
        Integer returnStatus = goodsReturn.getGoodsReturnStatus();
        AsyncExecutor.runAsync(() -> {
            // 通知退货事件
            if (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(returnStatus)) {
                thirdPartOrderRPCClient.noticeReturnEvent(goodsReturn.getReturnNo(), OrderReturnEventEnum.SUPPLIER_APPROVE_RETURN_ORDER);
            } else if (GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(returnStatus)) {
                thirdPartOrderRPCClient.noticeReturnEvent(goodsReturn.getReturnNo(), OrderReturnEventEnum.SUPPLIER_REJECT_RETURN_ORDER);
            } else if (GoodsReturnStatusEnum.SUCCESS.getCode().equals(returnStatus)) {
                thirdPartOrderRPCClient.noticeReturnEvent(goodsReturn.getReturnNo(), OrderReturnEventEnum.RETURN_ORDER_COMPLETE);
            }
        });

        // 原退货推送，后续考虑整合到上面noticeReturnEvent中
        AsyncExecutor.listenableRunAsync(() -> {
            OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
            boolean needPushReturn = false;
            if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
                boolean syncResult = OmsDockingConfigValueEnum.PUSH_BY_RUIJING.name().equals(config.getOrderDockingConfigDTO().getOrderSyncReplyReturnGoodsResult()) && (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(returnStatus) || GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(returnStatus));
                boolean syncAcceptReturnGoods = OmsDockingConfigValueEnum.PUSH_BY_RUIJING.name().equals(config.getOrderDockingConfigDTO().getOrderSyncSuppAcceptReturnGoods()) && GoodsReturnStatusEnum.SUCCESS.getCode().equals(returnStatus);
                needPushReturn = syncResult || syncAcceptReturnGoods;
            } else {
                needPushReturn = dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.RETURN_ALL_RETURN_AND_NOTICE, OrderDockingStrategyEnum.RETURN_PART_RETURN_AND_NOTICE, OrderDockingStrategyEnum.PUSH_RETURN_COMPLETE_STATUS));
            }
            // 新单位走order—thunder-service接口
            if (needPushReturn) {
                ThirdPartOrderReturnDTO thirdPartReturnInfo = GoodsReturnTranslator.doToThirdPartOrderDTO(goodsReturn);
                // 订单确认推送管理平台
                thirdPartOrderRPCClient.pushReturnInfo(orderMasterDO.getFusercode(), thirdPartReturnInfo, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
            } else {
                orderMasterForTPIService.asyncOrderReturn(goodsReturn, orderMasterDO.getFusercode());
            }
        }).addFailureCallback(throwable -> {
            LOGGER.error("通知第三方平台退货完成失败：" + throwable);
            Cat.logError(CAT_TYPE, "orderReturn", "通知第三方平台退货完成失败", throwable);
        });
    }

    private GoodsReturnLogDO buildReturnLogDO(GoodsReturn goodsReturn, GoodsReturnOperatorTypeEnum operatorType, GoodsReturnOperationTypeEnum operationType, String remark) {
        return this.buildReturnLogDO(goodsReturn, operatorType, operationType, remark, StringUtils.EMPTY);
    }

        /**
         * 构建操作日志记录
         * @param goodsReturn
         * @param operatorType
         * @param operationType
         * @param remark
         * @return
         */
    private GoodsReturnLogDO buildReturnLogDO(GoodsReturn goodsReturn, GoodsReturnOperatorTypeEnum operatorType, GoodsReturnOperationTypeEnum operationType, String remark, String imageURL) {
        GoodsReturnLogDO log = new GoodsReturnLogDO();
        log.setReturnId(goodsReturn.getId());
        log.setOperatorId(goodsReturn.getUserId());
        log.setOperatorName(goodsReturn.getApplyName());
        log.setOperatorType(operatorType.getCode());
        log.setOperationType(operationType.getCode());
        log.setRemark(remark);
        log.setImagesURL(imageURL);
        return log;
    }
}
