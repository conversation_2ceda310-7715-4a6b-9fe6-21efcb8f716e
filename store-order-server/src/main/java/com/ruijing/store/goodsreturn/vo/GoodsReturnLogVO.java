package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 17:50
 **/
public class GoodsReturnLogVO implements Serializable {

    private static final long serialVersionUID = 411486584900169478L;

    @RpcModelProperty("id")
    private Integer id;

    @RpcModelProperty("退货单id")
    private Integer returnId;

    @RpcModelProperty("操作人")
    private String operatorName;

    @RpcModelProperty("操作内容")
    private Integer operationType;

    @RpcModelProperty("时间")
    private Long createTime;

    @RpcModelProperty("备注")
    private String remark;

    @RpcModelProperty("凭证数组")
    private List<String> imagesURL;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getImagesURL() {
        return imagesURL;
    }

    public void setImagesURL(List<String> imagesURL) {
        this.imagesURL = imagesURL;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnLogVO{");
        sb.append("id=").append(id);
        sb.append(", returnId=").append(returnId);
        sb.append(", operatorName='").append(operatorName).append('\'');
        sb.append(", operationType=").append(operationType);
        sb.append(", createTime=").append(createTime);
        sb.append(", remark='").append(remark).append('\'');
        sb.append(", imagesURL=").append(imagesURL);
        sb.append('}');
        return sb.toString();
    }
}
