package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领商品分页查询返回信息")
public class ClaimProductPagingVO implements Serializable {

    private static final long serialVersionUID = -7293052080194680544L;

    @RpcModelProperty(value = "申领商品列表")
    private List<WarehouseProductInfoVO> claimProductList;

    @RpcModelProperty(value = "总条数")
    private Integer total;

    @RpcModelProperty(value = "总页数")
    private Integer totalPage;

    @RpcModelProperty(value = "当前页")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小")
    private Integer pageSize;

    public List<WarehouseProductInfoVO> getClaimProductList() {
        return claimProductList;
    }

    public void setClaimProductList(List<WarehouseProductInfoVO> claimProductList) {
        this.claimProductList = claimProductList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ClaimProductPagingVO{");
        sb.append("claimProductList=").append(claimProductList);
        sb.append(", total=").append(total);
        sb.append(", totalPage=").append(totalPage);
        sb.append(", currentPage=").append(currentPage);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
