package com.ruijing.store.warehouse.callback.impl;

import com.reagent.bid.api.rpc.enums.OperationEnum;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.warehouse.dto.FinishWarehouseDataDTO;
import com.ruijing.store.order.api.warehouse.service.OrderWareHouseRpcService;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.warehouse.callback.WarehouseCallbackService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/1 17:05
 * @description 处理订单入库时点相关回调
 */
@Service
public class WarehouseCallbackServiceImpl implements WarehouseCallbackService {

    /**
     * 入库推送会更新入库状态的单位
     */
    public static final List<String> ORG_CHANGE_INVENTORY_STATUS = New.list(
            OrgEnum.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode()
    );

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderWareHouseRpcService orderWareHouseRpcService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    /**
     * 处理回调
     *
     * @param orderEventPushResultResponseDTO 推送状态变更通知数据体
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "订单入库事件回调", serviceType = ServiceType.COMMON_SERVICE)
    public void handleCallback(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        String orderNo = orderEventPushResultResponseDTO.getOrderNo();
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderNo);
        Integer orderId = orderMaster.getId();
        OrderEventStatusEnum pushStatus = orderEventPushResultResponseDTO.getOrderEventStatusEnum();
        // 如果是温州医此时入库失败则更新调用入库失败的状态
        if (ORG_CHANGE_INVENTORY_STATUS.contains(orderMaster.getFusercode())) {
            if (OrderEventStatusEnum.FAILED.equals(pushStatus)) {
                UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                updateOrderParamDTO.setOrderId(orderId);
                updateOrderParamDTO.setInventoryStatus(InventoryStatusEnum.FAILED_TO_PUSH.getCode());
                orderMasterMapper.updateOrderById(updateOrderParamDTO);
            } else if (OrderEventStatusEnum.COMPLETE.equals(pushStatus)) {
                InventoryStatusEnum toUpdateInventoryStatus = InventoryStatusEnum.COMPLETE;
                List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
                // 温医附一，需要根据是否危化品订单来判断入库完成后应该恢复的状态。 非危化品无须入库
                if (OrgEnum.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orderMaster.getFuserid()) {
                    boolean unDangerousOrder = orderDetailDOList.stream().anyMatch(detail -> detail.getDangerousTypeId() == null || DangerousTypeEnum.UN_DANGEROUS.getValue().equals(detail.getDangerousTypeId()));
                    toUpdateInventoryStatus = unDangerousOrder ? InventoryStatusEnum.NOT_INBOUND : InventoryStatusEnum.COMPLETE;
                }
                // 推送成功处理
                // 1.更新库房状态
                UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                updateOrderParamDTO.setOrderId(orderId);
                updateOrderParamDTO.setInventoryStatus(toUpdateInventoryStatus.getCode());
                orderMasterMapper.updateOrderById(updateOrderParamDTO);
                // 2.调用入库完成事件
                FinishWarehouseDataDTO finishWarehouseDataDTO = new FinishWarehouseDataDTO();
                finishWarehouseDataDTO.setOrderId(orderId);
                orderWareHouseRpcService.finishWarehouse(finishWarehouseDataDTO);
            }
        }

        // 记日志
        OrderApprovalEnum orderApprovalEnum = null;
        switch (orderEventPushResultResponseDTO.getOrderEventStatusEnum()) {
            case PUSHING:
                orderApprovalEnum = OrderApprovalEnum.PUSHING_WAREHOUSE_TO_THIRD;
                break;
            case COMPLETE:
                orderApprovalEnum = OrderApprovalEnum.PUSH_WAREHOUSE_TO_THIRD_SUCCESS;
                break;
            case FAILED:
                orderApprovalEnum = OrderApprovalEnum.PUSH_WAREHOUSE_TO_THIRD_FAILURE;
                break;
            default:
                break;
        }
        if (orderApprovalEnum != null) {
            orderApprovalLogService.saveApprovalLog(orderId, orderApprovalEnum.getValue(), DockingConstant.SYSTEM_OPERATOR_ID, orderEventPushResultResponseDTO.getFailReason());
        }

        if (OrderEventStatusEnum.FAILED.equals(pushStatus)) {
            orderMessageHandler.sendPushFailMsg(orderMaster);
        }
    }
}
