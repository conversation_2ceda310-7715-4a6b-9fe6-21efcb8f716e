package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领审批状态键值")
public class ClaimApprovalStatusVO implements Serializable {

    private static final long serialVersionUID = -6200374521062579731L;

    @RpcModelProperty(value = "审批状态（0待审批、1审核通过、2审核驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称（待审批、审核通过、审核驳回）")
    private String approvalStatusName;

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseApprovalStatusVO{");
        sb.append("approvalStatus=").append(approvalStatus);
        sb.append(", approvalStatusName='").append(approvalStatusName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
