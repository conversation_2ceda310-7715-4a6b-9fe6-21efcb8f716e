package com.ruijing.store.warehouse.message.vo.outwarehouse;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.ElectronicSignDataVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseApprovalLogVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 * 出库申请单详细信息
 */
@RpcModel(description="出库单详情")
public class OutWarehouseApplicationDetailVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "出库单Id")
    private Integer outWarehouseApplicationId;

    @RpcModelProperty(value = "出库单号")
    private String outWarehouseApplicationNo;

    @RpcModelProperty(value = "订单Id")
    private Integer orderId;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "出库申请时间")
    private Long outWarehouseApplicationTime;

    @RpcModelProperty(value = "出库申请人")
    private String outWarehouseApplicant;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "单位名称")
    private String orgName;

    @RpcModelProperty(value = "该出库单对应的订单选择的经费卡所属的项目编码")
    private String projectCode;

    @RpcModelProperty(value = "该出库单对应的订单选择的经费卡所属的项目名称")
    private String projectName;

    @RpcModelProperty(value = "该出库单对应的订单选择的经费卡号")
    private String funCardNo;

    @RpcModelProperty(value = "该出库单对应的订单的供应商名称")
    private String supplierName;

    @RpcModelProperty(value = "该出库单对应的订单的供应商编码")
    private String supplierCode;

    @RpcModelProperty(value = "出库单状态（状态0未出库，1已出库）")
    private  Integer status;

    @RpcModelProperty(value = "申请出库商品状态名称（未出库、已出库）")
    private String statusName;

    @RpcModelProperty(value = "采购人姓名")
    private String purchaserName;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    @RpcModelProperty(value = "当前部门的上一级部门")
    private String departmentParentName;

    @RpcModelProperty(value = "出库单所有商品的总额的合计")
    private String totalPrice;

    @RpcModelProperty(value = "合计金额的大写")
    private String totalPriceInChinese;

    @RpcModelProperty(value = "科长(对宁波二院：出库单对应的订单对应的采购申请单的二级审批人姓名）")
    private String sectionChief;

    @RpcModelProperty(value = "验收人(对宁波二院：验收审批人)")
    private String acceptor;

    @RpcModelProperty(value = "课题组负责人（对宁波二院：订单的采购人所属的课题组的PI姓名）")
    private String departmentDirector;

    @RpcModelProperty(value = "出库日期，时间戳格式")
    private Long outWarehouseTime;

    @RpcModelProperty(value = "入库审核人姓名")
    private String approverName;

    @RpcModelProperty(value = "入库审核时间")
    private String approvalTimeString;

    @RpcModelProperty(value = "采购人联系方式")
    private String purchaserPhone;

    @RpcModelProperty(value = "订单号对应条形码(base64编码后的字符串)")
    private String orderNoBarcode;

    @RpcModelProperty(value = "出库单号对应条形码")
    private String exitNoBarcode;

    @RpcModelProperty(value = "发票单号，多个的话用逗号隔开")
    private String invoiceNo;

    @RpcModelProperty(value = "发票号列表，适应单位个性化, invoiceNo的列表化")
    private List<String> invoiceNoList;

    @RpcModelProperty(value = "开票日期时间戳列表")
    private List<Long> invoiceDateTimeList;

    @RpcModelProperty(value = "订单流程种类 0:线上, 1:线下")
    private Integer orderSpecies;

    @RpcModelProperty(value = "出库申请单关联商品信息列表")
    private List<WarehouseProductInfoVO> warehouseProductInfoVOList;

    @RpcModelProperty(value = "订单审批日志")
    List<WarehouseApprovalLogVO> orderApprovalLogs;

    @RpcModelProperty(value = "采购与竞价审批日志")
    List<WarehouseApprovalLogVO> purchaseApprovalLogs;

    @RpcModelProperty(value = "电子签名")
    List<ElectronicSignDataVO> electronicSignDataVOList;

    @RpcModelProperty(value = "出库科室/领用科室")
    private String exitDeptName;
    

    public Integer getOutWarehouseApplicationId() {
        return outWarehouseApplicationId;
    }

    public void setOutWarehouseApplicationId(Integer outWarehouseApplicationId) {
        this.outWarehouseApplicationId = outWarehouseApplicationId;
    }

    public String getOutWarehouseApplicationNo() {
        return outWarehouseApplicationNo;
    }

    public void setOutWarehouseApplicationNo(String outWarehouseApplicationNo) {
        this.outWarehouseApplicationNo = outWarehouseApplicationNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOutWarehouseApplicationTime() {
        return outWarehouseApplicationTime;
    }

    public void setOutWarehouseApplicationTime(Long outWarehouseApplicationTime) {
        this.outWarehouseApplicationTime = outWarehouseApplicationTime;
    }

    public String getOutWarehouseApplicant() {
        return outWarehouseApplicant;
    }

    public void setOutWarehouseApplicant(String outWarehouseApplicant) {
        this.outWarehouseApplicant = outWarehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }


    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<WarehouseProductInfoVO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductInfoVO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public void setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
    }

    public String getSectionChief() {
        return sectionChief;
    }

    public void setSectionChief(String sectionChief) {
        this.sectionChief = sectionChief;
    }

    public String getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(String acceptor) {
        this.acceptor = acceptor;
    }

    public String getDepartmentDirector() {
        return departmentDirector;
    }

    public void setDepartmentDirector(String departmentDirector) {
        this.departmentDirector = departmentDirector;
    }

    public Long getOutWarehouseTime() {
        return outWarehouseTime;
    }

    public void setOutWarehouseTime(Long outWarehouseTime) {
        this.outWarehouseTime = outWarehouseTime;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getPurchaserPhone() {
        return purchaserPhone;
    }

    public void setPurchaserPhone(String purchaserPhone) {
        this.purchaserPhone = purchaserPhone;
    }

    public String getOrderNoBarcode() {
        return orderNoBarcode;
    }

    public void setOrderNoBarcode(String orderNoBarcode) {
        this.orderNoBarcode = orderNoBarcode;
    }

    public String getFunCardNo() {
        return funCardNo;
    }

    public void setFunCardNo(String funCardNo) {
        this.funCardNo = funCardNo;
    }

    public String getApprovalTimeString() {
        return approvalTimeString;
    }

    public void setApprovalTimeString(String approvalTimeString) {
        this.approvalTimeString = approvalTimeString;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getExitNoBarcode() {
        return exitNoBarcode;
    }

    public void setExitNoBarcode(String exitNoBarcode) {
        this.exitNoBarcode = exitNoBarcode;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public List<String> getInvoiceNoList() {
        return invoiceNoList;
    }

    public void setInvoiceNoList(List<String> invoiceNoList) {
        this.invoiceNoList = invoiceNoList;
    }

    public List<Long> getInvoiceDateTimeList() {
        return invoiceDateTimeList;
    }

    public void setInvoiceDateTimeList(List<Long> invoiceDateTimeList) {
        this.invoiceDateTimeList = invoiceDateTimeList;
    }

    public List<WarehouseApprovalLogVO> getOrderApprovalLogs() {
        return orderApprovalLogs;
    }

    public void setOrderApprovalLogs(List<WarehouseApprovalLogVO> orderApprovalLogs) {
        this.orderApprovalLogs = orderApprovalLogs;
    }

    public List<WarehouseApprovalLogVO> getPurchaseApprovalLogs() {
        return purchaseApprovalLogs;
    }

    public void setPurchaseApprovalLogs(List<WarehouseApprovalLogVO> purchaseApprovalLogs) {
        this.purchaseApprovalLogs = purchaseApprovalLogs;
    }

    public List<ElectronicSignDataVO> getElectronicSignDataVOList() {
        return electronicSignDataVOList;
    }

    public void setElectronicSignDataVOList(List<ElectronicSignDataVO> electronicSignDataVOList) {
        this.electronicSignDataVOList = electronicSignDataVOList;
    }

    public String getExitDeptName() {
        return exitDeptName;
    }

    public void setExitDeptName(String exitDeptName) {
        this.exitDeptName = exitDeptName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OutWarehouseApplicationDetailVO{");
        sb.append("outWarehouseApplicationId=").append(outWarehouseApplicationId);
        sb.append(", outWarehouseApplicationNo='").append(outWarehouseApplicationNo).append('\'');
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", outWarehouseApplicationTime=").append(outWarehouseApplicationTime);
        sb.append(", outWarehouseApplicant='").append(outWarehouseApplicant).append('\'');
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName='").append(warehouseName).append('\'');
        sb.append(", orgName='").append(orgName).append('\'');
        sb.append(", projectCode='").append(projectCode).append('\'');
        sb.append(", projectName='").append(projectName).append('\'');
        sb.append(", funCardNo='").append(funCardNo).append('\'');
        sb.append(", supplierName='").append(supplierName).append('\'');
        sb.append(", supplierCode='").append(supplierCode).append('\'');
        sb.append(", status=").append(status);
        sb.append(", statusName='").append(statusName).append('\'');
        sb.append(", purchaserName='").append(purchaserName).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", departmentParentName='").append(departmentParentName).append('\'');
        sb.append(", totalPrice='").append(totalPrice).append('\'');
        sb.append(", totalPriceInChinese='").append(totalPriceInChinese).append('\'');
        sb.append(", sectionChief='").append(sectionChief).append('\'');
        sb.append(", acceptor='").append(acceptor).append('\'');
        sb.append(", departmentDirector='").append(departmentDirector).append('\'');
        sb.append(", outWarehouseTime=").append(outWarehouseTime);
        sb.append(", approverName='").append(approverName).append('\'');
        sb.append(", approvalTimeString='").append(approvalTimeString).append('\'');
        sb.append(", purchaserPhone='").append(purchaserPhone).append('\'');
        sb.append(", orderNoBarcode='").append(orderNoBarcode).append('\'');
        sb.append(", exitNoBarcode='").append(exitNoBarcode).append('\'');
        sb.append(", invoiceNo='").append(invoiceNo).append('\'');
        sb.append(", invoiceNoList=").append(invoiceNoList);
        sb.append(", invoiceDateTimeList=").append(invoiceDateTimeList);
        sb.append(", orderSpecies=").append(orderSpecies);
        sb.append(", warehouseProductInfoVOList=").append(warehouseProductInfoVOList);
        sb.append(", orderApprovalLogs=").append(orderApprovalLogs);
        sb.append(", purchaseApprovalLogs=").append(purchaseApprovalLogs);
        sb.append(", electronicSignDataVOList=").append(electronicSignDataVOList);
        sb.append(", exitDeptName='").append(exitDeptName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
