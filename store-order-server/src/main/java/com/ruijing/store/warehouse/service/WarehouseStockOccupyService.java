package com.ruijing.store.warehouse.service;

import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.wms.api.dto.BizWarehouseDangerousOccupyReturnGoodsDTO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2024-02-02 18:20
 * @description: 库房预占库存冻结解冻服务
 **/
public interface WarehouseStockOccupyService {

    /**
     * 退货完成后释放占用
     * @param goodsReturn 订单退货
     * @param completeReturnGoods 完成退货
     */
    void releaseAfterReturnGoodsComplete(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> completeReturnGoods);

    /**
     * 整单释放（关闭订单及提交整单无须入库）
     * @param orderId 订单id
     * @param orderNo 订单号
     */
    void releaseAll(Integer orderId, String orderNo);

    /**
     * 释放库存
     *
     * @param releaseStockParam 释放库存参数
     */
    void releaseStock(BizWarehouseDangerousOccupyReturnGoodsDTO releaseStockParam);
}
