package com.ruijing.store.warehouse.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.annotation.Async;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.warehouse.message.vo.claim.*;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseVO;
import com.ruijing.store.warehouse.service.ClaimGWService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/24 11:43
 */
@MSharpService(isGateway = "true")
@RpcApi(value = "采购人申领单网关服务",description = "采购人申领单网关服务")
@RpcMapping("/claim")
@Async(value = "inWareHouseExecutor")
public class ClaimGWController {

    @Resource
    private ClaimGWService claimGWService;

    @RpcMethod(value = "获取申领单列表(个人)")
    @RpcMapping("/getClaimList/personal")
    public RemoteResponse<ClaimPagingVO> getPersonalClaimList(ClaimPersonalPageRequestVO request) {
        ClaimPagingVO result = claimGWService.getPersonalClaimList(request);
        return RemoteResponse.<ClaimPagingVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取申领单列表(课题组)")
    @RpcMapping("/getClaimList/department")
    public RemoteResponse<ClaimPagingVO> getDeptClaimList(ClaimDeptPageRequestVO request) {
        ClaimPagingVO result = claimGWService.getDeptClaimList(request);
        return RemoteResponse.<ClaimPagingVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取申领单详情")
    @RpcMapping("/getClaimDetail")
    public RemoteResponse<ClaimDetailVO> getClaimDetail(ClaimDetailRequestVO request) {
        ClaimDetailVO result = claimGWService.getClaimDetail(request);
        return RemoteResponse.<ClaimDetailVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取申领单用到的常量信息", notes = "如用户下的课题组、审批状态枚举、申请状态枚举等")
    @RpcMapping("/getClaimConstantList")
    public RemoteResponse<ClaimConstantListVO> getClaimConstantList() {
        ClaimConstantListVO result = claimGWService.getClaimConstantList();
        return RemoteResponse.<ClaimConstantListVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "提交申领单")
    @RpcMapping("/submitClaim")
    public RemoteResponse<Boolean> submitClaim(ClaimSubmitRequestVO request) {
        boolean result = claimGWService.submitClaim(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "根据部门获取库房列表")
    @RpcMapping("/getRoomListByDepartment")
    public RemoteResponse<List<WarehouseVO>> getRoomListByDepartment(RoomListRequestVO request) {
        List<WarehouseVO> result = claimGWService.getRoomListByDepartment(request);
        return RemoteResponse.<List<WarehouseVO>>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "根据库房Id及申领方式获取商品列表")
    @RpcMapping("/getProductListByRoomAndClaimType")
    public RemoteResponse<ClaimProductPagingVO> getProductListByRoomAndClaimType(ProductListRequestVO request) {
        ClaimProductPagingVO result = claimGWService.getProductListByRoomAndClaimType(request);
        return RemoteResponse.<ClaimProductPagingVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "取消申领单")
    @RpcMapping("/cancelClaim")
    public RemoteResponse<Boolean> cancelClaim(ClaimDetailRequestVO request) {
        boolean result = claimGWService.cancelClaim(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取各状态的申领单数量(个人)")
    @RpcMapping("/getClaimCount/personal")
    public RemoteResponse<CountClaimVO> getPersonalClaimCount() {
        CountClaimVO result = claimGWService.getPersonalClaimCount();
        return RemoteResponse.<CountClaimVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取各状态的申领单数量(课题组)")
    @RpcMapping("/getClaimCount/department")
    public RemoteResponse<CountClaimVO> getDepartmentClaimCount() {
        CountClaimVO result = claimGWService.getDepartmentClaimCount();
        return RemoteResponse.<CountClaimVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "重新提交申领单")
    @RpcMapping("/reSubmitClaim")
    public RemoteResponse<Boolean> reSubmitClaim(ClaimResubmitRequestVO request) {
        boolean result = claimGWService.reSubmitClaim(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }
}
