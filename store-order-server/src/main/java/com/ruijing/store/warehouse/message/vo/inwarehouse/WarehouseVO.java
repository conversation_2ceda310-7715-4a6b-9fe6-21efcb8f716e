package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="库房信息")
public class WarehouseVO implements Serializable {

    private static final long serialVersionUID = 2357954128969105138L;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    public WarehouseVO() {
    }

    public WarehouseVO(Integer warehouseId, String warehouseName) {
        this.warehouseId = warehouseId;
        this.warehouseName = warehouseName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseVO{");
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName='").append(warehouseName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
