package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单常量列表")
public class ClaimConstantListVO implements Serializable {

    private static final long serialVersionUID = -5957750258992288403L;

    @RpcModelProperty(value = "申领单批状态列表（0审批中，1审批通过，2审批驳回）")
    private List<ClaimApprovalStatusVO> claimApprovalStatusVOList;

    @RpcModelProperty(value = "申领单状态列表（0未完成、1已完成）")
    private List<ClaimStatusVO> claimStatusVOList;

    @RpcModelProperty(value = "当前账号可选库房信息列表")
    private List<WarehouseVO> warehouseVOList;

    @RpcModelProperty(value = "当前账号的用户下的部门列表")
    private List<DepartmentVO> departmentVOList;

    @RpcModelProperty(value = "当前账号的机构是否启用危化品管理")
    private Boolean useChemicalManagementFlag;

    public List<ClaimApprovalStatusVO> getClaimApprovalStatusVOList() {
        return claimApprovalStatusVOList;
    }

    public void setClaimApprovalStatusVOList(List<ClaimApprovalStatusVO> claimApprovalStatusVOList) {
        this.claimApprovalStatusVOList = claimApprovalStatusVOList;
    }

    public List<ClaimStatusVO> getClaimStatusVOList() {
        return claimStatusVOList;
    }

    public void setClaimStatusVOList(List<ClaimStatusVO> claimStatusVOList) {
        this.claimStatusVOList = claimStatusVOList;
    }

    public List<WarehouseVO> getWarehouseVOList() {
        return warehouseVOList;
    }

    public void setWarehouseVOList(List<WarehouseVO> warehouseVOList) {
        this.warehouseVOList = warehouseVOList;
    }

    public List<DepartmentVO> getDepartmentVOList() {
        return departmentVOList;
    }

    public void setDepartmentVOList(List<DepartmentVO> departmentVOList) {
        this.departmentVOList = departmentVOList;
    }

    public Boolean getUseChemicalManagementFlag() {
        return useChemicalManagementFlag;
    }

    public void setUseChemicalManagementFlag(Boolean useChemicalManagementFlag) {
        this.useChemicalManagementFlag = useChemicalManagementFlag;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ClaimConstantListVO{");
        sb.append("claimApprovalStatusVOList=").append(claimApprovalStatusVOList);
        sb.append(", claimStatusVOList=").append(claimStatusVOList);
        sb.append(", warehouseVOList=").append(warehouseVOList);
        sb.append(", departmentVOList=").append(departmentVOList);
        sb.append(", useChemicalManagementFlag=").append(useChemicalManagementFlag);
        sb.append('}');
        return sb.toString();
    }
}
