package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

/**
 * Name: BizWarehouseEntryVO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/8/24
 */
@RpcModel("入库单VO")
public class BizWarehouseEntryVO {

    @RpcModelProperty("入库单id")
    private Integer id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
