package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 * 入库单详情查询对象
 */
@RpcModel(description="入库单详情查询对象")
public class WarehouseApplicationDetailRequestVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "入库申请单Id", example = "463")
    private Integer warehouseApplicationId;

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseApplicationDetailRequestVO{");
        sb.append("warehouseApplicationId=").append(warehouseApplicationId);
        sb.append('}');
        return sb.toString();
    }
}
