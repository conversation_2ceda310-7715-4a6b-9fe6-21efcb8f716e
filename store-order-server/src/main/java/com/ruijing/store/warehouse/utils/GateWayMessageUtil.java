package com.ruijing.store.warehouse.utils;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;

/**
 * <AUTHOR>
 * @date 2020/12/22 11:04
 * 网关信息处理工具类
 */
public class GateWayMessageUtil {
    /**
     * 从网关传过来的用户信息的key
     */
    private static final String SESSION_INFO_KEY_FROM_GATE_WAY = "RJ_SESSION_INFO";

    /**
     * 从网关获取当前登录用户信息
     * @return
     */
    public static RjSessionInfo getCurrentUser() {
        return (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(SESSION_INFO_KEY_FROM_GATE_WAY);
    }

    public static RjSessionInfo validateAndGetStoreUser() {
        RjSessionInfo currentUserInfo = getCurrentUser();
        BusinessErrUtil.notNull(currentUserInfo, ExecptionMessageEnum.USER_INFO_NOT_FOUND);
        BusinessErrUtil.notNull(currentUserInfo.getOrgId(), ExecptionMessageEnum.USER_INSTITUTION_ID_NOT_FOUND);
        return currentUserInfo;
    }
}
