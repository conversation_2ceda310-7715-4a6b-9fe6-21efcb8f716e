package com.ruijing.store.warehouse.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.goodsreturn.request.GoodsReturnBarcodeDataDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.rpc.client.BizWareHouseClient;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import com.ruijing.store.wms.api.dto.BizWarehouseDangerousOccupyReturnGoodsDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseDangerousOccupyReturnGoodsDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2024-02-02 18:39
 * @description:
 **/
@Service
public class WarehouseStockOccupyServiceImpl implements WarehouseStockOccupyService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void releaseAfterReturnGoodsComplete(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> completeReturnGoods) {
        BizWarehouseDangerousOccupyReturnGoodsDTO releaseStockParam = new BizWarehouseDangerousOccupyReturnGoodsDTO();
        releaseStockParam.setOrderNo(goodsReturn.getOrderNo());
        releaseStockParam.setReturnNo(goodsReturn.getReturnNo());
        List<BizWarehouseDangerousOccupyReturnGoodsDetailDTO> releaseDetails = completeReturnGoods.stream()
                        .map(goodsReturnInfoDetailVO -> {
                            BizWarehouseDangerousOccupyReturnGoodsDetailDTO releaseDetail = new BizWarehouseDangerousOccupyReturnGoodsDetailDTO();
                            releaseDetail.setProductId(Long.parseLong(goodsReturnInfoDetailVO.getProductId()));
                            releaseDetail.setNum(goodsReturnInfoDetailVO.getQuantity().intValue());
                            // 一物一码处理
                            if (CollectionUtils.isNotEmpty(goodsReturnInfoDetailVO.getGoodsReturnBarcodeDataDTOList())) {
                                List<String> qrCodeList = goodsReturnInfoDetailVO.getGoodsReturnBarcodeDataDTOList().stream()
                                        .map(GoodsReturnBarcodeDataDTO::getBarcode)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toList());
                                releaseDetail.setQrCodeList(qrCodeList);
                            }
                            return releaseDetail;
                        }).collect(Collectors.toList());
        releaseStockParam.setReturnGoodsDetailList(releaseDetails);
        this.releaseStock(releaseStockParam);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void releaseAll(Integer orderId, String orderNo) {
        BizWarehouseDangerousOccupyReturnGoodsDTO releaseStockParam = new BizWarehouseDangerousOccupyReturnGoodsDTO();
        releaseStockParam.setOrderNo(orderNo);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
        List<BizWarehouseDangerousOccupyReturnGoodsDetailDTO> releaseDetails = orderDetailDOList.stream()
                .map(orderDetailDO -> {
                    BizWarehouseDangerousOccupyReturnGoodsDetailDTO releaseDetail = new BizWarehouseDangerousOccupyReturnGoodsDetailDTO();
                    releaseDetail.setProductId(orderDetailDO.getProductSn());
                    releaseDetail.setNum(orderDetailDO.getFquantity().intValue());
                    return releaseDetail;
                }).collect(Collectors.toList());
        releaseStockParam.setReturnGoodsDetailList(releaseDetails);
        this.releaseStock(releaseStockParam);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public void releaseStock(BizWarehouseDangerousOccupyReturnGoodsDTO releaseStockParam){
        try{
            bizWareHouseClient.releaseStockByOrder(releaseStockParam);
        } catch (Exception e){
            String releaseNo = releaseStockParam.getReturnNo() != null ? releaseStockParam.getReturnNo() : releaseStockParam.getOrderNo();
            logger.error(releaseNo + "释放预占库存失败", e);
        }
    }
}
