package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDetailDTO;
import com.ruijing.store.wms.api.dto.StockDTO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date Created in 11:16 2020/5/14.
 * @Modified
 * @Description
 */
public class WarehouseProductTranslator {

    /**
     * 申领商品对象转store出入库专用商品对象
     * @param bizWarehouseReceiceDetailDTO
     */
    public static WarehouseProductInfoVO bizWarehouseReceiceDetailDTO2WarehouseProductInfoVO(BizWarehouseReceiceDetailDTO bizWarehouseReceiceDetailDTO) {
        WarehouseProductInfoVO warehouseProductInfoVO = new WarehouseProductInfoVO();
        warehouseProductInfoVO.setTotalQuantity(bizWarehouseReceiceDetailDTO.getMeasurementNum() == null ? null : bizWarehouseReceiceDetailDTO.getMeasurementNum().doubleValue());
        warehouseProductInfoVO.setQuantityUnit(bizWarehouseReceiceDetailDTO.getMeasurementUnit());
        warehouseProductInfoVO.setSupplierName(bizWarehouseReceiceDetailDTO.getSuppName());
        warehouseProductInfoVO.setSupplierId(bizWarehouseReceiceDetailDTO.getSuppId());
        warehouseProductInfoVO.setSpecifications(bizWarehouseReceiceDetailDTO.getSpecifications());
        warehouseProductInfoVO.setUnit(bizWarehouseReceiceDetailDTO.getReceivedUnit());
        warehouseProductInfoVO.setQuantity(bizWarehouseReceiceDetailDTO.getReceivedNum());
        warehouseProductInfoVO.setProductName(bizWarehouseReceiceDetailDTO.getProductName());
        warehouseProductInfoVO.setGoodCode(bizWarehouseReceiceDetailDTO.getProductCode());
        warehouseProductInfoVO.setCasNo(bizWarehouseReceiceDetailDTO.getCasNo());
        warehouseProductInfoVO.setBrand(bizWarehouseReceiceDetailDTO.getBrandName());
        warehouseProductInfoVO.setForm(bizWarehouseReceiceDetailDTO.getForm());
        warehouseProductInfoVO.setDangerousType(bizWarehouseReceiceDetailDTO.getDangerousType());
        warehouseProductInfoVO.setRegulatoryFlag(bizWarehouseReceiceDetailDTO.getControlFlag());
        warehouseProductInfoVO.setCategoryId(bizWarehouseReceiceDetailDTO.getCategory());
        if (bizWarehouseReceiceDetailDTO.getDangerousType() != null) {
            warehouseProductInfoVO.setDangerousTypeName(DangerousTypeEnum.get(bizWarehouseReceiceDetailDTO.getDangerousType()).getName());
        }
        return warehouseProductInfoVO;
    }

    /**
     * store出入库专用商品对象转申领商品对象
     * @param warehouseProductInfoVO
     */
    public static BizWarehouseReceiceDetailDTO warehouseProductInfoVO2BizWarehouseReceiceDetailDTO(WarehouseProductInfoVO warehouseProductInfoVO) {
        BizWarehouseReceiceDetailDTO bizWarehouseEntryDetailDTO = new BizWarehouseReceiceDetailDTO();
        bizWarehouseEntryDetailDTO.setId(warehouseProductInfoVO.getProductId() == null ? null : warehouseProductInfoVO.getProductId().intValue());
        bizWarehouseEntryDetailDTO.setProductCode(warehouseProductInfoVO.getGoodCode());
        bizWarehouseEntryDetailDTO.setBrandName(warehouseProductInfoVO.getBrand());
        bizWarehouseEntryDetailDTO.setCasNo(warehouseProductInfoVO.getCasNo());
        bizWarehouseEntryDetailDTO.setDangerousType(warehouseProductInfoVO.getDangerousType());
        bizWarehouseEntryDetailDTO.setProductName(warehouseProductInfoVO.getProductName());
        bizWarehouseEntryDetailDTO.setSpecifications(warehouseProductInfoVO.getSpecifications());
        bizWarehouseEntryDetailDTO.setSuppId(warehouseProductInfoVO.getSupplierId());
        bizWarehouseEntryDetailDTO.setSuppName(warehouseProductInfoVO.getSupplierName());
        bizWarehouseEntryDetailDTO.setMeasurementNum(warehouseProductInfoVO.getTotalQuantity() == null ? null : new BigDecimal(warehouseProductInfoVO.getTotalQuantity() + ""));
        bizWarehouseEntryDetailDTO.setMeasurementUnit(warehouseProductInfoVO.getQuantityUnit());
        bizWarehouseEntryDetailDTO.setReceivedNum(warehouseProductInfoVO.getQuantity());
        bizWarehouseEntryDetailDTO.setReceivedUnit(warehouseProductInfoVO.getUnit());
        bizWarehouseEntryDetailDTO.setForm(warehouseProductInfoVO.getForm());
        bizWarehouseEntryDetailDTO.setCategory(warehouseProductInfoVO.getCategoryId() );
        bizWarehouseEntryDetailDTO.setControlFlag(warehouseProductInfoVO.getRegulatoryFlag());
        bizWarehouseEntryDetailDTO.setProductPlatformCode(warehouseProductInfoVO.getProductCode());
        return bizWarehouseEntryDetailDTO;
    }
    
    public static WarehouseProductInfoVO stockDto2ProductInfoVo(StockDTO stockDTO){
        WarehouseProductInfoVO warehouseProductInfoVO = new WarehouseProductInfoVO();
        warehouseProductInfoVO.setTotalQuantity(stockDTO.getMeasurementNum() == null ? null : stockDTO.getMeasurementNum().doubleValue());
        warehouseProductInfoVO.setQuantityUnit(stockDTO.getMeasurementUnit());
        warehouseProductInfoVO.setSupplierName(stockDTO.getSuppName());
        warehouseProductInfoVO.setSupplierId(stockDTO.getSuppId());
        warehouseProductInfoVO.setSpecifications(stockDTO.getSpecifications());
        warehouseProductInfoVO.setUnit(stockDTO.getStockUnit());
        warehouseProductInfoVO.setQuantity(stockDTO.getStockNum());
        warehouseProductInfoVO.setProductName(stockDTO.getProductName());
        warehouseProductInfoVO.setGoodCode(stockDTO.getProductCode());
        warehouseProductInfoVO.setCasNo(stockDTO.getCasNo());
        warehouseProductInfoVO.setBrand(stockDTO.getBrandName());
        warehouseProductInfoVO.setForm(stockDTO.getForm());
        warehouseProductInfoVO.setDangerousType(stockDTO.getDangerousType());
        warehouseProductInfoVO.setProductPhoto(stockDTO.getFpicpath());
        warehouseProductInfoVO.setRegulatoryFlag(stockDTO.getControlFlag());
        if (stockDTO.getDangerousType() != null) {
            warehouseProductInfoVO.setDangerousTypeName(DangerousTypeEnum.get(stockDTO.getDangerousType()).getName());
        }
        //判断是否是危化品
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(stockDTO.getDangerousType());
        warehouseProductInfoVO.setDangerousFlag(isDangerousType ? 1 : 0);
        return warehouseProductInfoVO;
    }
}
