package com.ruijing.store.warehouse.message.bean;

import com.ruijing.store.order.api.base.enums.OrderTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/31 9:32
 * 订单基本信息，用作存储、复用库房的订单相关信息
 */
public class OrderBean {
    /**
     * 订单Id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 订单总金额
     */
    private Double totalPrice;

    /**
     * 采购人姓名
     */
    private String purchaserName;

    /**
     * 采购人联系方式
     */
    private String purchaserPhone;

    /**
     * 采购部门名称
     */
    private String departmentName;

    /**
     * 采购部门上一级部门名称
     */
    private String departmentParentName;

    /**
     * 订单验收人
     */
    private String acceptor;

    /**
     * 该订单选择的经费卡所属的项目编码,多个的情况,用分隔符隔开
     */
    private String projectCode;

    /**
     * 订单选择的经费卡所属的项目名称,多个的情况,用分隔符隔开
     */
    private String projectName;

    /**
     * 订单选择的经费卡号,多个的情况,用分隔符隔开
     */
    private String funCardNo;

    /**
     * 科长(对宁波二院：入库单对应的订单对应的采购申请单或竞价单的二级审批人姓名）
     */
    private String sectionChief;

    /**
     * 课题组负责人（订单的采购人所属的课题组的负责人姓名）
     */
    private String departmentDirector;

    /**
     * 发票单号，多个的话用逗号隔开
     */
    private String invoiceNo;

    /**
     * 发票号列表，单位个性化适应
     */
    private List<String> invoiceNoList;

    /**
     * 开票时间戳列表
     */
    private List<Long> invoiceDateTimeList;

    /**
     * 订单号对应条形码(base64编码后的字符串)
     */
    private String orderNoBarcode;

    /**
     * 流程种类 0:正常, 1:线下
     */
    private Integer species;

    /**
     * 订单收货图片
     */
    private List<String> receivingPhotos;

    /**
     * 当前订单的课题组可选库房列表
     */
    private List<WarehouseBean> warehousesCanBeChosenByDepartment;

    /**
     * 订单类型（采购竞价临床)
     */
    private OrderTypeEnum orderType;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<String> getReceivingPhotos() {
        return receivingPhotos;
    }

    public void setReceivingPhotos(List<String> receivingPhotos) {
        this.receivingPhotos = receivingPhotos;
    }

    public List<WarehouseBean> getWarehousesCanBeChosenByDepartment() {
        return warehousesCanBeChosenByDepartment;
    }

    public void setWarehousesCanBeChosenByDepartment(List<WarehouseBean> warehousesCanBeChosenByDepartment) {
        this.warehousesCanBeChosenByDepartment = warehousesCanBeChosenByDepartment;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSectionChief() {
        return sectionChief;
    }

    public void setSectionChief(String sectionChief) {
        this.sectionChief = sectionChief;
    }

    public String getFunCardNo() {
        return funCardNo;
    }

    public void setFunCardNo(String funCardNo) {
        this.funCardNo = funCardNo;
    }

    public String getDepartmentDirector() {
        return departmentDirector;
    }

    public void setDepartmentDirector(String departmentDirector) {
        this.departmentDirector = departmentDirector;
    }

    public String getPurchaserPhone() {
        return purchaserPhone;
    }

    public void setPurchaserPhone(String purchaserPhone) {
        this.purchaserPhone = purchaserPhone;
    }

    public String getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(String acceptor) {
        this.acceptor = acceptor;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getOrderNoBarcode() {
        return orderNoBarcode;
    }

    public void setOrderNoBarcode(String orderNoBarcode) {
        this.orderNoBarcode = orderNoBarcode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public List<Long> getInvoiceDateTimeList() {
        return invoiceDateTimeList;
    }

    public void setInvoiceDateTimeList(List<Long> invoiceDateTimeList) {
        this.invoiceDateTimeList = invoiceDateTimeList;
    }

    public List<String> getInvoiceNoList() {
        return invoiceNoList;
    }

    public void setInvoiceNoList(List<String> invoiceNoList) {
        this.invoiceNoList = invoiceNoList;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderBean{");
        sb.append("orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orgName='").append(orgName).append('\'');
        sb.append(", supplierName='").append(supplierName).append('\'');
        sb.append(", supplierCode='").append(supplierCode).append('\'');
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", purchaserName='").append(purchaserName).append('\'');
        sb.append(", purchaserPhone='").append(purchaserPhone).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", departmentParentName='").append(departmentParentName).append('\'');
        sb.append(", acceptor='").append(acceptor).append('\'');
        sb.append(", projectCode='").append(projectCode).append('\'');
        sb.append(", projectName='").append(projectName).append('\'');
        sb.append(", funCardNo='").append(funCardNo).append('\'');
        sb.append(", sectionChief='").append(sectionChief).append('\'');
        sb.append(", departmentDirector='").append(departmentDirector).append('\'');
        sb.append(", invoiceNo='").append(invoiceNo).append('\'');
        sb.append(", invoiceNoList=").append(invoiceNoList);
        sb.append(", invoiceDateTimeList=").append(invoiceDateTimeList);
        sb.append(", orderNoBarcode='").append(orderNoBarcode).append('\'');
        sb.append(", species=").append(species);
        sb.append(", receivingPhotos=").append(receivingPhotos);
        sb.append(", warehousesCanBeChosenByDepartment=").append(warehousesCanBeChosenByDepartment);
        sb.append(", orderType=").append(orderType);
        sb.append('}');
        return sb.toString();
    }
}
