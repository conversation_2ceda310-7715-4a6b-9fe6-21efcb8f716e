package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
public class InAndOutWarehouseApplicationDetailVO implements Serializable {

    private static final long serialVersionUID = 5343544478072982718L;

    List<WarehouseApplicationDetailVO> warehouseApplicationVOList;

    List<OutWarehouseApplicationDetailVO> outWarehouseApplicationVoList;

    public List<WarehouseApplicationDetailVO> getWarehouseApplicationVOList() {
        return warehouseApplicationVOList;
    }

    public void setWarehouseApplicationVOList(List<WarehouseApplicationDetailVO> warehouseApplicationVOList) {
        this.warehouseApplicationVOList = warehouseApplicationVOList;
    }

    public List<OutWarehouseApplicationDetailVO> getOutWarehouseApplicationVoList() {
        return outWarehouseApplicationVoList;
    }

    public void setOutWarehouseApplicationVoList(List<OutWarehouseApplicationDetailVO> outWarehouseApplicationVoList) {
        this.outWarehouseApplicationVoList = outWarehouseApplicationVoList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InAndOutWarehouseApplicationDetailVO{");
        sb.append("warehouseApplicationVOList=").append(warehouseApplicationVOList);
        sb.append(", outWarehouseApplicationVoList=").append(outWarehouseApplicationVoList);
        sb.append('}');
        return sb.toString();
    }
}
