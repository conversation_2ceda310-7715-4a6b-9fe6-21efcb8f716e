package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 * 课题组入库申请单列表查询对象
 */
@RpcModel(description="课题组入库申请单列表查询对象")
public class WarehouseApplicationPersonalPageRequestVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "入库申请单号", example = "RK280665006482132997")
    private String warehouseApplicationNo;

    @RpcModelProperty(value = "订单号", example = "DC201911215296401")
    private String orderNo;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）", example = "0")
    private Integer approvalStatus;

    @RpcModelProperty(value = "入库状态（0：未入库，1：已入库 ）", example = "0")
    private Integer status;

    @RpcModelProperty("推送状态")
    private Integer pushStatus;

    @RpcModelProperty(value = "库房Id，pc端用", example = "94")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房Id列表，微信端用", example = "[94,95,96]")
    private List<Integer> warehouseIdList;

    @RpcModelProperty(value = "申请时间下限", example = "1580486400000")
    private Long applicationLowerTime;

    @RpcModelProperty(value = "申请时间上限", example = "1581868799000")
    private Long applicationUpperTime;

    @RpcModelProperty(value = "当前页，>=1", example = "1")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小", example = "10")
    private Integer pageSize;

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPushStatus() {
        return pushStatus;
    }

    public WarehouseApplicationPersonalPageRequestVO setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
        return this;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getApplicationLowerTime() {
        return applicationLowerTime;
    }

    public void setApplicationLowerTime(Long applicationLowerTime) {
        this.applicationLowerTime = applicationLowerTime;
    }

    public Long getApplicationUpperTime() {
        return applicationUpperTime;
    }

    public void setApplicationUpperTime(Long applicationUpperTime) {
        this.applicationUpperTime = applicationUpperTime;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Integer> getWarehouseIdList() {
        return warehouseIdList;
    }

    public void setWarehouseIdList(List<Integer> warehouseIdList) {
        this.warehouseIdList = warehouseIdList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseApplicationPersonalPageRequestVO.class.getSimpleName() + "[", "]")
                .add("warehouseApplicationNo='" + warehouseApplicationNo + "'")
                .add("orderNo='" + orderNo + "'")
                .add("approvalStatus=" + approvalStatus)
                .add("status=" + status)
                .add("pushStatus=" + pushStatus)
                .add("warehouseId=" + warehouseId)
                .add("warehouseIdList=" + warehouseIdList)
                .add("applicationLowerTime=" + applicationLowerTime)
                .add("applicationUpperTime=" + applicationUpperTime)
                .add("currentPage=" + currentPage)
                .add("pageSize=" + pageSize)
                .toString();
    }
}
