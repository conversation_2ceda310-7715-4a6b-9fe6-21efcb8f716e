package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 入库所需发票信息
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-01-13 14:10
 */
@RpcModel("发票信息")
public class InvoiceInfoVO implements Serializable {

    private static final long serialVersionUID = 2296587108537244280L;

    @RpcModelProperty("发票id")
    private Integer id;

    @RpcModelProperty("发票号")
    private String invoiceNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    @Override
    public String toString() {
        return "InvoiceInfoVO{" +
                "id=" + id +
                ", invoiceNo='" + invoiceNo + '\'' +
                '}';
    }
}
