package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 批量提交入库入参
 * @author: zhong<PERSON><PERSON><PERSON>
 * @create: 2022-01-13 11:50
 */
@RpcModel("批量提交入库入参")
public class WarehouseSubmitBatchRequestVO implements Serializable {

    private static final long serialVersionUID = 5494374548642109584L;

    @RpcModelProperty("订单明细入参")
    List<WarehouseSubmitApplicationRequestVO> data;

    public List<WarehouseSubmitApplicationRequestVO> getData() {
        return data;
    }

    public void setData(List<WarehouseSubmitApplicationRequestVO> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "WarehouseSubmitBatchRequestVO{" +
                "data=" + data +
                '}';
    }
}
