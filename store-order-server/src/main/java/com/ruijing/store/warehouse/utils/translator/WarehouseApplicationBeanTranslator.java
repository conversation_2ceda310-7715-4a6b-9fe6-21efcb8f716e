package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.warehouse.message.bean.ApprovalProgressBean;
import com.ruijing.store.warehouse.message.bean.WarehouseApplicationBean;
import com.ruijing.store.warehouse.message.bean.WarehouseBean;
import com.ruijing.store.warehouse.message.bean.WarehouseOperationLogBean;
import com.ruijing.store.warehouse.message.vo.inwarehouse.ApprovalProgressVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseOperationLogVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseVO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryLogDTO;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.InboundStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/5 11:00
 */
public class WarehouseApplicationBeanTranslator {

    /**
     * 入库申请单图片分割符
     */
    private static final String WAREHOUSE_APPLICATION_PHOTO_SEPARATOR = ";";

    public static WarehouseApplicationBean bizWarehouseEntryDTO2WarehouseApplicationBean(BizWarehouseEntryDTO from) {
        WarehouseApplicationBean to = new WarehouseApplicationBean();
        to.setApprovalStatus(from.getApprovalStatus());
        to.setApprovalStatusName(from.getApprovalStatus() == null ? null : ApprovalTaskStatusEnum.valueOf(from.getApprovalStatus()).getDesc());
        to.setWarehouseName(from.getRoomName());
        to.setWarehouseId(from.getRoomId());
        to.setWarehouseApplicationDate(from.getCreateTime());
        to.setWarehouseApplicationNo(from.getEntryNo());
        to.setWarehouseApplicationId(from.getId());
        to.setWarehouseApplicant(from.getApplyUserName());
        to.setStatus(from.getStatus());
        to.setStatusName(from.getStatus() == null ? null : InboundStatus.valueOf(from.getStatus()).getDesc());
        to.setRemark(from.getRemark());
        to.setOrderNo(from.getOrderNo());
        to.setBusinessType(from.getBusinessType());
        to.setRecommit(from.isRecommit());
        to.setDocCreator(from.getDocCreator());
        to.setPickingDept(from.getPickingDept());
        to.setReceiveWarehouse(from.getReceiveWarehouse());
        to.setFirstReverter(from.getFirstReverter());
        to.setSecondReverter(from.getSecondReverter());
        if (StringUtils.isNotBlank(from.getEntryTime())) {
            to.setInWarehouseDate(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, from.getEntryTime()));
        }
        to.setDepartmentName(from.getDeptName());
        if (StringUtils.isNotBlank(from.getReceivePicUrls())) {
            to.setInWarehousePictureUrlList(Arrays.asList(from.getReceivePicUrls().split(WAREHOUSE_APPLICATION_PHOTO_SEPARATOR)));
        }
        if (CollectionUtils.isNotEmpty(from.getLogDTOList())) {
            populateEntryLog(to, from.getLogDTOList());
        }
        return to;
    }

    public static void populateEntryLog(WarehouseApplicationBean receiver, List<BizWarehouseEntryLogDTO> provider) {
        List<WarehouseOperationLogBean> operationLogBeans = new ArrayList<>();
        for (BizWarehouseEntryLogDTO warehouseEntryLogDTO : provider) {
            WarehouseOperationLogBean warehouseOperationLogBean = new WarehouseOperationLogBean();
            warehouseOperationLogBean.setOperator(warehouseEntryLogDTO.getUserName());
            warehouseOperationLogBean.setApprovalOpinion(warehouseEntryLogDTO.getRemark());
            warehouseOperationLogBean.setComment(warehouseEntryLogDTO.getRemark());
            warehouseOperationLogBean.setOperation(warehouseEntryLogDTO.getBusinessDesc());
            warehouseOperationLogBean.setOperationDate(warehouseEntryLogDTO.getCreateTime());
            warehouseOperationLogBean.setSign(warehouseEntryLogDTO.getSign());
            warehouseOperationLogBean.setSignPhoto(warehouseEntryLogDTO.getSignPhoto());
            operationLogBeans.add(warehouseOperationLogBean);
        }
        receiver.setOperationLogBeans(operationLogBeans);
    }

    public static WarehouseVO warehouseBean2WarehouseVO(WarehouseBean from) {
        if (from == null) {
            return null;
        }
        return new WarehouseVO(from.getWarehouseId(), from.getWarehouseName());
    }

    public static List<WarehouseVO> warehouseBeanList2WarehouseVOList(List<WarehouseBean> from) {
        if (CollectionUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        return from.stream().map(WarehouseApplicationBeanTranslator::warehouseBean2WarehouseVO).collect(Collectors.toList());
    }

    public static WarehouseOperationLogVO warehouseOperationLogBean2WarehouseOperationLogVO(WarehouseOperationLogBean from) {
        if (from == null) {
            return null;
        }
        WarehouseOperationLogVO to = new WarehouseOperationLogVO();
        to.setOperator(from.getOperator());
        to.setApprovalOpinion(from.getApprovalOpinion());
        to.setComment(from.getComment());
        to.setOperation(from.getOperation());
        to.setSign(from.getSign());
        to.setSignPhoto(from.getSignPhoto());
        if (from.getOperationDate() != null) {
            to.setOperationTime(from.getOperationDate().getTime());
        }
        return to;
    }

    public static List<WarehouseOperationLogVO> warehouseOperationLogBeanList2WarehouseOperationLogVOList(List<WarehouseOperationLogBean> from) {
        if (CollectionUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        return from.stream().map(WarehouseApplicationBeanTranslator::warehouseOperationLogBean2WarehouseOperationLogVO).collect(Collectors.toList());
    }

    public static ApprovalProgressVO approvalProgressBean2ApprovalProgressVO(ApprovalProgressBean from) {
        if (from == null) {
            return null;
        }
        ApprovalProgressVO to = new ApprovalProgressVO();
        to.setStatus(from.getStatus());
        to.setApprovalProgressName(from.getApprovalProgressName());
        to.setOperatorList(from.getOperatorList());
        to.setOperator(from.getOperator());
        to.setOptTime(from.getOptTime());
        to.setAutoExecute(from.getAutoExecute());
        return to;
    }

    public static List<ApprovalProgressVO> approvalProgressBeanList2ApprovalProgressVOList(List<ApprovalProgressBean> from) {
        if (CollectionUtils.isEmpty(from)) {
            return Collections.emptyList();
        }
        return from.stream().map(WarehouseApplicationBeanTranslator::approvalProgressBean2ApprovalProgressVO).collect(Collectors.toList());
    }
}
