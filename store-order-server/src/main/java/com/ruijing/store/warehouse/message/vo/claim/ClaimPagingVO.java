package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单分页查询返回信息")
public class ClaimPagingVO implements Serializable {

    private static final long serialVersionUID = -8836981416712490371L;

    @RpcModelProperty(value = "申领单列表")
    private List<ClaimSimpleVO> claimList;

    @RpcModelProperty(value = "总条数")
    private Integer total;

    @RpcModelProperty(value = "总页数")
    private Integer totalPage;

    @RpcModelProperty(value = "当前页")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小")
    private Integer pageSize;

    public List<ClaimSimpleVO> getClaimList() {
        return claimList;
    }

    public void setClaimList(List<ClaimSimpleVO> claimList) {
        this.claimList = claimList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ClaimPagingVO{");
        sb.append("claimList=").append(claimList);
        sb.append(", total=").append(total);
        sb.append(", totalPage=").append(totalPage);
        sb.append(", currentPage=").append(currentPage);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
