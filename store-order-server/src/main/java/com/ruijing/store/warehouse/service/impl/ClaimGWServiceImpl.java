package com.ruijing.store.warehouse.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.constant.PrintConfigConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.message.vo.claim.*;
import com.ruijing.store.warehouse.message.vo.inwarehouse.ApprovalProgressVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseVO;
import com.ruijing.store.warehouse.service.ClaimGWService;
import com.ruijing.store.warehouse.utils.CategoryUtil;
import com.ruijing.store.warehouse.utils.GateWayMessageUtil;
import com.ruijing.store.warehouse.utils.translator.WarehouseProductTranslator;
import com.ruijing.store.wms.api.dto.*;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.ReceiceStatus;
import com.ruijing.store.wms.api.enums.RoomProEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2020/12/28 13:02
 */
@Service
public class ClaimGWServiceImpl implements ClaimGWService {

    /**
     * 默认当前页
     */
    private static final int DEFAULT_CURRENT_PAGE = 1;

    /**
     * 默认页面大小
     */
    private static final int DEFAULT_PAGE_SIZE = 10;

    @Resource
    private ClaimServiceClient claimServiceClient;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private BizWarehouseRoomServiceClient bizWarehouseRoomServiceClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private UserClient userClient;

    @Resource
    private UserDepartmentRoleRpcServiceClient userDepartmentRoleRpcServiceClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Override
    public ClaimPagingVO getPersonalClaimList(ClaimPersonalPageRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        RjSessionInfo user = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(user.getUserId(RjUserTypeEnum.STORE_USER).intValue(), user.getOrgId());

        BizWarehouseReceiceDTO dtoToFindClaimList = this.claimPersonalPageRequestVO2BizWarehouseReceiceDTO(request);
        //额外个人和机构信息
        dtoToFindClaimList.setApplyUserGuid(user.getGuid());
        dtoToFindClaimList.setOrgId(user.getOrgId(RjUserTypeEnum.STORE_USER));

        PageApiResult<List<BizWarehouseReceiceDTO>> claimDTOPageResult = claimServiceClient.queryClaimPage(dtoToFindClaimList);
        Preconditions.notNull(claimDTOPageResult, "RPC查询申领单列表失败");
        ClaimPagingVO claimPagingVO = this.claimPagingDTO2ClaimPagingVO(claimDTOPageResult);
        this.setShowPrintButton(claimPagingVO.getClaimList(), user.getOrgId(RjUserTypeEnum.STORE_USER));
        return claimPagingVO;
    }

    @Override
    public ClaimPagingVO getDeptClaimList(ClaimDeptPageRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        RjSessionInfo user = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(user.getUserId(RjUserTypeEnum.STORE_USER).intValue(), user.getOrgId(RjUserTypeEnum.STORE_USER));

        BizWarehouseReceiceDTO dtoToFindClaimList = this.claimDeptPageRequestVO2BizWarehouseReceiceDTO(request);
        //额外机构信息
        dtoToFindClaimList.setOrgId(user.getOrgId(RjUserTypeEnum.STORE_USER));
        //部门限制
        if (CollectionUtils.isEmpty(dtoToFindClaimList.getDeptIdList())) {
            List<MiniDepartmentDTO> departmentsForUser = departmentRpcClient.getDeptTreeForUserByAccess(user.getUserId(RjUserTypeEnum.STORE_USER).intValue(), user.getOrgId(RjUserTypeEnum.STORE_USER), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
            BusinessErrUtil.notEmpty(departmentsForUser, ExecptionMessageEnum.USER_NOT_BOUND_TO_GROUP);
            dtoToFindClaimList.setDeptIdList(departmentsForUser.stream().map(MiniDepartmentDTO::getId).collect(Collectors.toList()));
        }

        PageApiResult<List<BizWarehouseReceiceDTO>> claimDTOPageResult = claimServiceClient.queryClaimPage(dtoToFindClaimList);
        Preconditions.notNull(claimDTOPageResult, "RPC查询申领单列表失败");
        ClaimPagingVO claimPagingVO = this.claimPagingDTO2ClaimPagingVO(claimDTOPageResult);
        this.setShowPrintButton(claimPagingVO.getClaimList(), user.getOrgId(RjUserTypeEnum.STORE_USER));
        return claimPagingVO;
    }

    @Override
    public ClaimDetailVO getClaimDetail(ClaimDetailRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getClaimId(), "申领单的Id不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        String orgCode = organizationClient.findSimpleOrgDTOById(sessionUser.getOrgId(RjUserTypeEnum.STORE_USER)).getCode();
        //查询申领单详情
        BizWarehouseReceiceDTO bizWarehouseReceiceDTO = claimServiceClient.queryClaimDetailById(request.getClaimId());
        BusinessErrUtil.notNull(bizWarehouseReceiceDTO, ExecptionMessageEnum.APPLICATION_FORM_ID_NOT_FOUND, request.getClaimId());
        //查询申领单操作日志列表
        List<ApprovalProgressDTO> approvalProgressDTOList = claimServiceClient.queryApprovalProgressById(request.getClaimId());

        ClaimDetailVO receiver = new ClaimDetailVO();
        populateClaimDetail(receiver, bizWarehouseReceiceDTO, orgCode);
        if (CollectionUtils.isNotEmpty(approvalProgressDTOList)) {
            populateClaimApprovalProgressList(receiver, approvalProgressDTOList);
        }
        return receiver;
    }

    @Override
    public ClaimConstantListVO getClaimConstantList() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        ClaimConstantListVO result = new ClaimConstantListVO();

        //获取是否启用危化品管理
        boolean isUseChemicalManagement = bizWareHouseClient.chemicalManagement(sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        result.setUseChemicalManagementFlag(isUseChemicalManagement);

        //获取并封装申领单审批状态列表
        populateApprovalStatusList(result, Arrays.asList(ApprovalTaskStatusEnum.values()));

        //获取并封装申领单状态列表
        populateClaimStatusList(result, Arrays.asList(ReceiceStatus.values()));

        List<MiniDepartmentDTO> departmentDTOList = departmentRpcClient.getDeptTreeForUserByAccess(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        if (CollectionUtils.isNotEmpty(departmentDTOList)) {
            populateDepartmentList(result, departmentDTOList);
            populateWarehouseList(result, departmentDTOList);
        }
        return result;
    }

    @Override
    @ServiceLog(description = "申领单提交", serviceType = ServiceType.COMMON_SERVICE)
    public boolean submitClaim(ClaimSubmitRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.isTrue(request.getDepartmentId() != null && request.getRoomId() != null && request.getClaimType() != null && CollectionUtils.isNotEmpty(request.getWarehouseProductInfoVOList()), "入参不完整，部门Id、库房Id、申领方式、商品列表不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), request.getDepartmentId(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        BizWarehouseReceiceDTO claimToSave = new BizWarehouseReceiceDTO();
        //保存前端传来的基本信息
        populateBaseSubmitClaim(claimToSave, request);
        //设置当前用户会话信息
        populateSessionUser(claimToSave, sessionUser);
        //设置当前用户基本信息
        populateBaseUser(claimToSave, baseUser);
        // 设置申领人信息
        populateClaimUser(claimToSave, request.getClaimUserVO());
        //设置固定值
        claimToSave.setApprovalStatus(ApprovalTaskStatusEnum.APPROVALING.getValue());
        claimToSave.setStatus(ReceiceStatus.DONE.getValue());
        //保存商品信息
        List<BizWarehouseReceiceDetailDTO> bizWarehouseProductListToSave = new ArrayList<>();
        for (WarehouseProductInfoVO warehouseProductInfoVO : request.getWarehouseProductInfoVOList()) {
            BizWarehouseReceiceDetailDTO bizWarehouseEntryDetailDTO = WarehouseProductTranslator.warehouseProductInfoVO2BizWarehouseReceiceDetailDTO(warehouseProductInfoVO);
            bizWarehouseProductListToSave.add(bizWarehouseEntryDetailDTO);
        }
        claimToSave.setBizWarehouseReceiceDetailDTOList(bizWarehouseProductListToSave);
        return claimServiceClient.createClaim(claimToSave);
    }

    @Override
    public List<WarehouseVO> getRoomListByDepartment(RoomListRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getDepartmentId(), "部门Id不能为空");
        List<BizWarehouseRoomDTO> warehouseRoomDTOList = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Collections.singletonList(request.getDepartmentId()));
        if (CollectionUtils.isEmpty(warehouseRoomDTOList)) {
            return Collections.emptyList();
        }
        return warehouseRoomDTOList.stream().filter(item-> !RoomProEnum.TRANSIT.getValue().equals(item.getRoomPro()))
                .map(o -> new WarehouseVO(o.getId(), o.getRoomName())).collect(Collectors.toList());
    }

    @Override
    public ClaimProductPagingVO getProductListByRoomAndClaimType(ProductListRequestVO request) {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        Preconditions.isTrue(request != null && request.getRoomId() != null && request.getClaimType() != null, "库房Id和申领方式不能为空");

        BizWarehouseReceiceDTO productQuery = this.productListRequestVO2BizWarehouseReceiceDTO(request);
        productQuery.setOrgId(sessionUser.getOrgId());
        PageApiResult<List<BizWarehouseReceiceDetailDTO>> productPage = claimServiceClient.queryStockPage(productQuery);
        return claimProductPagingDTO2ClaimProductPagingVO(productPage);
    }

    @Override
    public boolean cancelClaim(ClaimDetailRequestVO request) {
        Preconditions.isTrue(request != null && request.getClaimId() != null, "申领单Id不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        return claimServiceClient.cancelClaim(request.getClaimId(), baseUser.getName(), baseUser.getGuid());
    }

    @Override
    public CountClaimVO getPersonalClaimCount() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        BizWarehouseReceiceDTO receiceDTO = new BizWarehouseReceiceDTO();
        receiceDTO.setApplyUserGuid(sessionUser.getGuid());
        receiceDTO.setOrgId(sessionUser.getOrgId());
        CountDTO countDTO = claimServiceClient.getCount(receiceDTO);
        BusinessErrUtil.notNull(countDTO, ExecptionMessageEnum.APPLICATION_FORM_STATUS_NOT_FOUND);
        return this.countDTO2CountClaimVO(countDTO);
    }

    @Override
    public CountClaimVO getDepartmentClaimCount() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        BizWarehouseReceiceDTO receiceDTO = new BizWarehouseReceiceDTO();
        receiceDTO.setOrgId(sessionUser.getOrgId());
        //部门限制
        List<MiniDepartmentDTO> departmentsForUser = departmentRpcClient.getDeptTreeForUserByAccess(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        BusinessErrUtil.notEmpty(departmentsForUser, ExecptionMessageEnum.USER_NOT_BOUND_TO_GROUP_FAILED);
        receiceDTO.setDeptIdList(departmentsForUser.stream().map(MiniDepartmentDTO::getId).collect(Collectors.toList()));
        CountDTO countDTO = claimServiceClient.getCount(receiceDTO);
        BusinessErrUtil.notNull(countDTO, ExecptionMessageEnum.APPLICATION_FORM_STATUS_NOT_FOUND);
        return this.countDTO2CountClaimVO(countDTO);
    }

    @Override
    @ServiceLog(description = "申领单重新提交", serviceType = ServiceType.COMMON_SERVICE)
    public boolean reSubmitClaim(ClaimResubmitRequestVO request) {
        Preconditions.isTrue(request != null && StringUtils.isNotBlank(request.getClaimNo()) && request.getDepartmentId() != null && request.getRoomId() != null && request.getClaimType() != null && CollectionUtils.isNotEmpty(request.getWarehouseProductInfoVOList()), "入参不完整，申领单号、部门Id、库房Id、申领方式、商品列表不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), request.getDepartmentId(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        //要保存的申领单
        BizWarehouseReceiceDTO bizWarehouseReceiceToSave = new BizWarehouseReceiceDTO();
        populateBaseReSubmitClaim(bizWarehouseReceiceToSave, request);
        populateBaseUser(bizWarehouseReceiceToSave, baseUser);
        populateSessionUser(bizWarehouseReceiceToSave, sessionUser);
        populateClaimUser(bizWarehouseReceiceToSave,request.getClaimUserVO());
        //设置固定值
        bizWarehouseReceiceToSave.setApprovalStatus(ApprovalTaskStatusEnum.APPROVALING.getValue());
        bizWarehouseReceiceToSave.setStatus(ReceiceStatus.DONE.getValue());
        //保存商品信息
        List<BizWarehouseReceiceDetailDTO> bizWarehouseProductListToSave = new ArrayList<>();
        for (WarehouseProductInfoVO warehouseProductInfoVO : request.getWarehouseProductInfoVOList()) {
            BizWarehouseReceiceDetailDTO bizWarehouseEntryDetailDTO = WarehouseProductTranslator.warehouseProductInfoVO2BizWarehouseReceiceDetailDTO(warehouseProductInfoVO);
            bizWarehouseProductListToSave.add(bizWarehouseEntryDetailDTO);
        }
        bizWarehouseReceiceToSave.setBizWarehouseReceiceDetailDTOList(bizWarehouseProductListToSave);
        return claimServiceClient.createClaim(bizWarehouseReceiceToSave);
    }

    private void populateBaseReSubmitClaim(BizWarehouseReceiceDTO receiver, ClaimResubmitRequestVO provider) {
        receiver.setRoomId(provider.getRoomId());
        receiver.setRoomName(provider.getRoomName());
        receiver.setDeptId(provider.getDepartmentId());
        receiver.setDeptName(provider.getDepartmentName());
        receiver.setReceiceType(provider.getClaimType());
        receiver.setReceiceNo(provider.getClaimNo());
        receiver.setRemark(provider.getRemark());
    }

    private BizWarehouseReceiceDTO productListRequestVO2BizWarehouseReceiceDTO(ProductListRequestVO from) {
        BizWarehouseReceiceDTO to = new BizWarehouseReceiceDTO();
        to.setRoomId(from.getRoomId());
        to.setReceiceType(from.getClaimType());
        to.setPage(from.getCurrentPage() == null ? DEFAULT_CURRENT_PAGE : from.getCurrentPage());
        to.setSize(from.getPageSize() == null ? DEFAULT_PAGE_SIZE : from.getPageSize());
        BizWarehouseReceiceDetailDTO detailQuery = new BizWarehouseReceiceDetailDTO();
        detailQuery.setProductName(StringUtils.isEmpty(from.getProductName()) ? null : from.getProductName());
        detailQuery.setProductCode(StringUtils.isEmpty(from.getGoodCode()) ? null : from.getGoodCode());
        detailQuery.setCasNo(StringUtils.isEmpty(from.getCasNo()) ? null : from.getCasNo());
        to.setBizWarehouseReceiceDetailDTOList(Collections.singletonList(detailQuery));
        return to;
    }

    private ClaimProductPagingVO claimProductPagingDTO2ClaimProductPagingVO(PageApiResult<List<BizWarehouseReceiceDetailDTO>> from) {
        //封装申领单分页VO
        ClaimProductPagingVO result = new ClaimProductPagingVO();
        int total = (int) from.getTotal();
        int pageSize = from.getSize();
        int currentPage = from.getCurrent();
        int totalPage = (total + pageSize - 1) / pageSize;
        //判空封装申领单列表
        if (CollectionUtils.isEmpty(from.getData())) {
            result.setClaimProductList(Collections.emptyList());
        } else {
            List<WarehouseProductInfoVO> resultList = new ArrayList<>();
            for (BizWarehouseReceiceDetailDTO claimProductDTO : from.getData()) {
                WarehouseProductInfoVO productVO = bizWarehouseReceiceDetailDTO2WarehouseProductInfoVO(claimProductDTO);
                productVO.setProductId(claimProductDTO.getId() == null ? null : claimProductDTO.getId().longValue());
                resultList.add(productVO);
            }
            result.setClaimProductList(resultList);
        }
        result.setTotal(total);
        result.setTotalPage(totalPage);
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        return result;
    }


    private void populateBaseSubmitClaim(BizWarehouseReceiceDTO receiver, ClaimSubmitRequestVO provider) {
        receiver.setRoomId(provider.getRoomId());
        receiver.setRoomName(provider.getRoomName());
        receiver.setDeptId(provider.getDepartmentId());
        receiver.setDeptName(provider.getDepartmentName());
        receiver.setReceiceType(provider.getClaimType());
        receiver.setRemark(provider.getRemark());
    }

    private void populateBaseUser(BizWarehouseReceiceDTO receiver, UserBaseInfoDTO provider) {
        receiver.setApplyUserGuid(provider.getGuid());
        receiver.setApplyUserName(provider.getName());
    }

    private void populateClaimUser(BizWarehouseReceiceDTO receiver, ClaimUserVO claimUserVO) {
        if (Objects.isNull(claimUserVO)) {
            return;
        }
        receiver.setFirstUser(claimUserVO.getFirstUser());
        receiver.setFirstApplicant(claimUserVO.getFirstApplicant());
        receiver.setSecondUser(claimUserVO.getSecondUser());
        receiver.setSecondApplicant(claimUserVO.getSecondApplicant());
    }

    private void populateSessionUser(BizWarehouseReceiceDTO receiver, RjSessionInfo provider) {
        receiver.setOrgId(provider.getOrgId());
    }

    private void populateClaimStatusList(ClaimConstantListVO receiver, List<ReceiceStatus> provider) {
        List<ClaimStatusVO> claimStatusVOList = new ArrayList<>();
        for (ReceiceStatus receiceStatus : provider) {
            ClaimStatusVO claimStatus = new ClaimStatusVO();
            claimStatus.setStatus(receiceStatus.getValue());
            claimStatus.setStatusName(receiceStatus.getDesc());
            claimStatusVOList.add(claimStatus);
        }
        receiver.setClaimStatusVOList(claimStatusVOList);
    }

    private void populateDepartmentList(ClaimConstantListVO receiver, List<MiniDepartmentDTO> provider) {
        List<DepartmentVO> departmentVOList = new ArrayList<>();
        //提取用户部门信息返回给前端
        for (MiniDepartmentDTO departmentItem : provider) {
            DepartmentVO departmentVO = new DepartmentVO(departmentItem.getId(), departmentItem.getName());
            departmentVOList.add(departmentVO);
        }
        receiver.setDepartmentVOList(departmentVOList);
    }

    private void populateWarehouseList(ClaimConstantListVO receiver, List<MiniDepartmentDTO> provider) {
        List<Integer> departmentIds = provider.stream().map(o -> o.getId()).collect(Collectors.toList());
        //获取部门可选库房列表
        List<BizWarehouseRoomDTO> warehouseListCanBeChosen = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(departmentIds);
        List<WarehouseVO> warehouseVOList = new ArrayList<>();
        for (BizWarehouseRoomDTO warehouseCanBeChosen : warehouseListCanBeChosen) {
            WarehouseVO warehouseVO = new WarehouseVO(warehouseCanBeChosen.getId(), warehouseCanBeChosen.getRoomName());
            warehouseVOList.add(warehouseVO);
        }
        receiver.setWarehouseVOList(warehouseVOList);
    }

    private void populateApprovalStatusList(ClaimConstantListVO receiver, List<ApprovalTaskStatusEnum> provider) {
        List<ClaimApprovalStatusVO> approvalStatusVOList = new ArrayList<>();
        for (ApprovalTaskStatusEnum approvalTaskStatusEnum : provider) {
            ClaimApprovalStatusVO claimApprovalStatusVO = new ClaimApprovalStatusVO();
            claimApprovalStatusVO.setApprovalStatus(approvalTaskStatusEnum.getValue());
            claimApprovalStatusVO.setApprovalStatusName(approvalTaskStatusEnum.getDesc());
            approvalStatusVOList.add(claimApprovalStatusVO);
        }
        receiver.setClaimApprovalStatusVOList(approvalStatusVOList);
    }


    private void populateClaimApprovalProgressList(ClaimDetailVO receiver, List<ApprovalProgressDTO> provider) {
        List<ApprovalProgressVO> approvalProgressVOList = new ArrayList<>();
        for (ApprovalProgressDTO approvalProgressDTO : provider) {
            ApprovalProgressVO approvalProgressVO = new ApprovalProgressVO();
            approvalProgressVO.setStatus(approvalProgressDTO.getStatus());
            approvalProgressVO.setApprovalProgressName(approvalProgressDTO.getApprovalNodeName());
            approvalProgressVO.setOperatorList(approvalProgressDTO.getOperatorList());
            ApprovalLogDTO approvalLogDTO = approvalProgressDTO.getApprovalLogDTO();
            if (approvalLogDTO != null) {
                approvalProgressVO.setOperator(approvalLogDTO.getOperator());
                approvalProgressVO.setOptTime(approvalLogDTO.getOptTime());
                approvalProgressVO.setAutoExecute(approvalLogDTO.getAutoExecute());
            }
            approvalProgressVOList.add(approvalProgressVO);
        }
        receiver.setClaimApprovalLogList(approvalProgressVOList);
    }

    private void populateClaimDetail(ClaimDetailVO receiver, BizWarehouseReceiceDTO provider, String orgCode) {
        //封装基本申领单信息
        populateBaseClaim(receiver, provider);
        //封装申领单的商品信息
        if (CollectionUtils.isNotEmpty(provider.getBizWarehouseReceiceDetailDTOList())) {
            populateClaimProductList(receiver, provider.getBizWarehouseReceiceDetailDTOList(), orgCode);
        }
        //封装申领单的操作日志信息
        if (CollectionUtils.isNotEmpty(provider.getBizWarehouseReceiceLogDTOList())) {
            populateClaimOperationLogList(receiver, provider.getBizWarehouseReceiceLogDTOList());
        }
        // 封装申领人信息
        populateClaimUser2Detail(receiver, provider);
    }

    private void populateClaimUser2Detail(ClaimDetailVO receiver, BizWarehouseReceiceDTO provider) {
        if (ObjectUtil.hasNull(receiver, provider)) {
            return;
        }
        ClaimUserVO claimUserVO = new ClaimUserVO();
        claimUserVO.setFirstUser(provider.getFirstUser())
                .setFirstApplicant(provider.getFirstApplicant())
                .setSecondUser(provider.getSecondUser())
                .setSecondApplicant(provider.getSecondApplicant());
        receiver.setClaimUserVO(claimUserVO);
    }

    private void populateBaseClaim(ClaimDetailVO receiver, BizWarehouseReceiceDTO provider) {
        receiver.setClaimId(provider.getId());
        receiver.setClaimNo(provider.getReceiceNo());
        receiver.setOrderNo(provider.getOrderNo());
        receiver.setClaimType(provider.getReceiceType());
        receiver.setApprovalStatus(provider.getApprovalStatus());
        receiver.setApprovalStatusName(provider.getApprovalStatus() == null ? StringUtils.EMPTY : ApprovalTaskStatusEnum.valueOf(provider.getApprovalStatus()).getDesc());
        receiver.setClaimUserGuid(provider.getApplyUserGuid());
        receiver.setClaimUserName(provider.getApplyUserName());
        receiver.setCreateTime(provider.getCreateTime() == null ? null : provider.getCreateTime().getTime());
        receiver.setUpdateTime(provider.getUpdateTime() == null ? null : provider.getUpdateTime().getTime());
        receiver.setDepartmentId(provider.getDeptId());
        receiver.setDepartmentName(provider.getDeptName());
        receiver.setRoomId(provider.getRoomId());
        receiver.setRoomName(provider.getRoomName());
        receiver.setOrgId(provider.getOrgId());
        receiver.setStatus(provider.getStatus());
        receiver.setStatusName(provider.getStatus() == null ? StringUtils.EMPTY : ReceiceStatus.valueOf(provider.getStatus()).getDesc());
        receiver.setRemark(provider.getRemark());
    }

    private void populateClaimProductList(ClaimDetailVO receiver, List<BizWarehouseReceiceDetailDTO> provider, String orgCode) {
        List<WarehouseProductInfoVO> warehouseProductInfoVOList = new ArrayList<>();
        for (BizWarehouseReceiceDetailDTO bizWarehouseReceiceDetailDTO : provider) {
            WarehouseProductInfoVO warehouseProductInfoVO = WarehouseProductTranslator.bizWarehouseReceiceDetailDTO2WarehouseProductInfoVO(bizWarehouseReceiceDetailDTO);
            warehouseProductInfoVOList.add(warehouseProductInfoVO);
        }
        if (WarehouseConstant.ORG_CODE_NEED_FIRST_LEVEL_CATEGORY.contains(orgCode)) {
            //获取商品一级分类(个性化)
            this.selfPopulateFirstLevelCategory(warehouseProductInfoVOList);
        }
        receiver.setClaimProductList(warehouseProductInfoVOList);
    }

    private void selfPopulateFirstLevelCategory(List<WarehouseProductInfoVO> warehouseProductInfoVOList) {
        //获取商品一级分类(个性化)
        List<Integer> categoryIds = warehouseProductInfoVOList.stream().map(WarehouseProductInfoVO::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.getAllCategoryByIds(categoryIds);
        BusinessErrUtil.notEmpty(categoryDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO);
        for (WarehouseProductInfoVO item : warehouseProductInfoVOList) {
            CategoryDTO categoryDTO = CategoryUtil.getFirstLevelCategory(item.getCategoryId(), categoryDTOList);
            BusinessErrUtil.notNull(categoryDTO, ExecptionMessageEnum.PRIMARY_CATEGORY_INFO_NOT_FOUND, item.getCategoryId());
            item.setFirstLevelCategoryName(categoryDTO.getName());
        }
    }

    private void populateClaimOperationLogList(ClaimDetailVO receiver, List<BizWarehouseReceiceLogDTO> provider) {
        List<ClaimOperationLogVO> claimOperationLogVOList = new ArrayList<>();
        for (BizWarehouseReceiceLogDTO bizWarehouseReceiceLogDTO : provider) {
            ClaimOperationLogVO claimOperationLogVO = new ClaimOperationLogVO();
            claimOperationLogVO.setOperator(bizWarehouseReceiceLogDTO.getUserName());
            claimOperationLogVO.setApprovalOpinion(bizWarehouseReceiceLogDTO.getRemark());
            claimOperationLogVO.setOperation(bizWarehouseReceiceLogDTO.getBusinessDesc());
            if (bizWarehouseReceiceLogDTO.getCreateTime() != null) {
                claimOperationLogVO.setOperationTime(bizWarehouseReceiceLogDTO.getCreateTime().getTime());
            }
            claimOperationLogVOList.add(claimOperationLogVO);
        }
        receiver.setClaimOperationLogList(claimOperationLogVOList);
    }

    private BizWarehouseReceiceDTO claimPersonalPageRequestVO2BizWarehouseReceiceDTO(ClaimPersonalPageRequestVO from) {
        BizWarehouseReceiceDTO to = new BizWarehouseReceiceDTO();
        to.setReceiceNo(from.getClaimNo());
        to.setOrderNo(from.getOrderNo());
        to.setRoomIdList(from.getRoomIdList());
        if (from.getDepartmentId() != null) {
            to.setDeptIdList(Collections.singletonList(from.getDepartmentId()));
        }
        to.setStatus(from.getStatus());
        to.setApprovalStatus(from.getApprovalStatus());
        //时间范围参数，需判空
        to.setBeginTime(from.getApplicationLowerTime() == null ? null : new Date(from.getApplicationLowerTime()));
        to.setEndTime(from.getApplicationUpperTime() == null ? null : new Date(from.getApplicationUpperTime()));
        //页面参数,为空时设置默认值
        to.setPage(from.getCurrentPage() == null ? DEFAULT_CURRENT_PAGE : from.getCurrentPage());
        to.setSize(from.getPageSize() == null ? DEFAULT_PAGE_SIZE : from.getPageSize());
        return to;
    }

    private BizWarehouseReceiceDTO claimDeptPageRequestVO2BizWarehouseReceiceDTO(ClaimDeptPageRequestVO from) {
        BizWarehouseReceiceDTO to = new BizWarehouseReceiceDTO();
        to.setReceiceNo(from.getClaimNo());
        to.setOrderNo(from.getOrderNo());
        to.setRoomIdList(from.getRoomIdList());
        if (from.getDepartmentId() != null) {
            to.setDeptIdList(Collections.singletonList(from.getDepartmentId()));
        }
        to.setStatus(from.getStatus());
        to.setApprovalStatus(from.getApprovalStatus());
        to.setApplyUserName(from.getClaimUserName());
        //时间范围参数，需判空
        to.setBeginTime(from.getApplicationLowerTime() == null ? null : new Date(from.getApplicationLowerTime()));
        to.setEndTime(from.getApplicationUpperTime() == null ? null : new Date(from.getApplicationUpperTime()));
        //页面参数,为空时设置默认值
        to.setPage(from.getCurrentPage() == null ? DEFAULT_CURRENT_PAGE : from.getCurrentPage());
        to.setSize(from.getPageSize() == null ? DEFAULT_PAGE_SIZE : from.getPageSize());
        return to;
    }

    private ClaimSimpleVO bizWarehouseReceiceDTO2ClaimSimpleVO(BizWarehouseReceiceDTO from) {
        ClaimSimpleVO to = new ClaimSimpleVO();
        to.setClaimId(from.getId());
        to.setClaimNo(from.getReceiceNo());
        to.setOrderNo(from.getOrderNo());
        to.setClaimType(from.getReceiceType());
        to.setApprovalStatus(from.getApprovalStatus());
        to.setApprovalStatusName(from.getApprovalStatus() == null ? StringUtils.EMPTY : ApprovalTaskStatusEnum.valueOf(from.getApprovalStatus()).getDesc());
        to.setClaimUserName(from.getApplyUserName());
        to.setCreateTime(from.getCreateTime() == null ? null : from.getCreateTime().getTime());
        to.setDepartmentId(from.getDeptId());
        to.setDepartmentName(from.getDeptName());
        to.setRoomId(from.getRoomId());
        to.setRoomName(from.getRoomName());
        to.setStatus(from.getStatus());
        to.setStatusName(from.getStatus() == null ? StringUtils.EMPTY : ReceiceStatus.valueOf(from.getStatus()).getDesc());
        return to;
    }

    private ClaimPagingVO claimPagingDTO2ClaimPagingVO(PageApiResult<List<BizWarehouseReceiceDTO>> from) {
        //封装申领单分页VO
        ClaimPagingVO result = new ClaimPagingVO();
        int total = (int) from.getTotal();
        int pageSize = from.getSize();
        int currentPage = from.getCurrent();
        int totalPage = (total + pageSize - 1) / pageSize;
        //判空封装申领单列表
        if (CollectionUtils.isEmpty(from.getData())) {
            result.setClaimList(Collections.emptyList());
        } else {
            List<ClaimSimpleVO> claimListToReturn = new ArrayList<>(from.getData().size());
            for (BizWarehouseReceiceDTO claimDTO : from.getData()) {
                //申领单dto到vo的转换
                ClaimSimpleVO claimToReturn = this.bizWarehouseReceiceDTO2ClaimSimpleVO(claimDTO);
                claimListToReturn.add(claimToReturn);
            }
            result.setClaimList(claimListToReturn);
        }
        result.setTotal(total);
        result.setTotalPage(totalPage);
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        return result;
    }

    private WarehouseProductInfoVO bizWarehouseReceiceDetailDTO2WarehouseProductInfoVO(BizWarehouseReceiceDetailDTO from) {
        WarehouseProductInfoVO to = new WarehouseProductInfoVO();
        to.setTotalQuantity(from.getMeasurementNum() == null ? null : from.getMeasurementNum().doubleValue());
        to.setQuantityUnit(from.getMeasurementUnit());
        to.setSupplierName(from.getSuppName());
        to.setSupplierId(from.getSuppId());
        to.setSpecifications(from.getSpecifications());
        to.setUnit(from.getReceivedUnit());
        to.setQuantity(from.getReceivedNum());
        to.setProductName(from.getProductName());
        to.setGoodCode(from.getProductCode());
        to.setCasNo(from.getCasNo());
        to.setBrand(from.getBrandName());
        to.setForm(from.getForm());
        to.setDangerousType(from.getDangerousType());
        to.setRegulatoryFlag(from.getControlFlag());
        to.setCategoryId(from.getCategory());
        to.setProductCode(from.getProductPlatformCode());
        if (from.getDangerousType() != null) {
            DangerousTypeEnum dangerousType = DangerousTypeEnum.get(from.getDangerousType());
            String dangerousTypeName = DangerousTypeEnum.UN_DANGEROUS.equals(dangerousType) ? "非化学品" : dangerousType.getName();
            to.setDangerousTypeName(dangerousTypeName);
        }
        return to;
    }

    private CountClaimVO countDTO2CountClaimVO(CountDTO from) {
        CountClaimVO to = new CountClaimVO();
        to.setAllCount(from.getAllCount());
        to.setNotCompleteCount(from.getPendCount());
        to.setCompleteCount(from.getPassCount());
        to.setCancelCount(from.getCancelCount());
        return to;
    }

    private void validateUserSubmitAccessInDepartment(Integer userId, Integer departmentId, Integer orgId) {
        boolean hasAccess = userDepartmentRoleRpcServiceClient.findUserHasAccess(orgId, userId, departmentId, WarehouseConstant.BUYER_CENTER_CLAIM_OUT_WAREHOUSE);
        BusinessErrUtil.isTrue(hasAccess, ExecptionMessageEnum.NO_APPLICATION_PERMISSION);
    }

    private void validateUserViewAccessInOrganization(Integer userId, Integer orgId) {
        List<DepartmentDTO> departmentDTOList = userDepartmentRoleRpcServiceClient.findUserHasAccessDepartment(orgId, userId, WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        BusinessErrUtil.notEmpty(departmentDTOList, ExecptionMessageEnum.NO_VIEW_PERMISSION_IN_PURCHASER_CENTER);
    }

    /**
     * 设置打印按钮的展示
     *
     * @param claimSimpleVOList 申领单列表
     * @param orgId             orgId
     */
    private void setShowPrintButton(List<ClaimSimpleVO> claimSimpleVOList, Integer orgId) {
        if (CollectionUtils.isEmpty(claimSimpleVOList)) {
            return;
        }
        // 获取orgId
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(orgId);
        String orgCode = simpleOrgDTO.getCode();
        // 获取申领单打印按钮状态配置
        String confShowPrintWareHouseApplicationStatus = PrintConfigConstant.SHOW_PRINT_WAREHOUSE_CLAIM_APPLICATION_BUTTON_STATUS;
        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, New.list(confShowPrintWareHouseApplicationStatus));
        List<Integer> printClaimApplicationStatusList = New.emptyList();
        if (CollectionUtils.isNotEmpty(baseConfigList)) {
            printClaimApplicationStatusList = baseConfigList.stream().map(BaseConfigDTO::getConfigValue)
                    .map(item -> item.split(",")).flatMap(Arrays::stream).filter(StringUtils::isNoneBlank).map(Integer::valueOf).collect(toList());
        }

        for (ClaimSimpleVO claimSimpleVO : claimSimpleVOList) {
            // 根据申领单数据的状态判断
            Integer status = claimSimpleVO.getStatus();
            boolean showPrintWareHouseApplication = printClaimApplicationStatusList.contains(status);
            claimSimpleVO.setShowPrintClaimApplication(showPrintWareHouseApplication);
        }
    }
}
