package com.ruijing.store.warehouse.message.bean;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
public class WarehouseBean {
    /**
     * 库房Id
     */
    private Integer warehouseId;

    /**
     * 库房名称
     */
    private String warehouseName;

    public WarehouseBean(Integer warehouseId, String warehouseName) {
        this.warehouseId = warehouseId;
        this.warehouseName = warehouseName;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseBean{");
        sb.append("warehouseId=").append(warehouseId);
        sb.append(", warehouseName='").append(warehouseName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
