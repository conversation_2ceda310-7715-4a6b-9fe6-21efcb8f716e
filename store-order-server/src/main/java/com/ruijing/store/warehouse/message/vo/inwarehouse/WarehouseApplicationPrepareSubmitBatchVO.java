package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 批量订单准备入库信息返回
 * @author: zhong<PERSON><PERSON><PERSON>
 * @create: 2022-01-13 11:58
 */
@RpcModel(description="批量订单准备入库信息返回")
public class WarehouseApplicationPrepareSubmitBatchVO implements Serializable {

    private static final long serialVersionUID = 3166120965239831426L;

    @RpcModelProperty("订单信息明细")
    List<WarehouseApplicationPrepareSubmitVO> data;

    @RpcModelProperty("发票号信息")
    List<InvoiceInfoVO> invoiceInfo;

    public List<WarehouseApplicationPrepareSubmitVO> getData() {
        return data;
    }

    public void setData(List<WarehouseApplicationPrepareSubmitVO> data) {
        this.data = data;
    }

    public List<InvoiceInfoVO> getInvoiceInfo() {
        return invoiceInfo;
    }

    public void setInvoiceInfo(List<InvoiceInfoVO> invoiceInfo) {
        this.invoiceInfo = invoiceInfo;
    }

    @Override
    public String toString() {
        return "WarehouseApplicationPrepareSubmitBatchVO{" +
                "data=" + data +
                ", invoiceInfo=" + invoiceInfo +
                '}';
    }
}
