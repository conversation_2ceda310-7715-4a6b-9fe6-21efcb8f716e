package com.ruijing.store.warehouse.utils;

import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.shop.category.api.constant.CategoryConstant;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/4 11:44
 */
public class CategoryUtil {

    /**
     * 商品分类分隔符表达式
     */
    public static final String CATEGORY_PATH_SEPARATOR_REGEX = "\\.";

    public static CategoryDTO getFirstLevelCategory(Integer categoryId, List<CategoryDTO> categoryDTOList) {
        if (categoryId == null || CollectionUtils.isEmpty(categoryDTOList)) {
            throw new RuntimeException("categoryId、categoryDTOList不能为空");
        }
        Map<Long, CategoryDTO> categoryDTOMap = categoryDTOList.stream().collect(Collectors.toMap(o -> o.getId(), Function.identity(), (key1, key2) -> key1));
        CategoryDTO currentCategory = categoryDTOMap.get(categoryId.longValue());
        if (currentCategory == null) {
            throw new BusinessInterceptException(ExecptionMessageEnum.PRODUCT_CATEGORY_INFO_NOT_FOUND, categoryId);
        }

        CategoryDTO resultCategory;
        if (CategoryConstant.FIRST_LEVEL.equals(currentCategory.getLevel())) {
            resultCategory = currentCategory;
        } else if (CategoryConstant.SECOND_LEVEL.equals(currentCategory.getLevel())) {
            resultCategory = categoryDTOMap.get(currentCategory.getParentId());
        } else {
            //三级以上分类，获取path的第一个值作为一级分类
            String path = currentCategory.getPath();
            if (StringUtils.isBlank(path)) {
                throw new RuntimeException("该非一级商品分类的path为空：" + currentCategory.getId());
            }
            String[] pathIds = path.split(CATEGORY_PATH_SEPARATOR_REGEX);
            resultCategory = categoryDTOMap.get(Long.valueOf(pathIds[0]));
        }
        return resultCategory;
    }
}
