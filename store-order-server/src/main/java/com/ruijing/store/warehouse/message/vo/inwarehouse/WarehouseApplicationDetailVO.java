package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.warehouse.message.dto.WarehouseDockingDataDTO;
import com.ruijing.store.warehouse.message.vo.ElectronicSignDataVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库申请单详细信息")
public class WarehouseApplicationDetailVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "入库单Id")
    private Integer warehouseApplicationId;

    @RpcModelProperty(value = "入库单号")
    private String warehouseApplicationNo;

    @RpcModelProperty(value = "订单Id")
    private Integer orderId;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单类型")
    private OrderTypeEnum orderType;

    @RpcModelProperty(value = "订单总金额")
    private Double orderTotalPrice;

    @RpcModelProperty(value = "订单号对应条形码(base64编码后的字符串)")
    private String orderNoBarcode;

    @RpcModelProperty(value = "入库单号对应条形码")
    private String entryNoBarcode;

    @RpcModelProperty(value = "申请时间")
    private Long warehouseApplicationTime;

    @RpcModelProperty(value = "申请人")
    private String warehouseApplicant;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "单位名称")
    private String orgName;
    
    @RpcModelProperty(value = "单位logo")
    private String orgLogo;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡所属的项目编码")
    private String projectCode;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡所属的项目名称")
    private String projectName;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡号")
    private String funCardNo;

    @RpcModelProperty(value = "该入库单对应的订单的供应商名称")
    private String supplierName;

    @RpcModelProperty(value = "该入库单对应的订单的供应商编码")
    private String supplierCode;

    @RpcModelProperty(value = "入库申请页面，用户填写的备注内容，即入库单的备注字段")
    private String remark;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称（待审批、审核通过、审核驳回）")
    private String approvalStatusName;

    @RpcModelProperty(value = "入库单状态（0：未入库 1：已入库）")
    private  Integer status;

    @RpcModelProperty(value = "申请入库商品状态名称（未入库、已入库）")
    private String statusName;

    @RpcModelProperty(value = "采购人姓名")
    private String purchaserName;

    @RpcModelProperty(value = "采购人联系方式")
    private String purchaserPhone;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    @RpcModelProperty(value = "当前部门上一级部门名称")
    private String departmentParentName;

    @RpcModelProperty(value = "入库单所有商品的总额的合计")
    private String totalPrice;

    @RpcModelProperty(value = "合计金额的大写")
    private String totalPriceInChinese;

    @RpcModelProperty(value = "科长(对宁波二院：入库单对应的订单对应的采购申请单的二级审批人姓名）")
    private String sectionChief;

    @RpcModelProperty(value = "订单验收人(对宁波二院：验收审批人，暂时写死，其他机构取订单验收人)")
    private String acceptor;

    @RpcModelProperty(value = "课题组负责人（订单的采购人所属的课题组的负责人姓名）")
    private String departmentDirector;

    @RpcModelProperty(value = "入库日期，时间戳格式")
    private Long inWarehouseTime;

    @RpcModelProperty(value = "入库审核人姓名")
    private String approverName;

    @RpcModelProperty(value = "入库审核时间")
    private String approvalTimeString;

    @RpcModelProperty(value = "发票单号，多个的话用逗号隔开")
    private String invoiceNo;

    @RpcModelProperty(value = "发票号列表，发票号字段的冗余，适应单位个性需求, invoiceNo的列表化")
    private List<String> invoiceNoList;

    @RpcModelProperty(value = "发票时间，列表")
    private List<Long> invoiceDateTimeList;

    @RpcModelProperty(value = "订单流程种类 0:线上, 1:线下")
    private Integer orderSpecies;

    @RpcModelProperty(value = "入库申请图片")
    private List<String> inWarehousePictureUrlList;

    @RpcModelProperty(value = "入库申请单关联商品信息列表")
    private List<WarehouseProductInfoVO> warehouseProductInfoVOList;

    @RpcModelProperty(value = "审批进度列表")
    private List<ApprovalProgressVO> approvalProgressVOList;

    @RpcModelProperty(value = "关联订单的收货图片")
    private List<String> receivedPictures;

    @RpcModelProperty(value = "操作日志")
    private List<WarehouseOperationLogVO> operationLogVOList;

    @RpcModelProperty(value = "重新提交可用库房列表")
    private List<WarehouseVO> warehouseVOList;

    @RpcModelProperty(value = "入库单对应的即入即出的出库单")
    private OutWarehouseApplicationDetailVO immediatelyOutWarehouseApplicationVO;

    @RpcModelProperty(value = "入库方式")
    private Integer businessType;

    @RpcModelProperty(value = "订单审批日志")
    List<WarehouseApprovalLogVO> orderApprovalLogs;

    @RpcModelProperty(value = "采购与竞价审批日志")
    List<WarehouseApprovalLogVO> purchaseApprovalLogs;
    
    @RpcModelProperty(value = "电子签名")
    List<ElectronicSignDataVO> electronicSignDataVOList;
    
    @RpcModelProperty("入库对接推送数据")
    private WarehouseDockingDataDTO dockingInfo;

    /**
     * 经费卡信息，拼接的卡号卡名（从订单列表copy的）
     */
    @RpcModelProperty("经费卡信息，拼接的卡号卡名")
    private List<OrderFundcardVO> fundCard;

    @RpcModelProperty("是否重新提交")
    private boolean recommit;

    /**
     * 制单人
     */
    @RpcModelProperty("制单人")
    private String docCreator;

    /**
     * 领料部门
     */
    @RpcModelProperty("领料部门")
    private String pickingDept;

    /**
     * 领用库房
     */
    @RpcModelProperty("领用库房")
    private String receiveWarehouse;

    /**
     * 库房的推送状态，目前只管中爆
     */
    @RpcModelProperty(value = "推送状态", description = "推送状态  1 推送中 2推送成功 3推送失败")
    private Integer pushStatus;

    @RpcModelProperty("是否一物一码")
    private Boolean eachProductEachCode;

    @RpcModelProperty("是否需要填写批次")
    private Boolean suppNeedFillBatchesData;

    @RpcModelProperty(value = "第一归还人")
    private String firstReverter;

    @RpcModelProperty(value = "第二归还人")
    private String secondReverter;

    public String getSecondReverter() {
        return secondReverter;
    }

    public WarehouseApplicationDetailVO setSecondReverter(String secondReverter) {
        this.secondReverter = secondReverter;
        return this;
    }

    public String getFirstReverter() {
        return firstReverter;
    }

    public WarehouseApplicationDetailVO setFirstReverter(String firstReverter) {
        this.firstReverter = firstReverter;
        return this;
    }

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getWarehouseApplicationTime() {
        return warehouseApplicationTime;
    }

    public void setWarehouseApplicationTime(Long warehouseApplicationTime) {
        this.warehouseApplicationTime = warehouseApplicationTime;
    }

    public String getWarehouseApplicant() {
        return warehouseApplicant;
    }

    public void setWarehouseApplicant(String warehouseApplicant) {
        this.warehouseApplicant = warehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<WarehouseProductInfoVO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductInfoVO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public List<String> getReceivedPictures() {
        return receivedPictures;
    }

    public void setReceivedPictures(List<String> receivedPictures) {
        this.receivedPictures = receivedPictures;
    }

    public List<WarehouseOperationLogVO> getOperationLogVOList() {
        return operationLogVOList;
    }

    public void setOperationLogVOList(List<WarehouseOperationLogVO> operationLogVOList) {
        this.operationLogVOList = operationLogVOList;
    }

    public List<WarehouseVO> getWarehouseVOList() {
        return warehouseVOList;
    }

    public void setWarehouseVOList(List<WarehouseVO> warehouseVOList) {
        this.warehouseVOList = warehouseVOList;
    }

    public List<ApprovalProgressVO> getApprovalProgressVOList() {
        return approvalProgressVOList;
    }

    public void setApprovalProgressVOList(List<ApprovalProgressVO> approvalProgressVOList) {
        this.approvalProgressVOList = approvalProgressVOList;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLogo() {
        return orgLogo;
    }

    public void setOrgLogo(String orgLogo) {
        this.orgLogo = orgLogo;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public void setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
    }

    public String getSectionChief() {
        return sectionChief;
    }

    public void setSectionChief(String sectionChief) {
        this.sectionChief = sectionChief;
    }

    public String getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(String acceptor) {
        this.acceptor = acceptor;
    }

    public String getDepartmentDirector() {
        return departmentDirector;
    }

    public void setDepartmentDirector(String departmentDirector) {
        this.departmentDirector = departmentDirector;
    }

    public Long getInWarehouseTime() {
        return inWarehouseTime;
    }

    public void setInWarehouseTime(Long inWarehouseTime) {
        this.inWarehouseTime = inWarehouseTime;
    }

    public List<String> getInWarehousePictureUrlList() {
        return inWarehousePictureUrlList;
    }

    public void setInWarehousePictureUrlList(List<String> inWarehousePictureUrlList) {
        this.inWarehousePictureUrlList = inWarehousePictureUrlList;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getOrderNoBarcode() {
        return orderNoBarcode;
    }

    public void setOrderNoBarcode(String orderNoBarcode) {
        this.orderNoBarcode = orderNoBarcode;
    }

    public String getPurchaserPhone() {
        return purchaserPhone;
    }

    public void setPurchaserPhone(String purchaserPhone) {
        this.purchaserPhone = purchaserPhone;
    }

    public String getFunCardNo() {
        return funCardNo;
    }

    public void setFunCardNo(String funCardNo) {
        this.funCardNo = funCardNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getApprovalTimeString() {
        return approvalTimeString;
    }

    public void setApprovalTimeString(String approvalTimeString) {
        this.approvalTimeString = approvalTimeString;
    }

    public String getEntryNoBarcode() {
        return entryNoBarcode;
    }

    public void setEntryNoBarcode(String entryNoBarcode) {
        this.entryNoBarcode = entryNoBarcode;
    }

    public OutWarehouseApplicationDetailVO getImmediatelyOutWarehouseApplicationVO() {
        return immediatelyOutWarehouseApplicationVO;
    }

    public void setImmediatelyOutWarehouseApplicationVO(OutWarehouseApplicationDetailVO immediatelyOutWarehouseApplicationVO) {
        this.immediatelyOutWarehouseApplicationVO = immediatelyOutWarehouseApplicationVO;
    }

    public Double getOrderTotalPrice() {
        return orderTotalPrice;
    }

    public void setOrderTotalPrice(Double orderTotalPrice) {
        this.orderTotalPrice = orderTotalPrice;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public List<String> getInvoiceNoList() {
        return invoiceNoList;
    }

    public void setInvoiceNoList(List<String> invoiceNoList) {
        this.invoiceNoList = invoiceNoList;
    }

    public List<Long> getInvoiceDateTimeList() {
        return invoiceDateTimeList;
    }

    public void setInvoiceDateTimeList(List<Long> invoiceDateTimeList) {
        this.invoiceDateTimeList = invoiceDateTimeList;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public WarehouseApplicationDetailVO setBusinessType(Integer businessType) {
        this.businessType = businessType;
        return this;
    }

    public List<WarehouseApprovalLogVO> getOrderApprovalLogs() {
        return orderApprovalLogs;
    }

    public void setOrderApprovalLogs(List<WarehouseApprovalLogVO> orderApprovalLogs) {
        this.orderApprovalLogs = orderApprovalLogs;
    }

    public List<WarehouseApprovalLogVO> getPurchaseApprovalLogs() {
        return purchaseApprovalLogs;
    }

    public void setPurchaseApprovalLogs(List<WarehouseApprovalLogVO> purchaseApprovalLogs) {
        this.purchaseApprovalLogs = purchaseApprovalLogs;
    }

    public List<ElectronicSignDataVO> getElectronicSignDataVOList() {
        return electronicSignDataVOList;
    }

    public void setElectronicSignDataVOList(List<ElectronicSignDataVO> electronicSignDataVOList) {
        this.electronicSignDataVOList = electronicSignDataVOList;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public WarehouseDockingDataDTO getDockingInfo() {
        return dockingInfo;
    }

    public void setDockingInfo(WarehouseDockingDataDTO dockingInfo) {
        this.dockingInfo = dockingInfo;
    }

    public List<OrderFundcardVO> getFundCard() {
        return fundCard;
    }

    public void setFundCard(List<OrderFundcardVO> fundCard) {
        this.fundCard = fundCard;
    }

    public boolean getRecommit() {
        return recommit;
    }

    public void setRecommit(boolean recommit) {
        this.recommit = recommit;
    }

    public String getDocCreator() {
        return docCreator;
    }

    public void setDocCreator(String docCreator) {
        this.docCreator = docCreator;
    }

    public String getPickingDept() {
        return pickingDept;
    }

    public void setPickingDept(String pickingDept) {
        this.pickingDept = pickingDept;
    }

    public String getReceiveWarehouse() {
        return receiveWarehouse;
    }

    public void setReceiveWarehouse(String receiveWarehouse) {
        this.receiveWarehouse = receiveWarehouse;
    }

    public Integer getPushStatus() {
        return pushStatus;
    }

    public WarehouseApplicationDetailVO setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
        return this;
    }

    public Boolean getEachProductEachCode() {
        return eachProductEachCode;
    }

    public WarehouseApplicationDetailVO setEachProductEachCode(Boolean eachProductEachCode) {
        this.eachProductEachCode = eachProductEachCode;
        return this;
    }

    public Boolean getSuppNeedFillBatchesData() {
        return suppNeedFillBatchesData;
    }

    public WarehouseApplicationDetailVO setSuppNeedFillBatchesData(Boolean suppNeedFillBatchesData) {
        this.suppNeedFillBatchesData = suppNeedFillBatchesData;
        return this;
    }

    @Override
    public String toString() {
        return "WarehouseApplicationDetailVO{" +
                "warehouseApplicationId=" + warehouseApplicationId +
                ", warehouseApplicationNo='" + warehouseApplicationNo + '\'' +
                ", orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", orderType=" + orderType +
                ", orderTotalPrice=" + orderTotalPrice +
                ", orderNoBarcode='" + orderNoBarcode + '\'' +
                ", entryNoBarcode='" + entryNoBarcode + '\'' +
                ", warehouseApplicationTime=" + warehouseApplicationTime +
                ", warehouseApplicant='" + warehouseApplicant + '\'' +
                ", warehouseId=" + warehouseId +
                ", warehouseName='" + warehouseName + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgLogo='" + orgLogo + '\'' +
                ", projectCode='" + projectCode + '\'' +
                ", projectName='" + projectName + '\'' +
                ", funCardNo='" + funCardNo + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", remark='" + remark + '\'' +
                ", approvalStatus=" + approvalStatus +
                ", approvalStatusName='" + approvalStatusName + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", purchaserName='" + purchaserName + '\'' +
                ", purchaserPhone='" + purchaserPhone + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", departmentParentName='" + departmentParentName + '\'' +
                ", totalPrice='" + totalPrice + '\'' +
                ", totalPriceInChinese='" + totalPriceInChinese + '\'' +
                ", sectionChief='" + sectionChief + '\'' +
                ", acceptor='" + acceptor + '\'' +
                ", departmentDirector='" + departmentDirector + '\'' +
                ", inWarehouseTime=" + inWarehouseTime +
                ", approverName='" + approverName + '\'' +
                ", approvalTimeString='" + approvalTimeString + '\'' +
                ", invoiceNo='" + invoiceNo + '\'' +
                ", invoiceNoList=" + invoiceNoList +
                ", invoiceDateTimeList=" + invoiceDateTimeList +
                ", orderSpecies=" + orderSpecies +
                ", inWarehousePictureUrlList=" + inWarehousePictureUrlList +
                ", warehouseProductInfoVOList=" + warehouseProductInfoVOList +
                ", approvalProgressVOList=" + approvalProgressVOList +
                ", receivedPictures=" + receivedPictures +
                ", operationLogVOList=" + operationLogVOList +
                ", warehouseVOList=" + warehouseVOList +
                ", immediatelyOutWarehouseApplicationVO=" + immediatelyOutWarehouseApplicationVO +
                ", businessType=" + businessType +
                ", orderApprovalLogs=" + orderApprovalLogs +
                ", purchaseApprovalLogs=" + purchaseApprovalLogs +
                ", electronicSignDataVOList=" + electronicSignDataVOList +
                ", dockingInfo=" + dockingInfo +
                ", fundCard=" + fundCard +
                ", recommit=" + recommit +
                ", docCreator='" + docCreator + '\'' +
                ", pickingDept='" + pickingDept + '\'' +
                ", receiveWarehouse='" + receiveWarehouse + '\'' +
                ", pushStatus=" + pushStatus +
                ", eachProductEachCode=" + eachProductEachCode +
                ", suppNeedFillBatchesData=" + suppNeedFillBatchesData +
                ", firstReverter='" + firstReverter + '\'' +
                ", secondReverter='" + secondReverter + '\'' +
                '}';
    }
}
