package com.ruijing.store.warehouse.message.vo.claim;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:16 2020/12/24.
 * 申领单重新提交对象
 */
@RpcModel(description="申领单重新提交对象")
public class ClaimResubmitRequestVO implements Serializable {

    private static final long serialVersionUID = 4717454454475281942L;

    @RpcModelProperty(value = "申领单号", example = "")
    private String claimNo;

    @RpcModelProperty(value = "课题组Id", example = "10")
    private Integer departmentId;

    @RpcModelProperty(value = "课题组名称", example = "法大大课题组")
    private String departmentName;

    @RpcModelProperty(value = "申领库房Id, 增改申领单时使用", example = "94")
    private Integer roomId;

    @RpcModelProperty(value = "申领库房名称, 增改申领单时使用", example = "")
    private String roomName;

    @RpcModelProperty(value = "申领方式 0普通商品 1按危化品", example = "0")
    private Integer claimType;

    @RpcModelProperty(value = "申领人信息")
    private ClaimUserVO claimUserVO;
    
    @RpcModelProperty(value = "备注")
    private String remark;

    @RpcModelProperty(value = "申领的商品列表")
    private List<WarehouseProductInfoVO> warehouseProductInfoVOList;

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public Integer getClaimType() {
        return claimType;
    }

    public void setClaimType(Integer claimType) {
        this.claimType = claimType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<WarehouseProductInfoVO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductInfoVO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public ClaimUserVO getClaimUserVO() {
        return claimUserVO;
    }

    public ClaimResubmitRequestVO setClaimUserVO(ClaimUserVO claimUserVO) {
        this.claimUserVO = claimUserVO;
        return this;
    }

    @Override
    public String toString() {
        return "ClaimResubmitRequestVO{" +
                "claimNo='" + claimNo + '\'' +
                ", departmentId=" + departmentId +
                ", departmentName='" + departmentName + '\'' +
                ", roomId=" + roomId +
                ", roomName='" + roomName + '\'' +
                ", claimType=" + claimType +
                ", claimUserVO=" + claimUserVO +
                ", remark='" + remark + '\'' +
                ", warehouseProductInfoVOList=" + warehouseProductInfoVOList +
                '}';
    }
}
