package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:50 2020/12/24.
 * 根据订单查询入库单
 */
@RpcModel(description="根据订单查询入库单")
public class OrderRequestVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "订单Id", example = "95998")
    private Integer orderId;

    @RpcModelProperty(value = "订单号", example = "DC201911215296401")
    private String orderNo;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderRequestVO{");
        sb.append("orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
