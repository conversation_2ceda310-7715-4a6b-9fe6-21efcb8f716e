package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.dto.WarehouseDockingDataDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="提交入库请求")
public class WarehouseSubmitApplicationRequestVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "订单Id", example = "95998")
    private Integer orderId;

    @RpcModelProperty(value = "备注", example = "正常入库申请")
    private String remark;

    @RpcModelProperty(value = "入库申请图片", example = "http://images-test.rjmart.cn/image/c19bd248/a8a4188f-22db-4146-8561-6a231f9bff75.jpg;https://www.rjmart.cn/download/reagent/image/d15682ec/773f37a6-8a1c-4b9c-830b-abf5682a7d05.png")
    private String inWarehousePictureUrls;

    @RpcModelProperty(value = "订单关联商品信息列表")
    private List<WarehouseProductRequestVO> warehouseProductRequestList;

    @RpcModelProperty(value = "提交入库方式,0正常提交,1补救提交")
    private Integer submitWay;

    @RpcModelProperty(value = "发票信息")
    private List<InvoiceInfoVO> invoiceInfo;
    
    @RpcModelProperty("入库对接推送数据")
    private WarehouseDockingDataDTO dockingInfo;

    @RpcModelProperty(value = "是否确认了仅提醒配伍禁忌")
    public Boolean incompatibilityVerify;

    @RpcModelProperty(value = "是否确认了校验计量含量")
    public boolean checkMeasurementNum;

    public boolean isCheckMeasurementNum() {
        return checkMeasurementNum;
    }

    public void setCheckMeasurementNum(boolean checkMeasurementNum) {
        this.checkMeasurementNum = checkMeasurementNum;
    }

    public Integer getSubmitWay() {
        return submitWay;
    }

    public void setSubmitWay(Integer submitWay) {
        this.submitWay = submitWay;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<WarehouseProductRequestVO> getWarehouseProductRequestList() {
        return warehouseProductRequestList;
    }

    public void setWarehouseProductRequestList(List<WarehouseProductRequestVO> warehouseProductRequestList) {
        this.warehouseProductRequestList = warehouseProductRequestList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInWarehousePictureUrls() {
        return inWarehousePictureUrls;
    }

    public void setInWarehousePictureUrls(String inWarehousePictureUrls) {
        this.inWarehousePictureUrls = inWarehousePictureUrls;
    }

    public List<InvoiceInfoVO> getInvoiceInfo() {
        return invoiceInfo;
    }

    public void setInvoiceInfo(List<InvoiceInfoVO> invoiceInfo) {
        this.invoiceInfo = invoiceInfo;
    }

    public WarehouseDockingDataDTO getDockingInfo() {
        return dockingInfo;
    }

    public void setDockingInfo(WarehouseDockingDataDTO dockingInfo) {
        this.dockingInfo = dockingInfo;
    }

    public Boolean getIncompatibilityVerify() {
        return incompatibilityVerify;
    }

    public WarehouseSubmitApplicationRequestVO setIncompatibilityVerify(Boolean incompatibilityVerify) {
        this.incompatibilityVerify = incompatibilityVerify;
        return this;
    }

    @Override
    public String toString() {
        return "WarehouseSubmitApplicationRequestVO{" +
                "orderId=" + orderId +
                ", remark='" + remark + '\'' +
                ", inWarehousePictureUrls='" + inWarehousePictureUrls + '\'' +
                ", warehouseProductRequestList=" + warehouseProductRequestList +
                ", submitWay=" + submitWay +
                ", invoiceInfo=" + invoiceInfo +
                ", dockingInfo=" + dockingInfo +
                ", incompatibilityVerify=" + incompatibilityVerify +
                ", checkMeasurementNum=" + checkMeasurementNum +
                '}';
    }
}
