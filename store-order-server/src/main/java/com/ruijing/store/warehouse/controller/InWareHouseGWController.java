package com.ruijing.store.warehouse.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.fundamental.api.annotation.Async;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.warehouse.message.vo.inwarehouse.*;
import com.ruijing.store.warehouse.service.InWareHouseGWService;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryExitDTO;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhukai
 * @date : 2020/12/23 下午4:33
 * @description: 采购人入库网关接口
 */
@MSharpService(isGateway = "true")
@RpcApi(value = "采购人入库单网关服务",description = "采购人入库单网关服务")
@RpcMapping("/inwarehouse")
@Async(value = "inWareHouseExecutor")
public class InWareHouseGWController {

    @Resource
    private InWareHouseGWService inWareHouseGWService;

    @Resource
    private CacheClient cacheClient;

    @RpcMethod(value = "获取要提交入库申请的订单相关信息", notes = "订单id必填,其他字段在这里会忽略")
    @RpcMapping("/getPrepareSubmitInfoByOrderId")
    public RemoteResponse<WarehouseApplicationPrepareSubmitVO> getPrepareSubmitInfoByOrderId(OrderRequestVO request) {
        WarehouseApplicationPrepareSubmitVO result = inWareHouseGWService.getPrepareSubmitInfoByOrderId(request);
        return RemoteResponse.<WarehouseApplicationPrepareSubmitVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "批量获取要提交入库申请的订单相关信息", notes = "orderIdList必填,其他字段在这里会忽略")
    @RpcMapping("/getBatchPrepareSubmitInfoByOrderId")
    public RemoteResponse<WarehouseApplicationPrepareSubmitBatchVO> getBatchPrepareSubmitInfoByOrderId(OrderBasicParamDTO request) {
        WarehouseApplicationPrepareSubmitBatchVO result = inWareHouseGWService.getBatchPrepareSubmitInfoByOrderId(request);
        return RemoteResponse.<WarehouseApplicationPrepareSubmitBatchVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "提交入库申请单")
    @RpcMapping("/submitWarehouseApplication")
    public RemoteResponse<SubmitWarehouseVO> submitWarehouseApplication(WarehouseSubmitApplicationRequestVO request) {
        SubmitWarehouseVO result = inWareHouseGWService.submitWarehouseApplication(request);
        return RemoteResponse.<SubmitWarehouseVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "批量提交入库申请单")
    @RpcMapping("/batchSubmitWarehouseApplication")
    public RemoteResponse<SubmitWarehouseVO> batchSubmitWarehouseApplication(WarehouseSubmitBatchRequestVO request) {
        Preconditions.notEmpty(request.getData(),"操作失败, 入参不能为空");
        SubmitWarehouseVO submitWarehouseVO;
        List<Integer> orderIdList = request.getData().stream().map(WarehouseSubmitApplicationRequestVO::getOrderId).collect(Collectors.toList());
        orderIdList = orderIdList.subList(0,Math.min(orderIdList.size(), 10));
        String cacheKey = orderIdList.toString();
        try {
            cacheClient.controlRepeatOperation(cacheKey, 20, "存在同一个入库请求处理，请稍后刷新重试");
            submitWarehouseVO = inWareHouseGWService.batchSubmitWarehouseApplication(request);
        } catch (Exception e) {
            return RemoteResponse.<SubmitWarehouseVO>custom().setFailure("存在同一个入库请求处理，请稍后刷新重试").setData(null);
        } finally {
            cacheClient.removeCache(cacheKey);
        }
        return RemoteResponse.<SubmitWarehouseVO>custom().setSuccess().setData(submitWarehouseVO);
    }

    @RpcMethod(value = "重新提交入库申请单")
    @RpcMapping("/reSubmitWarehouseApplication")
    public RemoteResponse<SubmitWarehouseVO> reSubmitWarehouseApplication(WarehouseReSubmitApplicationRequestVO request) {
        SubmitWarehouseVO result = inWareHouseGWService.reSubmitWarehouseApplication(request);
        return RemoteResponse.<SubmitWarehouseVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "重新推送第三方平台入库状态")
    @RpcMapping("/rePushWarehousingToThirdPlatForm")
    public RemoteResponse<Boolean> rePushWarehousingToThirdPlatForm(OrderRequestVO request) {
        boolean result = inWareHouseGWService.rePushWarehousingToThirdPlatForm(request);
        if (result) {
            return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("重新推送入库状态失败").setData(false);
        }
    }

    @RpcMethod(value = "获取入库申请单列表(个人)")
    @RpcMapping("/getWarehouseApplicationList/personal")
    public RemoteResponse<WarehouseApplicationPageVO> getPersonalWarehouseApplicationList(WarehouseApplicationPersonalPageRequestVO request) {
        WarehouseApplicationPageVO result = inWareHouseGWService.getPersonalWarehouseApplicationList(request);
        return RemoteResponse.<WarehouseApplicationPageVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取入库申请单列表(课题组)")
    @RpcMapping("/getWarehouseApplicationList/department")
    public RemoteResponse<WarehouseApplicationPageVO> getDepartmentWarehouseApplicationList(WarehouseApplicationDeptPageRequestVO request) {
        WarehouseApplicationPageVO result = inWareHouseGWService.getDepartmentWarehouseApplicationList(request);
        return RemoteResponse.<WarehouseApplicationPageVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取入库申请单详情")
    @RpcMapping("/getWarehouseApplicationDetail")
    public RemoteResponse<WarehouseApplicationDetailVO> getWarehouseApplicationDetail(WarehouseApplicationDetailRequestVO request) {
        WarehouseApplicationDetailVO result = inWareHouseGWService.getWarehouseApplicationDetail(request);
        return RemoteResponse.<WarehouseApplicationDetailVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "取入库申请单列表的枚举常量列表")
    @RpcMapping("/getWarehouseConstantList")
    public RemoteResponse<WarehouseConstantListVO> getWarehouseConstantList() {
        WarehouseConstantListVO result = inWareHouseGWService.getWarehouseConstantList();
        return RemoteResponse.<WarehouseConstantListVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取入库申请单数量(个人)")
    @RpcMapping("/getWarehouseApplicationCount/personal")
    public RemoteResponse<CountWarehouseApplicationVO> getPersonalWarehouseApplicationCount() {
        CountWarehouseApplicationVO result = inWareHouseGWService.getPersonalWarehouseApplicationCount();
        return RemoteResponse.<CountWarehouseApplicationVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取入库申请单数量(课题组)")
    @RpcMapping("/getWarehouseApplicationCount/department")
    public RemoteResponse<CountWarehouseApplicationVO> getDepartmentWarehouseApplicationCount() {
        CountWarehouseApplicationVO result = inWareHouseGWService.getDepartmentWarehouseApplicationCount();
        return RemoteResponse.<CountWarehouseApplicationVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "是否使用新出入库系统")
    @RpcMapping("/isUseNewWarehouseSystem")
    public RemoteResponse<Boolean> isUseNewWarehouseSystem() {
        boolean result = inWareHouseGWService.isUseNewWarehouseSystem();
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "获取订单相关的入库与出库信息列表，前端渲染打印用")
    @RpcMapping("/getInAndOutWarehouseInfoForOrder")
    public RemoteResponse<InAndOutWarehouseApplicationDetailVO> getInAndOutWarehouseInfoForOrder(OrderRequestVO request) {
        InAndOutWarehouseApplicationDetailVO result = inWareHouseGWService.getInAndOutWarehouseInfoForOrder(request);
        return RemoteResponse.<InAndOutWarehouseApplicationDetailVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "用户是否具有库房系统访问权限，没有首页不会显示库房系统入口")
    @RpcMapping("/haveWarehouseAccess")
    public RemoteResponse<Boolean> haveWarehouseAccess() {
        boolean result = inWareHouseGWService.haveWarehouseAccess();
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "用户是否能把当前订单提交入库，入参：orderNo")
    @RpcMapping("/checkCanSubmitWarehouse")
    public RemoteResponse<Boolean> checkCanSubmitWarehouse(OrderRequestVO request) {
        boolean result = inWareHouseGWService.checkCanSubmitWarehouse(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "检查商品是否已入库")
    @RpcMapping("/checkProductHasInWarehouse")
    public RemoteResponse<WarehouseProductInfoVO> checkProductHasInWarehouse(WarehouseProductRequestVO request) {
        WarehouseProductInfoVO result = inWareHouseGWService.checkProductHasInWarehouse(request);
        return RemoteResponse.<WarehouseProductInfoVO>custom().setSuccess().setData(result);
    }

    @RpcMethod(value = "查询出入库单信息", notes = "订单列表-重新生成出入库单据使用")
    @RpcMapping("/queryEntryExitByOrderNo")
    public RemoteResponse<List<BizWarehouseEntryExitDTO>> queryEntryExitByOrderNo(@RpcMethodParam(includePropertyNames = { "orderNo" }) OrderRequestVO request) {
        return RemoteResponse.success(inWareHouseGWService.queryEntryExitByOrderNo(request));
    }

}
