package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.vo.claim.DepartmentVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库申请常量列表，封装审批状态列表、入库状态列表、当前用户可用库房列表给前端")
public class WarehouseConstantListVO implements Serializable {

    private static final long serialVersionUID = -5839237240496152227L;

    @RpcModelProperty(value = "入库审批状态列表（0审批中，1审批通过，2审批驳回）")
    private List<WarehouseApprovalStatusVO> warehouseApprovalStatusVOList;

    @RpcModelProperty(value = "入库状态列表（未入库、已入库）")
    private List<WarehouseStatusVO> warehouseStatusVOList;

    @RpcModelProperty(value = "库房信息列表")
    private List<WarehouseVO> warehouseVOList;

    @RpcModelProperty(value = "当前账号的用户下的部门列表")
    private List<DepartmentVO> departmentVOList;

    public List<WarehouseApprovalStatusVO> getWarehouseApprovalStatusVOList() {
        return warehouseApprovalStatusVOList;
    }

    public void setWarehouseApprovalStatusVOList(List<WarehouseApprovalStatusVO> warehouseApprovalStatusVOList) {
        this.warehouseApprovalStatusVOList = warehouseApprovalStatusVOList;
    }

    public List<WarehouseStatusVO> getWarehouseStatusVOList() {
        return warehouseStatusVOList;
    }

    public void setWarehouseStatusVOList(List<WarehouseStatusVO> warehouseStatusVOList) {
        this.warehouseStatusVOList = warehouseStatusVOList;
    }

    public List<WarehouseVO> getWarehouseVOList() {
        return warehouseVOList;
    }

    public void setWarehouseVOList(List<WarehouseVO> warehouseVOList) {
        this.warehouseVOList = warehouseVOList;
    }

    public List<DepartmentVO> getDepartmentVOList() {
        return departmentVOList;
    }

    public void setDepartmentVOList(List<DepartmentVO> departmentVOList) {
        this.departmentVOList = departmentVOList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseConstantListVO{");
        sb.append("warehouseApprovalStatusVOList=").append(warehouseApprovalStatusVOList);
        sb.append(", warehouseStatusVOList=").append(warehouseStatusVOList);
        sb.append(", warehouseVOList=").append(warehouseVOList);
        sb.append(", departmentVOList=").append(departmentVOList);
        sb.append('}');
        return sb.toString();
    }
}
