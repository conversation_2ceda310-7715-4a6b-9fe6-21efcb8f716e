package com.ruijing.store.warehouse.service;

import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.warehouse.message.bean.OrderBean;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseDetailRequestVO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/6 21:56
 */
public interface OutWarehouseGWService {

    /**
     * 获取出库申请单详情
     * @param request
     * @return
     */
    OutWarehouseApplicationDetailVO getOutWarehouseApplicationDetail(OutWarehouseDetailRequestVO request);

    /**
     * 获取出库单展示对象，即入即出用
     * @param outWarehouseApplicationBean 出库单中间存储对象
     * @param orderBean 订单信息中间存储对象
     * @return
     */
    OutWarehouseApplicationDetailVO getImmediatelyOutWarehouseApplicationDetail(OutWarehouseApplicationBean outWarehouseApplicationBean, OrderBean orderBean);

    /**
     * 获取出库单中间存储对象，即入即出用
     * @param outWarehouseApplicationInfo 出库单信息
     * @param orderDetailSearchDTOList 出库单对应的订单详情信息
     * @param orgCode 出库单对应机构编码
     * @return
     */
    OutWarehouseApplicationBean getImmediatelyOutWarehouseApplicationBean(BizWarehouseExitDTO outWarehouseApplicationInfo, List<OrderDetailSearchDTO> orderDetailSearchDTOList, String orgCode);

    /**
     * @description: 获取出库单中间存储对象，即入即出用，且用于通用打印接口getCommonPrintData中
     * @date: 2021/3/16 19:00
     * @author: zengyanru
     * @param outWarehouseApplicationInfo
     * @param orgCode
     * @return com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean
     */
    OutWarehouseApplicationBean getImmediatelyOutWarehouseApplicationBean(BizWarehouseExitDTO outWarehouseApplicationInfo, String orgCode);
}
