package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库申请单分页查询返回信息")
public class WarehouseApplicationPageVO implements Serializable {

    private static final long serialVersionUID = 4766004410388751570L;

    @RpcModelProperty(value = "入库申请单列表")
    private List<WarehouseApplicationVO> warehouseApplicationVOList;

    @RpcModelProperty(value = "总条数")
    private Integer total;

    @RpcModelProperty(value = "总页数")
    private Integer totalPage;

    @RpcModelProperty(value = "当前页")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小")
    private Integer pageSize;

    public List<WarehouseApplicationVO> getWarehouseApplicationVOList() {
        return warehouseApplicationVOList;
    }

    public void setWarehouseApplicationVOList(List<WarehouseApplicationVO> warehouseApplicationVOList) {
        this.warehouseApplicationVOList = warehouseApplicationVOList;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseApplicationPageVO{");
        sb.append("warehouseApplicationVOList=").append(warehouseApplicationVOList);
        sb.append(", total=").append(total);
        sb.append(", totalPage=").append(totalPage);
        sb.append(", currentPage=").append(currentPage);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
