package com.ruijing.store.warehouse.message.enums;

/**
 * <AUTHOR>
 * @Date Created in 9:54 2020/10/22.
 * @Modified
 * @Description 入库申请单提交方式
 * 创建目的：需求“订单首次选择无需入库后需要重新入库，增加直接访问提交入库页面的特殊处理办法”
 */
public enum WarehouseApplicationSubmitWayEnum {

    /**
     * 正常的入库提交方式
     */
    NORMAL_SUBMIT_WAY(0, "正常的入库提交方式"),

    /**
     * 补救的入库提交方式
     */
    RESCUED_SUBMIT_WAY(1, "补救的入库提交方式");

    private Integer value;

    private String name;

    WarehouseApplicationSubmitWayEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static WarehouseApplicationSubmitWayEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (WarehouseApplicationSubmitWayEnum singleEnum : WarehouseApplicationSubmitWayEnum.values()) {
            if (singleEnum.getValue().equals(value)) {
                return singleEnum;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
