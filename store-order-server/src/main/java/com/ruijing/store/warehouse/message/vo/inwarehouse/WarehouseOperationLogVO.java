package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库单操作日志")
public class WarehouseOperationLogVO implements Serializable {

    private static final long serialVersionUID = -6355861717679557460L;

    @RpcModelProperty(value = "操作人")
    private String operator;

    @RpcModelProperty(value = "操作内容")
    private String operation;

    @RpcModelProperty(value = "操作时间")
    private Long operationTime;

    @RpcModelProperty(value = "备注")
    private String comment;

    @RpcModelProperty(value = "审批意见")
    private String approvalOpinion;

    @RpcModelProperty(value = "是否有电子签名 0否1是")
    private Integer sign;

    @RpcModelProperty(value = "电子签名图片")
    private String signPhoto;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Long operationTime) {
        this.operationTime = operationTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    public Integer getSign() {
        return sign;
    }

    public WarehouseOperationLogVO setSign(Integer sign) {
        this.sign = sign;
        return this;
    }

    public String getSignPhoto() {
        return signPhoto;
    }

    public WarehouseOperationLogVO setSignPhoto(String signPhoto) {
        this.signPhoto = signPhoto;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseOperationLogVO{");
        sb.append("operator='").append(operator).append('\'');
        sb.append(", operation='").append(operation).append('\'');
        sb.append(", operationTime=").append(operationTime);
        sb.append(", comment='").append(comment).append('\'');
        sb.append(", approvalOpinion='").append(approvalOpinion).append('\'');
        sb.append(", sign=").append(sign);
        sb.append(", signPhoto='").append(signPhoto).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
