package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.wms.api.dto.BizWarehouseIncompatibilityDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Name: SubmitWarehouseVO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/2/6
 */
@RpcModel(value = "提交出库单返回参数")
public class SubmitWarehouseVO implements Serializable {
    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "入库单信息")
    private List<BizWarehouseEntryVO> warehouseEntryVOList;

    @RpcModelProperty("配伍禁忌或提交入库时校验计量含量")
    public List<BizWarehouseIncompatibilityDTO> bizWarehouseIncompatibilityDTOList;

    public List<BizWarehouseEntryVO> getWarehouseEntryVOList() {
        return warehouseEntryVOList;
    }

    public void setWarehouseEntryVOList(List<BizWarehouseEntryVO> warehouseEntryVOList) {
        this.warehouseEntryVOList = warehouseEntryVOList;
    }

    public SubmitWarehouseVO setBizWarehouseIncompatibilityDTOList(List<BizWarehouseIncompatibilityDTO> bizWarehouseIncompatibilityDTOList) {
        this.bizWarehouseIncompatibilityDTOList = bizWarehouseIncompatibilityDTOList;
        return this;
    }

    public List<BizWarehouseIncompatibilityDTO> getBizWarehouseIncompatibilityDTOList() {
        return bizWarehouseIncompatibilityDTOList;
    }

    @Override
    public String toString() {
        return "SubmitWarehouseVO{" +
                "warehouseEntryVOList=" + warehouseEntryVOList +
                ", bizWarehouseIncompatibilityDTOList=" + bizWarehouseIncompatibilityDTOList +
                '}';
    }
}
