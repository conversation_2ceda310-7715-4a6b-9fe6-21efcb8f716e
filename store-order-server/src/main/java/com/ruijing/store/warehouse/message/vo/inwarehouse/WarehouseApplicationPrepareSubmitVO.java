package com.ruijing.store.warehouse.message.vo.inwarehouse;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.warehouse.message.bean.OrderBean;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="订单准备入库信息返回")
public class WarehouseApplicationPrepareSubmitVO implements Serializable {

    private static final long serialVersionUID = 3514943570629810511L;

    @RpcModelProperty(value = "订单Id")
    private Integer orderId;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单总金额")
    private Double totalPrice;

    @RpcModelProperty(value = "采购人姓名")
    private String purchaserName;

    @RpcModelProperty(value = "采购部门名称")
    private String departmentName;

    @RpcModelProperty(value = "库房列表")
    private List<WarehouseVO> warehouseVOList;

    @RpcModelProperty(value = "要入库的商品列表")
    private List<WarehouseProductInfoVO> warehouseProductInfoVOList;

    @RpcModelProperty(value = "订单收货图片")
    private List<String> receivingPhotos;

    /**
     * 经费卡信息，拼接的卡号卡名（从订单列表copy的）
     */
    @RpcModelProperty("经费卡信息，拼接的卡号卡名")
    private List<OrderFundcardVO> fundCard;

    @RpcModelProperty("是否一物一码")
    private Boolean eachProductEachCode;

    @RpcModelProperty("是否需要填写批次")
    private Boolean suppNeedFillBatchesData;

    public void populateOrder(OrderBean provider) {
        orderId = provider.getOrderId();
        orderNo = provider.getOrderNo();
        totalPrice = provider.getTotalPrice();
        purchaserName = provider.getPurchaserName();
        departmentName = provider.getDepartmentName();
        receivingPhotos = provider.getReceivingPhotos();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<WarehouseVO> getWarehouseVOList() {
        return warehouseVOList;
    }

    public void setWarehouseVOList(List<WarehouseVO> warehouseVOList) {
        this.warehouseVOList = warehouseVOList;
    }

    public List<WarehouseProductInfoVO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductInfoVO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public List<String> getReceivingPhotos() {
        return receivingPhotos;
    }

    public void setReceivingPhotos(List<String> receivingPhotos) {
        this.receivingPhotos = receivingPhotos;
    }

    public List<OrderFundcardVO> getFundCard() {
        return fundCard;
    }

    public void setFundCard(List<OrderFundcardVO> fundCard) {
        this.fundCard = fundCard;
    }

    public Boolean getEachProductEachCode() {
        return eachProductEachCode;
    }

    public WarehouseApplicationPrepareSubmitVO setEachProductEachCode(Boolean eachProductEachCode) {
        this.eachProductEachCode = eachProductEachCode;
        return this;
    }

    public Boolean getSuppNeedFillBatchesData() {
        return suppNeedFillBatchesData;
    }

    public WarehouseApplicationPrepareSubmitVO setSuppNeedFillBatchesData(Boolean suppNeedFillBatchesData) {
        this.suppNeedFillBatchesData = suppNeedFillBatchesData;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseApplicationPrepareSubmitVO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderNo='" + orderNo + "'")
                .add("totalPrice=" + totalPrice)
                .add("purchaserName='" + purchaserName + "'")
                .add("departmentName='" + departmentName + "'")
                .add("warehouseVOList=" + warehouseVOList)
                .add("warehouseProductInfoVOList=" + warehouseProductInfoVOList)
                .add("receivingPhotos=" + receivingPhotos)
                .add("fundCard=" + fundCard)
                .add("eachProductEachCode=" + eachProductEachCode)
                .add("suppNeedFillBatchesData=" + suppNeedFillBatchesData)
                .toString();
    }
}
