package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/3/14 9:40
 */
@RpcModel("库房管理——打印——审批日志")
public class WarehouseApprovalLogVO implements Serializable {

    private static final long serialVersionUID = 7624808424186819292L;

    @RpcModelProperty("审批描述")
    private String approveDesc;

    @RpcModelProperty("审批登记")
    private Integer approveLevel;

    @RpcModelProperty("审批状态")
    private Integer approveStatus;

    @RpcModelProperty("审批时间")
    private Long approveTime;

    @RpcModelProperty("审批人")
    private String approver;
    
    @RpcModelProperty("电子签名图片url")
    private String approveUrl;

    public String getApproveDesc() {
        return approveDesc;
    }

    public void setApproveDesc(String approveDesc) {
        this.approveDesc = approveDesc;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Long getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Long approveTime) {
        this.approveTime = approveTime;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getApproveUrl() {
        return approveUrl;
    }

    public void setApproveUrl(String approveUrl) {
        this.approveUrl = approveUrl;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseApprovalLogVO{");
        sb.append("approveDesc='").append(approveDesc).append('\'');
        sb.append(", approveLevel=").append(approveLevel);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", approveTime=").append(approveTime);
        sb.append(", approver='").append(approver).append('\'');
        sb.append(", approveUrl='").append(approveUrl).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
