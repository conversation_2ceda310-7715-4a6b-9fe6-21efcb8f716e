package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: huangyouwang
 * @Description: 申领人信息对象
 * @Date: 2024/08/20 17:41
 */

@RpcModel(description="申领人信息")
public class ClaimUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty(value = "第一申领人")
    private String firstApplicant;

    @RpcModelProperty(value = "第二申领人")
    private String secondApplicant;

    @RpcModelProperty(value = "第一使用人")
    private String firstUser;

    @RpcModelProperty(value = "第二使用人")
    private String secondUser;

    public String getFirstApplicant() {
        return firstApplicant;
    }

    public ClaimUserVO setFirstApplicant(String firstApplicant) {
        this.firstApplicant = firstApplicant;
        return this;
    }

    public String getSecondApplicant() {
        return secondApplicant;
    }

    public ClaimUserVO setSecondApplicant(String secondApplicant) {
        this.secondApplicant = secondApplicant;
        return this;
    }

    public String getFirstUser() {
        return firstUser;
    }

    public ClaimUserVO setFirstUser(String firstUser) {
        this.firstUser = firstUser;
        return this;
    }

    public String getSecondUser() {
        return secondUser;
    }

    public ClaimUserVO setSecondUser(String secondUser) {
        this.secondUser = secondUser;
        return this;
    }

    @Override
    public String toString() {
        return "ClaimUserVO{" +
                "firstApplicant='" + firstApplicant + '\'' +
                ", secondApplicant='" + secondApplicant + '\'' +
                ", firstUser='" + firstUser + '\'' +
                ", secondUser='" + secondUser + '\'' +
                '}';
    }
}
