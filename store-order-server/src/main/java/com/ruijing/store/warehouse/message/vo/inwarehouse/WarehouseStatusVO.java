package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库单状态枚举")
public class WarehouseStatusVO implements Serializable {

    private static final long serialVersionUID = 6525311680618290542L;

    @RpcModelProperty(value = "入库单状态（0未入库、1已入库、9已作废）")
    private  Integer status;

    @RpcModelProperty(value = "入库单状态（未入库、已入库、已作废）")
    private String statusName;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseStatusVO{");
        sb.append("status=").append(status);
        sb.append(", statusName='").append(statusName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
