package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单详情请求对象")
public class ClaimDetailRequestVO implements Serializable {

    private static final long serialVersionUID = 4717454454475281942L;

    @RpcModelProperty(value = "申领单Id", example = "")
    private Integer claimId;

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ClaimDetailRequestVO{");
        sb.append("claimId=").append(claimId);
        sb.append('}');
        return sb.toString();
    }
}
