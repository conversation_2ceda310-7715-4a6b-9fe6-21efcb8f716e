package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

@RpcModel("退货条形码商品详情")
public class GoodsReturnBarCodeDetailRequest implements Serializable {

    private static final long serialVersionUID = 6343764333230906836L;

    /**
     * 二维码, 长整型
     */
    @RpcModelProperty("二维码, 退货必填")
    private String barCode;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号, 退货必填")
    private String orderNo;

    /**
     * 订单明细id
     */
    @RpcModelProperty("订单明细id, 退货必填")
    private Integer orderDetailId;

    /**
     * 商品名
     */
    @RpcModelProperty("商品名")
    private String productName;

    /**
     * 商品货号
     */
    @RpcModelProperty("商品货号")
    private String productCode;

    /**
     * 规格
     */
    @RpcModelProperty("规格")
    private String spec;

    /**
     * 品牌
     */
    @RpcModelProperty("品牌")
    private String brand;

    /**
     * 批号
     */
    @RpcModelProperty("批号")
    private String batches;

    /**
     * 有效期
     */
    @RpcModelProperty("有效期")
    private String expiration;

    /**
     * 耐久度(外观) 0正常1破损
     */
    @RpcModelProperty("耐久度(外观) 0正常1破损")
    private Integer exterior;

    @RpcModelProperty("退货原因")
    private String reason;

    @RpcModelProperty("退货说明")
    private String description;

    @RpcModelProperty("退货金额")
    private BigDecimal price;

    @RpcModelProperty("商品图片")
    private String productPicturePath;

    public String getBarCode() {
        return barCode;
    }

    public GoodsReturnBarCodeDetailRequest setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public GoodsReturnBarCodeDetailRequest setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public GoodsReturnBarCodeDetailRequest setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public GoodsReturnBarCodeDetailRequest setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public GoodsReturnBarCodeDetailRequest setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getSpec() {
        return spec;
    }

    public GoodsReturnBarCodeDetailRequest setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public GoodsReturnBarCodeDetailRequest setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public GoodsReturnBarCodeDetailRequest setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public GoodsReturnBarCodeDetailRequest setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public GoodsReturnBarCodeDetailRequest setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public GoodsReturnBarCodeDetailRequest setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public GoodsReturnBarCodeDetailRequest setDescription(String description) {
        this.description = description;
        return this;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public GoodsReturnBarCodeDetailRequest setPrice(BigDecimal price) {
        this.price = price;
        return this;
    }

    public String getProductPicturePath() {
        return productPicturePath;
    }

    public GoodsReturnBarCodeDetailRequest setProductPicturePath(String productPicturePath) {
        this.productPicturePath = productPicturePath;
        return this;
    }
}
