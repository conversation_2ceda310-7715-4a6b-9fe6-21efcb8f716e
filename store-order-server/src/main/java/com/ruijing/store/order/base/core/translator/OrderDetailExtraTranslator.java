package com.ruijing.store.order.base.core.translator;

import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;

/**
 * <AUTHOR>
 * @description 订单详情额外信息转换器
 * @date 2023/7/18 14:44
 */
public class OrderDetailExtraTranslator {

    public static com.reagent.order.base.order.dto.OrderDetailExtraDTO toDTOForGalaxy(OrderDetailExtraDTO dto){
        com.reagent.order.base.order.dto.OrderDetailExtraDTO target = new com.reagent.order.base.order.dto.OrderDetailExtraDTO();
        target.setId(dto.getId());
        target.setOrgId(dto.getOrgId());
        target.setOrderId(dto.getOrderId());
        target.setOrderDetailId(dto.getOrderDetailId());
        target.setExtraKey(dto.getExtraKey());
        target.setExtraValue(dto.getExtraValue());
        target.setExtraKeyType(dto.getExtraKeyType());
        return target;
    }

    public static OrderDetailExtraDTO fromGalaxyDTO(com.reagent.order.base.order.dto.OrderDetailExtraDTO dto) {
        OrderDetailExtraDTO target = new OrderDetailExtraDTO();
        target.setId(dto.getId());
        target.setOrgId(dto.getOrgId());
        target.setOrderId(dto.getOrderId());
        target.setOrderDetailId(dto.getOrderDetailId());
        target.setExtraKey(dto.getExtraKey());
        target.setExtraValue(dto.getExtraValue());
        target.setExtraKeyType(dto.getExtraKeyType());
        return target;
    }
}
