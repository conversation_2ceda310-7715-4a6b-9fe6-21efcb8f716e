package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.business.service.OrderAnnotationService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.gateway.buyercenter.request.OrderTipsRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderTipsVO;
import com.ruijing.store.order.rpc.client.OrganizationClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/9/14 15:33
 */
@Service
public class OrderAnnotationServiceImpl implements OrderAnnotationService {

    /**
     * 结算超时提醒code
     */
    private static final String SETTLE_HINTS_CODE = "USER_SETTLE_HINTS";

    /**
     * 验收超时提醒code
     */
    private static final String ACCEPTANCE_HINTS_CODE = "USER_ACCEPTANCE_HINTS";

    /**
     * 需要验收超时提醒的单位
     */
    private static final List<Integer> ORG_ID_NEED_ACCEPTANCE_HINTS = New.list(OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE.getValue());

    private static final Integer TWO_DAYS_SECOND = 2 * 24 * 3600;

    private static final String CAT_TYPE = "OrderAnnotationServiceImpl";

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrganizationClient organizationClient;

    /**
     * 超时结算提醒
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<List<OrderTipsVO>> overtimeSettleHint(Integer orgId, OrderTipsRequest request) {
        Preconditions.notNull(request);
        Integer userId = request.getUserId();
        Integer type = request.getType();
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.USER_NOT_LOGGED_IN_TIMEOUT_ORDERS);

        List<OrderTipsVO> tipsVOS = New.list();
        OrderTipsVO settleHint = this.getIfSettleHintsAbsent(userId, type);
        OrderTipsVO acceptanceHint = this.getIfAcceptanceHintsAbsent(orgId, userId, type);
        if(settleHint != null){
            tipsVOS.add(settleHint);
        }
        if(acceptanceHint != null){
            tipsVOS.add(acceptanceHint);
        }

        return RemoteResponse.<List<OrderTipsVO>>custom().setData(tipsVOS).setSuccess().setTotal(tipsVOS.size());
    }

    /**
     * 点击记录到缓存
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<Boolean> clickHints(Integer orgId, OrderTipsRequest request) {
        Preconditions.notNull(request);
        Integer userId = request.getUserId();
        Integer type = request.getType();
        String code = request.getCode();
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.USER_NOT_LOGGED_IN_TIMEOUT_ORDERS);
        Preconditions.notNull(code, "获取订单超时结算提示，传入配置代码为空");

        try {
            cacheClient.putToCache(this.getCacheKey(orgId, userId, type, code), 1, TWO_DAYS_SECOND);
        } catch (Throwable e) {
            Cat.logError(CAT_TYPE, "clickHints", "订单超时结算提醒，用户点击记录存入Redis失败。"+e.getMessage(), e);
            return RemoteResponse.<Boolean>custom().setFailure(e.getMessage()).setData(false);
        }
        return RemoteResponse.<Boolean>custom().setData(true).setSuccess();
    }

    /**
     * 删除登录人的点击历史
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<Boolean> removeClickHistory(Integer orgId, OrderTipsRequest request) {
        Preconditions.notNull(request);
        Integer userId = request.getUserId();
        Integer type = request.getType();
        String code = request.getCode();
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.DELETE_CLICK_HISTORY_USER_ID_REQUIRED);
        Preconditions.notNull(code, "获取订单超时结算提示，传入配置代码为空");

        try {
            cacheClient.removeCache(this.getCacheKey(orgId, userId, type, code));
        } catch (Throwable e) {
            Cat.logError(CAT_TYPE, "removeClickHistory", "订单超时结算提醒，删除点击历史失败。"+e.getMessage(), e);
            return RemoteResponse.<Boolean>custom().setFailure(e.getMessage()).setData(false);
        }
        return RemoteResponse.<Boolean>custom().setData(true).setSuccess();
    }

    /**
     * 获取结算超时提醒
     * @param userId 用户id
     * @param type 提示来源
     * @return 提示信息
     */
    private OrderTipsVO getIfSettleHintsAbsent(Integer userId, Integer type){
        String cacheKey = this.getCacheKey(null, userId, type, SETTLE_HINTS_CODE);

        // redis有记录，不提示，返回空
        Boolean exists = cacheClient.exists(cacheKey);
        if (Boolean.TRUE.equals(exists)) {
            return null;
        }
        // redis无记录，查找是否有超时(目前写死7天)
        long totalHits = this.getOverTimeCount(null, userId, null, 7, 0, TimeOutEnums.BALANCE);
        if (totalHits <= 0) {
            return null;
        }
        String overtimeHints = "您好，您有" + totalHits + "个订单已超过7天尚未提交结算,请尽快前往处理.";
        OrderTipsVO orderTipsVO = new OrderTipsVO();
        orderTipsVO.setCode(SETTLE_HINTS_CODE);
        orderTipsVO.setContent(overtimeHints);
        return orderTipsVO;
    }

    /**
     *
     * @param orgId 单位id
     * @param userId 用户id
     * @param type 提示来源
     * @return 提示信息
     */
    private OrderTipsVO getIfAcceptanceHintsAbsent(Integer orgId, Integer userId, Integer type){
        if(!ORG_ID_NEED_ACCEPTANCE_HINTS.contains(orgId)){
            // 不是需要提示的单位不提示
            return null;
        }
        String cacheKey = this.getCacheKey(orgId, userId, type, ACCEPTANCE_HINTS_CODE);

        String orgCode = organizationClient.findSimpleOrgDTOById(orgId).getCode();
        if(orgCode == null){
            return null;
        }
        Map<String, Integer> configMap = orderManageService.getTimeOutConfigMap(orgCode);
        int acceptLimitDay = configMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode());
        // redis有记录，不提示，返回空
        Boolean exists = cacheClient.exists(cacheKey);
        if (Boolean.TRUE.equals(exists)) {
            return null;
        }
        // redis无记录，查找是否有超时
        long totalHits = this.getOverTimeCount(orgId, userId, null,0, acceptLimitDay, TimeOutEnums.EXAMINE);
        if (totalHits <= 0) {
            return null;
        }
        String overtimeHints = "您好，您有"+totalHits+"张订单已超过"+acceptLimitDay+"天尚未验收，请尽快前往处理";
        OrderTipsVO orderTipsVO = new OrderTipsVO();
        orderTipsVO.setCode(ACCEPTANCE_HINTS_CODE);
        orderTipsVO.setContent(overtimeHints);
        return orderTipsVO;
    }

    /**
     * 获取超时数量
     * @param orgId 单位id
     * @param userId 用户id
     * @param balanceLimitDays 配置的结算超时天数
     * @param acceptLimitDay 配置的验收超时天数
     * @param timeOutEnums 超时类型
     * @return 超时数量
     */
    public long getOverTimeCount(Integer orgId, Integer userId, List<Integer> departmentIdList, int balanceLimitDays, int acceptLimitDay, TimeOutEnums timeOutEnums){
        TimeOutOrderParamsDTO paramsDTO = new TimeOutOrderParamsDTO();
        paramsDTO.setUserId(orgId);
        paramsDTO.setBuyerUserId(userId);
        paramsDTO.setDepartmentIds(departmentIdList);
        paramsDTO.setPageSize(0);
        paramsDTO.setOverTimeType(timeOutEnums.getType());
        SearchPageResultDTO<OrderMasterSearchDTO> searchPageResultDTO = timeoutQueryService.queryTimeOutOrder(paramsDTO, balanceLimitDays, acceptLimitDay);
        return searchPageResultDTO.getTotalHits();
    }




    private String getCacheKey(Integer orgId, Integer userId, Integer type, String code){
        String cacheKey;
        if(SETTLE_HINTS_CODE.equals(code)){
            cacheKey = userId + "_" + type + "_" + code;
        }else {
            cacheKey = orgId + "_" + type + "_" + code;
        }
        return cacheKey;
    }
}
