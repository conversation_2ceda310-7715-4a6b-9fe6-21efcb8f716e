package com.ruijing.store.order.gateway.buyercenter.request.balance;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.business.bo.MyNotBalance.FundCardInfoBO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/8 11:12
 * @Description
 **/
@RpcModel("更新订单经费卡请求体")
public class UpdateFundCardForOrderRequest implements Serializable {

    private static final long serialVersionUID = -7927921907562132668L;
    /**
     * 经费卡信息
     */
    @RpcModelProperty("经费卡信息")
    private List<FundCardInfoBO> fundCards;

    /**
     * 订单id，非必传
     */
    @RpcModelProperty("订单id，非必传")
    private Integer orderId;

    /**
     * 单位code，非必传
     */
    @RpcModelProperty("单位code，非必传")
    private String orgCode;

    /**
     * 订单id列表
     */
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIds;

    public List<FundCardInfoBO> getFundCards() {
        return fundCards;
    }

    public void setFundCards(List<FundCardInfoBO> fundCards) {
        this.fundCards = fundCards;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<Integer> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UpdateFundCardForOrderRequest{");
        sb.append("fundCards=").append(fundCards);
        sb.append(", orderId=").append(orderId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", orderIds=").append(orderIds);
        sb.append('}');
        return sb.toString();
    }
}
