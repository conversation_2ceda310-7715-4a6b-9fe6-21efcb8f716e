package com.ruijing.store.order.gateway.buyercenter.vo.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhangzhifeng
 * @Date: 2021/06/28 11:10
 */
@RpcModel("合同-订单商品合同信息返回体")
public class OrderProductContractVO implements Serializable {
    private static final long serialVersionUID = -9100839301015957547L;

    /**
     * 订单id
     */
    @RpcModelProperty(value="订单id")
    private Integer orderId;

    /**
     * 采购单id
     */
    @RpcModelProperty(value="采购单id")
    private Integer buyAppId;

    /**
     * 合同编号
     */
    @RpcModelProperty(value="合同编号")
    private String contractNo;

    /**
     * 供应商id
     */
    @RpcModelProperty(value="供应商id")
    private Integer fsuppid;

    /**
     * 供应商代码
     */
    @RpcModelProperty(value="供应商代码")
    private String fsuppcode;

    /**
     * 供应商名称
     */
    @RpcModelProperty(value="供应商名称")
    private String fsuppname;

    /**
     * 商品合同信息
     */
    @RpcModelProperty(value="商品合同信息")
    private List<ProductContractVO> productContractList;


    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getBuyAppId() {
        return buyAppId;
    }

    public void setBuyAppId(Integer buyAppId) {
        this.buyAppId = buyAppId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public List<ProductContractVO> getProductContractList() {
        return productContractList;
    }

    public void setProductContractList(List<ProductContractVO> productContractList) {
        this.productContractList = productContractList;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public String getFsuppcode() {
        return fsuppcode;
    }

    public void setFsuppcode(String fsuppcode) {
        this.fsuppcode = fsuppcode;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderProductContractVO{");
        sb.append("orderId=").append(orderId);
        sb.append(", buyAppId=").append(buyAppId);
        sb.append(", contractNo=").append(contractNo);
        sb.append(", fsuppid=").append(fsuppid);
        sb.append(", fsuppcode=").append(fsuppcode);
        sb.append(", fsuppname=").append(fsuppname);
        sb.append(", productContractList=").append(productContractList);
        sb.append('}');
        return sb.toString();
    }
}