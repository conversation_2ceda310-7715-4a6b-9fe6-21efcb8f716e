package com.ruijing.store.order.rpc.callback;

import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.fundcard.api.FundCardCallbackRPCService;
import com.reagent.research.fundcard.dto.ChangeFundCardCallbackResult;
import com.reagent.research.fundcard.dto.FreezeCallbackResult;
import com.reagent.research.fundcard.dto.FundCardCompleteCallbackResult;
import com.reagent.research.fundcard.dto.UnfreezeCallbackResult;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateFundStatusReqDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.order.rpc.client.CategoryDirectoryClient;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@MSharpService
public class FundCardCallbackRpcServiceImpl implements FundCardCallbackRPCService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "FundCardCallbackRpcServiceImpl";

    /**
     * 失败原因最大长度
     */
    private static final int FAIL_REASON_LENGTH_LIMIT = 500;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private OrderMasterCommonService orderMasterCommonService;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private CategoryDirectoryClient categoryDirectoryClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "经费卡冻结接口回调")
    public RemoteResponse handleFrozenReuslt(CallbackRequest<FreezeCallbackResult> request) {
        Preconditions.notNull(request.getData(), "回调失败, 出参为空");
        String orderNo = request.getData().getSerialNumber();
        Preconditions.notNull(orderNo, "回调失败, 流水单号为空");
        UpdateFundStatusReqDTO updateFundStatusReqDTO = new UpdateFundStatusReqDTO();
        updateFundStatusReqDTO.setOrderMasterNo(orderNo);
        logger.info("订单冻结回调:" + JsonUtils.toJsonIgnoreNull(request));
        if (request.isSuccess()) {
            updateFundStatusReqDTO.setFailReason(StringUtils.EMPTY);
            updateFundStatusReqDTO.setFundStatus(OrderFundStatusEnum.Freezed.getValue());

            DockingExtra dockingExtra = new DockingExtra();
            dockingExtra.setType(DockingTypeEnum.Order.getValue());
            dockingExtra.setInfo(orderNo);
            dockingExtra.setExtraInfo(request.getData().getExtraSerialNumber());
            dockingExtra.setStatusextra(DockingPushStatusEnum.SUCCESS.getCode());
            dockingExtraService.saveOrUpdateDockingExtra(dockingExtra);
        } else {
            updateFundStatusReqDTO.setFailReason(StringUtils.truncate(request.getMsg(), FAIL_REASON_LENGTH_LIMIT));
            updateFundStatusReqDTO.setFundStatus(OrderFundStatusEnum.FreezedFail.getValue());
        }
        orderMasterCommonService.updateFundStatusByOrderNo(updateFundStatusReqDTO);
        if(!request.isSuccess()){
            // 冻结失败，政采统计回滚
            Optional<ExtraDTO> ctgDirOnOptional = request.getData().getExtraDTOs().stream().filter(item->"isCategoryDirectoryOn".equals(item.getField())).findFirst();
            boolean isCategoryDirectoryOn = ctgDirOnOptional.filter(dto -> CommonValueUtils.parseNumberStrToBoolean(dto.getValue())).isPresent();
            if(isCategoryDirectoryOn){
                OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
                categoryDirectoryClient.cancelOrderStatistics(orderMasterDO.getFuserid(), orderMasterDO.getId());
            }
        }
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    /**
     * 解冻回调
     * @param callbackRequest
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "经费卡解冻接口回调")
    public RemoteResponse handleUnfrozenReuslt(CallbackRequest<UnfreezeCallbackResult> callbackRequest) {
        orderFundCardService.unFrozenCallback(callbackRequest);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 换卡回调
     * @param callbackRequest
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "经费卡换卡接口回调")
    public RemoteResponse handleChangeFundCardReuslt(CallbackRequest<ChangeFundCardCallbackResult> callbackRequest) {
        logger.info("换卡回调，{}",JsonUtils.toJson(callbackRequest));
        Cat.logEvent(CAT_TYPE,"handleChangeFundCardReuslt","换卡回调","");
        orderManageService.changeFundCardCallBack(callbackRequest);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 扣减回调
     * @param request
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "经费卡扣减接口回调")
    public RemoteResponse<Boolean> handleFundCardCompleteCallbackResult(CallbackRequest<FundCardCompleteCallbackResult> request) {
        Preconditions.notNull(request.getData(), "回调失败, 出参为空");
        String orderNo = request.getData().getSerialNumbers().get(0);
        Preconditions.notNull(orderNo, "回调失败, 流水单号为空");
        UpdateFundStatusReqDTO updateFundStatusReqDTO = new UpdateFundStatusReqDTO();
        updateFundStatusReqDTO.setOrderMasterNo(orderNo);
        logger.info("订单扣减回调:" + JsonUtils.toJsonIgnoreNull(request));
        if (request.isSuccess()) {
            updateFundStatusReqDTO.setFailReason(StringUtils.EMPTY);
            updateFundStatusReqDTO.setFundStatus(OrderFundStatusEnum.Deducted.getValue());
        } else {
            updateFundStatusReqDTO.setFailReason(StringUtils.truncate(request.getMsg(), FAIL_REASON_LENGTH_LIMIT));
            updateFundStatusReqDTO.setFundStatus(OrderFundStatusEnum.DEDUCT_FAILED.getValue());
        }
        orderMasterCommonService.updateFundStatusByOrderNo(updateFundStatusReqDTO);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }
}
