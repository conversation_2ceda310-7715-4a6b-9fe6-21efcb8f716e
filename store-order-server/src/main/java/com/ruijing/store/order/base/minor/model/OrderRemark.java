package com.ruijing.store.order.base.minor.model;

import java.util.Date;

public class OrderRemark {
    /**
    * 采购单id
    */
    private Integer ftbuyappid;

    /**
    * 供应商id
    */
    private Integer fsuppid;

    /**
    * 备注
    */
    private String remark;

    private Date creationTime;

    private Date updateTime;

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ftbuyappid=").append(ftbuyappid);
        sb.append(", fsuppid=").append(fsuppid);
        sb.append(", remark=").append(remark);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}