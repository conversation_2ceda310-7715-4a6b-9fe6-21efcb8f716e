package com.ruijing.store.order.gateway.buyercenter.request.orderlist;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptPictureDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.PictureDTO;

import java.io.Serializable;
import java.util.List;


@Model("覆盖上传图片请求体")
public class OverrideUploadImageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单id")
    private Integer orderId;

    @ModelProperty(value = "关联订单图片集合",description = "订单维度")
    private List<PictureDTO> pictureDTOList;

    @RpcModelProperty(value = "关联商品图片集合",description = "商品维度")
    private List<AcceptPictureDTO> detailPictureDTOList;

    public Integer getOrderId() {
        return orderId;
    }

    public OverrideUploadImageRequest setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public List<PictureDTO> getPictureDTOList() {
        return pictureDTOList;
    }

    public OverrideUploadImageRequest setPictureDTOList(List<PictureDTO> pictureDTOList) {
        this.pictureDTOList = pictureDTOList;
        return this;
    }

    public List<AcceptPictureDTO> getDetailPictureDTOList() {
        return detailPictureDTOList;
    }

    public OverrideUploadImageRequest setDetailPictureDTOList(List<AcceptPictureDTO> detailPictureDTOList) {
        this.detailPictureDTOList = detailPictureDTOList;
        return this;
    }

    @Override
    public String toString() {
        return "OverrideUploadImageRequest{" +
                "orderId=" + orderId +
                ", pictureDTOList=" + pictureDTOList +
                ", detailPictureDTOList=" + detailPictureDTOList +
                '}';
    }
}
