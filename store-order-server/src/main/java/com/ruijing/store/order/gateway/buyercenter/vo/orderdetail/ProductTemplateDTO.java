package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-05-05 14:31
 * @description:
 **/
public class ProductTemplateDTO implements Serializable {

    private static final long serialVersionUID = -3210772519769770919L;

    @RpcModelProperty("素材类型（1,图片;2,文本）")
    private Integer materialType;

    @RpcModelProperty("素材内容（文本、图片链接）")
    private String materialContent;

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    public String getMaterialContent() {
        return materialContent;
    }

    public void setMaterialContent(String materialContent) {
        this.materialContent = materialContent;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ProductTemplateDTO.class.getSimpleName() + "[", "]")
                .add("materialType=" + materialType)
                .add("materialContent='" + materialContent + "'")
                .toString();
    }
}
