package com.ruijing.store.order.rpc.client;

import com.reagent.research.reimbursement.api.ReimbursementConfigRPCService;
import com.reagent.research.reimbursement.dto.ReimbursementConfigDTO;
import com.reagent.research.reimbursement.exit.api.ExitAccountRpcService;
import com.reagent.research.reimbursement.exit.dto.ExitAccountRequestDTO;
import com.reagent.research.reimbursement.exit.dto.ExitAccountUpdateDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-11-06 14:32
 * @description:
 **/
@ServiceClient
public class ReimbursementClient {

    @MSharpReference
    private ReimbursementConfigRPCService reimbursementConfigRpcService;

    @MSharpReference
    private ExitAccountRpcService exitAccountRpcService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<ReimbursementConfigDTO> getUsableReimbursementConfig(){
        RemoteResponse<List<ReimbursementConfigDTO>> remoteResponse = reimbursementConfigRpcService.getUsableReimbursementConfig();
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT, description = "绿盾对接-保存出账信息")
    public void saveExitAccountList(List<ExitAccountRequestDTO> list){
        RemoteResponse<Boolean> response = exitAccountRpcService.saveExitAccountList(list);
        Preconditions.isTrue(response.isSuccess() && Boolean.TRUE.equals(response.getData()), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT, description = "绿盾对接-更新出账基本信息")
    public void updateExitAccountList(List<ExitAccountUpdateDTO> list){
        RemoteResponse<Boolean> response = exitAccountRpcService.updateExitAccountList(list);
        Preconditions.isTrue(response.isSuccess() && Boolean.TRUE.equals(response.getData()), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT, description = "绿盾对接-保存出账信息")
    public void updateExitAccountEnabledList(List<ExitAccountUpdateDTO> list){
        RemoteResponse<Boolean> response = exitAccountRpcService.updateExitAccountEnabledList(list);
        Preconditions.isTrue(response.isSuccess() && Boolean.TRUE.equals(response.getData()), response.getMsg());
    }
}
