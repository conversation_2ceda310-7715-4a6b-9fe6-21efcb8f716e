package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;

/**
 * <AUTHOR>
 * @date 2022/6/17 10:45
 * @description
 */
public class ElectronicSignBO {

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 采购部门id
     */
    private Integer departmentId;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 操作
     */
    private ElectronicSignatureOperationEnum operation;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public ElectronicSignatureOperationEnum getOperation() {
        return operation;
    }

    public void setOperation(ElectronicSignatureOperationEnum operation) {
        this.operation = operation;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ElectronicSignBO{");
        sb.append("orderId=").append(orderId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", departmentId=").append(departmentId);
        sb.append(", userGuid='").append(userGuid).append('\'');
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", password='").append(password).append('\'');
        sb.append(", operation=").append(operation);
        sb.append('}');
        return sb.toString();
    }
}
