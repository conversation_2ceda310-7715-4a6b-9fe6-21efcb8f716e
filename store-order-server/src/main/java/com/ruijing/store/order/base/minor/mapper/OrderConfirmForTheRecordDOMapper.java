package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 15:21
 * @Version 1.0
 * @Desc:描述
 */
public interface OrderConfirmForTheRecordDOMapper {
    int deleteByPrimaryKey(String id);

    int insert(OrderConfirmForTheRecordDO record);

    int insertSelective(OrderConfirmForTheRecordDO record);

    OrderConfirmForTheRecordDO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(OrderConfirmForTheRecordDO record);

    int updateByPrimaryKey(OrderConfirmForTheRecordDO record);

    /**
     * 根据订单id查询订单备案记录
     * @param orderIdCollection
     * @return
     */
    List<OrderConfirmForTheRecordDO> findByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * 根据orderId计数是否存在记录
     * @param orderId
     * @return
     */
    Integer countByOrderId(@Param("orderId")Integer orderId);

    /**
     * 根据订单id更新备案信息
     * @param updated
     * @return
     */
    int updateByOrderId(@Param("updated")OrderConfirmForTheRecordDO updated);

}