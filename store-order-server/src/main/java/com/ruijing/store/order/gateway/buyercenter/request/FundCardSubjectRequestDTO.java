package com.ruijing.store.order.gateway.buyercenter.request;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/22 16:53
 **/
public class FundCardSubjectRequestDTO implements Serializable {

    private static final long serialVersionUID = -7678565640820855812L;

    /**
     * 科目信息id
     */
    @RpcModelProperty("科目信息id")
    private String id;

    /**
     * 科目编码
     */
    @RpcModelProperty("科目编码")
    private String subjectCode;

    /**
     * 经费卡使用金额，只有眼科在使用
     */
    @RpcModelProperty("经费卡使用金额，只有眼科在使用")
    private BigDecimal useAmount;

    @RpcModelProperty("经费支出申请单号")
    private String expenseApplyNo;

    public String getSubjectCode() {
        return subjectCode;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }

    @Override
    public String toString() {
        return "FundCardSubjectRequestDTO{" +
                "id='" + id + '\'' +
                ", subjectCode='" + subjectCode + '\'' +
                ", useAmount=" + useAmount +
                ", expenseApplyNo='" + expenseApplyNo + '\'' +
                '}';
    }
}
