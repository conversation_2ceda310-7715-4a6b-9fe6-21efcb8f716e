package com.ruijing.store.order.base.core.model;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@RpcModel(value="com.ruijing.store.order.base.pojo.OrderMasterDO")
public class OrderMasterDO {
    /**
    * 
    */
    @RpcModelProperty(value="")
    private Integer id;

    /**
    * 订单id
    */
    @RpcModelProperty(value="订单id")
    private String fmasterguid;

    /**
    * 采购申请id
    */
    @RpcModelProperty(value="采购申请id")
    private Integer ftbuyappid;

    /**
    * 订单号
    */
    @RpcModelProperty(value="")
    private String forderno;

    /**
    * 订单签订日期
    */
    @RpcModelProperty(value="订单签订日期")
    private Date forderdate;

    /**
    * 购买者id
    */
    @RpcModelProperty(value="购买者id")
    private Integer fbuyerid;

    /**
    * 购买者代码
    */
    @RpcModelProperty(value="购买者代码")
    private String fbuyercode;

    /**
    * 购买者名称
    */
    @RpcModelProperty(value="购买者名称")
    private String fbuyername;

    /**
    * 采购人电邮
    */
    @RpcModelProperty(value="采购人电邮")
    private String fbuyeremail;

    /**
    * 采购联系人
    */
    @RpcModelProperty(value="采购联系人")
    private String fbuyercontactman;

    /**
    * 采购联系电话
    */
    @RpcModelProperty(value="采购联系电话")
    private String fbuyertelephone;

    /**
    * 采购部门id
    */
    @RpcModelProperty(value="采购部门id")
    private Integer fbuydepartmentid;

    /**
    * 采购部门
    */
    @RpcModelProperty(value="采购部门")
    private String fbuydepartment;

    /**
    * 供应商id
    */
    @RpcModelProperty(value="供应商id")
    private Integer fsuppid;

    /**
    * 供应商代码
    */
    @RpcModelProperty(value="供应商代码")
    private String fsuppcode;

    /**
    * 供应商名称
    */
    @RpcModelProperty(value="供应商名称")
    private String fsuppname;

    /**
    * 送货地点
    */
    @RpcModelProperty(value="送货地点")
    private String fbiderdeliveryplace;

    /**
    * 订单总价格
    */
    @RpcModelProperty(value="订单总价格")
    private BigDecimal forderamounttotal;

    /**
    * 经费状态：1、冻结成功，2、释放中，3、释放失败，4、释放成功5，扣减成功
    */
    @RpcModelProperty(value="经费状态：1、冻结成功，2、释放中，3、释放失败，4、释放成功5，扣减成功")
    private Integer fundStatus;

    /**
    * 经费释放失败原因
    */
    @RpcModelProperty(value="经费释放失败原因")
    private String failedReason;

    /**
    * 状态
    */
    @RpcModelProperty(value="状态")
    private Integer status;

    /**
    * 确认日期
    */
    @RpcModelProperty(value="确认日期")
    private Date fconfirmdate;

    /**
    * 确认人id
    */
    @RpcModelProperty(value="确认人id")
    private String fconfirmmanid;

    /**
    * 确认人
    */
    @RpcModelProperty(value="确认人")
    private String fconfirmman;

    /**
    * 撤消日期
    */
    @RpcModelProperty(value="撤消日期")
    private Date fcanceldate;

    /**
    * 撤消人id
    */
    @RpcModelProperty(value="撤消人id")
    private String fcancelmanid;

    /**
    * 撤消人
    */
    @RpcModelProperty(value="撤消人")
    private String fcancelman;

    /**
    * 送货日期
    */
    @RpcModelProperty(value="送货日期")
    private Date fdeliverydate;

    /**
    * 送货id
    */
    @RpcModelProperty(value="送货id")
    private String fdeliverymanid;

    /**
    * 送货人
    */
    @RpcModelProperty(value="送货人")
    private String fdeliveryman;

    /**
    * 最后收货日期
    */
    @RpcModelProperty(value="最后收货日期")
    private Date flastreceivedate;

    /**
    * 最后收货id
    */
    @RpcModelProperty(value="最后收货id")
    private String flastreceivemanid;

    /**
    * 最后收货人
    */
    @RpcModelProperty(value="最后收货人")
    private String flastreceiveman;

    /**
    * 评价日期
    */
    @RpcModelProperty(value="评价日期")
    private Date fassessdate;

    /**
    * 评价id
    */
    @RpcModelProperty(value="评价id")
    private String fassessmanid;

    /**
    * 评价人
    */
    @RpcModelProperty(value="评价人")
    private String fassessman;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String piemail;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String projectid;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String projectnumber;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String projecttitle;


    /**
    * 
    */
    @RpcModelProperty(value="")
    private Integer fuserid;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String fusercode;

    /**
    * 
    */
    @RpcModelProperty(value="")
    private String fusername;


    /**
    * 结算单id
    */
    @RpcModelProperty(value="结算单id")
    private Integer statementId;

    /**
    * 撤销原因
    */
    @RpcModelProperty(value="撤销原因")
    private String fcancelreason;

    /**
    * 拒绝取消原因
    */
    @RpcModelProperty(value="拒绝取消原因")
    private String frefuseCancelReason;

    /**
    * 订单关闭时间
    */
    @RpcModelProperty(value="订单关闭时间")
    private Date shutDownDate;

    /**
    * 发货说明
    */
    @RpcModelProperty(value="发货说明")
    private String deliveryInfo;

    /**
    * 发货单号
    */
    @RpcModelProperty(value="发货单号")
    private String deliveryNo;

    /**
    * 退货金额
    */
    @RpcModelProperty(value="退货金额")
    private Double returnAmount;

    /**
    * 拒绝撤销时间
    */
    @RpcModelProperty(value="拒绝撤销时间")
    private Date frefuseCancelDate;

    /**
    * 地址id
    */
    @RpcModelProperty(value="地址id")
    private Integer fdeliveryid;

    /**
    * 竞价单id
    */
    @RpcModelProperty(value="竞价单id")
    private String bidOrderId;

    /**
    * order类型,0:采购的,1:竞价单
    */
    @RpcModelProperty(value="order类型,0:采购的,1:竞价单")
    private Integer orderType;

    /**
    * 验收时图片
    */
    @RpcModelProperty(value="验收时图片")
    private String receivePicUrls;

    /**
    * 项目记录id
    */
    @RpcModelProperty(value="项目记录id")
    private String tpiProjectId;

    /**
    * 订单原价
    */
    @RpcModelProperty(value="订单原价")
    private BigDecimal originalAmount;

    /**
    * 出入库状态
    */
    @RpcModelProperty(value="出入库状态")
    private Byte inventoryStatus;

    /**
    * 流程种类 0:正常, 1:线下
    */
    @RpcModelProperty(value="流程种类 0:正常, 1:线下")
    private Byte species;

    /**
    * 更新时间
    */
    @RpcModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * 结算中开始时间
    */
    @RpcModelProperty(value="结算中开始时间")
    private Date inStateTime;

    /**
    * 采购来源：0 PC；1 小程序
    */
    @RpcModelProperty(value="采购来源：0 PC；1 小程序")
    private Integer purchaseRootinType;

    /**
    * 运费
    */
    @RpcModelProperty(value="运费")
    private BigDecimal carryFee;

    /**
     * 发票抬头id
     */
    @RpcModelProperty(value="发票抬头id")
    private Integer invoiceTitleId;

    /**
     * 发票抬头名称
     */
    @RpcModelProperty(value="发票抬头名称")
    private String invoiceTitle;

    /**
     * 发票抬头号
     */
    @RpcModelProperty(value="发票抬头号")
    private String invoiceTitleNumber;

    @RpcModelProperty(value="经费类型")
    private Integer fundType;

    @RpcModelProperty(value="经费类型描述")
    private String fundTypeName;

    @RpcModelProperty(value="订单实付金额=订单总金额-退货金额")
    private BigDecimal paymentAmount;

    @RpcModelProperty(value="结算子状态")
    private Integer statementStatus;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 审批流id
     */
    @RpcModelProperty(value = "审批流id")
    private Integer flowId;

    @RpcModelProperty(value = "学院id")
    private Integer deptParentId;

    @RpcModelProperty(value = "学院名称")
    private String deptParentName;

    /**
     * 完成时间
     */
    private Date finishDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFmasterguid() {
        return fmasterguid;
    }

    public void setFmasterguid(String fmasterguid) {
        this.fmasterguid = fmasterguid;
    }

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public String getForderno() {
        return forderno;
    }

    public void setForderno(String forderno) {
        this.forderno = forderno;
    }

    public Date getForderdate() {
        return forderdate;
    }

    public void setForderdate(Date forderdate) {
        this.forderdate = forderdate;
    }

    public Integer getFbuyerid() {
        return fbuyerid;
    }

    public void setFbuyerid(Integer fbuyerid) {
        this.fbuyerid = fbuyerid;
    }

    public String getFbuyercode() {
        return fbuyercode;
    }

    public void setFbuyercode(String fbuyercode) {
        this.fbuyercode = fbuyercode;
    }

    public String getFbuyername() {
        return fbuyername;
    }

    public String getFbuyeremail() {
        return fbuyeremail;
    }

    public void setFbuyeremail(String fbuyeremail) {
        this.fbuyeremail = fbuyeremail;
    }

    public void setFbuyername(String fbuyername) {
        this.fbuyername = fbuyername;
    }

    public String getFbuyercontactman() {
        return fbuyercontactman;
    }

    public void setFbuyercontactman(String fbuyercontactman) {
        this.fbuyercontactman = fbuyercontactman;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public void setFbuyertelephone(String fbuyertelephone) {
        this.fbuyertelephone = fbuyertelephone;
    }

    public String getFbuyertelephone() {
        return fbuyertelephone;
    }

    public Integer getFbuydepartmentid() {
        return fbuydepartmentid;
    }

    public void setFbuydepartmentid(Integer fbuydepartmentid) {
        this.fbuydepartmentid = fbuydepartmentid;
    }

    public String getFsuppcode() {
        return fsuppcode;
    }

    public void setFsuppcode(String fsuppcode) {
        this.fsuppcode = fsuppcode;
    }

    public String getFbuydepartment() {
        return fbuydepartment;
    }

    public void setFbuydepartment(String fbuydepartment) {
        this.fbuydepartment = fbuydepartment;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    public String getFbiderdeliveryplace() {
        return fbiderdeliveryplace;
    }

    public void setFbiderdeliveryplace(String fbiderdeliveryplace) {
        this.fbiderdeliveryplace = fbiderdeliveryplace;
    }

    public BigDecimal getForderamounttotal() {
        return forderamounttotal;
    }

    public void setForderamounttotal(BigDecimal forderamounttotal) {
        this.forderamounttotal = forderamounttotal;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getFconfirmdate() {
        return fconfirmdate;
    }

    public void setFconfirmdate(Date fconfirmdate) {
        this.fconfirmdate = fconfirmdate;
    }

    public String getFconfirmmanid() {
        return fconfirmmanid;
    }

    public void setFconfirmmanid(String fconfirmmanid) {
        this.fconfirmmanid = fconfirmmanid;
    }

    public String getFconfirmman() {
        return fconfirmman;
    }

    public void setFconfirmman(String fconfirmman) {
        this.fconfirmman = fconfirmman;
    }

    public Date getFcanceldate() {
        return fcanceldate;
    }

    public void setFcanceldate(Date fcanceldate) {
        this.fcanceldate = fcanceldate;
    }

    public String getFcancelmanid() {
        return fcancelmanid;
    }

    public void setFcancelmanid(String fcancelmanid) {
        this.fcancelmanid = fcancelmanid;
    }

    public String getFcancelman() {
        return fcancelman;
    }

    public void setFcancelman(String fcancelman) {
        this.fcancelman = fcancelman;
    }

    public Date getFdeliverydate() {
        return fdeliverydate;
    }

    public void setFdeliverydate(Date fdeliverydate) {
        this.fdeliverydate = fdeliverydate;
    }

    public String getFdeliverymanid() {
        return fdeliverymanid;
    }

    public void setFdeliverymanid(String fdeliverymanid) {
        this.fdeliverymanid = fdeliverymanid;
    }

    public String getFdeliveryman() {
        return fdeliveryman;
    }

    public void setFdeliveryman(String fdeliveryman) {
        this.fdeliveryman = fdeliveryman;
    }

    public Date getFlastreceivedate() {
        return flastreceivedate;
    }

    public void setFlastreceivedate(Date flastreceivedate) {
        this.flastreceivedate = flastreceivedate;
    }

    public String getFlastreceivemanid() {
        return flastreceivemanid;
    }

    public void setFlastreceivemanid(String flastreceivemanid) {
        this.flastreceivemanid = flastreceivemanid;
    }

    public String getFlastreceiveman() {
        return flastreceiveman;
    }

    public void setFlastreceiveman(String flastreceiveman) {
        this.flastreceiveman = flastreceiveman;
    }

    public Date getFassessdate() {
        return fassessdate;
    }

    public void setFassessdate(Date fassessdate) {
        this.fassessdate = fassessdate;
    }

    public String getFassessmanid() {
        return fassessmanid;
    }

    public void setFassessmanid(String fassessmanid) {
        this.fassessmanid = fassessmanid;
    }

    public String getFassessman() {
        return fassessman;
    }

    public void setFassessman(String fassessman) {
        this.fassessman = fassessman;
    }

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }

    public String getProjectnumber() {
        return projectnumber;
    }

    public void setProjectnumber(String projectnumber) {
        this.projectnumber = projectnumber;
    }

    public String getProjecttitle() {
        return projecttitle;
    }

    public void setProjecttitle(String projecttitle) {
        this.projecttitle = projecttitle;
    }

    public Integer getFuserid() {
        return fuserid;
    }

    public void setFuserid(Integer fuserid) {
        this.fuserid = fuserid;
    }

    public String getFusercode() {
        return fusercode;
    }

    public void setFusercode(String fusercode) {
        this.fusercode = fusercode;
    }

    public String getFusername() {
        return fusername;
    }

    public void setFusername(String fusername) {
        this.fusername = fusername;
    }

    public String getFcancelreason() {
        return fcancelreason;
    }

    public void setFcancelreason(String fcancelreason) {
        this.fcancelreason = fcancelreason;
    }

    public String getFrefuseCancelReason() {
        return frefuseCancelReason;
    }

    public void setFrefuseCancelReason(String frefuseCancelReason) {
        this.frefuseCancelReason = frefuseCancelReason;
    }

    public Date getShutDownDate() {
        return shutDownDate;
    }

    public void setShutDownDate(Date shutDownDate) {
        this.shutDownDate = shutDownDate;
    }

    public String getDeliveryInfo() {
        return deliveryInfo;
    }

    public void setDeliveryInfo(String deliveryInfo) {
        this.deliveryInfo = deliveryInfo;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Date getFrefuseCancelDate() {
        return frefuseCancelDate;
    }

    public void setFrefuseCancelDate(Date frefuseCancelDate) {
        this.frefuseCancelDate = frefuseCancelDate;
    }

    public Integer getFdeliveryid() {
        return fdeliveryid;
    }

    public void setFdeliveryid(Integer fdeliveryid) {
        this.fdeliveryid = fdeliveryid;
    }

    public String getBidOrderId() {
        return bidOrderId;
    }

    public void setBidOrderId(String bidOrderId) {
        this.bidOrderId = bidOrderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getReceivePicUrls() {
        return receivePicUrls;
    }

    public void setReceivePicUrls(String receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public String getTpiProjectId() {
        return tpiProjectId;
    }

    public void setTpiProjectId(String tpiProjectId) {
        this.tpiProjectId = tpiProjectId;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Byte getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Byte inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public Byte getSpecies() {
        return species;
    }

    public void setSpecies(Byte species) {
        this.species = species;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getInStateTime() {
        return inStateTime;
    }

    public void setInStateTime(Date inStateTime) {
        this.inStateTime = inStateTime;
    }

    public Integer getPurchaseRootinType() {
        return purchaseRootinType;
    }

    public void setPurchaseRootinType(Integer purchaseRootinType) {
        this.purchaseRootinType = purchaseRootinType;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public void setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public Integer getInvoiceTitleId() {
        return invoiceTitleId;
    }

    public void setInvoiceTitleId(Integer invoiceTitleId) {
        this.invoiceTitleId = invoiceTitleId;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceTitleNumber() {
        return invoiceTitleNumber;
    }

    public void setInvoiceTitleNumber(String invoiceTitleNumber) {
        this.invoiceTitleNumber = invoiceTitleNumber;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public String getFundTypeName() {
        return fundTypeName;
    }

    public void setFundTypeName(String fundTypeName) {
        this.fundTypeName = fundTypeName;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OrderMasterDO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Integer getFlowId() {
        return flowId;
    }

    public void setFlowId(Integer flowId) {
        this.flowId = flowId;
    }

    public Integer getDeptParentId() {
        return deptParentId;
    }

    public void setDeptParentId(Integer deptParentId) {
        this.deptParentId = deptParentId;
    }

    public String getDeptParentName() {
        return deptParentName;
    }

    public void setDeptParentName(String deptParentName) {
        this.deptParentName = deptParentName;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    @Override
    public String toString() {
        return "OrderMasterDO{" +
                "id=" + id +
                ", fmasterguid='" + fmasterguid + '\'' +
                ", ftbuyappid=" + ftbuyappid +
                ", forderno='" + forderno + '\'' +
                ", forderdate=" + forderdate +
                ", fbuyerid=" + fbuyerid +
                ", fbuyercode='" + fbuyercode + '\'' +
                ", fbuyername='" + fbuyername + '\'' +
                ", fbuyeremail='" + fbuyeremail + '\'' +
                ", fbuyercontactman='" + fbuyercontactman + '\'' +
                ", fbuyertelephone='" + fbuyertelephone + '\'' +
                ", fbuydepartmentid=" + fbuydepartmentid +
                ", fbuydepartment='" + fbuydepartment + '\'' +
                ", fsuppid=" + fsuppid +
                ", fsuppcode='" + fsuppcode + '\'' +
                ", fsuppname='" + fsuppname + '\'' +
                ", fbiderdeliveryplace='" + fbiderdeliveryplace + '\'' +
                ", forderamounttotal=" + forderamounttotal +
                ", fundStatus=" + fundStatus +
                ", failedReason='" + failedReason + '\'' +
                ", status=" + status +
                ", fconfirmdate=" + fconfirmdate +
                ", fconfirmmanid='" + fconfirmmanid + '\'' +
                ", fconfirmman='" + fconfirmman + '\'' +
                ", fcanceldate=" + fcanceldate +
                ", fcancelmanid='" + fcancelmanid + '\'' +
                ", fcancelman='" + fcancelman + '\'' +
                ", fdeliverydate=" + fdeliverydate +
                ", fdeliverymanid='" + fdeliverymanid + '\'' +
                ", fdeliveryman='" + fdeliveryman + '\'' +
                ", flastreceivedate=" + flastreceivedate +
                ", flastreceivemanid='" + flastreceivemanid + '\'' +
                ", flastreceiveman='" + flastreceiveman + '\'' +
                ", fassessdate=" + fassessdate +
                ", fassessmanid='" + fassessmanid + '\'' +
                ", fassessman='" + fassessman + '\'' +
                ", piemail='" + piemail + '\'' +
                ", projectid='" + projectid + '\'' +
                ", projectnumber='" + projectnumber + '\'' +
                ", projecttitle='" + projecttitle + '\'' +
                ", fuserid=" + fuserid +
                ", fusercode='" + fusercode + '\'' +
                ", fusername='" + fusername + '\'' +
                ", statementId=" + statementId +
                ", fcancelreason='" + fcancelreason + '\'' +
                ", frefuseCancelReason='" + frefuseCancelReason + '\'' +
                ", shutDownDate=" + shutDownDate +
                ", deliveryInfo='" + deliveryInfo + '\'' +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", returnAmount=" + returnAmount +
                ", frefuseCancelDate=" + frefuseCancelDate +
                ", fdeliveryid=" + fdeliveryid +
                ", bidOrderId='" + bidOrderId + '\'' +
                ", orderType=" + orderType +
                ", receivePicUrls='" + receivePicUrls + '\'' +
                ", tpiProjectId='" + tpiProjectId + '\'' +
                ", originalAmount=" + originalAmount +
                ", inventoryStatus=" + inventoryStatus +
                ", species=" + species +
                ", updateTime=" + updateTime +
                ", inStateTime=" + inStateTime +
                ", purchaseRootinType=" + purchaseRootinType +
                ", carryFee=" + carryFee +
                ", invoiceTitleId=" + invoiceTitleId +
                ", invoiceTitle='" + invoiceTitle + '\'' +
                ", invoiceTitleNumber='" + invoiceTitleNumber + '\'' +
                ", fundType=" + fundType +
                ", fundTypeName='" + fundTypeName + '\'' +
                ", paymentAmount=" + paymentAmount +
                ", statementStatus=" + statementStatus +
                ", createTime=" + createTime +
                ", flowId=" + flowId +
                ", deptParentId=" + deptParentId +
                ", deptParentName='" + deptParentName + '\'' +
                ", finishDate=" + finishDate +
                '}';
    }
}