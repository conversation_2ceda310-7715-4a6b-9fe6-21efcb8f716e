package com.ruijing.store.order.base.core.model;

/**
 * @description: 退货单统计DO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/1/6 15:57
 **/
public class GoodsReturnCountDO {
    /**
     * 退货状态
     */
    private Integer goodsReturnStatus;

    /**
     * 统计数量
     */
    private Integer count;

    public Integer getGoodsReturnStatus() {
        return goodsReturnStatus;
    }

    public void setGoodsReturnStatus(Integer goodsReturnStatus) {
        this.goodsReturnStatus = goodsReturnStatus;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnCountDO{");
        sb.append("goodsReturnStatus=").append(goodsReturnStatus);
        sb.append(", count=").append(count);
        sb.append('}');
        return sb.toString();
    }
}
