package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/3 16:29
 * @Description
 **/
@RpcModel(value = "退货单概览请求体")
public class GoodsReturnOrderRequest implements Serializable {

    private static final long serialVersionUID = 3425416716977701746L;

    /**
     * 状态
     */
    @RpcModelProperty(value = "搜索：退货单状态")
    private Integer status;

    /**
     * 查询条件(采购部门,订单号)
     */
    @RpcModelProperty(value = "查询条件(采购部门,订单号)")
    private String search;

    /**
     * 商品查询条件
     */
    @RpcModelProperty(value = "商品查询条件，暂时无用")
    private String goodSearch;

    /**
     * 采购部门
     */
    @RpcModelProperty(value = "搜索：采购部门")
    private Integer department;

    /**
     * 订单号
     */
    @RpcModelProperty(value = "搜索：订单号")
    private String orderNo;

    /**
     * 开始时间
     */
    @RpcModelProperty(value = "搜索：开始时间")
    private String startDate;

    /**
     * 结束时间
     */
    @RpcModelProperty(value = "搜索：结束时间")
    private String endDate;

    /**
     * 部门id列表
     */
    @RpcModelProperty(value = "部门id列表")
    private List<Integer> deptIdList;

    /**
     * 页码
     */
    @RpcModelProperty(value = "页码")
    private Integer pageNo;

    /**
     * 每页数量
     */
    @RpcModelProperty(value = "每页数量")
    private Integer pageSize;

    /**
     * 单位Id
     */
    @RpcModelProperty(value = "单位Id，非前端传")
    private Integer orgId;

    /**
     * 根部门id
     */
    @RpcModelProperty(value = "根部门id，非前端传")
    private Integer rootDepartmentId;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getGoodSearch() {
        return goodSearch;
    }

    public void setGoodSearch(String goodSearch) {
        this.goodSearch = goodSearch;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<Integer> getDeptIdList() {
        return deptIdList;
    }

    public void setDeptIdList(List<Integer> deptIdList) {
        this.deptIdList = deptIdList;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public GoodsReturnOrderRequest setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getRootDepartmentId() {
        return rootDepartmentId;
    }

    public GoodsReturnOrderRequest setRootDepartmentId(Integer rootDepartmentId) {
        this.rootDepartmentId = rootDepartmentId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnOrderRequest{");
        sb.append("status=").append(status);
        sb.append(", search='").append(search).append('\'');
        sb.append(", goodSearch='").append(goodSearch).append('\'');
        sb.append(", department=").append(department);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", startDate='").append(startDate).append('\'');
        sb.append(", endDate='").append(endDate).append('\'');
        sb.append(", deptIdList=").append(deptIdList);
        sb.append(", pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", orgId=").append(orgId);
        sb.append(", rootDepartmentId=").append(rootDepartmentId);
        sb.append('}');
        return sb.toString();
    }
}
