package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

/**
 * @description: 配置项返回体，主要作为value为json的json解析中间转换类
 * @Author: Zeng <PERSON>ru
 * @Date: 2020/12/28 10:46
 */
public class OrderConfigTemplateBO {

    /**
     * 配置项名
     */
    private String name;

    /**
     * 配置项值
     */
    private String value;

    public String getName() {
        return name;
    }

    public OrderConfigTemplateBO setName(String name) {
        this.name = name;
        return this;
    }

    public String getValue() {
        return value;
    }

    public OrderConfigTemplateBO setValue(String value) {
        this.value = value;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderConfigTemplateBO{");
        sb.append("name='").append(name).append('\'');
        sb.append(", value='").append(value).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
