package com.ruijing.store.order.constant;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.collections.SetBuilder;

import java.util.List;
import java.util.Set;

/**
 * @description: 订单结算相关的硬编码
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/5 11:13
 **/
public class OrderStatementConstant {

    /**
     * 待结算换卡不冻结经费，而是结算后才冻结经费的单位
     */
    public static final Set<String> SUBMIT_STATEMENT_FREEZE_COLLECT = SetBuilder.<String>custom()
            .add(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode())
            .add(OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getCode())
            .build();

    /**
     * 需要展示结算类型的单位
     */
    public final static List<Integer> SHOW_STATEMENT_TYPE_ORG_LIST = New.list(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getValue(),
            OrgEnum.JI_NAN_DA_XUE.getValue(), OrgEnum.NAN_FANG_YI_KE.getValue());

}
