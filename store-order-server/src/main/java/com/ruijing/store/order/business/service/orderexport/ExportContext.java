package com.ruijing.store.order.business.service.orderexport;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <PERSON><PERSON>
 * @Description: 用于分发不同的导出 头 数据 策略
 * @DateTime: 2022/5/24 9:40
 */
public class ExportContext {
    /**
     * 获取导出策略具体实例(因为没有可以串的业务逻辑，所以尽管是饿汉模式也不需要处理并发）
     * @param orgId
     * @return
     */
    public static ExportHeaderAndBody getExportStrategyByOrg(Integer orgId) {
        return new DefaultExport();
    }
}
