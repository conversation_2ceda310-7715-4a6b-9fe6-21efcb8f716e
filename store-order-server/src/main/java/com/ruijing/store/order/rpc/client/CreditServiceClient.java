package com.ruijing.store.order.rpc.client;

import com.reagent.credit.api.CommentService;
import com.reagent.credit.api.request.OrderCommentStatusReq;
import com.reagent.credit.api.request.SendOrderRecivedDataReq;
import com.reagent.credit.api.response.BaseResp;
import com.reagent.credit.api.response.OrderCommentStatus;
import com.reagent.credit.api.response.OrderCommentStatusResp;
import com.reagent.credit.global.common.ErrorCodeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.store.order.business.enums.myorderlist.OrderCommentStatusEnum;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: zhukai
 * @date : 2019/12/9 11:35 上午
 * @description: 评价系统RPC客户端
 */
@Service
@ServiceLog
public class CreditServiceClient {

    @MSharpReference(remoteAppkey="credit-service")
    private CommentService commentService;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "CreditServiceClient";

    /**
     * 发送订单信息给评价系统
     * @param sendOrderRecivedDataReq
     */
    public void addOrderReceivedData(SendOrderRecivedDataReq sendOrderRecivedDataReq) throws CallRpcException {
        final String methodName = "addOrderReceivedData";
        Transaction transaction = Cat.newTransaction(CAT_TYPE, methodName);
        try {
            BaseResp baseResp = commentService.addOrderRecivedData(sendOrderRecivedDataReq);
            transaction.setSuccess();
            String responseLog = JsonUtils.toJson(baseResp);
            transaction.addData("返回结果：", responseLog);
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("addOrderReceivedData异常",e);
            throw new CallRpcException("获取用户信息异常！");
        }finally {
            transaction.complete();
        }
    }

    @ServiceLog(description = "通过订单id列表获取评价状态",serviceType = ServiceType.COMMON_SERVICE)
    public Map<Integer, Short> getOrderCommentStatusMap(List<Integer> orderIdList) {
        Map<Integer, Short> orderIdCommentStatusMap = new HashMap<>(orderIdList.size());

        OrderCommentStatusReq request = new OrderCommentStatusReq();
        request.setOrderIdList(orderIdList.stream().map(Objects::toString).collect(Collectors.toList()));

        BaseResp<OrderCommentStatusResp> baseResp;
        baseResp = commentService.getOrderCommentStatus(request);
        if (null == baseResp || !ErrorCodeEnum.SUCCESS.getCode().equals(baseResp.getCode()) || CollectionUtils.isEmpty(baseResp.getData().getOrderCommentStatusList())) {
            for (Integer orderId:orderIdList) {
                orderIdCommentStatusMap.put(orderId,OrderCommentStatusEnum.NO_COMMENTS.getValue().shortValue());
            }
            return orderIdCommentStatusMap;
        }

        OrderCommentStatusResp orderCommentStatusInfo = baseResp.getData();
        List<OrderCommentStatus> orderCommentStatusList = orderCommentStatusInfo.getOrderCommentStatusList();
        for (OrderCommentStatus orderCommentStatus:orderCommentStatusList) {
            Short commentStatus = orderCommentStatus.getStatus() == null ? OrderCommentStatusEnum.NO_COMMENTS.getValue().shortValue() : orderCommentStatus.getStatus();
            orderIdCommentStatusMap.put(Integer.valueOf(orderCommentStatus.getOrderId()),commentStatus);
        }
        // 补足其他orderId的mapping，默认无评论
        for (Integer orderId:orderIdList) {
            if (!orderIdCommentStatusMap.containsKey(orderId)) {
                orderIdCommentStatusMap.put(orderId,OrderCommentStatusEnum.NO_COMMENTS.getValue().shortValue());
            }
        }
        return orderIdCommentStatusMap;
    }
}
