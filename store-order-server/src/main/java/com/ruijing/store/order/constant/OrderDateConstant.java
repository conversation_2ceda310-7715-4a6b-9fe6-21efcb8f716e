package com.ruijing.store.order.constant;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.MapBuilder;
import com.ruijing.fundamental.common.date.DateUtils;

import java.util.Date;
import java.util.Map;

/**
 * @description: 订单日期硬编码
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/1 11:12
 **/
public class OrderDateConstant {


    /**
     * 南方医的旧单约定时间
     */
    public final static String NAN_FANG_YI_KE = "2021-06-17 18:00:00";

    /**
     * 中山五院的旧单约定时间
     */
    public final static String ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NOT_DOCKING_FINANCIAL_DATE = "2021-04-08 18:00:00";

    /**
     * 中山大学附属第五医院新财务系统时间 又又是一个旧单时间
     */
    public final static Date ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NEW_FINANCIAL_DATE = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2023-07-09 18:00:00");

    /**
     * 杭州医的旧单约定时间
     */
    public final static String HANG_ZHOU_YI_XUE_YUAN = "2021-03-04 23:59:59";

    /**
     * 暨南大学的旧单约定时间
     */
    public final static String JI_NAN_DA_XUE = "2021-05-26 00:00:00";

    /**
     * 中大附一的旧单约定时间
     */
    public final static String ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN = "2022-06-14 18:00:00";

    /**
     * 广东药科大学的旧单约定时间
     */
    public final static String GUANG_DONG_YAO_KE_DA_XUE = "2021-09-22 12:00:00";

    /**
     * 浙肿的旧单时间
     */
    public final static String ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN = "2021-08-16 17:30:00";

    /**
     * 华农旧对接时间，为 采购单生成时间
     */
    public final static String HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME = "2022-09-15 19:00:00";

    /**
     * 广妇幼旧单展示时间
     */
    public final static Date GUANG_FU_YOU_SHOW_OLD_DATE = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2023-03-01 18:30:00");



    /**
     * 维护所有医院的旧单日期的字典
     */
    public final static Map<String, String> COMMON_ORDER_DATE_MAP = MapBuilder.<String, String>custom()
            .put(OrgEnum.NAN_FANG_YI_KE.getCode(), NAN_FANG_YI_KE)
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode(), ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NOT_DOCKING_FINANCIAL_DATE)
            .put(OrgEnum.HANG_ZHOU_YI_XUE_YUAN.getCode(), HANG_ZHOU_YI_XUE_YUAN)
            .put(OrgEnum.JI_NAN_DA_XUE.getCode(), JI_NAN_DA_XUE)
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode(),ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN)
            .put(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode(), GUANG_DONG_YAO_KE_DA_XUE)
            .build();

    /**
     * 医院对接财务后，旧单约定的时间，这个是需要收货后完成订单的医院集合
     */
    public final static Map<String, String> ORG_CODE_OLD_ORDER_DATE_MAP = MapBuilder.<String, String>custom()
            .put(OrgEnum.NAN_FANG_YI_KE.getCode(),"2021-06-17 18:00:00")
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode(),ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NOT_DOCKING_FINANCIAL_DATE)
            .build();

    /**
     * 医院对接财务后，旧单约定的时间，这个是不需要解冻的医院集合
     */
    public final static Map<String, String> ORG_CODE_OLD_ORDER_DATE_UNFREEZE_MAP = MapBuilder.<String, String>custom()
            .put(OrgEnum.HANG_ZHOU_YI_XUE_YUAN.getCode(),"2021-03-04 23:59:59")
            .put(OrgEnum.NAN_FANG_YI_KE.getCode(),"2021-06-17 18:00:00")
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode(),ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NOT_DOCKING_FINANCIAL_DATE)
            .put(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode(),"2021-08-11 14:00:00")
            .build();

    /**
     * 换财务系统后，新财务系统上线时间。代码上处理为冻结/解冻/换卡时给tpi一个isNew（旧单传  0，新单传  1）的参数
     */
    public final static Map<String, Date> ORG_CODE_NEW_FINANCIAL_DATE_MAP = MapBuilder.<String, Date>custom()
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode(), ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NEW_FINANCIAL_DATE)
            .build();

    /**
     * 旧单验收换卡：旧单约定时间，目前只有暨大有这个需求
     */
    public final static Map<String, String> ORG_CODE_OLD_ORDER_DATE_CHANGE_CARD_MAP = MapBuilder.<String, String>custom()
            .put(OrgEnum.JI_NAN_DA_XUE.getCode(),"2021-09-14 18:00:00")
            .put(OrgEnum.NAN_FANG_YI_KE.getCode(),"2021-03-04 23:59:59")
            .build();


    /**
     * 医院对接财务后，旧单约定的时间, 广西肿瘤旧的单需要到待结算
     */
    public final static Map<String, String> NO_NEED_WAITING_STATEMENT_ORG_CODE_OLD_ORDER_DATE_MAP = MapBuilder.<String, String>custom()
            .put(OrgEnum.GUANG_XI_ZHONG_LIU.getCode(),"2022-01-17 18:00:00")
            .build();

    /**
     * 判断订单是否为旧单
     * @param orgCode
     * @param orderDate
     * @return
     */
    public static boolean isOldOrderForNormal(String orgCode, Date orderDate) {
        String date = NO_NEED_WAITING_STATEMENT_ORG_CODE_OLD_ORDER_DATE_MAP.get(orgCode);
        if (date == null) {
            return false;
        }
        return orderDate.before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, date));
    }

    /**
     * 用于设置oldFlag字段以在前端展示的旧订单时间
     */
    public final static Map<String, Date> OLD_FLAG_OLD_ORDER_DATE_MAP = MapBuilder.<String, Date>custom()
//            .put(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode(), "2022-05-10 18:00:00")
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode(), ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN_NEW_FINANCIAL_DATE)
            .put(OrgEnum.GUANG_XI_ZHONG_LIU.getCode(), DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2022-01-17 18:00:00"))
            .put(OrgEnum.GUANG_ZHOU_SHI_FU_NV_ER_TONG_YI_LIAO_ZHONG_XIN.getCode(), GUANG_FU_YOU_SHOW_OLD_DATE)
            .put(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode(), DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN))
            .put(OrgEnum.QING_HUA_DA_XUE_GU_JI_YAN_JIU_SHENG_YUAN.getCode(), DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2023-11-20 18:00:00"))
            .put(OrgEnum.JIN_FENG_SHI_YAN_SHI.getCode(), DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2025-01-22 12:00:00"))
            .build();

    /**
     * 判断是否为旧订单，用于设置oldFlag字段以在前端展示
     * @param orgCode
     * @param orderDate
     * @return
     */
    public static boolean isOldOrderForView(String orgCode, Date orderDate) {
        Date date = OLD_FLAG_OLD_ORDER_DATE_MAP.get(orgCode);
        if (date == null) {
            return false;
        }
        return orderDate.before(date);
    }
}
