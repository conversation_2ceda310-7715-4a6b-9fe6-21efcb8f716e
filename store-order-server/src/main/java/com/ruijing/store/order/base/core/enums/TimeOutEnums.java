package com.ruijing.store.order.base.core.enums;

/**
 * 订单超时类型
 */
public enum TimeOutEnums {

    /**
     * 所有类型，不管是否超时
     */
    ALL(-2,"所有类型"),
    
    /**
     * 所有超时类型
     */
    ALL_TIMEOUT_TYPE(-1, "所有超时类型"),

    /**
     * 结算超时
     */
    BALANCE(0, "结算超时"),

    /**
     * 验收超时
     */
    EXAMINE(1, "验收超时"),

    ACCEPT_APPROVE_OR_WAREHOUSE(2, "验收审批或入库超时");

    /**
     * 超时类型代码值
     */
    private final int type;

    /**
     * 超时类型描述
     */
    private final String description;

    TimeOutEnums(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static TimeOutEnums getByType(int type) {
        for (TimeOutEnums e : TimeOutEnums.values()) {
            if (e.getType() == type) {
                return e;
            }
        }
        return null;
    }
}
