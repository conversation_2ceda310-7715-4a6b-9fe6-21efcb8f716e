package com.ruijing.store.order.business.bo.buyercenter.operationlog;

/**
 * <AUTHOR>
 * @Date 2021/1/13 10:12
 * @Description
 **/
public class OrderLogApproveBO {

    /**
     * id
     */
    private Integer id;

    /**
     * 验收或拒绝图片
     */
    private String photo;

    /**
     * 备注
     */
    private String reason;

    /**
     * 订单审批状态
     */
    private Integer orderApprovalStatus;

    /**
     * 审批等级
     */
    private Integer approveLevel;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 操作用户
     */
    private String userName;

    /**
     * 创建时间
     */
    private Long creationTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getOrderApprovalStatus() {
        return orderApprovalStatus;
    }

    public void setOrderApprovalStatus(Integer orderApprovalStatus) {
        this.orderApprovalStatus = orderApprovalStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Long creationTime) {
        this.creationTime = creationTime;
    }

    @Override
    public String toString() {
        return "OrderLogApproveBO{" +
                "id=" + id +
                ", photo='" + photo + '\'' +
                ", reason='" + reason + '\'' +
                ", orderApprovalStatus=" + orderApprovalStatus +
                ", approveLevel=" + approveLevel +
                ", statusDesc='" + statusDesc + '\'' +
                ", userName='" + userName + '\'' +
                ", creationTime=" + creationTime +
                '}';
    }
}
