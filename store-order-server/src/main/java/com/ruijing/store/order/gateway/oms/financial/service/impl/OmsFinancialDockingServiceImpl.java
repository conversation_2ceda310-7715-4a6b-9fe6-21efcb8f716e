package com.ruijing.store.order.gateway.oms.financial.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.gateway.oms.financial.request.FinancialDockingOrderQueryRequestDTO;
import com.ruijing.store.order.gateway.oms.financial.service.OmsFinancialDockingService;
import com.ruijing.store.order.gateway.oms.financial.vo.FinancialDockingOrderDataVO;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-04-12 11:44
 * @description:
 **/
@Service
public class OmsFinancialDockingServiceImpl implements OmsFinancialDockingService {

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Override
    public List<FinancialDockingOrderDataVO> queryOrderData(FinancialDockingOrderQueryRequestDTO financialDockingOrderQueryRequestDTO) {
        String orderNo = financialDockingOrderQueryRequestDTO.getOrderNo();
        String buyAppNo = financialDockingOrderQueryRequestDTO.getApplicationNo();
        String dockingNumber = financialDockingOrderQueryRequestDTO.getDockingNumber();
        if(StringUtils.isBlank(orderNo) && StringUtils.isBlank(buyAppNo) && StringUtils.isBlank(dockingNumber)){
            return New.emptyList();
        }
        if(StringUtils.isNotBlank(orderNo) || StringUtils.isNotBlank(buyAppNo)){
            // 有单号或采购单号，先根据这两个走搜索过滤，再根据对接单号过滤
            List<FinancialDockingOrderDataVO> searchResult = this.searchByOrderNoOrBuyAppNo(orderNo, buyAppNo);
            if(StringUtils.isNotBlank(dockingNumber)){
                return searchResult.stream().filter(dataItem->dockingNumber.equals(dataItem.getDockingNumber())).collect(Collectors.toList());
            }
            return searchResult;
        }
        // 否则先查出订单号，再用订单号走搜索查出数据
        List<DockingExtra> dockingExtraList = dockingExtraMapper.findByExtraInfoInAndType(New.list(dockingNumber), DockingTypeEnum.Order.getValue());
        if(CollectionUtils.isEmpty(dockingExtraList)){
            return New.emptyList();
        }
        return this.searchByOrderNoOrBuyAppNo(dockingExtraList.get(0).getInfo(), null);
    }

    /**
     * 通过订单号或采购单号搜索
     * @param orderNo 订单号
     * @param buyAppNo 采购单号
     * @return 搜索
     */
    private List<FinancialDockingOrderDataVO> searchByOrderNoOrBuyAppNo(String orderNo, String buyAppNo){
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setOrderNo(orderNo);
        if(StringUtils.isNotBlank(buyAppNo)){
            orderSearchParamDTO.setApplicationNoList(New.list(buyAppNo));
        }
        orderSearchParamDTO.setPageSize(100);
        SearchPageResultDTO<OrderMasterSearchDTO> searchPageResultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> dataList = searchPageResultDTO.getRecordList();
        return dataList.stream().map(orderMasterSearchDTO -> {
            FinancialDockingOrderDataVO financialDockingOrderDataVO = new FinancialDockingOrderDataVO();
            financialDockingOrderDataVO.setOrderNo(orderMasterSearchDTO.getForderno());
            financialDockingOrderDataVO.setApplicationNo(orderMasterSearchDTO.getFbuyapplicationno());
            financialDockingOrderDataVO.setDockingNumber(orderMasterSearchDTO.getExtraInfo());
            financialDockingOrderDataVO.setFundStatus(orderMasterSearchDTO.getFundStatus());
            financialDockingOrderDataVO.setOrderStatus(orderMasterSearchDTO.getStatus());
            return financialDockingOrderDataVO;
        }).collect(Collectors.toList());
    }
}
