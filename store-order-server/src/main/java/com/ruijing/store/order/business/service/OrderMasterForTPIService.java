package com.ruijing.store.order.business.service;

import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;

/**
 * 订单对接第三方平台服务
 */
public interface OrderMasterForTPIService {

    /**
     * 更新订单状态到第三方平台
     * @param updateStatus  订单信息
     * @param orderNo       订单号
     * @return              是否成功
     */
    default boolean updateThirdPlatformOrder(Integer updateStatus, String orderNo) {
        UpdateOrderParamDTO param = new UpdateOrderParamDTO();
        param.setStatus(updateStatus);
        param.setOrderNo(orderNo);
        return this.updateThirdPlatformOrder(param);
    }

    /**
     * 更新订单状态到第三方平台
     * @param request   订单入参
     * @return          是否成功
     */
    boolean updateThirdPlatformOrder(UpdateOrderParamDTO request);

    /**
     * 订单退货通知到第三方平台
     * @param request   退货单信息
     * @param orgCode   是否成功
     * @return
     */
    boolean orderReturn(GoodsReturn request, String orgCode);

    default ListenableFuture<Boolean> asyncOrderReturn(GoodsReturn request, String orgCode) {
        return ListenableFutures.forValue(this.orderReturn(request, orgCode));
    }

    /**
     * 异步更新订单状态到第三方平台，直接更新
     * @param thirdPartyPlatformOrderBO
     * @return
     */
    ListenableFuture<Boolean> updateOrderStatusAsync(ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO);
}
