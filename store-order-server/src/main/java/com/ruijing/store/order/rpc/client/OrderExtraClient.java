package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.RpcCallUtils;
import com.ruijing.order.whitehole.database.dto.extra.request.BaseOrderExtraRequestDTO;
import com.ruijing.order.whitehole.database.service.OrderExtraDataService;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.base.core.translator.OrderExtraTranslator;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/6/28 17:16
 */
@ServiceClient
public class OrderExtraClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderExtraRpcService orderExtraRpcService;

    @MSharpReference(remoteAppkey = "order-whitehole-service")
    private OrderExtraDataService orderExtraDataService;

    /**
     * 批量插入订单拓展表
     * @param orderExtraDTOList
     * @return
     */
    @ServiceLog(description = "批量插入订单拓展表", serviceType = ServiceType.COMMON_SERVICE)
    public Integer insertList(List<BaseOrderExtraDTO> orderExtraDTOList) {
        Preconditions.notNull(orderExtraDTOList, "批量插入订单拓展表入参不可为空");
        RemoteResponse<Integer> response = orderExtraRpcService.insertList(orderExtraDTOList);
        Preconditions.isTrue(response!=null && response.isSuccess(), "批量插入订单拓展表失败，orderExtraDTOList="+ JsonUtils.toJson(orderExtraDTOList));
        return response.getData();
    }

    /**
     * 通过订单id和拓展表操作类型id查找条目
     * @param query
     * @return
     */
    @ServiceLog(description = "通过订单id和拓展表操作类型id查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseOrderExtraDTO> selectByOrderIdAndExtraKey(BaseOrderExtraDTO query) {
        Preconditions.notNull(query, "通过订单id和拓展表操作类型id查找条目入参不可为空");
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdAndExtraKey(query);
        Preconditions.isTrue(response!=null && response.isSuccess(), "通过订单id和拓展表操作类型id查找条目失败, query="+JsonUtils.toJson(query));
        return response.getData();
    }

    /**
     * 通过订单id列表和拓展表操作类型id查找条目
     * @param orderIdCollection
     * @param extraKey {@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum}
     * @return
     */
    @ServiceLog(description = "通过订单id列表和拓展表操作类型id查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseOrderExtraDTO> selectByOrderIdInAndExtraKey(Collection<Integer> orderIdCollection, Integer extraKey) {
        List<Integer> orderIdList = (orderIdCollection instanceof List ? (List<Integer>) orderIdCollection : New.list(orderIdCollection));
        return RpcCallUtils.partitionExec(orderIdList, 200, (part)->orderExtraRpcService.selectByOrderIdInAndExtraKey(New.list(part), extraKey));
    }

    /**
     * 通过订单id列表查找条目
     * @param orderIdCollection
     * @return
     */
    @ServiceLog(description = "通过订单id列表查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseOrderExtraDTO> selectByOrderIdIn(Collection<Integer> orderIdCollection) {
        Preconditions.notEmpty(orderIdCollection, "通过订单id列表查找条目入参不可为空");
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdList(orderIdCollection);
        Preconditions.isTrue(response!=null && response.isSuccess(), "通过订单id列表查找条目失败, orderIdList="+orderIdCollection);
        return response.getData();
    }

    /**
     * 通过订单号列表查找条目
     * @param orderNoCollection
     * @return
     */
    @ServiceLog(description = "通过订单号列表查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseOrderExtraDTO> selectByOrderNoIn(Collection<String> orderNoCollection) {
        Preconditions.notEmpty(orderNoCollection, "通过订单号列表查找条目入参不可为空");
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderNoList(orderNoCollection);
        Preconditions.isTrue(response!=null && response.isSuccess(), "通过订单号列表查找条目失败, orderNoList="+orderNoCollection);
        return response.getData();
    }

    /**
     * 通过订单号列表查找条目
     * @param orderIdCol
     * @return
     */
    @ServiceLog(description = "通过订单号列表查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public void deleteInOrderId(Collection<Integer> orderIdCol) {
        RemoteResponse<Boolean> response = orderExtraRpcService.deleteInOrderId(orderIdCol);
        Preconditions.isTrue(response!=null && response.isSuccess(), "删除订单信息失败：" + response.getMsg());
    }

    @ServiceLog(description = "通过订单id列表和拓展表操作类型id查找条目", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderExtraDTO> selectByOrderIdAndExtraKey(List<Integer> orderIdList, List<Integer> extraKeyList){
        Preconditions.notEmpty(orderIdList, "订单id不可空");
        
        List<OrderExtraDTO> resultList = New.list();
        List<List<Integer>> partitionOrderIdList = Lists.partition(orderIdList, 200);
        for (List<Integer> partOrderIds : partitionOrderIdList) {
            BaseOrderExtraRequestDTO baseOrderExtraRequestDTO = new BaseOrderExtraRequestDTO();
            baseOrderExtraRequestDTO.setExtraKeyList(extraKeyList);
            baseOrderExtraRequestDTO.setOrderIdList(New.list(partOrderIds));
            
            RemoteResponse<List<com.ruijing.order.whitehole.database.dto.extra.data.BaseOrderExtraDTO>> response =
                    orderExtraDataService.selectByOrderIdAndExtraKey(baseOrderExtraRequestDTO);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            
            if (CollectionUtils.isNotEmpty(response.getData())) {
                resultList.addAll(response.getData().stream()
                        .map(OrderExtraTranslator::whiteHoleDto2OrderDto)
                        .collect(Collectors.toList()));
            }
        }
        return resultList;
    }

    @ServiceLog(description = "保存order_extra", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void saveList(List<BaseOrderExtraDTO> baseOrderExtraDTOList){
        RpcCallUtils.partitionWrite(baseOrderExtraDTOList, 100, part->orderExtraRpcService.saveList(New.list(part)));
    }
}
