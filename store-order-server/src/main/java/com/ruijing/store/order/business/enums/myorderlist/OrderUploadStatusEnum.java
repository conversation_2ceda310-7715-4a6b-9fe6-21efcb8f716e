package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/25 10:30
 * @Description
 **/
public enum OrderUploadStatusEnum {
    /**
     * 未上传
     */
    FALSE(0,"未上传"),
    /**
     * 已上传
     */
    TRUE(1,"已上传"),
    /**
     * 无需上传
     */
    NO_NEED_TO_UPLOAD(2,"无需上传");

    public final Integer value;

    public final String desc;

    OrderUploadStatusEnum(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderUploadStatusEnum getByName(String name) {
        for (OrderUploadStatusEnum orderUploadStatusEnum : OrderUploadStatusEnum.values()) {
            if (orderUploadStatusEnum.desc.equals(name)){
                return orderUploadStatusEnum;
            }
        }
        return null;
    }

    public static final OrderUploadStatusEnum getByValue(Integer value) {
        for (OrderUploadStatusEnum orderUploadStatusEnum : OrderUploadStatusEnum.values()) {
            if (orderUploadStatusEnum.value.equals(value)){
                return orderUploadStatusEnum;
            }
        }
        return null;
    }
}
