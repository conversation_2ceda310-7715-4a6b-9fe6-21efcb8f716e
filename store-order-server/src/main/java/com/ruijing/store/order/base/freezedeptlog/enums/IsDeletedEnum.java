package com.ruijing.store.order.base.freezedeptlog.enums;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/30 10:25
 **/
public enum  IsDeletedEnum // 是否删除枚举
{
    NOT_DELETED(0, "未删除"),
    ALREADY_DELETED(1, "已删除"),
    ;

    IsDeletedEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private int code;
    private String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
