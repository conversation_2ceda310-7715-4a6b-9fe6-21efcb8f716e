package com.ruijing.store.order.base.timeoutstatistics.service.impl;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.ordermaster.dto.GoodsReturnParamDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.timeoutstatistics.constant.TimeOutStatisticsServiceConstant;
import com.ruijing.store.order.base.timeoutstatistics.mapper.TimeoutStatisticsMapper;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.base.timeoutstatistics.translator.TimeoutStatisticTranslator;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 超时统计相关业务
 * @author: zhongyulei
 * @create: 2019/10/23 14:10
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public abstract class AbstractTimeoutStatisticsServiceImpl implements TimeoutStatisticsService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    private static final String CAT_TYPE = "TimeoutStatisticsServiceImpl";

    @Resource
    protected TimeoutStatisticsMapper timeoutStatisticsMapper;

    @Resource(name = TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_BALANCE)
    private TimeoutStatisticsService timeoutStatisticsWithBalance;

    @Resource(name = TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_EXAMINE)
    private TimeoutStatisticsService timeoutStatisticsWithExamine;

    @Resource
    private FreezeDeptLogService freezeDeptLogService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Override
    @ServiceLog(operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public int insertBatch(List<TimeoutStatisticsDTO> dtos) {
        final String methodName = "insertBatch";
        if (CollectionUtils.isEmpty(dtos)) {
            Cat.logWarn(CAT_TYPE, methodName, "插入失败!入参不能为空");
            return 0;
        }

        List<TimeoutStatisticsDO> doList = new ArrayList<>(dtos.size());
        for (TimeoutStatisticsDTO dto : dtos) {
            doList.add(TimeoutStatisticTranslator.dto2DO(dto));
        }
        return timeoutStatisticsMapper.insertList(doList);
    }

    @Override
    public int deleteAll() {
        return timeoutStatisticsMapper.delete();
    }

    @Override
    public List<TimeoutStatisticsDTO> findAll() {
        List<TimeoutStatisticsDO> listTimeoutStatisticsDO = timeoutStatisticsMapper.findAll();
        List<TimeoutStatisticsDTO> dtos = new ArrayList<>(listTimeoutStatisticsDO.size());

        for (TimeoutStatisticsDO timeoutStatisticsDO : listTimeoutStatisticsDO) {
            dtos.add(TimeoutStatisticTranslator.do2DTO(timeoutStatisticsDO));
        }
        return dtos;
    }

    @Override
    public TimeoutStatisticsDTO findByDepartmentIdAndType(int orgId, int departmentId, int type) {
        TimeoutStatisticsDO statisticsDO = timeoutStatisticsMapper.queryOneByOrgIdAndDepIdAndType(orgId, departmentId, type);
        if (statisticsDO == null) {
            return new TimeoutStatisticsDTO();
        }
        return TimeoutStatisticTranslator.do2DTO(statisticsDO);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public int updateAmountById(TimeoutStatisticsDTO dto) {
        if (dto != null) {
            return timeoutStatisticsMapper.updateByPrimaryKeySelective(TimeoutStatisticTranslator.dto2DO(dto));
        }
        return 0;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public int deleteById(long id) {
        return timeoutStatisticsMapper.deleteByPrimaryKey(id);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public void executeTimeOutStatisticsDecrease(Integer count, Integer orgId, String orgCode, Integer departmentId, TimeOutBusinessType timeOutType) {
        Assert.isTrue(count != null, "更新条数不能为空!");
        Assert.isTrue(orgId != null, "医院id不能为空!");
        Assert.isTrue(departmentId != null, "课题组id不能为空!");

        // 获取医院配置字典
        Map<String, Integer> timeOutConfigMap = orderManageService.getTimeOutConfigMap(orgCode);

        GoodsReturnParamDTO params = new GoodsReturnParamDTO();
        params.setOrganizationId(orgId);
        params.setDepartmentId(departmentId);

        boolean isLessThanConfigExamineAmount = false;
        boolean isLessThanConfigBalanceAmount = false;

        if (TimeOutBusinessType.ACCEPTANCE == timeOutType) {
            isLessThanConfigExamineAmount = timeoutStatisticsWithExamine.executeTimeOutStatisticsDecrease(count, params, timeOutConfigMap);
            isLessThanConfigBalanceAmount = timeoutStatisticsWithBalance.executeTimeOutStatisticsDecrease(0, params, timeOutConfigMap);
        } else if (TimeOutBusinessType.BALANCE == timeOutType) {
            isLessThanConfigExamineAmount = timeoutStatisticsWithExamine.executeTimeOutStatisticsDecrease(0, params, timeOutConfigMap);
            isLessThanConfigBalanceAmount = timeoutStatisticsWithBalance.executeTimeOutStatisticsDecrease(count, params, timeOutConfigMap);
        }

        // 同时低于 结算和验收 配置的张数阈值, 解冻课题组
        if (isLessThanConfigExamineAmount && isLessThanConfigBalanceAmount) {
            freezeDeptLogService.updateDeletedByOrgIdAndDepartmentIdAndType(params.getOrganizationId(), params.getDepartmentId());
        }
    }
}
