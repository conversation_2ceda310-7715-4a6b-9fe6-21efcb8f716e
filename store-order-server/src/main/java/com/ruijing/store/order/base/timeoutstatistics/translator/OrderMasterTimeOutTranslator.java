package com.ruijing.store.order.base.timeoutstatistics.translator;

import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;

import java.math.BigDecimal;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-03-10 15:06
 * @description:
 **/
public class OrderMasterTimeOutTranslator {
    
    public static OrderMasterTimeOutDTO searchMasterDto2TimeoutDto(OrderMasterSearchDTO searchDto){
        if(searchDto == null){
            return null;
        }
        OrderMasterTimeOutDTO orderMasterTimeOutDTO = new OrderMasterTimeOutDTO();
        orderMasterTimeOutDTO.setId(searchDto.getId());
        orderMasterTimeOutDTO.setOrderNo(searchDto.getForderno());
        orderMasterTimeOutDTO.setBuyDepartment(searchDto.getFbuydepartment());
        orderMasterTimeOutDTO.setBuyDepartmentId(searchDto.getFbuydepartmentid());
        orderMasterTimeOutDTO.setBuyerName(searchDto.getFbuyername());
        orderMasterTimeOutDTO.setOrderDate(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, searchDto.getForderdate()).getTime());
        orderMasterTimeOutDTO.setStatus(searchDto.getStatus());
        orderMasterTimeOutDTO.setSuppName(searchDto.getFsuppname());
        orderMasterTimeOutDTO.setStatementId(searchDto.getStatementId());
        orderMasterTimeOutDTO.setTotalPrice(BigDecimal.valueOf(searchDto.getForderamounttotal()));
        orderMasterTimeOutDTO.setStatementStatus(searchDto.getStatementStatus());
        orderMasterTimeOutDTO.setInventoryStatus(searchDto.getInventoryStatus());
        // 订单详情
        orderMasterTimeOutDTO.setOrderDetails(searchDto.getOrderDetail().stream().map(OrderMasterTimeOutTranslator::searchDetailDto2TimeoutDto).collect(Collectors.toList()));
        // 经费卡号
        orderMasterTimeOutDTO.setFundCardIdList(searchDto.getCard().stream().map(FundCardSearchDTO::getFundCardId).collect(Collectors.toList()));
        return orderMasterTimeOutDTO;
    }

    private static OrderDetailTimeOutDTO searchDetailDto2TimeoutDto(OrderDetailSearchDTO orderDetailSearchDTO){
        if(orderDetailSearchDTO == null){
            return null;
        }
        OrderDetailTimeOutDTO dto = new OrderDetailTimeOutDTO();
        dto.setGoodName(orderDetailSearchDTO.getFgoodname());
        dto.setBrand(orderDetailSearchDTO.getFbrand());
        dto.setGoodCode(orderDetailSearchDTO.getFgoodcode());
        dto.setSpec(orderDetailSearchDTO.getFspec());
        dto.setQuantity(BigDecimal.valueOf(orderDetailSearchDTO.getFquantity()));
        dto.setId(orderDetailSearchDTO.getDetailId());
        dto.setPrice(BigDecimal.valueOf(orderDetailSearchDTO.getFbidprice()));
        return dto;
    }
}
