package com.ruijing.store.order.base.core.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class RefFundcardOrderDO {
    /**
    * uuid
    */
    private String id;

    /**
    * 申请单ID
    */
    private String applicationId;

    /**
    * 订单ID
    */
    private String orderId;

    /**
    * 竞价单ID
    */
    private String bidId;

    /**
    * 经费卡ID
    */
    private String cardId;

    /**
    * 科目ID
    */
    private String subjectId;

    /**
    * 序号
    */
    private Integer serialNumber;

    /**
    * 订单使用金额
    */
    private BigDecimal usemoney;

    /**
    * 逻辑删除时间
    */
    private Date deletionTime;

    /**
    * 是否被删除
    */
    private Boolean isDeleted;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 经费卡编号
    */
    private String cardNo;

    /**
    * 中肿经费卡类型0普卡3零余额卡4民口账户
    */
    private Integer fundType;

    /**
    * 中大审批人id
    */
    private Integer approveUser;

    /**
    * 采购申请或订单对应的经费卡需要冻结的金额
    */
    private BigDecimal freezeAmount;

    /**
     * 院区编码（暂时是中科大附一的特殊需求）
     */
    private String campusCode = "";

    /**
     * 院区名称(暂时是中科大附一的特殊需求）
     */
    private String campusName = "";

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBidId() {
        return bidId;
    }

    public void setBidId(String bidId) {
        this.bidId = bidId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public BigDecimal getUsemoney() {
        return usemoney;
    }

    public void setUsemoney(BigDecimal usemoney) {
        this.usemoney = usemoney;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public Integer getApproveUser() {
        return approveUser;
    }

    public void setApproveUser(Integer approveUser) {
        this.approveUser = approveUser;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public String getCampusCode() {
        return campusCode;
    }

    public void setCampusCode(String campusCode) {
        this.campusCode = campusCode;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RefFundcardOrderDO{");
        sb.append("id='").append(id).append('\'');
        sb.append(", applicationId='").append(applicationId).append('\'');
        sb.append(", orderId='").append(orderId).append('\'');
        sb.append(", bidId='").append(bidId).append('\'');
        sb.append(", cardId='").append(cardId).append('\'');
        sb.append(", subjectId='").append(subjectId).append('\'');
        sb.append(", serialNumber=").append(serialNumber);
        sb.append(", usemoney=").append(usemoney);
        sb.append(", deletionTime=").append(deletionTime);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", cardNo='").append(cardNo).append('\'');
        sb.append(", fundType=").append(fundType);
        sb.append(", approveUser=").append(approveUser);
        sb.append(", freezeAmount=").append(freezeAmount);
        sb.append(", campusCode='").append(campusCode).append('\'');
        sb.append(", campusName='").append(campusName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}