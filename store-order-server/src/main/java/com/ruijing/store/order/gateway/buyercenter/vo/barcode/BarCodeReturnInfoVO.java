package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;


@RpcModel("退货信息")
public class BarCodeReturnInfoVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("退货单id")
    private Integer returnId;

    @RpcModelProperty("申请人")
    private String applyName;
    
    @RpcModelProperty("商品信息列表")
    private List<BarCodeGoodDetailVO> goodsList;

    public String getOrderNo() {
        return orderNo;
    }

    public BarCodeReturnInfoVO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public BarCodeReturnInfoVO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public BarCodeReturnInfoVO setReturnId(Integer returnId) {
        this.returnId = returnId;
        return this;
    }

    public String getApplyName() {
        return applyName;
    }

    public BarCodeReturnInfoVO setApplyName(String applyName) {
        this.applyName = applyName;
        return this;
    }

    public List<BarCodeGoodDetailVO> getGoodsList() {
        return goodsList;
    }

    public BarCodeReturnInfoVO setGoodsList(List<BarCodeGoodDetailVO> goodsList) {
        this.goodsList = goodsList;
        return this;
    }

    @Override
    public String toString() {
        return "BarCodeReturnInfoVO{" +
                "orderNo='" + orderNo + '\'' +
                ", returnNo='" + returnNo + '\'' +
                ", returnId=" + returnId +
                ", applyName='" + applyName + '\'' +
                ", goodsList=" + goodsList +
                '}';
    }
}