package com.ruijing.store.order.constant;

import com.ruijing.store.order.api.base.enums.OrderConfirmEnum;
import com.ruijing.store.order.api.base.enums.OrderConfirmTypeEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.enums.DangerousEnum;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/6/29 15:41
 */
public class DangerousConstant {

    //非易制爆
    public final static List<Integer> LIMIT_ORDER_REGULATORY_TYPES = new ArrayList<Integer>(){{
        add(OrderStatusEnum.WaitingForConfirm.value);
        add(OrderStatusEnum.WaitingForDelivery.value);
        add(OrderStatusEnum.OrderReceiveApproval.value);
        add(OrderStatusEnum.Statementing_1.value);
        add(OrderStatusEnum.OrderReceiveApprovalTwo.value);
        add(OrderStatusEnum.WaitingForReceive.value);
    }};

    //易制爆
    public final static List<Integer> OTHER_LIMIT_ORDER_REGULATORY_TYPES = new ArrayList<Integer>(){{
        add(OrderStatusEnum.WaitingForReceive.value);
        add(OrderStatusEnum.OrderReceiveApproval.value);
        add(OrderStatusEnum.WaitingForStatement_1.value);
        add(OrderStatusEnum.Statementing_1.value);
        add(OrderStatusEnum.Finish.value);
        add(OrderStatusEnum.Assess.value);
        add(OrderStatusEnum.OrderReceiveApprovalTwo.value);
    }};

    /**
     * 危化品类别是否需要备案和备案程度的判断
     * @param orderStatus
     * @param record
     * @return
     */
    public static OrderConfirmEnum transformType(Integer orderStatus, OrderConfirmForTheRecordDO record) {
        if (LIMIT_ORDER_REGULATORY_TYPES.contains(orderStatus)
                && Boolean.FALSE.equals(record.getConfirm())
                && Objects.equals(record.getType(), OrderConfirmTypeEnum.NOXIOUS.getCode())) {
            return OrderConfirmEnum.NEED_CONFIRM_AND_PICS;
        }
        if (LIMIT_ORDER_REGULATORY_TYPES.contains(orderStatus)
                && Boolean.TRUE.equals(record.getConfirm())
                && Objects.isNull(record.getAddPics())
                && Objects.equals(record.getType(), OrderConfirmTypeEnum.NOXIOUS.getCode())) {
            return OrderConfirmEnum.NO_CONFIRM_AND_CAN_PICS;
        }
        if (OTHER_LIMIT_ORDER_REGULATORY_TYPES.contains(orderStatus)
                && Boolean.FALSE.equals(record.getConfirm())
                && Objects.equals(record.getType(), OrderConfirmTypeEnum.EXPLOSIVES.getCode())) {
            return OrderConfirmEnum.NEED_CONFIRM_AND_NO_PICS;
        }
        return OrderConfirmEnum.NO_CONFIRM;
    }
}
