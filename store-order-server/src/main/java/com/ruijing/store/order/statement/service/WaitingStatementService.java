package com.ruijing.store.order.statement.service;

import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;

import java.util.List;

public interface WaitingStatementService {
    /**
     * 待结算业务表记录生成
     * @param orderIdList
     * @param orgCode
     * @return
     */
    List<WaitingStatementOrderRequestDTO> waitingStatementGenerate(List<Integer> orderIdList, String orgCode);

    /**
     * 推送待结算单
     * @param orgCode
     * @param orderIdList
     */
    void pushWaitingStatement(String orgCode, List<Integer> orderIdList);

    /**
     * 推入待结算
     * @param orgCode
     * @param orderIdList
     * @param currentInventoryStatus
     */
    void pushWaitingStatement(String orgCode, List<Integer> orderIdList, Integer currentInventoryStatus);

}
