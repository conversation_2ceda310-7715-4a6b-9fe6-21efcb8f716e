package com.ruijing.store.order.rpc.client;

import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.custom.api.fundcard.FundCardFreezeService;
import com.reagent.research.fundcard.api.FundCardRPCService;
import com.reagent.research.fundcard.api.budget.FundCardBindRPCService;
import com.reagent.research.fundcard.api.budget.dto.BindDTO;
import com.reagent.research.fundcard.dto.ChangeFundCardDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 经费卡业务相关rpc
 * <AUTHOR>
 */
@ServiceClient
public class FundCardRelateRpcClient {

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardRPCService fundCardRPCService;

    @MSharpReference(remoteAppkey = "research-fundcard-service")
    private FundCardBindRPCService fundCardBindRPCService;

    @MSharpReference(remoteAppkey = "research-custom-business-service")
    private FundCardFreezeService fundCardFreezeService;

    /**
     * 经费卡换卡，涉及财务对接
     */
    @ServiceLog(description = "换卡保存经费卡（财务）", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void changeFundCard(String orgCode, ChangeFundCardDTO param) {
        Assert.isTrue(StringUtils.isNotBlank(orgCode), "换卡失败，医院编码code为空！");
        OrgRequest<ChangeFundCardDTO> request = new OrgRequest<>();
        request.setData(param);
        request.setOrgCode(orgCode);

        RemoteResponse response = fundCardRPCService.changeFundCard(request);
        Assert.isTrue(response.isSuccess(), response.getMsg());

    }


    /**
     * 预算系统，经费卡绑定rpc接口
     * @param bindDTO
     * @param orgCode
     */
    @ServiceLog(description = "预算系统绑定经费卡，推送绑卡数据接口", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void fundCardBindBatch(List<BindDTO> bindDTO, String orgCode) {
        OrgRequest<List<BindDTO>> request = new OrgRequest<>();
        request.setData(bindDTO);
        request.setOrgCode(orgCode);

        RemoteResponse response = fundCardBindRPCService.fundCardBindBatch(request);
        Assert.isTrue(response.isSuccess(), response.getMsg());
    }

    /**
     * @description: 解冻经费卡，当前仅中山大学使用
     * @date: 2021/1/27 18:10
     * @author: zengyanru
     * @param orgRequest
     * @return java.lang.Boolean
     */
    @ServiceLog(description = "解冻经费卡", serviceType = ServiceType.COMMON_SERVICE)
    public void fundCardUnFreeze(OrgRequest orgRequest) {
        RemoteResponse response = fundCardFreezeService.fundCardUnFreeze(orgRequest);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
