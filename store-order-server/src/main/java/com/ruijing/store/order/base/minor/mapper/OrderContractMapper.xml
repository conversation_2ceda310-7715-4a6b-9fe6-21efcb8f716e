<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.OrderContractMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.OrderContract">
    <!--@mbg.generated-->
    <!--@Table t_order_contract-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="contract_location" jdbcType="VARCHAR" property="contractLocation" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, contract_location, contract_name, contract_no, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_contract
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_order_contract
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.OrderContract" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_contract (order_id, contract_location, contract_name, contract_no,
      create_time)
    values (#{orderId,jdbcType=INTEGER}, #{contractLocation,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.OrderContract" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="contractLocation != null">
        contract_location,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="contractLocation != null">
        #{contractLocation,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.OrderContract">
    <!--@mbg.generated-->
    update t_order_contract
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="contractLocation != null">
        contract_location = #{contractLocation,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        contract_no = #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.OrderContract">
    <!--@mbg.generated-->
    update t_order_contract
    set order_id = #{orderId,jdbcType=INTEGER},
      contract_location = #{contractLocation,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      contract_no = #{contractNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-12-04-->
  <select id="findByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_order_contract
    where order_id=#{orderId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2020-11-25-->
  <select id="selectByOrderIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_order_contract
    <where>
      <if test="orderIdCollection != null and orderIdCollection.size() > 0">
        and order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-01-06-->
  <delete id="deleteByOrderIdIn">
        delete from t_order_contract
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </delete>

<!--auto generated by MybatisCodeHelper on 2021-01-06-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO t_order_contract(
    order_id,
    contract_location,
    contract_name,
    contract_no,
    create_time
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.orderId,jdbcType=INTEGER},
      #{element.contractLocation,jdbcType=VARCHAR},
      #{element.contractName,jdbcType=VARCHAR},
      <choose>
        <when test="element.contractNo != null ">
            #{element.contractNo,jdbcType=VARCHAR},
        </when>
        <otherwise>
            '',
        </otherwise>
      </choose>
      #{element.createTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>