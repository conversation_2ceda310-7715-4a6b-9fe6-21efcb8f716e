package com.ruijing.store.order.base.core.model;

import java.util.Date;

public class RefOrderDetailTagDO {
    private Integer id;

    /**
    * refId
    */
    private String refId;

    /**
    * tagValue
    */
    private Integer tagValue;

    /**
    * tagName
    */
    private String tagName;

    /**
    * 标签类型
    */
    private Integer tagType;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 是否被删除
    */
    private Boolean isDeleted;

    /**
    * 逻辑删除时间
    */
    private Date deletionTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public Integer getTagValue() {
        return tagValue;
    }

    public void setTagValue(Integer tagValue) {
        this.tagValue = tagValue;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", refId=").append(refId);
        sb.append(", tagValue=").append(tagValue);
        sb.append(", tagName=").append(tagName);
        sb.append(", tagType=").append(tagType);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", deletionTime=").append(deletionTime);
        sb.append("]");
        return sb.toString();
    }
}