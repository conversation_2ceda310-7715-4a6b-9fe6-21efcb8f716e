package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2025-06-20 17:09
 * @description:
 */
@Model("物流详情")
public class OrderLogisticsInfoDetailVO implements Serializable {

    private static final long serialVersionUID = 7499274280382505603L;

    @ModelProperty("快递100物流状态值名称")
    private String logisticsStatusName;
    @ModelProperty("轨迹内容")
    private String context;
    @ModelProperty("物流时间")
    private Date logisticsTime;

    public String getLogisticsStatusName() {
        return logisticsStatusName;
    }

    public OrderLogisticsInfoDetailVO setLogisticsStatusName(String logisticsStatusName) {
        this.logisticsStatusName = logisticsStatusName;
        return this;
    }

    public String getContext() {
        return context;
    }

    public OrderLogisticsInfoDetailVO setContext(String context) {
        this.context = context;
        return this;
    }

    public Date getLogisticsTime() {
        return logisticsTime;
    }

    public OrderLogisticsInfoDetailVO setLogisticsTime(Date logisticsTime) {
        this.logisticsTime = logisticsTime;
        return this;
    }

    @Override
    public String toString() {
        return "OrderLogisticsInfoDetailVO{" +
                "logisticsStatusName='" + logisticsStatusName + '\'' +
                ", context='" + context + '\'' +
                ", logisticsTime=" + logisticsTime +
                '}';
    }
}
