package com.ruijing.store.order.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.reagent.research.statement.api.statement.dto.SimpleStatementDTO;
import com.reagent.research.sysu.order.api.dto.PlatformOperatorApprovalInfoDTO;
import com.reagent.store.control.api.directory.dto.DirectoryResultDTO;
import com.reagent.supp.api.order.dto.OrderLogisticsInfoDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.contract.dto.OrderContractQueryDTO;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.LocaleUtils;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.pojo.dto.SupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.UserDTO;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.srm.api.dto.brand.SuppBrandDTO;
import com.ruijing.shop.trade.api.dto.OrderDeliveryInfoDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.*;
import com.ruijing.store.apply.dto.offline.OfflineExtraDTO;
import com.ruijing.store.apply.enums.RegulationEnum;
import com.ruijing.store.apply.enums.offline.OfflineProcurementSourceEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.filing.dto.ProcurementApplicationDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderOperateLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.core.translator.OrderUploadFileTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.minor.mapper.OrderContractMapper;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.model.OrderContract;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO;
import com.ruijing.store.order.base.orderlog.enums.OrderLogUserTypeEnum;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OrderDetailGoodsReturnBO;
import com.ruijing.store.order.business.enums.PurchaseApproveStatusEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptAddPicEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptByPhotoEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptPermitEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.business.service.orgondemand.SYSUOrderService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.OrderOperateLogConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ContractInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnOrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.*;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.ProductTemplateDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.*;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * @description: 订单详情关联业务
 * @author: zhongyulei
 * @create: 2019/10/23 9:54
 **/
@Service
public class OrderDetailRelatedServiceImpl implements OrderDetailRelatedService {

    private static final String CAT_TYPE = "OrderDetailRelatedServiceImpl";

    @PearlValue(key="ThermoFisherSuppIdList", defaultValue = "[570]")
    private static List<Integer> thermoFiserSuppIdList;

    // 单层经费卡的单位
    private static final Set<String> SINGLE_LEVEL_ORG_MAP = new HashSet<String>(){{
        add(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode());
        add(OrgEnum.JI_NAN_DA_XUE.getCode());
        add(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode());
        add(OrgEnum.ZHENG_DA_FU_YI.getCode());
        add(OrgEnum.ZHONG_SHAN_DA_XUE.getCode());
        add(OrgEnum.ZHONG_SHAN_DA_XUE_SHEN_ZHEN.getCode());
    }};

    /**
     * 不在HMS，但要求能在采购人中心看到的修改的经费卡的单位
     */
    private static final Set<String> SHOW_CHANGED_FUNDCARD_IN_BUYER_CENTER = new HashSet<String>(){{
        add(OrgEnum.JI_NAN_DA_XUE.getCode());
        add(OrgEnum.NAN_FANG_YI_KE.getCode());
        add(OrgEnum.DONG_YING_SHI_REN_MIN_YI_YUAN.getCode());
    }};

    // 不需要展示代配送的一级分类
    private static final Set<Integer> NOT_SHOW_DELIVERY_PROXY_CATE = New.set(113, 460);


    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderPicMapper orderPicMapper;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Resource
    private OrderContractMapper orderContractMapper;

    @Resource
    private ProductDescriptionSnapshotMapper descSnapshotMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private PriceContractClient priceContractClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private ResearchCustomClient researchCustomClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private SYSUOrderService sysuOrderService;

    @Resource
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private BidClient bidClient;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    @Resource
    private CategoryDirectoryClient categoryDirectoryClient;

    @Resource
    private OrderContractClient orderContractClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private PlatformOperatorApprovalRpcClient platformOperatorApprovalRpcClient;


    private final static Integer SYSU_DEFAULT_PUBLIC_VALUE = 0;

    private final Integer ORDER_TYPE_FOR_DESC_PURCHASE = 1;

    private final Integer ORDER_TYPE_FOR_DESC_BID = 2;
    @Autowired
    private OrderOperateLogService orderOperateLogService;

    @Override
    public Set<Integer> findFmasterIdByReturnStatus(List<Integer> masterIdList, List<Integer> returnStatus) {
        // masterIdList做分批查询
        return orderDetailMapper.findFmasteridByReturnStatusIn(masterIdList, returnStatus);
    }

    /**
     * @return BaseConfigDTO
     * @description: 获取图片上传配置
     * @date: 2020/12/21 15:44
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "获取图片上传配置",serviceType = ServiceType.COMMON_SERVICE)
    public BaseConfigDTO getPicConfig(RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(loginInfo.getOrgCode(), New.list(ConfigConstant.ORG_RECEIPT_PIC_CONFIG));
        return configList.get(0);
    }

    /**
     * @param orderId
     * @param orderNo
     * @param orgId
     * @return java.util.List<com.ruijing.store.order.base.core.vo.orderdetail.OrderInvoiceInfoVO>
     * @description: 通过订单ID获取发票详细信息
     * @date: 2020/12/21 16:29
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "通过订单ID获取发票详细信息",serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderInvoiceInfoVO> listInvoiceByOrder(Integer orderId, String orderNo, Integer orgId) {
        if(orderId == null){
            BusinessErrUtil.isTrue(StringUtils.isNotBlank(orderNo), ExecptionMessageEnum.INVOICE_INFO_INTERFACE_FAILED);
            OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
            Preconditions.notNull(orderMasterDO, "订单"+ orderNo +"不存在!");
            orderId = orderMasterDO.getId();
        }
        InvoiceQueryDTO queryDTO = new InvoiceQueryDTO();
        queryDTO.setInvoiceType(InvoiceTypeEnum.ORDER);
        queryDTO.setOrgId(Long.valueOf(orgId));
        queryDTO.setSourceIds(New.list(Long.valueOf(orderId)));
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(queryDTO);
        return invoiceVOList;
    }

    @Override
    @ServiceLog(description = "订单详情-获取发票信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderInvoiceInfoRespVO retrieveInvoicesForOrder(Integer orderId, String orderNo, Integer orgId, Long userId, String accessCode) {

        OrderInvoiceInfoRespVO orderInvoiceInfoRespVO = new OrderInvoiceInfoRespVO();
        Byte species;
        OrderMasterDO orderMasterDO;

        if(orderId == null){
            BusinessErrUtil.isTrue(StringUtils.isNotBlank(orderNo), ExecptionMessageEnum.INVOICE_INFO_INTERFACE_FAILED);
            orderMasterDO = orderMasterMapper.findByForderno(orderNo);
            Preconditions.notNull(orderMasterDO, "订单"+ orderNo +"不存在!");
            orderId = orderMasterDO.getId();
        }else {
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(New.list(orderId));
            BusinessErrUtil.isTrue(orderMasterDOList.size() == 1, ExecptionMessageEnum.ABNORMAL_ORDER_DATA);
            orderMasterDO = orderMasterDOList.get(0);
        }
        //订单类型
        species = orderMasterDO.getSpecies();
        BusinessErrUtil.isTrue(Objects.nonNull(species), "订单类型为空");

        InvoiceQueryDTO queryDTO = new InvoiceQueryDTO();
        queryDTO.setInvoiceType(InvoiceTypeEnum.ORDER);
        queryDTO.setOrgId(Long.valueOf(orgId));
        queryDTO.setSourceIds(New.list(Long.valueOf(orderId)));
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(queryDTO);
        orderInvoiceInfoRespVO.setOrderInvoiceInfoVOList(invoiceVOList);
        //设置默认不能修改发票信息
        orderInvoiceInfoRespVO.setCanModifyInvoice(false);

        //当前用户能否修改发票信息
        Boolean canModifyInvoice = userClient.findUserHasAccess(orgId, userId.intValue(), null, accessCode);
        if (!canModifyInvoice) {
            return orderInvoiceInfoRespVO;
        }

        //订单状态
        Integer status = orderMasterDO.getStatus();
        if (OrderSpeciesEnum.OFFLINE.getValue().equals(species.intValue())) {
            //对于线下订单，如果不是【完成状态】，不能修改发票
            boolean isValidOfflineStatus = OrderStatusEnum.Finish.getValue().equals(status);
            if (!isValidOfflineStatus) {
                return orderInvoiceInfoRespVO;
            }
            //符合条件，可以修改发票信息
            orderInvoiceInfoRespVO.setCanModifyInvoice(true);
        } else if(OrderSpeciesEnum.NORMAL.getValue().equals(species.intValue())){
            //对于线上订单，如果不是 【订单完成】并且【发票信息不为空】，不能修改发票
            boolean isValidOnlineStatus = OrderStatusEnum.Finish.getValue().equals(status) && CollectionUtils.isNotEmpty(invoiceVOList);
            if (!isValidOnlineStatus) {
                return orderInvoiceInfoRespVO;
            }
            //非随货发票数量
            long nonAccompanyingInvoiceNum = invoiceVOList.stream()
                    .filter(orderInvoiceInfoVO -> !InvoiceTypeEnum.ORDER.getType().equals(orderInvoiceInfoVO.getType()))
                    .count();
            //含有非随货发票，则对应订单不能修改发票信息
            if (nonAccompanyingInvoiceNum != 0 ) {
                return orderInvoiceInfoRespVO;
            }
            //符合条件，可以修改发票信息
            orderInvoiceInfoRespVO.setCanModifyInvoice(true);
        }
        return orderInvoiceInfoRespVO;
    }

    /**
     * @description: 获取订单相关配置-验收、经费卡、合同相关
     * @date: 2020/12/22 14:08
     * @author: zengyanru
     * @param orgCode
     * @param configCodeList
     * @return com.ruijing.store.order.base.core.vo.orderdetail.OrderRelatedConfigVO
     */
    @Override
    @ServiceLog(description = "获取订单相关配置-验收、经费卡、合同相关",serviceType = ServiceType.COMMON_SERVICE)
    public OrderRelatedConfigVO getOrderRelatedConfig(String orgCode, List<String> configCodeList) {
        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode,configCodeList);
        OrderRelatedConfigVO orderRelatedConfig = new OrderRelatedConfigVO();
        for (BaseConfigDTO config:configList) {
            switch (config.getConfigCode()) {
                case ConfigConstant.MUST_HAVE_FUNDCARD:
                    orderRelatedConfig.setMustHaveFundCardConfig(config.getConfigValue());
                    break;
                case ConfigConstant.ORDER_CONTRACT_THRESHOLD:
                    orderRelatedConfig.setOrderContractThresholdConfig(config.getConfigValue());
                    break;
                case ConfigConstant.REQUIRE_SIGN_PROCUREMENT_CONTRACT:
                    orderRelatedConfig.setRequireContractConfig(config.getConfigValue());
                    break;
                case ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG:
                    orderRelatedConfig.setAcceptanceApprovalConfig(config.getConfigValue());
                    break;
                default:
                    break;
            }
        }
        return orderRelatedConfig;
    }

    /**
     * @param request
     * @return com.ruijing.store.order.base.core.vo.orderlist.OrderInfoVO
     * @description: 通过订单id获取订单详情信息
     * @date: 2020/12/22 14:58
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "通过订单id获取订单详情信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderInfoVO getOrderDetail(LoginUserInfoBO loginInfo, OrderBasicParamDTO request, Boolean isHms) throws ParseException {
        // 订单号与订单id查询的兼容
        BusinessErrUtil.isTrue(request.getOrderId() != null || request.getOrderNo() != null, ExecptionMessageEnum.ENTER_ORDER_NUMBER_OR_ID);
        OrderMasterDO orderMasterInfo;
        // 没传入订单主表数据，才去查
        if (request.getOrderId() != null) {
            orderMasterInfo = orderMasterMapper.selectByPrimaryKey(request.getOrderId());
        } else {
            orderMasterInfo = orderMasterMapper.findByForderno(request.getOrderNo());
        }

        return this.getOrderDetail(loginInfo, isHms, orderMasterInfo);
    }

    /**
     * @return com.ruijing.store.order.base.core.vo.orderlist.OrderInfoVO
     * @description: 通过订单id获取订单详情信息
     * @date: 2020/12/22 14:58
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "通过订单id获取订单详情信息",serviceType = ServiceType.COMMON_SERVICE)
    public OrderInfoVO getOrderDetail(LoginUserInfoBO loginInfo, Boolean isHms, OrderMasterDO orderMasterInfo) throws ParseException {

        BusinessErrUtil.notEmpty(loginInfo.getDeptList(), ExecptionMessageEnum.CANNOT_FIND_USER_RESEARCH_GROUP);

        BusinessErrUtil.notNull(orderMasterInfo, ExecptionMessageEnum.CANNOT_FIND_ORDER_PLEASE_RETRY);
        Integer orderId = orderMasterInfo.getId();
        Integer userId = loginInfo.getUserId();;

        // TODO:后续请多线程并行优化
        OrderListRequest orderListReq = new OrderListRequest();
        orderListReq.setOrderId(orderId);
        orderListReq.setPageNo(1);
        orderListReq.setPageSize(1);
        orderListReq.setNeedOfflineInfo(true);
        // 为了避免登陆人课题组与下单时课题组不一致的情况
        orderListReq.setMyOrderCheck(false);

        if(this.haveAccessAcceptApproveOrder(userId, orderMasterInfo)){
            // 有权限验收审批或曾经验收审批过，设置所在课题组为根部门，用于跳过权限校验
            List<DepartmentDTO> rootDept = userClient.getDepartmentListByIds(New.list(loginInfo.getRootDepartmentId()));
            loginInfo.setDeptList(rootDept);
        }

        // 根据订单id，获取大部分信息，需要额外查一次数据库
        PageableResponse<OrderListRespVO> curOrderListResp = buyerOrderService.getOrderListForWWW(orderListReq,loginInfo, isHms);
        // 构建可视报错
        List<String> deptNameList = loginInfo.getDeptList().stream().filter(Objects::nonNull).map(DepartmentDTO::getName).collect(toList());
        String cantFindIdHints = LocaleUtils.translate(ExecptionMessageEnum.BACKEND_CANNOT_FIND_ORDER_ID, deptNameList);
        BusinessErrUtil.isTrue(curOrderListResp != null && curOrderListResp.isSuccess(), cantFindIdHints);

        OrderListRespVO curOrderList = curOrderListResp.getData();
        BusinessErrUtil.notNull(curOrderList,cantFindIdHints);
        List<OrderInfoVO> curOrderInfoList = curOrderList.getOrderList();
        BusinessErrUtil.notEmpty(curOrderInfoList,cantFindIdHints);
        // OrderInfoFeatureVO,订单详情专用数据
        OrderInfoVO curOrderInfo = curOrderInfoList.get(0).setOrderInfoFeature(new OrderInfoFeatureVO());
        BusinessErrUtil.notNull(curOrderInfo,cantFindIdHints);
        BusinessErrUtil.notNull(curOrderInfo.getOrder(),cantFindIdHints);

        // 狗皮膏药+2，赛默飞优惠券码
        CompletableFuture<List<BaseOrderExtraDTO>> couponInfoListFuture = AsyncExecutor.callAsync(() -> {
            if (thermoFiserSuppIdList.contains(orderMasterInfo.getFsuppid())) {
                BaseOrderExtraDTO baseExtra = new BaseOrderExtraDTO();
                baseExtra.setOrderId(orderId);
                baseExtra.setExtraKey(OrderExtraEnum.COUPON_CODE.getValue());
                return orderExtraClient.selectByOrderIdAndExtraKey(baseExtra);
            }
            return New.list();
        });

        // 需要额外查找的权限
        String confUseStatementOnline = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM;
        String confUseStatementOffline = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM;
        List<String> configCodeList = New.list(confUseStatementOnline, confUseStatementOffline, ConfigConstant.ORDER_RETURN_ONLY_WHOLE);
        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orderMasterInfo.getFusercode(), configCodeList);
        Map<String, List<BaseConfigDTO>> codeBaseConfigMap = baseConfigList.stream().collect(groupingBy(BaseConfigDTO::getConfigCode));

        // 是否使用锐竞的结算系统
        Boolean onlineStatementCheck = true;
        Boolean offlineStatementCheck = true;
        if (CollectionUtils.isNotEmpty(codeBaseConfigMap.get(confUseStatementOnline))) {
            onlineStatementCheck = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(codeBaseConfigMap.get(confUseStatementOnline).get(0).getConfigValue());
        }
        if (CollectionUtils.isNotEmpty(codeBaseConfigMap.get(confUseStatementOffline))) {
            offlineStatementCheck = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(codeBaseConfigMap.get(confUseStatementOffline).get(0).getConfigValue());
        }
        Boolean statementCheck = OrderSpeciesEnum.NORMAL.getValue().equals(orderMasterInfo.getSpecies().intValue()) ? onlineStatementCheck : offlineStatementCheck;

        // 补充发货联系人信息
        curOrderInfo.getOrder().setDeliveryMobile(this.getDeliveryMobile(orderMasterInfo));
        if (orderMasterInfo.getFconfirmman() != null) {
            curOrderInfo.getOrder().setDeliveryName(orderMasterInfo.getFconfirmman());
        } else {
            String suppContactMan = "";
            if (OrderSpeciesEnum.OFFLINE.getValue().equals(orderMasterInfo.getSpecies().intValue())) {
                OfflineSupplierDTO offlineSupp = suppClient.getOfflineSuppById(orderMasterInfo.getFsuppid());
                suppContactMan = offlineSupp.getContactMan();
            } else {
                List<SupplierDTO> onlineSupp = suppClient.getSupplierListByIds(New.list(orderMasterInfo.getFsuppid()));
                if (CollectionUtils.isNotEmpty(onlineSupp)) {
                    SupplierDTO supplier = onlineSupp.get(0);
                    if (Objects.nonNull(supplier.getContactMan())) {
                        suppContactMan = supplier.getContactMan().getRealName();
                    }
                }
            }
            curOrderInfo.getOrder().setDeliveryName(suppContactMan);
        }

        // 采购人联系电话
        UserBaseInfoDTO buyerInfo = userClient.getUserInfo(orderMasterInfo.getFbuyerid(), orderMasterInfo.getFuserid());
        curOrderInfo.getOrder().setContactPhone(buyerInfo.getMobile());
        // 发票抬头id
        curOrderInfo.getOrder().setInvoiceTitleId(orderMasterInfo.getInvoiceTitleId());

        // 订单溯源，采购或竞价
        ApplicationMasterDTO appMasterInfo = this.setOrderSource(orderMasterInfo, curOrderInfo);

        // 获取订单验收描述
        curOrderInfo.getOrder().setOrderReceiveDesc(this.getReceiveDesc(String.valueOf(orderId)));

        // 订单审批信息
        List<OrderApprovalInfoVO> orderApprovalInfoList = orderOperateLogService.getOrderApprovalInfo(orderMasterInfo);
        curOrderInfo.setOrderApprovalInfoList(orderApprovalInfoList);

        // 部门特殊审批逻辑
        curOrderInfo.setApprovalPermit(this.specialOrgApprovePermission(loginInfo, curOrderInfo));

        // 如果在HMS界面,或是需求要求能在采购人中心看到验收审批换卡后的经费卡的，获取经费卡数据
        List<String> fundCardIdList = this.getOrderFundCardList(orderId, orderMasterInfo);
        curOrderInfo.getOrder().setFundCardIdList(fundCardIdList);
        if (isHms || SHOW_CHANGED_FUNDCARD_IN_BUYER_CENTER.contains(orderMasterInfo.getFusercode())) {
            // 经费卡卡号，卡名(目前看来是仅中大需要设值，但先全部都返回吧）
            curOrderInfo.getOrder().setFundCard(this.mergeFundCardNoAndName(orderMasterInfo, fundCardIdList));
            // 获取订单推送状态
            curOrderInfo.getOrder().setOrderPushEventStatusList(this.getOrderPushEventStatus(orderMasterInfo));
        }

        // 合同信息
        this.setOrderContractInfo(curOrderInfo);


        // 新版合同信息
        OrderContractQueryDTO queryDTO = new OrderContractQueryDTO();
        queryDTO.setOrderNoList(New.list(orderMasterInfo.getForderno()));
        List<OrderContractInfoVO> contractInfoVOList = orderContractClient.getOrderContractInfo(queryDTO);
        curOrderInfo.setOrderContractInfoVOList(contractInfoVOList);

        // 买家留言信息
        Integer buyAppId = curOrderInfo.getOrder().getBuyAppId();
        Integer suppId = curOrderInfo.getOrder().getSupplierId();
        curOrderInfo.getOrder().setOrderRemark(this.getOrderRemark(buyAppId, suppId));
        // 退货信息封装
        Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap = this.detailIdGoodsReturnMap(New.list(orderId));
        this.setGoodsReturnInfo(curOrderInfo, detailIdGoodsReturnMap, statementCheck);

        // 商品验收图片(前端代码难动，其后续迁移）
        if(CollectionUtils.isNotEmpty(curOrderInfo.getReceiveImages())){
            String receiveImgUrls = curOrderInfo.getReceiveImages().stream().map(OrderPicVO::getUrl).collect(Collectors.joining(";"));
            curOrderInfo.getOrder().setUrls(receiveImgUrls);
        }
        // 线下单信息
        curOrderInfo.getOrder().setOfflineInfo(this.getOfflineInfo(orderMasterInfo));

        // 物流信息
        curOrderInfo.setOrderLogisticsInfo(this.getLogisticsInfo(orderMasterInfo));
        // 对接信息 广工
        if (OrgConst.GUANG_DONG_GONG_YE_DA_XUE.equals(loginInfo.getOrgCode())) {
            List<DockingExtra> dockingExtraList = dockingExtraMapper.findByInfoInAndType(New.list(orderMasterInfo.getForderno()), DockingTypeEnum.Order.getValue());
            if (CollectionUtils.isNotEmpty(dockingExtraList)) {
                DockingExtra dockingExtra = dockingExtraList.get(0);
                if (dockingExtra != null) {
                    curOrderInfo.getOrder().setDockingStatus(dockingExtra.getStatusextra());
                    curOrderInfo.setDockingMemo(dockingExtra.getMemo());
                }
            }
        }
        // 订单所属采购单是否在公示中，中大逻辑，配置暂不走缓存
        curOrderInfo.getOrder().setPublicity(this.getPublicity(orderMasterInfo,appMasterInfo));

        // 设置是否需要图片验收
        curOrderInfo.setPhotoAcceptance(curOrderList.getPhotoAcceptance());

        // 判断是否显示追加验收图片按钮
        Boolean canAcceptByPermission;
        if (curOrderInfo.getOrder().getAcceptance() == null) {
            canAcceptByPermission = false;
        } else {
            canAcceptByPermission = curOrderInfo.getOrder().getAcceptance().equals(OrderAcceptPermitEnum.CAN_ACCEPT.getValue());
        }
        curOrderInfo.setCanAppendReceivePic(this.canAddReceptPic(canAcceptByPermission,orderMasterInfo,loginInfo));
        curOrderInfo.getOrder().setFundTypeName(orderMasterInfo.getFundTypeName());

        // 再贴一层狗皮膏药,查询是否有代配送地址信息, from zyl
        OrderAddressDTO addressDTO = orderAddressRPCClient.findByOrderId(orderId);
        if (addressDTO != null && DeliveryTypeEnum.PROXY_DELIVERY_LIST.contains(addressDTO.getDeliveryType())) {
            curOrderInfo.getOrder().setDeliveryProxyContactMan(addressDTO.getReceiverNameProxy());
            curOrderInfo.getOrder().setDeliveryProxyContactPhone(addressDTO.getReceiverPhoneProxy());
            // 再贴一层狗皮膏药+1，代配送标识，一级分类如果是科研服务和危化品（113，460），则不显示
            for (int i = 0; i < curOrderInfo.getOrderDetails().size(); i++) {
                if (!NOT_SHOW_DELIVERY_PROXY_CATE.contains(curOrderInfo.getOrderDetails().get(i).getFirstCategoryId())) {
                    curOrderInfo.getOrderDetails().get(i).setDeliveryProxyOn(true);
                }
            }
        }
        // 狗皮膏药+2，赛默飞优惠券码，错误也不希望阻塞。理论只有一条记录
        try {
            List<BaseOrderExtraDTO> couponInfoList = couponInfoListFuture.get();
            if (CollectionUtils.isNotEmpty(couponInfoList)) {
                curOrderInfo.getOrder().setCouponCode(couponInfoList.get(0) == null ? null : couponInfoList.get(0).getExtraValue());
            }
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getOrderDetail", "异步获取赛默飞优惠券码失败, orderId=" + orderId, e);
        }

        curOrderInfo.getOrder().setShutdownDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderMasterInfo.getShutDownDate()));
        if(OrderStatusEnum.Finish.getValue().equals(orderMasterInfo.getStatus())){
            curOrderInfo.getOrder().setFinishDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderMasterInfo.getFinishDate()));
        }

        List<OrderDetailDO> orderDetailDOList = null;
        if(categoryDirectoryClient.isCategoryDirectoryOn(orderMasterInfo.getFusercode(), false)){
            orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
            List<Integer> categoryIdList = orderDetailDOList.stream().map(OrderDetailDO::getCategoryDirectoryId).filter(ctgDirId->ctgDirId>0).distinct().collect(toList());
            if(CollectionUtils.isNotEmpty(categoryIdList)){
                List<DirectoryResultDTO> directoryResultDTOList = categoryDirectoryClient.getCategoryDirectoryByIds(categoryIdList);
                Map<Integer, Integer> detailIdCtgDirIdMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, OrderDetailDO::getCategoryDirectoryId);
                Map<Integer, DirectoryResultDTO> ctgDirIdIdentityMap = CollectionUtils.isEmpty(directoryResultDTOList) ? New.emptyMap() : DictionaryUtils.toMap(directoryResultDTOList, DirectoryResultDTO::getId, Function.identity());
                curOrderInfo.getOrderDetails().forEach(orderDetailVO -> {
                    DirectoryResultDTO matchCtgDir = ctgDirIdIdentityMap.get(detailIdCtgDirIdMap.get(orderDetailVO.getId()));
                    if(matchCtgDir != null){
                        orderDetailVO.setCategoryDirectoryCode(matchCtgDir.getCode()).setCategoryDirectoryName(matchCtgDir.getName());
                    }
                });
            }
        }
        // 返回是否限制只能整单退货，先判断一般的OMS配置，如果否再判断是否对接限制
        String val = CollectionUtils.isNotEmpty(codeBaseConfigMap.get(ConfigConstant.ORDER_RETURN_ONLY_WHOLE)) ? codeBaseConfigMap.get(ConfigConstant.ORDER_RETURN_ONLY_WHOLE).get(0).getConfigValue() : StringUtils.EMPTY;
        boolean limitOnlyWholeReturn = CommonValueUtils.TRUE_NUMBER_STR.equals(val);
        if(!limitOnlyWholeReturn){
            OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterInfo.getFusercode());
            limitOnlyWholeReturn = OmsDockingConfigValueEnum.WHOLE_ORDER.name().equals(config.getOrderDockingConfigDTO().getReturnGoodsRange());
            limitOnlyWholeReturn = limitOnlyWholeReturn && dockingConfigCommonService.isNewDockingEnable(config, orderMasterInfo, orderDetailDOList);
        }
        curOrderInfo.setLimitOnlyWholeReturn(limitOnlyWholeReturn);

        if(OrgEnum.XIANG_GANG_KE_JI_DA_XUE_GUANG_ZHOU.getValue() == orderMasterInfo.getFuserid()){
            ProcurementApplicationDTO procurementApplicationDTO = applicationBaseClient.getProcurementByAppId(orderMasterInfo.getFtbuyappid());
            curOrderInfo.getOrder().setProcurementNo(procurementApplicationDTO == null ? null : procurementApplicationDTO.getProcurementNo());
        }
        List<ApplyMasterExtensionDTO> extensionList = applicationBaseClient.findExtensionByApplicationId(new ApplyMasterExtensionQueryDTO(New.list(orderMasterInfo.getFtbuyappid())));
        if (CollectionUtils.isNotEmpty(extensionList)) {
            // 填充采购单的拓展信息
            ApplyMasterExtensionDTO extension = extensionList.get(0);
            ExtraInfoDTO extraInfo = extension.getExtraInfo();
            if (Objects.nonNull(extraInfo)) {
                // 实验室标签
                curOrderInfo.getOrderInfoFeature().setLabName(extraInfo.getLabName());
                // 关联关系说明附件
                List<ExtraInfoDTO.ApplicationAttachmentDTO> applicationAttachmentDTOS = extraInfo.getRelatedAttachments();
                List<OrderAttachmentVO> attachmentVOList = New.list();
                if (CollectionUtils.isNotEmpty(applicationAttachmentDTOS)) {
                    for (ExtraInfoDTO.ApplicationAttachmentDTO applyAttachmentDTO : applicationAttachmentDTOS) {
                        OrderAttachmentVO vo = new OrderAttachmentVO();
                        vo.setUrl(applyAttachmentDTO.getFilePath());
                        vo.setFileName(applyAttachmentDTO.getFileName());
                        attachmentVOList.add(vo);
                    }
                }
                curOrderInfo.getOrderInfoFeature().setRelatedAttachments(attachmentVOList);
            }
        }
        this.constructBindGasBottle(curOrderInfo);
        this.constructPlatformOperatorData(curOrderInfo);
        // 填充结算单信息
        setStatementInfo(curOrderInfo, orderMasterInfo);
        // 填充关闭原因文案
        setCloseReason(curOrderInfo, orderMasterInfo);
        // 填充申请取消订单信息
        setCancelOrderInfo(curOrderInfo, orderMasterInfo);
        // 填充订单拓展字段
        this.setOrderExtraInfo(curOrderInfo, orderMasterInfo);
        // 设置实时订单数据
        this.setRealTimeOrderData(curOrderInfo, orderMasterInfo);
        // 填充验收审批驳回原因
        setReceiveRejectReason(curOrderInfo, orderMasterInfo);
        return curOrderInfo;
    }
    /**
     * 填充验收审批驳回原因
     */
    private void setReceiveRejectReason(OrderInfoVO curOrderInfo, OrderMasterDO orderMasterInfo) {
        if (Objects.isNull(curOrderInfo) || Objects.isNull(orderMasterInfo) || Objects.isNull(curOrderInfo.getOrder())) {
            return;
        }

        // 待收货状态才返回
        if (!Objects.equals(OrderStatusEnum.WaitingForReceive.getValue(), curOrderInfo.getOrder().getStatus())) {
            return;
        }
        List<OrderApprovalInfoVO> orderApprovalInfoList = curOrderInfo.getOrderApprovalInfoList();
        if (CollectionUtils.isEmpty(orderApprovalInfoList)) {
            return;
        }
        
        // 查找最新的验收审批记录
        OrderApprovalInfoVO latestApprovalInfo = orderApprovalInfoList.stream()
                .filter(approval -> OrderApprovalEnum.PASS.equals(approval.getOrderApprovalStatus())
                        || OrderApprovalEnum.REJECT.equals(approval.getOrderApprovalStatus()))
                .max(Comparator.comparing(OrderApprovalInfoVO::getCreationTime))
                .orElse(null);
        
        // 如果存在验收审批驳回记录，则设置驳回原因
        if (Objects.nonNull(latestApprovalInfo) && OrderApprovalEnum.REJECT.equals(latestApprovalInfo.getOrderApprovalStatus())) {
            if (Objects.isNull(curOrderInfo.getOrderInfoFeature())) {
                curOrderInfo.setOrderInfoFeature(new OrderInfoFeatureVO());
            }
            curOrderInfo.getOrderInfoFeature().setReceiveRejectReason(latestApprovalInfo.getReason());
        }
    }

    /**
     * 填充订单拓展字段
     */
    private void setOrderExtraInfo(OrderInfoVO curOrderInfo, OrderMasterDO orderMasterDO) {
        Integer orgId = orderMasterDO.getFuserid();
        Integer orderId = orderMasterDO.getId();
        List<Integer> orderExtraKeyList = New.list();
        if (Objects.equals(OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE_FU_SHU_YI_YUAN.getValue(), orgId)) {
            orderExtraKeyList.add(OrderExtraEnum.EXPERIMENT_DATA_URL.getValue());
        }

        if (CollectionUtils.isEmpty(orderExtraKeyList)) {
            return;
        }
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), orderExtraKeyList);
        Map<Integer, String> extraKey2Value = New.map();
        for (OrderExtraDTO orderExtraDTO : orderExtraDTOList) {
            extraKey2Value.put(orderExtraDTO.getExtraKey(), orderExtraDTO.getExtraValue());
        }

        if (Objects.isNull(curOrderInfo.getOrderInfoFeature())) {
            curOrderInfo.setOrderInfoFeature(new OrderInfoFeatureVO());
        }

        // 江西中医附院 返回实验室URL
        if (Objects.equals(OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE_FU_SHU_YI_YUAN.getValue(), curOrderInfo.getOrder().getOrgId())) {
            curOrderInfo.getOrderInfoFeature().setExperimentDataUrl(extraKey2Value.get(OrderExtraEnum.EXPERIMENT_DATA_URL.getValue()));
        }
    }


    /**
     * 设置实时订单数据
     *
     * @param curOrderInfo
     * @param orderMasterDO
     */
    private void setRealTimeOrderData(OrderInfoVO curOrderInfo, OrderMasterDO orderMasterDO) {
        OrderMasterVO order = curOrderInfo.getOrder();
        order.setStatus(orderMasterDO.getStatus());
    }


    /**
     * 填充申请取消订单相关信息
     * @param curOrderInfo 当前订单信息
     * @param orderMasterDO 订单主表信息
     */
    private void setCancelOrderInfo(OrderInfoVO curOrderInfo, OrderMasterDO orderMasterDO) {
        if (ObjectUtil.hasNull(curOrderInfo, orderMasterDO)) {
            return;
        }

        // 非关闭或申请取消状态跳过
        Integer status = orderMasterDO.getStatus();
        if (!OrderStatusEnum.PurchaseApplyToCancel.getValue().equals(status)
                && !OrderStatusEnum.SupplierApplyToCancel.getValue().equals(status)
                && !OrderStatusEnum.Close.getValue().equals(status)) {
            return;
        }

        List<OrderApprovalInfoVO> orderApprovalLogList = curOrderInfo.getOrderApprovalInfoList();
        // 取最新一条同意日志
        orderApprovalLogList = orderApprovalLogList.stream()
                .filter(log -> OrderApprovalEnum.SUPPLIER_AGREE_CANCEL_ORDER.equals(log.getOrderApprovalStatus())
                        || OrderApprovalEnum.BUYER_AGREE_CANCEL_ORDER.equals(log.getOrderApprovalStatus()))
                .sorted((o1, o2) -> o2.getCreationTime().compareTo(o1.getCreationTime()))
                .collect(toList());
        if (CollectionUtils.isEmpty(orderApprovalLogList)) {
            return;
        }
        OrderApprovalInfoVO latestApprovalLog = orderApprovalLogList.get(0);

        // 设置同意取消时间
        curOrderInfo.getOrderInfoFeature().setAgreeCancelTime(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, new Date(latestApprovalLog.getCreationTime())));

        // 设置申请取消用户类型
        Integer cancelUserType = null;
        if (OrderApprovalEnum.BUYER_AGREE_CANCEL_ORDER.equals(latestApprovalLog.getOrderApprovalStatus())) {
            // 采购人同意，发起人为供应商
            cancelUserType = OrderLogUserTypeEnum.SUPPLIER.getCode();
        } else if (OrderApprovalEnum.SUPPLIER_AGREE_CANCEL_ORDER.equals(latestApprovalLog.getOrderApprovalStatus())) {
            // 供应商同意，发起人为采购人
            cancelUserType = OrderLogUserTypeEnum.PURCHASE.getCode();
        }
        // 设置发起取消申请的用户类型
        curOrderInfo.getOrderInfoFeature().setCancelUserType(cancelUserType);
    }

    /**
     * 处理订单关闭原因 文案
     */
    private void setCloseReason(OrderInfoVO orderInfoVO, OrderMasterDO masterDO) {
        if (ObjectUtil.hasNull(orderInfoVO, masterDO)) {
            return;
        }
        // 取消原因
        String closeReason = masterDO.getFcancelreason();
        String cancelPrefix;
        if (Objects.equals(OrderStatusEnum.PurchaseApplyToCancel.getValue(), masterDO.getStatus())) {
            cancelPrefix = OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX;
            if (StringUtils.startsWith(closeReason, OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX_OLD)) {
                cancelPrefix = OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX_OLD;
            }
            closeReason = StringUtils.substringAfter(closeReason, cancelPrefix);
        } else if (Objects.equals(OrderStatusEnum.SupplierApplyToCancel.getValue(), masterDO.getStatus())) {
            cancelPrefix = OrderOperateLogConstant.SUPPLIER_CANCEL_REASON_PREFIX;
            if (StringUtils.startsWith(closeReason, OrderOperateLogConstant.SUPPLIER_CANCEL_REASON_PREFIX_OLD)) {
                cancelPrefix = OrderOperateLogConstant.SUPPLIER_CANCEL_REASON_PREFIX_OLD;
            }
            closeReason = StringUtils.substringAfter(closeReason, cancelPrefix);
        }

        // 关闭原因
        if (Objects.equals(OrderStatusEnum.Close.getValue(), masterDO.getStatus())) {
            if (StringUtils.startsWith(closeReason, OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX)
        || StringUtils.startsWith(closeReason, OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX_OLD)) {
                closeReason = "采购人申请取消订单，商家同意取消。";
            } else if (StringUtils.startsWith(closeReason, OrderOperateLogConstant.SUPPLIER_CANCEL_REASON_PREFIX)||
            StringUtils.startsWith(closeReason, OrderOperateLogConstant.SUPPLIER_CANCEL_REASON_PREFIX_OLD)) {
                closeReason = "商家申请取消订单，采购人同意取消。";
            } else if (OrderOperateLogConstant.RETURN_FINISH_REASON.equals(closeReason)) {
                closeReason = "退货完成。";
            } else if (OrderOperateLogConstant.SUPPLIER_TIMEOUT_CANCEL_REASON.equals(closeReason)) {
                closeReason = "供应商超时确认订单，系统自动取消订单。";
            } else {
                // 非上述情况则不显示
                closeReason = null;
            }
        }
        orderInfoVO.getOrderInfoFeature().setCloseReason(closeReason);
    }

    /**
     * 设置经费卡信息
     * @param orderId
     * @param orderMasterInfo
     * @return
     */
    private List<String> getOrderFundCardList(Integer orderId, OrderMasterDO orderMasterInfo) {
        OrderFundCardDTO fundCardCache = null;
        List<String> fundCardIdList = New.list();
        // 经费卡缓存的单位
        List<Integer> orgUseCardCacheList = New.list(OrgEnum.JI_NAN_DA_XUE.getValue(), OrgEnum.NAN_FANG_YI_KE.getValue());
        // 暨南大学|南方医 经费卡逻辑
        if (orgUseCardCacheList.contains(orderMasterInfo.getFuserid())) {
            List<OrderFundCardDTO> fundCardCacheList = orderFundCardCacheClient.findOrderFundCardCache(New.list(orderId));
            fundCardCache = CollectionUtils.isEmpty(fundCardCacheList) ? null : fundCardCacheList.get(0);
        }
        if (fundCardCache != null) {
            fundCardIdList.add(fundCardCache.getFundCardId());
        } else {
            // 查询绑卡和设值
            List<RefFundcardOrderDTO> refFundCardList = refFundcardOrderService.findByOrderIdList(New.list(orderId));
            fundCardIdList = refFundCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        }
        return fundCardIdList;
    }

    /**
     * 设置结算单、汇总单信息
     *
     * @param curOrderInfo 订单信息
     * @param masterDO     订单主表信息
     */
    private void setStatementInfo(OrderInfoVO curOrderInfo, OrderMasterDO masterDO) {
        Integer statementId = masterDO.getStatementId();
        if (Objects.isNull(statementId)) {
            return;
        }
        List<SimpleStatementDTO> statementDTOList = statementPlatformClient.listSummaryAndStatementByIds(New.list(Long.valueOf(statementId)));
        if (CollectionUtils.isEmpty(statementDTOList)) {
            return;
        }
        SimpleStatementDTO simpleStatementDTO = statementDTOList.get(0);
        // 结算单ID
        curOrderInfo.getOrderInfoFeature().setSettlementId(statementId);
        // 汇总单ID
        if (Objects.nonNull(simpleStatementDTO.getSummaryId())) {
            curOrderInfo.getOrderInfoFeature().setSummaryId(simpleStatementDTO.getSummaryId());
        }
    }

    /**
     * 设置经费卡卡号卡名（目前仅中山大学）
     * @param orderMasterInfo
     * @param fundCardIdList
     */
    private OrderFundcardVO mergeFundCardNoAndName(OrderMasterDO orderMasterInfo, List<String> fundCardIdList) {
        OrderFundcardVO fundCard = new OrderFundcardVO();
        String cardNo = StringUtils.EMPTY;
        String cardName = StringUtils.EMPTY;
        String cardsStarTime = StringUtils.EMPTY;
        // 预算系统经费卡查询
        List<FundCardDTO> fundCardList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orderMasterInfo.getFusercode(), fundCardIdList);
        // 一级经费卡不用操作
        boolean singleLevel = SINGLE_LEVEL_ORG_MAP.contains(orderMasterInfo.getFusercode());
        if (!singleLevel) {
            // 二级经费卡转换
            List<FundCardDTO> interFundCardList = New.list();
            for (FundCardDTO fundCardDTO : fundCardList) {
                List<FundCardDTO> subFundCardList = fundCardDTO.getFundCardDTOs();
                boolean containSubFundCard = FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == fundCardDTO.getLevel() && CollectionUtils.isNotEmpty(subFundCardList);
                if (containSubFundCard) {
                    for (FundCardDTO cardDTO : subFundCardList) {
                        if (FundCardLevelEnum.FUND_CARD.getValue() != cardDTO.getLevel()) {
                            continue;
                        }
                        interFundCardList.add(cardDTO);
                    }
                }
            }
            // 只要二级经费卡
            fundCardList = interFundCardList;
        }
        for (FundCardDTO fundCardDTO : fundCardList) {
            if (StringUtils.isNotBlank(fundCardDTO.getCode())) {
                cardName += fundCardDTO.getCode() + ",";
            }
            if (StringUtils.isNotBlank(fundCardDTO.getName())) {
                cardNo += fundCardDTO.getName() + ";";
            }
            if (fundCardDTO.getStartTime() != null) {
                cardsStarTime += DateUtils.format("yyyy-MM", fundCardDTO.getStartTime()) + ";";
            }
        }
        // 经费支出申请单号
        List<RefOrderFundCardDTO> refFundcardOrderDTOList = refFundCardOrderClient.listInOrderId(New.list(orderMasterInfo.getId()));
        if(CollectionUtils.isNotEmpty(refFundcardOrderDTOList)){
            fundCard.setExpenseApplyNo(refFundcardOrderDTOList.get(0).getExpenseApplyNo());
        }

        fundCard.setCardName(cardName);
        fundCard.setCardNo(cardNo);
        fundCard.setCardsStartTime(cardsStarTime);
        return fundCard;
    }

    /**
     * @description: 订单溯源，采购或竞价的单据号设置，包括价格合同单的信息设置
     * @date: 2021/3/11 10:18
     * @author: zengyanru
     * @param orderMasterInfo
     * @param curOrderInfo
     * @return void
     */
    public ApplicationMasterDTO setOrderSource(OrderMasterDO orderMasterInfo, OrderInfoVO curOrderInfo) {
        ApplicationMasterDTO appMasterInfo = null;
        boolean purchaseCheck = OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterInfo.getOrderType()) || OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode().equals(orderMasterInfo.getOrderType());
        if (purchaseCheck) {
            // 获取采购单号码
            ApplicationQueryDTO param = new ApplicationQueryDTO();
            param.setApplyIds(New.list(Long.valueOf(orderMasterInfo.getFtbuyappid())));
            List<ApplicationMasterDTO> appMasterInfoList = applicationBaseClient.findByMasterId(param);
            if (CollectionUtils.isNotEmpty(appMasterInfoList)) {
                appMasterInfo = appMasterInfoList.get(0);
                curOrderInfo.getOrder().setBuyAppNo(appMasterInfo.getApplyNumber());
            }
        } else if (OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterInfo.getOrderType())) {
            curOrderInfo.getOrder().setBidOrderId(orderMasterInfo.getBidOrderId());
            // 根据存在master表中的竞价单数据分别操作。带有“-”的是旧竞价单id。存在bidno则取，否则用bidid替代
            if (orderMasterInfo.getBidOrderId().contains("-")) {
                List<BidApprovalLogDTO> oldApprovalLogInfo = bidClient.findOldApprovalLogInfo(orderMasterInfo.getBidOrderId());
                String replaceBidNo = orderMasterInfo.getBidOrderId()+"(竞价单id)";
                if (CollectionUtils.isEmpty(oldApprovalLogInfo)) {
                    curOrderInfo.getOrder().setBidOrderSn(replaceBidNo);
                } else {
                    BidApprovalLogDTO oldBidInfo = oldApprovalLogInfo.get(0);
                    String bidNo = oldBidInfo == null ? replaceBidNo : oldBidInfo.getBidNo() == null ? replaceBidNo : oldBidInfo.getBidNo();
                    curOrderInfo.getOrder().setBidOrderSn(bidNo);
                }
            } else {
                curOrderInfo.getOrder().setBidOrderSn(orderMasterInfo.getBidOrderId());
            }
        }
        return appMasterInfo;
    }

    /**
     * @description: 为订单（详情）设置退货相关的信息
     * @date: 2021/1/19 11:28
     * @author: zengyanru
     * @param curOrderInfo
     * @param detailIdGoodsReturnMap
     * @return void
     */
    @ServiceLog(description = "为订单（详情）设置退货相关的信息", serviceType = ServiceType.COMMON_SERVICE)
    private void setGoodsReturnInfo(OrderInfoVO curOrderInfo, Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap, Boolean statementCheck) {
        BusinessErrUtil.notEmpty(curOrderInfo.getOrderDetails(), ExecptionMessageEnum.NO_PURCHASED_ITEMS_IN_ORDER);
        List<GoodsReturnOrderDetailVO> goodsReturnInfoList = New.listWithCapacity(curOrderInfo.getOrderDetails().size());
        List<Integer> statusCanReturnGoods = New.list(OrderStatusEnum.WaitingForReceive.value,
                OrderStatusEnum.WaitingForStatement_1.value,
                OrderStatusEnum.OrderReceiveApproval.value,
                OrderStatusEnum.OrderReceiveApprovalTwo.value,
                OrderStatusEnum.ORDER_RECEIVE_APPROVAL_LEVEL_TWO_REJECT.value,
                OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL.getValue(),
                OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL_REJECT.getValue());
        if (Boolean.FALSE.equals(statementCheck)) {
            statusCanReturnGoods.add(OrderStatusEnum.Finish.value);
        } else if (OrderStatusEnum.Finish.getValue().equals(curOrderInfo.getOrder().getStatus()) && Objects.isNull(curOrderInfo.getOrder().getStatementId())){
            statusCanReturnGoods.add(OrderStatusEnum.Finish.value);
        }

        boolean canReturnGoods = statusCanReturnGoods.contains(curOrderInfo.getOrder().getStatus());
        for (OrderDetailVO curOrderDetail : curOrderInfo.getOrderDetails()) {
            // 初始化已退数目
            BigDecimal curReturnQuantity = new BigDecimal(0.00);
            // 退货信息
            if (curOrderDetail.getReturnStatus() != null) {
                List<OrderDetailGoodsReturnBO> goodsReturnList = detailIdGoodsReturnMap.get(curOrderDetail.getId());
                if (CollectionUtils.isEmpty(goodsReturnList)) {
                    continue;
                }
                // 多个退货单，逐个处理统计
                List<GoodsReturnOrderDetailVO> curReturnInfoList = New.listWithCapacity(goodsReturnList.size());
                for (OrderDetailGoodsReturnBO goodsReturn : goodsReturnList) {
                    if (goodsReturn == null) {
                        continue;
                    }
                    if (OrderDetailReturnStatus.CANCELREQUEST_1.getCode().equals(goodsReturn.getGoodsReturnStatus()) ||
                            OrderDetailReturnStatus.CANCELREQUEST_2.getCode().equals(goodsReturn.getGoodsReturnStatus())) {
                        continue;
                    }
                    BigDecimal singleReturnQuantiy = goodsReturn.getQuantity() == null ? new BigDecimal(0.00) : goodsReturn.getQuantity();
                    curReturnQuantity = curReturnQuantity.add(singleReturnQuantiy);
                    GoodsReturnOrderDetailVO goodsReturnVO = new GoodsReturnOrderDetailVO(goodsReturn,curOrderDetail);
                    if(CollectionUtils.isNotEmpty(goodsReturn.getReturnGasBottleBarcodes())){
                        // 提前写入，供后面去查
                        goodsReturnVO.setGasBottles(goodsReturn.getReturnGasBottleBarcodes().stream().map(barcode->
                        new GasBottleVO().setGasBottleCode(barcode)).collect(toList()));
                    }
                    curReturnInfoList.add(goodsReturnVO);
                }
                goodsReturnInfoList.addAll(curReturnInfoList);
            }
            // 总结可退货和已退货的数量
            if (canReturnGoods) {
                Double curCanReturn = BigDecimal.valueOf(curOrderDetail.getQuantity())
                        .subtract(curReturnQuantity)
                        .doubleValue();
                curOrderDetail.setQuantityCanReturn(curCanReturn);
            } else {
                curOrderDetail.setQuantityCanReturn(0.00);
            }
        }
        curOrderInfo.setGoodsReturnInfoList(goodsReturnInfoList);
    }

    /**
     * 获取中大 订单是否在公示状态中
     * @param orderMasterInfo
     * @param appMasterInfo
     * @return 是或否
     */
    @ServiceLog(description = "获取中大 订单是否在公示状态中", serviceType = ServiceType.COMMON_SERVICE)
    private Boolean getPublicity(OrderMasterDO orderMasterInfo, ApplicationMasterDTO appMasterInfo) {
        BusinessErrUtil.notNull(orderMasterInfo.getFusercode(), ExecptionMessageEnum.ORDER_CANNOT_FIND_UNIT);
        if (orderMasterInfo.getFtbuyappid() != null && ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orderMasterInfo.getFusercode())) {
            Integer publicDays = 0;
            try {
                // 通过配置获取公示日数
                List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orderMasterInfo.getFusercode(),New.list(ConfigConstant.PURCHASEAPPLY_FIRST_OFFLINE_PUBLICITY));
                if (CollectionUtils.isNotEmpty(baseConfigList)) {
                    BaseConfigDTO baseConfig = baseConfigList.get(0);
                    if (baseConfig != null) {
                        publicDays = Integer.valueOf(baseConfig.getConfigValue());
                    }
                }
            } catch (Exception e) {
                publicDays = SYSU_DEFAULT_PUBLIC_VALUE;
            }

            if (publicDays > 0) {
                return this.checkPublicity(publicDays,appMasterInfo);
            }
        }
        return false;
    }

    /**
     * 获取物流信息
     * @param orderMasterInfo
     * @return 物流信息
     */
    private OrderLogisticsInfoVO getLogisticsInfo(OrderMasterDO orderMasterInfo) {
        List<OrderDeliveryInfoDTO> orderDeliveryInfoDTOList = suppClient.queryOrderDeliveryInfo(New.list(orderMasterInfo.getId()));
        OrderLogisticsInfoVO logisticsVO = new OrderLogisticsInfoVO();
        if (CollectionUtils.isEmpty(orderDeliveryInfoDTOList) || orderDeliveryInfoDTOList.get(0) == null) {
            logisticsVO.setDeliveryDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,orderMasterInfo.getFdeliverydate()));
            logisticsVO.setDeliveryNo(orderMasterInfo.getDeliveryNo());
            logisticsVO.setDeliveryType(0);
            logisticsVO.setDeliveryRemark(orderMasterInfo.getDeliveryInfo());
        } else {
            OrderDeliveryInfoDTO deliveryInfoDTO = orderDeliveryInfoDTOList.get(0);
            logisticsVO.setDeliveryDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, deliveryInfoDTO.getDeliveryDate()));
            logisticsVO.setDeliveryNo(deliveryInfoDTO.getDeliveryNo());
            logisticsVO.setDeliveryRemark(deliveryInfoDTO.getDeliveryRemark());
            logisticsVO.setDeliveryType(deliveryInfoDTO.getDeliveryType());
            logisticsVO.setLogisticsCompany(deliveryInfoDTO.getLogisticsCompany());
            logisticsVO.setLogisticsCompanyLink(deliveryInfoDTO.getLogisticsCompanyLink());
            logisticsVO.setSubscribeId(deliveryInfoDTO.getSubscribeId());
            logisticsVO.setOrderId(orderMasterInfo.getId());
            logisticsVO.setOrderNo(orderMasterInfo.getForderno());
        }
        return logisticsVO;
    }

    /**
     * 获取合同信息
     *
     * @return 合同信息列表
     */
    @ServiceLog(description = "获取合同信息", serviceType = ServiceType.COMMON_SERVICE)
    public void setOrderContractInfo(OrderInfoVO orderInfo) {
        if (Objects.isNull(orderInfo)) {
            return;
        }
        Integer orderId = orderInfo.getOrder().getId();
        List<OrderContract> orderContractList = orderContractMapper.selectByOrderIdIn(New.list(orderId));
        List<ContractInfoVO> contractInfoVOList = New.listWithCapacity(orderContractList.size());

        if (CollectionUtils.isNotEmpty(orderContractList)) {
            // 设置 uploadContractNo，只需要取第一个合同编号
            orderInfo.setUploadContractNo(orderContractList.get(0).getContractNo());
            // 构建合同信息列表
            for (OrderContract orderContract : orderContractList) {
                if (orderContract != null) {
                    ContractInfoVO contractInfoVO = new ContractInfoVO();
                    contractInfoVO.setContractLocation(orderContract.getContractLocation())
                            .setContractName(orderContract.getContractName());
                    contractInfoVOList.add(contractInfoVO);
                }
            }
            orderInfo.setContractInfoVOList(contractInfoVOList);
        }
    }

    /**
     * @description: 通过订单主表信息获取线下单信息(一个单据）
     * @date: 2021/1/19 18:32
     * @author: zengyanru
     * @param orderMasterInfo
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO
     */
    @Override
    @ServiceLog(description = "获取线下单信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderOfflineInfoVO getOfflineInfo(OrderMasterDO orderMasterInfo) {
        if (ProcessSpeciesEnum.OFFLINE.getValue().equals(orderMasterInfo.getSpecies().intValue())) {
            OrderOfflineInfoVO offlineInfo = new OrderOfflineInfoVO();
            offlineInfo.setOffline(true);
            // 线下单额外信息（附件附言等）
            List<OfflineExtraDTO> offlineExtraList = applicationBaseClient.findOfflineByAppIdList(New.list(orderMasterInfo.getFtbuyappid()));
            OrderOfflineExtraVO orderOfflineExtra = this.constructOrderOfflineInfo(offlineExtraList);
            offlineInfo.setOfflineExtraDto(orderOfflineExtra);

            // 采购途径
            Integer procureChannelId = applicationBaseClient.findProcureChannelBySuppIdAndAppId(orderMasterInfo.getFsuppid(), orderMasterInfo.getFtbuyappid());
            String procureChannel = OfflineProcurementSourceEnum.get(procureChannelId).getName();
            offlineInfo.setProcurementChannel(procureChannel);
            return offlineInfo;
        }
        return null;
    }

    @Override
    @ServiceLog(description = "获取线下单信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderOfflineInfoVO> getOfflineInfoList(List<OrderMasterSearchDTO> masterSearchList) {
        List<Integer> suppIdList = New.list();
        List<Integer> applyIdList = New.list();
        Map<Integer, Integer> applyIdOrderIdMap = new HashMap<>();
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            if (ProcessSpeciesEnum.OFFLINE.getValue().equals(masterSearch.getSpecies())) {
                suppIdList.add(masterSearch.getFsuppid());
                applyIdList.add(masterSearch.getFtbuyappid());
                applyIdOrderIdMap.put(masterSearch.getFtbuyappid(), masterSearch.getId());
            }
        }
        if (CollectionUtils.isEmpty(applyIdList)) {
            return New.list();
        }
        // 附件等信息——可并行
        List<OfflineExtraDTO> offlineExtraList = applicationBaseClient.findOfflineByAppIdList(applyIdList);
        Map<Integer, List<OfflineExtraDTO>> applyIdOfflineExtraListMap = offlineExtraList.stream().collect(groupingBy(OfflineExtraDTO::getApplicationId));
        // 采购渠道等信息——可并行
        List<ApplyRefSuppBusinessDTO> applyRefSuppBusinessList = applicationBaseClient.findApplyRefSuppBusinessList(suppIdList, applyIdList);
        Map<Integer, Integer> applyIdProcurementChannelMap = applyRefSuppBusinessList.stream()
                .collect(Collectors.toMap(ApplyRefSuppBusinessDTO::getApplicationId, ApplyRefSuppBusinessDTO::getProcurementChannelId, (oldVal, newVal) -> newVal));

        List<OrderOfflineInfoVO> offlineInfoList = New.list();
        for (Map.Entry<Integer, List<OfflineExtraDTO>> applyIdOfflineExtra : applyIdOfflineExtraListMap.entrySet()) {
            Integer applyId = applyIdOfflineExtra.getKey();
            List<OfflineExtraDTO> extraList = applyIdOfflineExtra.getValue();

            // 找附件
            OrderOfflineExtraVO attachment = this.constructOrderOfflineInfo(extraList);
            // 找采购渠道
            Integer procurementId = applyIdProcurementChannelMap.get(applyId);
            String procureChannel = OfflineProcurementSourceEnum.get(procurementId).getName();
            // 对应的订单id
            Integer orderId = applyIdOrderIdMap.get(applyId);

            OrderOfflineInfoVO offlineInfo = new OrderOfflineInfoVO();
            offlineInfo.setOffline(true);
            offlineInfo.setOfflineExtraDto(attachment);
            offlineInfo.setProcurementChannel(procureChannel);
            offlineInfo.setOrderId(orderId);
            offlineInfoList.add(offlineInfo);
        }
        return offlineInfoList;
    }

    /**
     * 获取退货单详情
     * @param orderIdList
     * @return 退货单详情列表
     */
    @ServiceLog(description = "获取退货单详情", serviceType = ServiceType.COMMON_SERVICE)
    private Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap(List<Integer> orderIdList) {
        List<GoodsReturn> goodsReturnByOrderList = goodsReturnMapper.findByOrderIds(orderIdList);
        goodsReturnByOrderList = goodsReturnByOrderList.stream().filter(s -> GoodsReturnInvalidEnum.NORMAL.getCode().equals(s.getInvalid())).collect(toList());
        Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap = new HashMap<>();
        // 综合退货表和其中存储的json字串整理为订单详情id对应退货信息的map
        for (GoodsReturn goodsReturn : goodsReturnByOrderList) {
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            for (GoodsReturnInfoDetailVO goodsReturnInfoDetail : goodsReturnInfoDetailList) {
                Integer detailId = Integer.valueOf(goodsReturnInfoDetail.getDetailId());
                OrderDetailGoodsReturnBO curGoodsReturn = this.goodsReturnInfoTransform(goodsReturnInfoDetail, goodsReturn);
                if (detailIdGoodsReturnMap.containsKey(detailId)) {
                    detailIdGoodsReturnMap.get(detailId).add(curGoodsReturn);
                } else {
                    detailIdGoodsReturnMap.put(detailId, New.list(curGoodsReturn));
                }
            }
        }
        return detailIdGoodsReturnMap;
    }

    /**
     * @description: 退货表和退货json表的信息转换
     * @date: 2021/2/3 15:10
     * @author: zengyanru
     * @param goodsReturnInfoDetailVO
     * @param goodsReturnDO
     * @return com.ruijing.store.order.business.bo.buyercenter.myorderlist.OrderDetailGoodsReturnBO
     */
    private OrderDetailGoodsReturnBO goodsReturnInfoTransform(GoodsReturnInfoDetailVO goodsReturnInfoDetailVO, GoodsReturn goodsReturnDO) {
        OrderDetailGoodsReturnBO orderDetailGoodsReturnInfo = new OrderDetailGoodsReturnBO();
        orderDetailGoodsReturnInfo.setGoodsReturnId(goodsReturnDO.getId());
        orderDetailGoodsReturnInfo.setDetailId(Integer.valueOf(goodsReturnInfoDetailVO.getDetailId()));
        orderDetailGoodsReturnInfo.setGoodsReturnStatus(goodsReturnDO.getGoodsReturnStatus());
        orderDetailGoodsReturnInfo.setAmount(goodsReturnInfoDetailVO.getAmount());
        orderDetailGoodsReturnInfo.setQuantity(goodsReturnInfoDetailVO.getQuantity());
        orderDetailGoodsReturnInfo.setSupplierName(goodsReturnDO.getSupplierName());
        orderDetailGoodsReturnInfo.setReturnGasBottleBarcodes(goodsReturnInfoDetailVO.getReturnGasBottleBarcodes());
        orderDetailGoodsReturnInfo.setReturnNo(goodsReturnDO.getReturnNo());
        return orderDetailGoodsReturnInfo;
    }

    /**
     * 获取订单留言信息
     * @param buyAppId
     * @param suppId
     * @return 留言信息字串
     */
    @Override
    @ServiceLog(description = "获取订单留言信息", serviceType = ServiceType.COMMON_SERVICE)
    public String getOrderRemark(Integer buyAppId, Integer suppId) {
        if (buyAppId != null) {
            // 采购单的话需要获取留言信息
            BusinessErrUtil.notNull(suppId, ExecptionMessageEnum.CANNOT_FIND_SUPPLIER_INFO);
            List<OrderRemark> remarkInfoList = orderRemarkMapper.selectByFtbuyappidAndFsuppid(buyAppId,suppId);
            if (CollectionUtils.isNotEmpty(remarkInfoList) && remarkInfoList.get(0) != null) {
                return remarkInfoList.get(0).getRemark();
            }
        }
        return null;
    }

    /**
     * 部门特殊的订单审批逻辑
     * @param loginInfo
     * @return 是否可审批
     */
    @ServiceLog(description = "部门特殊的订单审批逻辑", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean specialOrgApprovePermission(LoginUserInfoBO loginInfo, OrderInfoVO curOrderInfo) {
        Preconditions.isTrue(curOrderInfo!=null && loginInfo !=null, "specialOrgApprovePermission方法入参为空");
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(loginInfo.getOrgCode())) {
            // TODO:中大显示审批权限，较长，先保障主流程
            return sysuOrderService.checkReceivePermission(curOrderInfo, loginInfo);
        } else {
            // 如果不是特殊处理，前面已经判断过了 沿用这个状态
            return curOrderInfo.isApprovalPermit();
        }
    }

    /**
     * 获取验收描述
     * @param orderId
     * @return 验收描述
     */
    @ServiceLog(description = "获取验收描述", serviceType = ServiceType.COMMON_SERVICE)
    private String getReceiveDesc(String orderId) {
        List<OrderApprovalLog> orderApprLogList = orderApprovalLogMapper.findByOrderIdDesc(Integer.valueOf(orderId));
        if (CollectionUtils.isNotEmpty(orderApprLogList)) {
            Integer orderApprStatus = orderApprLogList.get(0).getApproveStatus();
            if (OrderApprovalEnum.REJECT_DURING_RECEIVE_BY_BUYER.getValue().equals(orderApprStatus) || OrderApprovalEnum.REJECT_DURING_RECEIVE_BY_FACILITY.getValue().equals(orderApprStatus)) {
                return OrderApprovalEnum.getByValue(orderApprStatus).getName();
            }
        }
        return null;
    }

    /**
     * 获取订单送货人电话
     * @param orderMasterInfo
     * @return 送货人电话
     */
    @ServiceLog(description = "获取订单送货人电话", serviceType = ServiceType.COMMON_SERVICE)
    private String getDeliveryMobile(OrderMasterDO orderMasterInfo) {
        if (orderMasterInfo.getFconfirmmanid() != null && orderMasterInfo.getFdeliveryid() == null) {
            UserDTO suppUser = suppClient.getSuppUserById(Integer.valueOf(orderMasterInfo.getFconfirmmanid()));
            if (Objects.nonNull(suppUser)) {
                return suppUser.getMobile();
            }
        } else if (orderMasterInfo.getFdeliverymanid() != null){
            UserDTO suppUser = suppClient.getSuppUserById(Integer.valueOf(orderMasterInfo.getFdeliverymanid()));
            if (Objects.nonNull(suppUser)) {
                return suppUser.getMobile();
            }
        } else {
            // 通过supp查供应商信息并设值
            Integer suppId = orderMasterInfo.getFsuppid();
            if (OrderSpeciesEnum.NORMAL.getValue().equals(orderMasterInfo.getSpecies().intValue())) {
                SupplierDTO suppInfo = suppClient.getSuppById(suppId);
                BusinessErrUtil.notNull(suppInfo, ExecptionMessageEnum.CANNOT_FIND_SUPPLIER_FOR_DETAILS);
                return Objects.nonNull(suppInfo.getContactMan()) ? suppInfo.getContactMan().getMobile() : null;
            } else {
                OfflineSupplierDTO suppInfo = suppClient.getOfflineSuppById(suppId);
                return suppInfo.getMobile();
            }
        }
        return null;
    }

    /**
     * 判断是否订单在公示中，默认已经进入了中大逻辑，否则不可使用
     * @param applicationMasterInfo
     * @param publicDays
     * @return
     */
    @ServiceLog(description = "判断是否订单在公示中", serviceType = ServiceType.COMMON_SERVICE)
    private Boolean checkPublicity(Integer publicDays, ApplicationMasterDTO applicationMasterInfo) {
        if (applicationMasterInfo == null) {
            return Boolean.FALSE;
        }
        if (applicationMasterInfo.getSpecies() == null) {
            return Boolean.FALSE;
        }
        if (publicDays <= 0 || !ProcessSpeciesEnum.OFFLINE.getValue().equals(applicationMasterInfo.getSpecies())) {
            return Boolean.FALSE;
        }
        if (PurchaseApproveStatusEnum.APPROVAL_2.value.equals(applicationMasterInfo.getApplyStatusName()) || PurchaseApproveStatusEnum.FINAL.value.equals(applicationMasterInfo.getApplyStatusName())) {
            //走非管制品新流程（二级审批通过日志时间~现在是否大于3天来判断）
            if (applicationMasterInfo.getRegulation() == RegulationEnum.NOT_REGULATED.getValue()) {
                return researchCustomClient.getApplyPublicity(applicationMasterInfo.getId().intValue(), publicDays, true);
            } else {
                //走管制品新流程（三级审批通过日志时间~现在是否大于3天来判断）
                return researchCustomClient.getApplyPublicity(applicationMasterInfo.getId().intValue(), publicDays,false);
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 通过采购服务来源的线下单信息构造订单服务的线下单信息（单个订单的）
     * @param offlineExtraList
     * @return 构建好的线下单额外信息
     */
    @ServiceLog(description = "通过采购服务来源的线下单信息构造订单服务的线下单信息", serviceType = ServiceType.COMMON_SERVICE)
    private OrderOfflineExtraVO constructOrderOfflineInfo(List<OfflineExtraDTO> offlineExtraList) {
        // 取其中包含附件的，不然就包含内容的，否则返回第一条记录
        offlineExtraList.removeIf(Objects::isNull);
        if (CollectionUtils.isNotEmpty(offlineExtraList)) {
            List<OfflineExtraDTO> extraInfoWithAttach = offlineExtraList.stream().filter(s -> s.getAttachments() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(extraInfoWithAttach)) {
                return this.appOfflineToOrderOfflineTransform(extraInfoWithAttach.get(0));
            }
            List<OfflineExtraDTO> extraInfoWithContext = offlineExtraList.stream().filter(s -> s.getContext() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(extraInfoWithContext)) {
                return this.appOfflineToOrderOfflineTransform(extraInfoWithContext.get(0));
            }
            return this.appOfflineToOrderOfflineTransform(offlineExtraList.get(0));
        }
        return new OrderOfflineExtraVO();
    }

    /**
     * apply service的线下单额外信息转移为order service的订单额外信息
     * @param offlineExtra
     * @return 采购线下单额外信息dto转换为订单服务的线下单额外信息vo
     */
    @ServiceLog(description = "apply service的线下单额外信息转移为order service的订单额外信息", serviceType = ServiceType.COMMON_SERVICE)
    private OrderOfflineExtraVO appOfflineToOrderOfflineTransform(OfflineExtraDTO offlineExtra) {
        OrderOfflineExtraVO orderOfflineExtra = new OrderOfflineExtraVO();
        if (offlineExtra.getAttachments() != null) {
            orderOfflineExtra.setAttachments(New.list(offlineExtra.getAttachments().split(";")));
        }
        if (offlineExtra.getContext() != null) {
            orderOfflineExtra.setContext(offlineExtra.getContext());
        }
        orderOfflineExtra.setBuyApplicationMasterId(offlineExtra.getApplicationId());
        orderOfflineExtra.setId(offlineExtra.getId());
        return orderOfflineExtra;
    }

    /**
     * 判断是否可以追加验收图片
     * @param canAcceptByPermission
     * @param orderMasterInfo
     * @param loginInfo
     * @return 能否追加验收图片的枚举（先不改为boolean，因为可能具有其他的追加方式，待讨论）
     */
    @ServiceLog(description = "判断是否可以追加验收图片", serviceType = ServiceType.COMMON_SERVICE)
    private Integer canAddReceptPic(Boolean canAcceptByPermission, OrderMasterDO orderMasterInfo, LoginUserInfoBO loginInfo) {
        // 是否具有权限追加
        if (Boolean.TRUE.equals(canAcceptByPermission)) {
            Integer orderStatus = orderMasterInfo.getStatus();
            Boolean canAcceptByStatus = OrderStatusEnum.OrderReceiveApproval.value.equals(orderStatus)
                    || OrderStatusEnum.WaitingForStatement_1.value.equals(orderStatus)
                    || OrderStatusEnum.Statementing_1.value.equals(orderStatus)
                    || OrderStatusEnum.Finish.value.equals(orderStatus);
            //金凤要求只能在待验收审批才可以追加
            if(OrgEnum.JIN_FENG_SHI_YAN_SHI.getCode().equals(loginInfo.getOrgCode())){
                canAcceptByStatus = OrderStatusEnum.OrderReceiveApproval.value.equals(orderStatus);
            }

            // 订单状态是否允许追加
            if (Boolean.TRUE.equals(canAcceptByStatus)) {
                List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(loginInfo.getOrgCode(), New.list(ConfigConstant.ORG_RECEIPT_PIC_CONFIG));
                String configValue = configList.get(0).getConfigValue();
                // 配置是否允许追加
                Boolean canAcceptByConfig = OrderAcceptByPhotoEnum.COMPULSORY.getValue().toString().equals(configValue)
                        || OrderAcceptByPhotoEnum.NOT_COMPULSORY.getValue().toString().equals(configValue)
                        || OrderAcceptByPhotoEnum.COMPULSORY_EXCEPT_SERVICE.getValue().toString().equals(configValue);
                if (Boolean.TRUE.equals(canAcceptByConfig)) {
                    return OrderAcceptAddPicEnum.CAN_ADD_PIC.getValue();
                }
            }
        }
        return OrderAcceptAddPicEnum.CAN_NOT_ADD_PIC.getValue();
    }

    /**
     * @param detailId
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderSnapshotVO
     * @description: 通过订单id获取采购单或者竞价单快照信息
     * @date: 2021/1/4 15:01
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "通过订单id获取采购单或者竞价单快照信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderSnapshotVO getOrderSnapshot(Integer detailId) {
        Preconditions.notNull(detailId, "获取订单商品快照入参订单详情id不可为空");
        // 使用传入的商品 订单detailid，需要服务内组装。TODO：商品描述使用url返回给前端，描述保存到detail表，新增字段；
        OrderDetailDO orderDetail = orderDetailMapper.selectByPrimaryKey(detailId);
        BusinessErrUtil.notNull(orderDetail, ExecptionMessageEnum.NO_PRODUCT_INFO_FOR_ORDER);
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderDetail.getFmasterid());

        OrderSnapshotVO orderSnapshotVO = new OrderSnapshotVO();
        orderSnapshotVO.setDetailId((long) detailId);
        // 获取产品供应商的信息
        Integer suppId = orderDetail.getSuppId();
        SupplierDTO supp = suppClient.getSuppById((suppId));
        BusinessErrUtil.notNull(supp, ExecptionMessageEnum.CANNOT_FIND_SUPPLIER_FOR_ORDER, detailId);
        orderSnapshotVO.setSuppId(supp.getId());
        orderSnapshotVO.setSuppName(supp.getSupplierName());
        orderSnapshotVO.setSuppCode(supp.getSupplierCode());
        orderSnapshotVO.setSuppMobile(Objects.nonNull(supp.getContactMan()) ? supp.getContactMan().getMobile() : null);
        orderSnapshotVO.setSuppTelephone(supp.getTelephone());
        orderSnapshotVO.setSuppQQ(supp.getQq());
        orderSnapshotVO.setProvince(Objects.nonNull(supp.getLocation()) ? supp.getLocation().getProvince() : null);
        orderSnapshotVO.setCity(Objects.nonNull(supp.getLocation()) ? supp.getLocation().getCity() : null);
        orderSnapshotVO.setCounty(Objects.nonNull(supp.getLocation()) ? supp.getLocation().getDistrict() : null);

        // 线上单设置供应商证书信息
        if (Objects.equals(orderMaster.getSpecies().intValue(), OrderSpeciesEnum.NORMAL.getValue())) {
            Map<Integer, QualificationDTO> suppContactInfoMap = suppClient.getSuppContactInfo(New.list(suppId));
            QualificationDTO suppQual = suppContactInfoMap.get(suppId);
            if (suppQual != null) {
                orderSnapshotVO.setLicensePic(suppQual.getLicensePic());
                orderSnapshotVO.setLicensed(true);
            } else {
                orderSnapshotVO.setLicensed(false);
            }
        }

        // 商品代理品牌
        SuppBrandDTO suppBrand = suppClient.getSuppBrand(suppId, orderDetail.getFbrandid());
        if (suppBrand != null) {
            orderSnapshotVO.setSuppBrandLevel(suppBrand.getLevel());
        }

        // 商品详细信息
        Preconditions.notNull(orderDetail.getFquantity(), "采购数量记录为空，需要确认此单的流程正确性，请联系客服");
        orderSnapshotVO.setProductName(orderDetail.getFgoodname());
        orderSnapshotVO.setBrandName(orderDetail.getBrandEname());
        orderSnapshotVO.setProductPrice(orderDetail.getFbidprice() == null ? 0.0 : orderDetail.getFbidprice().doubleValue());
        orderSnapshotVO.setProductPicPaths(New.list(orderDetail.getFpicpath()));
        orderSnapshotVO.setGoodCode(orderDetail.getFgoodcode());
        orderSnapshotVO.setUnit(orderDetail.getFunit());
        orderSnapshotVO.setDeliveryTime(orderDetail.getDeliveryTime());
        orderSnapshotVO.setSpecification(orderDetail.getFspec());
        orderSnapshotVO.setCasNumber(orderDetail.getCasno());
        orderSnapshotVO.setCarryFee(Optional.ofNullable(orderDetail.getCarryFee()).map(BigDecimal::doubleValue).orElse(0.0));
        orderSnapshotVO.setQuantity(Optional.ofNullable(orderDetail.getFquantity()).map(BigDecimal::intValue).orElse(0));
        orderSnapshotVO.setFirstCategoryId(orderDetail.getFirstCategoryId());
        orderSnapshotVO.setFirstCategoryName(orderDetail.getFirstCategoryName());
        orderSnapshotVO.setSecondCategoryId(orderDetail.getSecondCategoryId());
        orderSnapshotVO.setSecondCategoryName(orderDetail.getSecondCategoryName());
        // TODO:可能不需要的三级分类id，只保留名字
        String thirdCategoryName = Objects.equals(orderDetail.getSecondCategoryName(), orderDetail.getFclassification()) || Objects.equals(orderDetail.getFirstCategoryName(), orderDetail.getFclassification())? null : orderDetail.getFclassification();
        orderSnapshotVO.setThirdCategoryName(thirdCategoryName);
        orderSnapshotVO.setRegulatoryType(orderDetail.getRegulatoryTypeId());
        orderSnapshotVO.setRegulatoryTypeName(orderDetail.getRegulatoryTypeName());

        // 危化品在枚举类上的的特殊处理
        Integer dangerousTypeId = orderDetail.getDangerousTypeId();
        // 判断null避免旧数据导致的出错
        if (dangerousTypeId != null) {
            boolean undangerousChems = Objects.equals(DangerousTypeEnum.LABORATORY_ANIMAL.getValue(), dangerousTypeId.intValue())
                    || Objects.equals(DangerousTypeEnum.UN_DANGEROUS.getValue(), dangerousTypeId.intValue())
                    || Objects.equals(DangerousTypeEnum.CONVENTIONAL_CHEMICALS.getValue(), dangerousTypeId.intValue());
            if (!undangerousChems) {
                orderSnapshotVO.setDangerousType(orderDetail.getDangerousTypeId());
                orderSnapshotVO.setDangerousTypeName(orderDetail.getDangerousTypeName());
            }
        }

        String productDesc = "";
        Integer orderType = orderMaster.getOrderType();
        List<Integer> normalOrderTypeList = Arrays.stream(OrderTypeEnum.values()).map(OrderTypeEnum::getCode).collect(toList());
        BusinessErrUtil.isTrue(normalOrderTypeList.contains(orderType), ExecptionMessageEnum.NO_SOURCE_TYPE_FOR_ORDER);
        // 商品快照描述表的枚举和订单详情表-单据来源的枚举 不一样
        Integer orderTypeForDesc;
        String detailIdForDesc;
        if (orderType.equals(OrderTypeEnum.PURCHASE_ORDER.getCode())) {
            orderTypeForDesc = ORDER_TYPE_FOR_DESC_PURCHASE;
            detailIdForDesc = orderMaster.getFtbuyappid().toString();
            // 采购单快照，获取广告、优势等数据
            ApplyProductSnapshotDTO applyProductSnapshotDTO = applicationBaseClient.getProductSnapshot(orderMaster.getFtbuyappid(), orderDetail.getProductSn());
            if(applyProductSnapshotDTO != null){
                orderSnapshotVO.setSubtitle(applyProductSnapshotDTO.getSubtitle());
                orderSnapshotVO.setAdvertisementUrl(applyProductSnapshotDTO.getAdvertisementUrl());
                if(applyProductSnapshotDTO.getShopDetailsTemplate() != null){
                    ProductTemplateDTO shopDetailsTemplate = new ProductTemplateDTO();
                    shopDetailsTemplate.setMaterialContent(applyProductSnapshotDTO.getShopDetailsTemplate().getMaterialContent());
                    shopDetailsTemplate.setMaterialType(applyProductSnapshotDTO.getShopDetailsTemplate().getMaterialType());
                    orderSnapshotVO.setShopDetailsTemplate(shopDetailsTemplate);
                }
                if(applyProductSnapshotDTO.getBrandDetailsTemplate() != null){
                    ProductTemplateDTO brandDetailsTemplate = new ProductTemplateDTO();
                    brandDetailsTemplate.setMaterialContent(applyProductSnapshotDTO.getBrandDetailsTemplate().getMaterialContent());
                    brandDetailsTemplate.setMaterialType(applyProductSnapshotDTO.getBrandDetailsTemplate().getMaterialType());
                    orderSnapshotVO.setBrandDetailsTemplate(brandDetailsTemplate);
                }
            }
        } else {
            orderTypeForDesc = ORDER_TYPE_FOR_DESC_BID;
            detailIdForDesc = detailId.toString();
        }
        try {
            List<ProductDescriptionSnapshotDO> descSnapshotList = descSnapshotMapper.findProductDescByIds(detailIdForDesc,orderTypeForDesc,orderDetail.getProductSn());
            if (CollectionUtils.isEmpty(descSnapshotList)) {
                productDesc = "未找到商品描述信息";
            } else {
                ProductDescriptionSnapshotDO descSnapshot = descSnapshotList.get(0);
                productDesc = descSnapshot.getDescription();
            }
        } catch (Exception e) {
            productDesc = "未找到商品描述信息";
        }
        orderSnapshotVO.setDescription(productDesc);
        // 处理国际化
        translateField(orderSnapshotVO);
        return orderSnapshotVO;
    }

    /**
     * 处理国际化
     */
    private void translateField(OrderSnapshotVO orderSnapshotVO) {
        if (Objects.isNull(orderSnapshotVO)) {
            return;
        }
        // 商品分类国际化
        List<String> categoryNameList = New.list(orderSnapshotVO.getFirstCategoryName(), orderSnapshotVO.getSecondCategoryName(), orderSnapshotVO.getThirdCategoryName());
        Map<String, String> categoryNameMap = suppClient.batchTranslateCategoryName(categoryNameList);
        orderSnapshotVO.setFirstCategoryName(categoryNameMap.get(orderSnapshotVO.getFirstCategoryName()));
        orderSnapshotVO.setSecondCategoryName(categoryNameMap.get(orderSnapshotVO.getSecondCategoryName()));
        orderSnapshotVO.setThirdCategoryName(categoryNameMap.get(orderSnapshotVO.getThirdCategoryName()));
    }

    /**
     * 获取订单推送状态数据
     * @param orderMasterDo 订单数据
     * @return 订单推送状态
     */
    private List<OrderPushEventStatusVO> getOrderPushEventStatus(OrderMasterDO orderMasterDo){
        List<OrderPushEventStatusVO> orderPushEventStatusVoList = New.list();
        // 如果是中大办公，进行查询
        if (ZhongShanDaXueBelongUtils.isBanGongByOrgCode(orderMasterDo.getFusercode())) {
            OrderEventStatusRequestDTO orderEventStatusRequestDto = new OrderEventStatusRequestDTO();
            orderEventStatusRequestDto.setOrderNoList(New.list(orderMasterDo.getForderno()));
            orderEventStatusRequestDto.setOrderPushEventEnumList(New.list(OrderPushEventEnum.PUSH_TO_SUPPLIER,OrderPushEventEnum.BUYER_CANCEL_TO_SUPPLIER_WHEN_WAITING_FOR_DELIVERY));
            // 查询thunder记录的订单推送状态
            List<OrderEventStatusResponseDTO> orderEventStatusResponseDtoList = orderPushEventStatusClient.listEventPushStatus(orderEventStatusRequestDto);
            for(OrderEventStatusResponseDTO orderEventStatusResponseDto : orderEventStatusResponseDtoList){
                OrderPushEventStatusVO orderPushEventStatusVo = new OrderPushEventStatusVO();
                orderPushEventStatusVo.setOrderPushEventType(orderEventStatusResponseDto.getOrderPushEventEnum().getValue());
                orderPushEventStatusVo.setOrderPushEventStatus(orderEventStatusResponseDto.getOrderEventStatusEnum().getValue());
                // 失败时返回原因
                if(OrderEventStatusEnum.FAILED.equals(orderEventStatusResponseDto.getOrderEventStatusEnum())){
                    orderPushEventStatusVo.setFailReason(orderEventStatusResponseDto.getFailReason());
                }
                orderPushEventStatusVoList.add(orderPushEventStatusVo);
            }
            return orderPushEventStatusVoList;
        }
        return orderPushEventStatusVoList;
    }

    /**
     * 构建绑定的气瓶数据
     * @param curOrderInfo 订单信息
     */
    private void constructBindGasBottle(OrderInfoVO curOrderInfo){
        // 1.先获取订单绑定的所有气瓶码
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(New.list(curOrderInfo.getOrder().getId()), null);
        Map<Integer, List<String>> detailIdGasBottleBarcodeMap = orderDetailExtraDTOList.stream().filter(orderDetailExtraDTO -> OrderDetailExtraEnum.BIND_GAS_BOTTLE_BARCODE.getType().equals(orderDetailExtraDTO.getExtraKeyType()))
                .collect(Collectors.toMap(OrderDetailExtraDTO::getOrderDetailId, orderDetailExtraDTO -> JsonUtils.parseList(orderDetailExtraDTO.getExtraValue(), String.class), (o, n)->n));
        List<String> gasBottleBarcodes = detailIdGasBottleBarcodeMap.values().stream().flatMap(List::stream).collect(toList());
        if(CollectionUtils.isEmpty(gasBottleBarcodes)){
            return;
        }
        // 2.获取相关的气瓶数据
        List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(gasBottleBarcodes);
        Map<String, GasBottleVO> gasBottleCodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
        // 3.设置订单详情里面的气瓶数据
        curOrderInfo.getOrderDetails().forEach(detail->{
            List<String> matchBarcodeList = detailIdGasBottleBarcodeMap.get(detail.getId());
            if(CollectionUtils.isNotEmpty(matchBarcodeList)){
                detail.setGasBottles(matchBarcodeList.stream().map(gasBottleCodeIdentityMap::get).filter(Objects::nonNull).collect(toList()));
            }
        });
        // 4.设置退货单的气瓶数据
        curOrderInfo.getGoodsReturnInfoList().forEach(goodsReturnOrderDetailVO -> {
            if(CollectionUtils.isNotEmpty(goodsReturnOrderDetailVO.getGasBottles())){
                List<GasBottleVO> fullGasBottleVOList = goodsReturnOrderDetailVO.getGasBottles().stream().map(vo->gasBottleCodeIdentityMap.get(vo.getGasBottleCode())).filter(Objects::nonNull).collect(toList());
                goodsReturnOrderDetailVO.setGasBottles(fullGasBottleVOList);
            }
        });
    }

    /**
     * 是否有权限验收审批订单（现在有或者曾经审过）
     * @param userId 用户id
     * @param orderMasterInfo 订单快照
     * @return 是否有权限审批
     */
    private boolean haveAccessAcceptApproveOrder(Integer userId, OrderMasterDO orderMasterInfo){
        List<Integer> currentOrderIdList = New.list(orderMasterInfo.getId());
        List<Integer> haveAuthAcceptApproveOrderIds = acceptApprovalClient.filterOrderUserHasAuthApprove(orderMasterInfo.getFuserid(), userId, currentOrderIdList);
        if(CollectionUtils.isNotEmpty(haveAuthAcceptApproveOrderIds)){
           return true;
        }
        List<OrderApprovalLog> acceptApprovalLogList = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(currentOrderIdList, New.list(OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.REJECT.getValue()));
        if(CollectionUtils.isNotEmpty(acceptApprovalLogList)){
            return acceptApprovalLogList.stream().anyMatch(log->userId.equals(log.getOperatorId()));
        }
        return false;
    }

    /**
     * 运营商核实信息
     * @param curOrderInfo 订单数据
     */
    private void constructPlatformOperatorData(OrderInfoVO curOrderInfo){
        if(!ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(curOrderInfo.getOrder().getOrgId())){
            return;
        }
        List<PlatformOperatorApprovalInfoDTO> platformOperatorApprovalInfoDTOList = platformOperatorApprovalRpcClient.listPlatformOperatorApprovalInfoByOrderNos(New.list(curOrderInfo.getOrder().getOrderNo()));
        if(CollectionUtils.isNotEmpty(platformOperatorApprovalInfoDTOList)){
            PlatformOperatorApprovalInfoDTO platformOperatorApprovalInfoDTO = platformOperatorApprovalInfoDTOList.get(0);
            if(CollectionUtils.isNotEmpty(platformOperatorApprovalInfoDTO.getVerifyMaterials())){
                curOrderInfo.getOrderInfoFeature().setVerifyMaterials(platformOperatorApprovalInfoDTO.getVerifyMaterials().stream().map(OrderUploadFileTranslator::sysuUploadFileDTO2OrderFileVO).collect(toList()));
            }
            if(CollectionUtils.isNotEmpty(platformOperatorApprovalInfoDTO.getSupplementaryDeliveryNotes())){
                curOrderInfo.getOrderInfoFeature().setSupplementaryDeliveryNotes(platformOperatorApprovalInfoDTO.getSupplementaryDeliveryNotes().stream().map(OrderUploadFileTranslator::sysuUploadFileDTO2OrderFileVO).collect(toList()));
            }
        }
    }
}
