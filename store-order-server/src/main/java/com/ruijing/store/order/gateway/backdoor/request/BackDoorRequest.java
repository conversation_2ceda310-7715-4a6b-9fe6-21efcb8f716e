package com.ruijing.store.order.gateway.backdoor.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.util.List;
import java.util.StringJoiner;

/**
 * Name: FixRiskAmountReturnRequest
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/7/14
 */
public class BackDoorRequest {

    private String slang;

    @RpcModelProperty("起始id")
    private Integer startId;

    @RpcModelProperty("截止id")
    private Integer endId;

    @RpcModelProperty("订单号列表")
    private List<String> orderNoList;

    @RpcModelProperty("工号")
    private String jobNumber;

    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("每秒最大写入数量")
    private Integer writeCountPerSecond;

    public String getSlang() {
        return slang;
    }

    public BackDoorRequest setSlang(String slang) {
        this.slang = slang;
        return this;
    }

    public Integer getStartId() {
        return startId;
    }

    public BackDoorRequest setStartId(Integer startId) {
        this.startId = startId;
        return this;
    }

    public Integer getEndId() {
        return endId;
    }

    public BackDoorRequest setEndId(Integer endId) {
        this.endId = endId;
        return this;
    }

    public Integer getWriteCountPerSecond() {
        return writeCountPerSecond;
    }

    public BackDoorRequest setWriteCountPerSecond(Integer writeCountPerSecond) {
        this.writeCountPerSecond = writeCountPerSecond;
        return this;
    }


    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public BackDoorRequest setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
        return this;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public BackDoorRequest setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public BackDoorRequest setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BackDoorRequest.class.getSimpleName() + "[", "]")
                .add("slang='" + slang + "'")
                .add("startId=" + startId)
                .add("endId=" + endId)
                .add("orderNoList=" + orderNoList)
                .add("jobNumber='" + jobNumber + "'")
                .add("writeCountPerSecond=" + writeCountPerSecond)
                .toString();
    }
}
