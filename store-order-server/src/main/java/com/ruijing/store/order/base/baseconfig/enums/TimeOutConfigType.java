package com.ruijing.store.order.base.baseconfig.enums;
/**
 * <AUTHOR>
 */
public enum TimeOutConfigType // 超时配置项枚举
{
    EXAMINE_CYCLE_LIMIT_AMOUNT(1,"EXAMINE_CYCLE_LIMIT_AMOUNT","验收周期限制张数",10),
    EXAMINE_CYCLE_LIMIT_DAYS(2,"EXAMINE_CYCLE_LIMIT_DAYS","验收周期限制天数",30),
    BALANCE_CYCLE_LIMIT_AMOUNT(3,"BALANCE_CYCLE_LIMIT_AMOUNT","结算周期限制张数",10),
    BALANCE_CYCLE_LIMIT_DAYS(4,"BALANCE_CYCLE_LIMIT_DAYS","结算周期限制天数",30),
    PURCHASER_EXAMINE_CYCLE_LIMIT_AMOUNT(5,"PURCHASER_EXAMINE_CYCLE_LIMIT_AMOUNT","订单超时限制-采购人验收超时限制", 100),
    PURCHASER_BALANCE_CYCLE_LIMIT_AMOUNT(6, "PURCHASER_BALANCE_CYCLE_LIMIT_AMOUNT", "订单超时限制-采购人结算超时限制", 100),
    ORDER_TIMEOUT_DEPT_LIMIT(7,"ORDER_TIMEOUT_DEPT_LIMIT","订单超时限制-课题组限制",1),
    ORDER_TIMEOUT_PURCHASER_LIMIT(8,"ORDER_TIMEOUT_PURCHASER_LIMIT","订单超时限制-采购人限制",0),
    ;

    private final Integer value;
    private final String code;
    private final String name;
    private final Integer defaultSet;

    TimeOutConfigType(Integer value, String code, String name , Integer defaultSet) {
        this.value = value;
        this.name = name;
        this.code = code;
        this.defaultSet = defaultSet;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public Integer getDefaultSet() {
        return defaultSet;
    }

    public static TimeOutConfigType getByCode(String code){
        for(TimeOutConfigType e : TimeOutConfigType.values()){
            if(e.code.equals(code)){
                return e;
            }
        }
        return null;
    }
}
