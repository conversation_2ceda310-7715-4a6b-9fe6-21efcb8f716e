package com.ruijing.store.order.base.core.translator;

import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;

/**
 * 采购单/竞价单 审批日志转换类
 * <AUTHOR>
 * @Date 2020/7/14 7:02 下午
 */
public class PurchaseApprovalLogTranslator {

    /**
     * 采购申请审批日志 dto 转 订单审批 dto
     * @param dto
     * @return
     */
    public static OrderPurchaseApprovalLogDTO dtoToOrderOrderPurchaseLogDTO(PurchaseApprovalLogDTO dto) {
        OrderPurchaseApprovalLogDTO item = new OrderPurchaseApprovalLogDTO();
        item.setDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, dto.getApproveTime()));
        item.setDateTimeStamp(dto.getApproveTime() != null ? dto.getApproveTime().getTime() : null);
        item.setApproverUserId(dto.getApproverId());
        item.setApprover(dto.getApprover());
        // 操作类型
        item.setOperate(dto.getResult());
        // 操作内容
        item.setOperateComment(dto.getComment());
        item.setApproveLevel(dto.getApproveLevel());
        item.setResult(dto.getResult());

        return item;
    }

    /**
     * 采购申请审批日志 dto 转 订单审批 dto
     * @param dto
     * @return
     */
    public static OrderPurchaseApprovalLogDTO dtoToOrderOrderBidLogDTO(BidApprovalLogDTO dto) {
        OrderPurchaseApprovalLogDTO item = new OrderPurchaseApprovalLogDTO();
        item.setDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, dto.getOperateDate()));
        item.setDateTimeStamp(dto.getOperateDate() != null ? dto.getOperateDate().getTime() : null);
        item.setApproverUserId(dto.getOperatorId());
        item.setApprover(dto.getOperatorName());
        // 操作类型
        item.setOperate(dto.getOperationName());
        // 操作内容
        item.setOperateComment(dto.getNote());
        item.setApproveLevel(dto.getLevel());
        item.setResult(dto.getOperationName());
        item.setElectronicSignUrl(dto.getElectronicSignUrl());

        return item;
    }
}
