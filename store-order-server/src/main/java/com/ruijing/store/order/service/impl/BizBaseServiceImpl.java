package com.ruijing.store.order.service.impl;

import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.electronicsign.api.dto.*;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignGroupEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.base.core.enums.ElectronicSignEnum;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.ElectronicSignBO;
import com.ruijing.store.order.rpc.client.ElectronicSignServiceClient;
import com.ruijing.store.order.service.BizBaseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 基础业务组业务方法集成类
 * @author: zhangzhifeng
 * @date: 2021-07-08 14:26
 **/
@Service
@CatAnnotation
@ServiceLog(description = "基础业务组业务方法集成类",serviceType = ServiceType.COMMON_SERVICE)
public class BizBaseServiceImpl implements BizBaseService {

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;
    
    @Override
    public List<ElectronicSignOperationRecordDTO> getElectronicSignData(List<OrderMasterDTO> orderMasterDTOList, OperationListDTO operationListDTO){
        Preconditions.notNull(operationListDTO.getBusinessType(),"单据业务类型为空!");
        Preconditions.notEmpty(orderMasterDTOList,"订单列表为空!");
        Preconditions.notEmpty(operationListDTO.getOperation(),"单据操作类型为空!");
        List<String> businessIdList = new ArrayList<>(orderMasterDTOList.size());
        // businessType与即业务id类型，这边做转换
        switch (operationListDTO.getBusinessType()){
            case BID:
                businessIdList = orderMasterDTOList.stream().map(OrderMasterDTO::getBidOrderId).collect(Collectors.toList());
                break;
            case APPLICATION:
                businessIdList = orderMasterDTOList.stream().map(OrderMasterDTO::getFtbuyappid).map(Object::toString).collect(Collectors.toList());
                break;
            case ORDER:
                businessIdList = orderMasterDTOList.stream().map(OrderMasterDTO::getId).map(Object::toString).collect(Collectors.toList());
                break;
            default:
                break;
        }
        operationListDTO.setBusinessIdList(businessIdList);
        return electronicSignServiceClient.getElectronicSignData(operationListDTO);
    }
    
    /**
     * 保存订单验收电子签名，打印时用
     * @param orderReceiptParamDTO 订单验收入参
     * @param orderMasterDO 订单主表
     */
    @Override
    public void saveAcceptElectronicSign(OrderReceiptParamDTO orderReceiptParamDTO, OrderMasterDO orderMasterDO) {
        Preconditions.notNull(orderReceiptParamDTO,"验收入参不能为空！");
        Preconditions.notNull(orderMasterDO,"订单信息不能为空！");

        ElectronicSignBO electronicSignBO = new ElectronicSignBO();
        electronicSignBO.setOrgCode(orderReceiptParamDTO.getOrgCode());
        electronicSignBO.setUserGuid(orderReceiptParamDTO.getUserGuid());
        electronicSignBO.setOrderId(orderMasterDO.getId());
        electronicSignBO.setUserName(orderReceiptParamDTO.getUserName());
        electronicSignBO.setPassword(orderReceiptParamDTO.getPassword());
        electronicSignBO.setDepartmentId(orderMasterDO.getFbuydepartmentid());
        electronicSignBO.setOperation(ElectronicSignatureOperationEnum.ORDER_RECEIVE);
        this.checkAndSaveElectronicSign(electronicSignBO);
    }

    /**
     * 保存订单验收电子签名，打印时用
     * @param electronicSignBO 电子签名入参
     */
    @Override
    public void checkAndSaveElectronicSign(ElectronicSignBO electronicSignBO) {
        Preconditions.notNull(electronicSignBO,"电子签名入参不能为空！");
        
        String orgCode = electronicSignBO.getOrgCode();
        String userGuid = electronicSignBO.getUserGuid();
        String userName = electronicSignBO.getUserName();
        Integer orderId = electronicSignBO.getOrderId();
        Integer departmentId = electronicSignBO.getDepartmentId();
        ElectronicSignatureOperationEnum operation = electronicSignBO.getOperation();
        
        // 获取电子签名配置
        ElectronicSignOperationDTO electronicSignOperationDTO = electronicSignServiceClient.searchOperationConfig(userGuid, orgCode,
                departmentId, operation);
        Integer electronicSignEnable = electronicSignOperationDTO.getElectronicSignEnable();
        // 不需要电子签名则返回
        if(electronicSignEnable == null || ElectronicSignEnum.FORBID.getType().equals(electronicSignEnable)){
            return;
        }
        String password = null;
        // 是否需要输入密码
        boolean needPassword = !(Boolean.TRUE.equals(electronicSignOperationDTO.getOrgWithoutCode())
                && Boolean.TRUE.equals(electronicSignOperationDTO.getUserWithoutCode()));
        if(needPassword){
            password = electronicSignBO.getPassword();
            BusinessErrUtil.isTrue(StringUtils.isNotBlank(password), ExecptionMessageEnum.PLEASE_INPUT_PASSWORD);
        }
        
        String orderIdStr = orderId.toString();
        // 保存电子签名，打印时用,有密码就会校验密码，无密码则进行保存等其他操作
        this.saveElectronicSign(userGuid, userName, orgCode, operation
                ,orderIdStr, orderIdStr, departmentId.toString(), password);
    }

    /**
     *
     * @param userGuid 用户id
     * @param userName  用户姓名
     * @param orgCode   单位code
     * @param operation 操作
     * @param businessId 业务id(单据Id)
     * @param interactionId 上层业务操作id（用作纪录单据关联的日志Id或者父单据Id等，这边不会使用，调用方自己决定存什么）
     * @param groupCode 部门id
     */
    @Override
    public void saveElectronicSign(String userGuid, String userName, String orgCode, ElectronicSignatureOperationEnum operation
            , String businessId, String interactionId, String groupCode, String password){
        ElectronicSignDTO electronicSignDTO = new ElectronicSignDTO();
        electronicSignDTO.setUserGuid(userGuid);
        electronicSignDTO.setUserName(userName);
        electronicSignDTO.setOrgCode(orgCode);
        electronicSignDTO.setOperation(operation);
        //存当前终审级数，便于区分是哪一级审批的电子签名
        electronicSignDTO.setBusinessId(businessId);
        electronicSignDTO.setBusinessType(BusinessTypeEnum.ORDER);
        electronicSignDTO.setInteractionId(interactionId);
        electronicSignDTO.setEsSign(true);
        electronicSignDTO.setPassword(password);
        electronicSignDTO.setGroupCode(groupCode);
        electronicSignDTO.setGroupEnum(ElectronicSignGroupEnum.PURCHASE);
        // 这个参数不管OMS的签名样式配置哪一项都选择为true；如果选择为系统样式那么智皓的接口就会自动生成系统样式，
        // 如果选择为手写或纸质样式智皓接口就会去校验有没有这两个样式，如果没有就会报错并提示用户去上传
        electronicSignDTO.setUseSystemTypePhoto(true);
        // 保存电子签名记录，打印时用
        electronicSignServiceClient.saveElectronicSign(electronicSignDTO);
    }

    /**
     * 校验电子签名密码
     * @param passWord
     * @param orgCode
     * @param guid
     */
    @Override
    public void checkESignPassWord(String passWord, String orgCode, String guid) {
        ValidatePasswordDTO validatePasswordDTO = new ValidatePasswordDTO();
        validatePasswordDTO.setOrgCode(orgCode);
        validatePasswordDTO.setUserGuid(guid);
        validatePasswordDTO.setPassword(passWord);
        electronicSignServiceClient.validatePassword(validatePasswordDTO);
    }
}
