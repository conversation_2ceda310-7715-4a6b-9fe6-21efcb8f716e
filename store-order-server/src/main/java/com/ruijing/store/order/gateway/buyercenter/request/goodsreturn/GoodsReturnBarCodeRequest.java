package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

@RpcModel("退货条形码模型")
public class GoodsReturnBarCodeRequest implements Serializable {

    private static final long serialVersionUID = -1116988357911181990L;

    @RpcModelProperty("退货单id, 修改退货时必填")
    private Integer returnId;

    @RpcModelProperty("码商品信息, 退货时必填")
    private List<GoodsReturnBarCodeDetailRequest> orderUniqueBarCodeList;

    @RpcModelProperty("退货凭证")
    private List<String> images;

    @RpcModelProperty(value = "退货原因", description = "退货单粒度")
    private String returnReason;

    @RpcModelProperty(value = "退货说明", description = "退货单粒度")
    private String remark;

    public String getReturnReason() {
        return returnReason;
    }

    public GoodsReturnBarCodeRequest setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public GoodsReturnBarCodeRequest setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public List<GoodsReturnBarCodeDetailRequest> getOrderUniqueBarCodeList() {
        return orderUniqueBarCodeList;
    }

    public GoodsReturnBarCodeRequest setOrderUniqueBarCodeList(List<GoodsReturnBarCodeDetailRequest> orderUniqueBarCodeList) {
        this.orderUniqueBarCodeList = orderUniqueBarCodeList;
        return this;
    }

    public List<String> getImages() {
        return images;
    }

    public GoodsReturnBarCodeRequest setImages(List<String> images) {
        this.images = images;
        return this;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public GoodsReturnBarCodeRequest setReturnId(Integer returnId) {
        this.returnId = returnId;
        return this;
    }
}
