package com.ruijing.store.order.business.service.impl;

import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.business.service.OrderMasterForScheduledService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyu<PERSON>i
 * @create: 2019/10/22 17:05
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class OrderMasterForScheduledServiceImpl implements OrderMasterForScheduledService {

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Override
    @ServiceLog(description = "查询超时结算订单",serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderTimeOutDTO> findTimeOutBalance(List<OrganizationConfigDTO> configs, List<Integer> status) {
        Preconditions.notEmpty(configs,"配置信息为空,查询超时结算订单失败!");
        // 过滤无效的配置信息
        List<OrganizationConfigDTO> timeoutConfigs = configs.stream().filter(c -> c.getConfigIntValue() != -1).collect(Collectors.toList());
        return orderMasterMapper.findTimeOutBalanceByConifgs(timeoutConfigs, status);
    }

    @Override
    @ServiceLog(description = "查询超时验收订单",serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderTimeOutDTO> findTimeOutExamine(List<OrganizationConfigDTO> configs, List<Integer> status) {
        Preconditions.notEmpty(configs,"配置信息为空,查询超时验收订单失败!");
        // 过滤无效的配置信息
        List<OrganizationConfigDTO> timeoutConfigs = configs.stream().filter(c -> c.getConfigIntValue() != -1).collect(Collectors.toList());
        return orderMasterMapper.findTimeOutExamineByConfigs(timeoutConfigs, status);
    }
}
