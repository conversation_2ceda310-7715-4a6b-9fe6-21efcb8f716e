package com.ruijing.store.order.base.timeoutstatistics.mapper;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TimeoutStatisticsMapper {
    int deleteByPrimaryKey(Long id);

    int batchDeleteById(@Param("idList")List<Long> idList);

    int insert(TimeoutStatisticsDO record);

    int insertSelective(TimeoutStatisticsDO record);

    TimeoutStatisticsDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimeoutStatisticsDO record);

    int updateByPrimaryKey(TimeoutStatisticsDO record);

    /**
     * 批量插入统计信息
     * @param list
     * @return
     */
    int insertList(@Param("list")List<TimeoutStatisticsDO> list);

    /**
     * 清表操作
     * @return
     */
    int delete();

    /**
     * 查找超时订单数据
     * @return
     */
    List<TimeoutStatisticsDO> findAll();

    /**
     * 根据部门/课题组id 查找 验收/结算 超时统计数据
     * @param orgId 医院/机构 id
     * @param depId 部门/课题组 id
     * @param type  超时类型
     * @return
     */
    TimeoutStatisticsDO queryOneByOrgIdAndDepIdAndType(@Param("orgId")Integer orgId, @Param("depId")Integer depId, @Param("type")Integer type);

    /**
     * 根据部门/课题组id 查找 验收/结算 超时统计数据
     * @param orgId
     * @param depIdCollection
     * @return
     */
    List<TimeoutStatisticsDO> queryByOrgIdAndDepIdIn(@Param("orgId")Integer orgId,@Param("depIdCollection")Collection<Integer> depIdCollection);

    /**
     * 根据 orgId, depId, type 删除统计记录
     * @param orgId
     * @param depId
     * @param type
     * @return
     */
    int deleteByOrgIdAndDepIdAndType(@Param("orgId")Integer orgId,@Param("depId")Integer depId,@Param("type")Integer type);

    /**
     * 通过id对amount进行加减，如果减去的数量大于当前数量，按0处理
     * @param id id
     * @param amount amount
     * @return 修改数量
     */
    int addAmountById(@Param("id")Long id, @Param("amount") Integer amount);
}