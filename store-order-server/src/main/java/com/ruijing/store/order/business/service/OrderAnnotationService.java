package com.ruijing.store.order.business.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.gateway.buyercenter.request.OrderTipsRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderTipsVO;

import java.util.List;
import java.util.Map;

/**
 * @Author: Zeng Yanru
 * @Description: 用于采购人中心，订单相关的弹窗提醒
 * @DateTime: 2021/9/14 15:15
 */
public interface OrderAnnotationService {

    /**
     * 超时结算提醒
     * @param request
     * @return
     */
    RemoteResponse<List<OrderTipsVO>> overtimeSettleHint(Integer orgId, OrderTipsRequest request);

    /**
     * 点击记录到缓存
     * @param request
     * @return
     */
    RemoteResponse<Boolean> clickHints(Integer orgId, OrderTipsRequest request);

    /**
     * 删除登录人的点击历史
     * @param request
     * @return
     */
    RemoteResponse<Boolean> removeClickHistory(Integer orgId, OrderTipsRequest request);


    /**
     * 超时订单数量
     *
     * @param orgId
     * @param userId
     * @param balanceLimitDays
     * @param acceptLimitDay
     * @param timeOutEnums
     * @return
     */
    long getOverTimeCount(Integer orgId, Integer userId, List<Integer> departmentIdList, int balanceLimitDays, int acceptLimitDay, TimeOutEnums timeOutEnums);
}
