package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/19 10:01
 * @Description
 **/
public enum OrderSpeciesEnum {
    NORMAL(0,"线上单"),
    OFFLINE(1,"线下单");

    private Integer value;

    private String name;

    OrderSpeciesEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return name;
    }

    public static final OrderSpeciesEnum getByName(String name) {
        for (OrderSpeciesEnum orderSpeciesEnum : OrderSpeciesEnum.values()) {
            if (orderSpeciesEnum.name.equals(name)){
                return orderSpeciesEnum;
            }
        }
        return null;
    }

    public static final OrderSpeciesEnum getByValue(Integer value) {
        for (OrderSpeciesEnum orderSpeciesEnum : OrderSpeciesEnum.values()) {
            if (orderSpeciesEnum.value.equals(value)){
                return orderSpeciesEnum;
            }
        }
        return null;
    }

}
