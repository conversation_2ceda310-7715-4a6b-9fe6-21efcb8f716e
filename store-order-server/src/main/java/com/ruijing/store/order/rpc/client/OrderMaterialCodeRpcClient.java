package com.ruijing.store.order.rpc.client;

/**
 * Name: OrderMaterialCodeRpcClient
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/13
 */

import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.dto.request.OrderMaterialCodeRequest;
import com.reagent.order.base.order.service.OrderDetailBatchesRPCService;
import com.reagent.order.base.order.service.OrderMaterialCodeRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

@ServiceClient
public class OrderMaterialCodeRpcClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderMaterialCodeRPCService orderMaterialCodeRPCService;


    /**
     * 新增物资编码
     * @param orderMaterialCodeDTOList      新增入参
     * @return                              新增结果
     */
    @ServiceLog(description = "新增物资编码", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer insert(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList){
        RemoteResponse<Integer> response = orderMaterialCodeRPCService.insert(orderMaterialCodeDTOList);
        Preconditions.isTrue(response.isSuccess(), "新增物资编码RPC调用code返回失败:" + response.getMsg());
        return response.getData();
    }


    /**
     * 通过 品牌+货号+规格 查询商品编码
     * @param request               查询入参
     * @return                      查询结果
     */
    @ServiceLog(description = "通过 品牌+货号+规格 查询商品编码", operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public List<OrderMaterialCodeDTO> queryByParam(List<OrderMaterialCodeRequest> request){
        RemoteResponse<List<OrderMaterialCodeDTO>> listRemoteResponse = orderMaterialCodeRPCService.queryByParam(request);
        Preconditions.isTrue(listRemoteResponse.isSuccess(), " 通过 品牌+货号+规格 查询商品物资编码RPC调用返回code失败！" + listRemoteResponse.getMsg());
        return listRemoteResponse.getData();
    }


    /**
     * 更新订单物资编码信息
     * @param orderMaterialCodeDTO  修改入参
     * @return                      修改结果
     */
    @ServiceLog(description = "更新物资编码", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer update(OrderMaterialCodeDTO orderMaterialCodeDTO){
        RemoteResponse<Integer> response = orderMaterialCodeRPCService.update(orderMaterialCodeDTO);
        Preconditions.isTrue(response.isSuccess(), "RPC更新订单物资编码信息code返回失败！" + response.getMsg());
        return response.getData();
    }
}
