package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/28 17:14
 */
public enum OrderAcceptAddPicEnum {

    CAN_NOT_ADD_PIC(0,"不可追加验收图片"),
    CAN_ADD_PIC(1,"可以追加验收图片");

    public final Integer value;

    public final String desc;

    OrderAcceptAddPicEnum(int value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderAcceptAddPicEnum getByName(String name) {
        for (OrderAcceptAddPicEnum orderAcceptAddPicEnum : OrderAcceptAddPicEnum.values()) {
            if (orderAcceptAddPicEnum.desc.equals(name)){
                return orderAcceptAddPicEnum;
            }
        }
        return null;
    }

    public static final OrderAcceptAddPicEnum getByValue(Integer value) {
        for (OrderAcceptAddPicEnum orderAcceptAddPicEnum : OrderAcceptAddPicEnum.values()) {
            if (orderAcceptAddPicEnum.value.equals(value)){
                return orderAcceptAddPicEnum;
            }
        }
        return null;
    }
}
