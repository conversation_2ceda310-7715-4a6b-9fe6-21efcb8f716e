package com.ruijing.store.order.rpc.client;

import com.reagent.research.api.dto.UserDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.sysu.order.api.dto.risk.OrderRiskVerifiedQueryDTO;
import com.reagent.research.sysu.order.api.risl.OrderRiskVerifiedService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

/**
 * <AUTHOR>
 * @description 中大风险订单服务客户端
 * @date 2023/11/29 09
 */
@ServiceClient
public class OrderRiskVerifiedServiceClient {

    @MSharpReference(remoteAppkey = "research-sysu-order-service")
    private OrderRiskVerifiedService orderRiskVerifiedService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "非服务类商品库风险订单校验")
    public void validateOrderRisk(Integer orgId, String orgCode, List<Integer> orderIdList){
        OrgRequest<OrderRiskVerifiedQueryDTO> orgRequest = new OrgRequest<>();
        orgRequest.setOrgId(orgId);
        orgRequest.setOrgCode(orgCode);
        OrderRiskVerifiedQueryDTO orderRiskVerifiedParamDTO = new OrderRiskVerifiedQueryDTO();
        orderRiskVerifiedParamDTO.setOrderIdList(orderIdList);
        orgRequest.setData(orderRiskVerifiedParamDTO);
        UserDTO userDTO = new UserDTO();
        orgRequest.setUserDTO(userDTO);
        RemoteResponse<Boolean> response = orderRiskVerifiedService.validateOrderRisk(orgRequest);
        Preconditions.isTrue(response.isSuccess(), "非服务类商品库风险订单校验失败");
    }
}
