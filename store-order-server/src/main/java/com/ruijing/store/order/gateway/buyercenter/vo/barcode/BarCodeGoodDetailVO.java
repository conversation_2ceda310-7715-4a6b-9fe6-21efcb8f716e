package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * 退货商品信息
 */
@RpcModel("退货商品信息")
public class BarCodeGoodDetailVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单详情ID")
    private Integer orderDetailId;

    @RpcModelProperty("条形码")
    private String barCode;

    @RpcModelProperty("失败原因")
    private String failureReason;

    public String getBarCode() {
        return barCode;
    }

    public BarCodeGoodDetailVO setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public BarCodeGoodDetailVO setFailureReason(String failureReason) {
        this.failureReason = failureReason;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public BarCodeGoodDetailVO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    @Override
    public String toString() {
        return "BarCodeGoodDetailVO{" +
                "orderDetailId=" + orderDetailId +
                ", barCode='" + barCode + '\'' +
                ", failureReason='" + failureReason + '\'' +
                '}';
    }
}