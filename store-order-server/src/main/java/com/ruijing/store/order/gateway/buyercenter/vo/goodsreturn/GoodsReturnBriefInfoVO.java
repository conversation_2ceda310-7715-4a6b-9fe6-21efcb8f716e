package com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/27 14:50
 * @Description
 **/
public class GoodsReturnBriefInfoVO implements Serializable {

    private static final long serialVersionUID = -1818639161285262329L;

    /**
     * 退货id
     */
    @RpcModelProperty("退货id")
    private Integer returnId;

    /**
     * 退货单号
     */
    @RpcModelProperty("退货单号")
    private String returnNo;

    /**
     * 状态
     */
    @RpcModelProperty("状态")
    private Integer goodsReturnStatus;

    /**
     * 关联的订单Id
     */
    @RpcModelProperty("关联的订单id")
    private Integer orderId;

    /**
     * 关联的订单号
     */
    @RpcModelProperty("关联的订单号")
    private String orderNo;

    /**
     * 关联的退货商品信息（订单详情id，商品名字）
     */
    @RpcModelProperty("关联的退货商品信息（订单详情id，商品名字，列表）")
    private List<GoodsReturnProductBriefVO> goodsReturnProductBrief;

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getGoodsReturnStatus() {
        return goodsReturnStatus;
    }

    public void setGoodsReturnStatus(Integer goodsReturnStatus) {
        this.goodsReturnStatus = goodsReturnStatus;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<GoodsReturnProductBriefVO> getGoodsReturnProductBrief() {
        return goodsReturnProductBrief;
    }

    public void setGoodsReturnProductBrief(List<GoodsReturnProductBriefVO> goodsReturnProductBrief) {
        this.goodsReturnProductBrief = goodsReturnProductBrief;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnBriefInfoVO{");
        sb.append("returnId=").append(returnId);
        sb.append(", returnNo='").append(returnNo).append('\'');
        sb.append(", goodsReturnStatus=").append(goodsReturnStatus);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", goodsReturnProductBrief=").append(goodsReturnProductBrief);
        sb.append('}');
        return sb.toString();
    }
}
