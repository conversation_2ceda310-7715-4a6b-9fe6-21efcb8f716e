package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/7/7 0008 10:52
 * @Version 1.0
 * @Desc:描述
 */
public class ElectronicSignInfoVO implements Serializable {
    private static final long serialVersionUID = -5644819768491850131L;
    /**
     * 是否使用电子签名
     */
    @RpcModelProperty(value = "是否使用电子签名")
    private Boolean useESign;
    /**
     * 信息是否已完善
     * 是否实名认证  图片上传  是否免密
     */
    @RpcModelProperty(value = "信息是否完善")
    private Boolean completeInformation;

    /**
     * 是否需要上传图片 true 需要
     */
    @RpcModelProperty(value = "是否需要上传图片(true-需要，false-不需要)")
    private Boolean needUploadESPhoto;
    /**
     * 是否实名认证  false 未实名认证
     */
    @RpcModelProperty(value = "是否实名认证(true-已实名认证，false-未实名认证)")
    private Boolean realNameAuthentication;

    /**
     * 是否需要输入密码
     */
    @RpcModelProperty(value = "是否需要输入密码")
    private Boolean needPassWord;

    /**
     * 类型
     */
    @RpcModelProperty(value = "类型")
    private Integer type;

    /**
     * 电子签名图片url
     */
    @RpcModelProperty(value = "电子签名图片url")
    private String esPhotoUrl;

    public Boolean getUseESign() {
        return useESign;
    }

    public void setUseESign(Boolean useESign) {
        this.useESign = useESign;
    }

    public Boolean getCompleteInformation() {
        return completeInformation;
    }

    public void setCompleteInformation(Boolean completeInformation) {
        this.completeInformation = completeInformation;
    }

    public Boolean getNeedUploadESPhoto() {
        return needUploadESPhoto;
    }

    public void setNeedUploadESPhoto(Boolean needUploadESPhoto) {
        this.needUploadESPhoto = needUploadESPhoto;
    }

    public Boolean getRealNameAuthentication() {
        return realNameAuthentication;
    }

    public void setRealNameAuthentication(Boolean realNameAuthentication) {
        this.realNameAuthentication = realNameAuthentication;
    }

    public Boolean getNeedPassWord() {
        return needPassWord;
    }

    public void setNeedPassWord(Boolean needPassWord) {
        this.needPassWord = needPassWord;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getEsPhotoUrl() {
        return esPhotoUrl;
    }

    public void setEsPhotoUrl(String esPhotoUrl) {
        this.esPhotoUrl = esPhotoUrl;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ElectronicSignInfoVO{");
        sb.append("useESign=").append(useESign);
        sb.append(", completeInformation=").append(completeInformation);
        sb.append(", needUploadESPhoto=").append(needUploadESPhoto);
        sb.append(", realNameAuthentication=").append(realNameAuthentication);
        sb.append(", needPassWord=").append(needPassWord);
        sb.append(", type=").append(type);
        sb.append(", esPhotoUrl='").append(esPhotoUrl).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
