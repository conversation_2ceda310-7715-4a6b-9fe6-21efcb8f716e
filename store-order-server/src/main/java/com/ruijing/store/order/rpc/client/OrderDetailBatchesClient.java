package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.service.OrderDetailBatchesRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

@ServiceClient
public class OrderDetailBatchesClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderDetailBatchesRPCService orderDetailBatchesRPCService;

    @ServiceLog(description = "查询商品订单明细批次", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderDetailBatchesDTO> findOrderDetailBatchesByDetailId(List<Integer> detailIdList) {
        Preconditions.notEmpty(detailIdList, "detailIdList can not be empty");
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailIdList(detailIdList);
        RemoteResponse<List<OrderDetailBatchesDTO>> response = orderDetailBatchesRPCService.findByDetailIdIn(request);
        Preconditions.isTrue(response.isSuccess(), "查询商品订单明细批次异常:" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }
}