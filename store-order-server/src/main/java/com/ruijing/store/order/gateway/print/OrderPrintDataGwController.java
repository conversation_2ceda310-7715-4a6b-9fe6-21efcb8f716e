package com.ruijing.store.order.gateway.print;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.PrintApproveDTO;
import com.ruijing.store.order.gateway.print.dto.PrintDataRequestDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintDataItemDTO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehousePrintDataDTO;
import com.ruijing.store.order.gateway.print.service.OrderPrintDataService;
import com.ruijing.store.order.gateway.print.service.WareHousePrintDataService;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @CreateTime 2023-02-24 14:22
 * @Description
 */
@RpcApi(value = "打印数据服务接口", description = "打印数据服务接口")
@GateWayController(requestMapping = "/print/data")
public class OrderPrintDataGwController {
    
    @Resource
    private OrderPrintDataService orderPrintDataService;
    
    @Resource
    private WareHousePrintDataService wareHousePrintDataService;

    @Resource
    private OrderManageRpcService orderManageRpcService;

    @RpcMapping("/deliveryNote")
    @RpcMethod("送货单数据打印接口提供")
    public RemoteResponse<List<OrderPrintDataItemDTO>> getDeliveryNoteData(PrintDataRequestDTO request) {
        return RemoteResponse.<List<OrderPrintDataItemDTO>>custom().setData(orderPrintDataService.getOrderPrintData(request)).setSuccess();
    }

    @RpcMapping("/acceptance")
    @RpcMethod("验收单数据打印接口提供")
    public RemoteResponse<List<OrderPrintDataItemDTO>> getAcceptanceData(PrintDataRequestDTO request) {
        return RemoteResponse.<List<OrderPrintDataItemDTO>>custom().setData(orderPrintDataService.getOrderPrintData(request)).setSuccess();
    }
    
    @RpcMapping("/inAndOutWarehouse")
    @RpcMethod("入库单打印")
    public RemoteResponse<List<WarehousePrintDataDTO>> getWarehousePrint(PrintDataRequestDTO request){
        List<WarehousePrintDataDTO> warehousePrintDataDTOList = wareHousePrintDataService.getPrintData(request.getSerialNumbers());
        return RemoteResponse.<List<WarehousePrintDataDTO>>custom().setData(warehousePrintDataDTOList).setSuccess();
    }

    /**
     * 获取线下汇总单打印单订单数据
     *
     * @param param
     * @return
     */
    @RpcMapping("/getOfflineSummaryPrintData")
    @RpcMethod(value = "入库单打印")
    public RemoteResponse<List<PrintApproveDTO>> getOfflineSummaryPrintData(@RequestBody OrderBasicParamDTO param) {
        return orderManageRpcService.getOfflineSummaryPrintData(Arrays.asList(param.getOrderId()));
    }

    /**
     * 获取线下汇总单打印单订单数据
     *
     * @param param
     * @return
     */
    @RpcMapping("/getCommonSummaryPrintData")
    @RpcMethod(value = "获取验收单打印订单数据")
    public RemoteResponse<List<PrintApproveDTO>> getCommonSummaryPrintData(@RequestBody OrderBasicParamDTO param) {
        return orderManageRpcService.getCommonSummaryPrintData(Arrays.asList(param.getOrderId()));
    }
}
