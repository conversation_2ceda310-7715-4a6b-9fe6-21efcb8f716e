package com.ruijing.store.order.base.core.mapper;
import com.ruijing.store.order.base.core.model.RefOrderDetailTagDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface RefOrderDetailTagDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RefOrderDetailTagDO record);

    int insertSelective(RefOrderDetailTagDO record);

    RefOrderDetailTagDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RefOrderDetailTagDO record);

    int updateByPrimaryKey(RefOrderDetailTagDO record);

    /**
     * 根据ref_id 查询
     * @param refIdCollection
     * @return
     */
    List<RefOrderDetailTagDO> findByRefIdIn(@Param("refIdCollection")Collection<String> refIdCollection);

    int insertList(@Param("list")List<RefOrderDetailTagDO> list);

}