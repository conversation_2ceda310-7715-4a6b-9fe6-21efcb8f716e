package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 前端经费卡模型
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/22 16:19
 **/
public class ChangeFundCardRequestDTO implements Serializable {

    private static final long serialVersionUID = -1051929283552236046L;

    /**
     * 订单ids
     */
    @RpcModelProperty("订单ids")
    private List<Integer> orderIds;

    /**
     * 保存的经费项目列表
     */
    @RpcModelProperty("保存的经费项目列表")
    private List<FundCardProjectRequestDTO> saveProjectList;

    /**
     * 实验耗材费，只有中肿用
     */
    @RpcModelProperty("实验耗材费，只有中肿用")
    private BigDecimal consumablesFee;

    /**
     * 测试分析费，只有中肿用
     */
    @RpcModelProperty("测试分析费，只有中肿用")
    private BigDecimal analysisFee;

    @RpcModelProperty("换卡原因")
    private String reason;

    public List<Integer> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }

    public List<FundCardProjectRequestDTO> getSaveProjectList() {
        return saveProjectList;
    }

    public void setSaveProjectList(List<FundCardProjectRequestDTO> saveProjectList) {
        this.saveProjectList = saveProjectList;
    }

    public BigDecimal getConsumablesFee() {
        return consumablesFee;
    }

    public void setConsumablesFee(BigDecimal consumablesFee) {
        this.consumablesFee = consumablesFee;
    }

    public BigDecimal getAnalysisFee() {
        return analysisFee;
    }

    public void setAnalysisFee(BigDecimal analysisFee) {
        this.analysisFee = analysisFee;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ChangeFundCardRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderIds=" + orderIds)
                .add("saveProjectList=" + saveProjectList)
                .add("consumablesFee=" + consumablesFee)
                .add("analysisFee=" + analysisFee)
                .add("reason='" + reason + "'")
                .toString();
    }
}
