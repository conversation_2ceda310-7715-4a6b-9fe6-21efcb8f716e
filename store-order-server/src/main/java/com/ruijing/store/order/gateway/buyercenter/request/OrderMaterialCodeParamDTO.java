package com.ruijing.store.order.gateway.buyercenter.request;

import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.util.List;

/**
 * Name: OrderMaterialCodeParamDTO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/13
 */
@RpcModel("订单物资编码批量新增入参")
public class OrderMaterialCodeParamDTO {

    @RpcModelProperty(value = "新增入参")
    private List<OrderMaterialCodeDTO> orderMaterialCodeDTOList;

    public List<OrderMaterialCodeDTO> getOrderMaterialCodeDTOList() {
        return orderMaterialCodeDTOList;
    }

    public void setOrderMaterialCodeDTOList(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList) {
        this.orderMaterialCodeDTOList = orderMaterialCodeDTOList;
    }
}
