package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.other.dto.OrderPicDTO;
import com.ruijing.store.order.api.base.other.service.OrderPicRPCService;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.model.OrderPic;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 订单图片相关 服务
 * @author: zhouxin
 * @create: 2021-05-31
 **/
@ServiceLog
@MSharpService
public class OrderPicRPCServiceImpl implements OrderPicRPCService {

    private String catType = "OrderPicRPCServiceImpl";

    @Resource
    private OrderPicMapper orderPicMapper;

    /**
     * 查询订单的关联图片
     *
     * @param orderNoList
     * @return
     */
    @Override
    public RemoteResponse<List<OrderPicDTO>> findPicByOrderNo(List<String> orderNoList) {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(orderNoList), "订单号列表不能为空！");
        Preconditions.isTrue(orderNoList.size() <= 100, "批量查询不能超过100条记录");
        List<OrderPic> orderPicList = orderPicMapper.batchSelectByOrderNo(orderNoList);

        List<OrderPicDTO> resultList = New.list();
        OrderPicDTO result;
        for (OrderPic orderPic : orderPicList) {
            result = new OrderPicDTO();
            result.setId(orderPic.getId());
            result.setOrderNo(orderPic.getOrderNo());
            result.setPicUrl(orderPic.getPic());
            result.setCreateTime(orderPic.getCreateTime());
            result.setUpdateTime(orderPic.getUpdateTime());
            resultList.add(result);
        }
        return RemoteResponse.<List<OrderPicDTO>>custom().setSuccess().setData(resultList).build();
    }

    /**
     * 按订单id列表，批量删除订单图片链接
     *
     * @param idList
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Integer> deletePicById(List<Integer> idList) {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(idList), "订单id列表不能为空！");
        BusinessErrUtil.isTrue(idList.size() <= 50, ExecptionMessageEnum.BATCH_DELETION_LIMIT_50_RECORDS);
        Integer count = orderPicMapper.batchDeleteById(idList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(count).build();
    }

    /**
     * 批量插入订单图片链接
     *
     * @param orderPicDTOS
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse batchInsertOrderPic(List<OrderPicDTO> orderPicDTOS) {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(orderPicDTOS), "订单列表不能为空");
        Preconditions.isTrue(orderPicDTOS.size() <= 50, "批量插入不能超过50条记录");
        List<OrderPic> orderPicList = New.list();
        OrderPic orderPic;
        for (OrderPicDTO orderPicDTO : orderPicDTOS) {
            orderPic = new OrderPic();
            orderPic.setPic(orderPicDTO.getPicUrl());
            orderPic.setOrderNo(orderPicDTO.getOrderNo());
            orderPic.setId(orderPic.getId());
            orderPicList.add(orderPic);
        }
        int count = orderPicMapper.batchInsert(orderPicList);
        if (count != orderPicList.size()) {
            int failedCount = orderPicList.size() - count;
            Preconditions.isTrue(false, failedCount + "条记录插入失败");
        }
        return RemoteResponse.custom().setSuccess().build();
    }

}
