package com.ruijing.store.order.rpc.client;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.order.base.log.enums.OrderDockingResultEnum;
import com.reagent.tpi.tpiclient.api.orgapi.HuaNongService;
import com.reagent.tpi.tpiclient.message.req.order.HNOrderBySelfBuyingReq;
import com.reagent.tpi.tpiclient.message.req.order.OrderReq;
import com.reagent.tpi.tpiclient.message.resp.BaseResp;
import com.reagent.tpi.tpiclient.message.resp.order.OrderResp;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Objects;

/**
 * @description: 华农client。store迁移过来....
 * @author: zhangzhfeng
 * @create: 2021/6/25 10:01
 **/
@CatAnnotation
@ServiceClient
@Deprecated
public class HuaNongServiceClient {

    @MSharpReference(remoteAppkey = "tpi-client")
    private HuaNongService huaNongService;

    @Autowired
    private OrderOtherLogClient orderOtherLogClient;

    @ServiceLog(description = "华农取消订单接口", serviceType = ServiceType.COMMON_SERVICE)
    public void cancelOrder(OrderReq orderReq, String forderno, String applyNumber) {
        Preconditions.isTrue(orderReq != null, "入参不能为空！");
        BaseResp<OrderResp> orderRespBaseResp = huaNongService.cancelOrder(orderReq);
        String prefixEx = "华农取消订单--";
        String message = "";
        if (orderRespBaseResp == null) {
            orderOtherLogClient.createOrderDockingLog(forderno,
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE, JsonUtils.toJsonIgnoreNull(orderReq), prefixEx + "华农调用取消接口失败" + ",采购单号为:" + applyNumber,
                    null, OrderDockingResultEnum.FAIL.result);
            Preconditions.isTrue(false, "基理返回对象为null");
        }

        // 失败
        if ("0002".equals(orderRespBaseResp.getCode())) {
            message = "取消失败，失败原因：" + orderRespBaseResp.getMessage();
            orderOtherLogClient.createOrderDockingLog(forderno,
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE, JsonUtils.toJsonIgnoreNull(orderReq), prefixEx + "华农调用接口成功," + message + ",采购单号为:" + applyNumber,
                    null, OrderDockingResultEnum.FAIL.result);
            Preconditions.isTrue(false, "取消失败,失败原因" + orderRespBaseResp.getMessage());
        }

        String status = orderRespBaseResp.getData().getStatus();
        // 调用华农订单取消接口成功 返回的结果,此状态表明，基理取消订单成功
        // public static String CANCLE_ORDER_SUCCESS = "8";
        if (status.equals("8")) {
            orderOtherLogClient.createOrderDockingLog(forderno,
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE, JsonUtils.toJsonIgnoreNull(orderReq), prefixEx + "华农调用取消接口成功,取消成功" + ",采购单号为:" + applyNumber,
                    null, OrderDockingResultEnum.FAIL.result);
        }
    }

    @ServiceLog(description = "保存华农订单", serviceType = ServiceType.COMMON_SERVICE)
    public BaseResp<OrderResp> saveOrder(HNOrderBySelfBuyingReq req, String forderno) {
        Preconditions.isTrue(req != null, "保存华农订单入参不能为空！");
        BaseResp<OrderResp> orderRespBaseResp = huaNongService.saveOrder(req);
        if (Objects.isNull(orderRespBaseResp)) {
            orderOtherLogClient.createOrderDockingLog(forderno,
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE, JsonUtils.toJsonIgnoreNull(req),  "订单验收失败,失败原因：保存华农订单失败" ,
                    null, OrderDockingResultEnum.FAIL.result);
            Preconditions.isTrue(false, "基理返回对象为null");
        }

        if (Objects.isNull(orderRespBaseResp.getData())) {
            orderOtherLogClient.createOrderDockingLog(forderno,
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE, JsonUtils.toJsonIgnoreNull(req),  "订单验收失败,失败原因：华农接口返回数据为空" ,
                    null, OrderDockingResultEnum.FAIL.result);
            Preconditions.isTrue(false, "订单验收失败,失败原因：华农接口返回数据为空 " + orderRespBaseResp.getMessage());
        }
        return orderRespBaseResp;
    }
}
