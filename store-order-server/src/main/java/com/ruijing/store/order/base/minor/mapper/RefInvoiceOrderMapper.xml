<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.RefInvoiceOrderMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.RefInvoiceOrder">
    <!--@mbg.generated-->
    <!--@Table t_ref_invoice_order-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="ref_type" jdbcType="INTEGER" property="refType" />
    <result column="ref_number" jdbcType="VARCHAR" property="refNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, invoice_id, ref_id, ref_type, ref_number
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_ref_invoice_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_ref_invoice_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruijing.store.order.base.minor.model.RefInvoiceOrder">
    <!--@mbg.generated-->
    insert into t_ref_invoice_order (id, invoice_id, ref_id, 
      ref_type, ref_number)
    values (#{id,jdbcType=VARCHAR}, #{invoiceId,jdbcType=INTEGER}, #{refId,jdbcType=VARCHAR}, 
      #{refType,jdbcType=INTEGER}, #{refNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ruijing.store.order.base.minor.model.RefInvoiceOrder">
    <!--@mbg.generated-->
    insert into t_ref_invoice_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="refType != null">
        ref_type,
      </if>
      <if test="refNumber != null">
        ref_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="refType != null">
        #{refType,jdbcType=INTEGER},
      </if>
      <if test="refNumber != null">
        #{refNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.RefInvoiceOrder">
    <!--@mbg.generated-->
    update t_ref_invoice_order
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="refType != null">
        ref_type = #{refType,jdbcType=INTEGER},
      </if>
      <if test="refNumber != null">
        ref_number = #{refNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.RefInvoiceOrder">
    <!--@mbg.generated-->
    update t_ref_invoice_order
    set invoice_id = #{invoiceId,jdbcType=INTEGER},
      ref_id = #{refId,jdbcType=VARCHAR},
      ref_type = #{refType,jdbcType=INTEGER},
      ref_number = #{refNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-01-16-->
  <select id="findByRefIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_invoice_order
        <where>
          and ref_type = 2
            <if test="refIdCollection != null and refIdCollection.size() > 0">
                and ref_id in
                <foreach item="item" index="index" collection="refIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-03-11-->
  <select id="findByInvoiceIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ref_invoice_order
    where invoice_id in
    <foreach item="item" index="index" collection="invoiceIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2020-03-25-->
  <delete id="deleteByInvoiceIdIn">
    delete from t_ref_invoice_order
    where invoice_id in
    <foreach item="item" index="index" collection="invoiceIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>

<!--auto generated by MybatisCodeHelper on 2020-12-02-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO t_ref_invoice_order(
    id,
    invoice_id,
    ref_id,
    ref_type,
    ref_number
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      UUID(),
      <choose>
        <when test="element.invoiceId != null ">
          #{element.invoiceId,jdbcType=INTEGER},
        </when>
        <otherwise>
          0,
        </otherwise>
      </choose>

      <choose>
        <when test="element.refId != null ">
          #{element.refId,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.refType != null ">
          #{element.refType,jdbcType=INTEGER},
        </when>
        <otherwise>
          0,
        </otherwise>
      </choose>

      <choose>
        <when test="element.refNumber != null ">
          #{element.refNumber,jdbcType=VARCHAR}
        </when>
        <otherwise>
          ''
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2020-12-02-->
  <select id="findByIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ref_invoice_order
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2020-12-02-->
  <delete id="deleteByIdIn">
    delete from t_ref_invoice_order
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </delete>
</mapper>