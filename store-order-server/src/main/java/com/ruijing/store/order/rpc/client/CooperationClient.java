package com.ruijing.store.order.rpc.client;

import com.ruijing.cooperation.cbsd.api.CbsdInfoRpcService;
import com.ruijing.cooperation.cbsd.api.StandingBookRpcService;
import com.ruijing.cooperation.cbsd.api.msg.CbsdStoreHouseDTO;
import com.ruijing.cooperation.cbsd.api.msg.RecordOrderInfoDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReferenceMethod;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zhukai
 * @date : 2020/1/6 5:10 下午
 * @description: 中爆对接文档
 */
@Service
public class CooperationClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "cooperation-cbsd-service";

    @MSharpReference(remoteAppkey = "cooperation-cbsd-service")
    @MSharpReferenceMethod(methodName = "pushOrderInfo")
    private StandingBookRpcService standingBookRpcService;

    @MSharpReference(remoteAppkey = "cooperation-cbsd-service")
    private CbsdInfoRpcService cbsdInfoRpcService;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    /**
     * 推送数据给中爆台账
     * @param orderInfo
     */
    @ServiceLog(description = "推送数据给中爆台账", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void pushOrderInfo(RecordOrderInfoDTO orderInfo){
        AsyncExecutor.listenableRunAsync(() -> {
            standingBookRpcService.pushOrderInfo(orderInfo);
        }).addFailureCallback( throwable -> {
            logger.error("推送数据给中爆台账异常:" + throwable);
            Cat.logError(CAT_TYPE, "pushOrderInfo", "推送数据给中爆台账异常", throwable);
        });
    }

    /**
     * 根据 departmentId 获取存储仓库列表
     * @param departmentId
     * @return
     */
    @ServiceLog(description = "根据 departmentId 获取存储仓库列表", serviceType = ServiceType.COMMON_SERVICE)
    public List<CbsdStoreHouseDTO> findStoreHouseByDepartmentId(Integer departmentId) {
        // 获取中爆从业id
        String companyId = departmentRpcClient.findRefCompanyIdByDepartmentId(departmentId);
        RemoteResponse<List<CbsdStoreHouseDTO>> response = cbsdInfoRpcService.getStoreHouseList(companyId);
        Preconditions.isTrue(response.isSuccess(), "获取存储仓库列表异常：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

}
