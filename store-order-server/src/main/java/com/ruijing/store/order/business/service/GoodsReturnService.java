package com.ruijing.store.order.business.service;

import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.search.dto.OrderPullParamDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnOrderRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnRespVO;

import java.util.List;
import java.util.Map;

public interface GoodsReturnService {

    /**
     * 获取 创建时间 和 更新时间 在范围内的 退货单
     * @param orderPullParamDTO
     * @return
     */
    BasePageResponseDTO<GoodsReturn> getRangeDateReturnOrder(OrderPullParamDTO orderPullParamDTO);

    /**
     * 获取 搜索请求 退货商品和退货单
     * @param requestDTO
     * @return
     */
    GoodsReturnRespVO getReturnOrderForProcure(GoodsReturnOrderRequest requestDTO, String userGuid, Integer orgId);

    /**
     * 获取所有未收货的退货单的数量
     * @return
     */
    List<GoodsReturn> getUnFinishReturnSuccess();

    /**
     * 根据订单id列表和需要的退货单状态，获取 订单id-订单明细id-退货列表
     * @param orderIdList
     * @return
     */
    Map<Integer, Map<Integer, List<GoodsReturnInfoDetailVO>>> getOrderGoodsReturnMap(List<Integer> orderIdList, List<Integer> returnStatusList);

    void syncReplyTime();
}
