package com.ruijing.store.order.rpc.impl;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.service.OrderExtraRpcService;
import com.ruijing.store.order.base.core.translator.OrderExtraTranslator;
import com.ruijing.store.order.rpc.client.OrderExtraClient;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2021/6/29 9:46
 */
@MSharpService
public class OrderExtraRpcServiceImpl implements OrderExtraRpcService {

    private final Integer searchLimit = 300;

    @Resource
    private OrderExtraClient orderExtraClient;

    /**
     * 通过订单id列表和拓展表操作类型id查找条目
     *
     * @param orderIdCollection
     * @param extraKey
     * @return
     */
    @Override
    @ServiceLog(description = "通过订单id列表和拓展表操作类型id查找条目", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<OrderExtraDTO>> selectByOrderIdInAndExtraValue(Collection<Integer> orderIdCollection, Integer extraKey) {
        BusinessErrUtil.isTrue(orderIdCollection.size() <= searchLimit, ExecptionMessageEnum.QUERY_ORDER_ID_LIST_LIMIT, searchLimit);
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdCollection, extraKey);
        List<OrderExtraDTO> orderExtraResultList = OrderExtraTranslator.galaxyDtoListToOrderDtoList(orderExtraDTOList);
        return RemoteResponse.<List<OrderExtraDTO>>custom().setData(orderExtraResultList).setSuccess();
    }

    /**
     * 通过订单id列表查找条目
     *
     * @param orderIdCollection
     * @return
     */
    @Override
    @ServiceLog(description = "通过订单id列表查找条目", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<OrderExtraDTO>> selectByOrderIdIn(Collection<Integer> orderIdCollection) {
        BusinessErrUtil.isTrue(orderIdCollection.size() <= searchLimit, ExecptionMessageEnum.QUERY_ORDER_ID_LIST_LIMIT, searchLimit);
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdIn(orderIdCollection);
        List<OrderExtraDTO> orderExtraResultList = OrderExtraTranslator.galaxyDtoListToOrderDtoList(orderExtraDTOList);
        return RemoteResponse.<List<OrderExtraDTO>>custom().setData(orderExtraResultList).setSuccess();
    }

    @Override
    public RemoteResponse<Boolean> addOrderExtra(Collection<OrderExtraDTO> extraDTOS) {
        List<BaseOrderExtraDTO> orderExtraDTOList = extraDTOS.stream().map(orderExtraDTO -> {
            BaseOrderExtraDTO extraDTO = new BaseOrderExtraDTO();
            extraDTO.setId(orderExtraDTO.getId());
            extraDTO.setOrderId(orderExtraDTO.getOrderId());
            extraDTO.setOrderNo(orderExtraDTO.getOrderNo());
            extraDTO.setOrgId(orderExtraDTO.getOrgId());
            extraDTO.setExtraKey(orderExtraDTO.getExtraKey());
            extraDTO.setExtraKeyDesc(orderExtraDTO.getExtraKeyDesc());
            extraDTO.setExtraValue(orderExtraDTO.getExtraValue());
            extraDTO.setCreateTime(orderExtraDTO.getCreateTime());
            extraDTO.setUpdateTime(orderExtraDTO.getUpdateTime());
            return extraDTO;
        }).collect(Collectors.toList());
        orderExtraClient.saveList(orderExtraDTOList);
        return RemoteResponse.success();
    }
}
