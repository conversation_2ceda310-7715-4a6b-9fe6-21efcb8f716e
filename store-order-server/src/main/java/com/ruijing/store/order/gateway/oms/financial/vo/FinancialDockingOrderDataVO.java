package com.ruijing.store.order.gateway.oms.financial.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-04-12 11:27
 * @description:
 **/
public class FinancialDockingOrderDataVO implements Serializable {

    private static final long serialVersionUID = -9223102722263233242L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("采购单号")
    private String applicationNo;

    @RpcModelProperty("对接单号")
    private String dockingNumber;

    @RpcModelProperty("经费状态")
    private Integer fundStatus;

    @RpcModelProperty("订单状态")
    private Integer orderStatus;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public void setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FinancialDockingOrderDataVO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("applicationNo='" + applicationNo + "'")
                .add("dockingNumber='" + dockingNumber + "'")
                .add("fundStatus=" + fundStatus)
                .add("orderStatus=" + orderStatus)
                .toString();
    }
}
