package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.user.api.dto.RoleDTO;
import com.ruijing.store.user.api.service.RoleRpcService;

import java.util.List;

/**
 * @description: 用户RPC客户端
 * @author: zhongyulei
 * @create: 2022-03-02 19:25
 */
@ServiceClient
public class RoleRPCClient {

    @MSharpReference(remoteAppkey = "store-user-service")
    private RoleRpcService roleRpcService;

    @ServiceLog(description = "查询医院有权限的角色", serviceType = ServiceType.RPC_CLIENT)
    public List<RoleDTO> findByOrgAndAccessCode(Integer orgId, String accessCode) {
        final RemoteResponse<List<RoleDTO>> response = roleRpcService.findByOrgAndAccessCode(orgId, accessCode);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "通过id获取角色")
    public List<RoleDTO> findByIds(List<Integer> roleIdList){
        RemoteResponse<List<RoleDTO>> response = roleRpcService.findByIds(roleIdList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
