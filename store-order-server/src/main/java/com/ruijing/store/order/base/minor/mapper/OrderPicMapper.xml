<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.OrderPicMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.OrderPic">
    <!--@mbg.generated-->
    <!--@Table t_order_pic-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, pic, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_order_pic
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_order_pic
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.OrderPic" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_pic (order_no, pic, create_time,
      update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{pic,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.OrderPic" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_pic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="pic != null">
        pic,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.OrderPic">
    <!--@mbg.generated-->
    update t_order_pic
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pic != null">
        pic = #{pic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.OrderPic">
    <!--@mbg.generated-->
    update t_order_pic
    set order_no = #{orderNo,jdbcType=VARCHAR},
      pic = #{pic,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_pic
    (order_no, pic)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.pic,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2020-02-11-->
  <delete id="deleteByOrderNo">
    delete from t_order_pic
    where order_no=#{orderNo,jdbcType=VARCHAR}
  </delete>

  <delete id="batchDeleteById">
    delete
    from t_order_pic
    where `id` IN
    <foreach item="id" collection="idList" open="(" close=")" separator=",">
      #{id,jdbcType=VARCHAR}
    </foreach>
  </delete>

  <!--auto generated by MybatisCodeHelper on 2020-12-23-->
  <select id="selectByOrderNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_order_pic
    where order_no=#{orderNo,jdbcType=VARCHAR}
    order by update_time asc
  </select>

  <select id="batchSelectByOrderNo" parameterType="java.util.List" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_order_pic
    where `order_no` IN
    <foreach item="orderNo" collection="orderNoList" open="(" close=")" separator=",">
        #{orderNo,jdbcType=VARCHAR}
    </foreach>
    order by order_no asc
  </select>
</mapper>