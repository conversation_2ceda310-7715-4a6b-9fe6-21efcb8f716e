package com.ruijing.store.order.business.service.impl.strategy;

import com.ruijing.shop.category.api.enums.RegulatoryTypeEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import com.ruijing.store.order.rpc.client.BizWareHouseClient;
import com.ruijing.store.order.rpc.client.UserClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 江西中医的验收策略
 */
@Service(OrderAcceptConstant.JIANG_XI_ZHONG_YI_YAO_DA_XUE_ACCEPT)
public class JiangXiZhongYiYaoOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    private static final Integer NOT_REGULATORY_WAREHOUSE_ID = 559;

    /**
     * 计算入库模式
     * 
     * @param orderMasterDO    订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList  订单商品详情
     * @return 入库模式
     */
    @Override
    public InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList) {
        // 江西中医需求：管制品手动提交入库，非管制品在验收审批时系统自动入库
        // 检查到管制品则不处理
        if (orderDetailList.stream().anyMatch(detail -> RegulatoryTypeEnum.REGULATORY.getRegulatoryType().equals(detail.getRegulatoryTypeId()))) {
            return InWarehouseModeEnum.NO_NEED;
        }
        return InWarehouseModeEnum.NEW_WAREHOUSE_SYSTEM_SAVE_APPLICATION_FORM;
    }

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        boolean isNormal = orderMasterDO.getSpecies().intValue() == ProcessSpeciesEnum.NORMAL.getValue();
        if (!isNormal) {
            return 1;
        }
        return orderAcceptService.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
    }

    /**
     * 向新库房系统插入入库申请单
     * @param orderMasterDO 订单主表信息
     * @param orderDetailList 订单详情信息
     * @param warehouseRoomId 申请入库的库房id
     * @return 最终库房状态
     */
    @Override
    protected InventoryStatusEnum batchSaveApplicationFormToNewWarehouseSystem(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, Integer warehouseRoomId) {
        // 暂时只需要插入到非管制品库房里面
        return super.batchSaveApplicationFormToNewWarehouseSystem(orderMasterDO, orderDetailList, NOT_REGULATORY_WAREHOUSE_ID);
    }
}
