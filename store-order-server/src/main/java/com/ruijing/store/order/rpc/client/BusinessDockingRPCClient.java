package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.service.BusinessDockingRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReferenceMethod;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/20 11:30 上午
 */
@ServiceClient
public class BusinessDockingRPCClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    @MSharpReferenceMethod(methodName = "updateByBusinessNo",callType = "oneway")
    private BusinessDockingRpcService businessDockingRpcService;

    /**
     * 查询第三方业务订单信息
     * @param orderNo  锐竞订单号
     * @return
     */
    public BusinessDockingDTO getBusinessDockingOrderInfo(String orderNo) {
        List<BusinessDockingDTO> dockingOrderInfoList = this.getBusinessDockingOrderInfoList(Arrays.asList(orderNo));
        if (CollectionUtils.isNotEmpty(dockingOrderInfoList)) {
            return dockingOrderInfoList.get(0);
        }
        return null;
    }

    /**
     * 查询第三方业务订单信息
     * @param orderNoList  锐竞订单号
     * @return
     */
    @ServiceLog(description = "查询第三方业务订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<BusinessDockingDTO> getBusinessDockingOrderInfoList(List<String> orderNoList) {
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderNumberList(orderNoList);
        RemoteResponse<List<BusinessDockingDTO>> response = businessDockingRpcService.findByBusinessNo(request);
        Preconditions.isTrue(response.isSuccess(), "查询失败" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    /**
     * 根据业务单号更新第三方业务订单信息
     * @param dto 业务单记录信息
     * @return
     */
    @ServiceLog(description = "根据业务单号更新第三方业务订单信息", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void updateByBusinessNo(BusinessDockingDTO dto) {
        businessDockingRpcService.updateByBusinessNo(dto);
    }

    /**
     * 根据业务单号更新第三方业务订单信息
     * @param dtoList 业务单记录信息
     * @return
     */
    @ServiceLog(description = "根据业务单号更新第三方业务订单信息", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public Integer updateByDockingNoList(List<BusinessDockingDTO> dtoList) {
        RemoteResponse<Integer> response = businessDockingRpcService.updateBatchByDockingNoList(dtoList);
        Preconditions.isTrue(response.isSuccess(), "根据业务单号更新第三方业务订单信息失败!" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "查询某些单位的业务订单", serviceType = ServiceType.COMMON_SERVICE)
    public List<BusinessDockingDTO> findByOrgCodeAndStatus(String orgCode, List<Integer> orderStatus) {
        OrderBaseParamDTO param = new OrderBaseParamDTO();
        param.setOrgCode(orgCode);
        param.setOrderStatusList(orderStatus);
        RemoteResponse<List<BusinessDockingDTO>> response = businessDockingRpcService.findByOrgCodeAndStatus(param);

        Preconditions.isTrue(response.isSuccess(), "查询某些单位的业务订单失败!" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "保存对接单号信息到galaxy", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public Integer saveBusinessOrders(List<BusinessDockingDTO> request) {
        RemoteResponse<Integer> response = businessDockingRpcService.saveBusinessOrders(request);
        Preconditions.isTrue(response.isSuccess(), "保存对接单号信息到galaxy异常：" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    public Integer saveBusinessOrders(String orderNo, String dockingNo, Integer status, Integer dockingStatus, Integer orgId, String orgCode) {
        BusinessDockingDTO item = new BusinessDockingDTO();
        item.setBusinessOrderNo(orderNo);
        item.setDockingNo(dockingNo);
        item.setReagentStatus(status);
        item.setExtraStatus(dockingStatus);
        item.setOrgId(orgId);
        item.setOrgCode(orgCode);

        return this.saveBusinessOrders(Arrays.asList(item));
    }
}
