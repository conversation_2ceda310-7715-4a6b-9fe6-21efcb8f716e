package com.ruijing.store.order.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.FundTypeEnum;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.StringUtil;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.service.ResearchBaseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @description: 科研团队的业务方法集成类
 * @author: zhangzhifeng
 * @date: 2021-05-07 17:11
 **/
@Service
@CatAnnotation
public class ResearchBaseServiceImpl implements ResearchBaseService {

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    /**
     *  配置平台经费orgSet
     */
    private Set<String> platformFoundOrgSet = New.set(OrgConst.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN);


    /**
     * 当前方法只有当 当前机构有平台运行经费 && 当前订单为平台运行经费支付 时才返回true
     * 选择了平台经费 && 要求设置了平台经费不进入结算流程的单位 不进入结算流程
     *      目前只有中山五院选择了平台经费，收货后订单状态为“已完成”，不进入结算流程 add 20210428
     * @param orderMasterDO 订单信息
     * @return 不进入结算流程 true ,进入结算流程 false
     */
    @Override
    public Boolean isPlatformFound(OrderMasterDO orderMasterDO) {
        if(needPlatformFoundCheck(orderMasterDO)){
            // 1.根据订单id获取对应的经费卡id  --> 通过查询 RefFundcardOrderDTO
            String orderId = String.valueOf(orderMasterDO.getId());
            List<RefFundcardOrderDO> fundCardOrderList = refFundcardOrderMapper.findByOrderId(orderId);
            BusinessErrUtil.notEmpty(fundCardOrderList, ExecptionMessageEnum.FUNDING_CARD_INFO_NOT_EXIST_FOR_ORDER_ID, orderId);
            RefFundcardOrderDO refFundcardOrderDO = fundCardOrderList.get(0);

            // 2.根据经费卡id获取对应的经费卡  FundCardRPCService#getFundCardListByCardIds
            List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(orderMasterDO.getFusercode(),
                    New.list(RefFundcardOrderTranslator.getLastLevelCardId(refFundcardOrderDO)));

            // 3.如果对应的经费卡类型不是FundTypeEnum.RESEARCH，即为平台经费
            FundCardDTO fundCardDTO = fundCardDTOList.get(0);
            return !new Integer(FundTypeEnum.RESEARCH.getValue()).equals(fundCardDTO.getFundType());
        }
        return false;
    }

    /**
     * 判断是否是平台经费
     * @param orderMasterDO 订单信息
     * @return true为需要进行平台经费校验，false为不需要进行平台经费校验
     */
    public boolean needPlatformFoundCheck(OrderMasterDO orderMasterDO) {
        String fusercode = orderMasterDO.getFusercode();
        // 当前机构配置了平台经费 && 当前单不是旧单(旧单不需要进行平台经费校验)
        if(platformFoundOrgSet.contains(fusercode)){
            // 中大五院
            String oldOrderDateString = OrderDateConstant.ORG_CODE_OLD_ORDER_DATE_MAP.get(fusercode);
            return isNewOrder(orderMasterDO,oldOrderDateString);
        }
        return false;
    }

    /**
     * 判断是否是新单
     *
     * @param orderMasterDO 订单信息
     * @return true为新单，false为旧单
     */
    public Boolean isNewOrder(OrderMasterDO orderMasterDO,String oldOrderDateString) {
        Integer oldOrderCompare = 1;
        // 是否有排除旧单的配置
        if (StringUtil.hasText(oldOrderDateString)) {
            // 判断是否是旧单，如果是则返回为 <=0
            // 如果 > 0，则说明不是旧单
            Date oldOrderDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, oldOrderDateString);
            oldOrderCompare = orderMasterDO.getForderdate().compareTo(oldOrderDate);
        }
        return oldOrderCompare > 0;
    }
}
