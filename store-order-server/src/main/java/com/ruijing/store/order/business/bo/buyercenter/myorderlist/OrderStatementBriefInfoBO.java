package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/25 16:57
 * @Description
 **/
public class OrderStatementBriefInfoBO {

    private Integer status;

    private String balanceDate;

    private String endDate;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getBalanceDate() {
        return balanceDate;
    }

    public void setBalanceDate(String balanceDate) {
        this.balanceDate = balanceDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderStatementBriefInfoBO{");
        sb.append("status=").append(status);
        sb.append(", balanceDate='").append(balanceDate).append('\'');
        sb.append(", endDate='").append(endDate).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
