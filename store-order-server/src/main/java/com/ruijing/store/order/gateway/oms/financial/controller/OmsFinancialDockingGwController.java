package com.ruijing.store.order.gateway.oms.financial.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.gateway.oms.financial.request.FinancialDockingOrderQueryRequestDTO;
import com.ruijing.store.order.gateway.oms.financial.service.OmsFinancialDockingService;
import com.ruijing.store.order.gateway.oms.financial.vo.FinancialDockingOrderDataVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-04-12 11:20
 * @description: oms财务对接相关接口
 **/
@RpcApi(value = "oms财务对接订单相关网关服务", description = "oms财务对接订单相关网关服务")
@GateWayController(requestMapping = "/oms/financial/docking")
public class OmsFinancialDockingGwController {

    @Resource
    private OmsFinancialDockingService omsFinancialDockingService;


    @RpcMapping("/query/orderData")
    @RpcMethod(value = "查询订单数据")
    public RemoteResponse<List<FinancialDockingOrderDataVO>> queryOrderData(FinancialDockingOrderQueryRequestDTO financialDockingOrderQueryRequestDTO){
        return RemoteResponse.success(omsFinancialDockingService.queryOrderData(financialDockingOrderQueryRequestDTO));
    }
}
