package com.ruijing.store.order.other.service.impl;

import com.reagent.research.reimbursement.api.ReimbursementConfigNoticeService;
import com.reagent.research.reimbursement.dto.ReimbursementConfigDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.other.service.ReimbursementService;

import javax.annotation.Resource;

/**
 * @author: liwenyu
 * @createTime: 2023-11-15 16:18
 * @description:
 **/
@MSharpService
public class ReimbursementConfigNoticeServiceImpl implements ReimbursementConfigNoticeService {

    @Resource
    private ReimbursementService reimbursementService;

    @Override
    public RemoteResponse<Boolean> reimbursementConfigChange(ReimbursementConfigDTO reimbursementConfigDTO) {
        reimbursementService.configChange(reimbursementConfigDTO.getOrgId(), reimbursementConfigDTO.getSuppId());
        return RemoteResponse.success(true);
    }
}
