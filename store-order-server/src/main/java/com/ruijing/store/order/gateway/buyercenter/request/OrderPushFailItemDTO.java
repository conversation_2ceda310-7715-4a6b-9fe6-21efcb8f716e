package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;
import com.ruijing.shop.crm.api.pojo.dto.PageableDTO;

/**
 * Name: OrderPushFailItemDTO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/6/9
 */
@RpcModel(value = "订单推送失败项分页查询")
public class OrderPushFailItemDTO extends PageableDTO {

    @RpcModelProperty(value = "类型 true为我的订单 false为课题组订单")
    private boolean isMy;

    @RpcModelProperty(value = "订单编号")
    private String orderNo;


    public boolean getIsMy() {
        return isMy;
    }

    public void setIsMy(boolean isMyy) {
        isMy = isMyy;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
