package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.ruijing.fundamental.api.annotation.MethodDeprecated;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.component.BeanContainer;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.OrderStatementRequestDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.FundCardFreezeCommonService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.enums.OmsFixDataEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.handler.SysLetterHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.constant.OrderInboundSucceedConstant;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.rpc.client.ThirdPartOrderRPCClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.service.OldDateService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单常用服务
 * @author: zhuk
 * @create: 2019-07-04 14:26
 **/
@ServiceLog(serviceType = ServiceType.RPC_SERVICE)
@MSharpService
public class OrderMasterCommonServiceImpl implements OrderMasterCommonService {

    private static final String CAT_TYPE = "OrderMasterCommonService";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 很久以前遗留下来的数据问题，因此经讨论约定这个1540 为供应商特别的标志
     */
    private final int PROMISE_SUPPLIER_FLAG = 1540;

    /**
     * thunder是否监听生成订单状态，如果是则关闭生成订单推送的开关
     */
    @PearlValue(key = "order.thunder.listen.warehouse", defaultValue = "false")
    private Boolean enableMqPushWarehouse;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private InboundSucceedCallbackService inboundSucceedCallbackService;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private SysLetterHandler sysLetterHandler;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private FundCardFreezeCommonService fundCardFreezeCommonService;

    @Resource
    private OldDateService oldDateService;

    @Resource
    private UserClient userClient;

    /**
     * 根据订单 id 列表查询订单
     *
     * @param orderMasterCommonReqDTO
     * @return
     */
    @Override
    @ServiceLog(description = "根据订单 id 列表查询订单")
    public RemoteResponse<List<OrderMasterDTO>> findOrderListByIds(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        List<OrderMasterDTO> orderMasterDTOList;
        try {
            List<Integer> orderMasterIds = orderMasterCommonReqDTO.getOrderMasterIds();
            if (CollectionUtils.isEmpty(orderMasterIds) ) {
                return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure("订单Id不能为空").build();
            }
            if (orderMasterIds.size() > 500) {
                return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure("订单Id不能超过100").build();
            }
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderMasterIds);
            //DO 转 DTO
            orderMasterDTOList = orderMasterDOList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
            // 旧单时间，含财务对接旧单判断
            orderMasterDTOList.forEach(orderMasterDTO -> orderMasterDTO.setOldFlag(oldDateService.isOldDate(orderMasterDTO.getFuserid(), orderMasterDTO.getForderdate(), orderMasterDTO.getFundStatus(), orderMasterDTO.getSpecies().intValue())));
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "findOrderListByIds", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), e);
            return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<List<OrderMasterDTO>>custom().setData(orderMasterDTOList).setSuccess().build();
    }

    /**
     * 根据 id 更新 订单
     * @param orderMasterDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateByPrimaryKeySelective(OrderMasterDTO orderMasterDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        Integer updateRows;
        OrderMasterDO orderMasterDO = OrderMasterTranslator.dtoToOrderMasterDO(orderMasterDTO);
        try {
            updateRows = orderMasterMapper.updateByPrimaryKeySelective(orderMasterDO);
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("更新订单时发生异常：{}", e);
            Cat.logError(CAT_TYPE, "updateByPrimaryKeySelective", "订单更新失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterDO), e);
            return RemoteResponse.<Integer>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<Integer>custom().setData(updateRows).setSuccess().build();
    }


    /**
     * 申请取消订单
     * @param applyCancelOrderReqDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse applyCancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        Preconditions.isTrue(OrderStatusEnum.SupplierApplyToCancel.getValue().equals(applyCancelOrderReqDTO.getStatus()), "发起错误！");
        Preconditions.notNull(applyCancelOrderReqDTO.getOrderId());
        // 发送邮件、微信消息
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(applyCancelOrderReqDTO.getOrderId());
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForConfirm.getValue().equals(orderMaster.getStatus())
                || OrderStatusEnum.WaitingForDelivery.getValue().equals(orderMaster.getStatus()), ExecptionMessageEnum.CURRENT_ORDER_STATUS_NO_RETURN);
        //修改订单状态
        try {
            Integer status = applyCancelOrderReqDTO.getStatus();
            OrderMasterDO orderMasterDO = new OrderMasterDO();
            orderMasterDO.setId(applyCancelOrderReqDTO.getOrderId());
            orderMasterDO.setFcanceldate(applyCancelOrderReqDTO.getFcanceldate());
            orderMasterDO.setFcancelman(applyCancelOrderReqDTO.getFcancelman());
            orderMasterDO.setFcancelmanid(applyCancelOrderReqDTO.getFcancelmanid());
            orderMasterDO.setFcancelreason(applyCancelOrderReqDTO.getFcancelreason());
            orderMasterDO.setStatus(OrderStatusEnum.SupplierApplyToCancel.getValue());
            orderMasterMapper.updateByPrimaryKeySelective(orderMasterDO);

            //记录取消日志
            OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
            orderApprovalLog.setOrderId(applyCancelOrderReqDTO.getOrderId());
            orderApprovalLog.setReason(applyCancelOrderReqDTO.getFcancelreason());
            orderApprovalLog.setOperatorId(PROMISE_SUPPLIER_FLAG);
            orderApprovalLog.setApproveStatus(5);
            orderApprovalLogMapper.insertSelective(orderApprovalLog);

            orderMaster.setStatus(status);
            orderMaster.setFcancelman(orderMasterDO.getFcancelman());

            UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMaster.getFbuyerid(), orderMaster.getFuserid());
            BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.PURCHASER_INFO_NOT_FOUND);
            orderMessageHandler.sendSuppApplyCancelMsg(orderMaster);

        } catch (Exception e) {
            logger.error("applyCancelOrder异常", e);
            Cat.logError(CAT_TYPE,"applyCancelOrder","申请取消订单异常",e);
            return  RemoteResponse.custom().setException("申请取消订单异常"+e.getMessage()).build();
        }
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(description = "根据订单 id 列表查询订单")
    public RemoteResponse<OrderMasterDTO> findOrderMasterById(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "findOrderMasterById");
        OrderMasterDTO orderMasterDTO;
        try {
            Integer orderMasterId = orderMasterCommonReqDTO.getOrderMasterId();
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderMasterId);
            //DO 转 DTO
            orderMasterDTO = OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO);
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "findOrderMasterById", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), e);
            return RemoteResponse.<OrderMasterDTO>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<OrderMasterDTO>custom().setData(orderMasterDTO).setSuccess().build();
    }

    /**
     * 根据订单号集合 查询 订单信息。
     * @param orderMasterCommonReqDTO
     *        orderMasterCommonReqDTO.orderMasterNoList 字段必填，且不能为空。
     * @return List<OrderMasterDTO>
     */
    @Override
    public RemoteResponse<List<OrderMasterDTO>> findOrderMasterByOrderNoList(OrderMasterCommonReqDTO orderMasterCommonReqDTO){
        List<String> orderMasterNoList = orderMasterCommonReqDTO.getOrderMasterNoList();
        Assert.isTrue(CollectionUtils.isNotEmpty(orderMasterNoList),"orderMasterNoList不能为空");
        Assert.isTrue(orderMasterNoList.size()<=300,"orderMasterNoList不能超过300");
        List<OrderMasterDO> orderMasterList = orderMasterMapper.findByFordernoIn(orderMasterNoList);
        List<OrderMasterDTO> result = orderMasterList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderMasterDTO>>custom().setData(result).setSuccess().build();
    }

    @Override
    public RemoteResponse<OrderMasterDTO> findOrderMasterByOrderNo(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "findOrderMasterByOrderNo");
        OrderMasterDTO orderMasterDTO;
        try {
            String orderMasterNo = orderMasterCommonReqDTO.getOrderMasterNo();
            OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderMasterNo);
            //DO 转 DTO
            orderMasterDTO = OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO);
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "findOrderMasterById", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), e);
            return RemoteResponse.<OrderMasterDTO>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<OrderMasterDTO>custom().setData(orderMasterDTO).setSuccess().build();
    }

    /**
     * 根据订单状态 和 截止 取消时间 查询订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderMasterDTO>> findByStatusAndFcanceldateLessThan(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        List<OrderMasterDTO> orderMasterDTOList;
        try {
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByStatusAndFcanceldateLessThan(orderMasterCommonReqDTO.getOrderStatus(),orderMasterCommonReqDTO.getDeadFcanceldate(),
                    orderMasterCommonReqDTO.getEqualFcanceldate(),orderMasterCommonReqDTO.getExcludeOrgIds());
            //DO 转 DTO
            orderMasterDTOList = orderMasterDOList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "findByStatusAndFcanceldateLessThan", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), e);
            return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<List<OrderMasterDTO>>custom().setData(orderMasterDTOList).setSuccess().build();
    }

    @Override
    public RemoteResponse<List<OrderMasterDTO>> findByStatusAndForderdateLessThan(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        List<OrderMasterDTO> orderMasterDTOList;
        try {
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByStatusAndForderdateLessThan(orderMasterCommonReqDTO.getOrderStatus(),
                    orderMasterCommonReqDTO.getDeadForderdate(),orderMasterCommonReqDTO.getEqualForderdate(),orderMasterCommonReqDTO.getExcludeOrgIds());
            //DO 转 DTO
            orderMasterDTOList = orderMasterDOList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "findByStatusAndForderdateLessThan", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), e);
            return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<List<OrderMasterDTO>>custom().setData(orderMasterDTOList).setSuccess().build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateStatusById(UpdateOrderStatusReqDTO updateOrderStatusReqDTO) {

        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        Integer updatedRows;
        try {
            Integer orderMasterId = updateOrderStatusReqDTO.getOrderMasterId();
            if (orderMasterId == null){
                return RemoteResponse.<Integer>custom().setFailure("orderMasterId不能为空！").build();
            }
            Integer orderStatus = updateOrderStatusReqDTO.getOrderStatus();
            if (orderStatus == null){
                return RemoteResponse.<Integer>custom().setFailure("orderStatus不能为空！").build();
            }
            updatedRows = orderMasterMapper.updateStatusById(orderStatus,orderMasterId);
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("更新订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "updateStatusById", "更新订单失败：" + e.getMessage() + "\n request：" + updateOrderStatusReqDTO.toString() , e);
            return RemoteResponse.<Integer>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<Integer>custom().setData(updatedRows).setSuccess().build();

    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateFundStatusByOrderNo(UpdateFundStatusReqDTO updateFundStatusReqDTO) {
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        Integer updatedRows;
        try {
            updatedRows = orderMasterMapper.updateFundStatusByforderno(updateFundStatusReqDTO.getFundStatus(),updateFundStatusReqDTO.getFailReason(),updateFundStatusReqDTO.getOrderMasterNo());
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("更新订单时异常：{}", e);
            Cat.logError(CAT_TYPE, "updateFundStatusByOrderNo", "更新订单失败：" + e.getMessage() + "\n request：" + updateFundStatusReqDTO.toString() , e);
            return RemoteResponse.<Integer>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<Integer>custom().setData(updatedRows).setSuccess().build();
    }


    /**
     * 根据订单状态和 机构id查寻订单
     * @param orderMasterCommonReqDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderMasterDTO>> findByStatusAndOrgId(OrderMasterCommonReqDTO orderMasterCommonReqDTO)
    {
        List<OrderMasterDTO> orderMasterDTOList;
        try
        {
            Integer startId = orderMasterCommonReqDTO.getLeftInterval();
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByStatusAndOrgId(orderMasterCommonReqDTO.getOrderStatus(),
                    orderMasterCommonReqDTO.getOrgId(),
                    startId,
                    orderMasterCommonReqDTO.getLimit());
            //DO 转 DTO
            orderMasterDTOList = orderMasterDOList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());

        }catch (Exception ex)
        {
            Cat.logError(CAT_TYPE, "findByStatusAndOrgId", "查询订单失败：" + ex.getMessage() + "\n request：" + JsonUtils.toJson(orderMasterCommonReqDTO), ex);
            return RemoteResponse.<List<OrderMasterDTO>>custom().setFailure(ex.getMessage()).build();
        }
        return RemoteResponse.<List<OrderMasterDTO>>custom().setData(orderMasterDTOList).setSuccess().build();
    }

    @Override
    public RemoteResponse<List<OrderMasterDTO>> findOrderMasterByBuyerAndStatus(OrderMasterForCmsReq orderMasterForCmsReq) {

        //采购人id
        Integer buyerId = orderMasterForCmsReq.getBuyerId();
        List<Integer> statusList = orderMasterForCmsReq.getStatusList();
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFbuyeridAndStatusIn(buyerId, statusList);
        List<OrderMasterDTO> orderMasterDTOList = orderMasterDOList.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderMasterDTO>>custom().setSuccess().setData(orderMasterDTOList).build();
    }

    @Override
    public RemoteResponse<List<OrderDetailDTO>> findOrderDetailsByMasterId(OrderDetailReq orderDetailReq) {

        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        List<OrderDetailDTO> torderDetailDTOList;
        try {
            Integer orderMasterId = orderDetailReq.getOrderMasterId();
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterId);
            torderDetailDTOList = orderDetailDOList.stream().map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO).collect(Collectors.toList());
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("查询订单时异常：{}",e);
            Cat.logError(CAT_TYPE, "searchOrders", "查询订单失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(orderDetailReq), e);
            return RemoteResponse.<List<OrderDetailDTO>>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }
        return RemoteResponse.<List<OrderDetailDTO>>custom().setSuccess().setData(torderDetailDTOList).build();
    }

    /**
     * 根据id 更新订单入库状态（不执行回调等操作，仅更新状态）
     * @param updateOrderParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateInventoryStatusById(UpdateOrderParamDTO updateOrderParamDTO){
        Integer orderId = updateOrderParamDTO.getOrderId();
        Preconditions.notNull(orderId ,"orderId不能为空");
        Preconditions.notNull(updateOrderParamDTO.getInventoryStatus() ,"inventoryStatus不能为空");
        OrderMasterDO orderInfo = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderInfo, ExecptionMessageEnum.UPDATE_ORDER_FAILED_INVALID_QUERY);

        // 判断是否进行入库并进行处理
        if (isInboundWareHouse(updateOrderParamDTO, orderInfo)) {
            // 入库回调处理
            InboundSucceedCallbackService customCallBackService = BeanContainer.getBean(InboundSucceedCallbackService.class, OrderInboundSucceedConstant.getInboundCallbackStrategy().get(orderInfo.getFusercode()));
            if (customCallBackService != null) {
                customCallBackService.inBoundCallBack(updateOrderParamDTO.getInventoryStatus(), orderInfo);
            } else {
                inboundSucceedCallbackService.inBoundCallBack(updateOrderParamDTO.getInventoryStatus(), orderInfo);
            }
        }

        UpdateOrderParamDTO updateParam = new UpdateOrderParamDTO();
        updateParam.setInventoryStatus(updateOrderParamDTO.getInventoryStatus());
        updateParam.setOrderId(updateOrderParamDTO.getOrderId());
        Integer result = orderMasterMapper.updateOrderById(updateParam);

        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

   /* todo : 1、新启一个更新入库状态的接口（包含入库逻辑）。
   *    2、创建一个 需要入库回调的分值逻辑
   * */

    /**
     * 根据id 更新订单 信息
     * @param updateOrderParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateOrderById(UpdateOrderParamDTO updateOrderParamDTO){
        Integer orderId = updateOrderParamDTO.getOrderId();
        Assert.isTrue(orderId != null,"orderId不能为空");
        OrderMasterDO orderInfo = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.isTrue(orderInfo != null, ExecptionMessageEnum.UPDATE_ORDER_FAILED_INVALID_QUERY);
        updateOrderParamDTO.setOrderNo(orderInfo.getForderno());

        Integer result = this.handleUpdateOrder(updateOrderParamDTO,orderInfo);
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    /**
     * 根据订单号 更新订单 信息
     * @param updateOrderParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<UpdateOrderParamDTO> updateOrderByOrderNo(UpdateOrderParamDTO updateOrderParamDTO){
        String orderNo = updateOrderParamDTO.getOrderNo();
        Assert.isTrue(orderNo != null, "orderNo不能为空");
        OrderMasterDO orderInfo = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.isTrue(orderInfo != null, ExecptionMessageEnum.UPDATE_ORDER_FAILED_INVALID_ORDER_NUMBER);
        updateOrderParamDTO.setOrderId(orderInfo.getId());
        pushOrderInfoToThirdPlatform(updateOrderParamDTO, orderInfo);

        this.handleUpdateOrder(updateOrderParamDTO,orderInfo);
        return RemoteResponse.<UpdateOrderParamDTO>custom().setSuccess().setData(updateOrderParamDTO).build();
    }

    /**
     * 推送订单数据到第三方管理平台, 如广州医管理平台
     * @param updateOrderParamDTO
     * @param orderInfo
     */
    private void pushOrderInfoToThirdPlatform(UpdateOrderParamDTO updateOrderParamDTO, OrderMasterDO orderInfo) {
        // 走order-thunder-service服务
        if (dockingConfigCommonService.getIfNeedOldPush(orderInfo, New.list(OuterBuyerDockingTypeEnum.ORDER_NO_PUSH, OuterBuyerDockingTypeEnum.ORDER_PUSH))) {
            Runnable task = () -> {
                ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderInfo);
                List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(orderInfo.getId());
                if (CollectionUtils.isNotEmpty(detailDOList)) {
                    thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
                }
                // 订单发货推送管理平台
                if (OrderStatusEnum.WaitingForReceive.getValue().equals(updateOrderParamDTO.getStatus())) {
                    thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, OrderEventTypeEnum.DELIVERY_ORDER, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
                }
                // 订单确认推送管理平台
                if (OrderStatusEnum.WaitingForDelivery.getValue().equals(updateOrderParamDTO.getStatus())) {
                    thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, OrderEventTypeEnum.CONFIRM_ORDER, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
                }
            };
            AsyncExecutor
                    .listenableRunAsync(task)
                    .addFailureCallback(ex -> {
                        logger.error("订单号:" + orderInfo.getForderno() + "更新第三方订单状态失败：" + ex);
                        Cat.logError(CAT_TYPE, "pushSingleOrderInfo", "更新第三方订单状态失败：", ex);
                        dockingExtraService.saveOrUpdateDockingExtra(orderInfo.getForderno(), orderInfo.getForderno(), false, ex.getMessage());
                    });
        } else {
            // 推送订单更新到第三方平台(未迁移的单位走这个老接口)
            orderMasterForTPIService.updateThirdPlatformOrder(updateOrderParamDTO);
        }
    }

    /**
     * 是否已入库
     * @param updateOrderParamDTO 更新入参
     * @return                    是否
     */
    private boolean isInboundWareHouse(UpdateOrderParamDTO updateOrderParamDTO, OrderMasterDO orderInfo) {
        Integer inventoryStatus = updateOrderParamDTO.getInventoryStatus();
        return InventoryStatusEnum.COMPLETE.getCode().equals(inventoryStatus)
                || InventoryStatusEnum.NOT_INBOUND.getCode().equals(inventoryStatus);
    }

    /**
     * 记录订单验收日志
     * @param userId
     * @param orderId
     */
    private void createOrderOperateLog(Integer orderId , Integer approveStatus, Integer userId , String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setCreationTime(new Date());
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
    }

    /**
     *
     * @param receiptConfigMap 单位在oms配置
     * @param isNormal 是否线上单
     * @return
     */
    private boolean isUsedStatement(Map<String, String> receiptConfigMap, boolean isNormal) {
        /* 当前单位是否使用结算系统 add by Kimmy 2021/03/29*/
        boolean usedStatement;
        if (isNormal) {
            //线上单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM);
            usedStatement = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        }else {
            //线下单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
            usedStatement = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        }
        return usedStatement;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse updateStatusByIdList(UpdateOrderStatusReqDTO requestParam) {
        List<Integer> orderIdList = requestParam.getOrderMasterIdList();
        Assert.notEmpty(orderIdList, "更新订单失败！订单id不能为空！");
        Assert.notNull(requestParam.getOrderStatus(), "更新订单失败！订单状态入参不能为空！");
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIdList);
        // 更新订单
        orderMasterMapper.updateStatusByIdIn(this.getUpdateParamByRequest(requestParam), orderIdList);
        // todo 增加修改状态的日志
        List<OrderMasterDO> unfinishedOrder = orderList.stream().filter(o -> !OrderStatusEnum.Finish.getValue().equals(o.getStatus())).collect(Collectors.toList());;
        AsyncExecutor.listenableRunAsync(() -> pushOrderDefrayInfo(unfinishedOrder, requestParam.getOrderStatus()))
                .addFailureCallback(throwable -> {
                    logger.error("推送订单支付金额到预算系统异常！", throwable);
                    Cat.logError(CAT_TYPE, "updateStatusByIdList", "推送订单支付金额到预算系统异常！", throwable);
                });

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    public RemoteResponse<Boolean> fixStatusByIdList(UpdateOrderStatusReqDTO requestParam) {
        List<Integer> orderIdList = requestParam.getOrderMasterIdList();
        Preconditions.notEmpty(orderIdList, "更新订单失败！订单id不能为空！");
        Preconditions.notNull(requestParam.getOrderStatus(), "更新订单失败！订单状态入参不能为空！");
        Integer statusToFix = requestParam.getOrderStatus();
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(requestParam.getOrderMasterIdList());
        // 更新订单
        orderMasterMapper.updateStatusByIdIn(this.getUpdateParamByRequest(requestParam), orderIdList);
        // 按照状态|原因存放，用于展示
        String reasonStr = statusToFix + "|" + requestParam.getReason();
        // 插入采购人中心订单日志
        List<OrderApprovalLog> orderApprovalLogList = orderIdList.stream().map(orderId->{
            OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
            orderApprovalLog.setOrderId(orderId);
            orderApprovalLog.setReason(reasonStr);
            orderApprovalLog.setApproveStatus(OmsFixDataEnum.FIX_ORDER_STATUS.getValue());
            orderApprovalLog.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            return orderApprovalLog;
        }).collect(toList());
        orderApprovalLogMapper.insertList(orderApprovalLogList);

        List<OrderMasterDO> unfinishedOrder = orderList.stream().filter(o -> !OrderStatusEnum.Finish.getValue().equals(o.getStatus())).collect(Collectors.toList());;
        AsyncExecutor.listenableRunAsync(() -> pushOrderDefrayInfo(unfinishedOrder, requestParam.getOrderStatus()))
                .addFailureCallback(throwable -> {
                    logger.error("推送订单支付金额到预算系统异常！", throwable);
                    Cat.logError(CAT_TYPE, "updateStatusByIdList", "推送订单支付金额到预算系统异常！", throwable);
                });
        return RemoteResponse.success(true);
    }

    private OrderMasterDO getUpdateParamByRequest(UpdateOrderStatusReqDTO requestParam){
        OrderMasterDO updated = new OrderMasterDO();
        updated.setStatus(requestParam.getOrderStatus());
        updated.setStatementStatus(requestParam.getStatementStatus());
        updated.setFlastreceivedate(requestParam.getLastReceiveDate());
        if(OrderStatusEnum.Close.value.equals(requestParam.getOrderStatus())){
            updated.setShutDownDate(new Date());
        }else if(OrderStatusEnum.Finish.value.equals(requestParam.getOrderStatus())){
            updated.setFinishDate(new Date());
        }
        return updated;
    }

    /**
     * 根据结算单id集合查询 结算订单关联关系
     *
     * @param statementIds 结算单id结合  size<= 100
     * @return
     */
    @Override
    public RemoteResponse<List<OrderStatementRefDTO>> findOrderBaseByStatementIds(List<Integer> statementIds) {
        Preconditions.isTrue( CollectionUtils.isNotEmpty(statementIds),"结算单id集合不能为空");
        Preconditions.isTrue( statementIds.size() <=100,"结算单id集合不能大于100");
        List<OrderStatementRefDTO> results = orderMasterMapper.findOrderBaseByStatementIds(statementIds);
        return RemoteResponse.success(results);
    }

    /**
     * 推送订单支付金额到预算系统
     * @param unfinishedOrderList
     */
    private void pushOrderDefrayInfo(List<OrderMasterDO> unfinishedOrderList, Integer updatedStatus) {
        if (CollectionUtils.isEmpty(unfinishedOrderList) || !OrderStatusEnum.Finish.getValue().equals(updatedStatus)) {
            logger.warn("无需推送支付信息，查无订单！");
            return;
        }
        fundCardFreezeCommonService.orderDefray(unfinishedOrderList);
    }

    @Override
    public RemoteResponse<List<OrderMasterDTO>> findOrderByApplicationIdList(OrderMasterCommonReqDTO applicationIdList) {
        List<Integer> params = applicationIdList.getApplicationIdList();
        Preconditions.notEmpty(params, "查询失败，采购单id为空！");
        Preconditions.isTrue(params.size() <= 200, "查询失败，单次查询最多支持200个单据！");
        List<OrderMasterDO> byBuyappidIn = orderMasterMapper.findByFtbuyappidIn(params);

        List<OrderMasterDTO> result = byBuyappidIn.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderMasterDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<Integer> findMaxOrderId() {
        Integer maxId = orderMasterMapper.selectMaxId();
        if (maxId == null) {
            maxId = 0;
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(maxId);
    }

    @Override
    @ServiceLog(description = "根据id分页查询订单数据")
    public RemoteResponse<List<OrderMasterDTO>> rangeById(OrderMasterCommonReqDTO request) {
        Integer left = request.getLeftInterval();
        Integer right = request.getRightInterval();

        Preconditions.isTrue(left >= 0, "左区间必须大于等于0！");
        Preconditions.isTrue(right > 0, "右区间必须大于0！");
        Preconditions.isTrue(left < right, "右区间必须大于左区间！");
        List<OrderMasterDO> list = orderMasterMapper.findByAfterMinAndBeforeMax(left, right);
        List<OrderMasterDTO> result = list.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderMasterDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "根据更新时间查询最近发生更新的订单的id")
    public RemoteResponse<List<Integer>> findIdByUpdateTimeAfter(OrderMasterCommonReqDTO request) {
        Date updateTime = request.getUpdateTime();
        Preconditions.notNull(updateTime, "查询失败！更新时间不可为空");
        List<Integer> idList = orderMasterMapper.findIdByUpdateTimeAfter(updateTime);

        return RemoteResponse.<List<Integer>>custom().setSuccess().setData(idList);
    }

    /**
     * 根据订单号集合修改竞价单id
     *
     * @param updateBidIdParamDTO
     * @return RemoteResponse<Boolean>
     */
    @Override
    public RemoteResponse<Boolean> updateBidIdByOrderNoSet(UpdateBidIdParamDTO updateBidIdParamDTO) {
        Preconditions.notNull(updateBidIdParamDTO, "入参不能为空");
        Preconditions.notEmpty(updateBidIdParamDTO.getOrderNoSet(), "订单号集合不能为空");
        Preconditions.isTrue(StringUtils.isNotBlank(updateBidIdParamDTO.getBidId()), "竞价单id不能为空");
        orderMasterMapper.updateBidOrderIdByOrderNoList(updateBidIdParamDTO.getBidId(), updateBidIdParamDTO.getOrderNoSet());
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "批量更新订单结算信息接口", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> batchUpdateOrderStatement(List<UpdateOrderParamDTO> request) {
        Preconditions.notEmpty(request);
        Preconditions.isTrue(request.size() < 101, "批量更新失败，单次调用最大支持100个更新");
        boolean parameterMissing = request.stream().anyMatch(item -> Objects.isNull(item.getOrderId()) || Objects.isNull(item.getStatus()) || Objects.isNull(item.getStatementId()));
        Preconditions.isTrue(!parameterMissing, "传参错误，orderId， status， statementId必填");

        // 统一设置订单的更新状态
        Integer status = request.get(0).getStatus();
        OrderMasterDO updated = new OrderMasterDO();
        updated.setStatus(status);

        Map<Integer, List<UpdateOrderParamDTO>> statementIdOrderInfoMap = request.stream().collect(Collectors.groupingBy(UpdateOrderParamDTO::getStatementId));
        List<OrderStatementRequestDTO> updateStatementList = new ArrayList<>(statementIdOrderInfoMap.keySet().size());
        // 给结算单id设置映射订单id
        statementIdOrderInfoMap.forEach((statementId, updateOrderList) -> {
            OrderStatementRequestDTO param = new OrderStatementRequestDTO();
            param.setStatementId(statementId);
            List<Integer> orderIdList = updateOrderList.stream().map(UpdateOrderParamDTO::getOrderId).collect(Collectors.toList());
            param.setOrderIdList(orderIdList);
            updateStatementList.add(param);
        });
        int affect = orderMasterMapper.updateStatementIdByOrderIdIn(updated, updateStatementList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }
    @Override
    @MethodDeprecated(value = "接口已废弃, 请尽快改成com.ruijing.store.order.api.base.docking.service.OrderForThirdPartRPCService.thirdPartyUpdateOrder", expireDate = "2021-10-31")
    @ServiceLog(description = "批量更新订单数据", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> batchUpdateByOrderNo(List<UpdateOrderParamDTO> request) {
        Preconditions.notEmpty(request, "更新订单失败！入参为空");
        Preconditions.isTrue(request.size() < 101, "批量更新单次最多支持100单");
        boolean illegal = request.stream().anyMatch(r -> Objects.isNull(r.getOrderNo()));
        Preconditions.isTrue(!illegal, "更新失败，orderNo不可为空");
        for (UpdateOrderParamDTO orderParamDTO : request) {
            orderMasterForTPIService.updateThirdPlatformOrder(orderParamDTO);
        }
        int affect = orderMasterMapper.updateFieldByForderno(request);
        if (affect == 0) {
            return RemoteResponse.<Integer>custom().setFailure("更新失败：未成功更新订单！").setData(affect);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    public RemoteResponse<Integer> updateFieldByOrderNo(List<UpdateOrderParamDTO> request) {
        Preconditions.notEmpty(request, "更新订单失败！入参为空");
        Preconditions.isTrue(request.size() < 101, "批量更新单次最多支持100单");
        boolean illegal = request.stream().anyMatch(r -> Objects.isNull(r.getOrderNo()));
        Preconditions.isTrue(!illegal, "更新失败，orderNo不可为空");
        int affect = orderMasterMapper.updateFieldByForderno(request);
        if (affect == 0) {
            return RemoteResponse.<Integer>custom().setFailure("更新失败：未成功更新订单！").setData(affect);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    public RemoteResponse<Boolean> waitingConfirmMessageToSupp(OrderMasterDTO orderMasterDTO) {
        OrderMasterDO orderMasterDO = OrderMasterTranslator.dtoToOrderMasterDO(orderMasterDTO);
        weChatMessageHandler.waitingConfirmToSupp(orderMasterDO);
        return RemoteResponse.success();
    }

    private Integer handleUpdateOrder(UpdateOrderParamDTO updateOrderParamDTO, OrderMasterDO orderInfo) {
        this.pushOrderInfoToThirdPlatform(updateOrderParamDTO, orderInfo);

        // 入库回调处理
        InboundSucceedCallbackService customCallBackService = BeanContainer.getBean(InboundSucceedCallbackService.class, OrderInboundSucceedConstant.getInboundCallbackStrategy().get(orderInfo.getFusercode()));
        boolean warehousingSuccess = false;

        // 判断是否进行入库并进行处理，如果入库先于验收审批完成，则记录标志符
        if (isInboundWareHouse(updateOrderParamDTO, orderInfo)) {
            if (customCallBackService != null) {
                customCallBackService.inBoundCallBack(updateOrderParamDTO.getInventoryStatus(), orderInfo);
            } else {
                // 温州医处理，如果是温州医且要将订单入库状态更新为COMPLETE(入库回调时)，将不进行自动结算并需进行推送
                if (OrgEnum.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orderInfo.getFusercode())) {
                    updateOrderParamDTO.setInventoryStatus(InventoryStatusEnum.WAITING_FOR_CALLBACK.getCode());
                    warehousingSuccess = true;
                } else {
                    warehousingSuccess = inboundSucceedCallbackService.inBoundCallBack(updateOrderParamDTO.getInventoryStatus(), orderInfo);
                }

            }
        }

        Integer result = orderMasterMapper.updateOrderById(updateOrderParamDTO);
        // 2022-04-08 change by liwenyu: 增加审批完成后调用order-thunder-service推送入库状态接口--start
        // 判断是否启用消息队列进行入库的推送，开启开关则这里不会进行推送入库，改由对接服务自己监听数据变化推送。如果前面的入库处理成功了，这边进行推送入库状态。
        if (!enableMqPushWarehouse && warehousingSuccess) {
            inboundSucceedCallbackService.pushWarehousingToThirdPlatForm(orderInfo.getId());
        }
        // 2022-04-08 change by liwenyu: 增加审批完成后调用order-thunder-service推送入库状态接口--end
        return result;
    }
}
