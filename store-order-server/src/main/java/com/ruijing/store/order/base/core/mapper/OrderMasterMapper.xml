<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.OrderMasterMapper">
    <resultMap id="GoodsReturnByDept" type="com.ruijing.store.order.base.core.model.GoodsReturnByDept">
        <result column="fbuydepartmentid" property="deptId"/>
        <result column="return_amount" property="sumReturnAmount"/>
    </resultMap>
    <resultMap id="OrderIdInvoiceTitleDO"
               type="com.ruijing.store.order.base.core.model.OrderIdInvoiceTitleDO">
        <result column="id" property="id"/>
        <result column="invoice_title" property="invoiceTitle"/>
        <result column="invoice_title_id" property="invoiceTitleId"/>
        <result column="fund_type_name" property="fundTypeName"/>
    </resultMap>
    <resultMap id="SuppInfoDO" type="com.ruijing.store.order.base.core.model.SuppSimpleInfoDO">
        <result column="id" property="masterId"/>
        <result column="fsuppid" property="suppId"/>
        <result column="fsuppcode" property="suppCode"/>
        <result column="fsuppname" property="suppName"/>
    </resultMap>
    <resultMap id="OrderStatementRefResult"
               type="com.ruijing.store.order.api.base.ordermaster.dto.OrderStatementRefDTO">
        <result column="id" property="orderId"/>
        <result column="forderno" property="orderNo"/>
        <result column="statement_id" property="statementId"/>
    </resultMap>
    <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.OrderMasterDO">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="fmasterguid" jdbcType="VARCHAR" property="fmasterguid"/>
        <result column="ftbuyappid" jdbcType="INTEGER" property="ftbuyappid"/>
        <result column="forderno" jdbcType="VARCHAR" property="forderno"/>
        <result column="forderdate" jdbcType="TIMESTAMP" property="forderdate"/>
        <result column="fbuyerid" jdbcType="INTEGER" property="fbuyerid"/>
        <result column="fbuyercode" jdbcType="VARCHAR" property="fbuyercode"/>
        <result column="fbuyername" jdbcType="VARCHAR" property="fbuyername"/>
        <result column="fbuyeremail" jdbcType="VARCHAR" property="fbuyeremail"/>
        <result column="fbuyercontactman" jdbcType="VARCHAR" property="fbuyercontactman"/>
        <result column="fbuyertelephone" jdbcType="VARCHAR" property="fbuyertelephone"/>
        <result column="fbuydepartmentid" jdbcType="INTEGER" property="fbuydepartmentid"/>
        <result column="fbuydepartment" jdbcType="VARCHAR" property="fbuydepartment"/>
        <result column="fsuppid" jdbcType="INTEGER" property="fsuppid"/>
        <result column="fsuppcode" jdbcType="VARCHAR" property="fsuppcode"/>
        <result column="fsuppname" jdbcType="VARCHAR" property="fsuppname"/>
        <result column="fbiderdeliveryplace" jdbcType="VARCHAR" property="fbiderdeliveryplace"/>
        <result column="forderamounttotal" jdbcType="DECIMAL" property="forderamounttotal"/>
        <result column="fund_status" jdbcType="INTEGER" property="fundStatus"/>
        <result column="failed_reason" jdbcType="VARCHAR" property="failedReason"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="fconfirmdate" jdbcType="TIMESTAMP" property="fconfirmdate"/>
        <result column="fconfirmmanid" jdbcType="VARCHAR" property="fconfirmmanid"/>
        <result column="fconfirmman" jdbcType="VARCHAR" property="fconfirmman"/>
        <result column="fcanceldate" jdbcType="TIMESTAMP" property="fcanceldate"/>
        <result column="fcancelmanid" jdbcType="VARCHAR" property="fcancelmanid"/>
        <result column="fcancelman" jdbcType="VARCHAR" property="fcancelman"/>
        <result column="fdeliverydate" jdbcType="TIMESTAMP" property="fdeliverydate"/>
        <result column="fdeliverymanid" jdbcType="VARCHAR" property="fdeliverymanid"/>
        <result column="fdeliveryman" jdbcType="VARCHAR" property="fdeliveryman"/>
        <result column="flastreceivedate" jdbcType="TIMESTAMP" property="flastreceivedate"/>
        <result column="flastreceivemanid" jdbcType="VARCHAR" property="flastreceivemanid"/>
        <result column="flastreceiveman" jdbcType="VARCHAR" property="flastreceiveman"/>
        <result column="fassessdate" jdbcType="TIMESTAMP" property="fassessdate"/>
        <result column="fassessmanid" jdbcType="VARCHAR" property="fassessmanid"/>
        <result column="fassessman" jdbcType="VARCHAR" property="fassessman"/>
        <result column="piEmail" jdbcType="VARCHAR" property="piemail"/>
        <result column="projectID" jdbcType="VARCHAR" property="projectid"/>
        <result column="projectnumber" jdbcType="VARCHAR" property="projectnumber"/>
        <result column="projecttitle" jdbcType="VARCHAR" property="projecttitle"/>
        <result column="fuserid" jdbcType="INTEGER" property="fuserid"/>
        <result column="fusercode" jdbcType="VARCHAR" property="fusercode"/>
        <result column="fusername" jdbcType="VARCHAR" property="fusername"/>
        <result column="statement_id" jdbcType="INTEGER" property="statementId"/>
        <result column="fcancelreason" jdbcType="VARCHAR" property="fcancelreason"/>
        <result column="frefuse_cancel_reason" jdbcType="VARCHAR" property="frefuseCancelReason"/>
        <result column="shut_down_date" jdbcType="TIMESTAMP" property="shutDownDate"/>
        <result column="delivery_info" jdbcType="VARCHAR" property="deliveryInfo"/>
        <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo"/>
        <result column="return_amount" jdbcType="DOUBLE" property="returnAmount"/>
        <result column="frefuse_cancel_date" jdbcType="TIMESTAMP" property="frefuseCancelDate"/>
        <result column="fdeliveryid" jdbcType="INTEGER" property="fdeliveryid"/>
        <result column="bid_order_id" jdbcType="VARCHAR" property="bidOrderId"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="receive_pic_urls" jdbcType="VARCHAR" property="receivePicUrls"/>
        <result column="tpi_project_id" jdbcType="VARCHAR" property="tpiProjectId"/>
        <result column="original_amount" jdbcType="DECIMAL" property="originalAmount"/>
        <result column="inventory_status" jdbcType="TINYINT" property="inventoryStatus"/>
        <result column="species" jdbcType="TINYINT" property="species"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="in_state_time" jdbcType="TIMESTAMP" property="inStateTime"/>
        <result column="purchase_RootIn_Type" jdbcType="INTEGER" property="purchaseRootinType"/>
        <result column="carry_fee" jdbcType="DECIMAL" property="carryFee"/>
        <result column="invoice_title_id" jdbcType="BIGINT" property="invoiceTitleId"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="invoice_title_number" jdbcType="VARCHAR" property="invoiceTitleNumber"/>
        <result column="fund_type" jdbcType="INTEGER" property="fundType"/>
        <result column="fund_type_name" jdbcType="VARCHAR" property="fundTypeName"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="statement_status" jdbcType="INTEGER" property="statementStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="flow_id" jdbcType="INTEGER" property="flowId"/>
        <result column="dept_parent_id" jdbcType="INTEGER" property="deptParentId"/>
        <result column="dept_parent_name" jdbcType="VARCHAR" property="deptParentName"/>
        <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate"/>
    </resultMap>

    <resultMap id="OrderReturnStatistics" type="com.ruijing.store.order.api.base.other.dto.OrderReturnStatisticsDTO">
        <result column="fuserid" jdbcType="INTEGER" property="fuserid" />
        <result column="fbuydepartmentid" jdbcType="INTEGER" property="fbuydepartmentid" />
        <result column="order_returning_count" jdbcType="INTEGER" property="orderReturningCount" />
    </resultMap>

    <resultMap id="OrderTimeOutDTO" type="com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="fuserid" jdbcType="INTEGER" property="fuserId" />
        <result column="fbuydepartmentid" jdbcType="INTEGER" property="fbuyDepartmentId" />
        <result column="forderno" jdbcType="VARCHAR" property="orderNo" />
        <result column="fund_status" jdbcType="INTEGER" property="fundStatus" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="inventory_status" jdbcType="TINYINT" property="inventoryStatus"/>
    </resultMap>

    <resultMap id="OrderMasterTimeOutDTO" type="com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="forderno" jdbcType="VARCHAR" property="orderNo" />
        <result column="fbuydepartmentid" jdbcType="INTEGER" property="buyDepartmentId" />
        <result column="fbuydepartment" jdbcType="VARCHAR" property="buyDepartment" />
        <result column="fbuyername" jdbcType="VARCHAR" property="buyerName" />
        <result column="forderdate" jdbcType="INTEGER" property="orderDate" />
        <result column="examine_date" jdbcType="INTEGER" property="waitingExamineDays" />
        <result column="balance_date" jdbcType="INTEGER" property="waitingBalanceDays" />
        <result column="fsuppname" jdbcType="VARCHAR" property="suppName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="statement_id" jdbcType="INTEGER" property="statementId" />
        <result column="statement_status" jdbcType="INTEGER" property="statementStatus" />
        <result column="forderamounttotal" jdbcType="DECIMAL" property="totalPrice" />
        <result column="inventory_status" jdbcType="TINYINT" property="inventoryStatus" />

        <collection property="orderDetails" javaType="java.util.List" ofType="com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO">
            <result column="detail_id" jdbcType="INTEGER" property="id" />
            <result column="fgoodname" jdbcType="VARCHAR" property="goodName" />
            <result column="fbrand" jdbcType="VARCHAR" property="brand" />
            <result column="fgoodcode" jdbcType="VARCHAR" property="goodCode" />
            <result column="fspec" jdbcType="VARCHAR" property="spec" />
            <result column="fbidprice" jdbcType="DECIMAL" property="price" />
            <result column="fquantity" jdbcType="INTEGER" property="quantity" />
        </collection>
    </resultMap>

    <resultMap id="productPrintDetailResultMap" type="com.ruijing.store.order.base.excel.dto.ProductExportDetailInfoDTO">
        <result column="forderno" jdbcType="VARCHAR" property="orderNo"/>
        <result column="ftbuyappid" jdbcType="INTEGER" property="applicationId"/>
        <result column="fbuydepartment" jdbcType="VARCHAR" property="departmentName"/>
        <result column="fbuyername" jdbcType="VARCHAR" property="purchaseName"/>
        <result column="fbuyercontactman" jdbcType="VARCHAR" property="receiver"/>
        <result column="forderdate" jdbcType="TIMESTAMP" property="orderDate"/>
        <result column="fsuppname" jdbcType="VARCHAR" property="suppName"/>
        <result column="forderamounttotal" jdbcType="DECIMAL" property="orderPrice"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="projectnumber" jdbcType="VARCHAR" property="fundCardNo"/>
        <result column="fgoodname" jdbcType="VARCHAR" property="productName"/>
        <result column="fbrand" jdbcType="VARCHAR" property="brand"/>
        <result column="fquantity" jdbcType="DECIMAL" property="count"/>
        <result column="fgoodcode" jdbcType="VARCHAR" property="productCode"/>
        <result column="fbidprice" jdbcType="DECIMAL" property="unitPrice"/>
        <result column="fbidamount" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="funit" jdbcType="VARCHAR" property="unit"/>
        <result column="fspec" jdbcType="VARCHAR" property="specifications"/>
        <result column="category_tag" jdbcType="VARCHAR" property="topCategoryName"/>
        <result column="fclassification" jdbcType="VARCHAR" property="thirdCategoryName"/>
        <result column="first_category_name" jdbcType="VARCHAR" property="firstCategoryName"/>
        <result column="second_category_name" jdbcType="VARCHAR" property="secondCategoryName"/>
    </resultMap>

    <resultMap id="orderPrintDetailResultMap" type="com.ruijing.store.order.base.excel.dto.OrderExportDetailInfoDTO">
        <result column="forderno" jdbcType="VARCHAR" property="orderNo"/>
        <result column="ftbuyappid" jdbcType="INTEGER" property="applicationId"/>
        <result column="fbuydepartment" jdbcType="VARCHAR" property="departmentName"/>
        <result column="fbuyername" jdbcType="VARCHAR" property="purchaseName"/>
        <result column="fbuyercontactman" jdbcType="VARCHAR" property="receiver"/>
        <result column="forderdate" jdbcType="TIMESTAMP" property="orderDate"/>
        <result column="fsuppname" jdbcType="VARCHAR" property="suppName"/>
        <result column="forderamounttotal" jdbcType="DECIMAL" property="orderPrice"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="projectnumber" jdbcType="VARCHAR" property="fundCardNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, fmasterguid, ftbuyappid, forderno, forderdate, fbuyerid, fbuyercode, fbuyername,
        fbuyeremail, fbuyercontactman, fbuyertelephone, fbuydepartmentid, fbuydepartment, fsuppid,
        fsuppcode, fsuppname, fbiderdeliveryplace,
        forderamounttotal, fund_status,
        failed_reason, `status`,  fconfirmdate, fconfirmmanid,
        fconfirmman, fcanceldate, fcancelmanid, fcancelman, fdeliverydate, fdeliverymanid,
        fdeliveryman, flastreceivedate, flastreceivemanid, flastreceiveman, fassessdate,
        fassessmanid, fassessman,  piEmail,
        projectID, projectnumber, projecttitle, fuserid, fusercode, fusername, statement_id,  fcancelreason, frefuse_cancel_reason,
        shut_down_date, delivery_info, delivery_no, return_amount, frefuse_cancel_date, fdeliveryid,
        bid_order_id, order_type, receive_pic_urls, tpi_project_id, original_amount, inventory_status,
        species, update_time, in_state_time, purchase_RootIn_Type, carry_fee, invoice_title_id, invoice_title, invoice_title_number,
        fund_type, fund_type_name, payment_amount, statement_status, create_time, flow_id, dept_parent_id, dept_parent_name, finish_date
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ruijing.store.order.base.core.model.OrderMasterDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into torder_master
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fmasterguid != null">
                fmasterguid,
            </if>
            <if test="ftbuyappid != null">
                ftbuyappid,
            </if>
            <if test="forderno != null">
                forderno,
            </if>
            <if test="forderdate != null">
                forderdate,
            </if>
            <if test="fbuyerid != null">
                fbuyerid,
            </if>
            <if test="fbuyercode != null">
                fbuyercode,
            </if>
            <if test="fbuyername != null">
                fbuyername,
            </if>
            <if test="fbuyeremail != null">
                fbuyeremail,
            </if>
            <if test="fbuyercontactman != null">
                fbuyercontactman,
            </if>
            <if test="fbuyertelephone != null">
                fbuyertelephone,
            </if>
            <if test="fbuydepartmentid != null">
                fbuydepartmentid,
            </if>
            <if test="fbuydepartment != null">
                fbuydepartment,
            </if>
            <if test="fsuppid != null">
                fsuppid,
            </if>
            <if test="fsuppcode != null">
                fsuppcode,
            </if>
            <if test="fsuppname != null">
                fsuppname,
            </if>
            <if test="fbiderdeliveryplace != null">
                fbiderdeliveryplace,
            </if>
            <if test="forderamounttotal != null">
                forderamounttotal,
            </if>
            <if test="fundStatus != null">
                fund_status,
            </if>
            <if test="failedReason != null">
                failed_reason,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="fconfirmdate != null">
                fconfirmdate,
            </if>
            <if test="fconfirmmanid != null">
                fconfirmmanid,
            </if>
            <if test="fconfirmman != null">
                fconfirmman,
            </if>
            <if test="fcanceldate != null">
                fcanceldate,
            </if>
            <if test="fcancelmanid != null">
                fcancelmanid,
            </if>
            <if test="fcancelman != null">
                fcancelman,
            </if>
            <if test="fdeliverydate != null">
                fdeliverydate,
            </if>
            <if test="fdeliverymanid != null">
                fdeliverymanid,
            </if>
            <if test="fdeliveryman != null">
                fdeliveryman,
            </if>
            <if test="flastreceivedate != null">
                flastreceivedate,
            </if>
            <if test="flastreceivemanid != null">
                flastreceivemanid,
            </if>
            <if test="flastreceiveman != null">
                flastreceiveman,
            </if>
            <if test="fassessdate != null">
                fassessdate,
            </if>
            <if test="fassessmanid != null">
                fassessmanid,
            </if>
            <if test="fassessman != null">
                fassessman,
            </if>
            <if test="piemail != null">
                piEmail,
            </if>
            <if test="projectid != null">
                projectID,
            </if>
            <if test="projectnumber != null">
                projectnumber,
            </if>
            <if test="projecttitle != null">
                projecttitle,
            </if>
            <if test="fuserid != null">
                fuserid,
            </if>
            <if test="fusercode != null">
                fusercode,
            </if>
            <if test="fusername != null">
                fusername,
            </if>
            <if test="statementId != null">
                statement_id,
            </if>
            <if test="fcancelreason != null">
                fcancelreason,
            </if>
            <if test="frefuseCancelReason != null">
                frefuse_cancel_reason,
            </if>
            <if test="shutDownDate != null">
                shut_down_date,
            </if>
            <if test="deliveryInfo != null">
                delivery_info,
            </if>
            <if test="deliveryNo != null">
                delivery_no,
            </if>
            <if test="returnAmount != null">
                return_amount,
            </if>
            <if test="frefuseCancelDate != null">
                frefuse_cancel_date,
            </if>
            <if test="fdeliveryid != null">
                fdeliveryid,
            </if>
            <if test="bidOrderId != null">
                bid_order_id,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="receivePicUrls != null">
                receive_pic_urls,
            </if>
            <if test="tpiProjectId != null">
                tpi_project_id,
            </if>
            <if test="originalAmount != null">
                original_amount,
            </if>
            <if test="inventoryStatus != null">
                inventory_status,
            </if>
            <if test="species != null">
                species,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="inStateTime != null">
                in_state_time,
            </if>
            <if test="purchaseRootinType != null">
                purchase_RootIn_Type,
            </if>
            <if test="carryFee != null">
                carry_fee,
            </if>
            <if test="invoiceTitleId != null">
                invoice_title_id,
            </if>
            <if test="invoiceTitle != null">
                invoice_title,
            </if>
            <if test="invoiceTitleNumber != null">
                invoice_title_number,
            </if>
            <if test="fundType != null">
                fund_type,
            </if>
            <if test="fundTypeName != null">
                fund_type_name,
            </if>
            <if test="paymentAmount != null">
                payment_amount,
            </if>
            <if test="statementStatus != null">
                statement_status,
            </if>
            <if test="flowId != null">
                flow_id,
            </if>
            <if test="deptParentId != null">
                dept_parent_id,
            </if>
            <if test="deptParentName != null">
                dept_parent_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fmasterguid != null">
                #{fmasterguid,jdbcType=VARCHAR},
            </if>
            <if test="ftbuyappid != null">
                #{ftbuyappid,jdbcType=INTEGER},
            </if>
            <if test="forderno != null">
                #{forderno,jdbcType=VARCHAR},
            </if>
            <if test="forderdate != null">
                #{forderdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fbuyerid != null">
                #{fbuyerid,jdbcType=INTEGER},
            </if>
            <if test="fbuyercode != null">
                #{fbuyercode,jdbcType=VARCHAR},
            </if>
            <if test="fbuyername != null">
                #{fbuyername,jdbcType=VARCHAR},
            </if>
            <if test="fbuyeremail != null">
                #{fbuyeremail,jdbcType=VARCHAR},
            </if>
            <if test="fbuyercontactman != null">
                #{fbuyercontactman,jdbcType=VARCHAR},
            </if>
            <if test="fbuyertelephone != null">
                #{fbuyertelephone,jdbcType=VARCHAR},
            </if>
            <if test="fbuydepartmentid != null">
                #{fbuydepartmentid,jdbcType=INTEGER},
            </if>
            <if test="fbuydepartment != null">
                #{fbuydepartment,jdbcType=VARCHAR},
            </if>
            <if test="fsuppid != null">
                #{fsuppid,jdbcType=INTEGER},
            </if>
            <if test="fsuppcode != null">
                #{fsuppcode,jdbcType=VARCHAR},
            </if>
            <if test="fsuppname != null">
                #{fsuppname,jdbcType=VARCHAR},
            </if>
            <if test="fbiderdeliveryplace != null">
                #{fbiderdeliveryplace,jdbcType=VARCHAR},
            </if>
            <if test="forderamounttotal != null">
                #{forderamounttotal,jdbcType=DECIMAL},
            </if>
            <if test="fundStatus != null">
                #{fundStatus,jdbcType=INTEGER},
            </if>
            <if test="failedReason != null">
                #{failedReason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="fconfirmdate != null">
                #{fconfirmdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fconfirmmanid != null">
                #{fconfirmmanid,jdbcType=VARCHAR},
            </if>
            <if test="fconfirmman != null">
                #{fconfirmman,jdbcType=VARCHAR},
            </if>
            <if test="fcanceldate != null">
                #{fcanceldate,jdbcType=TIMESTAMP},
            </if>
            <if test="fcancelmanid != null">
                #{fcancelmanid,jdbcType=VARCHAR},
            </if>
            <if test="fcancelman != null">
                #{fcancelman,jdbcType=VARCHAR},
            </if>
            <if test="fdeliverydate != null">
                #{fdeliverydate,jdbcType=TIMESTAMP},
            </if>
            <if test="fdeliverymanid != null">
                #{fdeliverymanid,jdbcType=VARCHAR},
            </if>
            <if test="fdeliveryman != null">
                #{fdeliveryman,jdbcType=VARCHAR},
            </if>
            <if test="flastreceivedate != null">
                #{flastreceivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="flastreceivemanid != null">
                #{flastreceivemanid,jdbcType=VARCHAR},
            </if>
            <if test="flastreceiveman != null">
                #{flastreceiveman,jdbcType=VARCHAR},
            </if>
            <if test="fassessdate != null">
                #{fassessdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fassessmanid != null">
                #{fassessmanid,jdbcType=VARCHAR},
            </if>
            <if test="fassessman != null">
                #{fassessman,jdbcType=VARCHAR},
            </if>
            <if test="piemail != null">
                #{piemail,jdbcType=VARCHAR},
            </if>
            <if test="projectid != null">
                #{projectid,jdbcType=VARCHAR},
            </if>
            <if test="projectnumber != null">
                #{projectnumber,jdbcType=VARCHAR},
            </if>
            <if test="projecttitle != null">
                #{projecttitle,jdbcType=VARCHAR},
            </if>
            <if test="fuserid != null">
                #{fuserid,jdbcType=INTEGER},
            </if>
            <if test="fusercode != null">
                #{fusercode,jdbcType=VARCHAR},
            </if>
            <if test="fusername != null">
                #{fusername,jdbcType=VARCHAR},
            </if>
            <if test="statementId != null">
                #{statementId,jdbcType=INTEGER},
            </if>
            <if test="fcancelreason != null">
                #{fcancelreason,jdbcType=VARCHAR},
            </if>
            <if test="frefuseCancelReason != null">
                #{frefuseCancelReason,jdbcType=VARCHAR},
            </if>
            <if test="shutDownDate != null">
                #{shutDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryInfo != null">
                #{deliveryInfo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNo != null">
                #{deliveryNo,jdbcType=VARCHAR},
            </if>
            <if test="returnAmount != null">
                #{returnAmount,jdbcType=DOUBLE},
            </if>
            <if test="frefuseCancelDate != null">
                #{frefuseCancelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="fdeliveryid != null">
                #{fdeliveryid,jdbcType=INTEGER},
            </if>
            <if test="bidOrderId != null">
                #{bidOrderId,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="receivePicUrls != null">
                #{receivePicUrls,jdbcType=VARCHAR},
            </if>
            <if test="tpiProjectId != null">
                #{tpiProjectId,jdbcType=VARCHAR},
            </if>
            <if test="originalAmount != null">
                #{originalAmount,jdbcType=DECIMAL},
            </if>
            <if test="inventoryStatus != null">
                #{inventoryStatus,jdbcType=TINYINT},
            </if>
            <if test="species != null">
                #{species,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="inStateTime != null">
                #{inStateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaseRootinType != null">
                #{purchaseRootinType,jdbcType=INTEGER},
            </if>
            <if test="carryFee != null">
                #{carryFee,jdbcType=DECIMAL},
            </if>
            <if test="invoiceTitleId != null">
                #{invoiceTitleId,jdbcType=BIGINT},

            </if>
            <if test="invoiceTitle != null">
                #{invoiceTitle,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTitleNumber != null">
                #{invoiceTitleNumber,jdbcType=VARCHAR},
            </if>

            <if test="fundType != null">
                #{fundType,jdbcType=INTEGER},
            </if>
            <if test="fundTypeName != null">
                #{fundTypeName,jdbcType=VARCHAR},
            </if>

            <if test="paymentAmount != null">
                #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="statementStatus != null">
                #{statementStatus,jdbcType=INTEGER},
            </if>
            <if test="flowId != null">
                #{flowId,jdbcType=INTEGER},
            </if>
            <if test="deptParentId != null">
                #{deptParentId,jdbcType=INTEGER},
            </if>
            <if test="deptParentName != null">
                #{deptParentName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.ruijing.store.order.base.core.model.OrderMasterDO">
        <!--@mbg.generated-->
        update torder_master
        <set>
            <if test="fmasterguid != null">
                fmasterguid = #{fmasterguid,jdbcType=VARCHAR},
            </if>
            <if test="ftbuyappid != null">
                ftbuyappid = #{ftbuyappid,jdbcType=INTEGER},
            </if>
            <if test="forderno != null">
                forderno = #{forderno,jdbcType=VARCHAR},
            </if>
            <if test="forderdate != null">
                forderdate = #{forderdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fbuyerid != null">
                fbuyerid = #{fbuyerid,jdbcType=INTEGER},
            </if>
            <if test="fbuyercode != null">
                fbuyercode = #{fbuyercode,jdbcType=VARCHAR},
            </if>
            <if test="fbuyername != null">
                fbuyername = #{fbuyername,jdbcType=VARCHAR},
            </if>
            <if test="fbuyeremail != null">
                fbuyeremail = #{fbuyeremail,jdbcType=VARCHAR},
            </if>
            <if test="fbuyercontactman != null">
                fbuyercontactman = #{fbuyercontactman,jdbcType=VARCHAR},
            </if>
            <if test="fbuyertelephone != null">
                fbuyertelephone = #{fbuyertelephone,jdbcType=VARCHAR},
            </if>
            <if test="fbuydepartmentid != null">
                fbuydepartmentid = #{fbuydepartmentid,jdbcType=INTEGER},
            </if>
            <if test="fbuydepartment != null">
                fbuydepartment = #{fbuydepartment,jdbcType=VARCHAR},
            </if>
            <if test="fsuppid != null">
                fsuppid = #{fsuppid,jdbcType=INTEGER},
            </if>
            <if test="fsuppcode != null">
                fsuppcode = #{fsuppcode,jdbcType=VARCHAR},
            </if>
            <if test="fsuppname != null">
                fsuppname = #{fsuppname,jdbcType=VARCHAR},
            </if>
            <if test="fbiderdeliveryplace != null">
                fbiderdeliveryplace = #{fbiderdeliveryplace,jdbcType=VARCHAR},
            </if>
            <if test="forderamounttotal != null">
                forderamounttotal = #{forderamounttotal,jdbcType=DECIMAL},
            </if>
            <if test="fundStatus != null">
                fund_status = #{fundStatus,jdbcType=INTEGER},
            </if>
            <if test="failedReason != null">
                failed_reason = #{failedReason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="fconfirmdate != null">
                fconfirmdate = #{fconfirmdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fconfirmmanid != null">
                fconfirmmanid = #{fconfirmmanid,jdbcType=VARCHAR},
            </if>
            <if test="fconfirmman != null">
                fconfirmman = #{fconfirmman,jdbcType=VARCHAR},
            </if>
            <if test="fcanceldate != null">
                fcanceldate = #{fcanceldate,jdbcType=TIMESTAMP},
            </if>
            <if test="fcancelmanid != null">
                fcancelmanid = #{fcancelmanid,jdbcType=VARCHAR},
            </if>
            <if test="fcancelman != null">
                fcancelman = #{fcancelman,jdbcType=VARCHAR},
            </if>
            <if test="fdeliverydate != null">
                fdeliverydate = #{fdeliverydate,jdbcType=TIMESTAMP},
            </if>
            <if test="fdeliverymanid != null">
                fdeliverymanid = #{fdeliverymanid,jdbcType=VARCHAR},
            </if>
            <if test="fdeliveryman != null">
                fdeliveryman = #{fdeliveryman,jdbcType=VARCHAR},
            </if>
            <if test="flastreceivedate != null">
                flastreceivedate = #{flastreceivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="flastreceivemanid != null">
                flastreceivemanid = #{flastreceivemanid,jdbcType=VARCHAR},
            </if>
            <if test="flastreceiveman != null">
                flastreceiveman = #{flastreceiveman,jdbcType=VARCHAR},
            </if>
            <if test="fassessdate != null">
                fassessdate = #{fassessdate,jdbcType=TIMESTAMP},
            </if>
            <if test="fassessmanid != null">
                fassessmanid = #{fassessmanid,jdbcType=VARCHAR},
            </if>
            <if test="fassessman != null">
                fassessman = #{fassessman,jdbcType=VARCHAR},
            </if>
            <if test="piemail != null">
                piEmail = #{piemail,jdbcType=VARCHAR},
            </if>
            <if test="projectid != null">
                projectID = #{projectid,jdbcType=VARCHAR},
            </if>
            <if test="projectnumber != null">
                projectnumber = #{projectnumber,jdbcType=VARCHAR},
            </if>
            <if test="projecttitle != null">
                projecttitle = #{projecttitle,jdbcType=VARCHAR},
            </if>
            <if test="fuserid != null">
                fuserid = #{fuserid,jdbcType=INTEGER},
            </if>
            <if test="fusercode != null">
                fusercode = #{fusercode,jdbcType=VARCHAR},
            </if>
            <if test="fusername != null">
                fusername = #{fusername,jdbcType=VARCHAR},
            </if>
            <if test="statementId != null">
                statement_id = #{statementId,jdbcType=INTEGER},
            </if>
            <if test="fcancelreason != null">
                fcancelreason = #{fcancelreason,jdbcType=VARCHAR},
            </if>
            <if test="frefuseCancelReason != null">
                frefuse_cancel_reason = #{frefuseCancelReason,jdbcType=VARCHAR},
            </if>
            <if test="shutDownDate != null">
                shut_down_date = #{shutDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryInfo != null">
                delivery_info = #{deliveryInfo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNo != null">
                delivery_no = #{deliveryNo,jdbcType=VARCHAR},
            </if>
            <if test="returnAmount != null">
                return_amount = #{returnAmount,jdbcType=DOUBLE},
            </if>
            <if test="frefuseCancelDate != null">
                frefuse_cancel_date = #{frefuseCancelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="fdeliveryid != null">
                fdeliveryid = #{fdeliveryid,jdbcType=INTEGER},
            </if>
            <if test="bidOrderId != null">
                bid_order_id = #{bidOrderId,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="receivePicUrls != null">
                receive_pic_urls = #{receivePicUrls,jdbcType=VARCHAR},
            </if>
            <if test="tpiProjectId != null">
                tpi_project_id = #{tpiProjectId,jdbcType=VARCHAR},
            </if>
            <if test="originalAmount != null">
                original_amount = #{originalAmount,jdbcType=DECIMAL},
            </if>
            <if test="inventoryStatus != null">
                inventory_status = #{inventoryStatus,jdbcType=TINYINT},
            </if>
            <if test="species != null">
                species = #{species,jdbcType=TINYINT},
            </if>
            <if test="inStateTime != null">
                in_state_time = #{inStateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaseRootinType != null">
                purchase_RootIn_Type = #{purchaseRootinType,jdbcType=INTEGER},
            </if>
            <if test="carryFee != null">
                carry_fee = #{carryFee,jdbcType=DECIMAL},
            </if>
            <if test="invoiceTitleId != null">
                invoice_title_id = #{invoiceTitleId,jdbcType=BIGINT},

            </if>
            <if test="invoiceTitle != null">
                invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
            </if>
            <if test="invoiceTitleNumber != null">
                invoice_title_number = #{invoiceTitleNumber,jdbcType=VARCHAR},
            </if>

            <if test="fundType != null">
                fund_type = #{fundType,jdbcType=INTEGER},
            </if>
            <if test="fundTypeName != null">
                fund_type_name = #{fundTypeName,jdbcType=VARCHAR},
            </if>

            <if test="paymentAmount != null">
                payment_amount = #{paymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="statementStatus != null">
                statement_status = #{statementStatus,jdbcType=INTEGER},
            </if>
            <if test="flowId != null">
                flow_id = #{flowId,jdbcType=INTEGER},
            </if>
            <if test="deptParentId != null">
                dept_parent_id = #{deptParentId,jdbcType=INTEGER},
            </if>
            <if test="deptParentName != null and deptParentName != ''">
                dept_parent_name = #{deptParentName,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2019-07-02-->
    <select id="findByFbuyeridAndStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where 1=1
        <if test="fbuyerid!=null">
            and fbuyerid=#{fbuyerid,jdbcType=INTEGER}
        </if>
        <if test="statusCollection!=null and statusCollection.size()>0">
            and `status` in
            <foreach item="item" index="index" collection="statusCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-07-04-->
    <select id="findByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-07-04-->
    <select id="findByDeptIdAndIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and fbuydepartmentid in
        <foreach item="item" index="index" collection="deptIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-07-10-->
    <update id="updateFundStatusByforderno">
        update torder_master
        set
        <if test="failedReason != null">
            failed_reason = #{failedReason,jdbcType=VARCHAR},
        </if>
        fund_status=#{fundStatus,jdbcType=INTEGER}
        where forderno = #{forderno,jdbcType=VARCHAR}
    </update>

    <!--auto generated by MybatisCodeHelper on 2019-07-11-->
    <select id="findByStatusAndFcanceldateLessThan" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        <where>
            `status`=#{status,jdbcType=INTEGER}
        <if test="equalFcanceldate!=null">
            and date(fcanceldate)=#{equalFcanceldate,jdbcType=INTEGER}
        </if>
        <if test="deadFcanceldate!=null">
            and fcanceldate <![CDATA[<]]> #{deadFcanceldate,jdbcType=TIMESTAMP}
        </if>
        <if test="excludeOrgIds!=null and excludeOrgIds.size()>0">
            and fuserid not in
            <foreach item="item" index="index" collection="excludeOrgIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-07-11-->
    <select id="findByStatusAndForderdateLessThan" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where 1=1
        <if test="status!=null">
            and `status`=#{status,jdbcType=INTEGER}
        </if>
        <if test="equalForderdate!=null">
            and date(forderdate)=#{equalForderdate,jdbcType=INTEGER}
        </if>
        <if test="deadForderdate!=null">
            and forderdate <![CDATA[<]]> #{deadForderdate,jdbcType=TIMESTAMP}
        </if>
        <if test="excludeOrgIds!=null and excludeOrgIds.size()>0">
            and fuserid not in
            <foreach item="item" index="index" collection="excludeOrgIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <select id="findByStatusAndOrgId" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        <where>
        <if test="status!=null">
            and `status`=#{status,jdbcType=INTEGER}
        </if>
        <if test="fuserid != null">
            and `fuserid` =#{fuserid,jdbcType=INTEGER}
        </if>
        <if test="startId != null">
            and id > #{startId, jdbcType=INTEGER}
        </if>
        </where>
        order by id limit #{limit,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-10-09-->
    <select id="findByForderno" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where forderno=#{forderno,jdbcType=VARCHAR}
    </select>

    <select id="findTimeOutBalanceByConifgs" parameterType="java.util.List"
            resultMap="OrderTimeOutDTO">
        <foreach item="item" collection="configs" separator="UNION ALL ">
            SELECT id, fuserid, fbuydepartmentid, forderno, fund_status, status, inventory_status FROM torder_master
            <where>
                <if test="status != null and status.size() > 0">
                    and `STATUS` IN
                    <foreach item="s" collection="status" open="(" close=")" separator=",">
                        #{s,jdbcType=INTEGER}
                    </foreach>
                </if>

                AND DATEDIFF(NOW(), IFNULL( flastreceivedate, NOW() ))+ 1 >= #{item.configIntValue,jdbcType=INTEGER}
                AND fuserid = #{item.orgId,jdbcType=INTEGER}
                AND species = 0
            </where>
        </foreach>
    </select>

    <select id="findTimeOutExamineByConfigs" parameterType="java.util.List"
            resultMap="OrderTimeOutDTO">
        <foreach item="item" collection="configs" separator="UNION ALL ">
            SELECT id, fuserid, fbuydepartmentid, forderno, fund_status FROM torder_master
            WHERE 1 = 1

            <if test="status != null and status.size() > 0">
                and `STATUS` IN
                <foreach item="s" collection="status" open="(" close=")" separator=",">
                    #{s,jdbcType=INTEGER}
                </foreach>
            </if>

            AND DATEDIFF(NOW(), IFNULL( fdeliverydate, NOW( ) ))+1 >= #{item.configIntValue,jdbcType=INTEGER}
            AND fuserid = #{item.orgId,jdbcType=INTEGER}
            AND species = 0
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2019-11-07-->
    <update id="updateStatusById">
        update torder_master
        set `status`=#{updatedStatus,jdbcType=INTEGER}
        where id=#{id,jdbcType=INTEGER}
    </update>

<!--auto generated by MybatisCodeHelper on 2019-12-10-->
    <select id="findReceiptTimeOut" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where
        fuserid=#{fuserid,jdbcType=INTEGER}
        and `status`=#{status,jdbcType=INTEGER}
        and DATEDIFF(now(),fdeliverydate) &gt;= #{limitDay,jdbcType=INTEGER}
        and species = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-12-20-->
    <update id="updateOrderById" parameterType="com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO">
        update torder_master
        <set>
            <if test="status != null">
                `status`=#{status,jdbcType=INTEGER},
            </if>
            <if test="statementId != null">
                statement_id=#{statementId,jdbcType=INTEGER},
            </if>
            <if test="inStateTime != null">
                in_state_time = #{inStateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="inventoryStatus != null">
                inventory_status = #{inventoryStatus,jdbcType=TINYINT},
            </if>
            <if test="fundStatus != null">
                fund_status = #{fundStatus,jdbcType=TINYINT},
            </if>
            <if test="failedReason != null">
                failed_reason = #{failedReason,jdbcType=VARCHAR},
            </if>
            <if test="receivePicUrls != null and receivePicUrls != ''">
                receive_pic_urls = #{receivePicUrls,jdbcType=VARCHAR},
            </if>
            <if test="shutDownDate != null">
                shut_down_date = #{shutDownDate,jdbcType=TIMESTAMP},
            </if>
            <if test="confirmDate != null">
                fconfirmdate = #{confirmDate,jdbcType=TIMESTAMP},
            </if>
            <if test="confirmManId != null">
                fconfirmmanid = #{confirmManId,jdbcType=VARCHAR},
            </if>
            <if test="confirmMan != null">
                fconfirmman = #{confirmMan,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDate != null">
                fdeliverydate = #{deliveryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryMan != null">
                fdeliveryman = #{deliveryMan,jdbcType=VARCHAR},
            </if>
            <if test="deliveryManId != null">
                fdeliverymanid = #{deliveryManId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryInfo != null">
                delivery_info = #{deliveryInfo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNo != null">
                delivery_no = #{deliveryNo,jdbcType=VARCHAR},
            </if>
            <if test="returnAmount != null">
                return_amount = #{returnAmount,jdbcType=DOUBLE},
            </if>
            <if test="cancelDate != null">
                fcanceldate = #{cancelDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelReason != null">
                fcancelreason = #{cancelReason,jdbcType=VARCHAR},
            </if>
            <if test="lastReceiveDate != null">
                flastreceivedate = #{lastReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastReceiveMan != null">
                flastreceiveman = #{lastReceiveMan,jdbcType=VARCHAR},
            </if>
            <if test="lastReceiveManId != null">
                flastreceivemanid = #{lastReceiveManId,jdbcType=VARCHAR},
            </if>
            <if test="deliveryPlace != null">
                fbiderdeliveryplace = #{deliveryPlace,jdbcType=VARCHAR},
            </if>
            <if test="buyerContactMan != null">
                fbuyercontactman = #{buyerContactMan,jdbcType=VARCHAR},
            </if>
            <if test="buyerTelephone != null">
                fbuyertelephone = #{buyerTelephone,jdbcType=VARCHAR},
            </if>
            <if test="finishDate != null">
                finish_date = #{finishDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id=#{orderId,jdbcType=INTEGER}
    </update>
    
    <update id="updateOrderByIdList">
        <foreach collection="list" item="updated" index="index" separator=";">
            update torder_master
            <set>
                <if test="updated.status != null">
                    `status`=#{updated.status,jdbcType=INTEGER},
                </if>
                <if test="updated.statementId != null">
                    statement_id=#{updated.statementId,jdbcType=INTEGER},
                </if>
                <if test="updated.inStateTime != null">
                    in_state_time = #{updated.inStateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.inventoryStatus != null">
                    inventory_status = #{updated.inventoryStatus,jdbcType=TINYINT},
                </if>
                <if test="updated.fundStatus != null">
                    fund_status = #{updated.fundStatus,jdbcType=TINYINT},
                </if>
                <if test="updated.failedReason != null">
                    failed_reason = #{updated.failedReason,jdbcType=VARCHAR},
                </if>
                <if test="updated.receivePicUrls != null and receivePicUrls != ''">
                    receive_pic_urls = #{updated.receivePicUrls,jdbcType=VARCHAR},
                </if>
                <if test="updated.shutDownDate != null">
                    shut_down_date = #{updated.shutDownDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.confirmDate != null">
                    fconfirmdate = #{updated.confirmDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.confirmManId != null">
                    fconfirmmanid = #{updated.confirmManId,jdbcType=VARCHAR},
                </if>
                <if test="updated.confirmMan != null">
                    fconfirmman = #{updated.confirmMan,jdbcType=VARCHAR},
                </if>
                <if test="updated.deliveryDate != null">
                    fdeliverydate = #{updated.deliveryDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.deliveryMan != null">
                    fdeliveryman = #{updated.deliveryMan,jdbcType=VARCHAR},
                </if>
                <if test="updated.deliveryManId != null">
                    fdeliverymanid = #{updated.deliveryManId,jdbcType=VARCHAR},
                </if>
                <if test="updated.deliveryInfo != null">
                    delivery_info = #{updated.deliveryInfo,jdbcType=VARCHAR},
                </if>
                <if test="updated.deliveryNo != null">
                    delivery_no = #{updated.deliveryNo,jdbcType=VARCHAR},
                </if>
                <if test="updated.returnAmount != null">
                    return_amount = #{updated.returnAmount,jdbcType=DOUBLE},
                </if>
                <if test="updated.cancelDate != null">
                    fcanceldate = #{updated.cancelDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.cancelReason != null">
                    fcancelreason = #{updated.cancelReason,jdbcType=VARCHAR},
                </if>
                <if test="updated.lastReceiveDate != null">
                    flastreceivedate = #{updated.lastReceiveDate,jdbcType=TIMESTAMP},
                </if>
                <if test="updated.lastReceiveMan != null">
                    flastreceiveman = #{updated.lastReceiveMan,jdbcType=VARCHAR},
                </if>
                <if test="updated.lastReceiveManId != null">
                    flastreceivemanid = #{updated.lastReceiveManId,jdbcType=VARCHAR},
                </if>
                <if test="updated.deliveryPlace != null">
                    fbiderdeliveryplace = #{updated.deliveryPlace,jdbcType=VARCHAR},
                </if>
                <if test="updated.buyerContactMan != null">
                    fbuyercontactman = #{updated.buyerContactMan,jdbcType=VARCHAR},
                </if>
                <if test="updated.buyerTelephone != null">
                    fbuyertelephone = #{updated.buyerTelephone,jdbcType=VARCHAR},
                </if>
                <if test="updated.finishDate != null">
                    finish_date = #{updated.finishDate, jdbcType=TIMESTAMP},
                </if>
            </set>
            where id=#{updated.orderId,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2019-12-30-->
    <update id="updateStatementIdByOrderIdIn">
        <foreach item="item" index="index" collection="params"
                 open="" separator=";" close="">
            update torder_master
            <set>
            <if test="updated.status != null">
                status = #{updated.status,jdbcType=INTEGER},
            </if>

            <if test="updated.inventoryStatus != null">
                inventory_status = #{updated.inventoryStatus,jdbcType=TINYINT},
            </if>

            <if test="updated.inStateTime != null">
                in_state_time = #{updated.inStateTime,jdbcType=TIMESTAMP},
            </if>

            <if test="item.statementId != null">
                statement_id = #{item.statementId,jdbcType=INTEGER},
            </if>
            </set>
            where id in
            <foreach item="orderId" index="i" collection="item.orderIdList"
                     open="(" separator=", " close=")">
                #{orderId,jdbcType=INTEGER}
            </foreach>
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2020-01-02-->
    <update id="updateStatementByIdIn">
        update torder_master
        <set>
            <if test="updated.status != null">
                status = #{updated.status,jdbcType=INTEGER},
            </if>

            <if test="updated.inventoryStatus != null">
                inventory_status = #{updated.inventoryStatus,jdbcType=TINYINT},
            </if>

            <if test="updated.statementStatus != null">
                statement_status = #{updated.statementStatus,jdbcType=TINYINT},
            </if>

            <if test="updated.inStateTime != null">
                in_state_time = #{updated.inStateTime,jdbcType=TIMESTAMP},
            </if>

            statement_id = #{updated.statementId,jdbcType=INTEGER}
        </set>
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2019-12-20-->
    <update id="updateOrderByOrderNo" parameterType="com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO">
        update torder_master
        <set>
            <if test="status != null">
                `status`=#{status,jdbcType=INTEGER},
            </if>
            <if test="statementId != null">
                statement_id=#{statementId,jdbcType=INTEGER},
            </if>
            <if test="inStateTime != null">
                in_state_time = #{inStateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="inventoryStatus != null">
                inventory_status = #{inventoryStatus,jdbcType=TINYINT},
            </if>
            <if test="fundStatus != null">
                fund_status = #{fundStatus,jdbcType=TINYINT},
            </if>
            <if test="failedReason != null">
                failed_reason = #{failedReason,jdbcType=VARCHAR},
            </if>
            <if test="receivePicUrls != null and receivePicUrls != ''">
                receive_pic_urls = #{receivePicUrls,jdbcType=VARCHAR},
            </if>
            <if test="lastReceiveDate != null">
                flastreceivedate = #{lastReceiveDate,jdbcType=TIMESTAMP}
            </if>
        </set>
        where forderno=#{orderNo,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2020-02-20-->
    <select id="findByIdInAndFbuydepartmentIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and fbuydepartmentid in
        <foreach item="item" index="index" collection="fbuydepartmentidCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-03-23-->
    <update id="updateStatusByIdIn">
        update torder_master
        <set>
            <if test="updated.status != null">
                `status`=#{updated.status,jdbcType=INTEGER},
            </if>

            <if test="updated.flastreceivedate != null">
                flastreceivedate = #{updated.flastreceivedate,jdbcType=TIMESTAMP},
            </if>

            <if test="updated.statementStatus != null">
                statement_status = #{updated.statementStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.finishDate != null">
                finish_date = #{updated.finishDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.shutDownDate != null">
                shut_down_date = #{updated.shutDownDate,jdbcType=TIMESTAMP},
            </if>
        </set>

        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2020-04-10-->
    <select id="findByFordernoIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where forderno in
        <foreach item="item" index="index" collection="fordernoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-09-13-->
    <update id="updateByOrderNoIn">
        update torder_master
        <set>
            <if test="updated.status != null">
                status = #{updated.status,jdbcType=INTEGER},
            </if>

            <if test="updated.flastreceivedate != null">
                flastreceivedate = #{updated.flastreceivedate,jdbcType=TIMESTAMP},
            </if>

            <if test="updated.inStateTime != null">
                in_state_time = #{updated.inStateTime,jdbcType=TIMESTAMP},
            </if>

            <if test="updated.shutDownDate != null">
                shut_down_date = #{updated.shutDownDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where forderno in
        <foreach item="item" index="index" collection="orderNoCollection "
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2020-10-21-->
    <select id="findOrderBaseByStatementIds"
            resultMap="OrderStatementRefResult">
        select id, forderno, statement_id
        from torder_master
        where statement_id in
        <foreach item="item" index="index" collection="statementIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-11-20-->
    <select id="findByOrgIdDeptIdStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        <where>
            <if test="fuserid != null">
                and fuserid=#{fuserid,jdbcType=INTEGER}
            </if>
            <if test="fbuydepartmentidCollection != null and fbuydepartmentidCollection.size() > 0">
                and fbuydepartmentid in
                <foreach item="item" index="index" collection="fbuydepartmentidCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="statusCollection != null and statusCollection.size() > 0">
                and `status` in
                <foreach item="item" index="index" collection="statusCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-11-30-->
    <select id="findByFtbuyappidIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where ftbuyappid in
        <foreach item="item" index="index" collection="ftbuyappidCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-07-->
    <select id="selectMaxId" resultType="java.lang.Integer">
        select max(id)
        from torder_master
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-07-->
    <select id="findByAfterMinAndBeforeMax" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where id &gt; #{minId,jdbcType=INTEGER}
        and id &lt;= #{maxId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-08-->
    <select id="findIdByUpdateTimeAfter" resultType="java.lang.Integer">
        select id
        from torder_master
        where update_time &gt;= #{updateTime,jdbcType=TIMESTAMP}
        order by id asc
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-17-->
    <update id="updateBidOrderIdByOrderNoList">
        update torder_master
        set bid_order_id=#{updatedBidOrderId,jdbcType=VARCHAR}
        where forderno in
        <foreach item="item" index="index" collection="orderNoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2021-02-25-->
    <update id="updateFieldByForderno">
        <foreach collection="list" index="index" item="item" separator=";">
            update torder_master
            <set>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>

                <if test="item.statementId != null">
                    statement_id = #{item.statementId,jdbcType=INTEGER},
                </if>

                <if test="item.inStateTime != null">
                    in_state_time = #{item.inStateTime,jdbcType=TIMESTAMP},
                </if>

                <if test="item.inventoryStatus != null">
                    inventory_status = #{item.inventoryStatus,jdbcType=INTEGER},
                </if>

                <if test="item.statementStatus != null">
                    statement_status = #{item.statementStatus,jdbcType=INTEGER},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deptParentId != null">
                    dept_parent_id = #{item.deptParentId,jdbcType=INTEGER},
                </if>
                <if test="item.deptParentName != null">
                    dept_parent_name = #{item.deptParentName,jdbcType=VARCHAR},
                </if>
            </set>
            where forderno = #{item.orderNo,jdbcType=VARCHAR}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2021-03-26-->
    <select id="findIdAndInvoiceTitleByIdIn" resultMap="OrderIdInvoiceTitleDO">
        select id, invoice_title, invoice_title_id, fund_type_name
        from torder_master
        <where>
            <if test="idCollection != null and idCollection.size() > 0">
                and id in
                <foreach item="item" index="index" collection="idCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-05-11-->
    <update id="updateFieldByIdIn">
        <foreach collection="updatedList" index="index" item="item" separator=";">
            update torder_master
            <set>
                <if test="item.fundStatus != null">
                    fund_status = #{item.fundStatus,jdbcType=INTEGER},
                </if>
                <if test="item.failedReason != null">
                    failed_reason = #{item.failedReason,jdbcType=VARCHAR},
                </if>
                <if test="item.statementStatus != null">
                    statement_status = #{item.statementStatus,jdbcType=INTEGER},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deptParentId != null">
                    dept_parent_id = #{item.deptParentId,jdbcType=INTEGER},
                </if>
                <if test="item.deptParentName != null">
                    dept_parent_name = #{item.deptParentName,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.orderId,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2021-07-09-->
    <select id="countByFlowId" resultType="java.lang.Integer">
        select count(1)
        from torder_master
        where forderdate between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and flow_id=#{flowId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-07-09-->
    <update id="batchUpdateFlowId">
        <foreach collection="updatedList" index="index" item="item" separator=";">
            update torder_master
            <set>
                <if test="item.flowId != null">
                    flow_id = #{item.flowId,jdbcType=INTEGER},
                </if>
                update_time = update_time
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2021-07-09-->
    <select id="selectByFlowIdAndForderdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where forderdate between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and flow_id=#{flowId,jdbcType=INTEGER}
        order by id asc
        limit #{startIdx,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-11-26-->
    <select id="findBySameOrderNoLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        where forderno like concat(#{sameOrderNo,jdbcType=VARCHAR},'%')
    </select>

<!--auto generated by MybatisCodeHelper on 2021-12-01-->
    <update id="updateDeliveryPlaceByIdAndControlStatus">
        update torder_master
        set fbiderdeliveryplace=
        case `status`
        when 8 then #{updatedFbiderdeliveryplace,jdbcType=VARCHAR}
        else fbiderdeliveryplace
        end,
        fbuyercontactman=
        case `status`
        when 8 then #{updatedBuyerContactMan,jdbcType=VARCHAR}
        else fbuyercontactman
        end,
        fbuyertelephone=
        case `status`
        when 8 then #{updatedBuyerTelephone,jdbcType=VARCHAR}
        else fbuyertelephone
        end
        where id=#{id,jdbcType=INTEGER}
    </update>   

<!--auto generated by MybatisCodeHelper on 2021-12-27-->
    <select id="selectIdAndDateBetween" resultMap="BaseResultMap">
        select id, forderdate
        from torder_master
        where id <![CDATA[>]]> #{minId,jdbcType=INTEGER} and id <![CDATA[<=]]> #{maxId,jdbcType=INTEGER}
    </select>

    <update id="batchUpdateStatementStatus">
        <foreach collection="syncStatementStatusDTOS" item="updated" index="index"
                 separator=";">
            update torder_master
            <set>
                statement_status = #{updated.statementStatus,jdbcType=INTEGER},
            </set>
            where statement_id = #{updated.statementId,jdbcType=INTEGER}
            and statement_status != #{updated.statementStatus,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="batchUpdateOrderTotalAmount">
        <foreach collection="col" item="element" index="index" separator=";">
            update torder_master
            <set>
                <if test="element.forderamounttotal != null">
                    forderamounttotal = #{element.forderamounttotal,jdbcType=DECIMAL},
                </if>
            </set>
            where id = #{element.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2022-05-07-->
    <select id="sumReturnAmountByDept"
            resultMap="GoodsReturnByDept">
        select fbuydepartmentid, sum(return_amount) as `return_amount`
        from torder_master
        where fuserid=#{orgId,jdbcType=INTEGER}
        and forderdate <![CDATA[>=]]> #{minForderdate,jdbcType=TIMESTAMP}
        and forderdate <![CDATA[<]]> #{maxForderdate,jdbcType=TIMESTAMP}
        and `status` not in
        <foreach item="item" index="index" collection="excludeStatusList "
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and fsuppid not in
        <foreach item="item" index="index" collection="excludeSuppIdList "
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        group by fbuydepartmentid
    </select>

    <select id="countBySuppIdAndStatus" resultType="int">
        select  count(*) from torder_master
        <where>
            <if test="suppIdCollection != null and suppIdCollection.size() > 0">
                 fsuppid in
                <foreach item="item" index="index" collection="suppIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="statusCollection != null and statusCollection.size() > 0">
                and `status` in
                <foreach item="item" index="index" collection="statusCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>
    <update id="batchUpdateOrgInfo">
        <foreach collection="orderMasterDoList" item="element" index="index" separator=";">
            update torder_master
            <set>
                <if test="element.fuserid != null">
                    fuserid = #{element.fuserid,jdbcType=INTEGER},
                </if>
                <if test="element.fusercode != null">
                    fusercode = #{element.fusercode,jdbcType=VARCHAR},
                </if>
                <if test="element.fusername != null">
                    fusername = #{element.fusername,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{element.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <!-- 根据单位ID查询订单，排除指定状态 -->
    <select id="findByExcludeStatusAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_master
        <where>
            <if test="orgId != null">
                fuserid = #{orgId,jdbcType=INTEGER}
            </if>
            <if test="excludeStatusList != null and excludeStatusList.size() > 0">
                and `status` not in
                <foreach item="item" index="index" collection="excludeStatusList"
                        open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

    <update id="batchUpdateDeliveryPlace">
        <foreach collection="orderMasterDoList" item="item" index="index" separator=";">
            update torder_master
            <set>
                <if test="item.fbiderdeliveryplace != null and item.fbiderdeliveryplace != ''">
                    fbiderdeliveryplace = #{item.fbiderdeliveryplace,jdbcType=VARCHAR}
                </if>
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>


    <update id="updateAcceptInfoById" parameterType="com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO">
        update torder_master
        <set>
            <if test="receivePicUrls != null">
                receive_pic_urls = #{receivePicUrls,jdbcType=VARCHAR},
            </if>
        </set>
        where id=#{orderId,jdbcType=INTEGER}
    </update>

    <update id="cancelDeliveryById">
        update torder_master
        set `status`=#{status,jdbcType=INTEGER},
            fdeliverydate = null,
            fdeliveryman = null,
            fdeliverymanid = null,
            delivery_info = null,
            delivery_no = null
        where id=#{orderId,jdbcType=INTEGER}
    </update>
</mapper>
