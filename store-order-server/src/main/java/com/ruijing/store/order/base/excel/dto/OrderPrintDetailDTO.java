package com.ruijing.store.order.base.excel.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import java.math.BigDecimal;

public class OrderPrintDetailDTO {


    /**
     * 订单编号
     */
    @ExcelProperty("订单编号")
    @ColumnWidth(value = 30)
    private String orderNo;

    /**
     * 采购组
     */
    @ExcelProperty("采购组")
    @ColumnWidth(value = 30)
    private String departmentName;

    /**
     * 采购人
     */
    @ExcelProperty("采购人")
    @ColumnWidth(value = 20)
    private String purchaseName;

    /**
     * 收货人
     */
    @ExcelProperty("收货人")
    @ColumnWidth(value = 20)
    private String  receiver;

    /**
     * 收货电话
     */
    @ExcelIgnore
    private String receiverPhone;

    /**
     * 收货地址
     */
    @ExcelIgnore
    private String receiveAddress;

    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    @ColumnWidth(value = 50)
    private String suppName;

    /**
     * 给供应商备注
     */
    @ExcelProperty("给供应商备注")
    @ColumnWidth(value = 50)
    private String remark;


    /**
     * 订单日期
     */
    @ExcelProperty("订单日期")
    @ColumnWidth(value = 30)
    private String orderDate;

    /**
     * 订单金额
     */
    @ExcelProperty("订单金额")
    @ColumnWidth(value = 20)
    private BigDecimal orderPrice;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    @ColumnWidth(value = 20)
    private String statusName;

    /**
     * 经费卡卡号
     */
    @ExcelProperty("经费卡卡号")
    @ColumnWidth(value = 30)
    private String fundCardNo;

    /**
     * 经费卡一级名称
     */
    @ExcelIgnore
    private String firstLevelCardName;

    /**
     * 经费卡一级编号
     */
    @ExcelIgnore
    private String firstLevelCardCode;

    /**
     * 经费卡二级名称
     */
    @ExcelIgnore
    private String secondLevelCardName;

    /**
     * 经费卡二级编号
     */
    @ExcelIgnore
    private String secondLevelCardCode;

    /**
     * 经费卡三级名称
     */
    @ExcelIgnore
    private String thirdLevelCardName;

    /**
     * 经费卡三级编号
     */
    @ExcelIgnore
    private String thirdLevelCardCode;

    // 下面是陆军军医特殊的逻辑
    /**
     * 货号
     */
    @ExcelIgnore
    private String goodsCode;

    /**
     * 产品名称
     */
    @ExcelIgnore
    private String productName;

    /**
     * 品牌
     */
    @ExcelIgnore
    private String brand;

    /**
     * 规格
     */
    @ExcelIgnore
    private String spec;

    /**
     * 数量
     */
    @ExcelIgnore
    private BigDecimal quantity;

    /**
     * 单位
     */
    @ExcelIgnore
    private String unit;

    /**
     * 单价
     */
    @ExcelIgnore
    private BigDecimal price;

    /**
     * 商品总价
     */
    @ExcelIgnore
    private BigDecimal goodsAmount;

    /**
     * 验收人1
     */
    @ExcelIgnore
    private String receiveChecker1;

    /**
     * 验收人2
     */
    @ExcelIgnore
    private String receiveChecker2;

    /**
     * 审批人
     */
    @ExcelIgnore
    private String approveMan;

    /**
     * 结算单id
     */
    @ExcelIgnore
    private Long statementId;

    /**
     * 成功退货的金额总额
     */
    @ExcelIgnore
    private BigDecimal successfulReturnAmount;

    /**
     * 结算方式
     */
    @ExcelIgnore
    private String statementType;

    /**
     * 退货后剩余的订单总额
     */
    @ExcelIgnore
    private BigDecimal orderPriceAfterReturn;

    /**
     * 退货后剩余的商品总额
     */
    @ExcelIgnore
    private BigDecimal goodsAmountAfterReturn;

    /**
     * 退货后剩余的商品总量
     */
    @ExcelIgnore
    private BigDecimal quantityAfterReturn;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getPurchaseName() {
        return purchaseName;
    }

    public void setPurchaseName(String purchaseName) {
        this.purchaseName = purchaseName;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public OrderPrintDetailDTO setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
        return this;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public OrderPrintDetailDTO setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
        return this;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getReceiveChecker1() {
        return receiveChecker1;
    }

    public void setReceiveChecker1(String receiveChecker1) {
        this.receiveChecker1 = receiveChecker1;
    }

    public String getReceiveChecker2() {
        return receiveChecker2;
    }

    public void setReceiveChecker2(String receiveChecker2) {
        this.receiveChecker2 = receiveChecker2;
    }

    public String getApproveMan() {
        return approveMan;
    }

    public void setApproveMan(String approveMan) {
        this.approveMan = approveMan;
    }

    public BigDecimal getGoodsAmount() {
        return goodsAmount;
    }

    public void setGoodsAmount(BigDecimal goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    public String getFirstLevelCardName() {
        return firstLevelCardName;
    }

    public void setFirstLevelCardName(String firstLevelCardName) {
        this.firstLevelCardName = firstLevelCardName;
    }

    public String getFirstLevelCardCode() {
        return firstLevelCardCode;
    }

    public void setFirstLevelCardCode(String firstLevelCardCode) {
        this.firstLevelCardCode = firstLevelCardCode;
    }

    public String getSecondLevelCardName() {
        return secondLevelCardName;
    }

    public void setSecondLevelCardName(String secondLevelCardName) {
        this.secondLevelCardName = secondLevelCardName;
    }

    public String getSecondLevelCardCode() {
        return secondLevelCardCode;
    }

    public void setSecondLevelCardCode(String secondLevelCardCode) {
        this.secondLevelCardCode = secondLevelCardCode;
    }

    public String getThirdLevelCardName() {
        return thirdLevelCardName;
    }

    public void setThirdLevelCardName(String thirdLevelCardName) {
        this.thirdLevelCardName = thirdLevelCardName;
    }

    public String getThirdLevelCardCode() {
        return thirdLevelCardCode;
    }

    public void setThirdLevelCardCode(String thirdLevelCardCode) {
        this.thirdLevelCardCode = thirdLevelCardCode;
    }

    public Long getStatementId() {
        return statementId;
    }

    public void setStatementId(Long statementId) {
        this.statementId = statementId;
    }

    public BigDecimal getSuccessfulReturnAmount() {
        return successfulReturnAmount;
    }

    public void setSuccessfulReturnAmount(BigDecimal successfulReturnAmount) {
        this.successfulReturnAmount = successfulReturnAmount;
    }

    public String getStatementType() {
        return statementType;
    }

    public void setStatementType(String statementType) {
        this.statementType = statementType;
    }

    public BigDecimal getOrderPriceAfterReturn() {
        return orderPriceAfterReturn;
    }

    public void setOrderPriceAfterReturn(BigDecimal orderPriceAfterReturn) {
        this.orderPriceAfterReturn = orderPriceAfterReturn;
    }

    public BigDecimal getGoodsAmountAfterReturn() {
        return goodsAmountAfterReturn;
    }

    public void setGoodsAmountAfterReturn(BigDecimal goodsAmountAfterReturn) {
        this.goodsAmountAfterReturn = goodsAmountAfterReturn;
    }

    public BigDecimal getQuantityAfterReturn() {
        return quantityAfterReturn;
    }

    public void setQuantityAfterReturn(BigDecimal quantityAfterReturn) {
        this.quantityAfterReturn = quantityAfterReturn;
    }
}
