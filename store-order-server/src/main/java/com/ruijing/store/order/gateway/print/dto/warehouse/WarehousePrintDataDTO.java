package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/3/1 15:47
 * @description
 */
public class WarehousePrintDataDTO implements Serializable {

    private static final long serialVersionUID = 7763509882088140829L;
    
    @RpcModelProperty("入库单数据")
    private List<InWarehousePrintDataDTO> inWarehouseDataList;
    
    @RpcModelProperty("出库单数据")
    private List<OutWarehousePrintDataDTO> outWarehouseDataList;

    @RpcModelProperty("申领单数据")
    private List<WarehouseClaimPrintDataDTO> warehouseReceiveDataList;
    
    @RpcModelProperty("订单数据")
    private WareHouseOrderPrintDataDTO orderData;

    //----日志
    
    @RpcModelProperty("采购单日志")
    private ApprovalLogFlatListDTO purchaseLog;

    @RpcModelProperty("竞价初审日志--最后一次审批驳回后的审批通过日志")
    private ApprovalLogFlatListDTO bidInitialApproveLog;

    @RpcModelProperty("竞价终审日志--最后一次审批驳回后的审批通过日志")
    private ApprovalLogFlatListDTO bidFinalApproveLog;

    @RpcModelProperty("订单日志--最后一次审批驳回后的审批通过日志")
    private ApprovalLogFlatListDTO orderApproveSuccessLog;

    @RpcModelProperty("采购/竞价审批日志")
    private List<OrderPrintApprovalLogDTO> purchaseOrBidApprovalLogList;

    @RpcModelProperty("提交验收日志")
    private OrderPrintApprovalLogDTO submitAcceptLog;

    @RpcModelProperty(value = "采购 提交审批日志",description = "最新一条提交初审日志")
    private OrderPrintApprovalLogDTO submitApprovalLog;

    public List<InWarehousePrintDataDTO> getInWarehouseDataList() {
        return inWarehouseDataList;
    }

    public void setInWarehouseDataList(List<InWarehousePrintDataDTO> inWarehouseDataList) {
        this.inWarehouseDataList = inWarehouseDataList;
    }

    public List<OutWarehousePrintDataDTO> getOutWarehouseDataList() {
        return outWarehouseDataList;
    }

    public void setOutWarehouseDataList(List<OutWarehousePrintDataDTO> outWarehouseDataList) {
        this.outWarehouseDataList = outWarehouseDataList;
    }

    public List<WarehouseClaimPrintDataDTO> getWarehouseReceiveDataList() {
        return warehouseReceiveDataList;
    }

    public WarehousePrintDataDTO setWarehouseReceiveDataList(List<WarehouseClaimPrintDataDTO> warehouseReceiveDataList) {
        this.warehouseReceiveDataList = warehouseReceiveDataList;
        return this;
    }

    public WareHouseOrderPrintDataDTO getOrderData() {
        return orderData;
    }

    public void setOrderData(WareHouseOrderPrintDataDTO orderData) {
        this.orderData = orderData;
    }

    public ApprovalLogFlatListDTO getPurchaseLog() {
        return purchaseLog;
    }

    public void setPurchaseLog(ApprovalLogFlatListDTO purchaseLog) {
        this.purchaseLog = purchaseLog;
    }

    public ApprovalLogFlatListDTO getBidInitialApproveLog() {
        return bidInitialApproveLog;
    }

    public void setBidInitialApproveLog(ApprovalLogFlatListDTO bidInitialApproveLog) {
        this.bidInitialApproveLog = bidInitialApproveLog;
    }

    public ApprovalLogFlatListDTO getBidFinalApproveLog() {
        return bidFinalApproveLog;
    }

    public void setBidFinalApproveLog(ApprovalLogFlatListDTO bidFinalApproveLog) {
        this.bidFinalApproveLog = bidFinalApproveLog;
    }

    public ApprovalLogFlatListDTO getOrderApproveSuccessLog() {
        return orderApproveSuccessLog;
    }

    public void setOrderApproveSuccessLog(ApprovalLogFlatListDTO orderApproveSuccessLog) {
        this.orderApproveSuccessLog = orderApproveSuccessLog;
    }

    public List<OrderPrintApprovalLogDTO> getPurchaseOrBidApprovalLogList() {
        return purchaseOrBidApprovalLogList;
    }

    public WarehousePrintDataDTO setPurchaseOrBidApprovalLogList(List<OrderPrintApprovalLogDTO> purchaseOrBidApprovalLogList) {
        this.purchaseOrBidApprovalLogList = purchaseOrBidApprovalLogList;
        return this;
    }

    public OrderPrintApprovalLogDTO getSubmitAcceptLog() {
        return submitAcceptLog;
    }

    public WarehousePrintDataDTO setSubmitAcceptLog(OrderPrintApprovalLogDTO submitAcceptLog) {
        this.submitAcceptLog = submitAcceptLog;
        return this;
    }

    public OrderPrintApprovalLogDTO getSubmitApprovalLog() {
        return submitApprovalLog;
    }

    public WarehousePrintDataDTO setSubmitApprovalLog(OrderPrintApprovalLogDTO submitApprovalLog) {
        this.submitApprovalLog = submitApprovalLog;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehousePrintDataDTO.class.getSimpleName() + "[", "]")
                .add("inWarehouseDataList=" + inWarehouseDataList)
                .add("outWarehouseDataList=" + outWarehouseDataList)
                .add("warehouseReceiveDataList=" + warehouseReceiveDataList)
                .add("orderData=" + orderData)
                .add("purchaseLog=" + purchaseLog)
                .add("bidInitialApproveLog=" + bidInitialApproveLog)
                .add("bidFinalApproveLog=" + bidFinalApproveLog)
                .add("orderApproveSuccessLog=" + orderApproveSuccessLog)
                .add("purchaseOrBidApprovalLogList=" + purchaseOrBidApprovalLogList)
                .add("submitAcceptLog=" + submitAcceptLog)
                .toString();
    }
}
