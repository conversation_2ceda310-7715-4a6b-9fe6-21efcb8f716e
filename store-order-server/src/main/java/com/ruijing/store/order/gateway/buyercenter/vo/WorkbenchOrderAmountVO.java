package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2020/12/28 下午2:13
 * @description: 工作台的订单金额VO
 */
public class WorkbenchOrderAmountVO implements Serializable {

    private static final long serialVersionUID = 2994104870866360573L;

    @RpcModelProperty("今日金额")
    private Double dayAmount;

    @RpcModelProperty("本月金额")
    private Double monthAmount;

    public Double getDayAmount() {
        return dayAmount;
    }

    public void setDayAmount(Double dayAmount) {
        this.dayAmount = dayAmount;
    }

    public Double getMonthAmount() {
        return monthAmount;
    }

    public void setMonthAmount(Double monthAmount) {
        this.monthAmount = monthAmount;
    }

    @Override
    public String toString() {
        return "WorkbenchOrderAmountVO{" +
                "dayAmount=" + dayAmount +
                ", monthAmount=" + monthAmount +
                '}';
    }
}
