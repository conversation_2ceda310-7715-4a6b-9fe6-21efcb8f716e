package com.ruijing.store.order.business.bo.buyercenter.operationlog;

import java.util.List;

/**
 * @Author: <PERSON>g <PERSON>
 * @Date: 2021/1/12 22:13
 */
public class InboundApproveResultBO {

    /**
     * 一级入库审批信息列表
     */
    private List<InboundInfoResult> inboundInfoResult;

    /**
     * 二级入库审批信息列表-中大眼科专用
     */
    private List<YKSecondInboundInfoResult> secondInboundInfoResult;

    public List<InboundInfoResult> getInboundInfoResult() {
        return inboundInfoResult;
    }

    public InboundApproveResultBO setInboundInfoResult(List<InboundInfoResult> inboundInfoResult) {
        this.inboundInfoResult = inboundInfoResult;
        return this;
    }

    public List<YKSecondInboundInfoResult> getSecondInboundInfoResult() {
        return secondInboundInfoResult;
    }

    public InboundApproveResultBO setSecondInboundInfoResult(List<YKSecondInboundInfoResult> secondInboundInfoResult) {
        this.secondInboundInfoResult = secondInboundInfoResult;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InboundApproveResultBO{");
        sb.append("inboundInfoResult=").append(inboundInfoResult);
        sb.append(", secondInboundInfoResult=").append(secondInboundInfoResult);
        sb.append('}');
        return sb.toString();
    }
}
