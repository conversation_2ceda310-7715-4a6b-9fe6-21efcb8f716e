package com.ruijing.store.order.gateway.supplier.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/2/1 15:56
 * @description
 */
@RpcModel("供应商信息")
public class SuppInfoVO implements Serializable {

    private static final long serialVersionUID = 2101178199637789775L;
    
    @RpcModelProperty("供应商名")
    private String suppName;
    
    @RpcModelProperty("供应商id")
    private Integer suppId;

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", SuppInfoVO.class.getSimpleName() + "[", "]")
                .add("suppName='" + suppName + "'")
                .add("suppId=" + suppId)
                .toString();
    }
}
