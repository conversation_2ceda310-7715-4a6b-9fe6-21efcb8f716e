package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2021/2/3 14:56
 * @Description
 **/
public class OrderDetailGoodsReturnBO {

    /**
     * 订单详情id
     */
    private Integer detailId;

    /**
     * 退货单号
     */
    private String returnNo;

    /**
     * 退货单id
     */
    private Integer goodsReturnId;

    /**
     * 退货数量
     */
    private BigDecimal quantity;

    /**
     * 供应商名字
     */
    private String supplierName;

    /**
     * 退货商品价格
     */
    private BigDecimal amount;

    /**
     * 退货单状态
     */
    private Integer GoodsReturnStatus;

    /**
     * 退货的气瓶码
     */
    private List<String> returnGasBottleBarcodes;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public OrderDetailGoodsReturnBO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public Integer getGoodsReturnId() {
        return goodsReturnId;
    }

    public void setGoodsReturnId(Integer goodsReturnId) {
        this.goodsReturnId = goodsReturnId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getGoodsReturnStatus() {
        return GoodsReturnStatus;
    }

    public void setGoodsReturnStatus(Integer goodsReturnStatus) {
        GoodsReturnStatus = goodsReturnStatus;
    }

    public List<String> getReturnGasBottleBarcodes() {
        return returnGasBottleBarcodes;
    }

    public OrderDetailGoodsReturnBO setReturnGasBottleBarcodes(List<String> returnGasBottleBarcodes) {
        this.returnGasBottleBarcodes = returnGasBottleBarcodes;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailGoodsReturnBO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("returnNo='" + returnNo + "'")
                .add("goodsReturnId=" + goodsReturnId)
                .add("quantity=" + quantity)
                .add("supplierName='" + supplierName + "'")
                .add("amount=" + amount)
                .add("GoodsReturnStatus=" + GoodsReturnStatus)
                .add("returnGasBottleBarcodes=" + returnGasBottleBarcodes)
                .toString();
    }
}
