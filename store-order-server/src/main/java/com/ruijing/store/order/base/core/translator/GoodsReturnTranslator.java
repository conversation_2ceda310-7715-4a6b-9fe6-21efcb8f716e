package com.ruijing.store.order.base.core.translator;

import com.reagent.order.dto.request.ThirdPartOrderReturnDTO;
import com.reagent.order.dto.request.ThirdPartOrderReturnDetailDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.goodsreturn.constant.GoodsReturnConstant;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyDetailRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnBarcodeDataDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnPageRequestDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnDetailPageVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoVO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnPageVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDetailDTO;
import com.ruijing.store.order.api.base.other.dto.OrderDetailReturnPrintDTO;
import com.ruijing.store.order.api.base.other.dto.OrderGoodsReturnPrintDTO;
import com.ruijing.store.order.base.core.bo.goodsreturn.GoodsReturnPageRequestBO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: GoodsReturn 转换类
 * @author: zhongyulei
 * @create: 2019/10/16 16:00
 **/
public class GoodsReturnTranslator {

    /**
     * GoodsReturnDTO 转换为 GoodsReturn
     * @param dto
     * @return
     */
    public static GoodsReturn dto2GoodsReturn(GoodsReturnDTO dto) {
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setId(dto.getId());
        goodsReturn.setCreationTime(dto.getCreationTime());
        goodsReturn.setUpdateTime(dto.getUpdateTime());
        goodsReturn.setReturnReason(dto.getReturnReason());
        goodsReturn.setRemark(dto.getRemark());

        Assert.notNull(dto.getQuantity(), "退货数量为空！");
        goodsReturn.setQuantity(dto.getQuantity());

        Assert.notNull(dto.getPrice(), "退货价格为空！");
        goodsReturn.setPrice(dto.getPrice());

        Assert.notNull(dto.getDetailId(), "退货单关联的订单详情Id为空！");
        goodsReturn.setDetailId(dto.getDetailId());

        goodsReturn.setUserId(dto.getUserId() != null ? dto.getUserId() : -1);
        goodsReturn.setSupplierId(dto.getSupplierId());
        goodsReturn.setSupplierName(dto.getSupplierName());
        goodsReturn.setGoodsReturnStatus(dto.getGoodsReturnStatus());

        Assert.notNull(dto.getReturnNo(), "退货单号为空！");
        goodsReturn.setReturnNo(dto.getReturnNo());
        goodsReturn.setReplyTime(dto.getReplyTime());
        goodsReturn.setReturnTime(dto.getReturnTime());
        goodsReturn.setReceiveTime(dto.getReceiveTime());
        goodsReturn.setCancelTime(dto.getCancelTime());
        goodsReturn.setRefuseReason(dto.getRefuseReason());
        goodsReturn.setApplyName(dto.getApplyName());
        goodsReturn.setPicth(dto.getPicth());
        goodsReturn.setAgreeReason(dto.getAgreeReason());

        return goodsReturn;
    }

    /**
     * do 转 goodsReturn 对象
     * @param goodsReturn
     * @return
     */
    public static GoodsReturnDTO doToDto(GoodsReturn goodsReturn) {
        GoodsReturnDTO goodsReturnDTO = new GoodsReturnDTO();
        goodsReturnDTO.setId(goodsReturn.getId());
        goodsReturnDTO.setCreationTime(goodsReturn.getCreationTime());
        goodsReturnDTO.setUpdateTime(goodsReturn.getUpdateTime());
        goodsReturnDTO.setReturnReason(goodsReturn.getReturnReason());
        goodsReturnDTO.setRemark(goodsReturn.getRemark());
        goodsReturnDTO.setQuantity(goodsReturn.getQuantity());
        goodsReturnDTO.setPrice(goodsReturn.getPrice());
        goodsReturnDTO.setDetailId(goodsReturn.getDetailId());
        goodsReturnDTO.setUserId(goodsReturn.getUserId() != null ? goodsReturn.getUserId() : -1);
        goodsReturnDTO.setSupplierId(goodsReturn.getSupplierId());
        goodsReturnDTO.setSupplierName(goodsReturn.getSupplierName());
        goodsReturnDTO.setGoodsReturnStatus(goodsReturn.getGoodsReturnStatus());
        goodsReturnDTO.setReturnNo(goodsReturn.getReturnNo());
        goodsReturnDTO.setReplyTime(goodsReturn.getReplyTime());
        goodsReturnDTO.setReturnTime(goodsReturn.getReturnTime());
        goodsReturnDTO.setReceiveTime(goodsReturn.getReceiveTime());
        goodsReturnDTO.setCancelTime(goodsReturn.getCancelTime());
        goodsReturnDTO.setRefuseReason(goodsReturn.getRefuseReason());
        goodsReturnDTO.setApplyName(goodsReturn.getApplyName());
        goodsReturnDTO.setPicth(goodsReturn.getPicth());
        goodsReturnDTO.setAgreeReason(goodsReturn.getAgreeReason());
        goodsReturnDTO.setInvalid(goodsReturn.getInvalid());
        goodsReturnDTO.setOrderId(goodsReturn.getOrderId());
        goodsReturnDTO.setOrderNo(goodsReturn.getOrderNo());

        String returnDetailJson = goodsReturn.getGoodsReturnDetailJSON();
        // 转换 detailArray JSON 数组
        if (StringUtils.isNotBlank(returnDetailJson)) {
            List<Map> detailListMap = JsonUtils.parseList(returnDetailJson, Map.class);
            List<GoodsReturnDetailDTO> detailList = new ArrayList<>(detailListMap.size());
            for (Map o : detailListMap) {
                Map<String, Object> detailMap = (Map<String, Object>) o;
                GoodsReturnDetailDTO goodsReturnDetailDTO = new GoodsReturnDetailDTO();
                goodsReturnDetailDTO.setDetailId(detailMap.get("detailId") == null ? 0 : Integer.parseInt(detailMap.get("detailId").toString()));
                goodsReturnDetailDTO.setGoodsName(detailMap.get("goodsName") == null ? "" : detailMap.get("goodsName").toString());
                goodsReturnDetailDTO.setGoodsCode(detailMap.get("goodsCode") == null ? "" : detailMap.get("goodsCode").toString());
                goodsReturnDetailDTO.setSpecification(detailMap.get("specification") == null ? "" : detailMap.get("specification").toString());
                goodsReturnDetailDTO.setBrand(detailMap.get("brand") == null ? "" : detailMap.get("brand").toString());
                goodsReturnDetailDTO.setPrice(detailMap.get("price") == null ? "" : detailMap.get("price").toString());
                goodsReturnDetailDTO.setQuantity(detailMap.get("quantity") == null ? "" : detailMap.get("quantity").toString());
                goodsReturnDetailDTO.setAmount(detailMap.get("amount") == null ? "" : detailMap.get("amount").toString());
                goodsReturnDetailDTO.setGoodsPicturePath(detailMap.get("goodsPicturePath") == null ? "" : detailMap.get("goodsPicturePath").toString());
                goodsReturnDetailDTO.setUnit(detailMap.get("unit") == null ? "" : detailMap.get("unit").toString());
                goodsReturnDetailDTO.setProductId(detailMap.get("productId") == null ? "" : detailMap.get("productId").toString());
                goodsReturnDetailDTO.setReturnReason(detailMap.get("returnReason") == null ? StringUtils.EMPTY : detailMap.get("returnReason").toString());
                goodsReturnDetailDTO.setRemark(detailMap.get("remark") == null ? StringUtils.EMPTY : detailMap.get("remark").toString());
                Object returnGasBottleBarcodes = detailMap.get("returnGasBottleBarcodes") == null ? null : detailMap.get("returnGasBottleBarcodes");
                if(returnGasBottleBarcodes != null){
                    if(returnGasBottleBarcodes instanceof List){
                        goodsReturnDetailDTO.setReturnGasBottleBarcodes((List<String>) returnGasBottleBarcodes);
                    } else if(returnGasBottleBarcodes instanceof String){
                        List<String> returnGasBottleBarcodeList = JsonUtils.parseList((String) returnGasBottleBarcodes, String.class);
                        goodsReturnDetailDTO.setReturnGasBottleBarcodes(returnGasBottleBarcodeList);
                    }
                }
                detailList.add(goodsReturnDetailDTO);
            }
            goodsReturnDTO.setGoodsReturnDetailDTOS(detailList);
        }

        return goodsReturnDTO;
    }

    /**
     * request dto 转 do 对象
     * @param request
     * @return
     */
    public static GoodsReturnPageRequestBO requestDTO2BO(GoodsReturnPageRequestDTO request) {
        GoodsReturnPageRequestBO result = new GoodsReturnPageRequestBO();
        result.setReturnNo(request.getReturnNo());
        result.setOrderNo(request.getOrderNo());
        Integer departmentId = request.getDepartmentId();
        if (departmentId != null) {
            result.setDepartmentIdList(New.list(departmentId));
        }
        result.setSupplierName(request.getSupplierName());
        result.setSupplierId(request.getSupplierId());
        result.setStartDate(request.getStartDate());
        result.setEndDate(request.getEndDate());

        return result;
    }

    /**
     * do 转 goodsReturn 对象
     * @param item
     * @return
     */
    public static GoodsReturnPageVO doToVO(GoodsReturn item) {
        GoodsReturnPageVO result = new GoodsReturnPageVO();
        result.setId(item.getId());
        result.setOrderId(item.getOrderId());
        result.setReturnNo(item.getReturnNo());
        result.setApplyTime(item.getCreationTime().getTime());
        result.setBuyerName(item.getBuyerName());
        result.setSupplierId(item.getSupplierId());
        result.setSupplierName(item.getSupplierName());
        result.setDepartmentName(item.getDepartmentName());
        result.setReturnStatus(item.getGoodsReturnStatus());
        result.setOrgCode(OrderCommonUtils.getOrgCodeByOrgId(item.getOrgId()));
        result.setOrgName(item.getOrgName());
        result.setOrderNo(item.getOrderNo());
        result.setOrderStatus(item.getOrderStatus());
        result.setTotalPrice(BigDecimal.ZERO);
        result.setDelayAcceptCount(item.getDelayAcceptCount());
        result.setReplyTime(item.getReplyTime());
        result.setAutoAcceptDays(0);
        if(result.getReplyTime() != null) {
            long autoAcceptDays = result.getReplyTime().getTime() / (1000 * 60 * 60 * 24) - System.currentTimeMillis() / (1000 * 60 * 60 * 24);
            autoAcceptDays = autoAcceptDays > 0 ? autoAcceptDays : 0;
            result.setAutoAcceptDays((int) autoAcceptDays);
        }
        String returnDetailJSON = item.getGoodsReturnDetailJSON();
        // 转换 detailArray JSON 数组
        if (StringUtils.isNotBlank(returnDetailJSON)) {
            List detailListMap = JsonUtils.fromJson(returnDetailJSON, List.class);
            List<GoodsReturnDetailPageVO> detailList = new ArrayList<>(detailListMap.size());
            BigDecimal totalPrice = BigDecimal.ZERO;
            for (Object o : detailListMap) {
                Map<String, Object> detailMap = (Map<String, Object>) o;
                GoodsReturnDetailPageVO detailItem = parseMapToPageVO(detailMap);
                totalPrice = totalPrice.add(BigDecimal.valueOf(Double.valueOf(detailItem.getAmount())));
                detailList.add(detailItem);
            }
            result.setGoodsReturnDetailList(detailList);
            result.setTotalPrice(totalPrice);
        }
        return result;
    }

    /**
     * 申请退货单入参 DTO 转换为 DO 对象
     * @param request   入参DTO
     * @return          DO对象
     */
    public static GoodsReturn applyRequestDTOToDO(GoodsReturnApplyRequestDTO request, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        GoodsReturn result = new GoodsReturn();
        result.setOrgId(orderMasterDO.getFuserid());
        result.setSupplierId(request.getSupplierId());
        result.setSupplierName(request.getSupplierName());
        // 申请退货初始状态为 0-待确认
        result.setGoodsReturnStatus(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
        List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList = request.getReturnApplyDetailList();
        if (CollectionUtils.isNotEmpty(returnApplyDetailList)) {
            // 填充订单粒度退货原因到商品粒度
            for (GoodsReturnApplyDetailRequestDTO applyDetailRequestDTO : returnApplyDetailList) {
                if (StringUtils.isBlank(applyDetailRequestDTO.getReturnReason())) {
                    applyDetailRequestDTO.setReturnReason(request.getReturnReason());
                }
                if (StringUtils.isBlank(applyDetailRequestDTO.getRemark())) {
                    applyDetailRequestDTO.setRemark(request.getRemark());
                }
            }
        }
        // 填充detail表信息
        buildReturnApplyDetails(request.getReturnApplyDetailList(), orderDetailDOList);
        result.setGoodsReturnDetailJSON(JsonUtils.toJson(returnApplyDetailList));
        result.setOrderNo(request.getOrderNo());
        result.setDepartmentId(request.getDepartmentId());
        result.setInvalid(GoodsReturnInvalidEnum.NORMAL.getCode());
        result.setDepartmentName(request.getDepartmentName());
        result.setBuyerName(request.getBuyerName());

        result.setOrderId(orderMasterDO.getId());
        result.setOrderNo(orderMasterDO.getForderno());
        result.setOrderStatus(orderMasterDO.getStatus());
        result.setBuyerName(orderMasterDO.getFbuyername());
        result.setDepartmentId(orderMasterDO.getFbuydepartmentid());
        result.setReturnReason(request.getReturnReason());
        result.setRemark(request.getRemark());
        return result;
    }

    /**
     * 填充退货的商品详情信息
     * @param returnApplyDetailList 申请退货详情列表
     * @param orderDetailDOList 订单详情DO
     */
    public static void buildReturnApplyDetails(List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList,
                                                List<OrderDetailDO> orderDetailDOList) {
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        returnApplyDetailList.forEach(item -> {
            OrderDetailDO orderDetailDO = detailIdIdentityMap.get(item.getDetailId());
            if (orderDetailDO == null) {
                return;
            }
            item.setProductId(orderDetailDO.getProductSn().toString());
            item.setGoodsName(orderDetailDO.getFgoodname());
            item.setGoodsCode(orderDetailDO.getFgoodcode());
            item.setProductCode(orderDetailDO.getProductCode());
            item.setSpecification(orderDetailDO.getFspec());
            item.setBrand(orderDetailDO.getFbrand());
            item.setPrice(orderDetailDO.getFbidprice());
            item.setAmount(orderDetailDO.getFbidprice().multiply(item.getQuantity()));
            item.setDangerousTag(orderDetailDO.getDangerousTypeName());
            item.setUnit(orderDetailDO.getFunit());
            item.setGoodsPicturePath(orderDetailDO.getFpicpath());
        });
    }

    /**
     * do 对象转换为 退货单详情对象 vo
     * @param item  do
     * @return      退货详情vo
     */
    public static GoodsReturnInfoVO doToGoodsReturnInfoVO(GoodsReturn item) {
        GoodsReturnInfoVO result = new GoodsReturnInfoVO();
        result.setAgreeReason(item.getAgreeReason());
        result.setOrgName(item.getOrgName());
        result.setOrgId(item.getOrgId());

        result.setId(item.getId());
        result.setReturnNo(item.getReturnNo());
        result.setOrderNo(item.getOrderNo());
        result.setOrderId(item.getOrderId());
        result.setDepartmentName(item.getDepartmentName());
        result.setBuyerName(item.getBuyerName());
        result.setSupplierId(item.getSupplierId());
        result.setSupplierName(item.getSupplierName());
        result.setReturnStatus(item.getGoodsReturnStatus());
        result.setSupplierName(item.getSupplierName());
        result.setCreationTime(item.getCreationTime());
        result.setDelayAcceptCount(item.getDelayAcceptCount());
        String returnDetailJSON = item.getGoodsReturnDetailJSON();
        // 转换 detailArray JSON 数组
        List<GoodsReturnInfoDetailVO> returnInfoDetailList = parseJSONToInfoDetailVO(returnDetailJSON);
        if (CollectionUtils.isNotEmpty(returnInfoDetailList)) {
            // 填充订单粒度退货原因到商品粒度
            for (GoodsReturnInfoDetailVO goodsReturnInfoDetailVO : returnInfoDetailList) {
                if(StringUtils.isBlank(goodsReturnInfoDetailVO.getReturnReason())){
                    goodsReturnInfoDetailVO.setReturnReason(item.getReturnReason());
                }
                if(StringUtils.isBlank(goodsReturnInfoDetailVO.getRemark())){
                    goodsReturnInfoDetailVO.setRemark(item.getRemark());
                }
            }
        }
        // 如果订单退货原因和所有商品退货原因相同，则设置前端按 订单退货原因粒度 展示
        result.setReturnReasonType(GoodsReturnConstant.RETURN_REASON_PRODUCT_TYPE);
        if (returnInfoDetailList.stream().allMatch(detailVO -> StringUtils.equals(detailVO.getReturnReason(), item.getReturnReason()))) {
            result.setReturnReasonType(GoodsReturnConstant.RETURN_REASON_ORDER_TYPE);
        }

        result.setReturnInfoDetailList(returnInfoDetailList);
        BigDecimal totalAmount = Optional.ofNullable(returnInfoDetailList).orElseGet(() -> new ArrayList<>()).stream()
                .map(GoodsReturnInfoDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setAmount(totalAmount);
        result.setReturnReason(item.getReturnReason());
        result.setRemark(item.getRemark());
        return result;
    }

    /**
     * 解析退货商品详情字符串为 GoodsReturnInfoDetailVO 数组
     * @param returnDetailJSON
     * @return
     */
    public static List<GoodsReturnInfoDetailVO> parseJSONToInfoDetailVO(String returnDetailJSON) {
        if (StringUtils.isBlank(returnDetailJSON)) {
            return Collections.emptyList();
        }
        List<Map> detailListMap = JsonUtils.parseList(returnDetailJSON, Map.class);
        List<GoodsReturnInfoDetailVO> detailList = new ArrayList<>(detailListMap.size());
        for (Map o : detailListMap) {
            Map<String, Object> detailMap = (Map<String, Object>) o;
            GoodsReturnInfoDetailVO detailItem = parseMapToInfoDetailVO(detailMap);
            detailList.add(detailItem);
        }
        return detailList;
    }

    /**
     * 将GoodsReturnInfoDetailVO列表转换为GoodsReturnApplyDetailRequestDTO列表
     * @param returnDetailVOList GoodsReturnInfoDetailVO列表
     * @return GoodsReturnApplyDetailRequestDTO列表
     */
    public static List<GoodsReturnApplyDetailRequestDTO> convertReturnDetail2Request(List<GoodsReturnInfoDetailVO> returnDetailVOList) {
        if (CollectionUtils.isEmpty(returnDetailVOList)) {
            return New.emptyList();
        }

        List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList = New.list();
        for (GoodsReturnInfoDetailVO detailVO : returnDetailVOList) {
            GoodsReturnApplyDetailRequestDTO detailDTO = new GoodsReturnApplyDetailRequestDTO();
            // 基本字段
            detailDTO.setDetailId(Integer.valueOf(detailVO.getDetailId()));
            detailDTO.setGoodsName(detailVO.getGoodsName());
            detailDTO.setGoodsCode(detailVO.getGoodsCode());
            detailDTO.setSpecification(detailVO.getSpecification());
            detailDTO.setBrand(detailVO.getBrand());
            detailDTO.setPrice(detailVO.getPrice());
            detailDTO.setQuantity(detailVO.getQuantity());
            detailDTO.setAmount(detailVO.getAmount());
            detailDTO.setDangerousTag(detailVO.getDangerousTag());
            detailDTO.setGoodsPicturePath(detailVO.getGoodsPicturePath());
            detailDTO.setProductId(detailVO.getProductId());
            detailDTO.setUnit(detailVO.getUnit());
            detailDTO.setReturnReason(detailVO.getReturnReason());
            detailDTO.setRemark(detailVO.getRemark());
            detailDTO.setProductCode(detailVO.getProductCode());
            detailDTO.setReturnGasBottleBarcodes(detailVO.getReturnGasBottleBarcodes());
            detailDTO.setGoodsReturnBarcodeDataDTOList(detailVO.getGoodsReturnBarcodeDataDTOList());
            returnApplyDetailList.add(detailDTO);
        }

        return returnApplyDetailList;
    }

    /**
     * 解析 do 对象中的JSON字符串为 退货商品 vo
     * @param detailMap
     * @return
     */
    private static GoodsReturnInfoDetailVO parseMapToInfoDetailVO(Map<String, Object> detailMap) {
        GoodsReturnInfoDetailVO result = new GoodsReturnInfoDetailVO();
        result.setDetailId(detailMap.get("detailId") == null ? "" : detailMap.get("detailId").toString());
        result.setBrand(detailMap.get("brand") == null ? "" : detailMap.get("brand").toString());
        Object detailAmount = detailMap.get("amount");
        if (detailAmount != null) {
            result.setAmount(new BigDecimal(detailAmount.toString()));
        } else {
            result.setAmount(BigDecimal.ZERO);
        }
        result.setGoodsName(detailMap.get("goodsName") == null ? "" : detailMap.get("goodsName").toString());
        result.setGoodsCode(detailMap.get("goodsCode") == null ? "" : detailMap.get("goodsCode").toString());
        result.setProductCode(detailMap.get("productCode") == null ? "" : detailMap.get("productCode").toString());
        result.setSpecification(detailMap.get("specification") == null ? "" : detailMap.get("specification").toString());
        result.setDangerousTag(detailMap.get("dangerousTag") == null ? "" : detailMap.get("dangerousTag").toString());
        Object quantity = detailMap.get("quantity");
        if (quantity != null) {
            result.setQuantity(new BigDecimal(quantity.toString()));
        } else {
            result.setQuantity(BigDecimal.ZERO);
        }
        result.setReturnReason(detailMap.get("returnReason") == null ? "" : detailMap.get("returnReason").toString());
        result.setRemark(detailMap.get("remark") == null ? "" : detailMap.get("remark").toString());
        result.setGoodsPicturePath(detailMap.get("goodsPicturePath") == null ? "" : detailMap.get("goodsPicturePath").toString());
        result.setProductId(detailMap.get("productId") == null ? "" : detailMap.get("productId").toString());
        result.setUnit(detailMap.get("unit") == null ? "" : detailMap.get("unit").toString());
        Object price = detailMap.get("price");
        if (price != null) {
            result.setPrice(new BigDecimal(price.toString()));
        } else {
            result.setPrice(BigDecimal.ZERO);
        }

        result.setReturnAcceptMan(detailMap.getOrDefault("returnAcceptMan", StringUtils.EMPTY).toString());
        result.setReturnAcceptPhone(detailMap.getOrDefault("returnAcceptPhone", StringUtils.EMPTY).toString());
        result.setReturnAcceptAddr(detailMap.getOrDefault("returnAcceptAddr", StringUtils.EMPTY).toString());
        Object returnGasBottleBarcodes = detailMap.get("returnGasBottleBarcodes") == null ? null : detailMap.get("returnGasBottleBarcodes");
        if(returnGasBottleBarcodes != null){
            if(returnGasBottleBarcodes instanceof List){
                result.setReturnGasBottleBarcodes((List<String>) returnGasBottleBarcodes);
            } else if(returnGasBottleBarcodes instanceof String){
                List<String> returnGasBottleBarcodeList = JsonUtils.parseList((String) returnGasBottleBarcodes, String.class);
                result.setReturnGasBottleBarcodes(returnGasBottleBarcodeList);
            }
        }
        List<Map<String, String>> goodsReturnBarcodeDataDTOMapList = detailMap.get("goodsReturnBarcodeDataDTOList") == null ? null : (List<Map<String, String>>) detailMap.get("goodsReturnBarcodeDataDTOList");
        if(CollectionUtils.isNotEmpty(goodsReturnBarcodeDataDTOMapList)){
            result.setGoodsReturnBarcodeDataDTOList(goodsReturnBarcodeDataDTOMapList.stream().map(map->{
                GoodsReturnBarcodeDataDTO goodsReturnBarcodeDataDTO = new GoodsReturnBarcodeDataDTO();
                goodsReturnBarcodeDataDTO.setBarcode(map.get("barcode"));
                goodsReturnBarcodeDataDTO.setReturnReason(map.get("returnReason"));
                goodsReturnBarcodeDataDTO.setReturnDescription(map.get("returnDescription"));
                return goodsReturnBarcodeDataDTO;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 解析JSON字典对象为退货商品VO对象
     * @param detailMap
     * @return
     */
    private static GoodsReturnDetailPageVO parseMapToPageVO(Map<String, Object> detailMap) {
        GoodsReturnDetailPageVO result = new GoodsReturnDetailPageVO();
        result.setDetailId(detailMap.get("detailId") == null ? "" : detailMap.get("detailId").toString());
        result.setProductId(detailMap.get("productId") == null ? "" : detailMap.get("productId").toString());
        result.setGoodsName(detailMap.get("goodsName") == null ? "" : detailMap.get("goodsName").toString());
        result.setGoodsCode(detailMap.get("goodsCode") == null ? "" : detailMap.get("goodsCode").toString());
        result.setProductCode(detailMap.get("productCode") == null ? "" : detailMap.get("productCode").toString());
        result.setSpecification(detailMap.get("specification") == null ? "" : detailMap.get("specification").toString());
        result.setBrand(detailMap.get("brand") == null ? "" : detailMap.get("brand").toString());
        result.setPrice(detailMap.get("price") == null ? "" : detailMap.get("price").toString());
        result.setQuantity(detailMap.get("quantity") == null ? "" : detailMap.get("quantity").toString());
        result.setAmount(detailMap.get("amount") == null ? "" : detailMap.get("amount").toString());
        result.setGoodsPicturePath(detailMap.get("goodsPicturePath") == null ? "" : detailMap.get("goodsPicturePath").toString());
        result.setDangerousTag(detailMap.get("dangerousTag") == null ? "" : detailMap.get("dangerousTag").toString());

        return result;
    }

    /**
     * 退货do转换为退货打印dto
     * @param goodsReturnList
     * @return
     */
    public static List<OrderGoodsReturnPrintDTO> doToPrintDTO(List<GoodsReturn> goodsReturnList) {
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return Collections.emptyList();
        }
        List<OrderGoodsReturnPrintDTO> result = goodsReturnList.stream().map(GoodsReturnTranslator::doToPrintDTO).collect(Collectors.toList());
        return result;
    }

    /**
     * 退货do转换为退货打印dto
     * @param g
     * @return
     */
    public static OrderGoodsReturnPrintDTO doToPrintDTO(GoodsReturn g) {
        OrderGoodsReturnPrintDTO result = new OrderGoodsReturnPrintDTO();
        result.setReturnNo(g.getReturnNo());
        result.setApplyGoodsReturnTime(g.getCreationTime());
        result.setReturnStatus(g.getGoodsReturnStatus());
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = parseJSONToInfoDetailVO(g.getGoodsReturnDetailJSON());
        List<OrderDetailReturnPrintDTO> orderDetailReturnPrintDTOList = new ArrayList<>(goodsReturnInfoDetailVOList.size());
        for (GoodsReturnInfoDetailVO voItem : goodsReturnInfoDetailVOList) {
            OrderDetailReturnPrintDTO printDTO = parseReturnVOToReturnPrintDTO(voItem);
            orderDetailReturnPrintDTOList.add(printDTO);
            BigDecimal returnTotalPrice = result.getTotalPrice();
            if (returnTotalPrice == null) {
                result.setTotalPrice(printDTO.getAmount());
                continue;
            }
            returnTotalPrice = returnTotalPrice.add(printDTO.getAmount());
            result.setTotalPrice(returnTotalPrice);
        }

        result.setReturnDetailPrintList(orderDetailReturnPrintDTOList);
        return result;
    }

    /**
     * 退货明细vo转换为退货明细打印dto
     * @param voItem    returnDetailVO
     * @return          returnDetailPrintDTO
     */
    private static OrderDetailReturnPrintDTO parseReturnVOToReturnPrintDTO(GoodsReturnInfoDetailVO voItem) {
        OrderDetailReturnPrintDTO result = new OrderDetailReturnPrintDTO();
        result.setGoodsName(voItem.getGoodsName());
        result.setGoodsCode(voItem.getGoodsCode());
        result.setBrand(voItem.getBrand());
        result.setSpecification(voItem.getSpecification());
        result.setUnit(voItem.getUnit());
        result.setQuantity(voItem.getQuantity());
        result.setReturnReason(voItem.getReturnReason());
        result.setPrice(voItem.getPrice());
        result.setAmount(voItem.getAmount());

        return result;
    }

    /**
     * 退货明细vo转换为第三方管理平台退货明细dto
     * @param item
     * @return
     */
    public static ThirdPartOrderReturnDTO doToThirdPartOrderDTO(GoodsReturn item) {
        ThirdPartOrderReturnDTO result = new ThirdPartOrderReturnDTO();
        result.setId(item.getId());
        result.setReturnNo(item.getReturnNo());
        result.setOrderNo(item.getOrderNo());
        result.setReturnStatus(item.getGoodsReturnStatus());
        result.setReturnDetailList(doToThirdPartReturnDetailDTO(parseJSONToInfoDetailVO(item.getGoodsReturnDetailJSON())));

        return result;
    }

    private static List<ThirdPartOrderReturnDetailDTO> doToThirdPartReturnDetailDTO(List<GoodsReturnInfoDetailVO> detailVOS) {
        return detailVOS
                .stream()
                .map(it -> {
                    ThirdPartOrderReturnDetailDTO detail = new ThirdPartOrderReturnDetailDTO();
                    detail.setDetailId(Integer.parseInt(it.getDetailId()));
                    detail.setProductId(it.getProductId());
                    detail.setQuantity(it.getQuantity());
                    detail.setPrice(it.getPrice());
                    detail.setAmount(it.getAmount());
                    return detail;
                })
                .collect(Collectors.toList());
    }
}
