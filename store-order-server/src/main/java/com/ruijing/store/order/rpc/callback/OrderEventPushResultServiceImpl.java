package com.ruijing.store.order.rpc.callback;

import com.reagent.order.api.OrderEventPushCallbackService;
import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.reagent.order.enums.OrderPushEventEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.cancel.business.rpc.service.CancelOrderCallbackService;
import com.ruijing.store.goodsreturn.callback.OrderGoodsReturnCallbackService;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.generate.callback.service.OrderGenerateCallBackService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.other.callback.PushInvoiceCallbackService;
import com.ruijing.store.warehouse.callback.WarehouseCallbackService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-05-11 10:53
 */
@MSharpService
public class OrderEventPushResultServiceImpl implements OrderEventPushCallbackService{

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private OrderGenerateCallBackService orderGenerateCallBackService;
    
    @Resource
    private WarehouseCallbackService warehouseCallbackService;
    
    @Resource
    private PushInvoiceCallbackService pushInvoiceCallbackService;

    @Resource
    private CancelOrderCallbackService cancelOrderCallbackService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderGoodsReturnCallbackService orderGoodsReturnCallbackService;


    @Override
    @ServiceLog(operationType = OperationType.WRITE,description = "处理推送回调", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> handleCallback(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        OrderPushEventEnum orderEventPushResultTypeEnum = orderEventPushResultResponseDTO.getOrderEventResultEnum();
        String failReason = orderEventPushResultResponseDTO.getFailReason();
        orderEventPushResultResponseDTO.setFailReason(StringUtils.truncate(failReason, DockingConstant.ORDER_APPROVAL_LOG_REASON_MAX_LENGTH));
        switch (orderEventPushResultTypeEnum){
            case GENERATE_ORDER:
                orderGenerateCallBackService.pushOrderToBuyerCallBack(orderEventPushResultResponseDTO);
                break;
            case WAREHOUSE:
                warehouseCallbackService.handleCallback(orderEventPushResultResponseDTO);
                break;
            case INVOICE_PUSH:
                pushInvoiceCallbackService.handleCallBack(orderEventPushResultResponseDTO);
                break;
            case BUYER_APPLY_RETURN_GOODS_TO_SUPPLIER:
                orderGoodsReturnCallbackService.pushOrderToSuppCallBack(orderEventPushResultResponseDTO);
                break;
            case QUERY_OUTER_BUYER_STATUS:
                break;
            default:
                this.defaultCallbackHandler(orderEventPushResultResponseDTO);
                break;
        }
        return RemoteResponse.success();
    }

    private void defaultCallbackHandler(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        String orderNo = orderEventPushResultResponseDTO.getOrderNo();
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderNo);
        Preconditions.notNull(orderMaster, "没有查到" + orderNo + "对应的订单");
        Integer orderId = orderMaster.getId();
        // 记日志
        OrderApprovalEnum orderApprovalEnum = null;
        switch (orderEventPushResultResponseDTO.getOrderEventStatusEnum()) {
            case PUSHING:
                orderApprovalEnum = OrderApprovalEnum.PUSHING_ORDER_STATUS_TO_THIRD;
                break;
            case COMPLETE:
                orderApprovalEnum = OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_SUCCESS;
                break;
            case FAILED:
                orderApprovalEnum = OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_FAILURE;
                break;
            default:
                break;
        }
        if (orderApprovalEnum != null) {
            orderApprovalLogService.saveApprovalLog(orderId, orderApprovalEnum.getValue(), DockingConstant.SYSTEM_OPERATOR_ID, orderEventPushResultResponseDTO.getFailReason());
        }

    }
}
