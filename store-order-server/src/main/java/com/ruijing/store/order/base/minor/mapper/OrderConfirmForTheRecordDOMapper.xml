<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO">
    <!--@mbg.generated-->
    <!--@Table t_order_confirm_for_the_record-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="pics" jdbcType="VARCHAR" property="pics" />
    <result column="add_pics" jdbcType="VARCHAR" property="addPics" />
    <result column="is_confirm" jdbcType="BIT" property="isConfirm" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="deletion_time" jdbcType="TIMESTAMP" property="deletionTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, pics, add_pics, is_confirm, `type`, creation_time, update_time, is_deleted, 
    deletion_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_confirm_for_the_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_order_confirm_for_the_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO">
    <!--@mbg.generated-->
    insert into t_order_confirm_for_the_record (id, order_id, pics, 
      add_pics, is_confirm, `type`, 
      creation_time, update_time, is_deleted, 
      deletion_time)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, #{pics,jdbcType=VARCHAR}, 
      #{addPics,jdbcType=VARCHAR}, #{isConfirm,jdbcType=BIT}, #{type,jdbcType=INTEGER},
      #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=BIT}, 
      #{deletionTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO">
    <!--@mbg.generated-->
    insert into t_order_confirm_for_the_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="pics != null">
        pics,
      </if>
      <if test="addPics != null">
        add_pics,
      </if>
      <if test="isConfirm != null">
        is_confirm,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="deletionTime != null">
        deletion_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="pics != null">
        #{pics,jdbcType=VARCHAR},
      </if>
      <if test="addPics != null">
        #{addPics,jdbcType=VARCHAR},
      </if>
      <if test="isConfirm != null">
        #{isConfirm,jdbcType=BIT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deletionTime != null">
        #{deletionTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO">
    <!--@mbg.generated-->
    update t_order_confirm_for_the_record
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="pics != null">
        pics = #{pics,jdbcType=VARCHAR},
      </if>
      <if test="addPics != null">
        add_pics = #{addPics,jdbcType=VARCHAR},
      </if>
      <if test="isConfirm != null">
        is_confirm = #{isConfirm,jdbcType=BIT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deletionTime != null">
        deletion_time = #{deletionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO">
    <!--@mbg.generated-->
    update t_order_confirm_for_the_record
    set order_id = #{orderId,jdbcType=INTEGER},
      pics = #{pics,jdbcType=VARCHAR},
      add_pics = #{addPics,jdbcType=VARCHAR},
      is_confirm = #{isConfirm,jdbcType=BIT},
      `type` = #{type,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      deletion_time = #{deletionTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-11-22-->
  <select id="findByOrderIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_order_confirm_for_the_record
    where order_id in
    <foreach item="item" index="index" collection="orderIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-07-29-->
  <select id="countByOrderId" resultType="java.lang.Integer">
        select count(1)
        from t_order_confirm_for_the_record
        where order_id=#{orderId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-09-16-->
  <update id="updateByOrderId">
    update t_order_confirm_for_the_record
    <set>
      <if test="updated.pics != null">
        pics = #{updated.pics,jdbcType=VARCHAR},
      </if>
      <if test="updated.addPics != null">
        add_pics = #{updated.addPics,jdbcType=VARCHAR},
      </if>
      <if test="updated.isConfirm != null">
        is_confirm = #{updated.isConfirm,jdbcType=BIT},
      </if>
      <if test="updated.type != null">
        type = #{updated.type,jdbcType=INTEGER},
      </if>
      <if test="updated.creationTime != null">
        creation_time = #{updated.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updateTime != null">
        update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.isDeleted != null">
        is_deleted = #{updated.isDeleted,jdbcType=BIT},
      </if>
      <if test="updated.deletionTime != null">
        deletion_time = #{updated.deletionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where order_id=#{updated.orderId,jdbcType=INTEGER}
  </update>
</mapper>