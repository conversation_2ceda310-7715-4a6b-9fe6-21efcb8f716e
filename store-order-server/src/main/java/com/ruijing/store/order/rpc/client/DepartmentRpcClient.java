package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.user.api.enums.department.DepartmentTypeEnum;
import com.ruijing.store.user.api.response.PageRemoteResponse;
import com.ruijing.store.user.api.service.DepartmentRpcService;
import com.ruijing.store.user.api.service.UserDepartmentInfoService;
import com.ruijing.store.user.api.service.DepartmentThirdPartyRpcService;
import com.ruijing.store.user.api.service.UserDepartmentRoleRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 部门/课题组的 rpc 客户端
 */
@ServiceClient
public class DepartmentRpcClient {

    @MSharpReference(remoteAppkey = "store-user-service")
    private DepartmentRpcService departmentRpcService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserDepartmentRoleRpcService userDepartmentRoleRpcService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserDepartmentInfoService userDepartmentInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private DepartmentThirdPartyRpcService departmentThirdPartyRpcService;

    @Resource
    private UserClient userClient;

    /**
     * 通过锐竞课题组id查找中爆特殊从业id
     * @return
     */
    @ServiceLog(description = "通过锐竞课题组id查找中爆特殊从业id", serviceType = ServiceType.COMMON_SERVICE)
    public String findRefCompanyIdByDepartmentId(Integer departmentId) {
        Assert.notNull(departmentId, "查询中爆特殊从业id失败，depatmentId不能为空");

        RemoteResponse<String> response = departmentRpcService.getRefDepartmentCpByCompanyId(departmentId);
        Assert.notNull(response, "查询中爆特殊从业id异常，response为空");
        Assert.isTrue(response.isSuccess(), "查询中爆特殊从业id异常" + response.getMsg());

        return response.getData();
    }

    /**
     * 获取用户当前登录单位所在的课题组
     * @param userId 用户id
     * @param orgId 当前登录单位id
     * @return 当前登录单位所在的课题组
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取用户当前登录单位所在的课题组")
    public List<DepartmentDTO> getDepartmentsForUser(Long userId, Long orgId) {
        Preconditions.notNull(userId, "用户id不能为空");
        Preconditions.notNull(orgId, "orgId不能为空");
        RemoteResponse<List<DepartmentDTO>> remoteResponse = departmentRpcService.getDepartmentsForUser(userId, orgId);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 通过id集合获取课题组信息
     * @param departmentIds 部门id
     * @return 部门
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取课题组信息")
    public List<DepartmentDTO> getDepartmentsByIds(List<Long> departmentIds) {
        List<List<Long>> partitionList = Lists.partition(departmentIds, 200);
        List<DepartmentDTO> departmentDTOList = new ArrayList<>(departmentIds.size());
        for (List<Long> departmentIdList : partitionList) {
            RemoteResponse<List<DepartmentDTO>> remoteResponse = departmentRpcService.getDepartmentsByIds(New.list(departmentIdList));
            Preconditions.isTrue(remoteResponse.isSuccess(),remoteResponse.getMsg());
            departmentDTOList.addAll(remoteResponse.getData());
        }
        return departmentDTOList;
    }

    /**
     * 获取根部门所有子节点
     * @param orgId
     * @param deptId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取子部门")
    public List<MiniDepartmentDTO> getAllChildDepartment(Integer orgId, Integer deptId) {
        RemoteResponse<List<MiniDepartmentDTO>> remoteResponse = departmentRpcService.getAllChild(orgId, deptId);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 根据单位orgid和department id列表批量获得子部门列表
     *
     * @param orgId  单位ID
     * @param deptIdList 父节点ID
     * @return 返回父节点下所有子部门列表
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据父节点id批量获得子部门列表")
    public List<DepartmentDTO> batchGetChildDeptList(Integer orgId, List<Integer> deptIdList) {
        if (CollectionUtils.isNotEmpty(deptIdList)) {
            List<List<Integer>> deptIdPartList = Lists.partition(deptIdList, 200);
            List<DepartmentDTO> deptList = New.list();
            for (List<Integer> deptIdPart : deptIdPartList) {
                RemoteResponse<List<DepartmentDTO>> remoteResponse = departmentRpcService.getOrgDepartment(orgId, New.list(deptIdPart));
                Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
                deptList.addAll(remoteResponse.getData() == null ? New.list() : remoteResponse.getData());
            }
            return deptList;
        } else {
            return New.list();
        }
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据父节点id获得子部门列表")
    public List<MiniDepartmentDTO> getAllChildWithoutParent(@NotNull Integer orgId, @NotNull Integer deptId){
        RemoteResponse<List<MiniDepartmentDTO>> response = departmentRpcService.getAllChildWithoutParent(orgId, deptId);
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError("DepartmentRpcClient", "getAllChildWithoutParent", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参|orgId:" + orgId + "|deptId:" + deptId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取用户有权限的直属课题组列表，和直属课题组中管理类型课题组的所有子课题组列表，去重返回(PS:采购类型课题组没子课题组)")
    public List<MiniDepartmentDTO> getDeptTreeForUserByAccess(Integer userId, Integer orgId, String accessCode) {
        //找出具有accessCode的课题组，没有就返回
        List<DepartmentDTO> hasAccessDeptList = userClient.getDeptForUserByAccess(userId, orgId, accessCode);
        if (CollectionUtils.isEmpty(hasAccessDeptList)) {
            return Collections.emptyList();
        }
        //有accessCode的课题组都需要返回
        List<MiniDepartmentDTO> deptListToReturn = new ArrayList<>();
        List<Integer> deptIdListToRemoveDuplicate = new ArrayList<>();
        for (DepartmentDTO hasAccessDept : hasAccessDeptList) {
            //如果已经包含了，说明有父部门被处理了，直接跳过
            if (deptIdListToRemoveDuplicate.contains(hasAccessDept.getId())) {
                continue;
            }
            deptListToReturn.add(this.departmentDTO2MiniDepartmentDTO(hasAccessDept));
            deptIdListToRemoveDuplicate.add(hasAccessDept.getId());
            //管理部门下的所有子课题组都需要返回
            if (Objects.equals(DepartmentTypeEnum.ADMINISTRATING.getValue(), hasAccessDept.getDepartmentType())) {
                List<MiniDepartmentDTO> childDeptList = this.getAllChildWithoutParent(orgId, hasAccessDept.getId());
                for (MiniDepartmentDTO childDept : childDeptList) {
                    if (deptIdListToRemoveDuplicate.contains(childDept.getId())) {
                        continue;
                    }
                    deptListToReturn.add(childDept);
                    deptIdListToRemoveDuplicate.add(childDept.getId());
                }
            }
        }
        return deptListToReturn;
    }

    private MiniDepartmentDTO departmentDTO2MiniDepartmentDTO(DepartmentDTO from) {
        MiniDepartmentDTO to = new MiniDepartmentDTO(from.getId(), from.getName(), from.getDepartmentType(), from.getParentId());
        return to;
    }

    /**
     * 查询父级部门
     * @param curDeptId
     * @return
     */
    public DepartmentDTO getDepartmentParentInfo(Integer curDeptId) {
        Preconditions.notNull(curDeptId, "查询父级部门入参不可为空");
        RemoteResponse<DepartmentDTO> response = userDepartmentInfoService.getDepartmentParentInfo(curDeptId);
        // TODO：下述的判断是因为历史问题，后续等基础业务改再修改
        Preconditions.isTrue(response != null && (response.isSuccess() || Objects.equals(response.getCode(), 0)), "查询父级部门失败，curDeptId=" + curDeptId);
        return response.getData();
    }

    /**
     * 获取第三方部门信息
     * @param orgCode
     * @param jobNum
     * @param departmentName
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取第三方部门信息")
    public List<DepartmentThirdPartyDTO> findByUserIdAndOrgIdAndDepName(String orgCode, String jobNum, String departmentName) {
        Assert.notNull(orgCode, "获取第三方部门信息失败，orgCode不能为空");
        Assert.notNull(jobNum, "获取第三方部门信息失败，jobNum不能为空");
        Assert.notNull(departmentName, "获取第三方部门信息失败，departmentName不能为空");
        PageRemoteResponse<List<DepartmentThirdPartyDTO>> pageRemoteResponse
                = departmentThirdPartyRpcService.findByUserIdAndOrgIdAndDeptName(orgCode, jobNum, departmentName);
        Preconditions.isTrue(pageRemoteResponse.isSuccess(), pageRemoteResponse.getMsg());
        Assert.notNull(pageRemoteResponse, "获取第三方部门信息异常，response为空");
        return pageRemoteResponse.getData();
    }
}
