package com.ruijing.store.order.statement.enums;

/**
 * @description: 待结算单操作状态
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/12/26 11:51
 **/
public enum WaitingStatementOrderStatusEnum {
    NORMAL(0, "正常待结算"),
    REJECTED(1, "结算被驳回"),
    CANCEL(2, "撤销待结算"),
    ;

    private Integer code;
    private String description;

    WaitingStatementOrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }
}
