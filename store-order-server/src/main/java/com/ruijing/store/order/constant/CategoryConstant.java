package com.ruijing.store.order.constant;

import com.ruijing.fundamental.common.collections.New;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-06-01 14:31
 * @description: 常用分类
 **/
public class CategoryConstant {

    /**
     * 仪器设备ID
     */
    public static int INSTRUMENT_ID = 700;

    /**
     * 实验动物ID
     */
    public static int EXPERIMENT_ANIMAL_ID = 56;

    /**
     * 危化品ID
     */
    public static int DANGEROUS_ID = 460;

    /**
     * 科研服务id
     */
    public static Integer SCIENCE_SERVICE_ID = 113;

    /**
     * 化学试剂ID
     */
    public static final Integer CHEMICAL_REAGENTS_ID = 51;

    /**
     * 常规化学试剂
     */
    public static int CONVENTIONAL_CHEMICAL_REAGENTS = 216;

    /**
     * 危化品管制品相关二级分类id
     */
    public static List<Integer> REGULATORY_DANGEROUS_SECOND_LEVEL_ID = New.list(488, 497, 506, 515, 516, 517);

    /**
     * 生物医学服务
     */
    public static Integer BIO_SCI_SERVICE_ID = 211;

    /**
     * 动物实验服务
     */
    public static Integer ANIMAL_EXPERIMENT_SERVICE_ID = 648;

    /**
     * 化工技术服务
     */
    public static Integer CHEMICAL_TECH_SERVICE_ID = 660;
}
