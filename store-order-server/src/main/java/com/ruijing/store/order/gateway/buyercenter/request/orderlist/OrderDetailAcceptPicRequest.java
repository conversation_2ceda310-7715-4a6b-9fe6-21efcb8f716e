package com.ruijing.store.order.gateway.buyercenter.request.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptPictureDTO;

import java.io.Serializable;
import java.util.List;

@RpcModel("追加 订单详情关联图片请求体")
public class OrderDetailAcceptPicRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单ID")
    private Integer orderId;

    @RpcModelProperty("增量-验收图片链接-关联订单详情 列表")
    private List<AcceptPictureDTO> detailPictureDTOList;

    @RpcModelProperty("跳过日志记录")
    private Boolean skipLog;

    public Boolean getSkipLog() {
        return skipLog;
    }

    public OrderDetailAcceptPicRequest setSkipLog(Boolean skipLog) {
        this.skipLog = skipLog;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<AcceptPictureDTO> getDetailPictureDTOList() {
        return detailPictureDTOList;
    }

    public void setDetailPictureDTOList(List<AcceptPictureDTO> detailPictureDTOList) {
        this.detailPictureDTOList = detailPictureDTOList;
    }

    @Override
    public String toString() {
        return "OrderDetailAcceptPicRequest{" +
                "orderId=" + orderId +
                ", detailPictureDTOList=" + detailPictureDTOList +
                ", skipLog=" + skipLog +
                '}';
    }
}
