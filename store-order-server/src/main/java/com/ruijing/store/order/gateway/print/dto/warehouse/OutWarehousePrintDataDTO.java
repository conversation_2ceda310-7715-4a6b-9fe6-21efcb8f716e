package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/3/2 11:15
 * @description
 */
public class OutWarehousePrintDataDTO implements Serializable {

    private static final long serialVersionUID = 5905628424362440772L;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单流程种类 0:线上, 1:线下")
    private Integer orderSpecies;

    @RpcModelProperty(value = "出库单Id")
    private Integer outWarehouseApplicationId;

    @RpcModelProperty(value = "出库单号")
    private String outWarehouseApplicationNo;

    @RpcModelProperty(value = "出库申请时间")
    private Long outWarehouseApplicationTime;

    @RpcModelProperty(value = "出库申请人")
    private String outWarehouseApplicant;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "出库单状态（状态0未出库，1已出库）")
    private Integer status;

    @RpcModelProperty(value = "申请出库商品状态名称（未出库、已出库）")
    private String statusName;

    @RpcModelProperty(value = "出库单所有商品的总额的合计")
    private String totalPrice;

    @RpcModelProperty(value = "合计金额的大写")
    private String totalPriceInChinese;

    @RpcModelProperty(value = "科长(对宁波二院：出库单对应的订单对应的采购申请单的二级审批人姓名）")
    private String sectionChief;

    @RpcModelProperty(value = "出库日期，时间戳格式")
    private Long outWarehouseTime;

    @RpcModelProperty(value = "出库单号对应条形码")
    private String exitNoBarcode;
    
    @RpcModelProperty(value = "出库部门名")
    private String departmentName;

    @RpcModelProperty(value = "出库申请单关联商品信息列表")
    private List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList;

    @RpcModelProperty(value = "出库科室/领用科室")
    private String exitDeptName;

    @RpcModelProperty("试剂类商品总金额")
    private String reagentTotalPrice;

    @RpcModelProperty("耗材、动物类商品总金额")
    private String consumablesAndAnimalTotalPrice;

    @RpcModelProperty(value = "出库单商品数量总和")
    private Integer totalProductQuantity;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public Integer getOutWarehouseApplicationId() {
        return outWarehouseApplicationId;
    }

    public void setOutWarehouseApplicationId(Integer outWarehouseApplicationId) {
        this.outWarehouseApplicationId = outWarehouseApplicationId;
    }

    public String getOutWarehouseApplicationNo() {
        return outWarehouseApplicationNo;
    }

    public void setOutWarehouseApplicationNo(String outWarehouseApplicationNo) {
        this.outWarehouseApplicationNo = outWarehouseApplicationNo;
    }

    public Long getOutWarehouseApplicationTime() {
        return outWarehouseApplicationTime;
    }

    public void setOutWarehouseApplicationTime(Long outWarehouseApplicationTime) {
        this.outWarehouseApplicationTime = outWarehouseApplicationTime;
    }

    public String getOutWarehouseApplicant() {
        return outWarehouseApplicant;
    }

    public void setOutWarehouseApplicant(String outWarehouseApplicant) {
        this.outWarehouseApplicant = outWarehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public void setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
    }

    public String getSectionChief() {
        return sectionChief;
    }

    public void setSectionChief(String sectionChief) {
        this.sectionChief = sectionChief;
    }

    public Long getOutWarehouseTime() {
        return outWarehouseTime;
    }

    public void setOutWarehouseTime(Long outWarehouseTime) {
        this.outWarehouseTime = outWarehouseTime;
    }


    public String getExitNoBarcode() {
        return exitNoBarcode;
    }

    public void setExitNoBarcode(String exitNoBarcode) {
        this.exitNoBarcode = exitNoBarcode;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public List<WarehouseProductPrintDataDTO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public String getExitDeptName() {
        return exitDeptName;
    }

    public void setExitDeptName(String exitDeptName) {
        this.exitDeptName = exitDeptName;
    }

    public String getReagentTotalPrice() {
        return reagentTotalPrice;
    }

    public OutWarehousePrintDataDTO setReagentTotalPrice(String reagentTotalPrice) {
        this.reagentTotalPrice = reagentTotalPrice;
        return this;
    }

    public String getConsumablesAndAnimalTotalPrice() {
        return consumablesAndAnimalTotalPrice;
    }

    public OutWarehousePrintDataDTO setConsumablesAndAnimalTotalPrice(String consumablesAndAnimalTotalPrice) {
        this.consumablesAndAnimalTotalPrice = consumablesAndAnimalTotalPrice;
        return this;
    }

    public Integer getTotalProductQuantity() {
        return totalProductQuantity;
    }

    public OutWarehousePrintDataDTO setTotalProductQuantity(Integer totalProductQuantity) {
        this.totalProductQuantity = totalProductQuantity;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OutWarehousePrintDataDTO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("orderSpecies=" + orderSpecies)
                .add("outWarehouseApplicationId=" + outWarehouseApplicationId)
                .add("outWarehouseApplicationNo='" + outWarehouseApplicationNo + "'")
                .add("outWarehouseApplicationTime=" + outWarehouseApplicationTime)
                .add("outWarehouseApplicant='" + outWarehouseApplicant + "'")
                .add("warehouseId=" + warehouseId)
                .add("warehouseName='" + warehouseName + "'")
                .add("status=" + status)
                .add("statusName='" + statusName + "'")
                .add("totalPrice='" + totalPrice + "'")
                .add("totalPriceInChinese='" + totalPriceInChinese + "'")
                .add("sectionChief='" + sectionChief + "'")
                .add("outWarehouseTime=" + outWarehouseTime)
                .add("exitNoBarcode='" + exitNoBarcode + "'")
                .add("departmentName='" + departmentName + "'")
                .add("warehouseProductInfoVOList=" + warehouseProductInfoVOList)
                .add("exitDeptName='" + exitDeptName + "'")
                .add("reagentTotalPrice='" + reagentTotalPrice + "'")
                .add("consumablesAndAnimalTotalPrice='" + consumablesAndAnimalTotalPrice + "'")
                .add("totalProductQuantity=" + totalProductQuantity)
                .toString();
    }
}
