package com.ruijing.store.order.base.core.model;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
@RpcModel(value="com.ruijing.store.order.base.pojo.OrderDetailDO")
public class OrderDetailDO implements Serializable {
    @RpcModelProperty(value="null")
    private Integer id;

    /**
    * 订单id
    */
    @RpcModelProperty(value="订单id")
    private Integer fmasterid;

    /**
    * 招标日期
    */
    @RpcModelProperty(value="招标日期")
    private Date fbiddate;

    /**
    * 订单b编号
    */
    @RpcModelProperty(value="订单b编号")
    private String fdetailno;

    @RpcModelProperty(value="null")
    private Integer categoryid;

    /**
    * 商品分类
    */
    @RpcModelProperty(value="商品分类")
    private String fclassification;

    /**
    * SPU，即商品下的唯一编码。规格不同，SPU也相同。
    */
    @RpcModelProperty(value="SPU,同一种商品下的唯一编码（规格不同，SPU也相同）")
    private String fgoodcode;

    /**
     * 原货号，SKU，平台编码。同一个商品在不同规格下的唯一编码
     */
    @RpcModelProperty("SKU,平台编码。同一个商品在不同规格下的唯一编码")
    private String productCode;

    /**
    * 商品名称
    */
    @RpcModelProperty(value="商品名称")
    private String fgoodname;

    /**
    * 参考品牌
    */
    @RpcModelProperty(value="参考品牌")
    private String fbrand;

    /**
    * 规格
    */
    @RpcModelProperty(value="规格")
    private String fspec;

    /**
    * 单位
    */
    @RpcModelProperty(value="单位")
    private String funit;

    /**
    * 数量
    */
    @RpcModelProperty(value="数量")
    private BigDecimal fquantity;

    /**
    * 招标价格
    */
    @RpcModelProperty(value="招标价格")
    private BigDecimal fbidprice;

    /**
    * 招标总价格
    */
    @RpcModelProperty(value="招标总价格")
    private BigDecimal fbidamount;

    /**
    * 图片位置
    */
    @RpcModelProperty(value="图片位置")
    private String fpicpath;

    @RpcModelProperty(value="null")
    private BigDecimal fremainquantity;

    @RpcModelProperty(value="null")
    private Integer fbrandid;

    @RpcModelProperty(value="null")
    private Integer tsuppmerpassid;

    @RpcModelProperty(value="null")
    private BigDecimal fcancelquantity;

    @RpcModelProperty(value="null")
    private BigDecimal fcancelamount;

    @RpcModelProperty(value="商品ID")
    private Long productSn;

    /**
    * 退货状态
    */
    @RpcModelProperty(value="退货状态")
    private Integer returnStatus;

    /**
    * 退货金额
    */
    @RpcModelProperty(value="退货金额")
    private Double returnAmount;

    /**
    * 商品项 原价
    */
    @RpcModelProperty(value="商品项 原价")
    private BigDecimal originalAmount;

    /**
    * 商品单价
    */
    @RpcModelProperty(value="商品单价")
    private BigDecimal originalPrice;

    /**
    * 是否修改价格
    */
    @RpcModelProperty(value="是否修改价格")
    private Boolean modifyPrice;

    /**
    * 货期
    */
    @RpcModelProperty(value="货期")
    private Integer deliveryTime;

    /**
    * 优惠券  商品项余额
    */
    @RpcModelProperty(value="优惠券  商品项余额")
    private BigDecimal remainderPrice;

    /**
    * 协议价
    */
    @RpcModelProperty(value="协议价")
    private BigDecimal negotiatedPrice;

    /**
    * 中大二级分类id
    */
    @RpcModelProperty(value="中大二级分类id")
    private Integer sysuCategoryId;

    /**
     * 采购目录ID
     */
    @RpcModelProperty(value="采购目录ID")
    private Integer categoryDirectoryId;

    /**
    * 更新时间
    */
    @RpcModelProperty(value="更新时间")
    private Date updateTime;

    @RpcModelProperty(value="null")
    private BigDecimal carryFee;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    private String firstCategoryName;

    /**
     * 二级分类id
     */
    private Integer secondCategoryId;

    /**
     * 二级分类名称
     */
    private String secondCategoryName;

    /**
     * 报账类型
     *
     * tag_type = 0;
     * tag_value  com.reagent.tags.global.enums.FeeType
     */
    private String feeTypeTag;

    /**
     * 分类标签
     * tag_type = 1;
     * com.reagent.tags.global.enums.FirstTierCategory
     */
    private String categoryTag;

    /**
     * 危化品标签Id/生猪品种id
     * {@link com.ruijing.shop.goods.api.enums.DangerousTypeEnum}
     * 字段含义取决于订单类型 {@link com.ruijing.store.order.api.base.enums.OrderTypeEnum}
     * 当订单类型为0/1/2时 代表危化品标签Id
     * 当订单类型为3时 代表生猪品种id
     */
    private Integer dangerousTypeId;

    /**
     * 危化品标签名称/生猪品种名称
     * {@link com.ruijing.shop.goods.api.enums.DangerousTypeEnum}
     * 字段含义取决于 {@link com.ruijing.store.order.api.base.enums.OrderTypeEnum}
     * 当订单类型为0/1/2时 代表危化品标签名称
     * 当订单类型为3时 代表生猪品种名称
     */
    private String dangerousTypeName;

    /**
     * 管制品类型Id
     */
    private Integer regulatoryTypeId;

    /**
     *管制品类型名称
     */
    private String regulatoryTypeName;

    /**
     *Cas号
     */
    private String casno;

    /**
     *供应商Id
     */
    private Integer suppId;

    /**
     *供应商名称
     */
    private String suppName;

    /**
     * 供应商编号
     */
    private String suppCode;

    /**
     * 品牌英文名
     */
    private String brandEname;

    private static final long serialVersionUID = 1L;

    public Integer getCategoryDirectoryId() {
        return categoryDirectoryId;
    }

    public void setCategoryDirectoryId(Integer categoryDirectoryId) {
        this.categoryDirectoryId = categoryDirectoryId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFmasterid() {
        return fmasterid;
    }

    public void setFmasterid(Integer fmasterid) {
        this.fmasterid = fmasterid;
    }

    public Date getFbiddate() {
        return fbiddate;
    }

    public void setFbiddate(Date fbiddate) {
        this.fbiddate = fbiddate;
    }

    public String getFdetailno() {
        return fdetailno;
    }

    public void setFdetailno(String fdetailno) {
        this.fdetailno = fdetailno;
    }

    public Integer getCategoryid() {
        return categoryid;
    }

    public void setCategoryid(Integer categoryid) {
        this.categoryid = categoryid;
    }

    public String getFclassification() {
        return fclassification;
    }

    public void setFclassification(String fclassification) {
        this.fclassification = fclassification;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public void setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public String getFbrand() {
        return fbrand;
    }

    public void setFbrand(String fbrand) {
        this.fbrand = fbrand;
    }

    public String getFspec() {
        return fspec;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public String getFunit() {
        return funit;
    }

    public void setFunit(String funit) {
        this.funit = funit;
    }

    public BigDecimal getFquantity() {
        return fquantity;
    }

    public void setFquantity(BigDecimal fquantity) {
        this.fquantity = fquantity;
    }

    public BigDecimal getFbidprice() {
        return fbidprice;
    }

    public void setFbidprice(BigDecimal fbidprice) {
        this.fbidprice = fbidprice;
    }

    public BigDecimal getFbidamount() {
        return fbidamount;
    }

    public void setFbidamount(BigDecimal fbidamount) {
        this.fbidamount = fbidamount;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public void setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
    }

    public BigDecimal getFremainquantity() {
        return fremainquantity;
    }

    public void setFremainquantity(BigDecimal fremainquantity) {
        this.fremainquantity = fremainquantity;
    }

    public Integer getFbrandid() {
        return fbrandid;
    }

    public void setFbrandid(Integer fbrandid) {
        this.fbrandid = fbrandid;
    }

    public Integer getTsuppmerpassid() {
        return tsuppmerpassid;
    }

    public void setTsuppmerpassid(Integer tsuppmerpassid) {
        this.tsuppmerpassid = tsuppmerpassid;
    }

    public BigDecimal getFcancelquantity() {
        return fcancelquantity;
    }

    public void setFcancelquantity(BigDecimal fcancelquantity) {
        this.fcancelquantity = fcancelquantity;
    }

    public BigDecimal getFcancelamount() {
        return fcancelamount;
    }

    public void setFcancelamount(BigDecimal fcancelamount) {
        this.fcancelamount = fcancelamount;
    }

    public Long getProductSn() {
        return productSn;
    }

    public void setProductSn(Long productSn) {
        this.productSn = productSn;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Boolean getModifyPrice() {
        return modifyPrice;
    }

    public void setModifyPrice(Boolean modifyPrice) {
        this.modifyPrice = modifyPrice;
    }

    public Integer getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Integer deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }

    public BigDecimal getNegotiatedPrice() {
        return negotiatedPrice;
    }

    public void setNegotiatedPrice(BigDecimal negotiatedPrice) {
        this.negotiatedPrice = negotiatedPrice;
    }

    public Integer getSysuCategoryId() {
        return sysuCategoryId;
    }

    public void setSysuCategoryId(Integer sysuCategoryId) {
        this.sysuCategoryId = sysuCategoryId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public void setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public void setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public Integer getDangerousTypeId() {
        return dangerousTypeId;
    }

    public void setDangerousTypeId(Integer dangerousTypeId) {
        this.dangerousTypeId = dangerousTypeId;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getRegulatoryTypeId() {
        return regulatoryTypeId;
    }

    public void setRegulatoryTypeId(Integer regulatoryTypeId) {
        this.regulatoryTypeId = regulatoryTypeId;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public void setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
    }

    public String getCasno() {
        return casno;
    }

    public void setCasno(String casno) {
        this.casno = casno;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getBrandEname() {
        return brandEname;
    }

    public void setBrandEname(String brandEname) {
        this.brandEname = brandEname;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailDO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("fmasterid=" + fmasterid)
                .add("fbiddate=" + fbiddate)
                .add("fdetailno='" + fdetailno + "'")
                .add("categoryid=" + categoryid)
                .add("fclassification='" + fclassification + "'")
                .add("fgoodcode='" + fgoodcode + "'")
                .add("productCode='" + productCode + "'")
                .add("fgoodname='" + fgoodname + "'")
                .add("fbrand='" + fbrand + "'")
                .add("fspec='" + fspec + "'")
                .add("funit='" + funit + "'")
                .add("fquantity=" + fquantity)
                .add("fbidprice=" + fbidprice)
                .add("fbidamount=" + fbidamount)
                .add("fpicpath='" + fpicpath + "'")
                .add("fremainquantity=" + fremainquantity)
                .add("fbrandid=" + fbrandid)
                .add("tsuppmerpassid=" + tsuppmerpassid)
                .add("fcancelquantity=" + fcancelquantity)
                .add("fcancelamount=" + fcancelamount)
                .add("productSn=" + productSn)
                .add("returnStatus=" + returnStatus)
                .add("returnAmount=" + returnAmount)
                .add("originalAmount=" + originalAmount)
                .add("originalPrice=" + originalPrice)
                .add("modifyPrice=" + modifyPrice)
                .add("deliveryTime=" + deliveryTime)
                .add("remainderPrice=" + remainderPrice)
                .add("negotiatedPrice=" + negotiatedPrice)
                .add("sysuCategoryId=" + sysuCategoryId)
                .add("categoryDirectoryId=" + categoryDirectoryId)
                .add("updateTime=" + updateTime)
                .add("carryFee=" + carryFee)
                .add("firstCategoryId=" + firstCategoryId)
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("feeTypeTag='" + feeTypeTag + "'")
                .add("categoryTag='" + categoryTag + "'")
                .add("dangerousTypeId=" + dangerousTypeId)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .add("regulatoryTypeId=" + regulatoryTypeId)
                .add("regulatoryTypeName='" + regulatoryTypeName + "'")
                .add("casno='" + casno + "'")
                .add("suppId=" + suppId)
                .add("suppName='" + suppName + "'")
                .add("suppCode='" + suppCode + "'")
                .add("brandEname='" + brandEname + "'")
                .toString();
    }
}