package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.ApplicationSaveDTO;
import com.ruijing.store.apply.dto.application.*;
import com.ruijing.store.apply.dto.application.manage.ApplyManageRequestDTO;
import com.ruijing.store.apply.dto.offline.OfflineExtraDTO;
import com.ruijing.store.apply.dto.offline.OfflineProductDTO;
import com.ruijing.store.apply.enums.offline.ExtraTypeEnum;
import com.ruijing.store.apply.service.application.*;
import com.ruijing.store.apply.service.offline.ApplicationOfflineService;
import com.ruijing.store.apply.service.offline.ApplyOfflineProductService;
import com.ruijing.store.approval.api.dto.ApprovalTaskDTO;
import com.ruijing.store.approval.api.enums.ApprovalDockingTypeEnum;
import com.ruijing.store.approval.api.service.ApprovalTaskService;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.filing.dto.ProcurementApplicationDTO;
import com.ruijing.store.filing.service.ProcurementApplicationRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采购申请基础接口client
 */
@ServiceClient
public class ApplicationBaseClient {

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplicationBaseService applicationBaseService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplicationOfflineService applicationOfflineService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplyRefSuppBusinessService applyRefSuppBusinessService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplyOfflineProductService applyOfflineProductService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplyMasterExtensionService applyMasterExtensionService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplyManageRpcService applyManageRpcService;

    @MSharpReference(remoteAppkey = "store-approval-service")
    private ApprovalTaskService approvalTaskService;

    @MSharpReference(remoteAppkey = "store-apply-service")
    private ApplyProductSnapshotService applyProductSnapshotService;

    @MSharpReference(remoteAppkey = "store-filing-service")
    private ProcurementApplicationRpcService procurementApplicationRpcService;

    public Integer saveApplicationMaster(Integer applicationId, Integer status, String applyStatusName, Integer pushStatus) {
        ApplicationSaveDTO param = new ApplicationSaveDTO();
        List<ApplicationMasterDTO> applicationMasterDTOS = new ArrayList<>();
        ApplicationMasterDTO item = new ApplicationMasterDTO();
        item.setId(applicationId.longValue());
        item.setApplyStatusName(applyStatusName);
        item.setStatus(status);
        item.setPushStatus(pushStatus);
        applicationMasterDTOS.add(item);
        param.setApplications(applicationMasterDTOS);
        return this.saveApplicationMaster(param);
    }

    @ServiceLog(description = "更新采购申请单详情记录", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Integer saveApplicationMaster(ApplicationSaveDTO param) {
        Preconditions.notNull(param, "更新采购申请单失败！参数为空！");
        RemoteResponse<List<ApplicationMasterDTO>> response = applicationBaseService.saveApplicationMaster(param);
        Preconditions.isTrue(response.isSuccess(), "更新采购申请单失败！" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData().size();
    }

    @ServiceLog(description = "更新采购申请单详情记录", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Integer saveApplicationDetail(ApplicationSaveDTO param) {
        Preconditions.notNull(param, "更新采购申请单失败！参数为空！");
        RemoteResponse<List<ApplicationDetailDTO>> response = applicationBaseService.saveApplicationDetail(param);
        Preconditions.isTrue(response.isSuccess(), "更新采购申请单失败！" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData().size();
    }

    @ServiceLog(description = "查询采购申请单主表记录", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplicationMasterDTO> findByMasterId(ApplicationQueryDTO param) {
        BusinessErrUtil.notNull(param, ExecptionMessageEnum.QUERY_PURCHASE_REQUISITION_FAILED);
        param.setBusiness(ApplicationQueryDTO.BusinessEnum.APPLY_ID);
        RemoteResponse<List<ApplicationMasterDTO>> response = applicationBaseService.listApplicationMaster(param);
        Assert.isTrue(response.isSuccess(), "查询失败！" + JsonUtils.toJson(response));
        Preconditions.notNull(response.getData(), "查询失败，返回结果为null！" + JsonUtils.toJson(response));
        return response.getData();
    }

    @ServiceLog(description = "查找采购申请单详情记录", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplicationDetailDTO> findDetailByMasterId(ApplicationQueryDTO param) {
        BusinessErrUtil.notNull(param, "查询采购申请单详情失败！参数为空！");
        param.setBusiness(ApplicationQueryDTO.BusinessEnum.APPLY_ID);
        RemoteResponse<List<ApplicationDetailDTO>> response = applicationBaseService.listApplicationDetail(param);
        Assert.isTrue(response.isSuccess(), "查询失败！" + JsonUtils.toJson(response));

        return response.getData();
    }

    /**
     * @param applicationIdList
     * @return
     * @description: 查找采购线下单额外信息
     */
    @ServiceLog(description = "查找采购线下单额外信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OfflineExtraDTO> findOfflineByAppIdList(List<Integer> applicationIdList) {
        Preconditions.notEmpty(applicationIdList, "查找线下单额外信息入参不可为空");
        RemoteResponse<List<OfflineExtraDTO>> offlineExtraResp = applicationOfflineService.listByApplicationIdsAndType(applicationIdList, ExtraTypeEnum.OFFLINE_PRODUCT.getValue());
        Preconditions.notNull(offlineExtraResp, "查找采购线下单额外信息接口异常，请重试或联系客服");
        Preconditions.isTrue(offlineExtraResp.isSuccess(), "查找采购线下单额外信息失败，请重试或联系客服");
        return offlineExtraResp.getData();
    }

    /**
     * @param suppId
     * @param applicationId
     * @return Integer procurementChannelId
     * @description: 通过供应商id，采购单id查询采购途径id（线下单）
     * @date: 2021/2/5 15:54
     * @author: zengyanru
     */
    @ServiceLog(description = "通过供应商id，采购单id查询采购途径id（线下单）", serviceType = ServiceType.COMMON_SERVICE)
    public Integer findProcureChannelBySuppIdAndAppId(Integer suppId, Integer applicationId) {
        RemoteResponse<List<ApplyRefSuppBusinessDTO>> listRemoteResponse = applyRefSuppBusinessService.listBySuppIdsAndApplicationIds(New.list(suppId), New.list(applicationId));
        Preconditions.notNull(listRemoteResponse, "获取采购单采购渠道出错，采购单id：" + applicationId + "，供应商id：" + suppId);
        Preconditions.isTrue(listRemoteResponse.isSuccess(), "获取采购单采购渠道出错，采购单id：" + applicationId + "，供应商id：" + suppId);
        List<ApplyRefSuppBusinessDTO> applyChannelInfoList = listRemoteResponse.getData();
        Preconditions.notEmpty(applyChannelInfoList, "未找到采购途径，采购单id：" + applicationId + "，供应商id：" + suppId);

        return applyChannelInfoList.get(0).getProcurementChannelId();
    }

    /**
     * 通过供应商id列表，采购单id列表查询线下采购供应商相关信息（线下单）
     * @param suppIdList
     * @param applyIdList
     * @return
     */
    @ServiceLog(description = "通过供应商id列表，采购单id列表查询线下采购供应商相关信息（线下单）", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplyRefSuppBusinessDTO> findApplyRefSuppBusinessList(List<Integer> suppIdList, List<Integer> applyIdList) {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(suppIdList) && CollectionUtils.isNotEmpty(applyIdList), "通过供应商id列表，采购单id列表查询线下采购供应商相关信息入参不可为空");
        RemoteResponse<List<ApplyRefSuppBusinessDTO>> response = applyRefSuppBusinessService.listBySuppIdsAndApplicationIds(suppIdList, applyIdList);
        Preconditions.notNull(response, "获取采购单采购渠道出错，采购单id列表：" + applyIdList + "，供应商id列表：" + suppIdList);
        Preconditions.isTrue(response.isSuccess(), "获取采购单采购渠道出错，采购单id：" + applyIdList + "，供应商id：" + applyIdList + ", " + response.getMsg());
        return response.getData();
    }

    /**
     * @param ids
     * @return
     * @description: 查找采购线下单商品信息
     */
    @ServiceLog(description = "查找采购线下单商品信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OfflineProductDTO> findApplyOfflineProductByIds(List<Long> ids) {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(ids), "查找采购线下单商品信息入参不可为空");
        RemoteResponse<List<OfflineProductDTO>> response = applyOfflineProductService.listByIds(ids);
        Preconditions.notNull(response, "查找采购线下单商品信息接口异常，请重试或联系客服");
        Preconditions.isTrue(response.isSuccess(), "查找采购线下单商品信息失败，请重试或联系客服");
        return response.getData();
    }

    @ServiceLog(description = "获取采购单拓展信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplyMasterExtensionDTO> findExtensionByApplicationId(ApplyMasterExtensionQueryDTO request) {
        Assert.notNull(request, "查询采购申请单详情失败！参数为空！");
        RemoteResponse<List<ApplyMasterExtensionDTO>> response = applyMasterExtensionService.listByApplyIds(request);
        Assert.isTrue(response.isSuccess(), "查询失败！" + JsonUtils.toJson(response));

        return response.getData();
    }

    @ServiceLog(description = "查询采购单是否预占-江苏中医附院", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApplyMasterExtensionDTO> occupy(ApplyMasterExtensionQueryDTO request) {
        Preconditions.notNull(request, "查询采购单是否预占！参数为空！");
        RemoteResponse<List<ApplyMasterExtensionDTO>> response = applyMasterExtensionService.occupy(request);
        Preconditions.isTrue(response.isSuccess(), "查询失败！" + JsonUtils.toJson(response));
        return response.getData();
    }

    public String findPermitCardByApplicationId(Integer applyId) {
        ApplyMasterExtensionQueryDTO request = new ApplyMasterExtensionQueryDTO();
        request.setApplyIds(Arrays.asList(applyId));
        List<ApplyMasterExtensionDTO> permitCardByApplicationId = this.findExtensionByApplicationId(request);
        if (CollectionUtils.isEmpty(permitCardByApplicationId)) {
            return StringUtils.EMPTY;
        }
        return permitCardByApplicationId.get(0).getOneCardPass();
    }

    @ServiceLog(description = "同一商品采购金额统计(根据商品名)", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean updateApplyManageProductUsage(ApplyManageRequestDTO applyManageRequestDTO) {
        Preconditions.notNull(applyManageRequestDTO, "同一商品采购金额统计(根据商品名)失败！参数为空！");
        RemoteResponse<Boolean> response = applyManageRpcService.updateApplyManageProductUsage(applyManageRequestDTO);
        Preconditions.notNull(response, "同一商品采购金额统计(根据商品名)接口异常，请重试或联系客服");
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * @param applyId
     * @param withDetail 采购商品详情配置 true，连同采购商品详情一起查询出来（会有一点性能损耗)，默认不查采购商品详情
     * @return
     */
    @ServiceLog(description = "根据采购单id获取对应采购单", operationType = OperationType.READ, serviceType = ServiceType.COMMON_SERVICE)
    public ApplicationMasterDTO getApplicationMasterByApplyId(Integer applyId, Boolean withDetail) {
        Preconditions.notNull(applyId, "采购单id为空");
        Preconditions.notNull(withDetail, "采购商品详情配置为空");
        ApplicationQueryDTO query = new ApplicationQueryDTO();
        query.setApplyIds(Collections.singletonList(applyId.longValue()));
        query.setWithDetail(withDetail);
        ApplicationMasterDTO result = null;
        RemoteResponse<List<ApplicationMasterDTO>> response = applicationBaseService.listApplicationMaster(query);
        Preconditions.notNull(response, "查找采购单信息接口异常，请重试或联系客服");
        Preconditions.isTrue(response.isSuccess(), "查找采购单信息失败，请重试或联系客服");

        if (CollectionUtils.isNotEmpty(response.getData())) {
            result = response.getData().get(0);
        }
        return result;
    }

    /**
     * 通过采购单号获取审批流id
     * @param purchaseIdList
     * @return
     */
    @ServiceLog(description = "通过采购单号获取审批流id", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApprovalTaskDTO> getAppInfoWithFlowId(List<Long> purchaseIdList) {
        RemoteResponse<List<ApprovalTaskDTO>> response = approvalTaskService.queryTasksByDockIds(ApprovalDockingTypeEnum.PURCHASE_APPLY, purchaseIdList);
        String errorMsg = response == null ? null : response.getMsg();
        Preconditions.isTrue(response != null && response.isSuccess(), "通过采购单号获取审批流id失败," + errorMsg + "，purchaseIdList="+purchaseIdList);
        return response.getData();
    }

    /**
     * 通过采购单id获取线下单供应商信息
     * @param appIdList     采购单id
     * @return              线下供应商信息
     */
    @ServiceLog(description = "通过采购单id获取线下单供应商信息", serviceType = ServiceType.RPC_CLIENT)
    public List<ApplyRefSuppBusinessDTO> getOfflineSupplierByAppIdList(List<Integer> appIdList) {
        RemoteResponse<List<ApplyRefSuppBusinessDTO>> response = applyRefSuppBusinessService.listBySuppIdsAndApplicationIds(null, appIdList);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }
    /**
     * 根据采购单id查询采购单
     * @param buyAppIdList
     * @return
     */
    public List<ApplicationMasterDTO> findByMasterId(List<Integer> buyAppIdList) {
        if (CollectionUtils.isEmpty(buyAppIdList)) {
            return Collections.emptyList();
        }
        
        // 分批处理，每批200个
        List<ApplicationMasterDTO> resultList = New.list();
        List<List<Integer>> batchLists = Lists.partition(buyAppIdList, 200);
        for (List<Integer> batchIds : batchLists) {
            List<Long> sourceIdList = batchIds.stream().map(Integer::longValue).collect(Collectors.toList());
            ApplicationQueryDTO query = new ApplicationQueryDTO();
            query.setApplyIds(sourceIdList);
            List<ApplicationMasterDTO> batchResult = this.findByMasterId(query);
            if (CollectionUtils.isNotEmpty(batchResult)) {
                resultList.addAll(batchResult);
            }
        }
        return resultList;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public ApplyProductSnapshotDTO getProductSnapshot(Integer buyAppId, Long productId){
        QueryApplyProductSnapshotRequestDTO queryApplyProductSnapshotRequestDTO = new QueryApplyProductSnapshotRequestDTO();
        queryApplyProductSnapshotRequestDTO.setApplicationId(buyAppId);
        queryApplyProductSnapshotRequestDTO.setProductIds(New.list(productId));
        RemoteResponse<List<ApplyProductSnapshotDTO>> response =  applyProductSnapshotService.listProductSnapshot(queryApplyProductSnapshotRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if(CollectionUtils.isEmpty(response.getData())){
            return null;
        }
        return response.getData().get(0);
    }

    @ServiceLog(operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT, description = "通过采购单id查询预审批单")
    public ProcurementApplicationDTO getProcurementByAppId(Integer appId){
        ProcurementApplicationDTO procurementApplicationDTO = new ProcurementApplicationDTO();
        procurementApplicationDTO.setApplicationId(appId);
        RemoteResponse<ProcurementApplicationDTO> response = procurementApplicationRpcService.getByApplicationId(procurementApplicationDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
