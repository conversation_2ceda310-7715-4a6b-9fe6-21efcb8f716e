package com.ruijing.store.order.business.service;

import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.SyncStatementStatusDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;
import java.util.Map;

/**
 * 订单结算service
 */
public interface OrderStatementService {
    /**
     * 订单结算核心代码
     *
     * @param orderSnapshot   订单快照信息
     * @param operatorId      操作人id
     * @param operatorName    操作人名称
     * @param isUsedStatement 是否使用结算系统
     * @param inventoryStatus 入库状态
     */
    void orderStatementCore(OrderMasterDO orderSnapshot, Integer operatorId,
                            String operatorName, boolean isUsedStatement, Integer inventoryStatus);

    /**
     * 发起结算，推到结算中
     *
     * @param orderSnapshot   订单快照信息
     * @param operatorId      操作人id
     * @param operatorName    操作人名称
     * @param inventoryStatus 入库状态
     */
    void createStatement(OrderMasterDO orderSnapshot, Integer operatorId,
                         String operatorName, Integer inventoryStatus);

    /**
     * 用于同步订单结算状态，状态不一致才修改，防止刷新表更新时间
     *
     * @param syncStatementStatusDTOS 批量更新结算状态DTO（订单id、结算状态）
     */
    Integer batchUpdateStatementStatus(List<SyncStatementStatusDTO> syncStatementStatusDTOS);


    /**
     * 判断是否使用结算系统
     * @param receiptConfigMap 结算配置
     * @param orderMasterDO 订单数据
     * @return 是否使用
     */
    boolean isUseStatement(Map<String, String> receiptConfigMap, OrderMasterDO orderMasterDO);
}
