package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2021/11/15 11:34
 */
public enum OrderAcceptButtonEnum {

    NOT_SHOW(0, "不显示验收按钮"),

    SHOW_NORMAL(1, "显示验收按钮，正常点击"),

    SHOW_GRAY(2, "显示验收按钮，置灰");

    private Integer value;

    private String desc;

    OrderAcceptButtonEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static final OrderAcceptButtonEnum getByValue(Integer value) {
        for (OrderAcceptButtonEnum curEnum : OrderAcceptButtonEnum.values()) {
            if (curEnum.value.equals(value)){
                return curEnum;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public OrderAcceptButtonEnum setValue(Integer value) {
        this.value = value;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public OrderAcceptButtonEnum setDesc(String desc) {
        this.desc = desc;
        return this;
    }
}
