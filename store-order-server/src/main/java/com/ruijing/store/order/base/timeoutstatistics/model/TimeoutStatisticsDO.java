package com.ruijing.store.order.base.timeoutstatistics.model;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class TimeoutStatisticsDO {
    /**
    * id
    */
    private Long id;

    /**
    * 医院ID
    */
    private Integer orgId;

    /**
    * 部门id
    */
    private Integer depId;

    /**
    * 统计类型 1-结算 2-验收
    */
    private Integer type;

    /**
    * 统计的个数
    */
    private Integer amount;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 当时统计时配置的天数
    */
    private Integer oldConfigDay;

    /**
    * 当时统计时配置的张数
    */
    private Integer oldConfigAmount;

    public TimeoutStatisticsDO() {
    }

    public TimeoutStatisticsDO(Integer orgId, Integer depId, Integer type, Integer amount, Date creationTime, Integer oldConfigDay, Integer oldConfigAmount) {
        this.orgId = orgId;
        this.depId = depId;
        this.type = type;
        this.amount = amount;
        this.creationTime = creationTime;
        this.oldConfigDay = oldConfigDay;
        this.oldConfigAmount = oldConfigAmount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getDepId() {
        return depId;
    }

    public void setDepId(Integer depId) {
        this.depId = depId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getOldConfigDay() {
        return oldConfigDay;
    }

    public void setOldConfigDay(Integer oldConfigDay) {
        this.oldConfigDay = oldConfigDay;
    }

    public Integer getOldConfigAmount() {
        return oldConfigAmount;
    }

    public void setOldConfigAmount(Integer oldConfigAmount) {
        this.oldConfigAmount = oldConfigAmount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orgId=").append(orgId);
        sb.append(", depId=").append(depId);
        sb.append(", type=").append(type);
        sb.append(", amount=").append(amount);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", oldConfigDay=").append(oldConfigDay);
        sb.append(", oldConfigAmount=").append(oldConfigAmount);
        sb.append("]");
        return sb.toString();
    }
}