package com.ruijing.store.order.base.core.model;

import java.math.BigDecimal;
import java.util.StringJoiner;


public class SimpleDetailInfoDO {
    private Integer id;
    private Integer fmasterid;
    private String fspec;
    private BigDecimal fbidprice;
    private String fbrand;
    private Integer fbrandid;
    private String fgoodname;

    /**
     * SPU，即商品+规格组合下的唯一编码
     */
    private String fgoodcode;

    /**
     * 原货号，平台编码。即商品纬度下的唯一编码（同规格时仍相同）
     */
    private String productCode;

    private String fpicpath;
    private Integer returnStatus;
    private Double returnAmount;
    private Long productSn;
    private Integer dangerousTypeId;
    private String dangerousTypeName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFmasterid() {
        return fmasterid;
    }

    public void setFmasterid(Integer fmasterid) {
        this.fmasterid = fmasterid;
    }

    public String getFspec() {
        return fspec;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public BigDecimal getFbidprice() {
        return fbidprice;
    }

    public void setFbidprice(BigDecimal fbidprice) {
        this.fbidprice = fbidprice;
    }

    public String getFbrand() {
        return fbrand;
    }

    public void setFbrand(String fbrand) {
        this.fbrand = fbrand;
    }

    public Integer getFbrandid() {
        return fbrandid;
    }

    public void setFbrandid(Integer fbrandid) {
        this.fbrandid = fbrandid;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public void setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public void setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Long getProductSn() {
        return productSn;
    }

    public void setProductSn(Long productSn) {
        this.productSn = productSn;
    }

    public Integer getDangerousTypeId() {
        return dangerousTypeId;
    }

    public SimpleDetailInfoDO setDangerousTypeId(Integer dangerousTypeId) {
        this.dangerousTypeId = dangerousTypeId;
        return this;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public SimpleDetailInfoDO setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", SimpleDetailInfoDO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("fmasterid=" + fmasterid)
                .add("fspec='" + fspec + "'")
                .add("fbidprice=" + fbidprice)
                .add("fbrand='" + fbrand + "'")
                .add("fbrandid=" + fbrandid)
                .add("fgoodname='" + fgoodname + "'")
                .add("fgoodcode='" + fgoodcode + "'")
                .add("productCode='" + productCode + "'")
                .add("fpicpath='" + fpicpath + "'")
                .add("returnStatus=" + returnStatus)
                .add("returnAmount=" + returnAmount)
                .add("productSn=" + productSn)
                .add("dangerousTypeId=" + dangerousTypeId)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .toString();
    }
}
