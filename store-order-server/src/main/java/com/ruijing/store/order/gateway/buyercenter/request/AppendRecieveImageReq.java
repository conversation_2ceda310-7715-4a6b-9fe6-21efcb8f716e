package com.ruijing.store.order.gateway.buyercenter.request;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/4/7 0007 17:25
 * @Version 1.0
 * @Desc:描述
 */
public class AppendRecieveImageReq {
    /**
     * 图片路径集合
     */
    private List<String> filePaths;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 图片id
     */
    private String imageId;

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public List<String> getFilePaths() {
        return filePaths;
    }

    public void setFilePaths(List<String> filePaths) {
        this.filePaths = filePaths;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
}
