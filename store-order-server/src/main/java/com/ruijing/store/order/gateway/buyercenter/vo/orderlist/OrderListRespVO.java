package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/11/17 15:24
 * @Description
 **/
@RpcModel(value = "订单管理-我的订单列表返回体")
public class OrderListRespVO implements Serializable {

    private static final long serialVersionUID = 3310521233336757548L;

    /**
     * 订单列表
     */
    @RpcModelProperty("订单列表")
    private List<OrderInfoVO> orderList;

    /**
     * 验收单打印权限
     */
    @RpcModelProperty("验收单打印权限")
    private Boolean acceptancePrint;

    /**。
     *拍照验收
     */
    @RpcModelProperty("拍照验收")
    private Boolean photoAcceptance;

    /**
     *总金额
     */
    @RpcModelProperty("总金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     *总页数
     */
    @RpcModelProperty("总页数")
    private Integer totalPages;

    @RpcModelProperty("测试订单数量")
    private Long testCount;

    public List<OrderInfoVO> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<OrderInfoVO> orderList) {
        this.orderList = orderList;
    }

    public Boolean getAcceptancePrint() {
        return acceptancePrint;
    }

    public void setAcceptancePrint(Boolean acceptancePrint) {
        this.acceptancePrint = acceptancePrint;
    }

    public Boolean getPhotoAcceptance() {
        return photoAcceptance;
    }

    public void setPhotoAcceptance(Boolean photoAcceptance) {
        this.photoAcceptance = photoAcceptance;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public OrderListRespVO() {}

    public OrderListRespVO(List<OrderInfoVO> orderList, int totalPage) {
        this.setOrderList(orderList);
        this.setTotalPages(totalPage);
    }

    public Long getTestCount() {
        return testCount;
    }

    public OrderListRespVO setTestCount(Long testCount) {
        this.testCount = testCount;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderListRespVO.class.getSimpleName() + "[", "]")
                .add("orderList=" + orderList)
                .add("acceptancePrint=" + acceptancePrint)
                .add("photoAcceptance=" + photoAcceptance)
                .add("totalAmount=" + totalAmount)
                .add("totalPages=" + totalPages)
                .add("testCount=" + testCount)
                .toString();
    }
}
