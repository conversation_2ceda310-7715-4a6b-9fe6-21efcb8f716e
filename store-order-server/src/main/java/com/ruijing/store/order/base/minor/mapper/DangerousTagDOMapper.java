package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 15:24
 * @Version 1.0
 * @Desc:描述
 */
public interface DangerousTagDOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DangerousTagDO record);

    int insertSelective(DangerousTagDO record);

    DangerousTagDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DangerousTagDO record);

    int updateByPrimaryKey(DangerousTagDO record);

    int batchInsert(@Param("list")List<DangerousTagDO> list);

    int insertList(@Param("list")List<DangerousTagDO> list);

    List<DangerousTagDO> selectByBusinessIdInAndBusinessType(@Param("businessIdCollection")Collection<String> businessIdCollection,@Param("businessType")Integer businessType);
}