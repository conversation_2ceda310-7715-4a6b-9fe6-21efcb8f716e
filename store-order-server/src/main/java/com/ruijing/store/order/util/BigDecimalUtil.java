package com.ruijing.store.order.util;

import java.math.BigDecimal;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2020/5/22 4:45 下午
 * @description: 数据经度处理工具
 */
public class BigDecimalUtil {

    private BigDecimalUtil(){

    }

    public static Double add(double v1 ,double v2){
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.add(b2).doubleValue();
    }

    public static Double subtract(double v1 ,double v2){
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.subtract(b2).doubleValue();
    }

    public static Double multiply(double v1 ,double v2){
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.multiply(b2).doubleValue();
    }

}
