package com.ruijing.store.order.base.core.enums;


/**
 * 电子签名枚举
 */
public enum ElectronicSignEnum {
    FORBID(0, "禁用"),
    CHOOSABLE(1, "选用"),
    MUSTUSE(2, "强制使用");

    private Integer type;
    private String description;

    ElectronicSignEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
