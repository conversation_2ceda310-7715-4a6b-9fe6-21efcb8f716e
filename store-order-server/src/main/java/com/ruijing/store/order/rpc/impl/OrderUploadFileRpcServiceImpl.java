package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.file.OrderUploadFileRpcService;
import com.ruijing.store.order.api.file.request.OrderDeleteUploadFileDTO;
import com.ruijing.store.order.api.file.request.OrderFileInfoParamDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.api.file.vo.UploadFileInfoDTO;
import com.ruijing.store.order.business.service.file.OrderUploadFileService;
import com.ruijing.store.order.gateway.file.request.OrderFileInfoRequestDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Name: OrderUploadFileRpcServiceImpl
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/8/21
 */
@MSharpService
public class OrderUploadFileRpcServiceImpl implements OrderUploadFileRpcService {

    @Resource
    private OrderUploadFileService orderUploadFileService;


    /**
     * 获取已上传的文件
     * @param request 获取用参数
     * @return 文件信息
     */
    @Override
    public RemoteResponse<List<UploadFileInfoDTO>> getUploadFileInfo(OrderFileInfoParamDTO request) {
        OrderFileInfoRequestDTO dto = new OrderFileInfoRequestDTO();
        dto.setOrderIdList(request.getOrderIdList());
        dto.setFileBusinessTypeList(request.getFileBusinessTypeList());
        List<UploadFileInfoVO> uploadFileInfoList = orderUploadFileService.getUploadFileInfoList(dto);
        List<UploadFileInfoDTO> list = uploadFileInfoList.stream().map(
                uploadFileInfoVO -> {
                    UploadFileInfoDTO info = new UploadFileInfoDTO();
                    info.setOrderId(uploadFileInfoVO.getOrderId());
                    info.setOrderNo(uploadFileInfoVO.getOrderNo());
                    info.setFileBusinessType(uploadFileInfoVO.getFileBusinessType());
                    info.setUrl(uploadFileInfoVO.getUrl());
                    info.setFileName(uploadFileInfoVO.getFileName());
                    info.setFileId(uploadFileInfoVO.getFileId());
                    return info;
                }
        ).collect(Collectors.toList());
        return RemoteResponse.<List<UploadFileInfoDTO>>custom().setSuccess().setData(list);
    }

    @Override
    public Boolean saveUploadFileInfo(OrderUploadFileDataRequestDTO request) {
        return orderUploadFileService.saveUploadFileInfo(request);
    }

    @Override
    public RemoteResponse<Boolean> deleteOrderFileByFileId(OrderDeleteUploadFileDTO orderDeleteUploadFileDTO) {
        return RemoteResponse.success(orderUploadFileService.deleteUploadFile(orderDeleteUploadFileDTO));
    }
}
