package com.ruijing.store.order.gateway.hms;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.enums.ExportFileTypeEnum;
import com.reagent.research.statement.api.enums.StatementAccessCodeEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.concurrent.ListenableThreadPoolExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderApprovalParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.service.OrderOperateLogService;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.base.excel.dto.OrderExportRequestDTO;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.CreateInvoiceDTO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoRespVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOperationLogVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.other.service.OrderInvoiceService;
import com.ruijing.store.order.rpc.client.OrderExportClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Objects;

@MSharpService(isGateway = "true")
@RpcApi(value = "hms网关服务",description = "hms网关服务")
@RpcMapping("/hms")
public class HmsOrderGWController {

    @Resource
    private OrderExportClient orderExportClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderExportService orderExportService;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderInvoiceService orderInvoiceService;

    @Resource
    private OrderApprovalService orderApprovalService;

    @Resource(name = "excelExportExecutor")
    private ListenableThreadPoolExecutor excelExportExecutor;

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private OrderOperateLogService orderOperateLogService;

    private final static String CAT_TYPE = "HmsOrderGWController";

    private static Logger logger = LoggerFactory.getLogger(HmsOrderGWController.class);


    /**
     * 查找订单操作日志
     * @param orderBasicParam
     * @return
     */
    @RpcMapping("/orderOperationLog")
    @RpcMethod("查找订单操作日志 /store/order/hms/orderOperationLog")
    public RemoteResponse<List<OrderOperationLogVO>> findOrderOperation(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParam) {
        List<OrderOperationLogVO> operationLogList = orderOperateLogService.orderOperationLog(orderBasicParam.getOrderId(), orderBasicParam.getOrderNo());
        return RemoteResponse.<List<OrderOperationLogVO>>custom().setData(operationLogList).setSuccess();
    }


    /**
     * 查找订单excel列表
     * @param orderExcelInfoQueryDTO
     * @return
     */
    @RpcMapping("/orderExcel/findOrderExcelList")
    @RpcMethod("查找订单excel列表 /store/order/hms/orderExcel/findOrderExcelList")
    public RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> findOrderExcelList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO) {
        OrderExportQueryDTO orderExportQueryDTO = new OrderExportQueryDTO();
        orderExportQueryDTO.setFileTypeList(New.list(
                ExportFileTypeEnum.HMS_ORDER_DETAIL.getValue(),
                ExportFileTypeEnum.HMS_PRODUCT_DETAIL.getValue(),
                ExportFileTypeEnum.DIY_HMS_ORDER.getValue(),
                ExportFileTypeEnum.RISK_ORDER.getValue()));
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = orderExportService.findOrderExcelList(orderExportQueryDTO, orderExcelInfoQueryDTO, rjSessionInfo);
        return RemoteResponse.success(basePageResultDTO);
    }

    /**
     * 删除订单excel
     * @param orderExportRequestDTO
     * @return
     */
    @RpcMapping("/orderExcel/delete")
    @RpcMethod("删除订单excel /store/order/hms/orderExcel/delete")
    public RemoteResponse<Boolean> deleteOrderExcel(RjSessionInfo rjSessionInfo, OrderExportRequestDTO orderExportRequestDTO) {
        orderExportService.deleteExportedFile(rjSessionInfo, orderExportRequestDTO, true);
        return RemoteResponse.success();
    }

    @RpcMethod("hms订单管理-我的订单")
    @RpcMapping("/getOrderList")
    public PageableResponse<OrderListRespVO> getMyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        Preconditions.notNull(request,"请求我的订单入参不可为空");
        Preconditions.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        request.setMyOrderCheck(false);
        // 默认线上单
        if (request.getProcessSpecies() == null) {
            request.setProcessSpecies(ProcessSpeciesEnum.NORMAL.getValue());
        }
        request.setNeedOfflineInfo(true);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        if(userClient.getHaveAccessViewStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue())){
            request.setShowTestOrderCount(true);
        }
        return buyerOrderService.getOrderListForWWW(request, loginInfo,true);
    }

    @RpcMethod("hms订单管理-订单详情")
    @RpcMapping("/orderDetail")
    public RemoteResponse<OrderInfoVO> getOrderDetail(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) throws ParseException {
        Preconditions.notNull(request,"获取订单详情接口入参为空");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.ORDER_VIEW);
        OrderInfoVO orderInfo = orderDetailRelatedService.getOrderDetail(loginInfo, request, true);
        return RemoteResponse.<OrderInfoVO>custom().setData(orderInfo).setSuccess();
    }

    @RpcMethod("hms订单管理-我的待审批")
    @RpcMapping("/getMyPendingOrderList")
    public PageableResponse<OrderListRespVO> getMyPendingOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        Preconditions.notNull(request,"请求我的订单入参不可为空");
        Preconditions.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        return orderApprovalService.getMyPendingOrderList(request, loginInfo, true);
    }

    /**
     * hms订单管理-我的已审批订单
     * @param rjSessionInfo             用户信息
     * @param request                   分页请求入参
     * @return                          我的已审批订单列表
     */
    @RpcMethod("hms订单管理-我的已审批订单")
    @RpcMapping("/myApprovedList")
    public PageableResponse<OrderListRespVO> myApprovedList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        return orderApprovalService.myApprovedList(request, loginInfo, true);
    }

    /**
     * 该接口已经迁移到order-saturn-service项目
      */
    @RpcMethod("hms/微信-验收审批")
    @RpcMapping("/approval")
    @Deprecated
    public RemoteResponse<Boolean> approval(RjSessionInfo rjSessionInfo, OrderApprovalParamDTO orderApprovalParamDTO) {
        Preconditions.notNull(orderApprovalParamDTO.getOrderId(), "orderId不能为空！");
        Preconditions.notNull(orderApprovalParamDTO.getOperateType(), "operateType不能为空！");
        Integer orgId = rjSessionInfo.getOrgId();
        UserBaseInfoDTO userInfo = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), orgId);
        String orgCode = null;
        try {
            orgCode = OrgEnum.getOrgEnumById(orgId).getCode();
        } catch (Exception e) {
            OrganizationDTO organizationDTO = userClient.getOrgById(orgId);
            Preconditions.notNull(organizationDTO, "根据orgId:" + orgId + "获取机构信息为空！");
            orgCode = organizationDTO.getCode();
        }
        orderApprovalParamDTO.setOrgCode(orgCode);
        orderApprovalParamDTO.setUserId(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue());
        orderApprovalParamDTO.setUserGuid(rjSessionInfo.getGuid());
        orderApprovalParamDTO.setUserName(userInfo.getName());
        orderApprovalParamDTO.setJobNumber(userInfo.getJobnumber());
        orderManageService.submitOrderApproval(orderApprovalParamDTO);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    @RpcMethod("hms批量释放解冻失败的订单")
    @RpcMapping("/batchUnfreezeFund")
    public RemoteResponse<Boolean> batchUnfreezeFund(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        Preconditions.isTrue(request != null,"批量释放经费入参不可为空");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        orderManageService.batchUnfreezeFund(loginInfo, request.getOrderIdList());
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    @RpcMapping("/orderCount")
    @RpcMethod("HMS-订单管理-订单计数")
    public RemoteResponse<OrderListCountVO> countOrderByStatus(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        OrderListCountVO orderCount = buyerOrderService.countOrderList(rjSessionInfo, request, true);
        return RemoteResponse.<OrderListCountVO>custom().setData(orderCount).setSuccess();
    }


    //todo 后续删除
    @RpcMapping("/orderDetailInvoiceList")
    @RpcMethod("HMS-订单管理-订单详情-获取发票信息")
    @Deprecated
    public RemoteResponse<List<OrderInvoiceInfoVO>> listInvoiceInfoByOrder(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.isTrue(rjSessionInfo != null && rjSessionInfo.getGuid() != null, ExecptionMessageEnum.INVOICE_USER_NOT_LOGGED_IN);
        BusinessErrUtil.notNull(request,"获取发票信息接口入参为空");
        String orderNo = request.getOrderNo();
        Integer orgId = rjSessionInfo.getOrgId();
        Long userId = rjSessionInfo.getUserId();
        Integer orderId = request.getOrderId();
        //hms修改发票权限code
        String hmsInvoiceEditAccessCode = StatementAccessCodeEnum.INVOICE_EDIT.getAccessCode();
        List<OrderInvoiceInfoVO> orderInvoiceInfoList = orderDetailRelatedService.listInvoiceByOrder(orderId, orderNo, orgId);
        return RemoteResponse.<List<OrderInvoiceInfoVO>>custom().setData(orderInvoiceInfoList).setSuccess();
    }


    @RpcMapping("/invoiceDetailsForOrders")
    @RpcMethod("HMS-订单管理-订单详情-获取发票信息（新）")
    public RemoteResponse<OrderInvoiceInfoRespVO> invoiceDetailsForOrders(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.isTrue(rjSessionInfo != null && rjSessionInfo.getGuid() != null, ExecptionMessageEnum.INVOICE_USER_NOT_LOGGED_IN);
        BusinessErrUtil.notNull(request,"获取发票信息接口入参为空");
        String orderNo = request.getOrderNo();
        Integer orgId = rjSessionInfo.getOrgId();
        Long userId = rjSessionInfo.getUserId();
        Integer orderId = request.getOrderId();
        //hms修改发票权限code
        String hmsInvoiceEditAccessCode = StatementAccessCodeEnum.INVOICE_EDIT.getAccessCode();
        OrderInvoiceInfoRespVO orderInvoiceInfoList = orderDetailRelatedService.retrieveInvoicesForOrder(orderId, orderNo, orgId, userId, hmsInvoiceEditAccessCode);
        // 检查发票附件查看权限
        Boolean canViewInvoiceAttachment = userClient.findUserHasAccess(orgId, userId.intValue(), null, ConfigConstant.INVOICE_ATTACHMENT_VIEW);
        if (BooleanUtils.isNotTrue(canViewInvoiceAttachment) && Objects.nonNull(orderInvoiceInfoList)) {
            orderInvoiceInfoList.setCanModifyInvoice(false);
            List<OrderInvoiceInfoVO> orderInvoiceInfoVOList = orderInvoiceInfoList.getOrderInvoiceInfoVOList();
            if (CollectionUtils.isNotEmpty(orderInvoiceInfoVOList)) {
                orderInvoiceInfoVOList.forEach(orderInvoiceInfoVO -> {
                    orderInvoiceInfoVO.setInvoiceFileList(New.emptyList());
                    orderInvoiceInfoVO.setPicturePathList(New.emptyList());
                });
            }
        }
        return RemoteResponse.<OrderInvoiceInfoRespVO>custom().setData(orderInvoiceInfoList).setSuccess();
    }


    /**
     * 线上单：只有随货发票才能修改
     * 线下单：无限制（按原本的逻辑）
     *
     * @param createInvoiceDTOList
     * @param rjSessionInfo
     * @return
     */
    @RpcMapping("/saveOrderInvoice")
    @RpcMethod("HMS-订单管理-订单详情-保存发票信息")
    public RemoteResponse<Boolean> saveOrderInvoice(List<CreateInvoiceDTO> createInvoiceDTOList, RjSessionInfo rjSessionInfo) {
        Integer orgId = rjSessionInfo.getOrgId();
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        // 检查发票查看权限
        Boolean canViewInvoice = userClient.findUserHasAccess(orgId, userId.intValue(), null, ConfigConstant.INVOICE_ATTACHMENT_VIEW);
        BusinessErrUtil.isTrue(BooleanUtils.isTrue(canViewInvoice), ExecptionMessageEnum.NO_INVOICE_ATTACHMENT_VIEW_PERMISSION);
        //hms修改发票权限code
        String hmsInvoiceEditAccessCode = StatementAccessCodeEnum.INVOICE_EDIT.getAccessCode();
        orderInvoiceService.saveInvoice(createInvoiceDTOList, userId, orgId, hmsInvoiceEditAccessCode);
        return RemoteResponse.success(true);
    }

    /**
     * 取消退货
     * @return 是否成功
     */
    @RpcMethod("取消退货")
    @RpcMapping("/cancelGoodsReturnDetail")
    public RemoteResponse<Boolean> cancelGoodsReturnDetail(RjSessionInfo rjSessionInfo, GoodsReturnBaseRequestDTO request) {
        boolean result = buyerGoodsReturnService.cancelGoodsReturn(request, rjSessionInfo);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(result);
    }
}
