package com.ruijing.store.order.gateway.print.util;

import com.alibaba.druid.util.StringUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryLogDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceLogDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2023/3/1 16:03
 * @description
 */
public class ApprovalLogTranslator {

    /**
     * 订单验收审批日志转为扁平打印数组
     * @param orderApprovalLogDTOList 验收审批日志（最后一次验收审批通过时）
     * @return 扁平数组
     */
    public static ApprovalLogFlatListDTO orderAcceptApproveLog2FlatListDTO(List<OrderApprovalLogDTO> orderApprovalLogDTOList){
        if(CollectionUtils.isEmpty(orderApprovalLogDTOList)){
            return null;
        }
        return ApprovalLogTranslator.setList2FlatList(orderApprovalLogDTOList, OrderApprovalLogDTO::getApproveLevel,ApprovalLogTranslator::acceptApproveLog2PrintLog);
    }

    /**
     * 订单验收审批日志转为扁平打印数组
     * @param orderPurchaseApprovalLogDTOList 验收审批日志（最后一次验收审批通过时）
     * @return 扁平数组
     */
    public static ApprovalLogFlatListDTO orderPurchaseApprovalLog2FlatListDTO(List<OrderPurchaseApprovalLogDTO> orderPurchaseApprovalLogDTOList){
        return ApprovalLogTranslator.setList2FlatList(orderPurchaseApprovalLogDTOList, OrderPurchaseApprovalLogDTO::getApproveLevel, ApprovalLogTranslator::orderPurchaseApprovalLog2PrintLog);
    }

    /**
     * 采购 最新一条提交初审日志
     */
    public static OrderPrintApprovalLogDTO getLastSubmitApprovalLog(List<OrderPurchaseApprovalLogDTO> orderPurchaseApprovalLogDTOList) {
        if (CollectionUtils.isEmpty(orderPurchaseApprovalLogDTOList)) {
            return null;
        }

        // 获取最新的一条"提交审批"日志
        OrderPurchaseApprovalLogDTO latestLog = orderPurchaseApprovalLogDTOList.stream()
                .filter(log -> StringUtils.equals("提交审批", log.getOperate()))
                .max(Comparator.nullsLast(Comparator.comparing(
                        OrderPurchaseApprovalLogDTO::getDateTimeStamp,
                        Comparator.nullsFirst(Long::compareTo)
                )))
                .orElse(null);
        // 转换为打印格式返回
        return orderPurchaseApprovalLog2PrintLog(latestLog);
    }

    /**
     * 入库审批日志转为扁平打印数组
     * @param warehouseLogList 验收审批日志（最后一次验收审批通过时）
     * @return 扁平数组
     */
    public static List<OrderPurchaseApprovalLogDTO> warehouseLog2ListDTO(List<BizWarehouseEntryLogDTO> warehouseLogList){
        if(CollectionUtils.isEmpty(warehouseLogList)){
            return null;
        }
        // 入库日志没等级，只能按时间排序作为等级
        warehouseLogList.sort(Comparator.comparing(BizWarehouseEntryLogDTO::getCreateTime));
        List<OrderPurchaseApprovalLogDTO> convertLogList = New.listWithCapacity(warehouseLogList.size());
        for(int i = 0; i < warehouseLogList.size(); i++){
            BizWarehouseEntryLogDTO log = warehouseLogList.get(i);
            OrderPurchaseApprovalLogDTO orderPurchaseApprovalLogDTO = new OrderPurchaseApprovalLogDTO();
            orderPurchaseApprovalLogDTO.setApprover(log.getUserName());
            orderPurchaseApprovalLogDTO.setApproveLevel(i + 1);
            if(log.getCreateTime() != null){
                orderPurchaseApprovalLogDTO.setDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, log.getCreateTime()));
                orderPurchaseApprovalLogDTO.setDateTimeStamp(log.getCreateTime().getTime());
            }
            orderPurchaseApprovalLogDTO.setOperate(log.getBusinessDesc());
            orderPurchaseApprovalLogDTO.setResult(log.getBusinessDesc());
            orderPurchaseApprovalLogDTO.setOperateComment(log.getRemark());
            orderPurchaseApprovalLogDTO.setElectronicSignUrl(log.getSignPhoto());
            convertLogList.add(orderPurchaseApprovalLogDTO);
        }
        return convertLogList;
    }

    public static ApprovalLogFlatListDTO warehouseClaimLog2FlatListDTO(List<BizWarehouseReceiceLogDTO> receicePassLogDTOList){
        if(CollectionUtils.isEmpty(receicePassLogDTOList)){
            return null;
        }
        List<OrderPurchaseApprovalLogDTO> orderPrintApprovalLogDTOList = new ArrayList<>(receicePassLogDTOList.size());
        for(int i = 0; i < receicePassLogDTOList.size(); i++){
            BizWarehouseReceiceLogDTO log = receicePassLogDTOList.get(i);
            OrderPurchaseApprovalLogDTO printDTO = new OrderPurchaseApprovalLogDTO();
//            printDTO.setDate(log.getCreateTime() != null ? DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, log.getCreateTime()) : null);
            printDTO.setDateTimeStamp(log.getCreateTime() != null ? log.getCreateTime().getTime() : null);
            printDTO.setApprover(log.getUserName());
            printDTO.setOperate(log.getBusinessDesc());
            // 订单验收审批没有等级，设置0
            printDTO.setApproveLevel(i + 1);
            printDTO.setResult(log.getBusinessDesc());
            printDTO.setOperateComment(log.getRemark());
            printDTO.setElectronicSignUrl(log.getSignPhoto());
            orderPrintApprovalLogDTOList.add(printDTO);
        }
        return ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(orderPrintApprovalLogDTOList);
    }
    
    private static <T> ApprovalLogFlatListDTO setList2FlatList(List<T> logList, Function<T, Integer> getLevelFunction, Function<T, OrderPrintApprovalLogDTO> translateFunction){
        ApprovalLogFlatListDTO approvalLogFlatListDTO = new ApprovalLogFlatListDTO();
        for(T logItem : logList){
            switch (getLevelFunction.apply(logItem)){
                case 1:
                    approvalLogFlatListDTO.setFirstLevelLog(translateFunction.apply(logItem));
                    break;
                case 2:
                    approvalLogFlatListDTO.setSecondLevelLog(translateFunction.apply(logItem));
                    break;
                case 3:
                    approvalLogFlatListDTO.setThirdLevelLog(translateFunction.apply(logItem));
                    break;
                case 4:
                    approvalLogFlatListDTO.setFourthLevelLog(translateFunction.apply(logItem));
                    break;
                case 5:
                    approvalLogFlatListDTO.setFifthLevelLog(translateFunction.apply(logItem));
                    break;
                case 6:
                    approvalLogFlatListDTO.setSixthLevelLog(translateFunction.apply(logItem));
                    break;
                case 7:
                    approvalLogFlatListDTO.setSeventhLevelLog(translateFunction.apply(logItem));
                    break;
                case 8:
                    approvalLogFlatListDTO.setEighthLevelLog(translateFunction.apply(logItem));
                    break;
                default:
                    break;
            }
        }
        return approvalLogFlatListDTO;
    }
    
    public static OrderPrintApprovalLogDTO orderPurchaseApprovalLog2PrintLog(OrderPurchaseApprovalLogDTO log){
        if(log == null){
            return null;
        }
        OrderPrintApprovalLogDTO printDTO = new OrderPrintApprovalLogDTO();
        printDTO.setDate(log.getDateTimeStamp());
        printDTO.setApprover(log.getApprover());
        printDTO.setOperate(log.getOperate());
        printDTO.setOperateComment(log.getOperateComment());
        printDTO.setApproveLevel(log.getApproveLevel());
        printDTO.setResult(log.getResult());
        printDTO.setApprovePhotoUrl(log.getElectronicSignUrl());
        return printDTO;
    }

    public static OrderPrintApprovalLogDTO acceptApproveLog2PrintLog(OrderApprovalLogDTO log) {
        if (log == null) {
            return null;
        }
        OrderPrintApprovalLogDTO printDTO = new OrderPrintApprovalLogDTO();
        printDTO.setDate(log.getCreationTime() != null ? log.getCreationTime().getTime() : null);
        printDTO.setApproverUserId(log.getOperatorId());
        printDTO.setApprover(log.getOperatorName());
        printDTO.setOperate(OrderApprovalEnum.getNameByValue(log.getApproveStatus()));
        // 订单验收审批没有等级，设置0
        printDTO.setApproveLevel(log.getApproveLevel());
        printDTO.setResult(OrderApprovalEnum.getNameByValue(log.getApproveStatus()));
        printDTO.setOperateComment(log.getReason());
        printDTO.setApprovePhotoUrl(log.getSignUrl());
        return printDTO;
    }
}
