package com.ruijing.store.order.gateway.print.dto.warehouse;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/3/2 11:51
 * @description
 */
public class WarehousePrintRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 900841085266110872L;
    
    private String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehousePrintRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .toString();
    }
}
