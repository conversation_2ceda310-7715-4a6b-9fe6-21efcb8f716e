package com.ruijing.store.order.gateway.common;

import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.gateway.common.vo.CommonOptionItemVO;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-04-27 15:11
 * @description:
 **/
@GateWayController(requestMapping = "/common")
public class OrderCommonGWService {

    /**
     * 通用不展示的订单状态。除已拆分外，都是被弃用的状态。Assess有少量单残余。
     */
    private final List<OrderStatusEnum> NOT_SHOW_STATUS_LIST = New.list(
            OrderStatusEnum.changeingFundCar,
            OrderStatusEnum.changeFundCarFailed,
            OrderStatusEnum.Assess,
            OrderStatusEnum.ORDER_SPLIT_UP);

    /**
     * 对接相关状态，特定单位展示
     */
    private final List<OrderStatusEnum> DOCKING_STATUS_LIST = New.list(
            OrderStatusEnum.DeckingFail,
            OrderStatusEnum.WaitingForDockingConfirm
    );

    private final List<OrderStatusEnum> ZHONG_SHAN_DA_XUE_STATUS_LIST = New.list(
            OrderStatusEnum.OrderRejectForStatement,
            OrderStatusEnum.OrderReceiveApprovalTwo
    );

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @RpcMapping("/getOrderStatus")
    public RemoteResponse<List<CommonOptionItemVO<Integer, String>>> getOrderStatus(OrderBasicParamDTO orderBasicParamDTO){
        String orgCode = orderBasicParamDTO.getOrgCode();
        boolean needPushOrder = false;
        if(orgCode != null){
            needPushOrder = dockingConfigCommonService.getIfOrgPushOrderAfterGenerate(orgCode);
        }
        boolean finalNeedPushOrder = needPushOrder;
        return RemoteResponse.success(Arrays.stream(OrderStatusEnum.values())
                .filter(orderStatusEnum -> {
                    if(NOT_SHOW_STATUS_LIST.contains(orderStatusEnum)){
                        // 固定不展示的状态
                        return false;
                    }
                    if(ZHONG_SHAN_DA_XUE_STATUS_LIST.contains(orderStatusEnum) && !ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orgCode)){
                        // 非中大系不展示中大状态
                        return false;
                    }
                    if(DOCKING_STATUS_LIST.contains(orderStatusEnum) && !finalNeedPushOrder){
                        // 非对接单位不展示对接状态
                        return false;
                    }
                    return true;
                })
                .map(orderStatusEnum -> {
            CommonOptionItemVO<Integer, String> commonOptionItemVO = new CommonOptionItemVO<>();
            commonOptionItemVO.setKey(orderStatusEnum.getValue());
            commonOptionItemVO.setValue(orderStatusEnum.getName());
            return commonOptionItemVO;
        }).collect(Collectors.toList()));
    }
}
