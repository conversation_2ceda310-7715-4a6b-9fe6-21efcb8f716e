package com.ruijing.store.order.gateway.oms.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/14 10:38
 * @description
 */
@RpcModel("错误信息返回体")
public class ErrorMsgVO implements Serializable {

    private static final long serialVersionUID = 6156250021926338910L;

    @RpcModelProperty("订单号")
    private String number;

    @RpcModelProperty("错误信息")
    private String errorInfo;

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public ErrorMsgVO(String number, String errorInfo) {
        this.number = number;
        this.errorInfo = errorInfo;
    }

    public ErrorMsgVO() {
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ErrorMsgVO.class.getSimpleName() + "[", "]")
                .add("number='" + number + "'")
                .add("errorInfo='" + errorInfo + "'")
                .toString();
    }
}
