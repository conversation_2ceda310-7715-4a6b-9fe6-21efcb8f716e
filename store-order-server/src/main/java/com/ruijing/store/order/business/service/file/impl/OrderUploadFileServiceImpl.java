package com.ruijing.store.order.business.service.file.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptanceFileRequestDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.order.whitehole.database.dto.file.OrderDeleteFileDTO;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptAttachmentDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AttachmentDTO;
import com.ruijing.store.order.api.file.request.OrderDeleteUploadFileDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.FileUploadKeyTypeEnum;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.file.OrderUploadFileService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OverrideUploadAttachmentRequest;
import com.ruijing.store.order.gateway.file.request.OrderDetailAcceptFileRequest;
import com.ruijing.store.order.gateway.file.request.OrderFileInfoRequestDTO;
import com.ruijing.store.order.gateway.file.request.OrderFileUploadRequestDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/6/29 11:33
 * @description
 */

@Service
public class OrderUploadFileServiceImpl implements OrderUploadFileService {

    @Resource
    private OrderUploadFileRpcClient orderUploadFileRpcClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    /**
     * 获取已上传的文件
     *
     * @param request 获取用参数
     * @return 文件信息
     */
    @Override
    @ServiceLog(description = "获取上传的文件", serviceType = ServiceType.COMMON_SERVICE)
    public List<UploadFileInfoVO> getUploadFileInfoList(OrderFileInfoRequestDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        BusinessErrUtil.notEmpty(orderIdList, ExecptionMessageEnum.NO_ORDER_ID_LIST_PROVIDED);
        List<OrderUploadFileDTO> orderUploadFileDTOList = orderUploadFileRpcClient.getOrderUploadFileList(orderIdList, request.getFileBusinessTypeList());
        return orderUploadFileDTOList.stream().map(item -> {
            UploadFileInfoVO uploadFileInfoVO = new UploadFileInfoVO();
            uploadFileInfoVO.setOrderId(item.getOrderId());
            uploadFileInfoVO.setOrderNo(item.getOrderNo());
            uploadFileInfoVO.setUrl(item.getUrl());
            uploadFileInfoVO.setFileName(item.getFileName());
            uploadFileInfoVO.setFileBusinessType(item.getFileBusinessType());
            uploadFileInfoVO.setFileId(item.getFileId());
            return uploadFileInfoVO;
        }).collect(Collectors.toList());
    }

    /**
     * 上传文件
     *
     * @param request 上传文件的参数
     * @return 是否成功
     */
    @Override
    @ServiceLog(description = "上传文件", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean saveUploadFileInfo(OrderUploadFileDataRequestDTO request) {
        List<OrderUploadFileDataDTO> orderUploadFileDataDTOList = request.getOrderUploadFileDataDTOList();
        if (CollectionUtils.isEmpty(orderUploadFileDataDTOList)){
            return true;
        }
        // 如果链接是还未上传oss的，则先进行上传(文件类型为4-入库单 文件类型为5 订单合同，参考FileUploadKeyTypeEnum)
        Integer fileTypeCode = orderUploadFileDataDTOList.get(0).getFileBusinessType();
        boolean needUpload = FileUploadKeyTypeEnum.legalType(fileTypeCode);
        if (needUpload){
            StringJoiner joiner = new StringJoiner(",");
            try{
                FileUploadKeyTypeEnum typeEnum = FileUploadKeyTypeEnum.getByCode(fileTypeCode);
                BusinessErrUtil.notNull(typeEnum, ExecptionMessageEnum.ILLEGAL_FILE_TYPE);
                Integer keyId = typeEnum.getKeyId();
                FileUploadClient fileUploadClient = UploadFileClient.getFileUploadClientById(keyId);
                for (OrderUploadFileDataDTO orderUploadFileDataDTO : orderUploadFileDataDTOList) {
                    String url = orderUploadFileDataDTO.getUrl();
                    joiner.add("[" + url + "]");
                    InputStream  urlInputStream = getUrlInputStream(url);
                    FileUploadResp fileUploadResp = fileUploadClient.uploadFile(orderUploadFileDataDTO.getFileName(), urlInputStream);
                    orderUploadFileDataDTO.setUrl(fileUploadResp.getAbsolutePath());
                }
            } catch (Exception e){
                Cat.logError(e);
                throw new BusinessInterceptException("oss上传文件失败,入参文件链接为：" + joiner + "异常信息：" + e.getMessage(), e);
            }
        }
        // 通过订单ID获取相关的订单
        List<Integer> orderIdList = orderUploadFileDataDTOList.stream().map(OrderUploadFileDataDTO::getOrderId).distinct().collect(Collectors.toList());
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        // 形成订单id与订单信息的映射
        Map<Integer, OrderMasterDO> orderIdIdentityMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getId, Function.identity());

        // 判断订单是否存在，存在则上传
        List<OrderUploadFileDTO> waitingUploadFileList = orderUploadFileDataDTOList.stream().map(item -> {
            OrderMasterDO orderMasterDO = orderIdIdentityMap.get(item.getOrderId());
            if (orderMasterDO == null) {
                return null;
            }
            item.setOrderNo(orderMasterDO.getForderno());
            return item;
        }).filter(Objects::nonNull).map(item -> {
            // 组装成白洞需要的数据
            OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
            orderUploadFileDTO.setOrderId(item.getOrderId());
            orderUploadFileDTO.setOrderNo(item.getOrderNo());
            orderUploadFileDTO.setUrl(item.getUrl());
            orderUploadFileDTO.setFileName(item.getFileName());
            orderUploadFileDTO.setFileBusinessType(item.getFileBusinessType());
            return orderUploadFileDTO;
        }).collect(Collectors.toList());

        // 获取已上传的文件
        List<Integer> businessTypeList = orderUploadFileDataDTOList.stream().map(OrderUploadFileDataDTO::getFileBusinessType).distinct().collect(Collectors.toList());
        List<OrderUploadFileDTO> alreadyUploadFileList = orderUploadFileRpcClient.getOrderUploadFileList(orderIdList, businessTypeList);
        // 将已上传的文件和未上传的文件分组
        Map<Integer, List<OrderUploadFileDTO>> alreadyUploadOrderIdFileMap = DictionaryUtils.groupBy(alreadyUploadFileList, OrderUploadFileDTO::getOrderId);
        Map<Integer, List<OrderUploadFileDTO>> waitingUploadOrderIdFileMap = DictionaryUtils.groupBy(waitingUploadFileList, OrderUploadFileDTO::getOrderId);

        // 获取上传文件用的比较器
        Comparator<OrderUploadFileDTO> comparator = getOrderUploadFileDTOComparator();
        // 最终上传的文件列表
        List<OrderUploadFileDTO> fileToOverWriteList = New.list();
        waitingUploadOrderIdFileMap.forEach((key, value) -> {
            List<OrderUploadFileDTO> matchAlreadyUploadList = alreadyUploadOrderIdFileMap.get(key);
            boolean isEqual = false;
            if (CollectionUtils.isNotEmpty(matchAlreadyUploadList)) {
                // 将数组先进行排序再转换为字符串比较
                isEqual = value.stream().sorted(comparator).map(OrderUploadFileDTO::toString).collect(Collectors.joining())
                        .equals(matchAlreadyUploadList.stream().sorted(comparator).map(OrderUploadFileDTO::toString).collect(Collectors.joining()));
            }
            // 如果数组不相等，则放入需要上传的数组
            if (!isEqual) {
                fileToOverWriteList.addAll(value);
            }
        });

        Boolean uploadResult = true;
        if (CollectionUtils.isNotEmpty(fileToOverWriteList)) {
            uploadResult = orderUploadFileRpcClient.overWriteList(fileToOverWriteList);
            // 上传成功
            if (uploadResult.equals(true)) {
                updateUploadFileStatus(fileToOverWriteList,orderIdIdentityMap);
            }
        }

        return uploadResult;
    }

    @Override
    public Boolean insertUploadFiles(OrderFileUploadRequestDTO request, String orderNo) {
        List<AttachmentDTO> attachmentList = CollectionUtils.isEmpty(request.getAttachmentList()) ? New.list() : request.getAttachmentList();
        List<AttachmentDTO> videoAttachmentList = CollectionUtils.isEmpty(request.getVideoAttachmentList()) ? New.list() : request.getVideoAttachmentList();
        if (CollectionUtils.isEmpty(attachmentList) && CollectionUtils.isEmpty(videoAttachmentList)) {
            return true;
        }
        List<OrderUploadFileDTO> orderUploadFileDTOList = attachmentList.stream()
                .map(attachmentDTO -> {
                    OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                    orderUploadFileDTO.setUrl(attachmentDTO.getUrl());
                    orderUploadFileDTO.setFileBusinessType(request.getFileBusinessType());
                    orderUploadFileDTO.setOrderId(request.getOrderId());
                    orderUploadFileDTO.setOrderNo(orderNo);
                    orderUploadFileDTO.setFileName(attachmentDTO.getFileName());
                    return orderUploadFileDTO;
                }).collect(Collectors.toList());
        //验收视频
        if (CollectionUtils.isNotEmpty(videoAttachmentList)) {
            List<OrderUploadFileDTO> orderUploadVideoFileDTOList = videoAttachmentList.stream()
                    .map(attachmentDTO -> {
                        OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                        orderUploadFileDTO.setUrl(attachmentDTO.getUrl());
                        orderUploadFileDTO.setFileBusinessType(FileBusinessTypeEnum.ACCEPTANCE_VIDEO.getCode());
                        orderUploadFileDTO.setOrderId(request.getOrderId());
                        orderUploadFileDTO.setOrderNo(orderNo);
                        orderUploadFileDTO.setFileName(attachmentDTO.getFileName());
                        return orderUploadFileDTO;
                    }).collect(Collectors.toList());

            orderUploadFileDTOList.addAll(orderUploadVideoFileDTOList);
        }
        return orderUploadFileRpcClient.insertList(orderUploadFileDTOList);
    }

    @Override
    public Boolean additionalAcceptanceAttachments(OrderFileUploadRequestDTO orderFileUploadRequestDTO) {
        RjSessionInfo rjSessionInfo = RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        UserBaseInfoDTO userDTO = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        BusinessErrUtil.notNull(userDTO, ExecptionMessageEnum.USER_INFO_NOT_FOUND);
        Integer orderId = orderFileUploadRequestDTO.getOrderId();
        BusinessErrUtil.notEmpty(New.list(orderId), "订单Id为空！");
        //查询订单信息
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(New.list(orderId));
        BusinessErrUtil.isTrue(orderMasterDOList.size() == 1, ExecptionMessageEnum.ABNORMAL_ORDER_DATA);
        //检查附件数量 参数封装
        Boolean needUpload = this.checkUploadFile(orderFileUploadRequestDTO, orderMasterDOList.get(0));
        if (!needUpload) {
            return true;
        }
        String orderNo = orderMasterDOList.get(0).getForderno();
        //上传追加附件
        Boolean result = this.insertUploadFiles(orderFileUploadRequestDTO, orderNo);
        // 记录操作日志
        if (CollectionUtils.isNotEmpty(orderFileUploadRequestDTO.getAttachmentList())) {
            this.createOrderOperateLog(orderId,
                    OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_ATTACHMENT.getValue(),
                    userDTO.getId(),
                    StrUtil.format("新追加{}个附件", CollectionUtils.size(orderFileUploadRequestDTO.getAttachmentList())));
        }
        return result;
    }

    /**
     * 构建订单详情关联附件数据
     * 
     * @param detailAttachmentDTOList 详情附件列表
     * @param orderId 订单ID
     * @return 详情关联附件数据列表
     */
    private List<OrderDetailAcceptanceFileRequestDTO> buildOrderDetailAcceptanceFileList(List<AcceptAttachmentDTO> detailAttachmentDTOList, Integer orderId) {
        if(CollectionUtils.isEmpty(detailAttachmentDTOList)) {
            return New.emptyList();
        }
        List<OrderDetailAcceptanceFileRequestDTO> saveList = New.list();
        for (AcceptAttachmentDTO acceptAttachmentDTO : detailAttachmentDTOList) {
            List<Integer> orderDetailIdList = acceptAttachmentDTO.getOrderDetailIdList();
            if (Objects.isNull(acceptAttachmentDTO.getAttachment())) {
                continue;
            }

            // 允许detailId为空
            if (CollectionUtils.isEmpty(orderDetailIdList)) {
                OrderDetailAcceptanceFileRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptanceFileRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderId);
                orderDetailAcceptanceFileDTO.setDetailId(null);
                orderDetailAcceptanceFileDTO.setUrl(acceptAttachmentDTO.getAttachment().getUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(acceptAttachmentDTO.getAttachment().getFileName()));
                saveList.add(orderDetailAcceptanceFileDTO);
                continue;
            }

            for (Integer orderDetailId : orderDetailIdList) {
                OrderDetailAcceptanceFileRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptanceFileRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderId);
                orderDetailAcceptanceFileDTO.setDetailId(orderDetailId);
                orderDetailAcceptanceFileDTO.setUrl(acceptAttachmentDTO.getAttachment().getUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(acceptAttachmentDTO.getAttachment().getFileName()));
                saveList.add(orderDetailAcceptanceFileDTO);
            }
        }
        return saveList;
    }

    /**
     * 追加上传订单详情关联附件
     */
    @ServiceLog(description = "追加详情关联验收附件", operationType = OperationType.WRITE)
    public void appendSaveDetailAttachment(RjSessionInfo rjSessionInfo,OrderDetailAcceptFileRequest request) {
        List<AcceptAttachmentDTO> detailAttachmentDTOList = request.getDetailAttachmentDTOList();
        Integer orderId = request.getOrderId();
        UserBaseInfoDTO userDTO = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        BusinessErrUtil.notNull(userDTO, ExecptionMessageEnum.USER_INFO_NOT_FOUND);
        BusinessErrUtil.notEmpty(request.getDetailAttachmentDTOList(), ExecptionMessageEnum.PLEASE_UPLOAD_ATTACHMENTS_FIRST);
        List<AttachmentDTO> attachmentDTOList = detailAttachmentDTOList.stream().map(AcceptAttachmentDTO::getAttachment).collect(Collectors.toList());
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        // 验收附件数量校验
        this.checkAcceptanceAttachmentsQuantity(attachmentDTOList, orderId, FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());

        // 先关联订单
        OrderFileUploadRequestDTO orderFileUploadRequestDTO = new OrderFileUploadRequestDTO();
        orderFileUploadRequestDTO.setOrderId(request.getOrderId());
        orderFileUploadRequestDTO.setAttachmentList(attachmentDTOList);
        orderFileUploadRequestDTO.setFileBusinessType(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());
        additionalAcceptanceAttachments(orderFileUploadRequestDTO);

        // 关联订单详情
        List<OrderDetailAcceptanceFileRequestDTO> saveList = buildOrderDetailAcceptanceFileList(detailAttachmentDTOList, orderMasterDO.getId());
        
        // 插入新的
        if (CollectionUtils.isNotEmpty(saveList)) {
            orderAcceptCommentClient.batchSaveDetailAcceptanceFile(saveList);
            this.createOrderOperateLog(orderId, OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_ATTACHMENT.getValue(), userDTO.getId(), StrUtil.format("新追加{}个附件", saveList.size()));
        }
    }

    /**
     * 验收附件数量校验
     *
     * @param attachmentUrlList 附件url列表 （可为空）
     * @param orderId           订单Id （
     * @param fileBusinessType  上传文件的类型（不可为空）  com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum
     * @return 需要上传返回true, 无需上传返回false
     */
    private Boolean checkAcceptanceAttachmentsQuantity(List<AttachmentDTO> attachmentUrlList, Integer orderId, Integer fileBusinessType) {

        if (CollectionUtils.isEmpty(attachmentUrlList)) {
            return false;
        }
        //附件数量校验
        OrderFileInfoRequestDTO orderFileInfoRequestDTO = new OrderFileInfoRequestDTO();
        orderFileInfoRequestDTO.setOrderIdList(New.list(orderId));
        orderFileInfoRequestDTO.setFileBusinessTypeList(New.list(fileBusinessType));
        //之前上传附件
        List<UploadFileInfoVO> uploadFileInfoList = this.getUploadFileInfoList(orderFileInfoRequestDTO);
        if(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode().equals(fileBusinessType)){
            BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(attachmentUrlList)
                    && uploadFileInfoList.size() + attachmentUrlList.size() <= OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT, ExecptionMessageEnum.UPLOAD_ATTACHMENT_LIMIT_EXCEEDED, OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT + "个");
        }
        if(FileBusinessTypeEnum.ACCEPTANCE_VIDEO.getCode().equals(fileBusinessType)){
            BusinessErrUtil.isTrue(uploadFileInfoList.size() + attachmentUrlList.size() <= OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT_VIDEO, ExecptionMessageEnum.UPLOAD_ATTACHMENT_VIDEO_LIMIT_EXCEEDED, OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT_VIDEO);
        }
        return true;
    }

    @Override
    public Boolean checkUploadFile(OrderFileUploadRequestDTO orderFileUploadRequestDTO, OrderMasterDO orderMasterDO) {

        Integer fileBusinessType = orderFileUploadRequestDTO.getFileBusinessType();
        BusinessErrUtil.notNull(fileBusinessType, "文件业务类型为空");

        //验收附件数量校验
        Boolean quantityCheck = false;
        if (FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode().equals(fileBusinessType)) {
            quantityCheck = this.checkAcceptanceAttachmentsQuantity(orderFileUploadRequestDTO.getAttachmentList(), orderMasterDO.getId(), fileBusinessType);
        }
        List<AttachmentDTO> videoAttachmentList = orderFileUploadRequestDTO.getVideoAttachmentList();
        if(CollectionUtils.isNotEmpty(videoAttachmentList)){
            quantityCheck = this.checkAcceptanceAttachmentsQuantity(videoAttachmentList, orderMasterDO.getId(), FileBusinessTypeEnum.ACCEPTANCE_VIDEO.getCode());
        }
        return quantityCheck;
    }

    /**
     * 根据文件ID删除上传的文件
     *
     * @param orderDeleteUploadFileDTO
     */
    @Override
    @ServiceLog(description = "删除订单文件", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean deleteUploadFile(OrderDeleteUploadFileDTO orderDeleteUploadFileDTO) {
        Preconditions.notNull(orderDeleteUploadFileDTO, "删除文件参数不可为空");
        Preconditions.notNull(orderDeleteUploadFileDTO.getOrderId(), "订单ID不可为空");
        Preconditions.notEmpty(orderDeleteUploadFileDTO.getFileIdList(), "需要删除的文件ID列表不可为空");
        Preconditions.notNull(orderDeleteUploadFileDTO.getFileBusinessType(), "文件业务类型不可为空");

        OrderDeleteFileDTO orderDeleteFileDTO = new OrderDeleteFileDTO();
        orderDeleteFileDTO.setOrderId(orderDeleteUploadFileDTO.getOrderId());
        orderDeleteFileDTO.setFileIdList(orderDeleteUploadFileDTO.getFileIdList());
        orderDeleteFileDTO.setFileBusinessType(orderDeleteUploadFileDTO.getFileBusinessType());
        return orderUploadFileRpcClient.deleteOrderUploadFile(orderDeleteFileDTO);
    }

    /**
     * 验收审批-覆盖上传验收附件
     *
     */
    @Override
    @ServiceLog(description = "验收审批-覆盖上传验收附件", operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public void acceptApprovalOverrideAttachment(RjSessionInfo rjSessionInfo, OverrideUploadAttachmentRequest request) {
        overrideAcceptAttachment(rjSessionInfo, request);
    }

    /**
     * 覆盖上传订单验收附件
     */
    private void overrideAcceptAttachment(RjSessionInfo rjSessionInfo, OverrideUploadAttachmentRequest request) {

        Integer orderId = request.getOrderId();
        List<AttachmentDTO> attachmentList = request.getAttachmentList();
        List<AcceptAttachmentDTO> detailAttachmentDTOList = request.getDetailAttachmentDTOList();

        // 查询当前用户信息和订单信息
        UserBaseInfoDTO userDTO = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        BusinessErrUtil.notNull(userDTO, ExecptionMessageEnum.USER_INFO_NOT_FOUND);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
        BusinessErrUtil.notEmpty(orderDetailDOList, ExecptionMessageEnum.NO_PRODUCT_DETAILS_FOR_ORDER);

        // 收集最终文件集合
        List<OrderUploadFileDTO> fileDTOList = New.emptyList();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            fileDTOList = attachmentList.stream().map(attachmentDTO -> {
                OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                orderUploadFileDTO.setOrderId(orderId);
                orderUploadFileDTO.setOrderNo(orderMasterDO.getForderno());
                orderUploadFileDTO.setUrl(attachmentDTO.getUrl());
                orderUploadFileDTO.setFileName(attachmentDTO.getFileName());
                orderUploadFileDTO.setFileBusinessType(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());
                return orderUploadFileDTO;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(detailAttachmentDTOList)) {
            fileDTOList = detailAttachmentDTOList.stream().map(AcceptAttachmentDTO::getAttachment).map(attachmentDTO -> {
                OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                orderUploadFileDTO.setOrderId(orderId);
                orderUploadFileDTO.setUrl(attachmentDTO.getUrl());
                orderUploadFileDTO.setFileName(attachmentDTO.getFileName());
                orderUploadFileDTO.setFileBusinessType(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());
                return orderUploadFileDTO;
            }).collect(Collectors.toList());
        }
        Set<String> fileUrlSet = fileDTOList.stream().map(OrderUploadFileDTO::getUrl).collect(Collectors.toSet());

        // 查询OMS验收配置
        List<String> configCodeList = New.list(ConfigConstant.OTHER_ORG_RECEIPT_ATTACHMENT,
                ConfigConstant.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION,
                ConfigConstant.ORDER_VIDEO_ATTACHMENT);
        Map<String, String> receiptConfigMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orderMasterDO.getFusercode(), configCodeList);

        // 校验验收配置
        orderAcceptService.validationAttachment(receiptConfigMap, attachmentList, New.emptyList(), orderDetailDOList);
        orderAcceptService.validateDetailAttachmentUpload(receiptConfigMap, orderDetailDOList, detailAttachmentDTOList);


        // 查询旧的验收附件
        List<OrderUploadFileDTO> oldAcceptanceAttachmentList = orderUploadFileRpcClient.getOrderUploadFileList(New.list(orderId), New.list(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode()));
        Set<String> oldUrlSet = oldAcceptanceAttachmentList.stream().map(OrderUploadFileDTO::getUrl).collect(Collectors.toSet());

        // 被删除的文件
        Set<OrderUploadFileDTO> deletedFiles = New.set(oldAcceptanceAttachmentList);
        deletedFiles.removeIf(item -> fileUrlSet.contains(item.getUrl()));

        // 新增的文件
        Set<OrderUploadFileDTO> addedFiles = New.set(fileDTOList);
        addedFiles.removeIf(item -> oldUrlSet.contains(item.getUrl()));

        // 覆盖上传文件
        if (CollectionUtils.isNotEmpty(fileDTOList)) {
            orderUploadFileRpcClient.overWriteList(fileDTOList);
        } else {
            // 删除全部附件
            OrderDeleteFileDTO orderDeleteFileDTO = new OrderDeleteFileDTO();
            orderDeleteFileDTO.setOrderId(orderId);
            orderDeleteFileDTO.setFileBusinessType(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());
            orderDeleteFileDTO.setFileIdList(oldAcceptanceAttachmentList.stream().map(OrderUploadFileDTO::getFileId).collect(Collectors.toList()));
            orderUploadFileRpcClient.deleteOrderUploadFile(orderDeleteFileDTO);
        }

        // 更新详情关联附件信息
        if (CollectionUtils.isNotEmpty(detailAttachmentDTOList)) {
            orderAcceptCommentClient.deleteDetailAcceptanceFile(orderId);
            List<OrderDetailAcceptanceFileRequestDTO> saveList = buildOrderDetailAcceptanceFileList(detailAttachmentDTOList, orderId);
            orderAcceptCommentClient.batchSaveDetailAcceptanceFile(saveList);
        }



        // 记录新增操作日志
        if (CollectionUtils.isNotEmpty(addedFiles)) {
            this.createOrderOperateLog(orderId,
                    OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_ATTACHMENT.getValue(),
                    userDTO.getId(),
                    StrUtil.format("新追加{}个附件", addedFiles.size()));
        }

        // 记录删除操作日志
        if (CollectionUtils.isNotEmpty(deletedFiles)) {
            Integer orderOperateLog = this.createOrderOperateLog(orderId, OrderApprovalEnum.DELETE_ACCEPTANCE_ATTACHMENT.getValue(), userDTO.getId(), StringUtils.EMPTY);
            this.createOrderFileLog(orderId, orderOperateLog, New.list(deletedFiles));
        }

    }

    /**
     * 获取网络文件流
     * @param fileUrl
     * @return
     * @throws IOException
     */
    private InputStream  getUrlInputStream(String fileUrl) throws IOException {
        fileUrl = URLUtil.encode(fileUrl);
        HttpURLConnection httpUrl = (HttpURLConnection) new URL(fileUrl).openConnection();
        httpUrl.connect();
        return httpUrl.getInputStream();
    }

    private Comparator<OrderUploadFileDTO> getOrderUploadFileDTOComparator(){
        // 上传文件用的比较器
        return   (o1, o2) -> {
            int cmp = o1.getOrderId().compareTo(o2.getOrderId());
            if (cmp != 0) {
                return cmp;
            }
            cmp = o1.getFileBusinessType().compareTo(o2.getFileBusinessType());
            if (cmp != 0) {
                return cmp;
            }
            cmp = o1.getFileName().compareTo(o2.getFileName());
            if (cmp != 0) {
                return cmp;
            }
            cmp = o1.getUrl().compareTo(o2.getUrl());
            return cmp;
        };
    }

    private void updateUploadFileStatus(List<OrderUploadFileDTO>  fileToOverWriteList,Map<Integer, OrderMasterDO> orderIdIdentityMap){
        // 筛选出上传了服务报告的订单
        List<Integer> uploadContractOrderIdList = fileToOverWriteList.stream().filter(item -> FileBusinessTypeEnum.SERVICE_REPORT.getCode().equals(item.getFileBusinessType()))
                .map(OrderUploadFileDTO::getOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uploadContractOrderIdList)){
            return;
        }
        // 查询是否已经修改过状态
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(uploadContractOrderIdList, OrderExtraEnum.UPLOAD_SERVICE_TECHNOLOGY_CONTRACT_STATUS.getValue());
        List<Integer> uploadOrderIdList = baseOrderExtraDTOList.stream().map(BaseOrderExtraDTO::getOrderId).collect(Collectors.toList());
        // 找出没有修改过上传状态的,拼接参数进行状态更新
        List<BaseOrderExtraDTO> updateStatusBaseExtraDTOList = uploadContractOrderIdList.stream()
                .filter(orderId -> !uploadOrderIdList.contains(orderId)).map(orderId -> {
                    BaseOrderExtraDTO toUpdateStatusItem = new BaseOrderExtraDTO();
                    OrderMasterDO orderMasterDO = orderIdIdentityMap.get(orderId);
                    toUpdateStatusItem.setOrderId(orderId);
                    toUpdateStatusItem.setOrderNo(orderMasterDO.getForderno());
                    toUpdateStatusItem.setOrgId(orderMasterDO.getFuserid());
                    toUpdateStatusItem.setExtraKey(OrderExtraEnum.UPLOAD_SERVICE_TECHNOLOGY_CONTRACT_STATUS.getValue());
                    toUpdateStatusItem.setExtraKeyDesc(OrderExtraEnum.UPLOAD_SERVICE_TECHNOLOGY_CONTRACT_STATUS.getDesc());
                    toUpdateStatusItem.setExtraValue("1");
                    return toUpdateStatusItem;
                }).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(updateStatusBaseExtraDTOList)){
            orderExtraClient.insertList(updateStatusBaseExtraDTOList);
        }
    }


    /**
     * 返回日志自增ID
     */
    private Integer createOrderOperateLog(Integer orderId , Integer approveStatus, Integer userId , String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setCreationTime(new Date());
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
        return orderApprovalLog.getId();
    }

    /**
     * 保存删除图片的文件操作日志
     *
     * @param orderId 订单ID
     * @param logId 日志ID
     * @param deleteFileDTOList 被删除的图片集合
     */
    private void createOrderFileLog(Integer orderId, Integer logId, List<OrderUploadFileDTO> deleteFileDTOList) {
        if (CollectionUtils.isEmpty(deleteFileDTOList)) {
            return;
        }
        List<OrderFileOperationLogRequestDTO> fileOperationLogs = deleteFileDTOList.stream().map(fileDTO -> {
            OrderFileOperationLogRequestDTO dto = new OrderFileOperationLogRequestDTO();
            dto.setOrderId(orderId);
            dto.setLogId(logId);
            dto.setUrl(fileDTO.getUrl());
            dto.setFileName(fileDTO.getFileName());
            return dto;
        }).collect(Collectors.toList());
        orderOtherLogClient.batchSaveFileLog(fileOperationLogs);
    }
}
