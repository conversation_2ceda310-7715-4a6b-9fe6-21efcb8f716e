package com.ruijing.store.order.other.dto;

import com.reagent.order.base.order.enums.BarCodeStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: liwenyu
 * @createTime: 2023-08-08 14:47
 * @description:
 **/
public class UniqueBarCodeDTO implements Serializable {

    private static final long serialVersionUID = 4094256046829488423L;

    @RpcModelProperty("条形码字段")
    private String barCode;

    /**
     * 条形码类型 1：单位条形码 2：中爆条形码
     */
    private Integer type;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单明细id
     */
    private Integer orderDetailId;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品货号
     */
    private String productCode;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 规格
     */
    private String spec;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 供应商Id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 批号
     */
    private String batches;

    /**
     * 有效期
     */
    private String expiration;

    /**
     * 生厂厂家
     */
    private String manufacturer;

    /**
     * 耐久度(外观) 0正常1破损
     */
    private Integer exterior;

    /**
     * 0：待录入批次
     * 1：待发货
     * 2：待收货
     * 3：待入库审批
     * 4：已入库
     * --入库驳回
     * 5：待出库审批
     * 6：已出库
     * 7：退货待确认
     * 8：取消退货
     * 9：同意退货
     * 10：退还货物
     * 11：已退货
     * 12: 拒绝退货
     * {@link BarCodeStatusEnum}
     */
    private Integer status;

    @RpcModelProperty(value = "批次状态", enumClass = OrderProductBatchesStatusEnum.class)
    private Integer batchesStatus;

    @RpcModelProperty(value = "交易状态", enumClass = OrderProductTransactionStatusEnum.class)
    private Integer transactionStatus;

    @RpcModelProperty(value = "库房状态", enumClass = OrderProductInventoryStatusEnum.class)
    private Integer inventoryStatus;

    /**
     * 库房id
     */
    private Integer roomId;

    /**
     * 入库单号
     */
    private String entryNo;

    /**
     * 申领单号
     */
    private String applyNo;

    /**
     * 出库单号
     */
    private String exitNo;

    /**
     * 退货单号
     */
    private String returnNo;

    /**
     * 是否已打印
     */
    private Integer printed;

    /**
     * 数量
     */
    private Integer total;

    /**
     * 商品图片
     */
    private String productPicture;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 退货说明
     */
    private String returnDescription;

    /**
     * 操作用户的guid, 入库必填
     */
    private String userGuid;

    /**
     * 是否有效, 1有效0无效
     */
    private Integer valid;

    @ModelProperty("绑定的气瓶码")
    private String gasBottleBarcode;

    @RpcModelProperty("绑定的气瓶")
    private GasBottleVO gasBottle;

    @ModelProperty("一级分类ID")
    private Integer firstCategoryId;

    @ModelProperty("货号")
    private String goodsCode;

    @ModelProperty("单位")
    private String unit;

    @ModelProperty("cas号")
    private String casNo;

    @ModelProperty("包装规格")
    private String packingSpec;

    @ModelProperty("产品规格")
    private String productSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @ModelProperty("危化品标签ID")
    private Integer dangerousType;

    @ModelProperty("危化品标签")
    private String dangerousTag;

    public Integer getDangerousType() {
        return dangerousType;
    }

    public UniqueBarCodeDTO setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
        return this;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public UniqueBarCodeDTO setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
        return this;
    }

    public String getPackingSpec() {
        return packingSpec;
    }

    public UniqueBarCodeDTO setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
        return this;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public UniqueBarCodeDTO setProductSpec(String productSpec) {
        this.productSpec = productSpec;
        return this;
    }

    public String getBarCode() {
        return barCode;
    }

    public UniqueBarCodeDTO setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public UniqueBarCodeDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public UniqueBarCodeDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public UniqueBarCodeDTO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public UniqueBarCodeDTO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public UniqueBarCodeDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public UniqueBarCodeDTO setProductionDate(String  productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public String getSpec() {
        return spec;
    }

    public UniqueBarCodeDTO setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public UniqueBarCodeDTO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public UniqueBarCodeDTO setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public UniqueBarCodeDTO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public UniqueBarCodeDTO setPrice(BigDecimal price) {
        this.price = price;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public UniqueBarCodeDTO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public UniqueBarCodeDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public UniqueBarCodeDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public UniqueBarCodeDTO setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public UniqueBarCodeDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getBatchesStatus() {
        return batchesStatus;
    }

    public UniqueBarCodeDTO setBatchesStatus(Integer batchesStatus) {
        this.batchesStatus = batchesStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public UniqueBarCodeDTO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public UniqueBarCodeDTO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public UniqueBarCodeDTO setRoomId(Integer roomId) {
        this.roomId = roomId;
        return this;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public UniqueBarCodeDTO setEntryNo(String entryNo) {
        this.entryNo = entryNo;
        return this;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public UniqueBarCodeDTO setApplyNo(String applyNo) {
        this.applyNo = applyNo;
        return this;
    }

    public String getExitNo() {
        return exitNo;
    }

    public UniqueBarCodeDTO setExitNo(String exitNo) {
        this.exitNo = exitNo;
        return this;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public UniqueBarCodeDTO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public Integer getPrinted() {
        return printed;
    }

    public UniqueBarCodeDTO setPrinted(Integer printed) {
        this.printed = printed;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public UniqueBarCodeDTO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public String getProductPicture() {
        return productPicture;
    }

    public UniqueBarCodeDTO setProductPicture(String productPicture) {
        this.productPicture = productPicture;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public UniqueBarCodeDTO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public UniqueBarCodeDTO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public UniqueBarCodeDTO setUserGuid(String userGuid) {
        this.userGuid = userGuid;
        return this;
    }

    public Integer getValid() {
        return valid;
    }

    public UniqueBarCodeDTO setValid(Integer valid) {
        this.valid = valid;
        return this;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public UniqueBarCodeDTO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    public GasBottleVO getGasBottle() {
        return gasBottle;
    }

    public UniqueBarCodeDTO setGasBottle(GasBottleVO gasBottle) {
        this.gasBottle = gasBottle;
        return this;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public UniqueBarCodeDTO setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public UniqueBarCodeDTO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public UniqueBarCodeDTO setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public UniqueBarCodeDTO setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public UniqueBarCodeDTO setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public UniqueBarCodeDTO setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
        return this;
    }

    public String getPress() {
        return press;
    }

    public UniqueBarCodeDTO setPress(String press) {
        this.press = press;
        return this;
    }

    public String getPurity() {
        return purity;
    }

    public UniqueBarCodeDTO setPurity(String purity) {
        this.purity = purity;
        return this;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public UniqueBarCodeDTO setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    @Override
    public String toString() {
        return "UniqueBarCodeDTO{" +
                "barCode='" + barCode + '\'' +
                ", type=" + type +
                ", orderNo='" + orderNo + '\'' +
                ", orderDetailId=" + orderDetailId +
                ", productName='" + productName + '\'' +
                ", productCode='" + productCode + '\'' +
                ", productionDate='" + productionDate + '\'' +
                ", spec='" + spec + '\'' +
                ", brand='" + brand + '\'' +
                ", supplierId=" + supplierId +
                ", supplierName='" + supplierName + '\'' +
                ", price=" + price +
                ", batches='" + batches + '\'' +
                ", expiration='" + expiration + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", exterior=" + exterior +
                ", status=" + status +
                ", batchesStatus=" + batchesStatus +
                ", transactionStatus=" + transactionStatus +
                ", inventoryStatus=" + inventoryStatus +
                ", roomId=" + roomId +
                ", entryNo='" + entryNo + '\'' +
                ", applyNo='" + applyNo + '\'' +
                ", exitNo='" + exitNo + '\'' +
                ", returnNo='" + returnNo + '\'' +
                ", printed=" + printed +
                ", total=" + total +
                ", productPicture='" + productPicture + '\'' +
                ", returnReason='" + returnReason + '\'' +
                ", returnDescription='" + returnDescription + '\'' +
                ", userGuid='" + userGuid + '\'' +
                ", valid=" + valid +
                ", gasBottleBarcode='" + gasBottleBarcode + '\'' +
                ", gasBottle=" + gasBottle +
                ", firstCategoryId=" + firstCategoryId +
                ", goodsCode='" + goodsCode + '\'' +
                ", unit='" + unit + '\'' +
                ", casNo='" + casNo + '\'' +
                ", packingSpec='" + packingSpec + '\'' +
                ", productSpec='" + productSpec + '\'' +
                ", modelNumber='" + modelNumber + '\'' +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                ", completionCycle='" + completionCycle + '\'' +
                ", press='" + press + '\'' +
                ", purity='" + purity + '\'' +
                ", dangerousType=" + dangerousType +
                ", dangerousTag='" + dangerousTag + '\'' +
                '}';
    }
}
