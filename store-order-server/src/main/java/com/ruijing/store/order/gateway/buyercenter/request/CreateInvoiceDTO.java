package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.ordermaster.dto.AttachmentDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-05-09 10:10
 * @description:
 **/
public class CreateInvoiceDTO implements Serializable {

    private static final long serialVersionUID = -2722680436015937255L;

    @RpcModelProperty(value = "发票id")
    private Integer id;

    @RpcModelProperty(value = "发票号码", required = true)
    private String invoiceNo;

    @RpcModelProperty(value = "发票代码", required = true)
    private String invoiceCode;

    @RpcModelProperty(value = "发票金额", required = true)
    private BigDecimal amount;

    @RpcModelProperty(value = "发票日期", required = true)
    private Date invoiceDate;

    @RpcModelProperty(value = "摘要内容", required = true)
    private String remark;

    @RpcModelProperty(value = "开票单位", required = true)
    private String drawer;

    @RpcModelProperty(value = "开户银行", required = true)
    private String bankName;

    @RpcModelProperty(value = "联行号")
    private String bankCode;

    @RpcModelProperty(value = "银行名称", required = true)
    private String bankNameCompany;

    @RpcModelProperty(value = "银行卡号", required = true)
    private String bankNum;

    @RpcModelProperty(value = "发票照片,多张照片用,隔开", required = true)
    private String invoicePhoto;

    @RpcModelProperty("电子发票")
    private String url;

    @RpcModelProperty(value = "汇总单ID")
    private Long summaryId;

    @RpcModelProperty(value = "订单ID")
    private Long orderId;

    @RpcModelProperty(value = "银行ID")
    private Integer bankId;

    @RpcModelProperty(value = "采购单位id")
    private Integer orgId;

    @RpcModelProperty(value = "关联订单ID")
    private List<Integer> orderIds;

    @RpcModelProperty(value = "纳税人识别号")
    private String taxpayerNo;

    @RpcModelProperty(value = "发票类型 1:增值税普通发票 2:增值税专用发票 3:增值税电子普通发票 4:机打发票")
    private Integer invoiceType;

    @RpcModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @RpcModelProperty(value = "不合税金额")
    private BigDecimal excludedAmount;

    @RpcModelProperty(value = "项目编码")
    private String projectCode;

    @RpcModelProperty(value = "发票分类 1:全电发票，0：默认发票", required = true)
    private Integer invoiceItem;

    @RpcModelProperty(value = "发票附件列表")
    private List<AttachmentDTO> invoiceFileList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDrawer() {
        return drawer;
    }

    public void setDrawer(String drawer) {
        this.drawer = drawer;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankNameCompany() {
        return bankNameCompany;
    }

    public void setBankNameCompany(String bankNameCompany) {
        this.bankNameCompany = bankNameCompany;
    }

    public String getBankNum() {
        return bankNum;
    }

    public void setBankNum(String bankNum) {
        this.bankNum = bankNum;
    }

    public String getInvoicePhoto() {
        return invoicePhoto;
    }

    public void setInvoicePhoto(String invoicePhoto) {
        this.invoicePhoto = invoicePhoto;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }

    public String getTaxpayerNo() {
        return taxpayerNo;
    }

    public void setTaxpayerNo(String taxpayerNo) {
        this.taxpayerNo = taxpayerNo;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getExcludedAmount() {
        return excludedAmount;
    }

    public void setExcludedAmount(BigDecimal excludedAmount) {
        this.excludedAmount = excludedAmount;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public Integer getInvoiceItem() {
        return invoiceItem;
    }

    public void setInvoiceItem(Integer invoiceItem) {
        this.invoiceItem = invoiceItem;
    }

    public List<AttachmentDTO> getInvoiceFileList() {
        return invoiceFileList;
    }

    public void setInvoiceFileList(List<AttachmentDTO> invoiceFileList) {
        this.invoiceFileList = invoiceFileList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CreateInvoiceDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("invoiceCode='" + invoiceCode + "'")
                .add("invoiceNo='" + invoiceNo + "'")
                .add("amount=" + amount)
                .add("invoiceDate=" + invoiceDate)
                .add("remark='" + remark + "'")
                .add("drawer='" + drawer + "'")
                .add("bankName='" + bankName + "'")
                .add("bankCode='" + bankCode + "'")
                .add("bankNameCompany='" + bankNameCompany + "'")
                .add("bankNum='" + bankNum + "'")
                .add("invoicePhoto='" + invoicePhoto + "'")
                .add("url='" + url + "'")
                .add("summaryId=" + summaryId)
                .add("orderId=" + orderId)
                .add("bankId=" + bankId)
                .add("orgId=" + orgId)
                .add("orderIds=" + orderIds)
                .add("taxpayerNo='" + taxpayerNo + "'")
                .add("invoiceType=" + invoiceType)
                .add("taxAmount=" + taxAmount)
                .add("excludedAmount=" + excludedAmount)
                .add("projectCode='" + projectCode + "'")
                .add("invoiceItem=" + invoiceItem)
                .toString();
    }
}
