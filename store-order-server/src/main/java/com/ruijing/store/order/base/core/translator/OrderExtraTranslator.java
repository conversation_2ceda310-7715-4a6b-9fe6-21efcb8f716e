package com.ruijing.store.order.base.core.translator;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderExtraDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;

/**
 * @Author: Zeng <PERSON>ru
 * @Description:
 * @DateTime: 2021/6/29 9:51
 */
public class OrderExtraTranslator {

    /**
     * order galaxy服务的orderextradto到store order的orderextradto
     * @param input
     * @return
     */
    public static OrderExtraDTO galaxyDtoToOrderDto(BaseOrderExtraDTO input) {
        OrderExtraDTO output = new OrderExtraDTO();
        output.setId(input.getId());
        output.setOrderId(input.getOrderId());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setExtraKey(input.getExtraKey());
        output.setExtraKeyDesc(input.getExtraKeyDesc());
        output.setExtraValue(input.getExtraValue());
        output.setCreateTime(input.getCreateTime());
        output.setUpdateTime(input.getUpdateTime());
        return output;
    }

    /**
     * store order的ExtraDTO -> order galaxy服务的BaseOrderExtraDTO
     * @param input GenerateOrderExtraDTO
     * @param orderMasterDO OrderMasterDO
     * @return BaseOrderExtraDTO
     */
    public static BaseOrderExtraDTO generateExtraDtoToGalaxyDto(GenerateOrderExtraDTO input, OrderMasterDO orderMasterDO) {
        OrderExtraEnum orderExtraEnum = input.getField();
        BaseOrderExtraDTO output = new BaseOrderExtraDTO();
        output.setOrderId(orderMasterDO.getId());
        output.setOrderNo(orderMasterDO.getForderno());
        output.setOrgId(orderMasterDO.getFuserid());
        output.setExtraKey(orderExtraEnum.getValue());
        output.setExtraKeyDesc(orderExtraEnum.getDesc());
        output.setExtraValue(input.getValue());
        return output;
    }

    /**
     * 白洞的ExtraDTO转为store-order的ExtraDTO
     * @param input 白洞的extraDTO
     * @return 订单的extraDTO
     */
    public static OrderExtraDTO whiteHoleDto2OrderDto(com.ruijing.order.whitehole.database.dto.extra.data.BaseOrderExtraDTO input){
        OrderExtraDTO output = new OrderExtraDTO();
        output.setOrderId(input.getOrderId());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setExtraKey(input.getExtraKey());
        output.setExtraKeyDesc(input.getExtraKeyDesc());
        output.setExtraValue(input.getExtraValue());
        output.setCreateTime(input.getCreateTime());
        output.setUpdateTime(input.getUpdateTime());
        return output;
    }

    public static BaseOrderExtraDTO orderMaterDO2BaseExtra(OrderMasterDO order, OrderExtraEnum extraKeyEnum, String value) {
        BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
        orderExtraDTO.setOrderId(order.getId());
        orderExtraDTO.setOrderNo(order.getForderno());
        orderExtraDTO.setOrgId(order.getFuserid());
        orderExtraDTO.setExtraKey(extraKeyEnum.getValue());
        orderExtraDTO.setExtraKeyDesc(extraKeyEnum.getDesc());
        orderExtraDTO.setExtraValue(value);
        return orderExtraDTO;
    }

    /**
     * order galaxy服务的orderextradto列表到store order的orderextradto列表
     * @param inputList
     * @return
     */
    public static List<OrderExtraDTO> galaxyDtoListToOrderDtoList(List<BaseOrderExtraDTO> inputList) {
        List<OrderExtraDTO> outputList = New.list();
        for (BaseOrderExtraDTO output : inputList) {
            outputList.add(galaxyDtoToOrderDto(output));
        }
        return outputList;
    }
}
