package com.ruijing.store.order.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class FileUtils {
    /** logger */
    private static final Logger log = LoggerFactory.getLogger(FileUtils.class);
    //转字符串  classpath下
    public static String translateTemplate(String path) {
            try (
                    InputStream inputStream = FileUtils.class.getResourceAsStream("/" + path);
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            ) {
                StringBuilder stringBuffer = new StringBuilder();
                String oneLine;
                while ((oneLine = bufferedReader.readLine()) != null) {
                    stringBuffer.append(oneLine.trim());
                }
                return stringBuffer.toString();
            } catch (IOException e) {
                e.printStackTrace();
            }
        return StringUtils.EMPTY;
    }

}
