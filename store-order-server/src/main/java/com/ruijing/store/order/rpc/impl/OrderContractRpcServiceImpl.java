package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.order.api.base.other.dto.OrderContractDTO;
import com.ruijing.store.order.api.base.other.dto.OrderContractQueryDTO;
import com.ruijing.store.order.api.base.other.service.OrderContractRpcService;
import com.ruijing.store.order.base.minor.mapper.OrderContractMapper;
import com.ruijing.store.order.base.minor.model.OrderContract;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.OperationType;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:46
 * @description
 */
@MSharpService
public class OrderContractRpcServiceImpl implements OrderContractRpcService {

    @Resource
    private OrderContractMapper orderContractMapper;
    
    /**
     * 查询订单合同信息
     *
     * @param orderContractQueryDTO 查询条件
     * @return 合同信息
     */
    @Override
    public RemoteResponse<List<OrderContractDTO>> listOrderContract(OrderContractQueryDTO orderContractQueryDTO) {
        List<Integer> orderIdList = orderContractQueryDTO.getOrderIdList();
        Preconditions.notEmpty(orderIdList,"传入参数不能为空");
        List<OrderContract> orderContractList =  orderContractMapper.selectByOrderIdIn(orderIdList);
        List<OrderContractDTO> returnDataList = orderContractList.stream().map(contract->{
            OrderContractDTO contractDTO = new OrderContractDTO();
            contractDTO.setContractNo(contract.getContractNo());
            contractDTO.setContractName(contract.getContractName());
            contractDTO.setOrderId(contract.getOrderId());
            contractDTO.setContractLocation(contract.getContractLocation());
            return contractDTO;
        }).collect(Collectors.toList());
        return RemoteResponse.<List<OrderContractDTO>>custom().setData(returnDataList).setSuccess();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "保存订单合同信息")
    public RemoteResponse<Boolean> saveOrderContract(List<OrderContractDTO> orderContractDTOList) {
        if(CollectionUtils.isEmpty(orderContractDTOList)){
            return RemoteResponse.success();
        }
        BusinessErrUtil.isTrue(orderContractDTOList.size() <= 100, "一次最多更新100条数据");
        Set<Integer> relatedOrderIds = new HashSet<>(orderContractDTOList.size());
        List<OrderContract> dataList = new ArrayList<>(orderContractDTOList.size());
        Date now = new Date();
        for(OrderContractDTO orderContractDTO : orderContractDTOList){
            BusinessErrUtil.notNull(orderContractDTO.getOrderId(), "合同订单id不可空");
            relatedOrderIds.add(orderContractDTO.getOrderId());
            OrderContract contract = new OrderContract();
            contract.setContractNo(orderContractDTO.getContractNo());
            contract.setContractName(orderContractDTO.getContractName());
            contract.setOrderId(orderContractDTO.getOrderId());
            contract.setContractLocation(orderContractDTO.getContractLocation());
            contract.setCreateTime(now);
            dataList.add(contract);
        }
        orderContractMapper.deleteByOrderIdIn(relatedOrderIds);
        orderContractMapper.insertList(dataList);
        return RemoteResponse.success();
    }
}
