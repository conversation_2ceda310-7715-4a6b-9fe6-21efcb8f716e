package com.ruijing.store.order.base.baseconfig.dto;

/**
 * @description: 用户机构配置相关DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/21 16:29
 **/
public class OrganizationConfigDTO {

    private static final String CAT_TYPE = "OrganizationConfigDTO";
    /**
     * 机构id
     */
    private Integer orgId;
    /**
     * 配置编码
     */
    private String configCode;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置值(int类型)
     */
    private Integer configIntValue;


    public OrganizationConfigDTO() {
    }

    public OrganizationConfigDTO(Integer orgId, Integer configIntValue) {
        this.orgId = orgId;
        this.configIntValue = configIntValue;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public Integer getConfigIntValue() {
        return configIntValue;
    }

    public void setConfigIntValue(Integer configIntValue) {
        this.configIntValue = configIntValue;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrganizationConfigDTO{");
        sb.append("orgId=").append(orgId);
        sb.append(", configCode='").append(configCode).append('\'');
        sb.append(", configValue='").append(configValue).append('\'');
        sb.append(", configIntValue=").append(configIntValue);
        sb.append('}');
        return sb.toString();
    }
}
