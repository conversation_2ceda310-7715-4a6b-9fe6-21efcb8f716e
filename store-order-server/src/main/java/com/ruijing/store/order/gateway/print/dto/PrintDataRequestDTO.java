package com.ruijing.store.order.gateway.print.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-03-27 12:01
 * @description:
 **/
@RpcModel("打印请求参数")
public class PrintDataRequestDTO implements Serializable {

    private static final long serialVersionUID = -8479085860070644365L;

    @RpcModelProperty("订单id")
    private List<Integer> ids;

    @RpcModelProperty("订单号")
    private List<String> serialNumbers;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public List<String> getSerialNumbers() {
        return serialNumbers;
    }

    public void setSerialNumbers(List<String> serialNumbers) {
        this.serialNumbers = serialNumbers;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", PrintDataRequestDTO.class.getSimpleName() + "[", "]")
                .add("ids=" + ids)
                .add("serialNumbers=" + serialNumbers)
                .toString();
    }
}
