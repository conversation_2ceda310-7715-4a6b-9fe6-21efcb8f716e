package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2021/1/11 10:03
 * @Description
 **/
@RpcApi
public class OrderListCountVO implements Serializable {

    private static final long serialVersionUID = 5745429767967268386L;

    /**
     * 全部计数
     */
    @RpcModelProperty("全部计数")
    private Integer total;
    /**
     * 待卖家确认
     */
    @RpcModelProperty("待卖家确认")
    private Integer waitingForConfirm;

    /**
     * 待发货
     */
    @RpcModelProperty("待发货")
    private Integer waitingForDelivery;

    /**
     * 待收货
     */
    @RpcModelProperty("待收货")
    private Integer waitingForReceive;

    /**
     * 待验收审批
     */
    @RpcModelProperty("待验收审批")
    private Integer orderReceiveApproval;

    /**
     * 待结算（多种状态合计）
     */
    @RpcModelProperty("待结算（多种状态合计）")
    private Integer waitingForStatement;

    /**
     * 结算中
     */
    @RpcModelProperty("结算中")
    private Integer settling;

    /**
     * 异常订单计数
     */
    @RpcModelProperty("异常订单计数")
    private Integer abnormalOrderCount;

    @RpcModelProperty("代配送订单数")
    private Integer deliveryProxyCount;

    @RpcModelProperty("供应商申请取消代配送订单数")
    private Integer applyCancelDeliveryProxyCount;


    @RpcModelProperty("已完成的订单")
    private Integer completedOrderCount;

    public Integer getTotal() {
        return total;
    }

    public OrderListCountVO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public Integer getWaitingForConfirm() {
        return waitingForConfirm;
    }

    public OrderListCountVO setWaitingForConfirm(Integer waitingForConfirm) {
        this.waitingForConfirm = waitingForConfirm;
        return this;
    }

    public Integer getWaitingForDelivery() {
        return waitingForDelivery;
    }

    public OrderListCountVO setWaitingForDelivery(Integer waitingForDelivery) {
        this.waitingForDelivery = waitingForDelivery;
        return this;
    }

    public Integer getWaitingForReceive() {
        return waitingForReceive;
    }

    public OrderListCountVO setWaitingForReceive(Integer waitingForReceive) {
        this.waitingForReceive = waitingForReceive;
        return this;
    }

    public Integer getOrderReceiveApproval() {
        return orderReceiveApproval;
    }

    public OrderListCountVO setOrderReceiveApproval(Integer orderReceiveApproval) {
        this.orderReceiveApproval = orderReceiveApproval;
        return this;
    }

    public Integer getWaitingForStatement() {
        return waitingForStatement;
    }

    public OrderListCountVO setWaitingForStatement(Integer waitingForStatement) {
        this.waitingForStatement = waitingForStatement;
        return this;
    }

    public Integer getSettling() {
        return settling;
    }

    public OrderListCountVO setSettling(Integer settling) {
        this.settling = settling;
        return this;
    }

    public Integer getAbnormalOrderCount() {
        return abnormalOrderCount;
    }

    public void setAbnormalOrderCount(Integer abnormalOrderCount) {
        this.abnormalOrderCount = abnormalOrderCount;
    }

    public Integer getDeliveryProxyCount() {
        return deliveryProxyCount;
    }

    public void setDeliveryProxyCount(Integer deliveryProxyCount) {
        this.deliveryProxyCount = deliveryProxyCount;
    }

    public Integer getApplyCancelDeliveryProxyCount() {
        return applyCancelDeliveryProxyCount;
    }

    public void setApplyCancelDeliveryProxyCount(Integer applyCancelDeliveryProxyCount) {
        this.applyCancelDeliveryProxyCount = applyCancelDeliveryProxyCount;
    }

    public Integer getCompletedOrderCount() {
        return completedOrderCount;
    }

    public void setCompletedOrderCount(Integer completedOrderCount) {
        this.completedOrderCount = completedOrderCount;
    }

    /**
     * @description: 改变构造函数，使初始化值为零
     * @date: 2021/1/11 11:07
     * @author: zengyanru
     * @param
     * @return
     */
    public OrderListCountVO() {
        this.setOrderReceiveApproval(0);
        this.setSettling(0);
        this.setTotal(0);
        this.setWaitingForConfirm(0);
        this.setWaitingForDelivery(0);
        this.setWaitingForReceive(0);
        this.setWaitingForStatement(0);
        this.setAbnormalOrderCount(0);
        this.setDeliveryProxyCount(0);
        this.setApplyCancelDeliveryProxyCount(0);
        this.setCompletedOrderCount(0);
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderListCountVO.class.getSimpleName() + "[", "]")
                .add("total=" + total)
                .add("waitingForConfirm=" + waitingForConfirm)
                .add("waitingForDelivery=" + waitingForDelivery)
                .add("waitingForReceive=" + waitingForReceive)
                .add("orderReceiveApproval=" + orderReceiveApproval)
                .add("waitingForStatement=" + waitingForStatement)
                .add("settling=" + settling)
                .add("abnormalOrderCount=" + abnormalOrderCount)
                .add("deliveryProxyCount=" + deliveryProxyCount)
                .add("applyCancelDeliveryProxyCount=" + applyCancelDeliveryProxyCount)
                .toString();
    }
}
