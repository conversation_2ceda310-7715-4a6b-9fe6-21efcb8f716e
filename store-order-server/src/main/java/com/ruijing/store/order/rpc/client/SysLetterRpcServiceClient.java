package com.ruijing.store.order.rpc.client;

import com.ruijing.base.letter.SysLetterRpcService;
import com.ruijing.base.letter.msg.SysSendDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

/**
 * @description: 站内信RPC客户端
 * @author: zhong<PERSON><PERSON>i
 * @create: 2021/1/20 10:50
 * 后续走消息中心途径发送，{@link  MessageSystemClient}
 **/
@ServiceClient
@Deprecated
public class SysLetterRpcServiceClient {

    @MSharpReference(remoteAppkey = "base-biz-letter-service")
    private SysLetterRpcService sysLetterRpcService;

    @ServiceLog(description = "发送站内和邮件信息给用户", serviceType = ServiceType.RPC_CLIENT)
    public void sendLetterToUser(SysSendDTO request) {
        RemoteResponse response = sysLetterRpcService.sendToUser(request);
        Preconditions.isTrue(response.isSuccess(), "发送站内信息异常：" + JsonUtils.toJsonIgnoreNull(response));
    }
}
