package com.ruijing.store.order.business.service;

import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.order.whitehole.database.dto.address.request.DeliveryOperationLogRequestDTO;
import com.ruijing.store.oms.api.enums.UserOrgModuleEnum;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.oms.request.DeliveryUpdateRequest;

import java.text.ParseException;
import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/7/8 16:09
 */
public interface OMSOrderService {

    /**
     * 获取代配送订单列表
     * @param request
     * @param beginId
     * @param rjSessionInfo
     * @param userOrgModuleEnum 根据关联的单位过滤用的权限
     * @return
     * @throws ParseException
     */
    PageableResponse<List<OrderInfoVO>> getDeliveryProxyOrderList(OrderListRequest request, Integer beginId, RjSessionInfo rjSessionInfo, UserOrgModuleEnum userOrgModuleEnum) throws ParseException;

    /**
     * 获取代配送订单详情
     * @param request
     * @param rjSessionInfo
     * @param userOrgModuleEnum 根据关联的单位过滤用的权限
     * @return
     */
    OrderInfoVO getDeliveryProxyOrderDetail(OrderListRequest request, RjSessionInfo rjSessionInfo, UserOrgModuleEnum userOrgModuleEnum);

    /**
     * （异步）导出 代配送订单列表
     *
     * @param request
     * @param rjSessionInfo
     * @return
     */
    void exportDeliveryProxyOrderList(OrderListRequest request, RjSessionInfo rjSessionInfo);

    /**
     * 导出的 代配送订单列表
     * @param rjSessionInfo
     * @param orderExcelInfoQueryDTO
     * @return
     */
    RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> exportedDeliveryProxyList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO);

    /**
     * 检验是否有查看某个模块的权限
     * @param userGuid 登录进来的session的guid
     * @param accessCode 某个模块的权限
     */
    void checkUserHasAccessOMS(String userGuid, String accessCode);

    /**
     * 查询用户是否有参数中的任意权限
     * @param guid
     * @param accessCodeList
     */
    void checkUserHasAccessOMS(String guid, List<String> accessCodeList);

    /**
     * 删除已导出的代配送列表项
     * @param exportedItemId
     * @return
     */
    Boolean deleteExportedDeliveryProxyItem(Integer exportedItemId);

    /**
     * 完成分拣或配送
     * @param rjSessionInfo
     * @param request
     * @return
     */
    Boolean updateDelivery(RjSessionInfo rjSessionInfo, DeliveryUpdateRequest request);

    /**
     * 分页查询待配送操作日志
     * @param rjSessionInfo
     * @param deliveryUpdateRequest
     * @return
     */
    PageableResponse<List<DeliveryOperationLogDTO>> listDeliveryOperationLog(RjSessionInfo rjSessionInfo, DeliveryOperationLogRequestDTO deliveryUpdateRequest);

    /**
     * OMS-单据查询-订单查询列表
     */
    PageableResponse<List<OrderInfoVO>> getReceiptOrderList(OrderListRequest request);
}
