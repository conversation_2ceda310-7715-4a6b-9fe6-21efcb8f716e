package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.order.whitehole.database.dto.address.request.DeliveryOperationLogRequestDTO;
import com.ruijing.order.whitehole.database.service.DeliveryOperationLogDataService;

import java.util.List;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-25 11:21
 */
@ServiceClient
public class DeliveryOperationLogClient {

    @MSharpReference(remoteAppkey = "order-whitehole-service")
    private DeliveryOperationLogDataService deliveryOperationLogDataService;

    @ServiceLog(description = "查询代配送日志")
    public PageableResponse<List<DeliveryOperationLogDTO>> listOperationLogInOrderId(DeliveryOperationLogRequestDTO requestDTO){
        PageableResponse<List<DeliveryOperationLogDTO>>  pageableResponse = deliveryOperationLogDataService.listOperationLogInOrderId(requestDTO);
        Preconditions.isTrue(pageableResponse.isSuccess(), "查询代配送操作日志失败");
        return pageableResponse;
    }

    @ServiceLog(description = "插入代配送日志", operationType = OperationType.WRITE)
    public Integer insertDeliveryOperationLog(DeliveryOperationLogDTO deliveryOperationLogDTO){
        RemoteResponse<Integer> remoteResponse = deliveryOperationLogDataService.insertDeliveryOperationLog(deliveryOperationLogDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), "插入代配送操作日志失败");
        return remoteResponse.getData();
    }
}
