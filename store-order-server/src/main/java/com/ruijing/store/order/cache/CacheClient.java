package com.ruijing.store.order.cache;

import com.ruijing.fundamental.cache.api.CacheKey;
import com.ruijing.fundamental.cache.redis.client.RedisCacheClient;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.constant.ProtocolConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

/**
 * <AUTHOR>
 * @Date 2021/1/5 15:47
 * @Description
 **/
@ServiceClient
public class CacheClient {

    private final static String SERVICE_NAME = "store-order";

    private final static String CAT_TYPE = "CacheClient";

    @MSharpReference(remoteAppkey = "msharp-cache-service", protocol = ProtocolConstant.REDIS)
    private RedisCacheClient redisCacheClient;

    /**
     * @description: 控制某个操作不在time limit时间内重复
     * @date: 2021/1/5 16:03
     * @author: zengyanru
     * @param uniqKey 某个操作的独特标识，可以是诸如订单id等的字串
     * @param timeLimit 缓存过期的描述限制
     * @return void
     */
    @ServiceLog(description = "控制某个操作不在time limit时间内重复", serviceType = ServiceType.COMMON_SERVICE)
    public void controlRepeatOperation(String uniqKey, Integer timeLimit) {
        this.controlRepeatOperation(uniqKey, timeLimit, "正在处理此单的其他操作，暂时无法提交操作, 请稍等半分钟再进行操作");
    }

    /**
     * @description: 控制某个操作不在time limit时间内重复
     * @date: 2021/12/22 17:03
     * @author: zhangzhifeng
     * @param uniqKey 某个操作的独特标识，可以是诸如订单id等的字串
     * @param timeLimit 缓存过期的描述限制
     * @param tipsMessage 重复操作消息提醒
     * @return void
     */
    @ServiceLog(description = "控制某个操作不在time limit时间内重复", serviceType = ServiceType.COMMON_SERVICE)
    public void controlRepeatOperation(String uniqKey, Integer timeLimit, String tipsMessage) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        Boolean notInCache = redisCacheClient.setIfAbsent(cacheKey, uniqKey, timeLimit);
        Preconditions.isTrue(Boolean.TRUE.equals(notInCache), tipsMessage);
    }

    @ServiceLog(description = "操作完成后立马删除对应的操作缓存，避免多余的等待时间", serviceType = ServiceType.COMMON_SERVICE)
    public void removeCache(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.delete(cacheKey);
    }

    /**
     * @description: 缓存数据到redis
     * @date: 2021/2/25 12:59
     * @author: zengyanru
     * @param uniqKey
     * @param value
     * @param timeLimit
     * @return void
     */
    @ServiceLog(description = "缓存数据到redis", serviceType = ServiceType.COMMON_SERVICE)
    public void putToCache(String uniqKey, Object value, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        try {
            redisCacheClient.setIfAbsent(cacheKey, value, timeLimit);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "putToCache", e.getMessage(), e);
        }
    }

    /**
     * @description: 从缓存获取对象
     * @date: 2021/2/25 13:05
     * @author: zengyanru
     * @param uniqKey
     * @return java.lang.Object
     */
    @ServiceLog(description = "从缓存获取对象", serviceType = ServiceType.COMMON_SERVICE)
    public Object getFromCache(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        try {
            return redisCacheClient.get(cacheKey);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getFromCache", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 设置缓存对象
     * @param uniqKey   唯一key
     * @param value     缓存对象
     * @param timeLimit 过期时间 seconds
     */
    @ServiceLog(description = "设置缓存对象", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void setToCache(String uniqKey, Object value, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        if (timeLimit == null) {
            timeLimit = 86400;
        }
        redisCacheClient.set(cacheKey, value, timeLimit);
    }

    /**
     * 是否存在某个key
     * @param uniqKey   唯一key
     */
    @ServiceLog(description = "是否存在key", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public Boolean exists(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return redisCacheClient.exists(cacheKey);
    }

    /**
     * 分布式锁，失败自旋重试
     * @param uniqKey   唯一key
     * @param timeLimit 过期时间 second
     * @return          is success
     */
    @ServiceLog(description = "分布式锁，失败自旋重试", serviceType = ServiceType.COMMON_SERVICE)
    public boolean lockRetry(String uniqKey, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        boolean success;
        do {
            success = redisCacheClient.setIfAbsent(cacheKey, uniqKey, timeLimit);
        } while (!success);
        return true;
    }

    @ServiceLog(description = "缓存自增", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void incrementOne(String uniKey, Integer expireInSeconds) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniKey);
        redisCacheClient.incrBy(cacheKey, 1L, expireInSeconds);
    }


    @ServiceLog(description = "缓存自减", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void decrementOne(String uniKey, Integer expireInSeconds) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniKey);
        redisCacheClient.decrBy(cacheKey, 1L, expireInSeconds);
    }

    /**
     * 原子操作, 释放锁
     *
     * @param uniqKey 唯一key
     */
    @ServiceLog(description = "操作完成后立马删除对应的操作缓存，避免多余的等待时间", serviceType = ServiceType.RPC_CLIENT)
    public void unlock(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.compareAndDelete(cacheKey, Environment.getAppKey() + ":" + Thread.currentThread().getId());
    }

    /**
     * 原子操作, 分布式锁
     *
     * @param uniqKey   唯一key
     * @param timeLimit 过期时间 second
     * @return 是否成功
     */
    @ServiceLog(description = "分布式锁，防重", serviceType = ServiceType.RPC_CLIENT)
    public boolean tryLock(String uniqKey, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return redisCacheClient.setIfAbsent(cacheKey, Environment.getAppKey() + ":" + Thread.currentThread().getId(), timeLimit);
    }
}
