package com.ruijing.store.order.rpc.impl;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.request.Request;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.shop.category.api.enums.ReimbursementExpenseTypeEnum;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefOrderDetailTagDOMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.model.RefOrderDetailTagDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.OrderConfirmForTheRecordTranslator;
import com.ruijing.store.order.base.core.translator.OrderFundCardCacheTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.core.translator.RefOrderDetailTagTranslator;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.mapper.RefInvoiceOrderMapper;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.minor.model.RefInvoiceOrder;
import com.ruijing.store.order.base.minor.service.OrderRemarkService;
import com.ruijing.store.order.base.minor.service.ThirdPartyCallbackInfoService;
import com.ruijing.store.order.base.minor.translator.OrderRelateTranslator;
import com.ruijing.store.order.base.timeoutstatistics.mapper.TimeoutStatisticsMapper;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.base.timeoutstatistics.service.impl.TimeoutQueryServiceImpl;
import com.ruijing.store.order.base.timeoutstatistics.translator.TimeoutStatisticTranslator;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单相关 服务
 * @author: zhuk
 * @create: 2019-10-12 16:56
 **/
@ServiceLog
@MSharpService
public class OrderRelatedRPCServiceImpl implements OrderRelatedRPCService {

    private String catType = "OrderRelatedRPCServiceImpl";

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private ThirdPartyCallbackInfoService thirdPartyCallbackInfoService;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Autowired
    private WaitingStatementService waitingStatementService;

    @Resource
    private OrderRemarkService orderRemarkService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Autowired
    private OrderManageService orderManageService;

    @Autowired
    private RefInvoiceOrderMapper refInvoiceOrderMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private RefOrderDetailTagDOMapper orderDetailTagDOMapper;

    @Resource
    private TimeoutStatisticsMapper timeoutStatisticsMapper;

    @Resource
    private BusinessDockingRPCClient businessDockingRPCClient;

    /**
     * 很久以前遗留下来的数据问题，因此经讨论约定这个1540 为供应商特别的标志
     */
    private final static int PROMISE_SUPPLIER_FLAG = 1540;

    @Resource
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private UserClient userClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private ResearchBaseService researchBaseService;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private OrderContractClient orderContractClient;

    @Resource
    private OrderMasterCommonService orderMasterCommonService;

    @Resource
    private TimeoutQueryService timeoutQueryService;



    /**
     * 查询订单 关联发票信息
     * @param orderIdList
     * @return
     */
    @Override
    public RemoteResponse<List<RefInvoiceOrderDTO>> findRefInvoiceOrderByRefId(List<String> orderIdList){
        Assert.isTrue(CollectionUtils.isNotEmpty(orderIdList),"关联订单Id集合不能为空！");
        List<RefInvoiceOrder> dataList = refInvoiceOrderMapper.findByRefIdIn(orderIdList);
        List<RefInvoiceOrderDTO> resultList = dataList.stream().map(OrderRelateTranslator::refInvoiceOrder2DTO).collect(Collectors.toList());
        return RemoteResponse.<List<RefInvoiceOrderDTO>>custom().setSuccess().setData(resultList).build();
    }

    /**
     * 根据发票id集合查询 发票信息
     * @param invoiceIdList
     * @return
     */
    @Override
    public RemoteResponse<List<RefInvoiceOrderDTO>> findRefInvoiceOrderByInvoiceIds(List<Integer> invoiceIdList){
        Assert.isTrue(CollectionUtils.isNotEmpty(invoiceIdList),"发票Id集合不能为空！");
        List<RefInvoiceOrder> dataList = refInvoiceOrderMapper.findByInvoiceIdIn(invoiceIdList);
        List<RefInvoiceOrderDTO> resultList = dataList.stream().map(OrderRelateTranslator::refInvoiceOrder2DTO).collect(Collectors.toList());
        return RemoteResponse.<List<RefInvoiceOrderDTO>>custom().setSuccess().setData(resultList).build();
    }

    /**
     * 根据订单id 获取 对应的 测试分析费 和实验耗材费
     * orderMasterIds 不能为空 且 不能超过300
     * @param orderMasterCommonReqDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderCategoryAmountDTO>> getFeeCategoryAmount(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        List<Integer> idList = orderMasterCommonReqDTO.getOrderMasterIds();
        Assert.isTrue(CollectionUtils.isNotEmpty(idList), "orderMasterIds 不能为空");
        Assert.isTrue(idList.size()<=300, "orderMasterIds 不能超过300条");

        Map<Integer, Map<String, BigDecimal>> tagAmountMap = this.getCategoryAmount(idList, CategoryTagTypeEnum.FEE_TAG_TYPE);
        if (MapUtils.isEmpty(tagAmountMap)) {
            return RemoteResponse.<List<OrderCategoryAmountDTO>>custom().setSuccess().setData(New.emptyList()).build();
        }

        List<OrderCategoryAmountDTO> orderCategoryAmountDTOList = New.listWithCapacity(tagAmountMap.size());
        tagAmountMap.forEach((orderId, tagAmount) -> {
            OrderCategoryAmountDTO orderCategoryAmountDTO = new OrderCategoryAmountDTO();
            orderCategoryAmountDTO.setOrderId(orderId);
            BigDecimal analyzeAmount = tagAmount.get(ReimbursementExpenseTypeEnum.TESTANALYSIS.getDesc()) != null ? tagAmount.get(ReimbursementExpenseTypeEnum.TESTANALYSIS.getDesc()) : BigDecimal.ZERO;
            BigDecimal reagentAmount = tagAmount.get(ReimbursementExpenseTypeEnum.EXPERIMENT.getDesc()) != null ? tagAmount.get(ReimbursementExpenseTypeEnum.EXPERIMENT.getDesc()) : BigDecimal.ZERO;
            orderCategoryAmountDTO.setAnalyzeAmount(analyzeAmount);
            orderCategoryAmountDTO.setReagentAmount(reagentAmount);
            orderCategoryAmountDTOList.add(orderCategoryAmountDTO);
        });
        return RemoteResponse.<List<OrderCategoryAmountDTO>>custom().setSuccess().setData(orderCategoryAmountDTOList).build();
    }

    /**
     * 根据订单id 获取 对应的  服务，耗材，试剂，动物 金额
     * @param orderMasterCommonReqDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderCategoryAmountDTO>> getCategoryTypeAmount(OrderMasterCommonReqDTO orderMasterCommonReqDTO) {
        List<Integer> idList = orderMasterCommonReqDTO.getOrderMasterIds();
        Assert.isTrue(CollectionUtils.isNotEmpty(idList), "orderMasterIds 不能为空");
        Assert.isTrue(idList.size()<=300, "orderMasterIds 不能超过300条");

        Map<Integer, Map<String, BigDecimal>> tagAmountMap = this.getCategoryAmount(idList, CategoryTagTypeEnum.CATEGORY_TAG_TYPE);
        if (MapUtils.isEmpty(tagAmountMap)) {
            return RemoteResponse.<List<OrderCategoryAmountDTO>>custom().setSuccess().setData(New.emptyList()).build();
        }

        List<OrderCategoryAmountDTO> orderCategoryAmountDTOList = New.listWithCapacity(tagAmountMap.size());
        tagAmountMap.forEach((orderId, tagAmount) -> {
            OrderCategoryAmountDTO orderCategoryAmountDTO = new OrderCategoryAmountDTO();
            orderCategoryAmountDTO.setOrderId(orderId);
            BigDecimal serviceAmount = tagAmount.get(InboundTypeEnum.SERVICE.getDesc()) != null ? tagAmount.get(InboundTypeEnum.SERVICE.getDesc()) : BigDecimal.ZERO;
            BigDecimal materialAmount = tagAmount.get(InboundTypeEnum.CONSUMABLES.getDesc()) != null ? tagAmount.get(InboundTypeEnum.CONSUMABLES.getDesc()) : BigDecimal.ZERO;
            BigDecimal potionsAmount = tagAmount.get(InboundTypeEnum.REAGENT.getDesc()) != null ? tagAmount.get(InboundTypeEnum.REAGENT.getDesc()) : BigDecimal.ZERO;
            BigDecimal animalAmount = tagAmount.get(InboundTypeEnum.ANIMAL.getDesc()) != null ? tagAmount.get(InboundTypeEnum.ANIMAL.getDesc()) : BigDecimal.ZERO;
            orderCategoryAmountDTO.setServiceAmount(serviceAmount);
            orderCategoryAmountDTO.setMaterialAmount(materialAmount);
            orderCategoryAmountDTO.setPotionsAmount(potionsAmount);
            orderCategoryAmountDTO.setAnimalAmount(animalAmount);
            orderCategoryAmountDTOList.add(orderCategoryAmountDTO);
        });
        return RemoteResponse.<List<OrderCategoryAmountDTO>>custom().setSuccess().setData(orderCategoryAmountDTOList).build();
    }

    /**
     * @description: 通过订单id，需要的分类标签tag（报销或是入库类型）枚举，返回订单id-分类标签tag的id对应可报销金额
     * @date: 2021/4/14 18:35
     * @author: zengyanru
     * @param orderIdList
     * @param categoryTagTypeEnum
     * @return java.util.Map<java.lang.Integer,java.util.Map<java.lang.Integer,java.math.BigDecimal>>
     */
    private Map<Integer, Map<String, BigDecimal>> getCategoryAmount(List<Integer> orderIdList,CategoryTagTypeEnum categoryTagTypeEnum){
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return New.emptyMap();
        }

        // key-->orderMasterId, key--> reimbursement/inbound type value--> totalAmount
        Map<Integer, Map<String, BigDecimal>> tagAmountMap = new HashMap<>();
        for (OrderDetailDO orderDetailDO : orderDetailList) {
            BigDecimal bidAmount = orderDetailDO.getFbidamount().subtract(orderDetailDO.getFcancelquantity().multiply(orderDetailDO.getFbidprice()));
            Integer orderId = orderDetailDO.getFmasterid();
            String categoryTagType = null;
            if (CategoryTagTypeEnum.FEE_TAG_TYPE.equals(categoryTagTypeEnum)) {
                categoryTagType = orderDetailDO.getFeeTypeTag();
            } else if (CategoryTagTypeEnum.CATEGORY_TAG_TYPE.equals(categoryTagTypeEnum)){
                categoryTagType = orderDetailDO.getCategoryTag();
            } else {
                Preconditions.isTrue(false, "com.ruijing.store.order.rpc.impl.OrderRelatedRPCServiceImpl.getCategoryAmount 枚举入参非法，枚举入参："+categoryTagTypeEnum);
            }
            // 若同一个订单，需要增加同一商品报销类别的金额
            if (tagAmountMap.containsKey(orderId)) {
                // 加入同一订单的相同类目金额。如果不存在且类型不为null，则新加入
                if (tagAmountMap.get(orderId).containsKey(categoryTagType)) {
                    BigDecimal curSum = tagAmountMap.get(orderId).get(categoryTagType).add(bidAmount);
                    tagAmountMap.get(orderId).put(categoryTagType, curSum);
                } else if (categoryTagType != null) {
                    tagAmountMap.get(orderId).put(categoryTagType, bidAmount);
                }
            } else {
                // 新建tagid对应的费用并避免null值为key
                Map<String, BigDecimal> tagIdAmountMap = new HashMap<>();
                if (categoryTagType != null) {
                    tagIdAmountMap.put(categoryTagType, bidAmount);
                }
                tagAmountMap.put(orderId, tagIdAmountMap);
            }
        }
        return tagAmountMap;
    }

    @Override
    public RemoteResponse insertOrderApproval(OrderApprovalLogDTO orderApprovalLogDTO) {
        return orderApprovalLogService.insertOrderApprovalLog(orderApprovalLogDTO);
    }

    /**
     * 根据订单id列表查询 订单经费卡关联信息
     * @param orderIdList
     * @return
     */
    @Override
    public RemoteResponse<List<RefFundcardOrderDTO>> findRefFundCardOrderByOrderIdList(List<Integer> orderIdList){
        Assert.isTrue(orderIdList.size()<301,"id集合不能超过300");
        List<RefFundcardOrderDTO> resultList = refFundcardOrderService.findByOrderIdList(orderIdList);
        return RemoteResponse.<List<RefFundcardOrderDTO>>custom().setSuccess().setData(resultList).build();
    }

    /**
     * 根据采购单id列表查询 订单经费卡关联信息
     * @param applicationIdList
     * @return
     */
    @Override
    public RemoteResponse<List<RefFundcardOrderDTO>> findRefFundCardOrderByApplicationIdList(List<Integer> applicationIdList){
        Assert.isTrue(applicationIdList.size()<301,"id集合不能超过300");
        List<RefFundcardOrderDTO> resultList = refFundcardOrderService.findByApplicationIdList(applicationIdList);
        return RemoteResponse.<List<RefFundcardOrderDTO>>custom().setSuccess().setData(resultList).build();
    }

    @Override
    public RemoteResponse insertRefFundcardOrder(RefFundcardOrderDTO refFundcardOrderDTO) {
        return refFundcardOrderService.insertRefFundcardOrder(refFundcardOrderDTO);
    }

    @Override
    @ServiceLog(description = "批量删除绑卡记录", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> deleteRefFundCardByIdList(List<RefFundcardOrderDTO> request) {
        Preconditions.notEmpty(request, "参数不可为空！");
        List<String> idList = request.stream().map(RefFundcardOrderDTO::getId).collect(Collectors.toList());
        refFundcardOrderService.deleteByIdList(idList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(idList.size());
    }

    @Override
    public RemoteResponse updateRefFundcardOrderByOrderId(RefFundcardOrderDTO refFundcardOrderDTO) {
        return refFundcardOrderService.updateRefFundcardOrderByOrderId(refFundcardOrderDTO);
    }

    @Override
    public RemoteResponse deleteRefFundcardOrderByOrderId(String orderId) {
        return refFundcardOrderService.deleteRefFundcardOrderByOrderId(orderId);
    }

    @Override
    @ServiceLog(description = "根据订单id删除绑卡记录", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> deleteRefFundCardByOrderIds(OrderBasicParamDTO request) {
        int affect = refFundcardOrderService.deleteByOrderIds(request.getOrderIdList());
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    public RemoteResponse insertCallbackInfo(ThirdPartyCallbackInfoDTO infoDTO) {
        return thirdPartyCallbackInfoService.insertCallbackInfo(infoDTO);
    }

    @Override
    public RemoteResponse updateCallbackInfoById(ThirdPartyCallbackInfoDTO infoDTO) {
        return thirdPartyCallbackInfoService.updateCallbackInfoById(infoDTO);
    }

    @Override
    public RemoteResponse<List<OrderRemarkDTO>> findOrderRemarkByPrimaryKeys(List<OrderRemarkDTO> orderRemarkDTOList) {
        Preconditions.notEmpty(orderRemarkDTOList, "params is not be null!");
        Preconditions.isTrue(orderRemarkDTOList.size() <= 500, "入参数组长度不能超过500");

        List<OrderRemarkDTO> result = orderRemarkService.findOrderRemarkByPrimaryKeys(orderRemarkDTOList);
        return RemoteResponse.<List<OrderRemarkDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> saveOrderRemark(OrderRemarkDTO request) {
        Preconditions.notNull(request.getFtbuyappid(), "保存失败，采购单id不可为空");
        Preconditions.notNull(request.getFsuppid(), "保存失败，供应商id不可为空");

        int affect = orderRemarkService.saveOrUpdateOrderRemark(request);
        if (affect == 0) {
            return RemoteResponse.<Boolean>custom().setFailure("更新失败！无效的记录").setData(false);
        }
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse revertOrderStatement(RevertStatementRequestDTO request) {
        Assert.notNull(request.getOrgCode(), "订单废除结算失败！机构编码不能为空");
        BusinessErrUtil.notEmpty(request.getOrderIdList(), ExecptionMessageEnum.ORDER_REJECTION_SETTLEMENT_FAILED_INVALID_ID);

        OrderMasterDO param = new OrderMasterDO();
        // 订单回到待结算状态
        param.setStatus(request.getOrderStatus().getValue());
        param.setInStateTime(OrderCommonUtils.getInitStateDate());
        param.setStatementId(null);
        param.setStatementStatus(-1);
        orderMasterMapper.updateStatementByIdIn(param, request.getOrderIdList());

        // 异步同步数据, 方法内部已做异常处理
        waitingStatementService.pushWaitingStatement(request.getOrgCode(), request.getOrderIdList());

        // 批量记录操作日志
        List<OrderApprovalLogDTO> logs = approvalLogGenerate(request);
        if (CollectionUtils.isNotEmpty(logs)) {
            orderApprovalLogService.insertOrderApprovalLogList(logs);
        }
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 生成批量操作日志
     * @param request
     * @return
     */
    private List<OrderApprovalLogDTO> approvalLogGenerate(RevertStatementRequestDTO request) {
        if (request.getOperatingStatus() == null || request.getOperatingType() == null) {
            Cat.logWarn(catType, "approvalLogGenerate", "发生订单未知操作！");
            return New.emptyList();
        }
        List<Integer> orderIdList = request.getOrderIdList();
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIdList);
        BusinessErrUtil.notEmpty(orderList, ExecptionMessageEnum.GENERATE_APPROVAL_LOG_FAILED_NO_ORDER, orderIdList);
        Map<Integer, Integer> orderIdBuyerIdMap = DictionaryUtils.toMap(orderList, OrderMasterDO::getId, OrderMasterDO::getFbuyerid);
        List<OrderApprovalLogDTO> logs = new ArrayList<>(orderIdList.size());
        OrderApprovalLogDTO log = null;
        for (Integer orderId : orderIdList) {
            log = new OrderApprovalLogDTO();
            log.setOrderId(orderId);
            Integer operatorId = request.getOperatorId();
            log.setOperatorId(operatorId);
            // 特殊的单位眼科操作人id传的是0，这里改操作人成订单采购人的id
            if (operatorId == null || operatorId <= 0) {
                log.setOperatorId(orderIdBuyerIdMap.get(orderId));
            }
            log.setApproveStatus(request.getOperatingStatus().getValue());

            // 是供应商就设置那个历史遗留的id，供应商特别的标志
            if (request.getOperatingType() == StatementOperatingEnum.SUPPLIER) {
                log.setOperatorId(PROMISE_SUPPLIER_FLAG);
            }else if(request.getOperatingType() == StatementOperatingEnum.SYSTEM){
                // 操作人是系统，用系统的id -1，用于异常数据处理功能
                log.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            }

            log.setCreationTime(new Date());
            log.setReason(request.getReason());
            logs.add(log);
        }

        return logs;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse statementUpdateOrder(List<OrderStatementRequestDTO> orderStatementRequestDTOList) {
        BusinessErrUtil.notEmpty(orderStatementRequestDTOList, ExecptionMessageEnum.INITIATE_SETTLEMENT_FAILED_NO_OBJECT);
        // 订单id
        List<Integer> allIdList = new ArrayList<>();
        orderStatementRequestDTOList.forEach(p -> allIdList.addAll(p.getOrderIdList()));

        if (CollectionUtils.isNotEmpty(allIdList)) {
            // 删除待结算业务表的记录
            statementPlatformClient.deleteWaitingStatementByOrderId(allIdList);
            OrderMasterDO param = new OrderMasterDO();
            // 订单进入结算中状态
            param.setStatus(OrderStatusEnum.Statementing_1.getValue());
            param.setInStateTime(new Date());
            // 更新订单的结算状态
            orderMasterMapper.updateStatementIdByOrderIdIn(param, orderStatementRequestDTOList);
   }

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse warehouseUpdateOrder(InventoryOrderRequestDTO param) {
        Assert.isTrue(param.getOrderId() != null, "订单出入库状态更新失败！订单id为空！");
        String orgCode = param.getOrgCode();
        Assert.isTrue(orgCode != null, "订单出入库状态更新失败！医院编码为空！");

        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(param.getOrderId());
        BusinessErrUtil.isTrue(orderMasterDO != null, ExecptionMessageEnum.ORDER_STATUS_UPDATE_FAILED_NOT_EXIST);
        orderManageService.wareHouseSuccess(param.getInventoryStatus().getCode().byteValue(), orgCode, orderMasterDO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 用于同步订单结算状态，状态不一致才修改，防止刷新表更新时间
     * @param syncStatementStatusDTOS 批量更新结算状态DTO（订单id、结算状态）
     * @return Boolean true更新成功 false更新失败
     */
    @Override
    public RemoteResponse<Integer> batchUpdateStatementStatus(List<SyncStatementStatusDTO> syncStatementStatusDTOS) {
        Preconditions.notEmpty(syncStatementStatusDTOS, "入参不能为空!");
        Preconditions.isTrue(syncStatementStatusDTOS.size() <= 100, "批量条数小于等于100条");
        int affect = orderStatementService.batchUpdateStatementStatus(syncStatementStatusDTOS);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    public RemoteResponse<OrgConfigResponseDTO> findSysConfigByOrgCode(OrgConfigRequestDTO param) {
        Assert.isTrue(param.getOrgCode() != null, "获取配置失败！机构编码为空！");
        Assert.isTrue(param.getConfigCode() != null, "获取配置失败！配置编码为空！");

        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(param.getOrgCode(), Collections.singletonList(param.getConfigCode()));
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(configList), ExecptionMessageEnum.CONFIG_RETRIEVAL_FAILED);

        BaseConfigDTO configDTO = configList.get(0);
        OrgConfigResponseDTO result = configConvert(configDTO);
        return RemoteResponse.<OrgConfigResponseDTO>custom().setSuccess().setData(result).build();
    }

    @Override
    public RemoteResponse<List<OrgConfigResponseDTO>> findSysConfigListByOrgCode(OrgConfigRequestDTO param) {
        Assert.isTrue(param.getOrgCode() != null, "获取配置失败！机构编码为空！");
        Assert.notEmpty(param.getConfigCodesList(), "获取配置失败！配置编码为空！");

        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(param.getOrgCode(), param.getConfigCodesList());
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(configList), ExecptionMessageEnum.CONFIG_RETRIEVAL_FAILED);

        List<OrgConfigResponseDTO> result = configList.stream().map(r -> configConvert(r)).collect(Collectors.toList());
        return RemoteResponse.<List<OrgConfigResponseDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 配置dto 转换函数
     * @param configDTO
     * @return
     */
    private OrgConfigResponseDTO configConvert(BaseConfigDTO configDTO) {
        OrgConfigResponseDTO result = new OrgConfigResponseDTO();
        result.setConfigCode(configDTO.getConfigCode());
        result.setConfigValue(configDTO.getConfigValue());
        result.setOrgCode(configDTO.getOrgCode());
        return result;
    }


    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse saveFundCardCaches(List<OrderFundCardCacheRequestDTO> fundCardList, String orgCode){

        // 余额校验
        List<Integer> orderIdList = this.checkFundCardSufficient(fundCardList,orgCode);

        // 2.判断是不是第一次改卡，是就把新旧卡都推进缓存表，否则只存新卡
        // 2.1 查询经费卡缓存数据
        List<OrderFundCardDTO> fundCardCache = orderFundCardCacheClient.findOrderFundCardCache(orderIdList);
        Map<Integer, List<OrderFundCardDTO>> orderIdCacheMap = DictionaryUtils.groupBy(fundCardCache, OrderFundCardDTO::getOrderId);

        List<String> orderIdString = orderIdList.stream().map(
                id -> {
                    return String.valueOf(id);
                }
        ).collect(Collectors.toList());

        List<RefFundcardOrderDTO> oldFundCardList = refFundcardOrderService.findByOrderId(orderIdString);
        Map<String, List<RefFundcardOrderDTO>> oldFundCardMap = DictionaryUtils.groupBy(oldFundCardList, RefFundcardOrderDTO::getOrderId);

        // 2.2 换卡时对新卡序列 + 1
        List<OrderFundCardDTO> finalFundCardList = new ArrayList<>();
        fundCardList.forEach(
                n -> {
                    // 如果是第一次换卡，从ref_fundcard_order取记录
                    if (!orderIdCacheMap.containsKey(n.getOrderId())) {
                        List<OrderFundCardDTO> oldFundCardListDto = OrderFundCardCacheTranslator.refToCacheDto(oldFundCardMap.get(n.getOrderId().toString()));
                        if (CollectionUtils.isNotEmpty(oldFundCardListDto)) {
                            finalFundCardList.addAll(oldFundCardListDto);
                        }
                    } else {
                        // 第n次换卡则序列加1
                        OrderFundCardDTO oldCardCache = orderIdCacheMap.get(n.getOrderId()).get(0);
                        n.setSequence(oldCardCache.getSequence() + 1);
                    }

                    finalFundCardList.add(OrderFundCardCacheTranslator.dtoToCacheDto(n));
                });

        try {
            orderFundCardCacheClient.saveFundCardCache(finalFundCardList);
        } catch (CallRpcException e) {
            Cat.logError(catType, "saveFundCard", "经费卡保存异常！", e);
            throw e;
        }
        return RemoteResponse.custom().setSuccess().build();
    }


    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse saveFundCardCache(List<OrderFundCardCacheRequestDTO> fundCardList) {
        // 1.获取订单号
        List<Integer> orderIdList = ListUtils.toList(fundCardList, OrderFundCardCacheRequestDTO::getOrderId);
        // 2.判断是不是第一次改卡，是就把新旧卡都推进缓存表，否则只存新卡
        // 2.1 查询经费卡缓存数据
        List<OrderFundCardDTO> fundCardCache = orderFundCardCacheClient.findOrderFundCardCache(orderIdList);
        Map<Integer, List<OrderFundCardDTO>> orderIdCacheMap = DictionaryUtils.groupBy(fundCardCache, OrderFundCardDTO::getOrderId);

        List<String> orderIdString = orderIdList.stream().map(
                id -> {
                    return String.valueOf(id);
                }
        ).collect(Collectors.toList());

        List<RefFundcardOrderDTO> oldFundCardList = refFundcardOrderService.findByOrderId(orderIdString);
        Map<String, List<RefFundcardOrderDTO>> oldFundCardMap = DictionaryUtils.groupBy(oldFundCardList, RefFundcardOrderDTO::getOrderId);

        // 2.2 换卡时对新卡序列 + 1
        List<OrderFundCardDTO> finalFundCardList = new ArrayList<>();
        fundCardList.forEach(
                n -> {
                    // 如果是第一次换卡，从ref_fundcard_order取记录
                    if (!orderIdCacheMap.containsKey(n.getOrderId())) {
                        List<OrderFundCardDTO> oldFundCardListDto = OrderFundCardCacheTranslator.refToCacheDto(oldFundCardMap.get(n.getOrderId().toString()));
                        if (CollectionUtils.isNotEmpty(oldFundCardListDto)) {
                            finalFundCardList.addAll(oldFundCardListDto);
                        }
                    } else {
                        // 第n次换卡则序列加1
                        OrderFundCardDTO oldCardCache = orderIdCacheMap.get(n.getOrderId()).get(0);
                        n.setSequence(oldCardCache.getSequence() + 1);
                    }

                    finalFundCardList.add(OrderFundCardCacheTranslator.dtoToCacheDto(n));
                });

        try {
            orderFundCardCacheClient.saveFundCardCache(finalFundCardList);
        } catch (CallRpcException e) {
            Cat.logError(catType, "saveFundCard", "经费卡保存异常！", e);
            throw e;
        }
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 检查经费是否充足：会逐一检查order和经费卡是否存在，对比需要冻结的金额和经费卡余额
     *
     * @param fundCardCacheList
     */
    private List<Integer> checkFundCardSufficient(List<OrderFundCardCacheRequestDTO> fundCardCacheList,String orgCode) {
        Preconditions.notEmpty(fundCardCacheList, "经费卡信息为空!");
        Preconditions.isTrue(StringUtils.isNotBlank(orgCode), "用户信息orgCode为空");

        // 1.获取订单list
        List<Integer> orderIdList = ListUtils.toList(fundCardCacheList, OrderFundCardCacheRequestDTO::getOrderId);
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);

        // 2.筛选出旧单
        OrderMasterDO oldOrder = orderMasterDOList.stream()
                .filter(orderMasterDO -> {
                    String fusercode = orderMasterDO.getFusercode();
                    return !researchBaseService.isNewOrder(orderMasterDO, OrderDateConstant
                            .ORG_CODE_OLD_ORDER_DATE_CHANGE_CARD_MAP.get(fusercode));
                })
                .findFirst().orElse(null);

        String forderno = oldOrder == null ? "" : oldOrder.getForderno();
        Preconditions.isNull(oldOrder, "旧订单" + forderno + "不支持换卡!");

        // 2.获取经费卡list
        // 获取当前用户orgCode
        // orgId需要从store服务获取
        List<String> fundCardIdList = ListUtils.toList(fundCardCacheList, OrderFundCardCacheRequestDTO::getFundCardId);
        // 根据经费卡id获取对应的经费卡
        List<FundCardDTO> fundCardList =
                researchFundCardServiceClient.getFundCardListByCardIds(fundCardIdList, orgCode);

        // 3.逐一判断经费卡余额是否足够支付订单需要冻结的金额
        // 订单id 映射对应 订单
        Map<Integer, OrderMasterDO> orderMasterDOMap = orderMasterDOList.stream()
                .collect(Collectors.toMap(OrderMasterDO::getId, singleOrder -> singleOrder, (orderOne, orderTwo) -> orderOne));

        // 经费卡id 映射对应 经费卡
        Map<String, FundCardDTO> orderFundCardMap = fundCardList.stream()
                .collect(Collectors.toMap(FundCardDTO::getId, singleFunCard -> singleFunCard, (funCardOne, funCardTwo) -> funCardOne));

        // fundCardCache 可找到对应经费卡，也可找到对应订单,依次对比金额
        fundCardCacheList.forEach(fundCardCache -> {
            // 校验FundCardDTO是否存在
            FundCardDTO fundCardDTO = orderFundCardMap.get(fundCardCache.getFundCardId());
            BusinessErrUtil.notNull(fundCardDTO, ExecptionMessageEnum.FUND_CARD_NOT_EXIST, fundCardCache.getId());

            // 校验order是否存在
            OrderMasterDO orderMasterDO = orderMasterDOMap.get(fundCardCache.getOrderId());
            Preconditions.notNull(orderMasterDO, "订单" + fundCardCache.getOrderId() + "不存在，请联系管理员！");

            // 订单总价格
            BigDecimal forderamounttotal = orderMasterDO.getForderamounttotal();
            // 退货金额
            Double returnAmount = orderMasterDO.getReturnAmount();
            // 需要冻结的金额
            BigDecimal freezeAmount = returnAmount == null ? forderamounttotal : forderamounttotal.subtract(new BigDecimal(returnAmount));
            researchFundCardServiceClient.checkFundCardSufficient(fundCardDTO, freezeAmount);
            // 如果校验通过，则设置冻结金额
            fundCardCache.setFreezeAmount(freezeAmount);
        });

        return orderIdList;
    }



    @Override
    public RemoteResponse<List<OrderFundCardResponseDTO>> findFundCardByDepartmentId(OrderFundCardRequestDTO request) {
        Assert.isTrue(request.getOrgCode() != null, "获取经费卡失败，机构编号为空！");
        Assert.isTrue(request.getOrderId() != null, "获取经费卡失败，订单id为空！");
        Assert.isTrue(request.getOrgId() != null, "获取经费卡失败，医院id为空！");

        // 获取部门pi的信息
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(request.getOrderId());
        BusinessErrUtil.isTrue(orderMaster != null, ExecptionMessageEnum.OBTAIN_FUNDING_CARD_FAILED_INVALID_ORDER);
        DepartmentDTO departmentInfo = userClient.getDepartmentInfo(orderMaster.getFbuydepartmentid());
        BusinessErrUtil.isTrue(departmentInfo != null, ExecptionMessageEnum.FUNDING_CARD_FAILURE_INVALID_TEAM);

        // 查询pi信息，采购人信息
        List<UserBaseInfoDTO> userList = userClient.getUserByIdsAndOrgId(New.list(departmentInfo.getManagerId(), orderMaster.getFbuyerid()), request.getOrgId());
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(userList), ExecptionMessageEnum.OBTAIN_FUNDING_CARD_FAILED_NO_PI_INFO);
        Map<Integer, String> jobNumberDictionary = DictionaryUtils.toMap(userList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getJobnumber);

        List<OrderFundCardDTO> orderFundCardCache = orderFundCardCacheClient.findOrderFundCardCache(New.list(request.getOrderId()));
        Set<String> fundCardIdSet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(orderFundCardCache)) {
            // 暨大的目前 项目和经费卡是一个维度，项目 == 经费卡
            fundCardIdSet = orderFundCardCache.stream().map(OrderFundCardDTO::getFundCardId).collect(Collectors.toSet());
        } else {
            // 没有换卡缓存，去找旧的fundcard_order
            List<RefFundcardOrderDTO> refFundcardOrder = refFundcardOrderService.findByOrderId(New.list(request.getOrderId().toString()));
            if (CollectionUtils.isNotEmpty(refFundcardOrder)) {
                fundCardIdSet = refFundcardOrder.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toSet());
            }
        }

        // 部门pi工号
        String piJobNumber = jobNumberDictionary.get(departmentInfo.getManagerId()) == null ? "" : jobNumberDictionary.get(departmentInfo.getManagerId());
        // 部门员工工号
        String buyerJobNumber = jobNumberDictionary.get(orderMaster.getFbuyerid()) == null ? "" : jobNumberDictionary.get(orderMaster.getFbuyerid());

        // 查找经费卡信息, 分页查询
        List<FundCardDTO> fundSummary = researchFundCardServiceClient.listFundCardByJobNumber(request.getOrgCode(), New.list(piJobNumber), buyerJobNumber, request.getPageNo(), request.getPageSize());

        List<OrderFundCardResponseDTO> data = new ArrayList<>();
        OrderFundCardResponseDTO item = null;

        // 目前只有暨大将经费项目和经费卡归属为同一个维度，即经费项目就是经费卡，项目编号对应经费卡号
        for (FundCardDTO summary : fundSummary) {
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == (summary.getLevel())) {
                item = new OrderFundCardResponseDTO();
                item.setProjectId(summary.getId());
                item.setProjectCode(summary.getCode());
                item.setProjectName(summary.getName());

                // 设置经费卡负责人
                List<FundCardManagerDTO> managerList = summary.getFundCardManagerDTOs();
                if (CollectionUtils.isNotEmpty(managerList)) {
                    Optional<String> manager = managerList.stream()
                            .filter(f -> f.getManagerName() != null)
                            .map(FundCardManagerDTO::getManagerName).reduce((n1, n2) -> {
                                n1 = ";" + n2;
                                return n1;
                            });
                    item.setProjectManager(manager.orElseGet(() -> ""));
                } else {
                    item.setProjectManager("");
                }

                // 设置选中的经费卡
                if (fundCardIdSet.contains(summary.getId())) {
                    item.setSelected(Boolean.TRUE);
                }

                data.add(item);
            }
        }
        return RemoteResponse.<List<OrderFundCardResponseDTO>>custom().setSuccess().setData(data).build();
    }

    @Override
    public RemoteResponse<OrderFundCardResponseDTO> findFundCardCacheByOrderId(OrderBasicParamDTO param) {
        Assert.isTrue(param.getOrderId() != null, "查询经费卡缓存失败！订单id为空");
        List<OrderFundCardDTO> fundCardCache = orderFundCardCacheClient.findOrderFundCardCache(Arrays.asList(param.getOrderId()));
        if (CollectionUtils.isEmpty(fundCardCache)) {
            return RemoteResponse.<OrderFundCardResponseDTO>custom().setSuccess().build();
        }

        // 换卡缓存
        OrderFundCardDTO cardDto = fundCardCache.get(0);
        OrderFundCardResponseDTO result = new OrderFundCardResponseDTO();
        result.setFundCardId(cardDto.getFundCardId());
        result.setFundCardNo(cardDto.getFundCardNo());
        result.setProjectCode(cardDto.getFundCardCode());
        result.setReferenceAmount(cardDto.getFreezeAmount());

        return RemoteResponse.<OrderFundCardResponseDTO>custom().setSuccess().setData(result).build();
    }

    @Override
    public RemoteResponse<List<RefOrderDetailTagResponseDTO>> findRefOrderDetailTag(RefOrderDetailTagRequestDTO param) {
        Assert.notEmpty(param.getRefIds(), "查询失败！refId为空！");
        List<RefOrderDetailTagDO> queryResult = orderDetailTagDOMapper.findByRefIdIn(param.getRefIds());

        List<RefOrderDetailTagResponseDTO> result = RefOrderDetailTagTranslator.doToDto(queryResult);
        return RemoteResponse.<List<RefOrderDetailTagResponseDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 根据经费卡id集合查订单id集合
     *
     * @param cardIds
     * @return
     */
    @Override
    public RemoteResponse<List<String>> findOrderByCardIds(List<String> cardIds) {
        List<String> orderIds = refFundcardOrderService.findOrderIdsByCardIds(cardIds);
        return RemoteResponse.<List<String>>custom().setData(orderIds).setSuccess().build();
    }

    /**
     * 获取采购或者订单 于经费卡条数
     *
     * @param orderQueryDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.READ)
    public RemoteResponse<Integer> findRefFundCardOrderCount(FundCardAndOrderQueryDTO orderQueryDTO) {
        Assert.notNull(orderQueryDTO, "orderQueryDTO 不能为空");
        Assert.notNull(orderQueryDTO.getRefFundCardOrderTypeEnum(), "OrderTypeEnum 不能为空");
        Assert.isTrue(orderQueryDTO.getBusinessId() != null && orderQueryDTO.getBusinessId() != 0, "OrderId 不能为空");

        RemoteResponse<Integer> resultResponse = RemoteResponse.<Integer>custom().setData(0).setSuccess().build();
        List<RefFundcardOrderDO> refFundcardOrderDOList = null;
        if (RefFundCardOrderTypeEnum.PURCHASE_TYPE_ENUM.equals(orderQueryDTO.getRefFundCardOrderTypeEnum())) {
            refFundcardOrderDOList = refFundcardOrderService.findByApplicationId(orderQueryDTO.getBusinessId().toString());
        } else if (RefFundCardOrderTypeEnum.ORDER_TYPE_ENUM.equals(orderQueryDTO.getRefFundCardOrderTypeEnum())) {
            refFundcardOrderDOList =refFundcardOrderService.findRefundcardOrderByOrderId(orderQueryDTO.getBusinessId().toString());
        }else if (RefFundCardOrderTypeEnum.Bid_TYPE_ENUM.equals(orderQueryDTO.getRefFundCardOrderTypeEnum())) {
            refFundcardOrderDOList =refFundcardOrderService.findByBidId(orderQueryDTO.getBusinessId().toString());
        }else {
            refFundcardOrderDOList = Lists.newArrayList();
        }

        //处理选人不选卡情况
        List<String> cards = refFundcardOrderDOList.stream().filter(c -> StringUtils.isNotBlank(c.getCardId())).map(RefFundcardOrderDO::getCardId).collect(Collectors.toList());
        resultResponse.setData(cards.size());
        return resultResponse;
    }

    @Override
    public RemoteResponse saveFundCardCommon(OrderChangeCommonDTO request) {
        return refFundcardOrderService.changeFundCardCommon(request.getRefFundCardOrderList(), request.getOrderOperatorDTO(), false, true ,StringUtils.EMPTY);
    }

    @Override
    public RemoteResponse<Integer> updateRefFundcardOrderByApplicationId(RefFundcardOrderDTO refFundcardOrderDTO) {
        Integer result = refFundcardOrderService.updateRefFundCardOrderByApplicationId(refFundcardOrderDTO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    @Override
    public RemoteResponse<Integer> updateRefFundcardOrderByBid(RefFundcardOrderDTO refFundcardOrderDTO) {
        Integer result = refFundcardOrderService.updateRefFundCardOrderByBId(refFundcardOrderDTO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    @Override
    @ServiceLog(description = "查询审批日志")
    public RemoteResponse<List<OrderApprovalLogDTO>> findByOrderIdListAndStatus(OrderApprovalRequestDTO request) {
        List<OrderApprovalLogDTO> result = orderApprovalLogService.findByOrderIdListAndStatus(request);
        return RemoteResponse.<List<OrderApprovalLogDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<TimeoutStatisticsDTO>> findTimeOutDepartmentByOrgIdAndDepartmentId(TimeOutOrderParamsDTO params) {

        Integer orgId = params.getUserId();

        List<Integer> departmentIds = params.getDepartmentIds();
        Assert.notNull(orgId, "查询超时统计数据失败！机构id为空！");
        Assert.notEmpty(departmentIds, "查询超时统计数据失败！课题组id为空！");
        //采购部门限制（默认开启）
        boolean isDeptLimitOn = true;

        List<String> timeoutCode = Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(toList());
        // 查询用户/机构的有效配置
        Map<String, String> timeOutCodeMap = sysConfigClient.getConfigMapByOrgIdAndConfigCode(New.list(orgId), timeoutCode);

        isDeptLimitOn = "1".equals(timeOutCodeMap.getOrDefault(TimeOutConfigType.ORDER_TIMEOUT_DEPT_LIMIT.getCode(), "1"));

        String deptLimit = timeOutCodeMap.get(TimeOutConfigType.ORDER_TIMEOUT_DEPT_LIMIT.getCode());

        //课题组超时张数
        int deptExamineLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode()));
        int deptBalanceLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode()));

        //超时天数
        Integer examineLimitDay = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode()));
        Integer balanceLimitDay = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()));
        if("0".equals(deptLimit)){
            return RemoteResponse.<List<TimeoutStatisticsDTO>>custom().setSuccess().setData(New.list()).build();
        }
        Map<Integer, Integer> deptId2BalanceCountMap = timeoutQueryService.handleBalance(false, isDeptLimitOn, orgId, null, balanceLimitDay, deptBalanceLimitAmount, null, departmentIds, New.list(), TimeoutQueryService.ValidationScopeEnum.ONLY_CHECK_DEPT, false);
        Map<Integer, Integer> deptId2AcceptanceCountMap = timeoutQueryService.handleAcceptance(false, isDeptLimitOn, orgId, null, examineLimitDay, deptExamineLimitAmount, null, departmentIds, New.list(), TimeoutQueryService.ValidationScopeEnum.ONLY_CHECK_DEPT, false);
        deptId2BalanceCountMap.putAll(deptId2AcceptanceCountMap);
        List<TimeoutStatisticsDTO> result = New.list();
        for (Map.Entry<Integer, Integer> entry : deptId2BalanceCountMap.entrySet()){
            TimeoutStatisticsDTO timeoutStatisticsDTO = new TimeoutStatisticsDTO();
            timeoutStatisticsDTO.setOrgId(orgId);
            timeoutStatisticsDTO.setDepId(entry.getKey());
            timeoutStatisticsDTO.setType(TimeOutBusinessType.BALANCE.getValue());
            timeoutStatisticsDTO.setAmount(entry.getValue());
            timeoutStatisticsDTO.setOldConfigDay(examineLimitDay);
            timeoutStatisticsDTO.setOldConfigAmount(deptExamineLimitAmount);
            result.add(timeoutStatisticsDTO);
        }

        for (Map.Entry<Integer, Integer> entry : deptId2AcceptanceCountMap.entrySet()){
            TimeoutStatisticsDTO timeoutStatisticsDTO = new TimeoutStatisticsDTO();
            timeoutStatisticsDTO.setOrgId(orgId);
            timeoutStatisticsDTO.setDepId(entry.getKey());
            timeoutStatisticsDTO.setType(TimeOutBusinessType.ACCEPTANCE.getValue());
            timeoutStatisticsDTO.setAmount(entry.getValue());
            timeoutStatisticsDTO.setOldConfigDay(examineLimitDay);
            timeoutStatisticsDTO.setOldConfigAmount(deptExamineLimitAmount);
            result.add(timeoutStatisticsDTO);
        }

        return RemoteResponse.<List<TimeoutStatisticsDTO>>custom().setSuccess().setData(result).build();
    }

    @Override
    @ServiceLog(description = "供应商操作订单，推送订单状态到第三方平台", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> updateThirdPlatformOrderForSupplier(UpdateOrderParamDTO request) {
        orderMasterForTPIService.updateThirdPlatformOrder(request);
        return RemoteResponse.<Boolean>custom().setSuccess();
    }


    @Override
    @ServiceLog(description = "根据订单号查询华农经费信息 ")
    public RemoteResponse<List<HuaNongFundInfoDTO>> findByFundInfoByOrderNoList(OrderMasterCommonReqDTO request) {
        List<String> orderNoList = request.getOrderMasterNoList();
        BusinessErrUtil.notEmpty(orderNoList, ExecptionMessageEnum.ORDER_NO_RECORDED_NUMBER);

        final List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(orderNoList);
        final Set<Integer> applicationIdSet = orderList.stream().map(OrderMasterDO::getFtbuyappid).collect(Collectors.toSet());
        final Map<Integer, List<PurchaseApprovalLogDTO>> applicationIdItemMap = purchaseApprovalLogClient.getApprovalLogMapByIdList(applicationIdSet);

        Map<String, List<PurchaseApprovalLogDTO>> orderNoPurchaseLogMap = new HashMap<>(orderList.size());
        for (OrderMasterDO orderItem : orderList) {
            orderNoPurchaseLogMap.put(orderItem.getForderno(), applicationIdItemMap.get(orderItem.getFtbuyappid()));
        }

        List<BusinessDockingDTO> businessDockingList = businessDockingRPCClient.getBusinessDockingOrderInfoList(orderNoList);
        List<HuaNongFundInfoDTO> collect = businessDockingList.stream().map(businessDocking -> {
            return this.translatorFundInfoDTO(businessDocking, orderNoPurchaseLogMap);
        }).collect(Collectors.toList());
        return RemoteResponse.<List<HuaNongFundInfoDTO>>custom().setSuccess().setData(collect);
    }

    private HuaNongFundInfoDTO translatorFundInfoDTO(BusinessDockingDTO businessDockingDTO, Map<String, List<PurchaseApprovalLogDTO>> orderNoPurchaseLogMap) {
        HuaNongFundInfoDTO result = new HuaNongFundInfoDTO();
        result.setBusinessNo(businessDockingDTO.getBusinessOrderNo());
        result.setDockingNo(businessDockingDTO.getDockingNo());

        if (StringUtils.isNotBlank(businessDockingDTO.getExtraJson())) {
            List<Map<String, String>> extraMapList = JsonUtils.fromJson(businessDockingDTO.getExtraJson(), List.class);
            for (Map<String, String> map : extraMapList) {
                String fieldProperty = map.get("field");
                String valueProperty = map.get("value");
                if (("fundCardCode".equals(fieldProperty))) {
                    result.setFundCardCode(valueProperty);
                    continue;
                }

                if (("fundCardManagerName".equals(fieldProperty))) {
                    result.setFundCardManagerName(valueProperty);
                    continue;
                }

                if (("operatorName".equals(fieldProperty))) {
                    result.setOperatorName(valueProperty);
                    continue;
                }

                if (("receiverName".equals(fieldProperty))) {
                    result.setReceiverName(valueProperty);
                    continue;
                }

                if (("approverName".equals(fieldProperty))) {
                    result.setApproveName(valueProperty);
                }
            }
        }

        result.setOrgId(businessDockingDTO.getOrgId());
        result.setOrgCode(businessDockingDTO.getOrgCode());
        result.setRemark(businessDockingDTO.getRemark());

        final List<PurchaseApprovalLogDTO> purchaseApprovalLogDTOS = orderNoPurchaseLogMap.get(businessDockingDTO.getBusinessOrderNo());
        if (CollectionUtils.isEmpty(purchaseApprovalLogDTOS)) {
            return result;
        }
        final Date approvedDate = purchaseApprovalLogDTOS.stream()
                .sorted((o1, o2) -> o2.getApproveLevel().compareTo(o1.getApproveLevel()))
                .findFirst()
                .map(PurchaseApprovalLogDTO::getApproveTime)
                .orElseGet(() -> null);
        final Date purchasedDate = purchaseApprovalLogDTOS.stream()
                .sorted((o1, o2) -> o1.getApproveLevel().compareTo(o2.getApproveLevel()))
                .findFirst()
                .map(PurchaseApprovalLogDTO::getApproveTime)
                .orElseGet(() -> null);

        result.setApprovedDate(approvedDate);
        result.setPurchasedDate(purchasedDate);

        return result;
    }

    @Override
    @ServiceLog(description = "查询订单备案信息")
    public RemoteResponse<List<OrderConfirmForTheRecordDTO>> findOrderConfirmByOrderIdList(OrderMasterCommonReqDTO request) {
        List<Integer> orderMasterIds = request.getOrderMasterIds();
        Preconditions.notEmpty(orderMasterIds, "订单id不可以为空！");
        List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOList = orderConfirmForTheRecordDOMapper.findByOrderIdIn(orderMasterIds);
        List<OrderConfirmForTheRecordDTO> result = orderConfirmForTheRecordDOList.stream().map(OrderConfirmForTheRecordTranslator::doToDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderConfirmForTheRecordDTO>>custom().setSuccess().setData(result);
    }

    /**
     * @param deptIdList
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<java.lang.Boolean>
     * @description: 检查在部门列表任一部门中是否存在有效订单, true表示有，false表示没有（当前有效订单表示全部状态订单）
     * @date: 2021/4/6 14:05
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "检查在部门列表任一部门中是否存在有效订单", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> checkDeptHasOrder(List<Integer> deptIdList) {
        Preconditions.notEmpty(deptIdList, "检查在部门列表任一部门中是否存在订单入参不可为空");
        Request request = new Request();
        request.addFilter(new TermFilter("fbuydepartmentid", deptIdList));
        long count = orderSearchBoostService.searchCountByRequest(request);
        Boolean countCheck = !Objects.equals(count, 0L);
        return RemoteResponse.<Boolean>custom().setData(countCheck).setSuccess();
    }
}
