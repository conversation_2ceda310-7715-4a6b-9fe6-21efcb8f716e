package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-06-18 16:40
 * @description: 订单列表tag枚举
 */
public enum OrderListTagEnum {

    /**
     * 中大附一用
     */
    NOT_DOCKING_ORDER("非对接单"),

    /**
     * 暨大用
     */
    COMPLETE_REIMBURSEMENT("已报账"),
    ;

    private final String tagName;

    OrderListTagEnum(String tagName) {
        this.tagName = tagName;
    }

    public String getTagName() {
        return tagName;
    }
}
