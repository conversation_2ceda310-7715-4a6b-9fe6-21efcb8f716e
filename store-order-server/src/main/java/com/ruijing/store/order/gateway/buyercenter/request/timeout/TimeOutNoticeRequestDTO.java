package com.ruijing.store.order.gateway.buyercenter.request.timeout;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2024-03-04 10:58
 * @description:
 **/
public class TimeOutNoticeRequestDTO implements Serializable {

    private static final long serialVersionUID = 816557328894476165L;

    private Integer orgId;

    private List<TimeOutNoticeItemDTO> dtos;

    /**
     * 结算/验收 -0 | 结算 -1 | 验收 -2
     */
    private Integer batchType;

    public Integer getOrgId() {
        return orgId;
    }

    public TimeOutNoticeRequestDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public List<TimeOutNoticeItemDTO> getDtos() {
        return dtos;
    }

    public TimeOutNoticeRequestDTO setDtos(List<TimeOutNoticeItemDTO> dtos) {
        this.dtos = dtos;
        return this;
    }

    public Integer getBatchType() {
        return batchType;
    }

    public TimeOutNoticeRequestDTO setBatchType(Integer batchType) {
        this.batchType = batchType;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TimeOutNoticeRequestDTO.class.getSimpleName() + "[", "]")
                .add("orgId=" + orgId)
                .add("dtos=" + dtos)
                .add("batchType=" + batchType)
                .toString();
    }
}
