package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.rule.dto.PurchaseRuleRequestDTO;
import com.ruijing.store.rule.dto.PurchaseRuleResultDTO;
import com.ruijing.store.rule.service.PurchaseRuleRpcService;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-07 16:46
 */
@ServiceClient
public class StoreRuleClient {

    @MSharpReference(remoteAppkey = "store-rule-service")
    private PurchaseRuleRpcService purchaseRuleRpcService;

    /**
     * 采购规则校验
     * @param purchaseRuleRequestDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "采购规则校验")
    public List<PurchaseRuleResultDTO> purchaseRulesVerify(PurchaseRuleRequestDTO purchaseRuleRequestDTO){
        if(CollectionUtils.isEmpty(purchaseRuleRequestDTO.getRuleProductVerificationList())){
            return Collections.emptyList();
        }
        RemoteResponse<List<PurchaseRuleResultDTO>> remoteResponse = purchaseRuleRpcService.purchaseRulesVerify(purchaseRuleRequestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), "校验采购规则失败" + remoteResponse.getMsg());
        return remoteResponse.getData();
    }

}
