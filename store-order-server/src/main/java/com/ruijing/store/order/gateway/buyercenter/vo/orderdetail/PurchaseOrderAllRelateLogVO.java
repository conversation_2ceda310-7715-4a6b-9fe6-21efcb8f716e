package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.OperateLogBussinessTypeEnum;

import java.io.Serializable;
import java.util.Date;

@RpcModel("订单关联采购所有日志")
public class PurchaseOrderAllRelateLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("日志ID")
    private Integer id;

    @RpcModelProperty("订单ID")
    private Integer orderId;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("采购单号/竞价单号")
    private String purchaseBidNo;

    @RpcModelProperty("结算单号")
    private String settlementNo;

    @RpcModelProperty("操作类型")
    private String operationType;

    @RpcModelProperty("操作人")
    private String operatorName;

    @RpcModelProperty("操作时间")
    private Date createTime;

    @RpcModelProperty(value = "日志业务类型", enumClass = OperateLogBussinessTypeEnum.class)
    private Integer businessType;

    @RpcModelProperty("备注")
    private String remark;

    public Integer getId() {
        return id;
    }

    public PurchaseOrderAllRelateLogVO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public PurchaseOrderAllRelateLogVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public PurchaseOrderAllRelateLogVO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getPurchaseBidNo() {
        return purchaseBidNo;
    }

    public PurchaseOrderAllRelateLogVO setPurchaseBidNo(String purchaseBidNo) {
        this.purchaseBidNo = purchaseBidNo;
        return this;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public PurchaseOrderAllRelateLogVO setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
        return this;
    }

    public String getOperationType() {
        return operationType;
    }

    public PurchaseOrderAllRelateLogVO setOperationType(String operationType) {
        this.operationType = operationType;
        return this;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public PurchaseOrderAllRelateLogVO setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public PurchaseOrderAllRelateLogVO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public PurchaseOrderAllRelateLogVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public PurchaseOrderAllRelateLogVO setBusinessType(Integer businessType) {
        this.businessType = businessType;
        return this;
    }

    @Override
    public String toString() {
        return "PurchaseOrderAllRelateLogVO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", purchaseBidNo='" + purchaseBidNo + '\'' +
                ", settlementNo='" + settlementNo + '\'' +
                ", operationType='" + operationType + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", createTime=" + createTime +
                ", businessType='" + businessType + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}