package com.ruijing.store.order.rpc.callback;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.financial.docking.dto.order.OrderDTO;
import com.reagent.research.financial.docking.dto.order.OrderQueryDTO;
import com.reagent.research.financial.docking.dto.order.OrderStatusQueryResult;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.tpi.tpiclient.api.order.v2.OrderStatusUpdateCallbackService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.goodsreturn.constant.GoodsReturnConstant;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyDetailRequestDTO;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidEnum;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 第三方订单更新回调对接
 *
 * <AUTHOR>
 * @Date 2020/8/10 10:10 上午
 */
@MSharpService
public class OrderStatusUpdateCallbackServiceImpl implements OrderStatusUpdateCallbackService {
    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Resource
    private WalletOrderRpcClient walletOrderRpcClient;

    @Resource
    private UserClient userClient;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Resource
    private TPIOrderClient tpiOrderClient;

    @Resource
    private CommonGoodsReturnService commonGoodsReturnService;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private DockingExtraService dockingExtraService;

    @ServiceLog(description = "订单状态更新回调接口")
    @Override
    public RemoteResponse<Boolean> handleOrderStatusUpdateResult(CallbackRequest<OrderStatusQueryResult> response) {
        OrderStatusQueryResult orderData = response.getData();
        String orderNo = orderData.getOrderNo();
        Preconditions.notNull(orderNo, "订单状态更新回调失败！订单号为空！");
        // 订单状态更新回调策略
        return RemoteResponse.<Boolean>custom().setSuccess().setData(this.syncOrderStatusStrategy(response));
    }

    public boolean syncOrderStatusStrategy(CallbackRequest<OrderStatusQueryResult> response) {
        String orgCode = response.getOrgCode();
        String orderNo = response.getData().getOrderNo();
        orderOtherLogClient.createOrderDockingLog(orderNo, orgCode, null, "code:" + response.getCode() + ":" + response.getMsg(), "订单状态更改回调", "");

        boolean callbackSuccess = response.isSuccess();
        // 记录单号对接信息
        dockingExtraService.saveOrUpdateDockingExtra(orderNo, null, callbackSuccess, response.getMsg());

        // 目前只有华农需要回调然后同步订单
        if (OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orgCode)) {
            // 获取第三方业务对接订单信息
            BusinessDockingDTO businessDockingOrderInfo = businessDockingRPCClient.getBusinessDockingOrderInfo(orderNo);
            Preconditions.notNull(businessDockingOrderInfo, "回调更新订单失败，查无关联订单关系");
            String extraOrderNo = businessDockingOrderInfo.getDockingNo();
            OrderMasterDO orderInfo = orderMasterMapper.findByForderno(orderNo);
            if (orderInfo == null) {
                LOGGER.warn("订单还未生成，无需处理！");
                return false;
            }
            // 获取对接订单信息
            CompletableFuture<RemoteResponse<OrderDTO>> dockingOrderInfoFuture = this.getThirdPartyPlatformOrderInfo(orderInfo, extraOrderNo);
            dockingOrderInfoFuture.whenCompleteAsync((remoteResponse, throwable) -> {
                if (throwable != null) {
                    orderOtherLogClient.createOrderDockingLog(orderInfo.getForderno(), orderInfo.getFusercode(), null, JsonUtils.toJsonIgnoreNull(response), "订单状态更改回调异常", "失败");
                    throw new IllegalStateException(throwable);
                } else {
                    this.syncStoreOrder(orderInfo, remoteResponse.getData(), response.getMsg());
                }
            }, AsyncExecutor.getListenableExecutorService());
        }
        return true;
    }

    /**
     * 同步锐竞商城订单状态
     * @param orderInfo
     * @param failedReason
     * @param dockingOrderInfo
     */
    public void syncStoreOrder(OrderMasterDO orderInfo, OrderDTO dockingOrderInfo, String failedReason) {
        Preconditions.notNull(dockingOrderInfo, "查询第三方订单信息失败！查无订单");
        UpdateOrderParamDTO updateOrderParam = new UpdateOrderParamDTO();
        String orderNo = orderInfo.getForderno();
        updateOrderParam.setOrderNo(orderNo);
        Date nowDate = new Date();
        this.updateOrderStatusProcess(orderInfo, dockingOrderInfo, failedReason, updateOrderParam, orderNo, nowDate);
    }

    private void updateOrderStatusProcess(OrderMasterDO orderInfo, OrderDTO dockingOrderInfo, String failedReason, UpdateOrderParamDTO updateOrderParam, String orderNo, Date nowDate) {
        final Integer status = dockingOrderInfo.getStatus();
        LOGGER.info("成功收到回调：订单号{},第三方平台的订单状态为{}", orderNo, status);
        switch (status) {
            case DockingConstant.CLOSE:
                updateOrderParam.setShutDownDate(nowDate);
                updateOrderParam.setStatus(OrderStatusEnum.Close.getValue());
                updateOrderParam.setFundStatus(OrderFundStatusEnum.ThrawSuccessed.getValue());
                break;

            case DockingConstant.WAITING_FOR_CONFIRM:
                updateOrderParam.setStatus(OrderStatusEnum.WaitingForConfirm.getValue());
            case DockingConstant.PURCHASE_APPLY_TO_CANCEL:
                ApplyCancelOrderReqDTO request = new ApplyCancelOrderReqDTO();
                request.setOrderId(orderInfo.getId());
                request.setFcancelreason("第三方对接采购人申请取消订单");
                cancelOrderManageService.cancelOrder(request);
                return;
            case DockingConstant.WAITING_STATEMENT:
                if (!OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderInfo.getStatus())) {
                    updateOrderParam.setLastReceiveDate(nowDate);
                    updateOrderParam.setStatus(OrderStatusEnum.WaitingForStatement_1.getValue());
                    // 进入扣费队列
                    walletOrderRpcClient.addOrderWalletQueue(orderInfo);
                    // 提前更新订单状态，方便根据订单状态去定时轮询华农的单
                    updateFundCardInfo(orderNo, dockingOrderInfo, OrderStatusEnum.WaitingForStatement_1.getValue());
                    break;
                }

            case DockingConstant.RETURNING:
                this.updateReturnInfo(orderInfo, orderNo);
                break;

            case DockingConstant.PAID:
                // 已付款
                updateOrderParam.setStatus(OrderStatusEnum.Statementing_1.getValue());
                // 更新经费卡信息
                updateFundCardInfo(orderNo, dockingOrderInfo, OrderStatusEnum.Statementing_1.getValue());
                // 生成结算单代开票状态, 只有未生成结算单时才生成，避免重复生成结算单
                if (orderInfo.getStatementId() == null) {
                    final StatementResultDTO statement = researchStatementClient.createStatementSingle(orderInfo.getFbuyerid(), orderInfo.getFbuyername(), orderInfo);
                    if (statement != null) {
                        updateOrderParam.setStatementId(statement.getId().intValue());
                        updateOrderParam.setInStateTime(nowDate);
                    }
                }
                break;

            case DockingConstant.FINISH:
                updateOrderParam.setStatus(OrderStatusEnum.Finish.getValue());

            case DockingConstant.FAILURE:
                // 失败原因
                updateOrderParam.setFailedReason(failedReason);
                break;

            default:
                LOGGER.warn("订单状态为{}，无意义的状态，无需处理！", status);
                return;
        }
        if (updateOrderParam.getStatus() != null || updateOrderParam.getFailedReason() != null) {
            orderMasterMapper.updateOrderByOrderNo(updateOrderParam);
        }
    }

    /**
     * 更新退货信息
     * @param orderInfo 订单master信息
     * @param orderNo   订单号
     */
    private void updateReturnInfo(OrderMasterDO orderInfo, String orderNo) {
        Integer orderId = orderInfo.getId();
        Preconditions.notNull(orderId, "订单状态更新回调失败！无效的订单号: " + orderNo);

        // 生成退货申请单
        List<OrderDetailDO> orderDetailInfoList = orderDetailMapper.findByFmasterid(orderId);
        List<GoodsReturn> goodsReturnList = generateGoodsReturns(orderInfo, orderDetailInfoList);
        transactionTemplate.execute(transactionStatus -> {
            if (CollectionUtils.isNotEmpty(orderDetailInfoList)) {
                orderDetailMapper.loopUpdateByIdIn(orderDetailInfoList);
            }

            if (CollectionUtils.isNotEmpty(goodsReturnList)) {
                goodsReturnMapper.insertList(goodsReturnList);
            }
            return transactionStatus.isCompleted();
        });
    }

    /**
     * 校验幂等，生成退货单
     * @param orderInfo
     * @param orderDetailInfoList
     * @return
     */
    private List<GoodsReturn> generateGoodsReturns(OrderMasterDO orderInfo, List<OrderDetailDO> orderDetailInfoList) {
        boolean existingGoodsReturn = orderDetailInfoList.stream().anyMatch(detail -> OrderCommonUtils.havingReturn(detail));
        Integer orgId = orderInfo.getFuserid();
        if (GoodsReturnConstant.ALL_RETURN_ORG.containsKey(orgId) && existingGoodsReturn) {
            LOGGER.info("退货单已存在，Ignore");
            return New.emptyList();
        }
        List<GoodsReturn> goodsReturnList = new ArrayList<>(orderDetailInfoList.size());
        for (int i = 0; i < orderDetailInfoList.size(); i++) {
            OrderDetailDO orderDetailItem = orderDetailInfoList.get(i);
            orderDetailItem.setReturnStatus(OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode());
            GoodsReturn goodsReturn = new GoodsReturn();
            goodsReturn.setApplyName(orderInfo.getFbuyername());
            goodsReturn.setUserId(orderInfo.getFbuyerid());
            goodsReturn = parseGoodsParams(orderInfo, orderDetailItem, goodsReturn);

            goodsReturnList.add(goodsReturn);
        }
        return goodsReturnList;
    }

    /**
     * 更新华农的经费编号信息
     * @param orderDTO
     */
    private void updateFundCardInfo(String orderNo, OrderDTO orderDTO, Integer status) {
        List<ExtraDTO> extraDTOList = orderDTO.getExtraDTOs();
        // 扩展字段增加一个经办人存到数据库
        ExtraDTO operator = new ExtraDTO();
        operator.setField("operatorName");
        operator.setValue(orderDTO.getBuyerName());
        extraDTOList.add(operator);

        String extraJSON = JsonUtils.toJson(extraDTOList);
        BusinessDockingDTO request = new BusinessDockingDTO();
        request.setExtraJson(extraJSON);
        request.setBusinessOrderNo(orderNo);
        request.setReagentStatus(status);
        businessDockingRPCClient.updateByBusinessNo(request);
    }

    /**
     * 获取第三方对接平台的订单状态
     * @return
     */
    public CompletableFuture<RemoteResponse<OrderDTO>> getThirdPartyPlatformOrderInfo(OrderMasterDO orderInfo, String extraOrderNo) {
        OrgRequest<OrderQueryDTO> request = new OrgRequest<>();
        OrderQueryDTO queryDTO = new OrderQueryDTO();
        queryDTO.setOrderNo(orderInfo.getForderno());
        queryDTO.setExtraOrderNo(extraOrderNo);

        String orgCode = orderInfo.getFusercode();
        if (OrgEnum.HUA_NAN_NONG_YE_DA_XUE.equals(orgCode)) {
            List<ExtraDTO> extraDTOList = new ArrayList<>();
            ExtraDTO extraDTO = new ExtraDTO();
            UserBaseInfoDTO userInfo = userClient.getUserInfo(orderInfo.getFbuyerid(), orderInfo.getFuserid());
            Preconditions.notNull(userInfo, "查询第三方平台订单信息失败，订单查无采购人");

            extraDTO.setField("jobNumber");
            extraDTO.setValue(userInfo.getJobnumber());
            extraDTOList.add(extraDTO);

            extraDTO = new ExtraDTO();
            extraDTO.setField("deptName");
            extraDTO.setValue(orderInfo.getFbuydepartment());
            extraDTOList.add(extraDTO);

            extraDTO = new ExtraDTO();
            extraDTO.setField("orderCreationTime");
            extraDTO.setValue(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderInfo.getForderdate()));
            extraDTOList.add(extraDTO);

            extraDTO = new ExtraDTO();
            extraDTO.setField("orderPrice");
            extraDTO.setValue(orderInfo.getForderamounttotal().toString());
            extraDTOList.add(extraDTO);

            extraDTO = new ExtraDTO();
            extraDTO.setField("suppId");
            extraDTO.setValue(orderInfo.getFsuppid().toString());
            extraDTOList.add(extraDTO);
            queryDTO.setExtraDTOs(extraDTOList);
        }

        request.setData(queryDTO);
        request.setOrgCode(orgCode);

        return tpiOrderClient.getOrderInfo(request);
    }

    /**
     * 解析退货商品信息
     * @param masterDo 主订单
     * @param detailDo 订单商品
     * @param params   入参
     * @return
     */
    private GoodsReturn parseGoodsParams(OrderMasterDO masterDo, OrderDetailDO detailDo, GoodsReturn params) {
        GoodsReturn dto = new GoodsReturn();
        Date nowDate = new Date();
        dto.setGoodsReturnStatus(OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode());
        dto.setCreationTime(nowDate);
        dto.setUpdateTime(nowDate);
        dto.setApplyName(params.getApplyName());
        dto.setUserId(params.getUserId() != null ? params.getUserId() : -1);
        dto.setSupplierId(masterDo.getFsuppid());
        dto.setSupplierName(masterDo.getFsuppname());
        dto.setRemark(params.getRemark());
        dto.setReturnReason(params.getReturnReason());
        dto.setOrderId(masterDo.getId());
        dto.setOrderNo(masterDo.getForderno());
        dto.setOrderStatus(masterDo.getStatus());
        dto.setOrgId(masterDo.getFuserid());
        dto.setOrgName(masterDo.getFusername());
        dto.setDepartmentId(masterDo.getFbuydepartmentid());
        dto.setDepartmentName(masterDo.getFbuydepartment());
        dto.setInvalid(GoodsReturnInvalidEnum.NORMAL.getCode());
        dto.setBuyerName(masterDo.getFbuyername());

        // todo
        List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList = null;
        Preconditions.notEmpty(returnApplyDetailList, "退货商品详情不可为空！");
        dto.setGoodsReturnDetailJSON(JsonUtils.toJson(returnApplyDetailList));
        dto.setGoodsReturnDetailJSON(null);
        // 生成returnNo 的算法
        if (params.getId() == null && StringUtils.isBlank(params.getReturnNo())) {
            dto.setReturnNo(commonGoodsReturnService.createReturnNo(masterDo.getForderno()));
        } else {
            dto.setReturnNo(params.getReturnNo());
        }
        return dto;
    }

}
