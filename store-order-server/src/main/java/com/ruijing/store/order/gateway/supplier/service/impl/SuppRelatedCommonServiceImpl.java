package com.ruijing.store.order.gateway.supplier.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.gateway.supplier.service.SuppRelatedCommonService;
import com.ruijing.store.order.gateway.supplier.vo.SuppInfoVO;
import com.ruijing.store.order.rpc.client.SuppClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/1 16:00
 * @description
 */
@Service
public class SuppRelatedCommonServiceImpl implements SuppRelatedCommonService {

    @Resource
    private SuppClient suppClient;

    @Override
    public List<SuppInfoVO> fuzzySearchSuppInfoByName(String name) {
        List<com.ruijing.shop.srm.api.dto.supp.TcardSupplyerInfoDTO> suppDtoList = suppClient.fuzzySearchSuppInfoByName(name);
        if(CollectionUtils.isEmpty(suppDtoList)){
            return New.emptyList();
        }
        return suppDtoList.stream().map(this::tcardSupplyerInfoDto2Vo).collect(Collectors.toList());
    }

    private SuppInfoVO tcardSupplyerInfoDto2Vo(com.ruijing.shop.srm.api.dto.supp.TcardSupplyerInfoDTO tcardSupplyerInfoDTO){
        SuppInfoVO suppInfoVO = new SuppInfoVO();
        suppInfoVO.setSuppId(tcardSupplyerInfoDTO.getSupplierId());
        suppInfoVO.setSuppName(tcardSupplyerInfoDTO.getSuppName());
        return suppInfoVO;
    }
}
