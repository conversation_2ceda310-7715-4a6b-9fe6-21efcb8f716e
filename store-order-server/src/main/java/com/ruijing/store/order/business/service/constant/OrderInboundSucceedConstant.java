package com.ruijing.store.order.business.service.constant;

import com.ruijing.fundamental.common.collections.MapBuilder;

import java.util.Map;

/**
 * @description: 订单出入库常量类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-01-14 16:00
 */
public class OrderInboundSucceedConstant {

    /**
     * 入库回调后缀
     */
    public static final String INBOUND_CALLBACK_SUFFIX = "_INBOUND_CALLBACK_SUFFIX";

    /**
     * 孙逸仙医院
     */
    public static final String ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN = "ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN";

    /**
     * 孙逸仙医院
     */
    public static final String ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_INBOUND_CALLBACK = ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN + INBOUND_CALLBACK_SUFFIX;

    private static final Map<String, String> orderInboundCallbackStrategy = MapBuilder.<String, String>custom()
            .put(ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN, ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_INBOUND_CALLBACK)
            .build();

    public static Map<String, String> getInboundCallbackStrategy() {
        return orderInboundCallbackStrategy;
    }
}
