package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.OrderDockingNumberRpcService;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.request.OrderDockingNumberRequestDTO;
import com.reagent.order.enums.DockingNumberTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-10-20 14:47
 * @description:
 **/
@ServiceClient
public class OrderDockingNumberRpcClient {

    @MSharpReference
    private OrderDockingNumberRpcService orderDockingNumberRpcService;

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void insertList(List<OrderDockingNumberDTO> orderDockingNumberDTOList){
        RemoteResponse<Integer> response = orderDockingNumberRpcService.insertList(orderDockingNumberDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public List<OrderDockingNumberDTO> listByParam(List<String> numbers, DockingNumberTypeEnum dockingNumberTypeEnum){
        OrderDockingNumberRequestDTO orderDockingNumberRequestDTO = new OrderDockingNumberRequestDTO();
        orderDockingNumberRequestDTO.setOrderNoList(numbers);
        orderDockingNumberRequestDTO.setType(dockingNumberTypeEnum.getType());
        RemoteResponse<List<OrderDockingNumberDTO>> response = orderDockingNumberRpcService.listByParam(orderDockingNumberRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
