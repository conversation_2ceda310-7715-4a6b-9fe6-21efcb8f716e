package com.ruijing.store.order.gateway.print.warehouse;

import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.InWarehousePrintDataDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:41
 * @description
 */
public interface InWarehousePrintDataService {

    /**
     * 通过订单号取入库单数据（含入库商品）
     * @param orderNoList 订单号
     * @return 入库单数据
     */
    List<BizWarehouseEntryDTO> getBizWarehouseEntryDtoListByOrderNo(List<String> orderNoList);

    /**
     * 获取入库单打印数据
     * @param orderMasterDO 订单主表数据
     * @param orderDetailDOList 订单详情表数据
     * @param warehouseApplicationInfoList 入库单数据
     * @return 入库单打印数据
     */
    List<InWarehousePrintDataDTO> getInWarehousePrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList);
}
