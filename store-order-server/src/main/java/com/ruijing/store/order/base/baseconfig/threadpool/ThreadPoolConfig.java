package com.ruijing.store.order.base.baseconfig.threadpool;

import com.ruijing.fundamental.common.threadpool.NamedThreadFactory;
import com.ruijing.fundamental.concurrent.ListenableExecutors;
import com.ruijing.fundamental.concurrent.ListenableThreadPoolExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.*;

/**
 * 自定义线程池配置类
 * <AUTHOR>
 * @Date 2020/7/13 8:13 上午
 */
@Configuration
public class ThreadPoolConfig {

    /**
     * 创建自定义IO线程池，默认核心数/线程数为 机器cpu核心数 * 2
     * 非核心线程存活时间默认为 5 秒
     * @return
     */
    @Bean
    @Primary
    public Executor defaultIoExecutor() {
        // 设置线程池名称，方便定位问题
        NamedThreadFactory namedThreadFactory = new NamedThreadFactory("order-IO-thread-pool");
        Executor executor = new ThreadPoolExecutor(
                4,
                8,
                61,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1024),
                namedThreadFactory,
                // 使用默认的拒绝策略，队列满时使用当前调度线程去执行
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        return executor;
    }

    /**
     *    广工同步线程隔离，30分钟定时一次，1分钟调一次tpi。队列最多30个任务
     * @return
     */
    @Bean
    public Executor defaultScheduledExecutor() {

        NamedThreadFactory namedThreadFactory = new NamedThreadFactory("order-scheduled-thread-pool");
        ExecutorService executor = new ThreadPoolExecutor(
                1,
                10,
                61,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(30),
                namedThreadFactory,
                // 使用默认的拒绝策略，队列满时使用当前调度线程去执行
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        return ListenableExecutors.newThreadExecutor(executor);
    }

    /**
     *    入库相关请求的隔离线程池
     * @return
     */
    @Bean
    public Executor inWareHouseExecutor() {

        NamedThreadFactory namedThreadFactory = new NamedThreadFactory("order-inWareHouse-thread-pool");
        ExecutorService executor = new ThreadPoolExecutor(
                8,
                16,
                61,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1024),
                namedThreadFactory,
                // 使用默认的拒绝策略，队列满时使用当前调度线程去执行
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        return ListenableExecutors.newThreadExecutor(executor);
    }

    @Bean
    public ListenableThreadPoolExecutor excelExportExecutor() {
        // 设置线程池名称，方便定位问题
        NamedThreadFactory namedThreadFactory = new NamedThreadFactory("order-excel-export-thread-pool");
        return new ListenableThreadPoolExecutor(
                new ThreadPoolExecutor(
                        8,
                        16,
                        61,
                        TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(128),
                        namedThreadFactory));
    }
}
