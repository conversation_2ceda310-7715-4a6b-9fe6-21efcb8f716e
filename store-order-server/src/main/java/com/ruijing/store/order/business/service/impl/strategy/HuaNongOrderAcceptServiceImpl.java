package com.ruijing.store.order.business.service.impl.strategy;

import com.google.common.collect.Lists;
import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:05
 * @description
 */
@Service(OrgConst.HUA_NAN_NONG_YE_DA_XUE+OrderAcceptConstant.ACCEPT_SUFFIX)
public class HuaNongOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    /**
     * 判断 订单 使用哪种验收模式
     *
     * @param orderMasterDO     订单信息
     * @param orderDetailList   订单商品明细
     * @param isAcceptApproval  手否 验收审批
     * @param platformWorkFunds 是否 平台经费
     * @param unRelateOrderData 是否 旧单
     * @return Integer  无匹配模式（需要报错）
     * 1--验收--完成 模式
     * 2--验收--审批 模式
     * 3--验收--（待）结算 模式
     */
    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        Date oldDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME);
        if(orderMasterDO.getForderdate().before(oldDate)){
            // 订单日期早于旧单日期 一定为旧单，收货后订单为已完成状态
            return 1;
        }
        // 否则就需要查一下采购单生成日期
        List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(New.list(orderMasterDO.getFtbuyappid()));
        Preconditions.notEmpty(applicationMasterDTOList, "无法获取到采购单数据！验收失败！");
        ApplicationMasterDTO applicationMasterDTO = applicationMasterDTOList.get(0);
        if(applicationMasterDTO.getCreateTime().before(oldDate)){
            // 采购单生成日期早于旧单日期，是旧单，收货后订单为已完成状态
            return 1;    
        }
        return super.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
    }
}
