package com.ruijing.store.order.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.ruijing.base.biz.organization.api.org.service.dto.RefOrgBusinessRpcDTO;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.oms.api.dto.OmsAccessDTO;
import com.ruijing.store.oms.api.enums.OmsAccessEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.*;
import com.ruijing.store.order.base.core.model.*;
import com.ruijing.store.order.base.core.service.FundCardFreezeCommonService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.business.enums.OmsFixDataEnum;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.OmsFixDataService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OmsConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.gateway.oms.request.*;
import com.ruijing.store.order.gateway.oms.vo.ErrorMsgVO;
import com.ruijing.store.order.gateway.oms.vo.OmsFixDataLogVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import com.ruijing.store.wms.api.dto.BizWarehouseDangerousOccupyReturnGoodsDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseDangerousOccupyReturnGoodsDetailDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/8 10:07
 * @description
 */
@Service
public class OmsFixDataServiceImpl implements OmsFixDataService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private final static String OMS_FIX_DATA_REDIS_CACHE_KEY = "OMS_FIX_DATA";

    /**
     * 可以修改成的订单状态
     */
    final List<Integer> ORDER_STATUS_CAN_FIX_TO = New.list(
            OrderStatusEnum.Close.getValue(),
            OrderStatusEnum.Finish.getValue(),
            OrderStatusEnum.WaitingForStatement_1.getValue());

    /**
     * 可以修改成的订单状态文本
     */
    final String ORDER_STATUS_CAN_FIX_TO_DESC = OmsFixDataServiceImpl.getStatusListDescStr(ORDER_STATUS_CAN_FIX_TO);

    /**
     * 可以关闭订单的订单状态
     */
    final List<Integer> CLOSE_TO_VERIFY = New.list(
            OrderStatusEnum.WaitingForConfirm.getValue(),
            OrderStatusEnum.WaitingForReceive.getValue(),
            OrderStatusEnum.OrderReceiveApproval.getValue(),
            OrderStatusEnum.WaitingForStatement_1.getValue(),
            OrderStatusEnum.PurchaseApplyToCancel.getValue(),
            OrderStatusEnum.SupplierApplyToCancel.getValue());

    /**
     * 可以关闭订单的订单状态文本
     */
    final String CLOSE_TO_VERIFY_DESC = OmsFixDataServiceImpl.getStatusListDescStr(CLOSE_TO_VERIFY);

    /**
     * 可以完成订单的订单状态
     */
    final List<Integer> FINISH_TO_VERIFY = New.list(
            OrderStatusEnum.WaitingForStatement_1.getValue(),
            OrderStatusEnum.Statementing_1.getValue());

    /**
     * 可以完成订单的订单状态文本
     */
    final String FINISH_TO_VERIFY_DESC = OmsFixDataServiceImpl.getStatusListDescStr(FINISH_TO_VERIFY);

    /**
     * 可以改待结算的订单状态。已评价状态已经不在用了 界面显示的是已完成，也允许修改为待结算
     */
    final List<Integer> WAITING_STATEMENT_TO_VERIFY = New.list(
            OrderStatusEnum.Assess.getValue(),
            OrderStatusEnum.Finish.getValue());

    /**
     * 可以改待结算的订单状态文本
     */
    final String WAITING_STATEMENT_TO_VERIFY_DESC = OmsFixDataServiceImpl.getStatusListDescStr(WAITING_STATEMENT_TO_VERIFY);

    /**
     * 可以修改成的经费状态
     */
    final List<Integer> FUND_STATUS_CAN_FIX_TO = New.list(
            OrderFundStatusEnum.Freezed.getValue(),
            OrderFundStatusEnum.FreezedFail.getValue(),
            OrderFundStatusEnum.ThrawSuccessed.getValue(),
            OrderFundStatusEnum.ThrawFailed.getValue()
    );

    /**
     * 可以修改成的经费状态文本
     */
    final String FUND_STATUS_CAN_FIX_TO_DESC = OmsFixDataServiceImpl.getFundStatusListDescStr(FUND_STATUS_CAN_FIX_TO);

    /**
     * 日志数据经费卡ID前缀
     */
    private final String LOG_FUND_CARD_ID_PREFIX = "|cardId：";


    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private DataOperationLogRpcClient dataOperationLogRpcClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private WaitingStatementService waitingStatementService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private FundCardFreezeCommonService fundCardFreezeCommonService;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private WarehouseStockOccupyService warehouseStockOccupyService;

    /**
     * 允许使用OMS经费冻结功能的单位
     */
    @PearlValue(key = "ALLOW_OMS_FREEZE_FUND_ORG_LIST", defaultValue = "[10,61]")
    private static List<Integer> allowOmsFreezeFundOrgList;

    /**
     * 将状态列表拼接为【状态1】【状态2】的字符串
     *
     * @param statusList 状态列表
     * @return 字符串
     */
    private static String getStatusListDescStr(List<Integer> statusList) {
        return statusList.stream().map(status -> "【" + Objects.requireNonNull(OrderStatusEnum.get(status)).getName() + "】").collect(Collectors.joining(""));
    }

    private static String getFundStatusListDescStr(List<Integer> fundStatusList) {
        return fundStatusList.stream().map(status -> "【" + Objects.requireNonNull(OrderFundStatusEnum.get(status)).getName() + "】").collect(Collectors.joining(""));
    }

    @Override
    public PageableResponse<List<OrderInfoVO>> getOrderList(OrderListRequest request) {
        // 构造搜索入参（业务处理增加代配送搜，增加id倒序排序）
        OrderSearchParamDTO searchParam = new OrderSearchParamDTO();
        buyerOrderService.constructSearchPageParam(searchParam, request);
        // 搜索结果组装
        SearchPageResultDTO<OrderMasterSearchDTO> searchRes = orderSearchBoostService.commonSearch(searchParam);
        // 从搜索中获取对应信息
        Long totalHit = searchRes.getTotalHits();
        List<OrderMasterSearchDTO> masterSearchList = searchRes.getRecordList();
        if (CollectionUtils.isEmpty(masterSearchList)) {
            return PageableResponse.<List<OrderInfoVO>>custom().setData(Collections.emptyList()).setTotal(totalHit).setSuccess();
        }
        List<Integer> orderIdList = masterSearchList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Map<Integer, OrderMasterDO> orderIdMasterMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getId, Function.identity());
        List<OrderInfoVO> orderInfoVOList = masterSearchList.stream().map(orderMasterSearchDTO -> {
            OrderInfoVO orderInfoVO = this.getOrderInfoVO(orderMasterSearchDTO);
            OrderMasterVO orderMasterVO = orderInfoVO.getOrder();
            OrderMasterDO orderMasterDO = orderIdMasterMap.get(orderMasterVO.getId());
            if (orderMasterDO != null) {
                orderMasterVO.setFailedReason(orderMasterDO.getFailedReason());
                orderMasterVO.setStatementStatus(orderMasterDO.getStatementStatus());
            }
            return orderInfoVO;
        }).collect(Collectors.toList());

        return PageableResponse.<List<OrderInfoVO>>custom().setData(orderInfoVOList).setTotal(totalHit).setSuccess();
    }

    @Override
    public PageableResponse<List<OmsFixDataLogVO>> getDataOperationLog(OmsFixDataLogQueryRequest omsFixDataLogQueryRequest) {
        DataOperationLogListRequest dataOperationLogListRequest = new DataOperationLogListRequest();
        if(StringUtils.isNotBlank(omsFixDataLogQueryRequest.getNumber())){
            dataOperationLogListRequest.setOrderNo(omsFixDataLogQueryRequest.getNumber());
        }
        dataOperationLogListRequest.setOrgId(omsFixDataLogQueryRequest.getOrgId());
        if(StringUtils.isNotBlank(omsFixDataLogQueryRequest.getOperationName())){
            dataOperationLogListRequest.setOperatorName(omsFixDataLogQueryRequest.getOperationName());
        }
        if(StringUtils.isNotBlank(omsFixDataLogQueryRequest.getDingTalkApprovalNumber())){
            dataOperationLogListRequest.setApproveNumber(omsFixDataLogQueryRequest.getDingTalkApprovalNumber());
        }
        dataOperationLogListRequest.setStarTime(omsFixDataLogQueryRequest.getStartTime());
        dataOperationLogListRequest.setEndTime(omsFixDataLogQueryRequest.getEndTime());
        // 默认页码1，页大小20
        Integer pageNo = omsFixDataLogQueryRequest.getPageNo();
        pageNo = pageNo != null ? pageNo : 1;
        dataOperationLogListRequest.setPageNo(pageNo);
        Integer pageSize = omsFixDataLogQueryRequest.getPageSize();
        pageSize = pageSize != null ? pageSize : 20;
        dataOperationLogListRequest.setPageSize(pageSize);
        PageableResponse<List<DataOperationLogDTO>> pageableResponse = dataOperationLogRpcClient.getDataOperationLog(dataOperationLogListRequest);
        List<OmsFixDataLogVO> omsFixDataLogVOList = pageableResponse.getData().stream().map(this::translateToOmsFixDataLogVO).collect(Collectors.toList());
        return PageableResponse.<List<OmsFixDataLogVO>>custom().setData(omsFixDataLogVOList).setPageNo(pageNo).setPageSize(pageSize).setTotal(pageableResponse.getTotal());
    }

    @Override
    public List<ErrorMsgVO> reUnfreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        Predicate<OrderMasterDO> predicate = orderMasterDO -> !OrderFundStatusEnum.ThrawFailed.getValue().equals(orderMasterDO.getFundStatus());
        return this.unfreezeCore(loginUserInfoBO, fixDataCommonRequest, OmsFixDataEnum.FUND_RE_UNFREEZE, predicate, "不是经费解冻失败状态");
    }

    @Override
    public List<ErrorMsgVO> unfreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        final List<Integer> orderStatusListToVerify = New.list(OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.Close.getValue(), OrderStatusEnum.Finish.getValue());
        Predicate<OrderMasterDO> predicate = orderMasterDO -> !(OrderFundStatusEnum.Freezed.getValue().equals(orderMasterDO.getFundStatus()) && orderStatusListToVerify.contains(orderMasterDO.getStatus()));
        return this.unfreezeCore(loginUserInfoBO, fixDataCommonRequest, OmsFixDataEnum.FUND_UNFREEZE, predicate, "只能解冻对接单位待结算、已完成、订单关闭且经费冻结成功状态的订单");
    }

    @Override
    public List<ErrorMsgVO> reFreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(fixDataCommonRequest.getIds());
        // 过滤中大的单
        List<ErrorMsgVO> errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderMasterDO.getFuserid()), "暂不处理中大的单据");
        if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
            return errorMsgVOList;
        }
        errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> !OrderFundStatusEnum.FreezedFail.getValue().equals(orderMasterDO.getFundStatus()), "不是经费冻结失败状态。");
        if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
            return errorMsgVOList;
        }
        return this.exec(orderMasterDOList, orderMasterDO -> {
            // 循环写日志，处理重新冻结
            this.writeOmsFixDataLog(OmsFixDataEnum.FUND_RE_FREEZE, null, orderMasterDO, fixDataCommonRequest.getReason(),
                    fixDataCommonRequest.getDingTalkApprovalNumber(), loginUserInfoBO);
            this.writeBuyerCenterLog(OmsFixDataEnum.FUND_RE_FREEZE, null, orderMasterDO.getId(), fixDataCommonRequest.getReason());
            refFundcardOrderService.reFreezeFundCard(orderMasterDO);
        }, "OMS批量重新冻结{}失败", "重新冻结处理失败：");
    }

    @Override
    @ServiceLog(description = "修正订单详情商品分类快照", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public List<ErrorMsgVO> fixOrderDetailProductCategory(LoginUserInfoBO loginUserInfoBO, FixOrderDetailCommonRequest fixOrderDetailCommonRequest) {
        List<FixOrderDetailRequest> fixOrderDetailList = fixOrderDetailCommonRequest.getFixOrderDetailList();
        String reason = fixOrderDetailCommonRequest.getReason();
        String dingTalkApprovalNumber = fixOrderDetailCommonRequest.getDingTalkApprovalNumber();
        if(CollectionUtils.isEmpty(fixOrderDetailList)){
            return New.list();
        }
        List<Integer> orderIdList= fixOrderDetailList.stream()
                .map(FixOrderDetailRequest::getOrderId)
                .collect(Collectors.toList());
        this.checkModifyDataPermission(loginUserInfoBO, orderIdList);

        //不能只配一级分类 不配二三级分类
        boolean onlyPrimaryCategory = fixOrderDetailList.stream()
                .filter(fixOrderDetailRequest -> fixOrderDetailRequest.getFirstCategoryId() != null)
                .anyMatch(fixOrderDetailRequest -> fixOrderDetailRequest.getSecondCategoryId() == null && fixOrderDetailRequest.getThirdCategoryId() == null);
        BusinessErrUtil.isTrue(!onlyPrimaryCategory, "不能只配一级分类 不配二三级分类");
        List<OrderDetailDO> fixOrderDetailDOList = New.list();
        for(FixOrderDetailRequest fixOrderDetail : fixOrderDetailList){
            OrderDetailDO orderDetailDO = new OrderDetailDO();
            orderDetailDO.setId(fixOrderDetail.getOrderDetailId());
            orderDetailDO.setFmasterid(fixOrderDetail.getOrderId());
            orderDetailDO.setFirstCategoryId(fixOrderDetail.getFirstCategoryId());
            orderDetailDO.setFirstCategoryName(fixOrderDetail.getFirstCategoryName());
            orderDetailDO.setSecondCategoryId(fixOrderDetail.getSecondCategoryId());
            orderDetailDO.setSecondCategoryName(fixOrderDetail.getSecondCategoryName());
            //最末级分类
            orderDetailDO.setCategoryid(fixOrderDetail.getThirdCategoryId() == null ? fixOrderDetail.getSecondCategoryId() : fixOrderDetail.getThirdCategoryId());
            orderDetailDO.setFclassification(StringUtils.isBlank(fixOrderDetail.getThirdCategoryName()) ? fixOrderDetail.getSecondCategoryName() : fixOrderDetail.getThirdCategoryName());
            fixOrderDetailDOList.add(orderDetailDO);
        }
        List<OrderDetailDO> originOrderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Map<Integer, OrderDetailDO> orderDetailId2originDetailDOMap = DictionaryUtils.toMap(originOrderDetailDOList, OrderDetailDO::getId, Function.identity());
        Map<Integer, OrderMasterDO> orderId2OrderMasterDOMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getId, Function.identity());
        //记录操作日志
        List<DataOperationLogDTO> dataOperationLogDTOList = this.prepareOmsFixOrderDetailLog(OmsFixDataEnum.FIX_ORDER_ITEM_CATEGORY, fixOrderDetailDOList, reason, dingTalkApprovalNumber, loginUserInfoBO, orderDetailId2originDetailDOMap, orderId2OrderMasterDOMap);
        this.batchWriteOmsFixOrderDetailLog(dataOperationLogDTOList);
        //更新商品分类
        orderDetailMapper.batchUpdateCategory(fixOrderDetailDOList);
        return New.emptyList();
    }

    @Override
    @ServiceLog(description = "删除测试订单", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public List<ErrorMsgVO> deleteTestOrders(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        String dingTalkApprovalNumber = fixDataCommonRequest.getDingTalkApprovalNumber();
        List<Integer> orderIdList = fixDataCommonRequest.getIds();
        if(CollectionUtils.isEmpty(orderIdList)){
            return New.emptyList();
        }
        String reason = fixDataCommonRequest.getReason();
        this.checkModifyDataPermission(loginUserInfoBO, orderIdList);
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Set<String> testSuppCodeSet = Environment.isProdEnv() ? OmsConstant.PRO_SUPPLIER_CODE_TEST_SET : OmsConstant.TEST_SUPPLIER_CODE_SET;
        Integer newOrgId = Environment.isProdEnv() ? OmsConstant.PRO_DEMO_ORG_ID : OmsConstant.TEST_DEMO_ORG_ID;
        OrganizationClient.SimpleOrgDTO newOrgDTO = organizationClient.findSimpleOrgDTOById(newOrgId);

        List<ErrorMsgVO> errorMsgVOS = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> !testSuppCodeSet.contains(orderMasterDO.getFsuppcode()), "非测试供应商订单，不能删除");
        if(CollectionUtils.isNotEmpty(errorMsgVOS)){
            return errorMsgVOS;
        }
        //操作日志
        this.batchWriteOmsFixDataLog(OmsFixDataEnum.DELETE_TEST_ORDER, null, orderMasterDOList, reason, dingTalkApprovalNumber, loginUserInfoBO);
        for(OrderMasterDO orderMasterDO: orderMasterDOList){
            orderMasterDO.setFuserid(newOrgId);
            orderMasterDO.setFusercode(newOrgDTO.getCode());
            orderMasterDO.setFusername(newOrgDTO.getName());
        }
        //更新单位信息
        orderMasterMapper.batchUpdateOrgInfo(orderMasterDOList);
        return New.emptyList();
    }

    @ServiceLog(description = "修改订单状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    @Override
    public List<ErrorMsgVO> fixOrderStatus(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        String lockKey = OMS_FIX_DATA_REDIS_CACHE_KEY + loginUserInfoBO.getUserGuid();
        boolean getLock = false;
        try {
            // 1.获取当前订单验收的执行锁
            getLock = cacheClient.tryLock(lockKey, 20);
            // 2.校验
            Preconditions.isTrue(ORDER_STATUS_CAN_FIX_TO.contains(fixDataCommonRequest.getStatus()), "只能改为" + ORDER_STATUS_CAN_FIX_TO_DESC + "中的其中一种状态");
            this.checkModifyDataPermission(loginUserInfoBO, fixDataCommonRequest.getIds());

            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(fixDataCommonRequest.getIds());
            List<ErrorMsgVO> errorMsgVOList;
            // 过滤中大的单
            errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderMasterDO.getFuserid()), "暂不处理中大的单据");
            if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                return errorMsgVOList;
            }
            OrderStatusEnum statusFixTo = Objects.requireNonNull(OrderStatusEnum.get(fixDataCommonRequest.getStatus()));
            OrderMasterDO updated = new OrderMasterDO();
            updated.setStatus(statusFixTo.getValue());
            // 校验状态是否符合条件，结算单是否符合条件
            switch (fixDataCommonRequest.getStatus()) {
                case 3:
                    // 已关闭
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> !CLOSE_TO_VERIFY.contains(orderMasterDO.getStatus()), "只能修改" + CLOSE_TO_VERIFY_DESC + "的订单");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> orderMasterDO.getStatementId() != null, "请先撤销订单的结算单或作废汇总单。");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    // 过滤一物一码的单
                    List<BaseOrderExtraDTO> baseOrderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(fixDataCommonRequest.getIds(), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
                    Map<Integer, Boolean> orderIdIsOneProductOneCodeMap = DictionaryUtils.toMap(baseOrderExtraDTOList, BaseOrderExtraDTO::getOrderId, item-> CommonValueUtils.parseNumberStrToBoolean(item.getExtraValue()));
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> orderIdIsOneProductOneCodeMap.getOrDefault(orderMasterDO.getId(), false), "一物一码的单据不支持OMS修改状态");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    OrderDetailReq orderDetailReq = new OrderDetailReq();
                    orderDetailReq.setOrderMasterIdList(New.list(fixDataCommonRequest.getIds()));
                    orderDetailReq.setReturnBindGasBottle(true);
                    List<OrderDetailDTO> orderDetailDTOList = orderDetailService.findDetailByOrderIdListExcludeReturnStatus(orderDetailReq).getData();
                    Map<Integer, Boolean> orderIdBindGasBottle = DictionaryUtils.toMap(orderDetailDTOList, OrderDetailDTO::getFmasterid, detail->CollectionUtils.isNotEmpty(detail.getBindGasBottleBarcodes()), (o, n)->o || n);
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> orderIdBindGasBottle.getOrDefault(orderMasterDO.getId(), false), "绑定气瓶的单据不支持OMS修改状态");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    List<Integer> orderIdList = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
                    // 删除待结算信息
                    statementPlatformClient.deleteWaitingStatementByOrderId(orderIdList);
                    // 释放预占库存
                    orderMasterDOList.forEach(this::releaseStockForCloseOrder);
                    updated.setShutDownDate(new Date());
                    break;
                case 6:
                    // 待结算
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> !WAITING_STATEMENT_TO_VERIFY.contains(orderMasterDO.getStatus()), "只能修改" + WAITING_STATEMENT_TO_VERIFY_DESC + "的订单");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> orderMasterDO.getStatementId() != null, "订单绑定有结算单或汇总单，禁止修改。");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    break;
                case 11:
                    // 已完成
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> !FINISH_TO_VERIFY.contains(orderMasterDO.getStatus()), "只能修改" + FINISH_TO_VERIFY_DESC + "的订单");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> orderMasterDO.getStatementId() != null && !StatementStatusEnum.Completed.getStatus().equals(orderMasterDO.getStatementStatus()), "订单的结算单或汇总单必须是已完成状态。");
                    if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                        return errorMsgVOList;
                    }
                    updated.setFinishDate(new Date());
                    break;
                default:
                    return New.emptyList();
            }
            // 3.批量写日志，更新数据
            this.batchWriteOmsFixDataLog(OmsFixDataEnum.FIX_ORDER_STATUS, statusFixTo.getValue(), orderMasterDOList, fixDataCommonRequest.getReason(),
                    fixDataCommonRequest.getDingTalkApprovalNumber(), loginUserInfoBO);
            this.batchWriteBuyerCenterLog(OmsFixDataEnum.FIX_ORDER_STATUS, statusFixTo.getValue(), orderMasterDOList, fixDataCommonRequest.getReason());
            List<Integer> orderIdList = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
            orderMasterMapper.updateStatusByIdIn(updated, orderIdList);
            // 4.更新状态后处理
            switch (fixDataCommonRequest.getStatus()) {
                case 3:
                    this.deleteWaitingStatementData(orderMasterDOList);
                    // 已关闭状态，需要取消退货单，如果有冻结的还需要解冻
                    buyerGoodsReturnService.systemCancelGoodsReturn(orderIdList, GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN, fixDataCommonRequest.getReason());
                    List<OrderMasterDO> freezeOrderList = orderMasterDOList.stream().filter(orderMasterDO -> OrderFundStatusEnum.Freezed.getValue().equals(orderMasterDO.getFundStatus())).collect(Collectors.toList());
                    freezeOrderList.forEach(order -> {
                        this.writeBuyerCenterLog(OmsFixDataEnum.FUND_UNFREEZE, null, order.getId(), "开始调用解冻经费卡接口");
                        orderManageService.orderFundCardUnFreeze(order);
                    });
                    break;
                case 6:
                    // 待结算,将订单推到待结算
                    Map<String, List<Integer>> inventoryStatusOrderIdListMap = orderMasterDOList.stream().collect(Collectors.groupingBy(OrderMasterDO::getFusercode, Collectors.mapping(OrderMasterDO::getId, Collectors.toList())));
                    inventoryStatusOrderIdListMap.forEach((orgCode, ids) -> waitingStatementService.pushWaitingStatement(orgCode, ids));
                    break;
                case 11:
                    this.deleteWaitingStatementData(orderMasterDOList);
                    // 已完成,需要取消退货单
                    buyerGoodsReturnService.systemCancelGoodsReturn(orderIdList, GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN, fixDataCommonRequest.getReason());
                    fundCardFreezeCommonService.orderDefray(orderMasterDOList);
                    break;
                default:
                    break;
            }
            return New.emptyList();
        } catch (Exception e) {
            LOGGER.error("OMS批量改订单状态失败", e);
            throw e;
        } finally {
            if (getLock) {
                // 如果获取到执行锁成功，则释放
                cacheClient.unlock(lockKey);
            }
        }
    }

    @ServiceLog(description = "修改经费状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    @Override
    public List<ErrorMsgVO> fixFundStatus(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest) {
        String lockKey = OMS_FIX_DATA_REDIS_CACHE_KEY + loginUserInfoBO.getUserGuid();
        boolean getLock = false;
        try {
            // 1.获取当前订单验收的执行锁
            getLock = cacheClient.tryLock(lockKey, 10);
            // 2.校验
            Preconditions.isTrue(FUND_STATUS_CAN_FIX_TO.contains(fixDataCommonRequest.getStatus()), "只能改为" + FUND_STATUS_CAN_FIX_TO_DESC + "中的其中一种状态");
            this.checkModifyDataPermission(loginUserInfoBO, fixDataCommonRequest.getIds());

            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(fixDataCommonRequest.getIds());
            // 过滤中大的单
            List<ErrorMsgVO> errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderMasterDO.getFuserid()), "暂不处理中大的单据");
            if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                return errorMsgVOList;
            }
            // 校验状态是否符合条件
            OrderFundStatusEnum fundStatusFixTo = Objects.requireNonNull(OrderFundStatusEnum.get(fixDataCommonRequest.getStatus()));
            errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> fundStatusFixTo.getValue().equals(orderMasterDO.getFundStatus()), "所选订单经费状态与要修改的状态相同。");
            if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                return errorMsgVOList;
            }
            // 3.批量写日志，处理数据
            this.batchWriteOmsFixDataLog(OmsFixDataEnum.FIX_FUND_STATUS, fundStatusFixTo.getValue(), orderMasterDOList, fixDataCommonRequest.getReason(),
                    fixDataCommonRequest.getDingTalkApprovalNumber(), loginUserInfoBO);
            this.batchWriteBuyerCenterLog(OmsFixDataEnum.FIX_FUND_STATUS, fundStatusFixTo.getValue(), orderMasterDOList, fixDataCommonRequest.getReason());
            List<UpdateOrderParamDTO> updateOrderParamDTOList = orderMasterDOList.stream().map(orderMasterDO -> {
                UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                updateOrderParamDTO.setOrderId(orderMasterDO.getId());
                updateOrderParamDTO.setFundStatus(fundStatusFixTo.getValue());
                return updateOrderParamDTO;
            }).collect(Collectors.toList());
            orderMasterMapper.updateFieldByIdIn(updateOrderParamDTOList);
            return New.emptyList();
        } catch (Exception e) {
            LOGGER.error("OMS批量改经费状态失败", e);
            throw e;
        } finally {
            if (getLock) {
                // 如果获取到执行锁成功，则释放
                cacheClient.unlock(lockKey);
            }
        }
    }


    /**
     * 解冻方法
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单请求
     * @param omsFixDataEnum    操作类型
     */
    private List<ErrorMsgVO> unfreezeCore(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest, OmsFixDataEnum omsFixDataEnum, Predicate<OrderMasterDO> predicate, String predicateFailStr) {
        String lockKey = OMS_FIX_DATA_REDIS_CACHE_KEY + loginUserInfoBO.getUserGuid();
        boolean getLock = false;
        try {
            // 1.获取当前订单验收的执行锁
            getLock = cacheClient.tryLock(lockKey, 10);
            Preconditions.isTrue(getLock, "不允许连续点击【" + omsFixDataEnum.getName() + "】按钮！");
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(fixDataCommonRequest.getIds());
            // 2.校验
            // 过滤中大的单
            List<ErrorMsgVO> errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, orderMasterDO -> ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderMasterDO.getFuserid()), "暂不处理中大的单据");
            if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                return errorMsgVOList;
            }
            // 是否满足解冻的前置条件，经费状态是否满足
            errorMsgVOList = this.constructErrorMsgVOList(orderMasterDOList, predicate, predicateFailStr);
            if (CollectionUtils.isNotEmpty(errorMsgVOList)) {
                return errorMsgVOList;
            }
            return this.exec(orderMasterDOList, orderMasterDO -> {
                // 循环写日志，处理解冻
                this.writeOmsFixDataLog(omsFixDataEnum, null, orderMasterDO, fixDataCommonRequest.getReason(),
                        fixDataCommonRequest.getDingTalkApprovalNumber(), loginUserInfoBO);
                this.writeBuyerCenterLog(omsFixDataEnum, null, orderMasterDO.getId(), fixDataCommonRequest.getReason());
                orderManageService.orderFundCardUnFreeze(orderMasterDO);
            }, "OMS批量解冻{}失败", "解冻处理失败：");
        } catch (Exception e){
            LOGGER.error("OMS批量解冻失败", e);
            throw e;
        } finally{
            if (getLock) {
                // 如果获取到执行锁成功，则释放
                cacheClient.unlock(lockKey);
            }
        }
    }

    /**
     * 对每个订单for循环执行，错误的累加到返回结果中
     * @param orderMasterDOList 订单
     * @param consumer 执行代码
     * @param errLogTemplate log的模板
     * @param errMsgPrefix 返回给前端异常前缀
     * @return 给前端的异常
     */
    private List<ErrorMsgVO> exec(List<OrderMasterDO> orderMasterDOList, Consumer<OrderMasterDO> consumer, String errLogTemplate, String errMsgPrefix){
        List<ErrorMsgVO> execFailList = New.list();
        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            try{
                consumer.accept(orderMasterDO);
            } catch (Exception e){
                LOGGER.error(errLogTemplate, orderMasterDO, e);
                execFailList.add(this.constructErrorMsgVO(orderMasterDO.getForderno(), errMsgPrefix + e.getMessage()));
            }
        }
        return execFailList;
    }

    private OmsFixDataLogVO translateToOmsFixDataLogVO(DataOperationLogDTO dataOperationLogDTO) {
        OmsFixDataLogVO omsFixDataLogVO = new OmsFixDataLogVO();
        omsFixDataLogVO.setId(dataOperationLogDTO.getId());
        omsFixDataLogVO.setOperationTime(dataOperationLogDTO.getOperateTime().getTime());
        omsFixDataLogVO.setOperationName(dataOperationLogDTO.getOperatorName());
        omsFixDataLogVO.setOrgName(dataOperationLogDTO.getOrgName());
        omsFixDataLogVO.setNumber(dataOperationLogDTO.getOrderNo());
        omsFixDataLogVO.setDingTalkApprovalNumber(dataOperationLogDTO.getApproveNumber());
        omsFixDataLogVO.setReason(dataOperationLogDTO.getFixReason());
        String prevDesc = dataOperationLogDTO.getPreDesc();
        String operation = dataOperationLogDTO.getOperation();
        // 冻结/解冻经费 不展示cardId内容
        if (Objects.equals(OmsFixDataEnum.FUND_FREEZE.getValue(), dataOperationLogDTO.getOperationType())) {
            operation = StringUtils.substringBefore(operation, LOG_FUND_CARD_ID_PREFIX);
        } else if (Objects.equals(OmsFixDataEnum.FUND_UNFREEZE.getValue(), dataOperationLogDTO.getOperationType())) {
            prevDesc = StringUtils.substringBefore(prevDesc, LOG_FUND_CARD_ID_PREFIX);
        }
        omsFixDataLogVO.setPrevStatus(prevDesc);
        omsFixDataLogVO.setContent(operation);
        return omsFixDataLogVO;

    }

    /**
     * 拼装返回数据
     *
     * @param masterSearch 搜索数据
     * @return vo
     */
    private OrderInfoVO getOrderInfoVO(OrderMasterSearchDTO masterSearch) {
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        OrderMasterVO order = new OrderMasterVO();

        order.setId(masterSearch.getId());
        order.setBuyerName(masterSearch.getFbuyername());
        order.setOrgName(masterSearch.getFusername());
        order.setOrderNo(masterSearch.getForderno());
        order.setTotalPrice(BigDecimal.valueOf(masterSearch.getForderamounttotal()));
        order.setSupplierName(masterSearch.getFsuppname());
        order.setOrderDate(masterSearch.getForderdate());
        order.setDepartmentName(masterSearch.getFbuydepartment());
        order.setOrgId(masterSearch.getFuserid());
        order.setStatus(masterSearch.getStatus());
        order.setFundStatus(masterSearch.getFundStatus());
        order.setInventoryStatus(masterSearch.getInventoryStatus());

        orderInfoVO.setOrder(order);

        List<OrderDetailVO> orderDetailVOList = masterSearch.getOrderDetail().stream().map(orderDetailSearchDTO -> {
            OrderDetailVO orderDetailVO = new OrderDetailVO();
            orderDetailVO.setId(orderDetailSearchDTO.getDetailId());
            orderDetailVO.setGoodsName(orderDetailSearchDTO.getFgoodname());
            orderDetailVO.setGoodsCode(orderDetailSearchDTO.getFgoodcode());
            orderDetailVO.setFirstCategoryTag(orderDetailSearchDTO.getFirstCategoryName());
            orderDetailVO.setFirstCategoryId(orderDetailSearchDTO.getFirstCategoryId());
            orderDetailVO.setSecondCategoryTag(orderDetailSearchDTO.getSecondCategoryName());
            orderDetailVO.setSecondCategoryId(orderDetailSearchDTO.getSecondCategoryId());
            Integer thirdCategoryId = Objects.equals(orderDetailSearchDTO.getSecondCategoryId(), orderDetailSearchDTO.getCategoryId()) || Objects.equals(orderDetailSearchDTO.getFirstCategoryId(), orderDetailSearchDTO.getCategoryId()) ? null : orderDetailSearchDTO.getCategoryId();
            orderDetailVO.setThirdCategoryId(thirdCategoryId);
            String thirdCategoryName = Objects.equals(orderDetailSearchDTO.getSecondCategoryName(), orderDetailSearchDTO.getCategoryName()) || Objects.equals(orderDetailSearchDTO.getFirstCategoryName(), orderDetailSearchDTO.getCategoryName())? null : orderDetailSearchDTO.getCategoryName();
            orderDetailVO.setThirdCategoryTag(thirdCategoryName);
            orderDetailVO.setCategoryName(orderDetailSearchDTO.getCategoryName());
            orderDetailVO.setCategoryTag(orderDetailSearchDTO.getCategoryTag());
            orderDetailVO.setPrice(orderDetailSearchDTO.getFbidprice());
            orderDetailVO.setQuantity(orderDetailSearchDTO.getFquantity().doubleValue());
            orderDetailVO.setTotalPrice(orderDetailSearchDTO.getFbidamount());
            return orderDetailVO;
        }).collect(Collectors.toList());
        orderInfoVO.setOrderDetails(orderDetailVOList);
        return orderInfoVO;
    }

    private void batchWriteBuyerCenterLog(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, List<OrderMasterDO> orderMasterDOList, String reason) {
        String reasonToSave = this.getSaveFixLogReason(omsFixDataEnum, statusFixTo, reason);
        List<OrderApprovalLog> orderApprovalLogList = orderMasterDOList.stream().map(orderMasterDO -> this.getBuyerCenterLog(omsFixDataEnum, orderMasterDO.getId(), reasonToSave)).collect(Collectors.toList());
        orderApprovalLogMapper.insertList(orderApprovalLogList);
    }

    /**
     * 写采购人中心日志
     *
     * @param omsFixDataEnum 订单日志枚举
     * @param orderId           订单id
     * @param reason            原因
     */
    private void writeBuyerCenterLog(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, Integer orderId, String reason) {
        String reasonToSave = this.getSaveFixLogReason(omsFixDataEnum, statusFixTo, reason);
        orderApprovalLogMapper.insert(this.getBuyerCenterLog(omsFixDataEnum, orderId, reasonToSave));
    }

    private String getSaveFixLogReason(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, String reason){
        String reasonToSave = reason;
        if(OmsFixDataEnum.FIX_ORDER_STATUS.equals(omsFixDataEnum) || OmsFixDataEnum.FIX_FUND_STATUS.equals(omsFixDataEnum)){
            reasonToSave = statusFixTo + "|" + reason;
        }
        return reasonToSave;
    }

    /**
     * 获取采购人中心日志数据
     *
     * @param omsFixDataEnum 订单审批枚举
     * @param orderId           订单id
     * @param reason            原因
     * @return 日志数据
     */
    private OrderApprovalLog getBuyerCenterLog(OmsFixDataEnum omsFixDataEnum, Integer orderId, String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
        // 按照状态|原因存放，用于展示
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setApproveStatus(omsFixDataEnum.getValue());
        return orderApprovalLog;
    }

    /**
     * 写OMS修正日志数据
     *
     * @param omsFixDataEnum 订单操作枚举
     * @param orderMasterDO     订单数据
     * @param reason            原因
     * @param approveNo         审批编号
     * @param loginUserInfoBO   用户信息
     */
    private void writeOmsFixDataLog(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, OrderMasterDO orderMasterDO, String reason, String approveNo, LoginUserInfoBO loginUserInfoBO) {
        dataOperationLogRpcClient.insert(this.getSaveFixDataLogDTO(omsFixDataEnum, statusFixTo, orderMasterDO, reason, approveNo, loginUserInfoBO));
    }

    /**
     * 批量写OMS修正日志数据
     *
     * @param omsFixDataEnum 订单操作枚举
     * @param orderMasterDOList 订单数据
     * @param reason            原因
     * @param approveNo         审批编号
     * @param loginUserInfoBO   用户信息
     */
    private void batchWriteOmsFixDataLog(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, List<OrderMasterDO> orderMasterDOList, String reason, String approveNo, LoginUserInfoBO loginUserInfoBO) {
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return;
        }
        // 取第一个生成日志数据
        DataOperationLogDTO dataOperationLogDTO = this.getSaveFixDataLogDTO(omsFixDataEnum, statusFixTo, orderMasterDOList.get(0), reason, approveNo, loginUserInfoBO);
        // 全部拿第一份的数据的拷贝,替换掉修改前状态和订单号
        List<DataOperationLogDTO> dataOperationLogDTOList = orderMasterDOList.stream().skip(1).map(orderMasterDO -> {
            DataOperationLogDTO cloneObj = this.cloneSaveFixDataLogDTO(dataOperationLogDTO);
            if (OmsFixDataEnum.FIX_ORDER_STATUS.equals(omsFixDataEnum)) {
                cloneObj.setPreDesc(Objects.requireNonNull(OrderStatusEnum.get(orderMasterDO.getStatus())).name);
            } else if (OmsFixDataEnum.FIX_FUND_STATUS.equals(omsFixDataEnum)) {
                cloneObj.setPreDesc(Objects.requireNonNull(OrderFundStatusEnum.get(orderMasterDO.getFundStatus())).name);
            }
            cloneObj.setOrderNo(orderMasterDO.getForderno());
            return cloneObj;
        }).collect(Collectors.toList());
        dataOperationLogDTOList.add(dataOperationLogDTO);
        dataOperationLogRpcClient.insertList(dataOperationLogDTOList);
    }

    private List<DataOperationLogDTO> prepareOmsFixOrderDetailLog(OmsFixDataEnum omsFixDataEnum,
                                                                  List<OrderDetailDO> fixOrderDetailDOList,
                                                                  String reason,
                                                                  String approveNo,
                                                                  LoginUserInfoBO loginUserInfoBO,
                                                                  Map<Integer, OrderDetailDO> orderDetailId2originDetailDOMap,
                                                                  Map<Integer, OrderMasterDO> orderId2OrderMasterDOMap) {
        List<DataOperationLogDTO> dataOperationLogDTOList = New.list();
        for(OrderDetailDO orderDetailDO: fixOrderDetailDOList){
            OrderMasterDO orderMasterDO = orderId2OrderMasterDOMap.get(orderDetailDO.getFmasterid());
            if(OmsFixDataEnum.FIX_ORDER_ITEM_CATEGORY.equals(omsFixDataEnum)){
                DataOperationLogDTO dataOperationLogDTO = new DataOperationLogDTO();
                dataOperationLogDTO.setFixReason(reason);
                dataOperationLogDTO.setOperatorGuid(loginUserInfoBO.getUserGuid());
                dataOperationLogDTO.setOperatorName(loginUserInfoBO.getUserName());
                dataOperationLogDTO.setOperationType(omsFixDataEnum.getValue());
                dataOperationLogDTO.setApproveNumber(approveNo);
                dataOperationLogDTO.setOperateTime(new Date());
                OrderDetailDO originalDetail = orderDetailId2originDetailDOMap.get(orderDetailDO.getId());
                dataOperationLogDTO.setPreDesc("分类：" + this.getCategoryInfo(originalDetail));
                String operation = "修改商品快照：" + originalDetail.getFgoodname() + "\n分类修改：" + this.getCategoryInfo(orderDetailDO);
                dataOperationLogDTO.setOperation(operation);
                dataOperationLogDTO.setOrderNo(originalDetail.getFdetailno());
                dataOperationLogDTO.setOrgId(orderMasterDO.getFuserid());
                dataOperationLogDTO.setOrgName(orderMasterDO.getFusername());
                dataOperationLogDTOList.add(dataOperationLogDTO);
            }
        }
        return dataOperationLogDTOList;
    }

    private String getCategoryInfo(OrderDetailDO orderDetailDO){
        String firstCategoryName = orderDetailDO.getFirstCategoryName();
        String secondCategoryName = orderDetailDO.getSecondCategoryName();
        String classification = orderDetailDO.getFclassification();
        String thirdCategoryName = StringUtils.equals(classification, firstCategoryName) || StringUtils.equals(classification, secondCategoryName) ? null : classification;
        String info = firstCategoryName + ">" + secondCategoryName;
        if(StringUtils.isBlank(thirdCategoryName)){
            return info;
        }
        return info + ">" + thirdCategoryName;
    }

    @ServiceLog(description = "修改订单详情日志", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    private void batchWriteOmsFixOrderDetailLog(List<DataOperationLogDTO> dataOperationLogDTOList) {
        dataOperationLogRpcClient.insertList(dataOperationLogDTOList);
    }

    /**
     * 获取OMS修正日志数据
     *
     * @param omsFixDataEnum 订单操作枚举
     * @param orderMasterDO     订单数据
     * @param reason            原因
     * @param approveNo         审批编号
     * @param loginUserInfoBO   用户信息
     */
    private DataOperationLogDTO getSaveFixDataLogDTO(OmsFixDataEnum omsFixDataEnum, Integer statusFixTo, OrderMasterDO orderMasterDO, String reason, String approveNo, LoginUserInfoBO loginUserInfoBO) {
        DataOperationLogDTO dataOperationLogDTO = new DataOperationLogDTO();
        dataOperationLogDTO.setOrderNo(orderMasterDO.getForderno());
        dataOperationLogDTO.setFixReason(reason);
        dataOperationLogDTO.setOperatorGuid(loginUserInfoBO.getUserGuid());
        dataOperationLogDTO.setOperatorName(loginUserInfoBO.getUserName());
        dataOperationLogDTO.setApproveNumber(approveNo);
        dataOperationLogDTO.setOrgId(orderMasterDO.getFuserid());
        dataOperationLogDTO.setOrgName(orderMasterDO.getFusername());
        dataOperationLogDTO.setOperateTime(new Date());
        // 操作类型，目前与订单日志是有对应的，直接用审批日志的操作类型枚举值。
        dataOperationLogDTO.setOperationType(omsFixDataEnum.getValue());
        // 操作内容，修改的动作+修改状态。记录修改前状态，非修改订单和经费状态不需要记录
        String statusDesc = null;
        String preDesc;
        if (OmsFixDataEnum.FIX_ORDER_STATUS.equals(omsFixDataEnum)) {
            statusDesc = Objects.requireNonNull(OrderStatusEnum.get(statusFixTo)).getName();
            preDesc = Objects.requireNonNull(OrderStatusEnum.get(orderMasterDO.getStatus())).name;
        } else if (OmsFixDataEnum.FIX_FUND_STATUS.equals(omsFixDataEnum)) {
            statusDesc = Objects.requireNonNull(OrderFundStatusEnum.get(statusFixTo)).getName();
            preDesc = Objects.requireNonNull(OrderFundStatusEnum.get(orderMasterDO.getFundStatus())).name;
        } else {
            preDesc = null;
        }
        dataOperationLogDTO.setOperation(statusDesc == null ? omsFixDataEnum.getName() : omsFixDataEnum.getName() + "：" + statusDesc);
        // 修改前描述
        dataOperationLogDTO.setPreDesc(preDesc);
        return dataOperationLogDTO;
    }

    /**
     * 获取OMS日志修复数据的复制，没有订单号和修改前状态
     *
     * @param dataOperationLogDTO 作为副本的数据
     * @return 日志数据
     */
    private DataOperationLogDTO cloneSaveFixDataLogDTO(DataOperationLogDTO dataOperationLogDTO) {
        DataOperationLogDTO cloneObj = new DataOperationLogDTO();
        cloneObj.setFixReason(dataOperationLogDTO.getFixReason());
        cloneObj.setOperatorGuid(dataOperationLogDTO.getOperatorGuid());
        cloneObj.setOperatorName(dataOperationLogDTO.getOperatorName());
        cloneObj.setApproveNumber(dataOperationLogDTO.getApproveNumber());
        cloneObj.setOrgId(dataOperationLogDTO.getOrgId());
        cloneObj.setOrgName(dataOperationLogDTO.getOrgName());
        cloneObj.setOperateTime(dataOperationLogDTO.getOperateTime());
        cloneObj.setOperationType(dataOperationLogDTO.getOperationType());
        cloneObj.setOperation(dataOperationLogDTO.getOperation());
        return cloneObj;
    }

    /**
     * 封装错误信息
     *
     * @param orderMasterDOList 含错误的订单
     * @param errorMsg          错误信息
     * @return 错误信息体
     */
    private List<ErrorMsgVO> constructErrorMsgVOList(List<OrderMasterDO> orderMasterDOList, Predicate<OrderMasterDO> filterPredicate, String errorMsg) {
        return orderMasterDOList.stream().filter(filterPredicate).map(orderMasterDO -> this.constructErrorMsgVO(orderMasterDO.getForderno(), errorMsg)).collect(Collectors.toList());
    }

    private ErrorMsgVO constructErrorMsgVO(String orderNo, String errMsg){
        ErrorMsgVO errorMsgVO = new ErrorMsgVO();
        errorMsgVO.setNumber(orderNo);
        errorMsgVO.setErrorInfo(errMsg);
        return errorMsgVO;
    }

    /**
     * 删除待结算数据
     * @param orderMasterDOList 订单列表
     */
    private void deleteWaitingStatementData(List<OrderMasterDO> orderMasterDOList){
        // 待结算的订单需要删掉待结算记录
        List<Integer> waitingStatementOrderIdList = orderMasterDOList.stream().filter(orderMasterDO -> OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus())).map(OrderMasterDO::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(waitingStatementOrderIdList)){
            return;
        }
        statementPlatformClient.deleteWaitingStatementByOrderId(waitingStatementOrderIdList);
    }

    /**
     * 当前用户是否可以修改数据
     *
     * @param loginUserInfoBO
     * @param orderIdList
     */
    private void checkModifyDataPermission(LoginUserInfoBO loginUserInfoBO, List<Integer> orderIdList){
        List<RefOrgBusinessRpcDTO> refOrgList = organizationClient.listByBusinessTypeAndOperators(New.list(loginUserInfoBO.getUserGuid()));
        Set<Integer> allRefOrgIdSet = refOrgList.stream()
                .map(RefOrgBusinessRpcDTO::getBusinessOrgId)
                .collect(Collectors.toSet());
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Set<Integer> fixOrgIdSet = orderMasterDOList.stream().map(OrderMasterDO::getFuserid).collect(Collectors.toSet());
        boolean canFixData = allRefOrgIdSet.containsAll(fixOrgIdSet);
        if(!canFixData){
            List<OmsAccessDTO> accessDtoList = userClient.findAccessByGuid(loginUserInfoBO.getUserGuid());
            List<String> accessCode = accessDtoList.stream().map(OmsAccessDTO::getCode).collect(Collectors.toList());
            canFixData = accessCode.contains(OmsAccessEnum.ALL_ORG_DATA_CORRECT.getCode());
        }
        BusinessErrUtil.isTrue(canFixData, "当前用户不是该单位业务人员或没有数据修正权限");
    }

    /**
     * 数据修正-冻结经费
     *
     * @param omsLoginUserInfoBO OMS登录用户信息
     * @param request            冻结经费请求参数
     */
    @Override
    @ServiceLog(description = "数据修正-冻结经费", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public List<ErrorMsgVO> freezeFund(LoginUserInfoBO omsLoginUserInfoBO, FixFundDataRequest request) {
        // 基本参数校验
        Preconditions.notNull(request.getCallInterfaceFlag(), "请选择是否调用接口。");
        Preconditions.notEmpty(request.getIds(), "请选择需要操作的单据。");
        Preconditions.hasLength(request.getReason(), "请填写修正原因。");
        Preconditions.isTrue(Objects.nonNull(request.getChangeFundCardRequestDTO())
                && CollectionUtils.isNotEmpty(request.getChangeFundCardRequestDTO().getSaveProjectList()), "请选择需要冻结的经费卡。");

        String lockKey = OMS_FIX_DATA_REDIS_CACHE_KEY + OmsFixDataEnum.FUND_FREEZE.name() + omsLoginUserInfoBO.getUserGuid();
        boolean getLock = false;
        try {
            // 加锁处理
            getLock = cacheClient.tryLock(lockKey, 20);
            Preconditions.isTrue(getLock, "不允许连续点击【冻结经费】按钮！");
            // 校验权限
            checkModifyDataPermission(omsLoginUserInfoBO, request.getIds());

            // 初始化错误信息列表
            List<ErrorMsgVO> errorMsgVOList = New.list();

            // 查询订单信息
            List<Integer> orderIds = request.getIds();
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIds);
            if (CollectionUtils.isEmpty(orderMasterDOList)) {
                errorMsgVOList.add(new ErrorMsgVO(null, "未找到相关订单信息。"));
                return errorMsgVOList;
            }

            // 假设所有订单应来自同一单位，取第一个订单的orgId作为共同单位ID
            Integer orgId = orderMasterDOList.get(0).getFuserid();

            // 调用冻结接口，需校验当前单位是否开放此功能
            if (BooleanUtils.isTrue(request.getCallInterfaceFlag())) {
                OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(orgId);
                Preconditions.notNull(simpleOrgDTO,StrUtil.format("未找到组织信息，orgId:{}", orgId));
                Preconditions.isTrue(allowOmsFreezeFundOrgList.contains(orgId), StrUtil.format("{}未开启修改冻结经费，请联系技术开通单位修改权限。", simpleOrgDTO.getName()));
            }

            // 查询采购人信息
            List<Integer> userIds = orderMasterDOList.stream().map(OrderMasterDO::getFbuyerid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(userIds, orgId);
            BusinessErrUtil.notEmpty(userBaseInfoDTOList, ExecptionMessageEnum.NO_USER_INFO);
            Map<Integer, UserBaseInfoDTO> userId2UserDTOMap = DictionaryUtils.toMap(userBaseInfoDTOList, UserBaseInfoDTO::getId, Function.identity());

            // 对每个订单进行单独校验
            for (OrderMasterDO order : orderMasterDOList) {
                preValidateFreezeFund(order, orgId, userId2UserDTOMap, errorMsgVOList);
            }

            // 筛选出校验通过的订单
            List<String> failOrderNoList = errorMsgVOList.stream().map(ErrorMsgVO::getNumber).collect(Collectors.toList());
            List<OrderMasterDO> validPassOrderList = orderMasterDOList.stream()
                    .filter(order -> !failOrderNoList.contains(order.getForderno()))
                    .collect(Collectors.toList());

            // 开始执行冻结操作
            for (OrderMasterDO orderMasterDO : validPassOrderList) {
                try {
                    // 每次单个订单执行
                    Integer orderId = orderMasterDO.getId();
                    request.getChangeFundCardRequestDTO().setOrderIds(New.list(orderId));
                    freezeFundOrder(orderMasterDO, userId2UserDTOMap.get(orderMasterDO.getFbuyerid()), request);

                    // 成功后记录日志
                    Map<Integer, String> orderId2NewCardNoMap = getOrderId2CardNoMap(New.list(orderMasterDO));
                    Map<Integer, String> orderId2NewFundIdMap = getOrderId2FundIdMap(New.list(orderMasterDO));
                    this.saveFreezeFundLog(OmsFixDataEnum.FUND_FREEZE, orderMasterDO, omsLoginUserInfoBO, request, orderId2NewCardNoMap.get(orderId), orderId2NewFundIdMap.get(orderId));
                } catch (Exception e) {
                    LOGGER.error("数据修正-冻结经费失败, orderId: {}", orderMasterDO.getId(), e);
                    errorMsgVOList.add(new ErrorMsgVO(orderMasterDO.getForderno(), "冻结经费失败，原因：" + e.getMessage()));
                }
            }
            return errorMsgVOList;
        } catch (Exception e) {
            LOGGER.error("OMS批量冻结经费失败", e);
            throw e;
        } finally {
            if (getLock) {
                cacheClient.unlock(lockKey);
            }
        }
    }

    @ServiceLog(description = "数据修正-解冻经费", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    @Override
    public List<ErrorMsgVO> unfreezeFund(LoginUserInfoBO omsLoginUserInfoBO, FixFundDataRequest request) {
        // 基本参数校验
        Preconditions.notNull(request.getCallInterfaceFlag(), "请选择是否调用接口。");
        Preconditions.notEmpty(request.getIds(), "请选择需要操作的单据。");
        Preconditions.hasLength(request.getReason(), "请填写修正原因。");

        String lockKey = OMS_FIX_DATA_REDIS_CACHE_KEY + OmsFixDataEnum.FUND_UNFREEZE.name() + omsLoginUserInfoBO.getUserGuid();
        boolean getLock = false;
        try {
            // 加锁处理
            getLock = cacheClient.tryLock(lockKey, 20);
            Preconditions.isTrue(getLock, "不允许连续点击【解冻经费】按钮！");
            // 校验权限
            checkModifyDataPermission(omsLoginUserInfoBO, request.getIds());

            // 初始化错误信息列表
            List<ErrorMsgVO> errorMsgVOList = New.list();

            // 查询订单信息
            List<Integer> orderIds = request.getIds();
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIds);
            if (CollectionUtils.isEmpty(orderMasterDOList)) {
                errorMsgVOList.add(new ErrorMsgVO(null, "未找到相关订单信息。"));
                return errorMsgVOList;
            }

            // 假设所有订单应来自同一单位，取第一个订单的orgId作为共同单位ID
            Integer orgId = orderMasterDOList.get(0).getFuserid();

            // 查询采购人信息
            List<Integer> userIds = orderMasterDOList.stream().map(OrderMasterDO::getFbuyerid).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(userIds, orgId);
            BusinessErrUtil.notEmpty(userBaseInfoDTOList, ExecptionMessageEnum.NO_USER_INFO);
            Map<Integer, UserBaseInfoDTO> userId2UserDTOMap = DictionaryUtils.toMap(userBaseInfoDTOList, UserBaseInfoDTO::getId, Function.identity());

            // 对每个订单进行单独校验
            for (OrderMasterDO order : orderMasterDOList) {
                preValidateUnFreezeFund(order, orgId, userId2UserDTOMap, errorMsgVOList);
            }

            // 筛选出校验通过的订单
            List<String> failOrderNoList = errorMsgVOList.stream().map(ErrorMsgVO::getNumber).collect(Collectors.toList());
            List<OrderMasterDO> validPassOrderList = orderMasterDOList.stream()
                    .filter(order -> !failOrderNoList.contains(order.getForderno()))
                    .collect(Collectors.toList());

            // 构建订单id-经费卡号信息
            Map<Integer, String> orderId2OldCardNoMap = getOrderId2CardNoMap(validPassOrderList);
            // 构建订单id-经费id信息
            Map<Integer, String> orderId2OldFundIdMap = getOrderId2FundIdMap(validPassOrderList);

            // 开始执行解冻操作
            for (OrderMasterDO orderMasterDO : validPassOrderList) {
                try {
                    Integer orderId = orderMasterDO.getId();
                    // 每次单个订单执行
                    unfreezeFundOrder(orderMasterDO, request.getCallInterfaceFlag());
                    // 成功后记录日志
                    this.saveFreezeFundLog(OmsFixDataEnum.FUND_UNFREEZE, orderMasterDO, omsLoginUserInfoBO, request, orderId2OldCardNoMap.get(orderId), orderId2OldFundIdMap.get(orderId));
                } catch (Exception e) {
                    LOGGER.error("数据修正-解冻经费失败, orderId: {}", orderMasterDO.getId(), e);
                    errorMsgVOList.add(new ErrorMsgVO(orderMasterDO.getForderno(), "冻结经费失败，原因：" + e.getMessage()));
                }
            }
            return errorMsgVOList;
        } catch (Exception e) {
            LOGGER.error("OMS批量冻结经费失败", e);
            throw e;
        } finally {
            if (getLock) {
                cacheClient.unlock(lockKey);
            }
        }
    }

    /**
     * 经费冻结处理
     *
     * @param orderMasterDO 订单信息
     */
    private void freezeFundOrder(OrderMasterDO orderMasterDO, UserBaseInfoDTO userInfo, FixFundDataRequest request) {
        refFundcardOrderService.changeFundCard(userInfo, orderMasterDO.getFuserid(), request.getChangeFundCardRequestDTO()
                , false, false, request.getCallInterfaceFlag());

        // 中大附一，有绑卡记录但是未冻结的，无需修改为冻结
        if (Objects.equals(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue(), orderMasterDO.getFuserid())) {
            List<RefFundcardOrderDO> fundcardOrderDOList = refFundcardOrderMapper.findByOrderId(String.valueOf(orderMasterDO.getId()));
            if (CollectionUtils.isNotEmpty(fundcardOrderDOList)) {
                OrderMasterDO masterDO = orderMasterMapper.selectByPrimaryKey(orderMasterDO.getId());
                if (Objects.equals(masterDO.getFundStatus(), OrderFundStatusEnum.UN_FREEZE.getValue())) {
                    return;
                }
            }
        }
        // 如果不调用外部接口，在这修改经费状态为冻结成功
        if (BooleanUtils.isFalse(request.getCallInterfaceFlag())) {
            Integer orderId = orderMasterDO.getId();
            UpdateOrderParamDTO updateOrderDO = new UpdateOrderParamDTO();
            updateOrderDO.setOrderId(orderId);
            updateOrderDO.setFundStatus(OrderFundStatusEnum.Freezed.getValue());
            orderMasterMapper.updateOrderById(updateOrderDO);
        }
    }

    /**
     * 经费解冻处理
     *
     * @param orderMasterDO     订单信息
     * @param callInterfaceFlag 是否调用外部接口
     */
    private void unfreezeFundOrder(OrderMasterDO orderMasterDO, boolean callInterfaceFlag) {
        orderFundCardService.orderFundCardUnFreeze(orderMasterDO, null, callInterfaceFlag);
        // 如果不调用外部接口，在这修改经费状态为解冻成功
        if (BooleanUtils.isFalse(callInterfaceFlag)) {
            Integer orderId = orderMasterDO.getId();
            UpdateOrderParamDTO updateOrderDO = new UpdateOrderParamDTO();
            updateOrderDO.setOrderId(orderId);
            updateOrderDO.setFundStatus(OrderFundStatusEnum.ThrawSuccessed.getValue());
            orderMasterMapper.updateOrderById(updateOrderDO);
        }
    }


    /**
     * 数据修正-冻结经费前置校验
     */
    private void preValidateFreezeFund(OrderMasterDO order, Integer orgId, Map<Integer, UserBaseInfoDTO> userId2UserDTOMap, List<ErrorMsgVO> errorMsgVOList) {
        // 东营人医不支持此功能
        if (Objects.equals(OrgEnum.DONG_YING_SHI_REN_MIN_YI_YUAN.getValue(), orgId)) {
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "东营市人民医院有四级经费卡，暂不支持此功能。"));
            return;
        }
        UserBaseInfoDTO userBaseInfoDTO = userId2UserDTOMap.get(order.getFbuyerid());
        if (Objects.isNull(userBaseInfoDTO)) {
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "未找到采购人信息。"));
            return;
        }

        // 校验订单是否来自同一单位
        validateOrderSameOrg(order, orgId, errorMsgVOList);
        // 校验订单经费状态是否可以冻结
        validateOrderStatusCanFreeze(order, errorMsgVOList);
    }

    /**
     * 数据修正-冻结经费前置校验
     */
    private void preValidateUnFreezeFund(OrderMasterDO order, Integer orgId, Map<Integer, UserBaseInfoDTO> userId2UserDTOMap, List<ErrorMsgVO> errorMsgVOList) {
        UserBaseInfoDTO userBaseInfoDTO = userId2UserDTOMap.get(order.getFbuyerid());
        if (Objects.isNull(userBaseInfoDTO)) {
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "未找到采购人信息。"));
            return;
        }
        // 校验订单是否来自同一单位
        validateOrderSameOrg(order, orgId, errorMsgVOList);
        // 校验订单经费状态是否可以解冻
        validateOrderStatusCanUnFreeze(order, errorMsgVOList);
    }

    /**
     * 校验订单是否来自同一单位，并记录错误信息。
     *
     * @param order          当前订单
     * @param orgId          共同单位ID
     * @param errorMsgVOList 错误信息列表
     */
    private void validateOrderSameOrg(OrderMasterDO order, Integer orgId, List<ErrorMsgVO> errorMsgVOList) {
        Integer orderOrgId = order.getFuserid();
        // 检验订单是否来自同一单位
        if (!Objects.equals(orderOrgId, orgId)) {
            String orgName = null;
            try {
                orgName = OrgEnum.getOrgEnumById(orderOrgId).getName();
            } catch (Exception e) {
                LOGGER.error("获取单位名称失败,orgID:{}", orderOrgId, e);
            }
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "冻结经费只能选择相同采购单位的订单（" + orgName + "）"));
            return;
        }
    }

    /**
     * 校验订单经费状态是否可以冻结，并记录错误信息。
     *
     * @param order          当前订单
     * @param errorMsgVOList 错误信息列表
     */
    private void validateOrderStatusCanFreeze(OrderMasterDO order, List<ErrorMsgVO> errorMsgVOList) {
        // 仅允许冻结失败、未冻结、解冻成功的订单  冻结
        final Set<Integer> allowedStatuses = New.set(
                OrderFundStatusEnum.FreezedFail.getValue(),
                OrderFundStatusEnum.UN_FREEZE.getValue(),
                OrderFundStatusEnum.ThrawSuccessed.getValue()
        );
        if (!allowedStatuses.contains(order.getFundStatus())) {
            OrderFundStatusEnum fundStatusEnum = OrderFundStatusEnum.get(order.getFundStatus());
            String currentStatus = Objects.nonNull(fundStatusEnum) ? fundStatusEnum.getName() : "未知状态";
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "订单经费状态（" + currentStatus + "）无法冻结。"));
        }
        // 有绑卡记录但是未冻结的，暂时拦截确认
        List<RefFundcardOrderDO> fundcardOrderDOList = refFundcardOrderMapper.findByOrderId(String.valueOf(order.getId()));
        if (CollectionUtils.isNotEmpty(fundcardOrderDOList) && Objects.equals(order.getFundStatus(), OrderFundStatusEnum.UN_FREEZE.getValue())) {
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "订单存在绑卡记录，但经费处于未冻结状态，请联系开发人员确认处理"));
        }
    }


    /**
     * 校验订单经费状态是否可以解冻，并记录错误信息。
     *
     * @param order          当前订单
     * @param errorMsgVOList 错误信息列表
     */
    private void validateOrderStatusCanUnFreeze(OrderMasterDO order, List<ErrorMsgVO> errorMsgVOList) {
        // 仅允许已冻结、换卡失败、冻结成功的订单  解冻
        final Set<Integer> allowedStatuses = New.set(
                OrderFundStatusEnum.Freezed.getValue(),
                OrderFundStatusEnum.ThrawFailed.getValue(),
                OrderFundStatusEnum.ChangedCardFail.getValue()
        );
        if (!allowedStatuses.contains(order.getFundStatus())) {
            OrderFundStatusEnum fundStatusEnum = OrderFundStatusEnum.get(order.getFundStatus());
            String currentStatus = Objects.nonNull(fundStatusEnum) ? fundStatusEnum.getName() : "未知状态";
            errorMsgVOList.add(new ErrorMsgVO(order.getForderno(), "订单经费状态（" + currentStatus + "）无法解冻。"));
        }
    }

    private void saveFreezeFundLog(OmsFixDataEnum omsFixDataEnum, OrderMasterDO orderMasterDO, LoginUserInfoBO loginUserInfoBO,
                                   FixFundDataRequest request, String cardNo, String cardId) {
        String reason = request.getReason();
        String approveNo = request.getDingTalkApprovalNumber();
        String callInterfaceStr = BooleanUtils.isTrue(request.getCallInterfaceFlag()) ? "是" : "否";
        String preDesc = StringUtils.EMPTY;
        String operation = StringUtils.EMPTY;
        // 修改前状态
        if (OmsFixDataEnum.FUND_FREEZE.equals(omsFixDataEnum)) {
            operation = "冻结经费：" + cardNo + "\n" + "调用接口：" + callInterfaceStr;
            operation += LOG_FUND_CARD_ID_PREFIX + cardId;
        } else if (OmsFixDataEnum.FUND_UNFREEZE.equals(omsFixDataEnum)) {
            operation = "解冻经费" + "\n" + "调用接口：" + callInterfaceStr;
            preDesc = cardNo;
            preDesc += LOG_FUND_CARD_ID_PREFIX + cardId;
        }

        DataOperationLogDTO dataOperationLogDTO = new DataOperationLogDTO();
        dataOperationLogDTO.setOrderNo(orderMasterDO.getForderno());
        dataOperationLogDTO.setPreDesc(preDesc);
        dataOperationLogDTO.setFixReason(reason);
        dataOperationLogDTO.setOperatorGuid(loginUserInfoBO.getUserGuid());
        dataOperationLogDTO.setOperatorName(loginUserInfoBO.getUserName());
        dataOperationLogDTO.setApproveNumber(approveNo);
        dataOperationLogDTO.setOrgName(orderMasterDO.getFusername());
        dataOperationLogDTO.setOperation(operation);
        dataOperationLogDTO.setOperationType(omsFixDataEnum.getValue());
        dataOperationLogDTO.setOrgId(orderMasterDO.getFuserid());
        dataOperationLogDTO.setOperateTime(new Date());
        dataOperationLogRpcClient.insert(dataOperationLogDTO);
    }


    /**
     * 获取订单ID-经费ID
     * @param orderMasterDOList 订单列表
     *
     * @return 订单ID-经费ID信息
     */
    private Map<Integer, String> getOrderId2FundIdMap(List<OrderMasterDO> orderMasterDOList) {
        Map<Integer, String> orderId2FundIdMap = New.map();
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return orderId2FundIdMap;
        }
        List<String> orderIds = orderMasterDOList.stream().map(OrderMasterDO::getId).map(String::valueOf).collect(Collectors.toList());
        // 查询订单绑卡信息
        List<RefFundcardOrderDO> refFundcardOrderDOList = refFundcardOrderMapper.findByOrderIdIn(orderIds);
        if(CollectionUtils.isEmpty(refFundcardOrderDOList)) {
            return orderId2FundIdMap;
        }
        for (RefFundcardOrderDO refFundcardOrderDO : refFundcardOrderDOList) {
            orderId2FundIdMap.put(Integer.valueOf(refFundcardOrderDO.getOrderId()), refFundcardOrderDO.getCardId());
        }
        return orderId2FundIdMap;
    }



    /**
     * 获取经费卡号(多级/拼接卡号)  一级/二级/三级
     *
     * @param orderMasterDOList 订单列表
     * @return 订单ID-经费卡号信息
     */
    private Map<Integer, String> getOrderId2CardNoMap(List<OrderMasterDO> orderMasterDOList) {
        Map<Integer, String> orderId2CardNoMap = New.map();
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return orderId2CardNoMap;
        }
        Integer orgId = orderMasterDOList.get(0).getFuserid();
        List<String> orderIds = orderMasterDOList.stream().map(OrderMasterDO::getId).map(String::valueOf).collect(Collectors.toList());
        // 查询订单绑卡信息
        List<RefFundcardOrderDO> refFundcardOrderDOList = refFundcardOrderMapper.findByOrderIdIn(orderIds);
        if(CollectionUtils.isEmpty(refFundcardOrderDOList)) {
            return orderId2CardNoMap;
        }
        // 查询经费卡详情信息
        List<String> cardIds = refFundcardOrderDOList.stream().map(RefFundcardOrderDO::getCardId).distinct().collect(Collectors.toList());
        List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.getFundCardListByCardIds(cardIds, String.valueOf(orgId));

        if (CollectionUtils.isNotEmpty(fundCardDTOList)) {
            Map<String, FundCardDTO> cardId2FundCardMap = DictionaryUtils.toMap(fundCardDTOList, FundCardDTO::getId, Function.identity());
            // 遍历订单关联的经费卡信息
            for (RefFundcardOrderDO refFundcardOrder : refFundcardOrderDOList) {
                FundCardDTO fundCard = cardId2FundCardMap.get(refFundcardOrder.getCardId());
                if (Objects.nonNull(fundCard)) {
                    // 构建层级编码字符串
                    StringBuilder cardNoBuilder = new StringBuilder();
                    if (StringUtils.isNotBlank(fundCard.getFirstLevelCode())) {
                        cardNoBuilder.append(fundCard.getFirstLevelCode());
                    }
                    if (StringUtils.isNotBlank(fundCard.getSecondLevelCode())) {
                        cardNoBuilder.append("/").append(fundCard.getSecondLevelCode());
                    }
                    if (StringUtils.isNotBlank(fundCard.getThirdLevelCode())) {
                        cardNoBuilder.append("/").append(fundCard.getThirdLevelCode());
                    }

                    // 将订单ID和经费卡层级信息放入map
                    orderId2CardNoMap.put(Integer.valueOf(refFundcardOrder.getOrderId()),
                            cardNoBuilder.toString());
                }
            }
        }
        return orderId2CardNoMap;
    }

    /**
     * 关闭订单时释放库存
     *
     * @param orderMasterDO 订单信息
     */
    private void releaseStockForCloseOrder(OrderMasterDO orderMasterDO) {
        // 1. 获取订单信息
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.CANNOT_FIND_ORDER_PLEASE_RETRY);
        Integer orderId = orderMasterDO.getId();
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(orderId);
        BusinessErrUtil.notEmpty(orderDetailList, ExecptionMessageEnum.NO_PRODUCT_INFO_FOR_ORDER);

        // 2. 获取成功退货数量
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(New.list(orderId));
        // 过滤出正常且退货成功的记录
        goodsReturnList = goodsReturnList.stream()
                .filter(s -> GoodsReturnInvalidEnum.NORMAL.getCode().equals(s.getInvalid())
                        && GoodsReturnStatusEnum.SUCCESS.getCode().equals(s.getGoodsReturnStatus()))
                .collect(Collectors.toList());

        // 3. 按商品维度统计成功退货数量
        Map<String, BigDecimal> productReturnQuantityMap = New.map();
        for (GoodsReturn goodsReturn : goodsReturnList) {
            List<GoodsReturnInfoDetailVO> details = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            for (GoodsReturnInfoDetailVO detail : details) {
                String productId = detail.getProductId();
                BigDecimal returnQty = detail.getQuantity();
                // 累加退货数量
                productReturnQuantityMap.merge(productId, returnQty, BigDecimal::add);
            }
        }

        // 4. 构建释放库存参数
        List<BizWarehouseDangerousOccupyReturnGoodsDetailDTO> releaseDetails = orderDetailList.stream()
                .map(orderDetail -> {
                    BizWarehouseDangerousOccupyReturnGoodsDetailDTO detail = new BizWarehouseDangerousOccupyReturnGoodsDetailDTO();
                    detail.setProductId(orderDetail.getProductSn());
                    // 释放数量 = 原始数量- 成功退货数量
                    BigDecimal returnedQty = productReturnQuantityMap.getOrDefault(orderDetail.getProductSn().toString(), BigDecimal.ZERO);
                    int releaseQty = orderDetail.getFquantity().subtract(returnedQty).intValue();
                    // 如果计算出的释放数量大于0才需要释放
                    if (releaseQty > 0) {
                        detail.setNum(releaseQty);
                        return detail;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(releaseDetails)) {
            return;
        }

        BizWarehouseDangerousOccupyReturnGoodsDTO releaseStockParam = new BizWarehouseDangerousOccupyReturnGoodsDTO();
        releaseStockParam.setOrderNo(orderMasterDO.getForderno());
        releaseStockParam.setReturnGoodsDetailList(releaseDetails);

        // 5. 调用释放库存接口
        try {
            warehouseStockOccupyService.releaseStock(releaseStockParam);
        } catch (Exception e) {
            LOGGER.error("OMS关闭订单释放库存失败, 订单号:{}", orderMasterDO.getForderno(), e);
        }
    }
}
