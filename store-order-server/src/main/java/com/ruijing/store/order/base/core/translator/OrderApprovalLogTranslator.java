package com.ruijing.store.order.base.core.translator;

import com.reagent.local.deploy.api.order.dto.SyncOrderLogMessageDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderAllRelateLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.PurchaseOrderAllRelateLogVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: OrderApprovalLog对象转换类
 * @author: zhuk
 * @create: 2019-07-05 17:02
 **/
public class OrderApprovalLogTranslator {

    /**
     * 很久以前遗留下来的数据问题，因此经讨论约定这个1540 为供应商特别的标志
     */
    private static final int PROMISE_SUPPLIER_FLAG = 1540;

    /**
     * OrderApprovalLog  转成DTO 对象
     * @param orderApprovalLog
     * @return
     */
    public static OrderApprovalLogDTO orderApprovalLogToDto(OrderApprovalLog orderApprovalLog){
        OrderApprovalLogDTO orderApprovalLogDTO = new OrderApprovalLogDTO();
        orderApprovalLogDTO.setId(orderApprovalLog.getId());
        orderApprovalLogDTO.setOrderId(orderApprovalLog.getOrderId());
        orderApprovalLogDTO.setPhoto(orderApprovalLog.getPhoto());
        orderApprovalLogDTO.setReason(orderApprovalLog.getReason());
        orderApprovalLogDTO.setApproveStatus(orderApprovalLog.getApproveStatus());
        orderApprovalLogDTO.setApproveLevel(orderApprovalLog.getApproveLevel());

        Integer operatorId = orderApprovalLog.getOperatorId();
        orderApprovalLogDTO.setOperatorId(operatorId);

        OrderApprovalEnum approvalEnum = OrderApprovalEnum.getByValue(orderApprovalLog.getApproveStatus());
        //如果操作人是1540 而且是驳回状态 的话 既是（财务驳回了）供应商也操作取消 订单表获取供应商的名字 ----【特殊需求切记】
        if (PROMISE_SUPPLIER_FLAG == operatorId && OrderApprovalEnum.REJECT_FOR_STATEMENT.getValue().equals(orderApprovalLog.getApproveStatus())) {
            orderApprovalLogDTO.setApproveDescription("供应商取消结算");
        } else if (PROMISE_SUPPLIER_FLAG == operatorId && OrderApprovalEnum.CANCEL.getValue().equals(orderApprovalLog.getApproveStatus())) {
            orderApprovalLogDTO.setApproveDescription("供应商申请取消");
        } else {
            orderApprovalLogDTO.setApproveDescription(approvalEnum != null ? approvalEnum.getName() : "");
        }

        orderApprovalLogDTO.setCreationTime(orderApprovalLog.getCreationTime());
        return orderApprovalLogDTO;
    }

    /**
     * DTO 对象 转成 OrderApprovalLog
     * @param orderApprovalLogDTO
     * @return
     */
    public static OrderApprovalLog dtoToOrderApprovalLog(OrderApprovalLogDTO orderApprovalLogDTO){

        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setCreationTime(orderApprovalLogDTO.getCreationTime());
        orderApprovalLog.setId(orderApprovalLogDTO.getId());
        orderApprovalLog.setOrderId(orderApprovalLogDTO.getOrderId());
        orderApprovalLog.setPhoto(orderApprovalLogDTO.getPhoto());
        orderApprovalLog.setReason(orderApprovalLogDTO.getReason());
        orderApprovalLog.setApproveStatus(orderApprovalLogDTO.getApproveStatus());
        orderApprovalLog.setOperatorId(orderApprovalLogDTO.getOperatorId());
        return orderApprovalLog;

    }

    /**
     * DTO 对象 转成 OrderApprovalLog, 批量
     * @param dtoList
     * @return
     */
    public static List<OrderApprovalLog> dtoToOrderApprovalLog(List<OrderApprovalLogDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return New.emptyList();
        }

        return dtoList.stream().map(dto -> dtoToOrderApprovalLog(dto)).collect(Collectors.toList());
    }

    /**
     * OrderApprovalLog  转成DTO 对象
     * @param orderApprovalLog
     * @return
     */
    public static OrderApprovalLogDTO dtoToDto(com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalLogDTO orderApprovalLog){
        OrderApprovalLogDTO orderApprovalLogDTO = new OrderApprovalLogDTO();
        orderApprovalLogDTO.setId(orderApprovalLog.getId());
        orderApprovalLogDTO.setOrderId(orderApprovalLog.getOrderId());
        orderApprovalLogDTO.setPhoto(orderApprovalLog.getPhoto());
        orderApprovalLogDTO.setReason(orderApprovalLog.getReason());
        orderApprovalLogDTO.setApproveStatus(orderApprovalLog.getApproveStatus());
        orderApprovalLogDTO.setOperatorId(orderApprovalLog.getOperatorId());
        orderApprovalLogDTO.setCreationTime(orderApprovalLog.getCreationTime());
        return orderApprovalLogDTO;
    }

    /**
     * PurchaseOrderAllRelateLogVO 转成 OrderAllRelateLogDTO 对象
     * @param purchaseOrderAllRelateLogVOList 采购订单相关日志VO列表
     * @return 订单相关日志DTO列表
     */
    public static List<OrderAllRelateLogDTO> purchaseOrderAllRelateLogVO2DTO(List<PurchaseOrderAllRelateLogVO> purchaseOrderAllRelateLogVOList) {
        if (CollectionUtils.isEmpty(purchaseOrderAllRelateLogVOList)) {
            return New.emptyList();
        }
        
        return purchaseOrderAllRelateLogVOList.stream()
                .map(vo -> {
                    OrderAllRelateLogDTO dto = new OrderAllRelateLogDTO();
                    dto.setId(vo.getId());
                    dto.setOrderId(vo.getOrderId());
                    dto.setOrderNo(vo.getOrderNo());
                    dto.setPurchaseBidNo(vo.getPurchaseBidNo());
                    dto.setSettlementNo(vo.getSettlementNo());
                    dto.setOperationType(vo.getOperationType());
                    dto.setOperatorName(vo.getOperatorName());
                    dto.setCreateTime(vo.getCreateTime());
                    dto.setRemark(vo.getRemark());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public static SyncOrderLogMessageDTO do2SyncLogDTO(OrderApprovalLog logDo){
        if(logDo == null){
            return null;
        }
        return new SyncOrderLogMessageDTO()
                .setId(logDo.getId())
                .setOrderId(logDo.getOrderId())
                .setPhoto(logDo.getPhoto())
                .setReason(logDo.getReason())
                .setApproveLevel(logDo.getApproveLevel())
                .setApproveStatus(logDo.getApproveStatus())
                .setOperatorId(logDo.getOperatorId())
                .setCreationTime(logDo.getCreationTime());
    }
}
