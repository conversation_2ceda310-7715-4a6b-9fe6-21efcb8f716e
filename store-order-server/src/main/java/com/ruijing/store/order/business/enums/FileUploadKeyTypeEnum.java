package com.ruijing.store.order.business.enums;

import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.store.order.rpc.client.UploadFileClient;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Name: FileUploadKeyTypeEnum
 * Description: 记录订单附件中需要进行额外上传操作的文件类型
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/1/2
 */
public enum FileUploadKeyTypeEnum {

    // 入库单
    WAREHOUSE(FileBusinessTypeEnum.WAREHOUSE.getCode(), UploadFileClient.WARE_HOUSE_LIST_ID),

    // 出库单
    ORDER_LIST(FileBusinessTypeEnum.OUT_WAREHOUSE.getCode(), UploadFileClient.OUT_WAREHOUSE_ID),

    // 订单合同
    ORDER_CONTRACT(FileBusinessTypeEnum.ORDER_CONTRACT.getCode(), UploadFileClient.ORDER_CONTRACT_ID),
    ;

    private Integer fileTypeCode;

    private Integer keyId;


    FileUploadKeyTypeEnum(Integer fileTypeCode, Integer keyId){
        this.fileTypeCode = fileTypeCode;
        this.keyId = keyId;
    }

    public Integer getFileTypeCode() {
        return fileTypeCode;
    }

    public void setFileTypeCode(Integer fileTypeCode) {
        this.fileTypeCode = fileTypeCode;
    }

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }


    public static FileUploadKeyTypeEnum getByCode(Integer fileTypeCode){
        for (FileUploadKeyTypeEnum fileUploadKeyTypeEnum : FileUploadKeyTypeEnum.values()) {
            if (fileUploadKeyTypeEnum.getFileTypeCode().equals(fileTypeCode)){
                return fileUploadKeyTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否为需要额外上传的文件类型
     * @param fileCode          文件类型
     * @return
     */
    public static Boolean legalType(Integer fileCode){
        FileUploadKeyTypeEnum[] values = FileUploadKeyTypeEnum.values();
        List<FileUploadKeyTypeEnum> collect = Arrays.stream(values).filter(fileUploadKeyTypeEnum -> fileUploadKeyTypeEnum.getFileTypeCode().equals(fileCode))
                                                    .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(collect);
    }
}
