package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.apply.service.application.ApplyMasterExtensionService;
import com.ruijing.store.contract.ClinicalBudgetBizService;
import com.ruijing.store.contract.PriceContractBaseService;
import com.ruijing.store.contract.PriceContractBizService;
import com.ruijing.store.contract.dto.*;
import com.ruijing.store.contract.dto.budget.ClinicalBudgetTransactionDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/2/24 11:06
 * @Description
 **/
@ServiceClient
public class PriceContractClient {

    @MSharpReference(remoteAppkey = "store-price-contract-service")
    private PriceContractBizService priceContractBizService;

    @MSharpReference(remoteAppkey="store-apply-service")
    private ApplyMasterExtensionService applyMasterExtensionService;

    @MSharpReference(remoteAppkey = "store-price-contract-service")
    private PriceContractBaseService priceContractBaseService;

    @MSharpReference(remoteAppkey = "store-price-contract-service")
    private ClinicalBudgetBizService clinicalBudgetBizService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    /**
     * 临床采购单，释放价格合同的使用金额
     * @param clinicalBudgetTransactionDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "撤单或退货时释放试剂库商品年度使用金额、使用数量， 会校验预算金额管控是否满足释放")
    public boolean releaseReagentProductAnnualUsage(ClinicalBudgetTransactionDTO clinicalBudgetTransactionDTO){
        RemoteResponse<Boolean> response = clinicalBudgetBizService.releaseReagentProductAnnualUsage(clinicalBudgetTransactionDTO);
        Preconditions.isTrue(response.isSuccess(), "释放金额及用量异常");
        return response.getData();
    }

    /**
     * @description: 根据订单信息查找价格合同id
     * @date: 2021/2/24 11:04
     * @author: zengyanru
     * @param ftbuyappid
     * @return java.lang.long
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据订单信息查找价格合同id")
    public Long getPriceContractId(Integer ftbuyappid) {
        ApplyMasterExtensionQueryDTO applyMasterExtensionQuery = new ApplyMasterExtensionQueryDTO();
        applyMasterExtensionQuery.setApplyIds(New.list(ftbuyappid));
        RemoteResponse<List<ApplyMasterExtensionDTO>> response = applyMasterExtensionService.listByApplyIds(applyMasterExtensionQuery);
        Preconditions.notNull(response, "rpc方法applyMasterExtensionService.listByApplyIds调用出错\n入参: ftbuyappid=" + ftbuyappid);
        List<ApplyMasterExtensionDTO> applyExtraInfoList = response.getData();
        Preconditions.isTrue(response.isSuccess() && applyExtraInfoList != null,"rpc方法applyMasterExtensionService.listByApplyIds调用出错," + response.getMsg() + "\n入参: ftbuyappid=" + ftbuyappid);
        if (CollectionUtils.isEmpty(applyExtraInfoList)) {
            return null;
        }
        return applyExtraInfoList.get(0).getContractId();
    }

    /**
     * @description: 根据订单信息查找价格合同 list
     * @date: 2021/06/28 17:08
     * @author: zhangzhifeng
     * @param ftbuyappidList
     * @return java.lang.long
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据订单信息查找价格合同 list")
    public List<ApplyMasterExtensionDTO> getPriceContractList(List<Integer> ftbuyappidList) {
        ApplyMasterExtensionQueryDTO applyMasterExtensionQuery = new ApplyMasterExtensionQueryDTO();
        applyMasterExtensionQuery.setApplyIds(ftbuyappidList);
        RemoteResponse<List<ApplyMasterExtensionDTO>> response = applyMasterExtensionService.listByApplyIds(applyMasterExtensionQuery);
        Preconditions.notNull(response, "rpc方法applyMasterExtensionService.listByApplyIds调用出错\n入参: ftbuyappidList=" + ftbuyappidList);
        List<ApplyMasterExtensionDTO> applyExtraInfoList = response.getData();
        Preconditions.isTrue(response.isSuccess() && applyExtraInfoList != null,"rpc方法applyMasterExtensionService.listByApplyIds调用出错," + response.getMsg() + "\n入参: ftbuyappidList=" + ftbuyappidList);
        return applyExtraInfoList;
    }


    /**
     * @description: 获取价格合同基础信息
     * @date: 2021/3/9 10:00
     * @author: zengyanru
     * @param contractIdList
     * @return java.util.List<com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO>
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取价格合同基础信息")
    public List<PriceContractBaseBO> getPriceContractBaseList(List<Long> contractIdList) {
        Preconditions.notEmpty(contractIdList, "获取价格合同信息rpc方法getPriceContractBaseList入参不可为空");
        // aboolean need product info, aboolean1 need accessory
        ListPriceContractRequestDTO contractReq = new ListPriceContractRequestDTO();
        contractReq.setContractIds(contractIdList);
        RemoteResponse<List<PriceContractDTO>> response = priceContractBaseService.listPriceContractByIdList(contractReq);
        Preconditions.notNull(response, "获取价格合同信息rpc方法getPriceContractBaseList返回值为null， 入参\ncontractIdList:" + contractIdList);
        Preconditions.isTrue(response.isSuccess(), "获取价格合同信息rpc方法getPriceContractBaseList失败，入参\ncontractIdList:" + contractIdList);
        List<PriceContractBaseBO> contractBaseList = New.list();
        for (PriceContractDTO priceContractDTO : response.getData()) {
            PriceContractBaseBO priceContractBaseBO = new PriceContractBaseBO();
            priceContractBaseBO.setContractId(priceContractDTO.getId());
            priceContractBaseBO.setContractNo(priceContractDTO.getContractNo());
            contractBaseList.add(priceContractBaseBO);
        }
        return contractBaseList;
    }

}
