package com.ruijing.store.order.gateway.buyercenter.vo.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Zeng <PERSON>ru
 * @Date: 2020/12/29 10:47
 */
@RpcModel("合同-合同与订单信息返回体")
public class OrderContractInfoVO implements Serializable {

    private static final long serialVersionUID = -8684181564696726046L;

    /**
     * 合同相关-合同信息
     */
    @RpcModelProperty("合同相关-合同信息")
    private List<ContractInfoVO> contractList;

    /**
     * 合同相关-订单详情信息
     */
    @RpcModelProperty("合同相关-订单详情信息")
    private List<OrderDetailForContractVO> orderDetailInfoList;

    /**
     * 合同相关-订单基本信息
     */
    @RpcModelProperty("合同相关-订单基本信息")
    private List<OrderInfoForContractVO> orderBasicInfoList;

    public List<ContractInfoVO> getContractList() {
        return contractList;
    }

    public OrderContractInfoVO setContractList(List<ContractInfoVO> contractList) {
        this.contractList = contractList;
        return this;
    }

    public List<OrderDetailForContractVO> getOrderDetailInfoList() {
        return orderDetailInfoList;
    }

    public OrderContractInfoVO setOrderDetailInfoList(List<OrderDetailForContractVO> orderDetailInfoList) {
        this.orderDetailInfoList = orderDetailInfoList;
        return this;
    }

    public List<OrderInfoForContractVO> getOrderBasicInfoList() {
        return orderBasicInfoList;
    }

    public OrderContractInfoVO setOrderBasicInfoList(List<OrderInfoForContractVO> orderBasicInfoList) {
        this.orderBasicInfoList = orderBasicInfoList;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderContractInfoVO{");
        sb.append("contractList=").append(contractList);
        sb.append(", orderDetailInfoList=").append(orderDetailInfoList);
        sb.append(", orderBasicInfoList=").append(orderBasicInfoList);
        sb.append('}');
        return sb.toString();
    }
}
