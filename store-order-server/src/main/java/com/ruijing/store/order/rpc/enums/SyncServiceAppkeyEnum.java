package com.ruijing.store.order.rpc.enums;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/10 16:17
 **/
public enum SyncServiceAppkeyEnum {
    ALL(-1, "all"),
    ORDER(0, "store-search-sync-order-service"),
    PURCHASE(1, "store-search-sync-buyapp-service"),
    FUND_CARD(2, "research-search-sync-fundcard-service"),
    STATEMENT(3, "research-search-sync-state-service"),
    SUMMARY(4, "research-search-sync-sum-service"),
    BID(5, "store-search-sync-bid-service"),
    USER(6, "store-search-sync-user-service"),
    ;

    /**
     * appke 对应的编号
     */
    private Integer code;

    /**
     * 同步服务appkey
     */
    private String appkey;

    SyncServiceAppkeyEnum(Integer code, String appkey) {
        this.code = code;
        this.appkey = appkey;
    }

    public Integer getCode() {
        return code;
    }

    public String getAppkey() {
        return appkey;
    }

}
