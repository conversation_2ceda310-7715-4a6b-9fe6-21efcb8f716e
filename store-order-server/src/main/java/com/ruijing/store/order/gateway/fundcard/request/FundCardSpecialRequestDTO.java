package com.ruijing.store.order.gateway.fundcard.request;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/5/21 12:48
 * @Description: 经费卡换卡入参DTO
 */
@RpcModel("订单-换卡请求体")
public class FundCardSpecialRequestDTO implements Serializable {
    private static final long serialVersionUID = -4001512070512099039L;

    /**
     * 科目信息id
     */
    @RpcModelProperty("经费卡换卡入参列表")
    List<OrderFundCardParam> orderFundCardParams;

    public List<OrderFundCardParam> getOrderFundCardParams() {
        return orderFundCardParams;
    }

    public void setOrderFundCardParams(List<OrderFundCardParam> orderFundCardParams) {
        this.orderFundCardParams = orderFundCardParams;
    }
}
