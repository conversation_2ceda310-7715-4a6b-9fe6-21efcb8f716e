package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/12/2 15:17
 * @Description
 **/
public enum OrderBalanceAcceptEnum {
    BALANCE(1,"结算"),

    ACCEPT(0,"验收");

    private final Integer value;

    private final String desc;

    OrderBalanceAcceptEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderBalanceAcceptEnum getByName(String name) {
        for (OrderBalanceAcceptEnum orderBalanceAcceptEnum : OrderBalanceAcceptEnum.values()) {
            if (orderBalanceAcceptEnum.desc.equals(name)){
                return orderBalanceAcceptEnum;
            }
        }
        return null;
    }

    public static final OrderBalanceAcceptEnum getByValue(Integer value) {
        for (OrderBalanceAcceptEnum orderBalanceAcceptEnum : OrderBalanceAcceptEnum.values()) {
            if (orderBalanceAcceptEnum.value.equals(value)){
                return orderBalanceAcceptEnum;
            }
        }
        return null;
    }
}
