package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.batchcode.api.batches.dto.OrderProductBatchesDTO;
import com.ruijing.store.batchcode.api.batches.service.OrderBatchesRpcService;

import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2025-03-20 15:41
 * @description:
 */
@ServiceClient
public class OrderBatchesRpcClient {

    @MSharpReference
    private OrderBatchesRpcService orderBatchesRpcService;

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT, description = "写入订单批次信息")
    public void overwriteOrderBatches(List<OrderProductBatchesDTO> orderProductBatchesDTOList){
        RemoteResponse<Boolean> response = orderBatchesRpcService.overwriteOrderBatches(orderProductBatchesDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "查询批次")
    public List<OrderProductBatchesDTO> listBatchesByOrderNos(List<String> orderNos){
        RemoteResponse<List<OrderProductBatchesDTO>> response = orderBatchesRpcService.listBatchesByOrderNos(orderNos);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
