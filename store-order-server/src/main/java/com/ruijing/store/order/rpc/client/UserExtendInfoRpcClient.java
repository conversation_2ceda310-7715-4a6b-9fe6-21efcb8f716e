package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.user.api.dto.extend.UserExtendInfoDTO;
import com.ruijing.store.user.api.enums.user.UserExtendInfoTypeEnum;
import com.ruijing.store.user.api.service.UserExtendInfoRpcService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/12 15:53
 * @description
 */
@ServiceClient
public class UserExtendInfoRpcClient {

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserExtendInfoRpcService userExtendInfoRpcService;

    @ServiceLog(description = "查询用户扩展信息", serviceType = ServiceType.RPC_CLIENT)
    public List<UserExtendInfoDTO> findByUserIdList(List<Integer> userIdList, Integer orgId, UserExtendInfoTypeEnum userExtendInfoTypeEnum){
        RemoteResponse<List<UserExtendInfoDTO>> response = userExtendInfoRpcService.findBy(userIdList, orgId, userExtendInfoTypeEnum);
        Preconditions.isTrue(response.isSuccess(), "查询用户扩展信息记录失败");
        return response.getData();
    }
}
