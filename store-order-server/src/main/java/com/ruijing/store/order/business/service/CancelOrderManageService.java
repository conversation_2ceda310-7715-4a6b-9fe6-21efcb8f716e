package com.ruijing.store.order.business.service;

import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;

import java.util.List;

public interface CancelOrderManageService {

    /**
     * 拒绝取消订单
     * @param cancelOrderReqDTO
     * @return
     */
    void refuseCancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 同意取消订单
     * @param cancelOrderReqDTO
     */
    void agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 取消订单
     * @param applyCancelOrderReqDTO
     */
    void cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * 取消订单
     * @param applyCancelOrderReqDTO
     */
    void cancelOrderByThunder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * 取消线下订单接口，只有线下单会调用
     * @param cancelOrderReqDTO
     */
    void cancelOfflineOrder(CancelOrderReqDTO cancelOrderReqDTO);
}
