package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.order.base.order.service.RefFundCardOrderRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @description galaxy库的订单关联经费信息表客户端
 * @date 2024/2/5 下午 05:37
 */
@ServiceClient
public class RefFundCardOrderClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private RefFundCardOrderRpcService refFundCardOrderRpcService;

    /**
     * 根据订单id查询关联经费信息
     * @param orderIdList 订单id
     * @return 经费卡信息
     */
    public List<RefOrderFundCardDTO> listInOrderId(List<Integer> orderIdList){
        RemoteResponse<List<RefOrderFundCardDTO>> remoteResponse = refFundCardOrderRpcService.listInOrderId(orderIdList);
        Preconditions.isTrue(remoteResponse.isSuccess(), "查询经费信息失败");
        return remoteResponse.getData();
    }

    @ServiceLog(description = "插入关联经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public void batchInsertSelective(List<RefOrderFundCardDTO> list){
        RemoteResponse<Boolean> remoteResponse = refFundCardOrderRpcService.batchInsertSelective(list);
        Preconditions.isTrue(remoteResponse.isSuccess(), "批量插入订单信息失败");
    }

    @ServiceLog(description = "更新关联经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public void batchUpdateSelective(List<RefOrderFundCardDTO> list){
        RemoteResponse<Boolean> remoteResponse = refFundCardOrderRpcService.batchUpdateSelective(list);
        Preconditions.isTrue(remoteResponse.isSuccess(), "批量更新订单信息失败");
    }

    @ServiceLog(description = "根据订单id更新关联经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public void updateByOrderId(RefOrderFundCardDTO refOrderFundCardDTO) {
        RemoteResponse<Boolean> remoteResponse = refFundCardOrderRpcService.updateByOrderId(refOrderFundCardDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据订单id更新关联经费信息表");
    }

    @ServiceLog(description = "根据订单id更新关联经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public void deleteInOrderId(List<Integer> orderIdList) {
        RemoteResponse<Boolean> remoteResponse = refFundCardOrderRpcService.deleteInOrderId(orderIdList);
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据订单id更新关联经费信息表");
    }
}
