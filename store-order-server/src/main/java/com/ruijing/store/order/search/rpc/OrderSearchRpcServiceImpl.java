package com.ruijing.store.order.search.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.api.search.service.OrderSearchRpcService;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.search.translator.OrderPojoTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: Rpc
 * @author: zhuk
 * @create: 2019-08-22 19:31
 **/
@MSharpService
@ServiceLog
public class OrderSearchRpcServiceImpl implements OrderSearchRpcService {

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    private static final String CAT_TYPE = "OrderSearchRpcService";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private GoodsReturnService goodsReturnService;

    /**
     * 订单搜索通用查询
     * @param orderSearchParamDTO
     * @return
     */
    @Override
    public RemoteResponse<SearchPageResultDTO<OrderMasterSearchDTO>> commonSearch(OrderSearchParamDTO orderSearchParamDTO){
        SearchPageResultDTO searchPageResultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        return RemoteResponse.<SearchPageResultDTO<OrderMasterSearchDTO>>custom().setSuccess().setData(searchPageResultDTO).build();
    }

    /**
     * 根据结算单id 查询订单信息
     * @param orderSearchParamDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderMasterSearchDTO>> searchOrderByStatementIdList(OrderSearchParamDTO orderSearchParamDTO){
        List<Integer> statementIdList = orderSearchParamDTO.getStatementIdList();
        if (CollectionUtils.isEmpty(statementIdList)) {
            return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(New.emptyList()).build();
        }
        SortOrderEnum orderDateSort = orderSearchParamDTO.getOrderDateSort();
        List<OrderMasterSearchDTO> result = orderSearchBoostService.searchOrderByStatementIds(statementIdList ,orderDateSort);
        return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 供应商获取退货单数据
     * @param orderPullParamDTO 入参  pageSize 不能超过1000
     * @return RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>>
     */
    @Override
    public RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>> getReturnOrderForSupp(OrderPullParamDTO orderPullParamDTO){
        //获取退货单
        BasePageResponseDTO<GoodsReturn> returnOrderPage = goodsReturnService.getRangeDateReturnOrder(orderPullParamDTO);
        List<GoodsReturn> goodsReturnList = returnOrderPage.getData();
        List<SuppOrderPullDTO> resultList = New.listWithCapacity(goodsReturnList.size());
        //退货单关联的订单信息
        List<Integer> orderDetailList = goodsReturnList
                .stream()
                .map(GoodsReturn::getGoodsReturnDetailJSON)
                .map(GoodsReturnTranslator::parseJSONToInfoDetailVO)
                .flatMap(List::stream)
                .map(d -> Integer.parseInt(d.getDetailId()))
                .collect(Collectors.toList());
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = orderSearchBoostService.searchOrderByDetailIds(orderDetailList);
        //对象拼装
        for (OrderMasterSearchDTO orderMasterSearchDTO : orderMasterSearchDTOList) {
            List<OrderDetailSearchDTO> orderDetail = orderMasterSearchDTO.getOrderDetail();
            for (OrderDetailSearchDTO orderDetailSearchDTO : orderDetail) {
                for (GoodsReturn goodsReturn : goodsReturnList) {
                   if (orderDetailSearchDTO.getDetailId().equals(goodsReturn.getDetailId())){
                       SuppOrderPullDTO suppOrderPullDTO = new SuppOrderPullDTO();
                       suppOrderPullDTO.setReturnOrderNo(goodsReturn.getReturnNo());
                       suppOrderPullDTO.setOrderNo(orderMasterSearchDTO.getForderno());
                       suppOrderPullDTO.setSuppId(orderMasterSearchDTO.getFsuppid());
                       suppOrderPullDTO.setStatus(orderMasterSearchDTO.getStatus());
                       suppOrderPullDTO.setOrderAmount(orderMasterSearchDTO.getForderamounttotal());
                       suppOrderPullDTO.setConfirmTime(orderMasterSearchDTO.getFconfirmdate());
                       suppOrderPullDTO.setOrderUpdateTime(orderMasterSearchDTO.getUpdateTime());
                       suppOrderPullDTO.setOrderGenerateTime(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT,orderMasterSearchDTO.getForderdate()));
                       suppOrderPullDTO.setDeliveryTime(orderMasterSearchDTO.getFdeliverydate());
                       suppOrderPullDTO.setReturnApplyTime(goodsReturn.getCreationTime());
                       suppOrderPullDTO.setReturnConfirmTime(goodsReturn.getReplyTime());
                       suppOrderPullDTO.setReturnAmount(goodsReturn.getPrice());
                       suppOrderPullDTO.setReturnOrderUpdateTime(goodsReturn.getUpdateTime());
                       resultList.add(suppOrderPullDTO);
                   }
                }
            }
        }
        BasePageResponseDTO basePageResponseDTO = new BasePageResponseDTO(returnOrderPage.getPageNo(),returnOrderPage.getPageSize(),returnOrderPage.getTotal(),resultList);
        return  RemoteResponse.<BasePageResponseDTO<SuppOrderPullDTO>>custom().setData(basePageResponseDTO).setSuccess().build();
    }


    /**
     * 按时间范围 推送数据给供应商
     * @param orderPullParamDTO 入参
     * @return
     */
    @Override
    @ServiceLog
    public RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>> getBaseOrderForSupp(OrderPullParamDTO orderPullParamDTO){
        BasePageResponseDTO<OrderMasterSearchDTO> orderMasterBasePage = orderSearchBoostService.getBaseOrderByRangeDate(orderPullParamDTO);
        List<OrderMasterSearchDTO> orderMasterList = orderMasterBasePage.getData();
        List<SuppOrderPullDTO> resultList = orderMasterList.stream().map(OrderPojoTranslator::createSuppOrderPull).collect(Collectors.toList());
        BasePageResponseDTO<SuppOrderPullDTO> basePageResponseDTO = new BasePageResponseDTO(orderMasterBasePage.getPageNo()
                ,orderMasterBasePage.getPageSize(),orderMasterBasePage.getTotal(),resultList);
        return RemoteResponse.<BasePageResponseDTO<SuppOrderPullDTO>>custom().setSuccess().setData(basePageResponseDTO).build();
    }

    /**
     * 根据订单id 搜索订单
     * @param orderSearchParamDTO   订单id
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    @ServiceLog
    public RemoteResponse<List<OrderMasterSearchDTO>> searchOrderById(OrderSearchParamDTO orderSearchParamDTO){
        Integer orderId = orderSearchParamDTO.getOrderId();
        Assert.notNull(orderId,"订单id不能为空");
        List<OrderMasterSearchDTO> result = orderSearchBoostService.searchOrderById(orderId);
        return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 根据订单id 搜索订单
     * @param orderSearchParamDTO   订单idList 大小不能超过500
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    @ServiceLog
    public RemoteResponse<List<OrderMasterSearchDTO>> searchOrderByIdList(OrderSearchParamDTO orderSearchParamDTO){
        List<Integer> orderIdList = orderSearchParamDTO.getOrderIdList();
        if (CollectionUtils.isEmpty(orderIdList)) {
            return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(New.emptyList()).build();
        }
        Assert.isTrue(orderIdList.size()<=500,"orderIdList不能超过500");
        SortOrderEnum orderDateSort = orderSearchParamDTO.getOrderDateSort();
        List<OrderMasterSearchDTO> result = orderSearchBoostService.searchOrderByIdList(orderIdList ,orderDateSort);
        return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 根据订单idList 搜索订单.并计算返回 现在实际的金额
     * @param orderSearchParamDTO  订单id
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    @ServiceLog
    public RemoteResponse<List<OrderMasterSearchDTO>> searchActualOrderByIdList(OrderSearchParamDTO orderSearchParamDTO){
        List<Integer> orderIdList = orderSearchParamDTO.getOrderIdList();
        if (CollectionUtils.isEmpty(orderIdList)) {
            return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(New.emptyList()).build();
        }
        Assert.isTrue(orderIdList.size()<=500,"orderIdList不能超过500");
        SortOrderEnum orderDateSort = orderSearchParamDTO.getOrderDateSort();
        List<OrderMasterSearchDTO> result = orderSearchBoostService.getActualOrderByIdList(orderIdList ,orderDateSort);
        return RemoteResponse.<List<OrderMasterSearchDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 统计某段时间活跃供应商数量（排除演示供应商）
     * @param omsStatisticsParamDTO 时间范围，状态list， getNoSuppIdList
     * @return 供应商数量
     */
    @Override
    @ServiceLog
    public RemoteResponse<Double> countSuppForOms(OmsStatisticsParamDTO omsStatisticsParamDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countStatisticsOrder");
        RemoteResponse result;
        try {
            Double suppliers = orderSearchBoostService.countSuppliers(omsStatisticsParamDTO);
            result = RemoteResponse.custom().setData(suppliers).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",omsStatisticsParamDTO);
            logger.error("countSuppForOms异常： ", e);
            Cat.logError(CAT_TYPE, "countSuppForOms", JsonUtils.toJson(omsStatisticsParamDTO), e);
            return RemoteResponse.<Double>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 统计一段时间内总买家量（排除演示单位）
     * @param omsStatisticsParamDTO  fuserid列表（不包含）
     * @return 买家数量
     */
    @Override
    @ServiceLog
    public RemoteResponse<Double> countBuyersForOms(OmsStatisticsParamDTO omsStatisticsParamDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countStatisticsOrder");
        RemoteResponse result;
        try {
            Double buyers = orderSearchBoostService.countBuyers(omsStatisticsParamDTO);
            result = RemoteResponse.custom().setData(buyers).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",omsStatisticsParamDTO);
            logger.error("countBuyersForOms异常： ", e);
            Cat.logError(CAT_TYPE, "countBuyersForOms", JsonUtils.toJson(omsStatisticsParamDTO), e);
            return RemoteResponse.<Double>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 统计 订单金额，课题组数量，供应商数量
     * @param paramDTO 入参
     * @return 订单金额，课题组数量，供应商数量
     */
    @Override
    @ServiceLog
    public RemoteResponse<StatisticsManagerResultDTO> countStatisticsOrder(StatisticsManagerParamDTO paramDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countStatisticsOrder");
        RemoteResponse result;
        try {
            StatisticsManagerResultDTO resultDTO = orderSearchBoostService.countStatisticsOrder(paramDTO);
            result = RemoteResponse.custom().setData(resultDTO).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",paramDTO);
            logger.error("countStatisticsOrder异常： ", e);
            Cat.logError(CAT_TYPE, "countStatisticsOrder", JsonUtils.toJson(paramDTO), e);
            return RemoteResponse.<StatisticsManagerResultDTO>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 统计供应商数量
     * @param paramDTO 入参
     * @return 供应商数量
     */
    @Override
    @ServiceLog
    public RemoteResponse<StatisticsManagerResultDTO> countSupplierQuantity(StatisticsManagerParamDTO paramDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countSupplierQuantity");
        RemoteResponse result;
        try {
            StatisticsManagerResultDTO resultDTO = orderSearchBoostService.countSupplierQuantity(paramDTO);
            result = RemoteResponse.custom().setData(resultDTO).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",paramDTO);
            logger.error("countSupplierQuantity异常： ", e);
            Cat.logError(CAT_TYPE, "countSupplierQuantity", JsonUtils.toJson(paramDTO), e);
            return RemoteResponse.<StatisticsManagerResultDTO>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 统计课题组数量
     * @param paramDTO 入参
     * @return 课题组数量
     */
    @Override
    public RemoteResponse<StatisticsManagerResultDTO> countDepartmentQuantity(StatisticsManagerParamDTO paramDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countDepartmentQuantity");
        RemoteResponse result;
        try {
            StatisticsManagerResultDTO resultDTO = orderSearchBoostService.countDepartmentQuantity(paramDTO);
            result = RemoteResponse.custom().setData(resultDTO).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",paramDTO);
            logger.error("countDepartmentQuantity异常： ", e);
            Cat.logError(CAT_TYPE, "countDepartmentQuantity", JsonUtils.toJson(paramDTO), e);
            return RemoteResponse.<StatisticsManagerResultDTO>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 订单管理 统计订单金额
     * @param paramDTO 入参
     * @return 订单金额
     */
    @Override
    public RemoteResponse<StatisticsManagerResultDTO> countOrderTotalAmount(StatisticsManagerParamDTO paramDTO){
        Transaction transaction = Cat.newTransaction(CAT_TYPE,"countOrderTotalAmount");
        RemoteResponse result;
        try {
            Double amount = orderSearchBoostService.countOrderTotalAmount(paramDTO);
            StatisticsManagerResultDTO resultDTO = new StatisticsManagerResultDTO();
            resultDTO.setOriginalAmount(amount);
            result = RemoteResponse.custom().setData(resultDTO).setSuccess().build();
            transaction.setSuccess();
            transaction.addData("结果", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData("入参：",paramDTO);
            logger.error("countOrderTotalAmount 异常： ", e);
            Cat.logError(CAT_TYPE, "countDepartmentQuantity", JsonUtils.toJson(paramDTO), e);
            return RemoteResponse.<StatisticsManagerResultDTO>custom().setFailure(e.getMessage()).build();
        }finally {
            transaction.complete();
        }
        return result;
    }

    /**
     * 查询用户个状态的 订单数量
     * @param orderStatisticsParamDTO 入参 用户id 组织id
     * @return 订单状态 及 对应数量
     */
    @Override
    public RemoteResponse<Map<Integer,Integer>> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO){
        try {
            Map<Integer, Integer> resultMap = orderSearchBoostService.countOrderByStatus(orderStatisticsParamDTO);
            return RemoteResponse.<Map<Integer,Integer>>custom().setData(resultMap).setSuccess().build();
        } catch (Exception e) {
            logger.error("countOrderByStatus异常： ", e);
            Cat.logError(CAT_TYPE, "countOrderByStatus", "countOrderByStatus失败：" + e.getMessage() + "\n request：" + orderStatisticsParamDTO.toString(), e);
            return RemoteResponse.<Map<Integer,Integer>>custom().setFailure(e.getMessage()).build();
        }
    }


    /**
     * 查询商品 一段时间的销量 （时间段不能大大于60天）
     * @param productSalesParamDTO 入参
     * @return 结果
     */
    @Override
    @ServiceLog(description = "查询商品 一段时间的销量 ")
    public RemoteResponse<ProductSalesResultDTO> countProductSales(ProductSalesParamDTO productSalesParamDTO){
        Long productId = productSalesParamDTO.getProductId();
        if (productId == null){
            return RemoteResponse.<ProductSalesResultDTO>custom().setFailure("productId不能为空！").build();
        }

        try {
            double saleQuantity = orderSearchBoostService.countProductSales(productSalesParamDTO);
            ProductSalesResultDTO productSalesResultDTO = new ProductSalesResultDTO();
            productSalesResultDTO.setProductId(productId);
            productSalesResultDTO.setQuantity(saleQuantity);
            return RemoteResponse.<ProductSalesResultDTO>custom().setSuccess().setData(productSalesResultDTO).build();
        } catch (Exception e) {
            logger.error("countProductSales异常：",e);
            Cat.logError(CAT_TYPE,"countProductSales", JsonUtils.toJson(productSalesParamDTO),e);
            return RemoteResponse.<ProductSalesResultDTO>custom().setFailure("countProductSales异常").build();
        }
    }

    @Override
    @ServiceLog(description = "单位销量商品排名统计 ")
    public RemoteResponse<List<OrderOrgStatResultDTO>> countOrgTopProduct(OrderOrgStatParamDTO dto) {
        try {
           return  RemoteResponse.<List<OrderOrgStatResultDTO>>custom().setSuccess().setData(orderSearchBoostService.countOrgTopProduct(dto)).build();
        }catch (Exception e){
            logger.error("countOrgTopProduct异常：",e);
            Cat.logError(CAT_TYPE,"countOrgTopProduct", dto.toString(), e);
            return RemoteResponse.<List<OrderOrgStatResultDTO>>custom().setFailure("countOrgTopProduct异常").build();
        }
    }

    @Override
    public RemoteResponse<BigDecimal> sumSuppOrgSale(OrderOrgSuppStatParamDTO dto) {
        Transaction transaction  = Cat.newTransaction(CAT_TYPE,"sumSuppOrgSale");
        RemoteResponse remoteResponse = null;
        try {
            remoteResponse = RemoteResponse.custom().setSuccess().setData(orderSearchBoostService.sumSuppOrgSale(dto)).build();
            transaction.setSuccessStatus();
        }catch (Exception e){
            logger.error("sumSuppOrgSale异常：",e);
            transaction.setStatus(e);
            transaction.addData(JsonUtils.toJson(dto));
            Cat.logError(CAT_TYPE,"sumSuppOrgSale", dto.toString(), e);
            remoteResponse =  RemoteResponse.custom().setFailure("sumSuppOrgSale异常").build();
        }
        return remoteResponse;
    }

    /**
     * 订单属性 聚合 订单金额和数量
     * @param paramDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderAggregationResultDTO>> aggOrderAmountAndCount(StatisticsManagerParamDTO paramDTO){
        List<OrderAggregationResultDTO> orderAggregationResultDTOS = orderSearchBoostService.aggOrderAmountAndCount(paramDTO);
        return RemoteResponse.<List<OrderAggregationResultDTO>>custom().setSuccess().setData(orderAggregationResultDTOS). build();
    }

    /**
     * 订单商品属性 聚合  金额 和 数量
     * @param paramDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderAggregationResultDTO>> aggProductAmountAndCount(StatisticsManagerParamDTO paramDTO){
        List<OrderAggregationResultDTO> orderAggregationResultDTOS = orderSearchBoostService.aggProductAmountAndCount(paramDTO);
        return RemoteResponse.<List<OrderAggregationResultDTO>>custom().setSuccess().setData(orderAggregationResultDTOS). build();
    }

    /**
     * 订单商品属性 聚合  金额 和 数量
     * @param paramDTO
     * @return
     */
    @Override
    public RemoteResponse<List<OrderDateHistogramResultDTO>> aggOrderAmountDateHistogram(StatisticsManagerParamDTO paramDTO){
        List<OrderDateHistogramResultDTO> orderDateHistogramResultDTOS = orderSearchBoostService.aggOrderAmountDateHistogram(paramDTO);
        return RemoteResponse.<List<OrderDateHistogramResultDTO>>custom().setSuccess().setData(orderDateHistogramResultDTOS).build();
    }

    /**
     * 统计商品的 数量 和金额
     * @param paramDTO
     * @return
     */
    @Override
    public RemoteResponse<OrderAggregationResultDTO> sumProductAmountAndQuantity(StatisticsManagerParamDTO paramDTO){
        OrderAggregationResultDTO aggregationResultDTO = orderSearchBoostService.sumProductAmountAndQuantity(paramDTO);
        return RemoteResponse.<OrderAggregationResultDTO>custom().setSuccess().setData(aggregationResultDTO).build();
    }

    @Override
    public RemoteResponse<Map<OrderStatusEnum, Long>> countOrderStatusStatistics(OrderStatisticsParamDTO request) {
        final Map<OrderStatusEnum, Long> data = orderSearchBoostService.countOrderStatusStatistics(request);
        return RemoteResponse.<Map<OrderStatusEnum, Long>>custom().setSuccess().setData(data).build();
    }
}
