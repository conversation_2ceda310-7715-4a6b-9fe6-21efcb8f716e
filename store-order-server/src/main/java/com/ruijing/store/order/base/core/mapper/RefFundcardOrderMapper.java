package com.ruijing.store.order.base.core.mapper;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RefFundcardOrderMapper {
    int deleteByPrimaryKey(String id);

    int insert(RefFundcardOrderDO record);

    int insertSelective(RefFundcardOrderDO record);

    RefFundcardOrderDO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RefFundcardOrderDO record);

    int updateByPrimaryKey(RefFundcardOrderDO record);

    /**
     * 逻辑删除数据
     *
     * @param updatedIsDeleted
     * @param orderId
     * @return
     */
    int updateIsDeletedByOrderId(@Param("updatedIsDeleted") Boolean updatedIsDeleted, @Param("orderId") String orderId);

    /**
     * 物理删除数据
     *
     * @param orderId
     * @return
     */
    int deleteByOrderId(@Param("orderId") String orderId);

    /**
     * 根据订单id删除关联经费卡记录
     *
     * @param orderIdCollection
     * @return
     */
    int deleteByOrderIdIn(@Param("orderIdCollection") Collection<String> orderIdCollection);

    /**
     * 通过订单号获取关联经费卡信息
     *
     * @param orderIdCollection
     * @return
     */
    List<RefFundcardOrderDO> selectAllByOrderIdIn(@Param("orderIdCollection") Collection<String> orderIdCollection);

    /**
     * 根据采购单id数组查询
     * @param applicationIdCollection
     * @return
     */
    List<RefFundcardOrderDO> selectAllByApplicationIdIn(@Param("applicationIdCollection") Collection<String> applicationIdCollection);

    /**
     * 批量插入订单经费卡记录
     *
     * @param list
     * @return
     */
    int insertList(@Param("list") List<RefFundcardOrderDO> list);

    /**
     * 通过订单id 查询ref 经费卡关联对象
     *
     * @param orderIdCollection
     * @return
     */
    List<RefFundcardOrderDO> findByOrderIdIn(@Param("orderIdCollection") Collection<String> orderIdCollection);

    /**
     * 根据卡号查询
     * @param cardIdCollection
     * @return
     */
    List<String> findOrderIdByCardIds(@Param("cardIdCollection") Collection<String> cardIdCollection);

    /**
     * 根据采购单id查询
     * @param applicationId
     * @return
     */
    List<RefFundcardOrderDO> findByApplicationId(@Param("applicationId") String applicationId);

    /**
     * 根据订单id查询
     * @param orderId
     * @return
     */
    List<RefFundcardOrderDO> findByOrderId(@Param("orderId") String orderId);

    /**
     * 根据竞价单id查询绑卡记录
     * @param bidId
     * @return
     */
    List<RefFundcardOrderDO> findByBidId(@Param("bidId") String bidId);

    /**
     * 根据订单id更新记录
     * @param record
     * @return
     */
    int updateByOrderId(RefFundcardOrderDO record);

    /**
     * 根据采购单id更新绑卡记录
     * @param updated
     * @return
     */
    int updateByApplicationId(@Param("updated")RefFundcardOrderDO updated);

    /**
     * 根据竞价单id更新
     * @param updated
     * @return
     */
    int updateByBidId(@Param("updated")RefFundcardOrderDO updated);

    /**
     * 批量插入绑卡记录
     * @param list  绑卡数组
     * @return      插入数量
     */
    int batchInsertSelective(@Param("list") List<RefFundcardOrderDO> list);

    /**
     * 根据id批量删除
     * @param idCollection
     * @return
     */
    int deleteByIdIn(@Param("idCollection")Collection<String> idCollection);

}