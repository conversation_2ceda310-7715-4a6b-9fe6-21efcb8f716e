package com.ruijing.store.order.business.handler;

import com.reagent.auth.api.pojo.dto.UserDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.dto.WeChatMessageDTO;
import com.ruijing.message.api.dto.WeChatMessageDataDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.message.api.service.MessageService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.delivery.service.util.DeliveryProxyUtil;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.OrderAddressRPCClient;
import com.ruijing.store.order.rpc.client.SuppClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.rpc.client.UserSyncClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 微信消息处理类
 * @date ：Created in 2022-05-20 11:34
 */
@Service
@ServiceLog
public class WeChatMessageHandler {

    /**
     * 待确认订单标题
     */
    private static final String WAITING_CONFIRM_TITLE = "您有一张%s单位的新订单";


    /**
     * 自动取消清单
     */
    private static final String AUTO_CANCEL_ORDER_TITLE = "%s单位的订单自动取消";

    /**
     * 待确认订单备注
     */
    private static final String WAITING_CONFIRM_NOTE = "您还有%S张订单未确认，请尽快处理，点击查看订单详情";


    /**
     * 供应商超时未确认订单
     */
    private static final String CONFIRM_OVER_TIME_NOTE = "已超过45天未确认，系统自动取消，点击查看订单详情";

    /**
     * 超时未确认取消订单
     */
    private static final String CONFIRM_CANCEL_OVER_TIME = "取消申请14天未处理，系统自动取消，点击查看订单详情";

    /**
     * wwCat 采购人 订单详情链接
     */
    @PearlValue(key = "wechat.order.supp.detail.url")
    private String weChatSuppOrderDetailLink;

    @PearlValue(key = "weChat.serviceKey")
    private String serviceKey;

    @PearlValue(key = "weChat.templateId.waitingConfirm")
    private String waitingConfirmTemplateId;

    @PearlValue(key = "weChat.templateId.cancelOrder")
    private String cancelOrderTemplateId;

    /**
     * 拒绝取消代配送模板id
     */
    private final String CANCEL_PROXY_TEMPLATE_ID = Environment.isProdEnv() ? "b79sq1AvrtCqo1eN-EehZfdhgNOFvz_FEJovh1-PCuE" : "_ZDhZ9UjWApTgxodBx3BcB6UBLgoBfIVCc52Vt3TA8A";

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private MessageService messageService;

    @Resource
    private SuppClient suppClient;

    @Resource
    private UserSyncClient userSyncClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderAddressRPCClient orderAddressRpcClient;
    
    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    /**
     * 发送待确认订单的消息给供应商
     * @param orderMasterDO
     */
    public void waitingConfirmToSupp(OrderMasterDO orderMasterDO) {
        OrderAddressDTO orderAddressDTO = orderAddressRpcClient.findByOrderId(orderMasterDO.getId());
        this.waitingConfirmToSupp(orderMasterDO, orderAddressDTO);
    }
    
    public void waitingConfirmToSupp(OrderMasterDO orderMasterDO, OrderAddressDTO orderAddressDTO){
        boolean deliveryProxyOnWithBuyerOpen = DeliveryProxyUtil.isDeliveryProxyOnWithBuyerOpen(orderAddressDTO);
        // 获取待确认的订单数量
        int waitingConfirmCount = orderMasterMapper.countBySuppIdAndStatus(New.list(orderMasterDO.getFsuppid()), New.list(OrderStatusEnum.WaitingForConfirm.getValue()));

        // 获取openId
        List<String> openIds = getSupplierOpenId(orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());

        if(CollectionUtils.isEmpty(openIds)){
            return;
        }

        String note = String.format(WAITING_CONFIRM_NOTE, waitingConfirmCount) 
                + (deliveryProxyOnWithBuyerOpen ? "。采购用户已选择平台代配送服务，你可以进入“账号中心-代配送服务管理”去开启。" : StringUtils.EMPTY);
        // 封装消息体
        String title = String.format(WAITING_CONFIRM_TITLE, orderMasterDO.getFusername());
        MessageDTO messageDTO = generateMessageDTO(orderMasterDO, title, orderMasterDO.getForderdate(), note, openIds, waitingConfirmTemplateId);

        messageService.asyncSend(messageDTO);
    }

    /**
     * 发送拒绝取消代配送消息给供应商
     * @param orderMasterDO 订单数据
     * @param rejectReason 原因
     */
    public void sendRejectCancelDeliveryProxy2Supp(OrderMasterDO orderMasterDO, String rejectReason){
        // 查询openId
        List<String> openIds = getSupplierOpenId(orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());

        if(CollectionUtils.isEmpty(openIds)){
            return;
        }
        List<OrderApprovalLog> orderApprovalLogList = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(New.list(orderMasterDO.getId()), New.list(OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY.getValue()));
        String cancelReason = CollectionUtils.isEmpty(orderApprovalLogList) ? StringUtils.EMPTY : orderApprovalLogList.get(orderApprovalLogList.size() - 1).getReason();
        
        MessageDTO<WeChatMessageDTO> messageDTO = new MessageDTO<>();
        messageDTO.setOrgId(orderMasterDO.getFuserid());
        messageDTO.setMessageType(MessageTypeEnum.WECHAT_MESSAGE);
        WeChatMessageDTO dto = new WeChatMessageDTO();
        dto.setServiceKey(serviceKey);
        dto.setTemplateId(CANCEL_PROXY_TEMPLATE_ID);
        dto.setUserOpenIds(openIds);
        dto.setUrl(String.format(weChatSuppOrderDetailLink, orderMasterDO.getId()));
        List<WeChatMessageDataDTO> weChatMessageDataDTOList = new ArrayList<>();
        addWeChatMessageDataDTO(weChatMessageDataDTOList, "first", "拒绝取消代配送服务通知", null);
        addWeChatMessageDataDTO(weChatMessageDataDTOList, "keyword1", orderMasterDO.getForderno(), null);
        addWeChatMessageDataDTO(weChatMessageDataDTOList, "keyword2", cancelReason, null);
        addWeChatMessageDataDTO(weChatMessageDataDTOList, "remark", "采购人拒绝取消：" + rejectReason, "#FF0000");
        dto.setList(weChatMessageDataDTOList);
        messageDTO.setT(dto);

        messageService.asyncSend(messageDTO);
    }

    /**
     * 发送超时未确认订单的消息给供应商
     * @param orderMasterDOList
     */
    public void confirmOverTimeToSupp(List<OrderMasterDO> orderMasterDOList) {
        orderMasterDOList.forEach((orderMasterDO) ->
        {
            // 查询openId
            List<String> openIds = getSupplierOpenId(orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());

            if(CollectionUtils.isEmpty(openIds)){
                return;
            }

            String title = String.format(AUTO_CANCEL_ORDER_TITLE, orderMasterDO.getFusername());
            // 封装消息体
            MessageDTO messageDTO = generateMessageDTO(orderMasterDO, title, orderMasterDO.getFcanceldate(), CONFIRM_OVER_TIME_NOTE, openIds, cancelOrderTemplateId);

            messageService.asyncSend(messageDTO);
        });
    }

    /**
     * 发送超时未确认取消订单的消息给供应商（供应商超时未确认取消订单）
     * @param orderMasterDOList
     */
    public void confirmCancelOverTimeToSupp(List<OrderMasterDO> orderMasterDOList) {
        orderMasterDOList.forEach((orderMasterDO) -> {
            // 查询openId
            List<String> openIds = getSupplierOpenId(orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());

            if(CollectionUtils.isEmpty(openIds)){
                return;
            }

            String title = String.format(AUTO_CANCEL_ORDER_TITLE, orderMasterDO.getFusername());
            // 封装消息体
            MessageDTO messageDTO = generateMessageDTO(orderMasterDO, title, orderMasterDO.getFcanceldate(),CONFIRM_CANCEL_OVER_TIME, openIds, cancelOrderTemplateId);

            messageService.asyncSend(messageDTO);
        });
    }

    /**
     * 生成消息体
     */
    private MessageDTO<WeChatMessageDTO> generateMessageDTO(OrderMasterDO orderMasterDO, String title, Date date,String note, List<String> openIds, String templateId) {
        MessageDTO<WeChatMessageDTO> messageDTO = new MessageDTO<>();
        messageDTO.setOrgId(orderMasterDO.getFuserid());
        messageDTO.setMessageType(MessageTypeEnum.WECHAT_MESSAGE);
        WeChatMessageDTO dto = new WeChatMessageDTO();
        dto.setServiceKey(serviceKey);
        dto.setTemplateId(templateId);
        dto.setUserOpenIds(openIds);
        dto.setUrl(String.format(weChatSuppOrderDetailLink, orderMasterDO.getId()));
        List<WeChatMessageDataDTO> weChatMessageDataDTOS = new ArrayList<>();
        addWeChatMessageDataDTO(weChatMessageDataDTOS, "first", title, null);
        addWeChatMessageDataDTO(weChatMessageDataDTOS, "keyword1", orderMasterDO.getForderno(), null);
        addWeChatMessageDataDTO(weChatMessageDataDTOS, "keyword2", orderMasterDO.getForderamounttotal().setScale(2, BigDecimal.ROUND_HALF_UP).toString(), null);
        addWeChatMessageDataDTO(weChatMessageDataDTOS, "keyword3", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, date), null);
        addWeChatMessageDataDTO(weChatMessageDataDTOS, "remark", note, null);
        dto.setList(weChatMessageDataDTOS);
        messageDTO.setT(dto);

        return messageDTO;
    }

    /**
     * 获取供应商的openId
     *
     * @param suppId
     * @param orgId
     * @return
     */
    private List<String> getSupplierOpenId(Integer suppId, Integer orgId) {
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOS = suppClient.getSuppliersOrgBusinessUser(suppId, orgId);
        List<String> guids = suppUserDTOS.stream().map(com.ruijing.shop.crm.api.pojo.dto.UserDTO::getGuid).collect(Collectors.toList());
        return getUserByGuid(guids).stream().map(UserDTO::getOpenId).collect(Collectors.toList());

    }

    /**
     * 根据guid获取openId
     *
     * @param guids
     * @return
     */
    private List<UserDTO> getUserByGuid(List<String> guids) {
        List<UserDTO> userBaseInfoDTOS = userSyncClient.getUsersByGuid(guids);
        return userBaseInfoDTOS;
    }

    /**
     * 添加微信消息的消息体
     *
     * @param weChatMessageDataDTOS
     * @param keyword
     * @param content
     * @param color
     * @return
     */
    private List<WeChatMessageDataDTO> addWeChatMessageDataDTO(List<WeChatMessageDataDTO> weChatMessageDataDTOS, String keyword, String content, String color) {
        WeChatMessageDataDTO weChatMessageDataDTO = new WeChatMessageDataDTO();
        weChatMessageDataDTO.setKeyword(keyword);
        weChatMessageDataDTO.setContent(content);
        weChatMessageDataDTO.setColor(color);
        weChatMessageDataDTOS.add(weChatMessageDataDTO);
        return weChatMessageDataDTOS;
    }
}
