package com.ruijing.store.order.business.bo.buyercenter.operationlog;

import java.util.Date;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2021/1/12 21:42
 */
public class InboundBO {

    /**
     * 入库id
     */
    private String inboundId;

    /**
     * 操作日期
     */
    private Date operationTime;

    /**
     * 操作人
     */
    private String opeartor;

    /**
     * 自动入库1或手动入库2
     */
    private String value;

    public String getInboundId() {
        return inboundId;
    }

    public InboundBO setInboundId(String inboundId) {
        this.inboundId = inboundId;
        return this;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public InboundBO setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
        return this;
    }

    public String getOpeartor() {
        return opeartor;
    }

    public InboundBO setOpeartor(String opeartor) {
        this.opeartor = opeartor;
        return this;
    }

    public String getValue() {
        return value;
    }

    public InboundBO setValue(String value) {
        this.value = value;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InboundBO{");
        sb.append("inboundId='").append(inboundId).append('\'');
        sb.append(", operationTime=").append(operationTime);
        sb.append(", opeartor='").append(opeartor).append('\'');
        sb.append(", value='").append(value).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
