package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.request.OrderAddressRequestDTO;
import com.reagent.order.base.order.service.OrderAddressRPCService;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 订单地址RPC服务
 */
@ServiceClient
public class OrderAddressRPCClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderAddressRPCService orderAddressRPCService;

    @ServiceLog(description = "批量插入配送地址接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer insertList(List<OrderAddressDTO> request) {
        RemoteResponse<Integer> response = orderAddressRPCService.insertList(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "查询配送地址接口", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderAddressDTO> findByOrderId(List<Integer> orderIdList) {
        Preconditions.notEmpty(orderIdList, "orderIdList must not be empty");

        List<Long> orderIdParams = orderIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderIdList(orderIdParams);
        RemoteResponse<List<OrderAddressDTO>> response = orderAddressRPCService.listByOrderId(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "查询配送地址接口", serviceType = ServiceType.RPC_CLIENT)
    public OrderAddressDTO findByOrderNo(String orderNo) {
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderNumber(orderNo);
        RemoteResponse<OrderAddressDTO> response = orderAddressRPCService.findByOrderNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "查询操作过的待配送订单id", serviceType = ServiceType.RPC_CLIENT)
    public PageableResponse<List<OrderAddressDTO>>findOrderIdInOperatorIdAndDeliveryStatus(String guid, Integer deliveryStatus) {
        OrderAddressRequestDTO request = new OrderAddressRequestDTO();
        request.setOperatorGuid(guid);
        request.setDeliveryStatus(deliveryStatus);
        PageableResponse<List<OrderAddressDTO>> response = orderAddressRPCService.findOrderIdInOperatorIdAndDeliveryStatus(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response;
    }

    public OrderAddressDTO findByOrderId(Integer orderId) {
        List<OrderAddressDTO> result = findByOrderId(Collections.singletonList(orderId));
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 根据订单id和订单号，保存或更新送货地址
     * @param request
     * @return
     */
    public Boolean upsertAddress(OrderAddressDTO request) {
        try {
            RemoteResponse<Boolean> response = orderAddressRPCService.saveAddress(request);
            String errorMsg = response == null ? "" : response.getMsg();
            Preconditions.isTrue(response != null && response.isSuccess(), "根据订单id和订单号，保存或更新送货地址失败。" + errorMsg);
            return response.getData();
        } catch (Exception e) {
            Preconditions.isTrue(false, e.getMessage());
        }
        return true;
    }

    /**
     * 更新地址表信息，不连带更新订单表
     * @param request
     * @return
     */
    public Boolean updateAddress(OrderAddressDTO request) {
        try {
            RemoteResponse<Boolean> response = orderAddressRPCService.updateAddress(request);
            String errorMsg = response == null ? "" : response.getMsg();
            Preconditions.isTrue(response != null && response.isSuccess(), "更新地址表失败" + errorMsg);
            return response.getData();
        } catch (Exception e) {
            Preconditions.isTrue(false, e.getMessage());
        }
        return true;
    }

    /**
     * 获取订单id与实际最终收货地址的map
     * @param orderIdList
     * @return
     */
    public Map<Integer, String> orderUltimateAddress(List<Integer> orderIdList) {
        List<List<Integer>> partition = Lists.partition(orderIdList, 500);
        Map<Integer, String> orderIdAddressMap = new HashMap<>();
        for (List<Integer> part : partition) {
            List<OrderAddressDTO> addressList = this.findByOrderId(New.list(part));
            for (OrderAddressDTO addressDTO : addressList) {
                String receiverAddress = StringUtils.defaultIfBlank(addressDTO.getProvince(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getCity(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getRegion(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getAddress(), StringUtils.EMPTY);
                orderIdAddressMap.put(addressDTO.getId(), receiverAddress);
            }
        }
        return orderIdAddressMap;
    }

    /**
     * 批量更新订单地址信息
     * @param orderAddressDTOList
     */
    public void batchUpdateByOrderNo(List<OrderAddressDTO> orderAddressDTOList) {
        RemoteResponse<Boolean> response = orderAddressRPCService.batchUpdateByOrderNo(orderAddressDTOList);
        Preconditions.isTrue(response.isSuccess(), "批量更新订单地址信息失败" + response.getMsg());
    }
}
