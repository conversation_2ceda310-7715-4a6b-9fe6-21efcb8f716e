package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.RePushRPCService;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.research.reimbursement.api.ReimbursementConfigRPCService;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;

import java.util.Objects;

/**
 * @author: chenzhanliang
 * @createTime: 2024-11-06 15:20
 * @description:
 **/
@ServiceClient
public class RePushRPCServiceClient {


    @MSharpReference(remoteAppkey = "order-thunder-service")
    private RePushRPCService rePushRpcService;

    @ServiceLog(description = "重推", serviceType = ServiceType.RPC_CLIENT)
    public void rePush(String orderNo, String returnNo, OrderPushEventEnum event, Boolean ignoreFailedLog) {
        BusinessErrUtil.notNull(orderNo, "订单号不可空");
        ignoreFailedLog = Objects.nonNull(ignoreFailedLog) ? ignoreFailedLog : false;
        rePushRpcService.rePush(orderNo, returnNo, event, ignoreFailedLog);
    }
}
