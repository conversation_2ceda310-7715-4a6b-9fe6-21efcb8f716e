package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/22 16:44
 * @Description
 **/
@Model("订单详情-物流信息")
public class OrderLogisticsInfoVO implements Serializable {

    private static final long serialVersionUID = 7003057828472209141L;

    /**
     * 订单id
     */
    @ModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单号
     */
    @ModelProperty("订单号")
    private String orderNo;

    /**
     * 物流发货类型：1,商家自送；2,物流公司配送；
     */
    @ModelProperty("物流发货类型：1,商家自送；2,物流公司配送；")
    private Integer deliveryType;

    /**
     * 物流单号
     */
    @ModelProperty("物流单号")
    private String deliveryNo;

    /**
     * 发货说明
     */
    @ModelProperty("发货说明")
    private String deliveryRemark;

    /**
     * 物流公司
     */
    @ModelProperty("物流公司")
    private String logisticsCompany;

    @ModelProperty("物流公司logo url")
    private String logisticsCompanyLogoUrl;

    /**
     * 发货日期
     */
    @ModelProperty("发货日期")
    private String deliveryDate;

    @ModelProperty(value = "物流公司链接")
    private String logisticsCompanyLink;

    @ModelProperty("预计到达时间")
    private Date arrivalTime;

    @ModelProperty("物流详情")
    private List<OrderLogisticsInfoDetailVO> logisticsDetails;

    @ModelProperty("锐竞100订阅id")
    private Long subscribeId;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderLogisticsInfoVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderLogisticsInfoVO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public OrderLogisticsInfoVO setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
        return this;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public OrderLogisticsInfoVO setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
        return this;
    }

    public String getDeliveryRemark() {
        return deliveryRemark;
    }

    public OrderLogisticsInfoVO setDeliveryRemark(String deliveryRemark) {
        this.deliveryRemark = deliveryRemark;
        return this;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public OrderLogisticsInfoVO setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
        return this;
    }

    public String getLogisticsCompanyLogoUrl() {
        return logisticsCompanyLogoUrl;
    }

    public OrderLogisticsInfoVO setLogisticsCompanyLogoUrl(String logisticsCompanyLogoUrl) {
        this.logisticsCompanyLogoUrl = logisticsCompanyLogoUrl;
        return this;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getLogisticsCompanyLink() {
        return logisticsCompanyLink;
    }

    public void setLogisticsCompanyLink(String logisticsCompanyLink) {
        this.logisticsCompanyLink = logisticsCompanyLink;
    }

    public Date getArrivalTime() {
        return arrivalTime;
    }

    public OrderLogisticsInfoVO setArrivalTime(Date arrivalTime) {
        this.arrivalTime = arrivalTime;
        return this;
    }

    public List<OrderLogisticsInfoDetailVO> getLogisticsDetails() {
        return logisticsDetails;
    }

    public OrderLogisticsInfoVO setLogisticsDetails(List<OrderLogisticsInfoDetailVO> logisticsDetails) {
        this.logisticsDetails = logisticsDetails;
        return this;
    }

    public Long getSubscribeId() {
        return subscribeId;
    }

    public OrderLogisticsInfoVO setSubscribeId(Long subscribeId) {
        this.subscribeId = subscribeId;
        return this;
    }

    @Override
    public String toString() {
        return "OrderLogisticsInfoVO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", deliveryType=" + deliveryType +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", deliveryRemark='" + deliveryRemark + '\'' +
                ", logisticsCompany='" + logisticsCompany + '\'' +
                ", logisticsCompanyLogoUrl='" + logisticsCompanyLogoUrl + '\'' +
                ", deliveryDate='" + deliveryDate + '\'' +
                ", logisticsCompanyLink='" + logisticsCompanyLink + '\'' +
                ", arrivalTime=" + arrivalTime +
                ", logisticsDetails=" + logisticsDetails +
                ", subscribeId=" + subscribeId +
                '}';
    }
}
