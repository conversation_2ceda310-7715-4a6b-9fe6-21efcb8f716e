package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: zhu<PERSON>
 * @date : 2020/12/29 下午2:52
 * @description: 订单评价商品信息VO
 */
@RpcModel(value = "订单列表-评价-订单商品信息")
public class OrderDetailForCommentVO implements Serializable {

    private static final long serialVersionUID = -2397590585449618214L;

    @RpcModelProperty("订单详情Id")
    private Integer detailId;

    @RpcModelProperty("一级分类id")
    private Integer firstCategoryId;

    @RpcModelProperty("一级分类名称")
    private String firstCategoryName;

    @RpcModelProperty("CAS号")
    private String casNo;

    @RpcModelProperty("商品图片")
    private String productPicUrl;

    @RpcModelProperty("商品名称")
    private String productName;

    @RpcModelProperty("货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("供应商名称")
    private String suppName;

    @RpcModelProperty("供应商Id")
    private Integer suppId;

    @RpcModelProperty("商品id")
    private Long productId;


    @RpcModelProperty("包装规格")
    private String packingSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @RpcModelProperty("产品规格")
    private String productSpec;

    @RpcModelProperty("单位")
    private String unit;

    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("品牌")
    private String brand;


    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductPicUrl() {
        return productPicUrl;
    }

    public void setProductPicUrl(String productPicUrl) {
        this.productPicUrl = productPicUrl;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getPackingSpec() {
        return packingSpec;
    }

    public OrderDetailForCommentVO setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
        return this;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public OrderDetailForCommentVO setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public OrderDetailForCommentVO setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public OrderDetailForCommentVO setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
        return this;
    }

    public String getPress() {
        return press;
    }

    public OrderDetailForCommentVO setPress(String press) {
        this.press = press;
        return this;
    }

    public String getPurity() {
        return purity;
    }

    public OrderDetailForCommentVO setPurity(String purity) {
        this.purity = purity;
        return this;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public OrderDetailForCommentVO setProductSpec(String productSpec) {
        this.productSpec = productSpec;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public OrderDetailForCommentVO setUnit(String unit) {
        this.unit = unit;
        return this;
    }


    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public OrderDetailForCommentVO setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public OrderDetailForCommentVO setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public OrderDetailForCommentVO setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public OrderDetailForCommentVO setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderDetailForCommentVO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailForCommentVO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("firstCategoryId=" + firstCategoryId)
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("casNo='" + casNo + "'")
                .add("productPicUrl='" + productPicUrl + "'")
                .add("productName='" + productName + "'")
                .add("goodsCode='" + goodsCode + "'")
                .add("specification='" + specification + "'")
                .add("suppName='" + suppName + "'")
                .add("suppId=" + suppId)
                .add("productId=" + productId)
                .add("packingSpec='" + packingSpec + "'")
                .add("modelNumber='" + modelNumber + "'")
                .add("medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + "'")
                .add("completionCycle='" + completionCycle + "'")
                .add("press='" + press + "'")
                .add("purity='" + purity + "'")
                .add("productSpec='" + productSpec + "'")
                .add("unit='" + unit + "'")
                .add("dangerousTag='" + dangerousTag + "'")
                .add("brand='" + brand + "'")
                .toString();
    }
}
