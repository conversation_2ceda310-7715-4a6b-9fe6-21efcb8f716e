package com.ruijing.store.order.cache;

import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 订单消息本地缓存
 */
public class OrderMessageCache {

    /**
     * 订单标志符缓存集合，记录各个业务的操作，
     * 可以用作提前标记订单已完成某些操作
     *
     * 规则：操作类型+订单id+时间戳
     */
    public static final Set<String> ORDER_MESSAGE_CACHE = new CopyOnWriteArraySet<>();

    /**
     * 订单入库完成的标志符
     */
    public static final String ORDER_INBOUND_COMPLETED = "order_inbound_completed";
}

