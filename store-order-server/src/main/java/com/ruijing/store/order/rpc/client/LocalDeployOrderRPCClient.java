package com.ruijing.store.order.rpc.client;

import com.reagent.local.deploy.api.order.SyncOrderRPCService;
import com.reagent.local.deploy.api.order.dto.SyncOrderMessageDTO;
import com.ruijing.base.mq.rpc.MqRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.constant.TransportConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2020/9/29 17:37
 **/
@ServiceClient
@CatAnnotation
public class LocalDeployOrderRPCClient {

    @MSharpReference(
            remoteAppkey = "local-deploy-platform-web",
            serviceUrls = "${order.local.deploy.url}",
            transportType = TransportConstant.HTTP_1_1,
            timeout = "200000"
    )
    private SyncOrderRPCService syncOrderRPCService;

    @MSharpReference(remoteAppkey = "base-biz-mq-service")
    private MqRpcService<SyncOrderMessageDTO> mqRpcService;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @ServiceLog(description = "同步订单数据RPC接口", serviceType = ServiceType.COMMON_SERVICE)
    public void syncOrderData(List<SyncOrderMessageDTO> request) {
        logger.info("本地部署同步订单数据记录数{}", request.size());
        RemoteResponse<Integer> response = syncOrderRPCService.syncOrderData(request);
        Preconditions.isTrue(response.isSuccess(), "本地部署同步订单数据失败：" + JsonUtils.toJsonIgnoreNull(response));
    }

    @ServiceLog(description = "本地化部署同步数据放到MQ中", serviceType = ServiceType.COMMON_SERVICE)
    public void push2MQ(int orgId, List<SyncOrderMessageDTO> syncOrderMessageDTOList){
        RemoteResponse response = mqRpcService.localPush("77_order_data", orgId, syncOrderMessageDTOList);
        Preconditions.isTrue(response.isSuccess(), "推送订单数据到本地部署失败!" + JsonUtils.toJsonIgnoreNull(response));
    }

}
