package com.ruijing.store.order.base.excel.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class OrderPrintQueryDTO implements Serializable {

    private static final long serialVersionUID = 7857935010435710052L;

    /**
     * 订单开始时间
     */
    @RpcModelProperty(value = "订单开始时间")
    private Date startDate;

    /**
     * 订单截至时间
     */
    @RpcModelProperty(value = "订单截至时间")
    private Date endDate;

    /**
     * 商品信息
     */
    @RpcModelProperty(value = "商品信息")
    private String productInfo;

    /**
     * 关键字
     */
    @RpcModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 订单状态
     */
    @RpcModelProperty(value = "订单状态")
    private Integer status;

    /**
     * 线上单线下单
     */
    @RpcModelProperty(value = "0-线上单，1-线下单")
    private Integer species = 0;

    /**
     * 内部使用，需要去除的状态单据，目前仅影响到excel的导出，在此写死
     */
    private List<Integer> excludeStatusList = New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue());

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(String productInfo) {
        this.productInfo = productInfo;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public List<Integer> getExcludeStatusList() {
        return excludeStatusList;
    }

    public OrderPrintQueryDTO setExcludeStatusList(List<Integer> excludeStatusList) {
        this.excludeStatusList = excludeStatusList;
        return this;
    }
}
