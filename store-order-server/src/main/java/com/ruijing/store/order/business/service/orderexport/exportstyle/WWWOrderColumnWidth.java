package com.ruijing.store.order.business.service.orderexport.exportstyle;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.ruijing.store.order.business.service.orderexport.ExportHeaderName;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/7/13 10:11
 */
public class WWWOrderColumnWidth extends AbstractColumnWidthStyleStrategy {
    // www 订单 名称与Pair<idx，width>
    private static Map<Integer, Pair<String, Double>> headerIdxWidthMap = new HashMap<Integer, Pair<String, Double>>() {{
        put(0, Pair.of(ExportHeaderName.orderNo.get(0), 20.0));
        put(1, Pair.of(ExportHeaderName.purchaseGroup.get(0), 25.0));
        put(2, Pair.of(ExportHeaderName.buyer.get(0), 15.0));
        put(3, Pair.of(ExportHeaderName.contactMan.get(0), 15.0));
        put(4, Pair.of(ExportHeaderName.receivePhone.get(0), 25.0));
        put(5, Pair.of(ExportHeaderName.receiveAddr.get(0), 135.0));
        put(6, Pair.of(ExportHeaderName.supplier.get(0), 45.0));
        put(7, Pair.of(ExportHeaderName.toSupplierNote.get(0), 115.0));
        put(8, Pair.of(ExportHeaderName.orderDate.get(0), 18.0));
        put(9, Pair.of(ExportHeaderName.orderAmount.get(0), 11.8));
        put(10, Pair.of(ExportHeaderName.successFulReturnAmount.get(0), 11.8));
        put(11, Pair.of(ExportHeaderName.orderStatus.get(0), 15.0));
        put(12, Pair.of(ExportHeaderName.firstCardCode.get(0), 15.0));
        put(13, Pair.of(ExportHeaderName.firstCardName.get(0), 15.0));
        put(14, Pair.of(ExportHeaderName.secondCardCode.get(0), 15.0));
        put(15, Pair.of(ExportHeaderName.secondCardName.get(0), 15.0));
        put(16, Pair.of(ExportHeaderName.thirdCardCode.get(0), 15.0));
        put(17, Pair.of(ExportHeaderName.thirdCardName.get(0), 15.0));
    }};

    /**
     * Sets the column width when head create
     * @param writeSheetHolder
     * @param cellDataList
     * @param cell
     * @param head/
     * @param relativeRowIndex
     * @param isHead
     */
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        int curColIdx = cell.getColumnIndex();
        Pair<String, Double> headWidthPair = headerIdxWidthMap.get(curColIdx);
        if (headWidthPair != null) {
            writeSheetHolder.getSheet().setColumnWidth(curColIdx, (int)Math.round(headWidthPair.getRight())*256);
        }
    }
}
