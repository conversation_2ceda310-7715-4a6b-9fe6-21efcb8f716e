package com.ruijing.store.order.business.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.order.api.base.ordermaster.dto.AdditionalAcceptancePicDTO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderAcceptPicRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderDetailAcceptPicRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OverrideUploadImageRequest;

import java.util.List;

/**
 * @author: chenzhanliang
 * @createTime: 2024-09-23 10:12
 * @description:
 **/
public interface OrderPicRelatedService {

    /**
     * 保存追加验收图片到数据库（含条件校验）
     *
=     * @param rjSessionInfo
     * @return java.lang.Boolean
     */
     Boolean checkAndSaveAppendAcceptanceImages(RjSessionInfo rjSessionInfo, OrderAcceptPicRequest orderAcceptPicRequest);

    void appendDetailAcceptanceImages(RjSessionInfo rjSessionInfo, OrderDetailAcceptPicRequest orderDetailAcceptPicRequest);

    /**
     * 追加验收图片（不含条件校验）
     * @param additionalAcceptancePicDTO
     */
    void appendReceiveImagesSkipCheck(AdditionalAcceptancePicDTO additionalAcceptancePicDTO);


    /**
     * 保存替换验收图片搭配数据库
     *
     * @param rjSessionInfo
     * @param orderId
     * @param filePaths
     * @return
     */
    Boolean overwriteAcceptanceImages(RjSessionInfo rjSessionInfo, Integer orderId, List<String> filePaths);

    /**
     * 验收审批--覆盖上传验收图片
     */
    void acceptApprovalOverrideImage(RjSessionInfo rjSessionInfo, OverrideUploadImageRequest request);

}
