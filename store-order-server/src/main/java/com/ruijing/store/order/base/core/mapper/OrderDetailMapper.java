package com.ruijing.store.order.base.core.mapper;
import java.math.BigDecimal;

import com.ruijing.store.order.api.sysu.dto.OrderDetailPriceDTO;
import com.ruijing.store.order.base.core.model.DeficiencyDetailDO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.SimpleDetailInfoDO;
import com.ruijing.store.order.base.core.model.UpdateDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface OrderDetailMapper {

    /**
     * 根据逐渐查询订单详情
     * @param id
     * @return
     */
    OrderDetailDO selectByPrimaryKey(Integer id);

    /**
     *根据 orderMasterId 查询订单详情
     * @param fmasterid
     * @return
     */
    List<OrderDetailDO> findByFmasterid(@Param("fmasterid")Integer fmasterid);

    /**
     *根据 orderMasterId 数组 查询订单详情
     * @param fmasteridCollection
     * @return
     */
    List<OrderDetailDO> findAllByFmasteridIn(@Param("fmasteridCollection")Collection<Integer> fmasteridCollection);

    int updateByPrimaryKeySelective(OrderDetailDO record);

    /**
     * 根据退货状态查找订单id
     * @param returnStatusCollection
     * @return
     */
    Set<Integer> findFmasteridByReturnStatusIn(@Param("masterIdList")List<Integer> masterIdList, @Param("returnStatusCollection")Collection<Integer> returnStatusCollection);

    /**
     * 根据detailId查找商品id
     * @param id
     * @return
     */
    Long findProductSnById(@Param("id")Integer id);

    /**
     * 谨慎使用！目前只有中大在使用；
     * 批量更新订单商品名，用于将订单商品的的英文名改成中文，更新的条数不宜过多
     * @param updatedFgoodname
     * @param idCollection
     * @return
     */
    int updateFgoodnameByIdIn(@Param("updatedFgoodname")String updatedFgoodname,@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 批量更新订单明细
     * orderDetailList 明细数组
     * @return
     */
    int loopUpdateByIdIn(@Param("list") List<OrderDetailDO> orderDetailList);

    /**
     * 批量插入订单明细
     * @param list
     * @return
     */
    int insertList(@Param("list")List<OrderDetailDO> list);

    /**
     * 通过idlist查询部分退货展示信息
     * @return
     */
    List<SimpleDetailInfoDO> selectPartInfoByIdList(@Param("idCollection")Collection<Integer> idCollection);

    Integer selectLastId();

    /**
     * 根据id批量查询
     * @param idCollection
     * @return
     */
    List<OrderDetailDO> findByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 查询空报销类型（入库类型）的detail id条目
     * @return
     */
    List<Integer> selectEmptyCategoryTagItem();

    /**
     * 不侵入update time的依照detailId批量更新categoryTag和feetypetag
     * @param detailList
     * @return
     */
    Integer completeCategoryTagAndFeeTag(@Param("detailList") List<OrderDetailDO> detailList);

    /**
     * 范围查询detail id列表
     * @param minId
     * @param maxId
     * @return
     */
    List<Integer> selectIdByIdBetween(@Param("minId")Integer minId,@Param("maxId")Integer maxId);

    /**
     * 通过master id 高低查询订单明细日期相关的信息
     * @param minMasterId
     * @param maxMasterId
     * @return
     */
    List<OrderDetailDO> selectDateAndRelatedInfoByMasterIdBetween(@Param("minMasterId")Integer minMasterId, @Param("maxMasterId")Integer maxMasterId);

    /**
     * 根据id批量更新detail，后续有需要可在此增加字段
     * @param updatedList
     * @return
     */
    int batchUpdateBidDate(@Param("updatedList")List<OrderDetailDO> updatedList);

    /**
     * 通过订单详情id查询剩余金额
     * @param idCollection
     * @return
     */
    List<OrderDetailDO> selectRemainderPriceByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 通过id删除订单详情
     * @param idCol
     */
    void deleteInId(@Param("idCol") Collection<Integer> idCol);

    void batchUpdatePrice(@Param("col") List<OrderDetailPriceDTO> orderDetailPriceDTOList);


    /**
     * 更新商品分类信息
     *
     * @param orderDetailDOList
     */
    void batchUpdateCategory(@Param("orderDetailDOList") List<OrderDetailDO> orderDetailDOList);

    /**
     * 根据订单id查订单列表商品信息
     * @param orderIds 订单商品id列表
     * @return 订单商品详情
     */
    List<OrderDetailDO> selectOrderListDetailByOrderId(@Param("orderIds") Collection<Integer> orderIds);
}
