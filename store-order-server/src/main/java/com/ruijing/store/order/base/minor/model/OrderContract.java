package com.ruijing.store.order.base.minor.model;

import java.util.Date;

public class OrderContract {
    private Integer id;

    /**
    * 订单id
    */
    private Integer orderId;

    /**
    * 合同文件地址
    */
    private String contractLocation;

    /**
    * 合同文件名
    */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getContractLocation() {
        return contractLocation;
    }

    public void setContractLocation(String contractLocation) {
        this.contractLocation = contractLocation;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderContract{");
        sb.append("id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", contractLocation='").append(contractLocation).append('\'');
        sb.append(", contractName='").append(contractName).append('\'');
        sb.append(", contractNo='").append(contractNo).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append('}');
        return sb.toString();
    }
}