package com.ruijing.store.order.gateway;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.OrderCommonPrintDataDTO;
import com.ruijing.store.order.api.gateway.dto.DeliveryNoteDTO;
import com.ruijing.store.order.api.gateway.dto.OrderOMSConfigRequestDTO;
import com.ruijing.store.order.api.gateway.dto.OrderOMSConfigResponseDTO;
import com.ruijing.store.order.api.gateway.service.OrderManageGWService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

/**
 * @author: zhukai
 * @date : 2020/9/2 4:59 下午
 * @description: 订单管理 网关服务
 */

@MSharpService(isGateway = "true")
@RpcMapping("/orderManage")
public class OrderManageGWServiceImpl implements OrderManageGWService {

    @Autowired
    private OrderManageService orderManageService;

    @Autowired
    private OrderManageRpcService orderManageRpcService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private UserClient userClient;

    @Override
    @RpcMapping("/getDeliveryNote")
    @RpcMethod("订单列表-打印送货单,后续会废弃,请使用/getCommonPrintData")
    @ServiceLog(description = "获取送货单数据,请使用/getCommonPrintData")
    @Deprecated
    public RemoteResponse<DeliveryNoteDTO> getDeliveryNote(OrderBasicParamDTO orderBasicParamDTO) {
        Integer orderId = orderBasicParamDTO.getOrderId();
        Preconditions.notNull(orderId,"订单id不能为空！");
        DeliveryNoteDTO deliveryNoteDTO = orderManageService.assembleDeliveryNote(orderId);
        return RemoteResponse.<DeliveryNoteDTO>custom().setSuccess().setData(deliveryNoteDTO);
    }

    @Override
    @RpcMapping("/getCommonPrintData")
    @RpcMethod("订单列表-打印单据")
    public RemoteResponse<OrderCommonPrintDataDTO> getCommonPrintData(OrderCommonPrintParamDTO request) {
        return orderManageRpcService.getCommonPrintData(request);
    }

    @Override
    @RpcMapping("/retryPushOrderInfo")
    @RpcMethod("订单管理-重新推送订单")
    @ServiceLog(description = "重新推送订单")
    public RemoteResponse<Boolean> retryPushOrderInfo(OrderBasicParamDTO orderBasicParamDTO) {
        return orderManageRpcService.retryPushOrderByOrderNo(orderBasicParamDTO.getOrderNo());
    }

    @Override
    @RpcMapping("/retryPushOrderByAppId")
    @RpcMethod("订单管理-通过采购单id重新推送订单，只支持不拆单的单位，如广州医")
    @ServiceLog(description = "重新推送订单")
    public RemoteResponse<Integer> retryPushOrderByAppId(OrderBasicParamDTO orderBasicParamDTO) {
        return orderManageRpcService.retryPushOrderByAppId(orderBasicParamDTO.getAppId());
    }

    @Override
    @RpcMapping("/getOrderOMSConfig")
    @RpcMethod("订单管理-获取订单OMS相关配置")
    @ServiceLog(description = "获取订单OMS相关配置")
    public RemoteResponse<OrderOMSConfigResponseDTO> getOrderOMSConfig(OrderOMSConfigRequestDTO request) {
        Set<String> configCode = request.getConfigCode();
        Preconditions.notEmpty(configCode, "获取订单OMS相关配置失败！配置编码为空！");
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        String orgCode = null;
        try {
            orgCode = OrgEnum.getOrgEnumById(rjSessionInfo.getOrgId()).getCode();
        } catch (Exception e) {
            OrganizationDTO organizationDTO = userClient.getOrgById(rjSessionInfo.getOrgId());
            Preconditions.notNull(organizationDTO, "根据orgId获取机构信息为空！");
            orgCode = organizationDTO.getCode();
        }

        Map<String, String> configMapByOrgCodeAndConfigCode = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orgCode, new ArrayList<>(configCode));

        OrderOMSConfigResponseDTO result = new OrderOMSConfigResponseDTO();
        result.setReceiptStoreConfig(configMapByOrgCodeAndConfigCode.get(ConfigConstant.ORG_RECEIPT_STORE_CONFIG));
        result.setUseWarehouseSystem(configMapByOrgCodeAndConfigCode.get(ConfigConstant.USE_WAREHOUSE_SYSTEM));
        return RemoteResponse.<OrderOMSConfigResponseDTO>custom().setSuccess().setData(result);
    }

}
