package com.ruijing.store.order.rpc.client;

import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.file.UploadFileSimpleDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceRefResultDTO;
import com.reagent.research.statement.api.invoice.service.InvoiceApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.user.api.dto.invoicetitle.InvoiceTitleDTO;
import com.ruijing.store.user.api.service.InvoiceTitleRpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 发票rpc客户端
 */
@ServiceClient
public class InvoiceClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @MSharpReference(remoteAppkey = "research-statement-web")
    private InvoiceApi invoiceApi;

    @MSharpReference(remoteAppkey="store-user-service")
    private InvoiceTitleRpcService invoiceTitleRpcService;

    private static final String CAT_TYPE = "InvoiceClient";

    @ServiceLog(description = "根据业务类型id号删除发票接口", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void deleteInvoiceByOrderIds(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }

        List<Long> orderIdLongType = orderIdList.stream()
                .filter(Objects::nonNull)
                .map(Integer::longValue)
                .collect(Collectors.toList());
        RemoteResponse<Boolean> remoteResponse = invoiceApi.deleteBySourceIdsAndType(orderIdLongType, InvoiceTypeEnum.ORDER.getType());
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
    }

    /**
     * @description: 发票信息从rpc接口返回体改为本服务的对象，DDD
     * @date: 2021/4/6 17:21
     * @author: zengyanru
     * @param invoiceInfo
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO
     */
    private OrderInvoiceInfoVO invoiceInfoTransform(InvoiceDTO invoiceInfo) {
        OrderInvoiceInfoVO orderInvoiceInfo = new OrderInvoiceInfoVO();

        // TODO:需要确认开票日期是否为invoicedate
        orderInvoiceInfo.setAmount(invoiceInfo.getAmount())
                .setBankName(invoiceInfo.getBankName())
                .setBankNameCompany(invoiceInfo.getBankNameCompany())
                .setBankNo(invoiceInfo.getBankNum())
                .setDrawer(invoiceInfo.getDrawer())
                .setInvoiceId(invoiceInfo.getId())
                .setRemark(invoiceInfo.getRemark())
                .setTaxpayerNo(invoiceInfo.getTaxpayerNo())
                .setInvoiceItem(invoiceInfo.getInvoiceItem())
                .setBankCode(invoiceInfo.getBankCode())
                .setType(invoiceInfo.getType());

        List<InvoiceRefResultDTO> invoiceRefList = invoiceInfo.getInvoiceRefDTOS();
        Preconditions.notEmpty(invoiceRefList, "发票来源信息为空，来源信息未设值，发票信息为"+invoiceInfo);
        List<Integer> orderIdList = New.list();
        for (InvoiceRefResultDTO invoiceRef : invoiceRefList) {
            if (InvoiceTypeEnum.ORDER.getType().equals(invoiceRef.getType()) && invoiceRef.getSourceId() != null) {
                orderIdList.add(invoiceRef.getSourceId().intValue());
            }
        }
        orderInvoiceInfo.setOrderIdList(orderIdList);

        // 未在产品文档的默认记录发票号规则：存在“-”分割的，后面的是发票号，前面是发票code
        String invoiceNoWithCode = invoiceInfo.getInvoiceNo();
        if (StringUtils.isNotBlank(invoiceNoWithCode)) {
            String[] invoiceNoWithCodeList = invoiceNoWithCode.split("-");
            if (invoiceNoWithCodeList.length > 1) {
                orderInvoiceInfo.setInvoiceCode(invoiceNoWithCodeList[0]);
                orderInvoiceInfo.setInvoiceNo(invoiceNoWithCodeList[1]);
            } else {
                orderInvoiceInfo.setInvoiceNo(invoiceNoWithCode);
            }
        } else {
            orderInvoiceInfo.setInvoiceCode("");
            orderInvoiceInfo.setInvoiceNo("");
        }

        // 开票时间的特殊处理
        String dateString = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, invoiceInfo.getInvoiceDate());
        orderInvoiceInfo.setIssueDate(dateString).setIssueDateTimeStamp(invoiceInfo.getInvoiceDate().getTime());

        if (StringUtils.isNotBlank(invoiceInfo.getInvoicePhoto())) {
            List<String> invoiceUrlList = New.list(invoiceInfo.getInvoicePhoto().split(";|,"));
            invoiceUrlList = invoiceUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            orderInvoiceInfo.setPicturePathList(invoiceUrlList);
        }
        //发票附件信息
        List<UploadFileSimpleDTO> invoiceFileList = invoiceInfo.getInvoiceFileList();
        if (!CollectionUtils.isEmpty(invoiceFileList)) {
            List<OrderAttachmentVO> invoiceUrlList = invoiceFileList.stream()
                    .map(invoiceFileInfo ->{
                        OrderAttachmentVO orderAttachmentVO = new OrderAttachmentVO();
                        orderAttachmentVO.setUrl(invoiceFileInfo.getUrl());
                        orderAttachmentVO.setFileName(invoiceFileInfo.getFileName());
                        return orderAttachmentVO;
                    }).collect(Collectors.toList());
            orderInvoiceInfo.setInvoiceFileList(invoiceUrlList);
        }
        return orderInvoiceInfo;
    }

    /**
     * @description: 通过订单id，单位id，发票业务类型获取发票信息并转换
     * @date: 2021/3/17 14:52
     * @author: zengyanru
     * @param invoiceQueryDTO
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO>
     */
    @ServiceLog(description = "通过订单id，单位id，发票业务类型获取发票信息并转换", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderInvoiceInfoVO> findInvoiceVOList(InvoiceQueryDTO invoiceQueryDTO) {
        List<InvoiceDTO> invoiceList = this.findInvoiceList(invoiceQueryDTO);
        List<OrderInvoiceInfoVO> invoiceVOList = New.list();
        for (InvoiceDTO invoiceDTO : invoiceList) {
            OrderInvoiceInfoVO orderInvoiceInfoVO = this.invoiceInfoTransform(invoiceDTO);
            invoiceVOList.add(orderInvoiceInfoVO);
        }
        return invoiceVOList;
    }
    
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<InvoiceDTO> findInvoiceList(InvoiceQueryDTO invoiceQueryDTO) {
        Preconditions.notNull(invoiceQueryDTO, "入参为空");
        RemoteResponse<List<InvoiceDTO>> response = invoiceApi.findInvoiceList(invoiceQueryDTO);
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "findInvoiceList", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(invoiceQueryDTO) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    /**
     * 查订单相关发票列表（支持新旧）(注意，这个有时候会搜不出来会失效，请用findInvoiceList)
     * @param orderNo
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    @Deprecated
    public List<InvoiceDTO> findInvoiceListByOrderNo(String orderNo) {
        Preconditions.hasText(orderNo, "入参为空");
        RemoteResponse<List<InvoiceDTO>> response = invoiceApi.findInvoiceListByOrderNo(orderNo);
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "findInvoiceListByOrderNo", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orderNo + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    /**
     * @description: 通过发票抬头id列表找发票抬头
     * @date: 2021/4/12 15:04
     * @author: zengyanru
     * @param invoiceTitleIdList
     * @return java.util.List<com.ruijing.store.user.api.dto.invoicetitle.InvoiceTitleDTO>
     */
    @ServiceLog(description = "通过发票抬头id列表找发票抬头", serviceType = ServiceType.COMMON_SERVICE)
    public List<InvoiceTitleDTO> findInvoiceTitleByIdList(List<Integer> invoiceTitleIdList) {
        Preconditions.notEmpty(invoiceTitleIdList, "通过发票抬头id列表找发票抬头入参不可为空");
        RemoteResponse<List<InvoiceTitleDTO>> response = invoiceTitleRpcService.getByIds(invoiceTitleIdList);
        Preconditions.isTrue(response != null && response.isSuccess(), "通过发票抬头id列表找发票抬头rpc方法异常,入参："+invoiceTitleIdList);
        return response.getData();
    }

    /**
     * 通过订单id列表找发票信息
     * @param orderIdList   订单id
     * @param orgId         机构id
     * @return              发票集合
     */
    public List<InvoiceDTO> findInvoiceList(List<Integer> orderIdList, Integer orgId) {
        Preconditions.notEmpty(orderIdList, "orderIdList为空!");
        Preconditions.notNull(orgId, "orgId为空!");

        List<Long> sourceIdList = orderIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        InvoiceQueryDTO request = new InvoiceQueryDTO();
        request.setInvoiceType(InvoiceTypeEnum.ORDER);
        request.setSourceIds(sourceIdList);
        request.setOrgId(orgId.longValue());
        return this.findInvoiceList(request);
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT, description = "保存发票")
    public void saveInvoice(List<InvoiceDTO> invoiceDTOList){
        RemoteResponse<Boolean> response = invoiceApi.saveInvoiceList(invoiceDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
