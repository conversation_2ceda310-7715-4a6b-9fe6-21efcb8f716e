package com.ruijing.store.order.gateway.print.warehouse;

import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseClaimPrintDataDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;

import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2024-09-27 17:29
 * @description:
 */
public interface WarehouseClaimPrintDataService {

    /**
     * 获取入库单打印数据
     * @param orderMasterDO 订单主表数据
     * @param orderDetailDOList 订单详情表数据
     * @param warehouseApplicationInfoList 入库单数据
     * @return 入库单打印数据
     */
    List<WarehouseClaimPrintDataDTO> getClaimPrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList);
}
