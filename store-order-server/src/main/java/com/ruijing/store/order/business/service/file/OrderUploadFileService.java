package com.ruijing.store.order.business.service.file;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.order.api.file.request.OrderDeleteUploadFileDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OverrideUploadAttachmentRequest;
import com.ruijing.store.order.gateway.file.request.OrderDetailAcceptFileRequest;
import com.ruijing.store.order.gateway.file.request.OrderFileInfoRequestDTO;
import com.ruijing.store.order.gateway.file.request.OrderFileUploadRequestDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:51
 * @description
 */
public interface OrderUploadFileService {


    /**
     * 获取已上传的文件
     * @param request 获取用参数
     * @return 文件信息 
     */
    List<UploadFileInfoVO> getUploadFileInfoList(OrderFileInfoRequestDTO request);

    /**
     * 覆盖上传文件（覆盖）
     * @param request 上传文件的参数
     * @return 是否成功
     */
    Boolean saveUploadFileInfo(OrderUploadFileDataRequestDTO request);

    /**
     * 增量上传文件
     *
     * @param request 上传文件的参数
     * @return 是否成功
     */
    Boolean insertUploadFiles(OrderFileUploadRequestDTO request, String orderNo);

    /**
     * 追加验收附件
     *
     * @param request
     * @return
     */
    Boolean additionalAcceptanceAttachments(OrderFileUploadRequestDTO request);


    /**
     * 校验上传文件
     *
     * @param orderFileUploadRequestDTO
     * @param orderMasterDO
     * @return
     */
    Boolean checkUploadFile(OrderFileUploadRequestDTO orderFileUploadRequestDTO, OrderMasterDO orderMasterDO);

    /**
     * 追加保存订单详情附件
     */
    void appendSaveDetailAttachment(RjSessionInfo rjSessionInfo,OrderDetailAcceptFileRequest request);

    /**
     * 根据文件ID删除上传的文件
     */
    Boolean deleteUploadFile(OrderDeleteUploadFileDTO orderDeleteUploadFileDTO);

    /**
     * 验收审批-覆盖上传验收附件
     */
    void acceptApprovalOverrideAttachment(RjSessionInfo rjSessionInfo, OverrideUploadAttachmentRequest request);
}
