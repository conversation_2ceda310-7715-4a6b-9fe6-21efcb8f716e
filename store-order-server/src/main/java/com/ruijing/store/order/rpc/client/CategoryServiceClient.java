package com.ruijing.store.order.rpc.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.category.api.constant.CategoryConstant;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.category.api.dto.CategoryQueryDTO;
import com.ruijing.shop.category.api.service.CategoryService;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 后台商品分类远程客户端
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2019/12/04 17:03
 **/
@Service
public class CategoryServiceClient {

    private static final String CAT_TYPE = "CategoryServiceClient";

    private final int cacheKEY=1;

    private Cache<Integer, Integer> cache = CacheBuilder.newBuilder().maximumSize(1).expireAfterWrite(1, TimeUnit.HOURS).build();

    private Map<Long, CategoryDTO> cacheMap;

    private ReentrantLock lock=new ReentrantLock(true);

    @MSharpReference(remoteAppkey = "shop-category-service")
    private CategoryService categoryService;


    /**
     * 一级分类
     */
    private static final int FIRST_LEVEL = 1;

    /**
     * 二级分类
     */
    private static final int SECOND_LEVEL = 2;



    /**
     * 根据分类ID 获取其一级分类信息
     *
     * @param idList
     * @return key:要查询的分类ID，value: 一级分类
     */
    public Map<Long, CategoryDTO> getTopCategoryBySubId(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<Long, CategoryDTO> categoryDTOMap = this.queryAllMapByCache();
        if (MapUtils.isEmpty(categoryDTOMap)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<Long, CategoryDTO> map = new HashMap<>(idList.size());
        for (Long id: idList){
            CategoryDTO dto = categoryDTOMap.get(id);
            String path = dto.getPath();
            if (dto.getLevel() == FIRST_LEVEL)
            {
                map.put(id, dto);
            }
            else if (dto.getLevel() == SECOND_LEVEL)
            {
                map.put(id, categoryDTOMap.get(Long.valueOf(path)));
            }
            else if (dto.getLevel() == 3)
            {
                map.put(id, categoryDTOMap.get(Long.valueOf(path.substring(0, path.indexOf(".")))));
            }
        }
        return map;
    }

    /**
     * rpc 底层查询
     * @param id id
     * @return CategoryDTO
     */
    public CategoryDTO load(final Long id) {
        CategoryDTO categoryDTO = null;
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "load");
        try {
            final RemoteResponse<CategoryDTO> response = categoryService.load(id);
            if (null != response && response.isSuccess()) {
                categoryDTO = response.getData();
            }
            transaction.setSuccessStatus();
        } catch (Exception ex) {
            transaction.addData(JsonUtils.toJson(id));
            transaction.setStatus(ex);
        } finally {
            transaction.complete();
        }
        return categoryDTO;
    }

    /**
     * rpc 底层查询
     * @param queryDTO
     * @return list
     */
    public List<CategoryDTO> queryForList(final CategoryQueryDTO queryDTO) {
        List<CategoryDTO> categoryDTOList = Collections.emptyList();
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryForList");
        try {
            final RemoteResponse<List<CategoryDTO>> response = categoryService.queryForList(queryDTO);
            if (null != response && response.isSuccess()) {
                categoryDTOList = response.getData();
            }
            transaction.setSuccessStatus();
        } catch (Exception ex) {
            transaction.addData(JsonUtils.toJson(queryDTO));
            transaction.setStatus(ex);
        } finally {
            transaction.complete();
        }
        return categoryDTOList;
    }

    /**
     * 全量查询转换
     * @return list
     */
    public List<CategoryDTO> queryAllByCache() {
        Map<Long, CategoryDTO> map=this.queryAllMapByCache();
        if(map==null){
            return Collections.emptyList();
        }
        return new ArrayList<>(map.values());
    }

    /**
     * 全量缓存查询设计层
     * @return map
     */
    public Map<Long, CategoryDTO> queryAllMapByCache() {
        Integer value= cache.getIfPresent(cacheKEY);
        if (value!=null) {
            return cacheMap;
        }
        boolean isLock=lock.tryLock();
        if(!isLock){
            return cacheMap;
        }
        try {
            List<CategoryDTO> list=this.queryListAllAction();
            if(CollectionUtils.isNotEmpty(list)){
                Map<Long, CategoryDTO> map=new HashMap<>();
                for(CategoryDTO c:list){
                    map.put(c.getId(), c);
                }
                cache.put(cacheKEY, cacheKEY);
                cacheMap=map;
            }
        }catch (Exception e){
            Cat.logWarn(CAT_TYPE, "queryAll", e);
        }finally {
            lock.unlock();
        }
        return cacheMap;
    }

    /**
     * rpc 有效的全量查询
     * @return list
     */
    private List<CategoryDTO> queryListAllAction(){
        List<CategoryDTO> categoryDTOList=null;
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryAll");
        try {
            final CategoryQueryDTO queryDTO = new CategoryQueryDTO();
            queryDTO.setStatus(2);
            final RemoteResponse<List<CategoryDTO>> response = categoryService.queryForList(queryDTO);
            if (null != response && response.isSuccess()) {
                categoryDTOList = response.getData();
            }
            transaction.setSuccessStatus();
        } catch (Exception ex) {
            transaction.setStatus(ex);
        } finally {
            transaction.complete();
        }
        return categoryDTOList;
    }

    /**
     * 业务方法
     * @param categoryIdList
     * @return list
     */
    public List<CategoryDTO> queryListByCache(final List<Long> categoryIdList) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryListByCache");
        List<CategoryDTO> categoryDTOList = New.list();
        try {
            List<CategoryDTO> list = queryAllByCache();
            if (CollectionUtils.isEmpty(categoryIdList)) {
                return list;
            }
            if (CollectionUtils.isEmpty(list)) {
                return categoryDTOList;
            }
            for (int i = 0, size = list.size(); i < size; i++) {
                CategoryDTO categoryDTO = list.get(i);
                if (null != categoryDTO && categoryIdList.contains(categoryDTO.getId())) {
                   categoryDTOList.add(categoryDTO);
                }
            }
            transaction.setSuccessStatus();
        } catch (Exception ex) {
            transaction.addData(JSONArray.fromObject(categoryIdList).toString());
            transaction.setStatus(ex);
        } finally {
            transaction.complete();
        }
        return categoryDTOList;
    }


    /**
     *根据分类id集合查询分类信息
     * @param categoryIdList
     * @return
     */
    @ServiceLog(description = "根据分类id集合查询分类信息")
    public List<CategoryDTO> batchLoad(List<Integer> categoryIdList) {
        List<Long> idList = categoryIdList.stream().map(Integer::longValue).collect(Collectors.toList());
        List<List<Long>> partitionList = Lists.partition(idList, 200);

        List<CategoryDTO> categoryDTOList = new ArrayList<>(idList.size());
        for (List<Long> categoryIds : partitionList) {
            RemoteResponse<List<CategoryDTO>> remoteResponse = categoryService.batchLoad(New.list(categoryIds));
            Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
            categoryDTOList.addAll(remoteResponse.getData());
        }

        return categoryDTOList;
    }
    
    /**
     * 根据id集合批量获取分类详细信息 , 只查自己
     * @param ids 分类id集合
     */
    @ServiceLog(description = "根据id集合批量获取分类详细信息,只查自己", serviceType = ServiceType.RPC_CLIENT)
    public List<CategoryDTO> batchLoadSelf(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return New.emptyList();
        }
        List<List<Long>> partitionList = Lists.partition(ids, 200);
        List<CategoryDTO> categoryDTOList = New.listWithCapacity(ids.size());

        for (List<Long> subIds : partitionList) {
            RemoteResponse<List<CategoryDTO>> response = categoryService.batchLoadSelf(New.list(subIds));
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (CollectionUtils.isNotEmpty(response.getData())) {
                categoryDTOList.addAll(response.getData());
            }
        }
        return categoryDTOList;
    }



    public List<CategoryDTO> getAllCategoryByIds(List<Integer> categoryIds) {
        CategoryQueryDTO categoryQueryDTO = new CategoryQueryDTO();
        categoryQueryDTO.setCategoryIdList(categoryIds.stream().map(Integer::longValue).collect(Collectors.toList()));
        categoryQueryDTO.setQueryTag(CategoryConstant.QUERY_TAG3);
        //是否包含传入分类id,0否，1是
        categoryQueryDTO.setContainCategoryId(1);
        List<CategoryDTO> categoryListForAllProduct = this.queryForList(categoryQueryDTO);
        return categoryListForAllProduct;
    }
}
