package com.ruijing.store.order.base.timeoutstatistics.service;

import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.timeoutstatistics.service.impl.TimeoutQueryServiceImpl;
import com.ruijing.store.order.gateway.buyercenter.request.timeout.TimeOutNoticeRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OverTimeSettingVO;
import com.ruijing.store.order.gateway.buyercenter.vo.TimeOutTipsVO;

import java.util.List;
import java.util.Map;

/**
 * @author: liwenyu
 * @createTime: 2023-03-10 10:09
 * @description:
 **/
public interface TimeoutQueryService {

    /**
     * 获取部门超时状况
     * @param orgId 单位id
     * @param deptId 部门id
     * @return 超时状况
     */
    List<TimeOutTipsVO> checkDeptTimeoutStat(Integer orgId, Integer deptId);

    /**
     * 获取订单验收超时状态
     * @param orgId 单位id
     * @param userId 用户id
     * @return 超时状况
     */
    List<TimeOutTipsVO> checkOrderAcceptanceStat(Integer orgId, Integer userId);

    /**
     * 获取订单结算超时状态
     * @param orgId 单位id
     * @param userId 用户id
     * @return 超时状况
     */
    List<TimeOutTipsVO> checkOrderBalanceStat(Integer orgId, Integer userId);

    /**
     * 超时订单查询（HMS列表展示）
     * @param params 参数
     * @return 超时订单列表
     */
    BasePageResponseDTO<OrderMasterTimeOutDTO> findTimeOutOrders(TimeOutOrderParamsDTO params);

    /**
     * 超时订单查询
     * @param params 超时查询参数
     * @param balanceDaysInt 结算超时时间配置
     * @param examineDaysInt 验收超时时间配置
     * @return 超时订单列表
     */
    SearchPageResultDTO<OrderMasterSearchDTO> queryTimeOutOrder(TimeOutOrderParamsDTO params, int balanceDaysInt, int examineDaysInt);


    /**
     * 超时订单查询 按部门分组
     *
     * @param params
     * @param balanceDaysInt
     * @param examineDaysInt
     * @return
     */
    Map<Integer, Integer> queryTimeOutOrderCountByDepartmentId(TimeOutOrderParamsDTO params, int balanceDaysInt, int examineDaysInt);

    /**
     * 验收/结算超时查询
     *
     * @param params             请求参数
     * @param balanceDaysInt     结算超时时间
     * @param useWareHouseSystem 使用库房系统
     * @return 超时数据
     */
    SearchPageResultDTO<OrderMasterSearchDTO> queryTimeOutAccOrWhOrder(TimeOutOrderParamsDTO params, int balanceDaysInt, boolean useWareHouseSystem);

    /**
     * 通过邮件通知用户
     * @param timeOutNoticeRequestDTO 超时提醒参数
     */
    void noticeUserByEmail(TimeOutNoticeRequestDTO timeOutNoticeRequestDTO);


    /**
     * 获取单位超时旧设置
     *
     * @param orgId 单位id
     * @return 超时设置
     */
    OverTimeSettingVO getOldOverTimeSetting(Integer orgId);

    /**
     * 获取个人超时状况
     * @param orgId 单位id
     * @param deptId 部门id
     * @return 超时状况
     */
    List<TimeOutTipsVO> checkPersonalTimeoutStat(Integer orgId, Integer userId);


     Map<Integer, Integer> handleAcceptance(Boolean isPersonalLimitOn,
                                                  Boolean isDeptLimitOn,
                                                  Integer orgId,
                                                  Integer userId,
                                                  Integer examineLimitDay,
                                                  Integer deptExamineLimitAmount,
                                                  Integer purchaserExamineLimitAmount,
                                                  List<Integer> deptIdList,
                                                  List<TimeOutTipsVO> timeOutTipsVOList,
                                                  ValidationScopeEnum validationScopeEnum,
                                                  Boolean isPurchaserPriority);

     Map<Integer, Integer> handleBalance(Boolean isPersonalLimitOn,
                                               Boolean isDeptLimitOn,
                                               Integer orgId,
                                               Integer userId,
                                               Integer balanceLimitDay,
                                               Integer deptBalanceLimitAmount,
                                               Integer purchaserBalanceLimitAmount,
                                               List<Integer> deptIdList,
                                               List<TimeOutTipsVO> timeOutTipsVOList,
                                               ValidationScopeEnum validationScopeEnum,
                                               Boolean isPurchaserPriority);


     enum ValidationScopeEnum{

        ALL( "全校验"),
        ONLY_CHECK_DEPT("只校验课题组"),
        ONLY_CHECK_PERSON("只校验采购人");

        private String description;

        public String getDescription() {
            return description;
        }

        ValidationScopeEnum(String description) {
            this.description = description;
        }
    }

}
