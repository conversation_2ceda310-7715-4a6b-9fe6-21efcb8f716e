<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.GoodsReturnImageDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.GoodsReturnImageDO">
    <!--@mbg.generated-->
    <!--@Table t_goods_return_image-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="return_id" jdbcType="INTEGER" property="returnId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, creation_time, update_time, image_url, return_id
  </sql>


<!--auto generated by MybatisCodeHelper on 2021-03-02-->
  <select id="findByReturnIdBetween" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_goods_return_image
    where return_id <![CDATA[>]]> #{minReturnId,jdbcType=INTEGER} and return_id <![CDATA[<]]>
    #{maxReturnId,jdbcType=INTEGER}
  </select>
</mapper>