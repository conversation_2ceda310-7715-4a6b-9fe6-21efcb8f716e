package com.ruijing.store.order.business.enums;

/**
 * <AUTHOR>
 * @Date 2021/1/12 17:10
 * @Description
 **/
public enum OrderOperationTypeEnum {
    /**
     * 生成订单
     */
    CREATE_ORDER(1, "生成订单"),

    /**
     * 供应商确认订单
     */
    SUPP_VERIFY_ORDER(2, "供应商确认订单"),

    /**
     * 供应商发货
     */
    SUPP_SHIPMENTS(3, "供应商发货"),

    /**
     * 确认验收
     */
    VERIFY_ACCEPTATION(4, "确认验收"),

    /**
     * 自动入库
     */
    AUTO_INBOUND(5, "自动入库"),

    /**
     * 提交入库
     */
    SUBMIT_INBOUND(6, "提交入库"),

    /**
     * 审批入库
     */
    INBOUND_SUCCESS(7, "审批入库"),

    /**
     * 一级入库审批
     */
    FIRST_INBOUND_SUCCESS(8, "一级入库审批"),

    /**
     * 二级入库审批
     */
    SECOND_INBOUND_SUCCESS(9, "二级入库审批"),

    /**
     * 发起结算
     */
    START_STATEMENT(10, "发起结算"),

    /**
     * 完成结算
     */
    FINISH_STATEMENT(11, "完成结算"),

    /**
     * 退货
     */
    GOODS_RETURN(12,"申请退货"),

    /**
     * 撤销入库
     */
    CANCEL_RECEIPT(13, "撤销入库");

    public final Integer value;
    public final String desc;

    OrderOperationTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
