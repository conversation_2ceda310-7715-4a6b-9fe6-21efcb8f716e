package com.ruijing.store.order.base.docking.service;

import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.base.docking.model.DockingExtra;

import java.util.List;

/**
 * @description: 订单对接服务
 * @author: zhuk
 * @create: 2019-09-25 10:01
 **/

public interface DockingExtraService {
    /**
     * 插入 dockingExtra
     * @param dockingExtraDTO 入参
     */
    void insertDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 查询DockingExtra
     * @param dockingExtraDTO 入参
     * @return  结果
     */
    List<DockingExtraDTO> findDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 更新单据对接记录
     * @param dockingExtraDTO
     * @return  是否更新成功
     */
    Boolean updateDockingExtra(DockingExtraDTO dockingExtraDTO);

    /**
     * 更新单据对接记录
     * @param extraInfo 对接单号
     * @param info      我方单号
     */
    void updateExtraInfoByInfo(String extraInfo, String info);

    /**
     * 批量插入
     * @param dockingExtraDTOList
     * @return
     */
    Integer insertList(List<DockingExtraDTO> dockingExtraDTOList);

    /**
     * 查询DockingExtra
     * @param dockingExtraDTO 入参
     * @return  结果
     */
    List<DockingExtraDTO> findDockingExtraByInfo(List<String> dockingExtraDTO);

    /**
     * 通过extraInfo，type批量查询对接单信息
     * @param extraInfoList
     * @param type
     * @return
     */
    List<DockingExtraDTO> findDockingByExtraInfoAndType(List<String> extraInfoList, Integer type);

    /**
     * 保存&更新对接记录
     * @param orderNo
     * @param extraNo
     * @param processResult
     * @param message
     * @return
     */
    Integer saveOrUpdateDockingExtra(String orderNo, String extraNo, Boolean processResult, String message);

    /**
     * 保存&更新对接记录
     * @param dockingExtra 对接记录
     * @return
     */
    Integer saveOrUpdateDockingExtra(DockingExtra dockingExtra);

    /**
     * 校验推送状态
     * @param info
     */
    void customValidationDockingStatus(String info);
}
