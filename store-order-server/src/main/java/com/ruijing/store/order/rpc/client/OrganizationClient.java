package com.ruijing.store.order.rpc.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.base.biz.organization.api.org.enums.OrgBusinessTypeEnum;
import com.ruijing.base.biz.organization.api.org.service.RefOrgBusinessRpcService;
import com.ruijing.base.biz.organization.api.org.service.dto.RefOrgBusinessRpcDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.service.OrganizationRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/12/22 11:18
 */
@ServiceClient
public class OrganizationClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationClient.class);

    private static final String CAT_TYPE = "OrganizationClient";

    private Cache<Integer, OrganizationDTO> orgCache = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(1, TimeUnit.HOURS).build();

    @MSharpReference(remoteAppkey = "store-user-service")
    private OrganizationRpcService organizationRpcService;

    @MSharpReference(remoteAppkey = "base-biz-organization-service")
    private RefOrgBusinessRpcService refOrgBusinessRpcService;

    public SimpleOrgDTO findSimpleOrgDTOById(Integer id) {
        try {
            OrgEnum org = OrgEnum.getOrgEnumById(id);
            SimpleOrgDTO result = new SimpleOrgDTO();
            result.setName(org.getName());
            result.setCode(org.getCode());
            result.setId(org.getValue());
            return result;
        } catch (Exception e) {
            OrganizationDTO organizationDTO = findByIdWithCache(id);
            return this.organizationDTO2SimpleOrgDTO(organizationDTO);
        }
    }

    public OrganizationDTO findByIdWithCache(Integer id) {
        if (id == null) {
            return null;
        }
        String handlerName = "findByIdWithCache";
        OrganizationDTO result = orgCache.getIfPresent(id);
        if (result != null) {
            return result;
        }
        RemoteResponse<List<OrganizationDTO>> response;
        try {
            response = organizationRpcService.getByIds(Collections.singletonList(id));
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, handlerName, "rpc调用异常，入参：" + id + "\n", e);
            return null;
        }
        if (response != null && response.isSuccess()) {
            List<OrganizationDTO> resultList = response.getData();
            if (CollectionUtils.isEmpty(resultList)) {
                LOGGER.error("根据Id:{}查找不到机构", id);
                return null;
            }
            orgCache.put(id, resultList.get(0));
            return resultList.get(0);
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc返回失败结果：" + JsonUtils.toJson(response) + "\n", new RuntimeException("rpc返回失败"));
        return null;
    }

    private SimpleOrgDTO organizationDTO2SimpleOrgDTO(OrganizationDTO from) {
        if (from == null) {
            return null;
        }
        SimpleOrgDTO to = new SimpleOrgDTO();
        to.setId(from.getId());
        to.setCode(from.getCode());
        to.setName(from.getName());
        return to;
    }

    @ServiceLog(description = "通过orgIdList批量查询机构", serviceType = ServiceType.RPC_CLIENT)
    public List<OrganizationDTO> findByIdList(List<Integer> orgIdList) {
        Preconditions.notEmpty(orgIdList, "orgIdList must not be empty");
        List<List<Integer>> partition = Lists.partition(orgIdList, 100);
        List<OrganizationDTO> result = new ArrayList<>(orgIdList.size());
        for (List<Integer> listItem : partition) {
            RemoteResponse<List<OrganizationDTO>> response = organizationRpcService.getByIds(New.list(listItem));
            Preconditions.isTrue(response.isSuccess(), "通过orgIdList批量查询机构异常：" + JsonUtils.toJsonIgnoreNull(response));
            if (CollectionUtils.isEmpty(response.getData())) {
                continue;
            }
            result.addAll(response.getData());
        }
        return result;
    }

    @ServiceLog(description = "通过guid批量查询当前用户负责的运营单位", serviceType = ServiceType.RPC_CLIENT)
    public List<RefOrgBusinessRpcDTO> listByBusinessTypeAndOperators(List<String> guidList) {
        if(CollectionUtils.isEmpty(guidList)) {
            return New.list();
        }
        RemoteResponse<List<RefOrgBusinessRpcDTO>> response = refOrgBusinessRpcService.listByBusinessTypeAndOperators(New.list(OrgBusinessTypeEnum.RESEARCH_PURCHASE_PLATFORM.getVal(),
                OrgBusinessTypeEnum.MEDICAL_PURCHASE_PLATFORM.getVal(), OrgBusinessTypeEnum.MATERIAL_PURCHASE_PLATFORM.getVal()), guidList);
        BusinessErrUtil.isTrue(response.isSuccess(), "通过guid批量查询当前用户负责的运营单位异常：" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    public static class SimpleOrgDTO {
        /**
         * id
         */
        private Integer id;

        /**
         * 机构名称
         */
        private String name;

        /**
         * 组织编码
         */
        private String code;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("SimpleOrgDTO{");
            sb.append("id=").append(id);
            sb.append(", name='").append(name).append('\'');
            sb.append(", code='").append(code).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }
}
