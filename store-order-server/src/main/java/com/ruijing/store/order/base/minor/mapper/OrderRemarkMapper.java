package com.ruijing.store.order.base.minor.mapper;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderRemarkMapper {
    int deleteByPrimaryKey(@Param("ftbuyappid") Integer ftbuyappid, @Param("fsuppid") Integer fsuppid);

    int insert(OrderRemark record);

    int insertSelective(OrderRemark record);

    /**
     * 通过联合主键查询记录
     * @param ftbuyappid
     * @param fsuppid
     * @return
     */
    OrderRemark selectByPrimaryKey(@Param("ftbuyappid") Integer ftbuyappid, @Param("fsuppid") Integer fsuppid);

    /**
     * 通过主键有选择性的更新更新记录
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderRemark record);

    /**
     * 通过主键更新记录
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderRemark record);

    /**
     * 通过联合主键ftbuyappid 和 fsuppid 批量查询
     * @param orderRemarks
     * @return
     */
    List<OrderRemark> findAllByPrimaryKey(@Param("list") List<OrderRemark> orderRemarks);

    List<OrderRemark> selectByFtbuyappidIn(@Param("ftbuyappidCollection")Collection<Integer> ftbuyappidCollection);

    List<OrderRemark> selectByFtbuyappidAndFsuppid(@Param("ftbuyappid")Integer ftbuyappid,@Param("fsuppid")Integer fsuppid);


}