package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/20 16:11
 * @Description
 **/
@RpcModel("订单管理-验收时返回的控制信息")
public class OrderAcceptCheckVO implements Serializable {

    private static final long serialVersionUID = -1626482037838147603L;

    /**
     * 退货单号列表
     */
    @RpcModelProperty("退货单号列表")
    private List<String> returnNoList;

    /**
     * 退货单id列表
     */
    @RpcModelProperty("退货单id列表")
    private List<Integer> returnIdList;

    public List<String> getReturnNoList() {
        return returnNoList;
    }

    public void setReturnNoList(List<String> returnNoList) {
        this.returnNoList = returnNoList;
    }

    public List<Integer> getReturnIdList() {
        return returnIdList;
    }

    public void setReturnIdList(List<Integer> returnIdList) {
        this.returnIdList = returnIdList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderAcceptCheckVO{");
        sb.append("returnNoList=").append(returnNoList);
        sb.append(", returnIdList=").append(returnIdList);
        sb.append('}');
        return sb.toString();
    }
}
