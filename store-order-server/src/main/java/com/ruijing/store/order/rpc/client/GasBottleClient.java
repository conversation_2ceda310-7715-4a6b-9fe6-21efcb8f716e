package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.RpcCallUtils;
import com.ruijing.shop.srm.api.constant.GasBottleUsageConstant;
import com.ruijing.shop.srm.api.dto.shopgasbottle.BindShopGasBottleReqDTO;
import com.ruijing.shop.srm.api.dto.shopgasbottle.GasDTO;
import com.ruijing.shop.srm.api.dto.shopgasbottle.ShopGasBottleDTO;
import com.ruijing.shop.srm.api.service.ShopGasBottleRpcService;
import com.ruijing.shop.srm.api.support.enums.ShopGasBottleUnbindEnum;
import com.ruijing.shop.wms.api.service.WmsGasBottleRPCService;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasVO;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Liwenyu
 * @create: 2024-04-24 17:36
 * @description: 气瓶服务客户端
 */
@ServiceClient
public class GasBottleClient {

    @MSharpReference(remoteAppkey = "shop-srm-service")
    private ShopGasBottleRpcService shopGasBottleRpcService;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private OrderDetailService orderDetailService;

    @MSharpReference(remoteAppkey = "shop-wms-service")
    private WmsGasBottleRPCService wmsGasBottleRpcService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<GasBottleVO> getGasBottleVOByQrCodes(List<String> gasBottleQrCodes){
        if(CollectionUtils.isEmpty(gasBottleQrCodes)){
            return New.emptyList();
        }
        List<ShopGasBottleDTO> shopGasBottleDTOList = RpcCallUtils.partitionExec(gasBottleQrCodes, 200, (part)->shopGasBottleRpcService.getShopGasBottleByQrCodes(part));
        if(CollectionUtils.isEmpty(shopGasBottleDTOList)){
            return New.emptyList();
        }
        return shopGasBottleDTOList.stream().map(this::shopGasBottleDTO2VO).collect(Collectors.toList());
    }

    /**
     * 取消订单成功，解绑气瓶
     *
     * @param orderId 订单id
     * @param orderNo 订单号
     * @param orgId 单位id
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE, description = "解绑气瓶(整单）")
    public void unbindGasBottle(Integer orderId, String orderNo, Integer orgId, ShopGasBottleUnbindEnum scene) {
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        boolean eachProductEachCode = CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTOList.get(0).getExtraValue());
        List<String> bindGasBottleBarcodes;
        if (eachProductEachCode) {
            // 一物一码，获取一物纬度绑定气瓶
            List<OrderUniqueBarCodeDTO> uniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderNo, null);
            bindGasBottleBarcodes = uniqueBarCodeDTOList.stream().filter(item -> OrderProductTransactionStatusEnum.RETURNED.getCode() != item.getTransactionStatus())
                    .map(OrderUniqueBarCodeDTO::getGasBottleBarcode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        } else {
            // 否则获取商品纬度绑定气瓶
            OrderDetailReq orderDetailReq = new OrderDetailReq();
            orderDetailReq.setOrderMasterIdList(New.list(orderId));
            orderDetailReq.setReturnBindGasBottle(true);
            orderDetailReq.setExcludeReturnStatusList(New.list(GoodsReturnStatusEnum.SUCCESS.getCode()));
            RemoteResponse<List<OrderDetailDTO>> response = orderDetailService.findDetailByOrderIdListExcludeReturnStatus(orderDetailReq);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            List<OrderDetailDTO> orderDetailDTOList = response.getData();
            bindGasBottleBarcodes = orderDetailDTOList.stream().map(OrderDetailDTO::getBindGasBottleBarcodes).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
        }
        this.unbindShopGasBottleByQrCode(orderNo, orgId, bindGasBottleBarcodes, scene);
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE, description = "解绑气瓶")
    public void unbindShopGasBottleByQrCode(String businessNo, Integer orgId, List<String> gasBottleQrCodes, ShopGasBottleUnbindEnum scene){
        if(CollectionUtils.isEmpty(gasBottleQrCodes)){
            return;
        }
        List<BindShopGasBottleReqDTO> reqDataList = gasBottleQrCodes.stream().map(qrcode->{
            BindShopGasBottleReqDTO reqDTO = new BindShopGasBottleReqDTO();
            reqDTO.setOrderNo(businessNo);
            reqDTO.setGasBottleQrCode(qrcode);
            return reqDTO;
        }).collect(Collectors.toList());
        RemoteResponse<?> response = shopGasBottleRpcService.unbindShopGasBottleByQrCode(reqDataList, scene, DockingConstant.SYSTEM_OPERATOR_NAME);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if(scene == ShopGasBottleUnbindEnum.RETURN_GOODS){
            wmsGasBottleRpcService.updateUsageStatus(gasBottleQrCodes, orgId, GasBottleUsageConstant.ALREADY_RECYCLE);
        }
    }

    private GasBottleVO shopGasBottleDTO2VO(ShopGasBottleDTO shopGasBottleDTO){
        GasBottleVO vo = new GasBottleVO();
        vo.setSuppId(shopGasBottleDTO.getSuppId());
        vo.setGasBottleCode(shopGasBottleDTO.getGasBottleCode());
        vo.setGasBottleType(shopGasBottleDTO.getGasBottleType());
        vo.setGasId(shopGasBottleDTO.getGasId());
        vo.setBrand(shopGasBottleDTO.getBrand());
        vo.setMaterial(shopGasBottleDTO.getMaterial());
        vo.setManufactureDate(shopGasBottleDTO.getManufactureDate());
        vo.setLastMaintenanceDate(shopGasBottleDTO.getLastMaintenanceDate());
        vo.setNextMaintenanceDate(shopGasBottleDTO.getNextMaintenanceDate());
        vo.setScrapDate(shopGasBottleDTO.getScrapDate());
        vo.setGasBottleCapacity(shopGasBottleDTO.getGasBottleCapacity());
        vo.setGasBottleCapacityUnit(shopGasBottleDTO.getGasBottleCapacityUnit());
        vo.setExpirationStatus(shopGasBottleDTO.getExpirationStatus());
        vo.setUsageStatus(shopGasBottleDTO.getUsageStatus());
        vo.setQrCode(shopGasBottleDTO.getQrCode());
        vo.setGasDTO(this.gasDTO2VO(shopGasBottleDTO.getGasDTO()));
        vo.setGasBottleId(shopGasBottleDTO.getGasId());
        return vo;
    }

    private GasVO gasDTO2VO(GasDTO gasDTO){
        if(gasDTO == null){
            return null;
        }
        return new GasVO()
                .setName(gasDTO.getName())
                .setBodyColor(gasDTO.getBodyColor())
                .setTypeface(gasDTO.getTypeface())
                .setLetterColor(gasDTO.getLetterColor())
                .setColourRing(gasDTO.getColourRing())
                .setTopHeadColor(gasDTO.getTopHeadColor())
                .setBottomHeadColor(gasDTO.getBottomHeadColor());
    }
}
