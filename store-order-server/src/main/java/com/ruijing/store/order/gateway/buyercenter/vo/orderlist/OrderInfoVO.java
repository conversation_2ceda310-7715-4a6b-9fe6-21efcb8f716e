package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.reagent.order.enums.OrderEventStatusEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;
import com.ruijing.store.apply.enums.OfflineAccountTypeEnum;
import com.ruijing.store.order.api.base.enums.OrderAcceptanceWayEnum;
import com.ruijing.store.order.api.base.enums.OrderButtonTypeEnum;
import com.ruijing.store.order.api.base.enums.OrderConfirmEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptAttachmentDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptPictureDTO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderUploadStatusEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ContractInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnOrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderLogisticsInfoVO;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/11/17 15:25
 * @Description
 **/
@RpcModel(value = "订单管理-我的订单列表订单详细信息")
public class OrderInfoVO implements Serializable {

    private static final long serialVersionUID = -6799708428725543550L;

    /**
     * 订单主表信息
     */
    @RpcModelProperty("订单主表信息")
    private OrderMasterVO order;

    /**
     *待验收时长
     */
    @RpcModelProperty("待验收时长")
    private Integer acceptanceDay;

    /**
     *待结算时长
     */
    @RpcModelProperty("待结算时长")
    private Integer balanceDay;

    @RpcModelProperty("中大相关的approvalPermit此处未使用")
    private boolean approvalPermit = false;

    @RpcModelProperty("展示送货单按钮")
    private Boolean showPrintDeliveryNote;

    @RpcModelProperty("展示验收单打印按钮")
    private Boolean showPrintAcceptance;

    @RpcModelProperty("展示出入库单打印按钮")
    private Boolean showPrintWareHouseApplication;

    @RpcModelProperty("展示撤销入库按钮")
    private Boolean showCancelReceipt;

    /**
     *订单明细信息
     */
    @RpcModelProperty("订单明细信息")
    private List<OrderDetailVO> orderDetails;

    /**
     *验收权限
     */
    @RpcModelProperty("验收权限")
    private Integer acceptance;

    /**
     *拍照验收图片
     */
    @RpcModelProperty("拍照验收图片")
    private List<String> images;

    /**
     *图片详情
     */
    @RpcModelProperty("图片详情")
    private List<OrderPicVO> receiveImages;

    /**
     *拍照验收
     */
    @RpcModelProperty("拍照验收")
    private Boolean photoAcceptance;

    /**
     *对接备注
     */
    @RpcModelProperty("对接备注")
    private String dockingMemo;

    /**
     *能否确认备案 0不需备案  1确认订单  待备案，需上传图片 2待验收后  待备案
     */
    @RpcModelProperty("能否确认备案 0不需备案  1确认订单  待备案，需上传图片 2待验收后  待备案")
    private Integer confirmForTheRecord = OrderConfirmEnum.NO_CONFIRM.getValue();

    /**
     *单合同状态 0未上传  1已上传  2无需上传
     */
    @RpcModelProperty(value = "单合同状态 0未上传  1已上传  2无需上传", enumClass = OrderUploadStatusEnum.class)
    private Integer orderContractStatus;

    /**
     * 上传的合同编号
     */
    @RpcModelProperty("上传的合同编号")
    private String uploadContractNo;

    /**
     * 合同详细信息
     */
    @RpcModelProperty("合同详细信息")
    private List<ContractInfoVO> contractInfoVOList;

    /**
     *是否可以追加验收图片,0不能追加验收图片/1可以追加验收图片
     */
    @RpcModelProperty("是否可以追加验收图片,0不能/1可以")
    private Integer canAppendReceivePic;

    /**
     * 旧单标识，true是旧单
     */
    @RpcModelProperty("旧单标识，true是旧单")
    private Boolean oldFlag;

    /**
     * 订单物流信息
     */
    @RpcModelProperty("订单物流信息")
    private OrderLogisticsInfoVO orderLogisticsInfo;

    /**
     * 订单退货信息
     */
    @RpcModelProperty("订单退货信息")
    private List<GoodsReturnOrderDetailVO> goodsReturnInfoList;

    /**
     * 订单验收审批信息
     */
    @RpcModelProperty("订单验收审批信息")
    private List<OrderApprovalInfoVO> orderApprovalInfoList;

    /**
     * 是否已经全部退货完毕
     */
    @RpcModelProperty("是否已经全部退货完毕")
    private Boolean isAllReturn;

    /**
     * 限制是否只能整单退货
     */
    @RpcModelProperty("限制只能整单退货")
    private Boolean limitOnlyWholeReturn;

    @RpcModelProperty("代配送状态")
    private Integer deliveryStatus;

    @RpcModelProperty("分拣员")
    private String sortedUser;

    @RpcModelProperty("配送员")
    private String deliveryUser;

    @RpcModelProperty("分拣图片")
    private List<String> sortedFileUrlList;

    @RpcModelProperty("配送图片")
    private List<String> deliveryFileUrlList;

    @RpcModelProperty("最新分拣备注")
    private String sortedNote;

    @RpcModelProperty("最新配送备注")
    private String deliveryNote;

    @RpcModelProperty("配送时间")
    private Date deliveredTime;

    @RpcModelProperty("已上传发票")
    private Boolean uploadInvoiceComplete;

    @RpcModelProperty("新版合同简易信息")
    private List<OrderContractInfoVO> orderContractInfoVOList;

    @RpcModelProperty("计算方式id")
    private String statementWayId;

    @RpcModelProperty("验收附件详情")
    private List<OrderAttachmentVO> attachmentList;

    @RpcModelProperty("验收视频附件详情")
    private List<OrderAttachmentVO> orderVideoAttachmentList;

    @RpcModelProperty("是否一物一码")
    private Boolean eachProductEachCode;

    @RpcModelProperty("商家是否填写批次信息")
    private Boolean suppNeedFillBatchesData;

    @RpcModelProperty(value = "验收方式", enumClass = OrderAcceptanceWayEnum.class)
    private String acceptanceWay;

    @RpcModelProperty(value = "货仓标识",enumClass = StockTypeEnum.class)
    private Integer stockWarehouseType;

    @RpcModelProperty(value = "验收审批流程状态")
    private AcceptApprovalStatusVO acceptApprovalStatusVO;

    @ModelProperty("验收图片链接-关联订单详情 列表")
    private List<AcceptPictureDTO> detailPictureDTOList;

    @ModelProperty(value = "验收附件-关联订单详情 列表")
    private List<AcceptAttachmentDTO> detailAttachmentDTOList;

    @ModelProperty(value = "提交报销申请状态(NULL表示未提交)", description = "江西中医附院定制", enumClass = OrderEventStatusEnum.class)
    private Integer submitExpenseStatus;

    @RpcModelProperty("支付记录")
    private List<OrderAttachmentVO> paymentRecordList;

    @RpcModelProperty(value = "账户类型",description = "新建线下采购单选择的的收款账户类型",enumClass = OfflineAccountTypeEnum.class)
    private Integer accountType;

    @ModelProperty(value = "入库模式", enumClass = InWarehouseModeEnum.class)
    private Integer inWarehouseMode;

    /**
     * 目前只有暨南大学使用，后续考虑将单位对接的都收归到这边
     */
    @ModelProperty("对接错误提示")
    private String dockingErrorHint;

    /**
     * 此字段专门维护详情信息，请后续将详情页面的字段尽量加在里面。如果需要提升到列表的，
     * 让前端配合做修改，将其提高至列表级别
     */
    @RpcModelProperty("订单详情页面明细数据")
    private OrderInfoFeatureVO orderInfoFeature;

    @RpcModelProperty(value = "需要展示的按钮集合", enumClass = OrderButtonTypeEnum.class)
    private List<OrderButtonTypeEnum> showButtonList = New.list();

    @ModelProperty("订单标签")
    private List<String> showTagList = New.list();

    public Integer getStockWarehouseType() {
        return stockWarehouseType;
    }

    public OrderInfoVO setStockWarehouseType(Integer stockWarehouseType) {
        this.stockWarehouseType = stockWarehouseType;
        return this;
    }

    @RpcModelProperty(value = "展示重新解冻按钮")
    private Boolean showReUnFreeze;

    public Boolean getShowReUnFreeze() {
        return showReUnFreeze;
    }

    public OrderInfoVO setShowReUnFreeze(Boolean showReUnFreeze) {
        this.showReUnFreeze = showReUnFreeze;
        return this;
    }

    public Date getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(Date deliveredTime) {
        this.deliveredTime = deliveredTime;
    }

    public String getSortedNote() {
        return sortedNote;
    }

    public void setSortedNote(String sortedNote) {
        this.sortedNote = sortedNote;
    }

    public String getDeliveryNote() {
        return deliveryNote;
    }

    public void setDeliveryNote(String deliveryNote) {
        this.deliveryNote = deliveryNote;
    }

    public List<String> getSortedFileUrlList() {
        return sortedFileUrlList;
    }

    public void setSortedFileUrlList(List<String> sortedFileUrlList) {
        this.sortedFileUrlList = sortedFileUrlList;
    }

    public List<String> getDeliveryFileUrlList() {
        return deliveryFileUrlList;
    }

    public void setDeliveryFileUrlList(List<String> deliveryFileUrlList) {
        this.deliveryFileUrlList = deliveryFileUrlList;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getSortedUser() {
        return sortedUser;
    }

    public void setSortedUser(String sortedUser) {
        this.sortedUser = sortedUser;
    }

    public String getDeliveryUser() {
        return deliveryUser;
    }

    public void setDeliveryUser(String deliveryUser) {
        this.deliveryUser = deliveryUser;
    }

    public Boolean getAllReturn() {
        return isAllReturn;
    }

    public void setAllReturn(Boolean allReturn) {
        isAllReturn = allReturn;
    }

    public Boolean getLimitOnlyWholeReturn() {
        return limitOnlyWholeReturn;
    }

    public OrderInfoVO setLimitOnlyWholeReturn(Boolean limitOnlyWholeReturn) {
        this.limitOnlyWholeReturn = limitOnlyWholeReturn;
        return this;
    }

    public OrderMasterVO getOrder() {
        return order;
    }

    public void setOrder(OrderMasterVO order) {
        this.order = order;
    }

    public Integer getAcceptanceDay() {
        return acceptanceDay;
    }

    public void setAcceptanceDay(Integer acceptanceDay) {
        this.acceptanceDay = acceptanceDay;
    }

    public Integer getBalanceDay() {
        return balanceDay;
    }

    public void setBalanceDay(Integer balanceDay) {
        this.balanceDay = balanceDay;
    }

    public boolean isApprovalPermit() {
        return approvalPermit;
    }

    public void setApprovalPermit(boolean approvalPermit) {
        this.approvalPermit = approvalPermit;
    }

    public Boolean getShowPrintDeliveryNote() {
        return showPrintDeliveryNote;
    }

    public void setShowPrintDeliveryNote(Boolean showPrintDeliveryNote) {
        this.showPrintDeliveryNote = showPrintDeliveryNote;
    }

    public Boolean getShowPrintAcceptance() {
        return showPrintAcceptance;
    }

    public void setShowPrintAcceptance(Boolean showPrintAcceptance) {
        this.showPrintAcceptance = showPrintAcceptance;
    }

    public Boolean getShowPrintWareHouseApplication() {
        return showPrintWareHouseApplication;
    }

    public void setShowPrintWareHouseApplication(Boolean showPrintWareHouseApplication) {
        this.showPrintWareHouseApplication = showPrintWareHouseApplication;
    }

    public List<OrderDetailVO> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<OrderDetailVO> orderDetails) {
        this.orderDetails = orderDetails;
    }

    public Integer getAcceptance() {
        return acceptance;
    }

    public void setAcceptance(Integer acceptance) {
        this.acceptance = acceptance;
    }

    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }

    public List<OrderPicVO> getReceiveImages() {
        return receiveImages;
    }

    public void setReceiveImages(List<OrderPicVO> receiveImages) {
        this.receiveImages = receiveImages;
    }

    public Boolean getPhotoAcceptance() {
        return photoAcceptance;
    }

    public void setPhotoAcceptance(Boolean photoAcceptance) {
        this.photoAcceptance = photoAcceptance;
    }

    public String getDockingMemo() {
        return dockingMemo;
    }

    public void setDockingMemo(String dockingMemo) {
        this.dockingMemo = dockingMemo;
    }

    public Integer getConfirmForTheRecord() {
        return confirmForTheRecord;
    }

    public void setConfirmForTheRecord(Integer confirmForTheRecord) {
        this.confirmForTheRecord = confirmForTheRecord;
    }

    public Integer getOrderContractStatus() {
        return orderContractStatus;
    }

    public void setOrderContractStatus(Integer orderContractStatus) {
        this.orderContractStatus = orderContractStatus;
    }

    public Integer getCanAppendReceivePic() {
        return canAppendReceivePic;
    }

    public void setCanAppendReceivePic(Integer canAppendReceivePic) {
        this.canAppendReceivePic = canAppendReceivePic;
    }

    public OrderLogisticsInfoVO getOrderLogisticsInfo() {
        return orderLogisticsInfo;
    }

    public OrderInfoVO setOrderLogisticsInfo(OrderLogisticsInfoVO orderLogisticsInfo) {
        this.orderLogisticsInfo = orderLogisticsInfo;
        return this;
    }

    public List<GoodsReturnOrderDetailVO> getGoodsReturnInfoList() {
        return goodsReturnInfoList;
    }

    public void setGoodsReturnInfoList(List<GoodsReturnOrderDetailVO> goodsReturnInfoList) {
        this.goodsReturnInfoList = goodsReturnInfoList;
    }

    public OrderInfoVO() {}

    public OrderInfoVO(OrderMasterVO order) {
        this.setOrder(order);
    }

    /**
     * 订单主表和detail的信息
     * @param order
     * @param orderDetails
     */
    public void setOrderAndDetail(OrderMasterVO order, List<OrderDetailVO> orderDetails) {
        this.setOrder(order);
        orderDetails = orderDetails.stream().sorted(Comparator.comparing(OrderDetailVO::getId)).collect(Collectors.toList());
        this.setOrderDetails(orderDetails);
    }

    public OrderInfoVO(OrderMasterVO order, List<OrderDetailVO> orderDetails, Integer acceptanceDay, Integer balanceDay) {
        this.setOrder(order);
        this.setOrderDetails(orderDetails);
        this.acceptanceDay = acceptanceDay;
        this.balanceDay = balanceDay;
    }

    public String getUploadContractNo() {
        return uploadContractNo;
    }

    public OrderInfoVO setUploadContractNo(String uploadContractNo) {
        this.uploadContractNo = uploadContractNo;
        return this;
    }

    public List<ContractInfoVO> getContractInfoVOList() {
        return contractInfoVOList;
    }

    public OrderInfoVO setContractInfoVOList(List<ContractInfoVO> contractInfoVOList) {
        this.contractInfoVOList = contractInfoVOList;
        return this;
    }

    public List<OrderApprovalInfoVO> getOrderApprovalInfoList() {
        return orderApprovalInfoList;
    }

    public void setOrderApprovalInfoList(List<OrderApprovalInfoVO> orderApprovalInfoList) {
        this.orderApprovalInfoList = orderApprovalInfoList;
    }

    public Boolean getOldFlag() {
        return oldFlag;
    }

    public void setOldFlag(Boolean oldFlag) {
        this.oldFlag = oldFlag;
    }

    public Boolean getShowCancelReceipt() {
        return showCancelReceipt;
    }

    public void setShowCancelReceipt(Boolean showCancelReceipt) {
        this.showCancelReceipt = showCancelReceipt;
    }

    public Boolean getUploadInvoiceComplete() {
        return uploadInvoiceComplete;
    }

    public void setUploadInvoiceComplete(Boolean uploadInvoiceComplete) {
        this.uploadInvoiceComplete = uploadInvoiceComplete;
    }

    public List<OrderContractInfoVO> getOrderContractInfoVOList() {
        return orderContractInfoVOList;
    }

    public void setOrderContractInfoVOList(List<OrderContractInfoVO> orderContractInfoVOList) {
        this.orderContractInfoVOList = orderContractInfoVOList;
    }

    public String getStatementWayId() {
        return statementWayId;
    }

    public OrderInfoVO setStatementWayId(String statementWayId) {
        this.statementWayId = statementWayId;
        return this;
    }

    public List<OrderAttachmentVO> getAttachmentList() {
        return attachmentList;
    }

    public OrderInfoVO setAttachmentList(List<OrderAttachmentVO> attachmentList) {
        this.attachmentList = attachmentList;
        return this;
    }

    public Boolean getEachProductEachCode() {
        return eachProductEachCode;
    }

    public OrderInfoVO setEachProductEachCode(Boolean eachProductEachCode) {
        this.eachProductEachCode = eachProductEachCode;
        return this;
    }

    public Boolean getSuppNeedFillBatchesData() {
        return suppNeedFillBatchesData;
    }

    public OrderInfoVO setSuppNeedFillBatchesData(Boolean suppNeedFillBatchesData) {
        this.suppNeedFillBatchesData = suppNeedFillBatchesData;
        return this;
    }

    public String getAcceptanceWay() {
        return acceptanceWay;
    }

    public OrderInfoVO setAcceptanceWay(String acceptanceWay) {
        this.acceptanceWay = acceptanceWay;
        return this;
    }

    public String getDockingErrorHint() {
        return dockingErrorHint;
    }

    public OrderInfoVO setDockingErrorHint(String dockingErrorHint) {
        this.dockingErrorHint = dockingErrorHint;
        return this;
    }

    public OrderInfoFeatureVO getOrderInfoFeature() {
        return orderInfoFeature;
    }

    public OrderInfoVO setOrderInfoFeature(OrderInfoFeatureVO orderInfoFeature) {
        this.orderInfoFeature = orderInfoFeature;
        return this;
    }

    public List<AcceptPictureDTO> getDetailPictureDTOList() {
        return detailPictureDTOList;
    }

    public void setDetailPictureDTOList(List<AcceptPictureDTO> detailPictureDTOList) {
        this.detailPictureDTOList = detailPictureDTOList;
    }

    public List<AcceptAttachmentDTO> getDetailAttachmentDTOList() {
        return detailAttachmentDTOList;
    }

    public void setDetailAttachmentDTOList(List<AcceptAttachmentDTO> detailAttachmentDTOList) {
        this.detailAttachmentDTOList = detailAttachmentDTOList;
    }

    public AcceptApprovalStatusVO getAcceptApprovalStatusVO() {
        return acceptApprovalStatusVO;
    }

    public OrderInfoVO setAcceptApprovalStatusVO(AcceptApprovalStatusVO acceptApprovalStatusVO) {
        this.acceptApprovalStatusVO = acceptApprovalStatusVO;
        return this;
    }

    public List<OrderButtonTypeEnum> getShowButtonList() {
        return showButtonList;
    }

    public OrderInfoVO setShowButtonList(List<OrderButtonTypeEnum> showButtonList) {
        this.showButtonList = showButtonList;
        return this;
    }

    public List<OrderAttachmentVO> getOrderVideoAttachmentList() {
        return orderVideoAttachmentList;
    }

    public OrderInfoVO setOrderVideoAttachmentList(List<OrderAttachmentVO> orderVideoAttachmentList) {
        this.orderVideoAttachmentList = orderVideoAttachmentList;
        return this;
    }

    public Integer getInWarehouseMode() {
        return inWarehouseMode;
    }

    public void setInWarehouseMode(Integer inWarehouseMode) {
        this.inWarehouseMode = inWarehouseMode;
    }

    public List<OrderAttachmentVO> getPaymentRecordList() {
        return paymentRecordList;
    }

    public void setPaymentRecordList(List<OrderAttachmentVO> paymentRecordList) {
        this.paymentRecordList = paymentRecordList;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getSubmitExpenseStatus() {
        return submitExpenseStatus;
    }

    public void setSubmitExpenseStatus(Integer submitExpenseStatus) {
        this.submitExpenseStatus = submitExpenseStatus;
    }

    public List<String> getShowTagList() {
        return showTagList;
    }

    public OrderInfoVO setShowTagList(List<String> showTagList) {
        this.showTagList = showTagList;
        return this;
    }

    @Override
    public String toString() {
        return "OrderInfoVO{" +
                "order=" + order +
                ", acceptanceDay=" + acceptanceDay +
                ", balanceDay=" + balanceDay +
                ", approvalPermit=" + approvalPermit +
                ", showPrintDeliveryNote=" + showPrintDeliveryNote +
                ", showPrintAcceptance=" + showPrintAcceptance +
                ", showPrintWareHouseApplication=" + showPrintWareHouseApplication +
                ", showCancelReceipt=" + showCancelReceipt +
                ", orderDetails=" + orderDetails +
                ", acceptance=" + acceptance +
                ", images=" + images +
                ", receiveImages=" + receiveImages +
                ", photoAcceptance=" + photoAcceptance +
                ", dockingMemo='" + dockingMemo + '\'' +
                ", confirmForTheRecord=" + confirmForTheRecord +
                ", orderContractStatus=" + orderContractStatus +
                ", uploadContractNo='" + uploadContractNo + '\'' +
                ", contractInfoVOList=" + contractInfoVOList +
                ", canAppendReceivePic=" + canAppendReceivePic +
                ", oldFlag=" + oldFlag +
                ", orderLogisticsInfo=" + orderLogisticsInfo +
                ", goodsReturnInfoList=" + goodsReturnInfoList +
                ", orderApprovalInfoList=" + orderApprovalInfoList +
                ", isAllReturn=" + isAllReturn +
                ", limitOnlyWholeReturn=" + limitOnlyWholeReturn +
                ", deliveryStatus=" + deliveryStatus +
                ", sortedUser='" + sortedUser + '\'' +
                ", deliveryUser='" + deliveryUser + '\'' +
                ", sortedFileUrlList=" + sortedFileUrlList +
                ", deliveryFileUrlList=" + deliveryFileUrlList +
                ", sortedNote='" + sortedNote + '\'' +
                ", deliveryNote='" + deliveryNote + '\'' +
                ", deliveredTime=" + deliveredTime +
                ", uploadInvoiceComplete=" + uploadInvoiceComplete +
                ", orderContractInfoVOList=" + orderContractInfoVOList +
                ", statementWayId='" + statementWayId + '\'' +
                ", attachmentList=" + attachmentList +
                ", orderVideoAttachmentList=" + orderVideoAttachmentList +
                ", eachProductEachCode=" + eachProductEachCode +
                ", suppNeedFillBatchesData=" + suppNeedFillBatchesData +
                ", acceptanceWay='" + acceptanceWay + '\'' +
                ", stockWarehouseType=" + stockWarehouseType +
                ", acceptApprovalStatusVO=" + acceptApprovalStatusVO +
                ", detailPictureDTOList=" + detailPictureDTOList +
                ", detailAttachmentDTOList=" + detailAttachmentDTOList +
                ", submitExpenseStatus=" + submitExpenseStatus +
                ", paymentRecordList=" + paymentRecordList +
                ", accountType=" + accountType +
                ", inWarehouseMode=" + inWarehouseMode +
                ", dockingErrorHint='" + dockingErrorHint + '\'' +
                ", orderInfoFeature=" + orderInfoFeature +
                ", showButtonList=" + showButtonList +
                ", showTagList=" + showTagList +
                ", showReUnFreeze=" + showReUnFreeze +
                '}';
    }
}
