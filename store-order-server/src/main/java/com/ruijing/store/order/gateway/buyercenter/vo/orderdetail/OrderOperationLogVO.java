package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.OperateLogBussinessTypeEnum;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @Author: Zeng <PERSON>ru
 * @Date: 2020/12/25 18:38
 */
@RpcModel("订单详情-订单操作日志返回体")
public class OrderOperationLogVO implements Serializable {

    private static final long serialVersionUID = -9100839301015957547L;

    /**
     * id
     */
    @RpcModelProperty("id")
    private Integer id;

    /**
     * 操作时间
     */
    @RpcModelProperty("时间")
    private String createTime;

    /**
     * 操作人
     */
    @RpcModelProperty("操作人")
    private String operatorName;

    /**
     * 动作
     */
    @RpcModelProperty("动作")
    private String operationType;

    /**
     * 审批等级
     */
    @RpcModelProperty("审批等级")
    private Integer approveLevel;

    /**
     * 备注
     */
    @RpcModelProperty("备注")
    private String remark;

    /**
     * 时间戳
     */
    @RpcModelProperty("时间戳")
    private String timeStamp;

    /**
     * 退货单号
     */
    @RpcModelProperty("退货单号")
    private Integer returnId;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty(value = "日志业务类型",enumClass = OperateLogBussinessTypeEnum.class)
    private Integer businessType;


    public Integer getId() {
        return id;
    }

    public OrderOperationLogVO setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public OrderOperationLogVO setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public OrderOperationLogVO setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public String getOperationType() {
        return operationType;
    }

    public OrderOperationLogVO setOperationType(String operationType) {
        this.operationType = operationType;
        return this;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public OrderOperationLogVO setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public OrderOperationLogVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public OrderOperationLogVO setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
        return this;
    }

    public Integer getReturnId() {
        return returnId;
    }

    public OrderOperationLogVO setReturnId(Integer returnId) {
        this.returnId = returnId;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderOperationLogVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderOperationLogVO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("createTime='" + createTime + "'")
                .add("operatorName='" + operatorName + "'")
                .add("operationType='" + operationType + "'")
                .add("approveLevel=" + approveLevel)
                .add("remark='" + remark + "'")
                .add("timeStamp='" + timeStamp + "'")
                .add("returnId=" + returnId)
                .add("orderId=" + orderId)
                .add("businessType=" + businessType)
                .toString();
    }
}
