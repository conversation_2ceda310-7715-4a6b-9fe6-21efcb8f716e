package com.ruijing.store.order.business.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.myorderlist.OrderUploadStatusEnum;
import com.ruijing.store.order.gateway.buyercenter.request.contract.UploadContractRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderContractTemplateVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 订单合同服务接口
 */
public interface OrderContractService {

    /**
     * 获取订单合同上传状态
     *
     * @param orderMasterList    订单主表数据列表
     * @param orderDetailMap     订单详情数据映射，key为订单ID，value为订单详情列表
     * @param contractConfigJson 合同配置JSON字符串
     * @param orgId              组织ID
     * @return key:orderId,value: {@link OrderUploadStatusEnum}
     */
    Map<Integer, Integer> getOrderContractUploadStatus(List<OrderMasterDO> orderMasterList, Map<Integer, List<OrderDetailDO>> orderDetailMap, String contractConfigJson, Integer orgId);

    /**
     * @param request
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO>
     * @description: 获取订单合同信息和其他基本信息
     * @date: 2021/1/6 15:30
     * @author: zengyanru
     */
    List<OrderInfoVO> getContractInfo(OrderBasicParamDTO request);

    /**
     * @param request
     * @return java.lang.Boolean
     * @description: 上传订单合同
     * @date: 2021/1/6 17:40
     * @author: zengyanru
     */
    Boolean uploadContractList(RjSessionInfo rjSessionInfo, UploadContractRequest request);

    /**
     * @return
     * @description: 获取当前登录用户单位的需签合同模板
     */
    List<OrderContractTemplateVO> getOrderTemplateList(RjSessionInfo rjSessionInfo);
}
