package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptPhotoConfigEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderUploadStatusEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: <PERSON>wenyu
 * @create: 2024-08-26 15:20
 * @description: 验收配置，点击验收后调用的验收配置
 */
public class OrderAcceptanceConfigVO implements Serializable {

    private static final long serialVersionUID = -8233245359098678663L;

    @ModelProperty("订单id")
    private Integer orderId;

    @ModelProperty(value = "上传验收图片配置", enumClass = OrderAcceptPhotoConfigEnum.class)
    private Integer photoAcceptance;

    @ModelProperty("绑定的经费卡信息")
    private List<OrderFundcardVO> orderFundcardVOList;

    @ModelProperty(value = "是否需要上传从我们系统生成的打印单据", enumClass = OrderUploadStatusEnum.class)
    private Integer uploadPrintFormStatus;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderAcceptanceConfigVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Integer getPhotoAcceptance() {
        return photoAcceptance;
    }

    public OrderAcceptanceConfigVO setPhotoAcceptance(Integer photoAcceptance) {
        this.photoAcceptance = photoAcceptance;
        return this;
    }

    public List<OrderFundcardVO> getOrderFundcardVOList() {
        return orderFundcardVOList;
    }

    public OrderAcceptanceConfigVO setOrderFundcardVOList(List<OrderFundcardVO> orderFundcardVOList) {
        this.orderFundcardVOList = orderFundcardVOList;
        return this;
    }

    public Integer getUploadPrintFormStatus() {
        return uploadPrintFormStatus;
    }

    public OrderAcceptanceConfigVO setUploadPrintFormStatus(Integer uploadPrintFormStatus) {
        this.uploadPrintFormStatus = uploadPrintFormStatus;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderAcceptanceConfigVO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("photoAcceptance=" + photoAcceptance)
                .add("orderFundcardVOList=" + orderFundcardVOList)
                .add("uploadPrintFormStatus=" + uploadPrintFormStatus)
                .toString();
    }
}
