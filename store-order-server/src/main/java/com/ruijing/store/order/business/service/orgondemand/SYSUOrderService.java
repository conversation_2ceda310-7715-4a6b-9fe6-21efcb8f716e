package com.ruijing.store.order.business.service.orgondemand;

import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.custom.dto.fundcard.FundCardAuthAndExtraDTO;
import com.reagent.research.custom.dto.fundcard.FundCardDTO;
import com.reagent.research.custom.dto.fundcard.SysuUnFreezeInfoDTO;
import com.reagent.research.custom.enums.BusinessTypeEnum;
import com.reagent.research.custom.enums.SystemAccountEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.enums.FundCardAuthStatusEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.OrderOperationConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.rpc.client.FundCardRelateRpcClient;
import com.ruijing.store.order.rpc.client.ResearchCustomClient;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.AccessDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Date: 2020/12/30 13:32
 */
@Service
public class SYSUOrderService {

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Resource
    private FundCardRelateRpcClient fundCardRelateRpcClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderEmailHandler orderEmailHandler;
    
    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private ResearchCustomClient researchCustomClient;

    @Resource
    private UserClient userClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @PearlValue(key = "SYSU_RECEIVE_PIC_URL_PREFIX", defaultValue = "http://sycl.sysu.edu.cn/hmsd")
    private static String SYSU_RECEIVE_PIC_URL_PREFIX;

    @PearlValue(key = "cancelorder.applycancel.overday", defaultValue = "14")
    public Long applyCancelOrderOverday;

    private final Logger logger = LoggerFactory.getLogger(SYSUOrderService.class);

    private final static String CAT_TYPE = "SYSUOrderService";

    /**
     * @description: 中大特殊的同意供应商取消订单方法
     * @date: 2021/1/27 18:28
     * @author: zengyanru
     * @param orderMasterInfo
     * @param loginInfo
     * @return java.lang.Boolean
     */
    @ServiceLog(description = "中大特殊的同意供应商取消订单方法", operationType = OperationType.WRITE)
    public Boolean agreeCancelOrder(OrderMasterDO orderMasterInfo, LoginUserInfoBO loginInfo) {
        try {
            cacheClient.controlRepeatOperation(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderMasterInfo.getId().toString(), 3*60);
            // 关闭订单，修改订单经费状态
            OrderMasterDO orderMasterToUpdate = new OrderMasterDO();
            Date shutDownDate = new Date();
            orderMasterToUpdate.setStatus(OrderStatusEnum.Close.getValue());
            orderMasterToUpdate.setShutDownDate(shutDownDate);
            orderMasterToUpdate.setFundStatus(OrderFundStatusEnum.Thrawing.getValue());
            orderMasterToUpdate.setId(orderMasterInfo.getId());
            Integer affectOrder = orderMasterMapper.updateByPrimaryKeySelective(orderMasterToUpdate);
            BusinessErrUtil.isTrue(affectOrder > 0, ExecptionMessageEnum.FAILED_TO_UPDATE_ORDER_STATUS);

            // 记录订单审批日志，操作人 为系统
            OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
            orderApprovalLog.setApproveStatus(OrderApprovalEnum.UNFROZEN_FUNDS.getValue());
            orderApprovalLog.setOperatorId(SystemAccountEnum.SYSTEM_ACCOUNT.getValue());
            orderApprovalLog.setOrderId(orderMasterInfo.getId());
            orderApprovalLog.setReason("系统发起解冻经费");
            Integer affectApprovalLog = orderApprovalLogMapper.insert(orderApprovalLog);
            BusinessErrUtil.isTrue(affectApprovalLog > 0, ExecptionMessageEnum.FAILED_TO_UPDATE_ORDER_LOG);

            // 订单异步解冻
            List<DockingExtra> dockingExtraList = dockingExtraMapper.findByInfoIn(New.list(orderMasterInfo.getForderno()));
            Set<String> extraInfoSet = dockingExtraList.stream().map(DockingExtra::getExtraInfo).collect(Collectors.toSet());
            FundCardDTO fundCardDTO = new FundCardDTO();
            fundCardDTO.setSerialNumber(orderMasterInfo.getForderno());
            fundCardDTO.setOperatorJobNumber(loginInfo.getJobNumber());
            fundCardDTO.setBusinessId(orderMasterInfo.getId());
            fundCardDTO.setBusinessType(BusinessTypeEnum.PRODUCT.getValue());
            fundCardDTO.setOpinion("同意取消");
            fundCardDTO.setCallBackMethodName("orderCancelUnFreezeSuccessFlow");
            SysuUnFreezeInfoDTO sysuUnFreezeInfoDTO = new SysuUnFreezeInfoDTO();
            sysuUnFreezeInfoDTO.setExtraInfo(New.list(extraInfoSet));
            fundCardDTO.setUserId(loginInfo.getUserId());
            fundCardDTO.setSysuUnFreezeInfoDTO(sysuUnFreezeInfoDTO);

            OrgRequest<FundCardDTO> orgRequest = new OrgRequest<>();
            orgRequest.setData(fundCardDTO);
            orgRequest.setOrgCode(loginInfo.getOrgCode());
            fundCardRelateRpcClient.fundCardUnFreeze(orgRequest);

            // 取消完成后 发送邮件通知供应商
            orderMessageHandler.sendSupplierApplyCancelEmailToSupplier(New.list(orderMasterInfo), applyCancelOrderOverday);
        } catch (Exception e) {
            throw e;
        } finally {
            cacheClient.removeCache(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderMasterInfo.getId().toString());
        }
        return true;
    }

    /**
     * @description: 中大验收审批权限校验
     * @date: 2021/3/31 17:37
     * @author: zengyanru
     * @param curOrderInfo
     * @param loginInfo
     * @return boolean
     */
    public boolean checkReceivePermission(OrderInfoVO curOrderInfo, LoginUserInfoBO loginInfo) {
        String curMethod = "checkReceivePermission";
        // 严格校验经费卡是否存在
        List<String> fundCardIdList = curOrderInfo.getOrder().getFundCardIdList();
        if (CollectionUtils.isEmpty(fundCardIdList) || StringUtils.isBlank(fundCardIdList.get(0))) {
            logger.warn("订单{}没有关联经费卡", curOrderInfo.getOrder().getOrderNo());
            Cat.logWarn(CAT_TYPE, curMethod, "订单" + curOrderInfo.getOrder().getOrderNo() + "没有关联经费卡");
            return false;
        }

        List<com.reagent.research.fundcard.dto.v2.FundCardDTO> fundCardDetailList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(loginInfo.getOrgCode(), fundCardIdList);
        if (CollectionUtils.isEmpty(fundCardDetailList) || Objects.isNull(fundCardDetailList.get(0))) {
            logger.warn("经费卡数据出错，找不到订单：{}关联的经费卡id：{}的信息", curOrderInfo.getOrder().getOrderNo(),fundCardIdList.get(0));
            Cat.logWarn(CAT_TYPE, curMethod, "经费卡数据出错，找不到经费卡id：" + fundCardIdList.get(0) + "的信息");
            return false;
        }

        // 严格校验经费卡负责人和课题组负责人是否一致（需要一致，判断经费卡负责人包含课题组负责人即可）
        List<FundCardManagerDTO> fundCardManagerList = fundCardDetailList.get(0).getFundCardManagerDTOs();
        List<Integer> fundCardManageUserIdList = fundCardManagerList.stream().map(FundCardManagerDTO::getUserId).collect(Collectors.toList());
        List<DepartmentDTO> deptList = loginInfo.getDeptList();
        deptList = deptList.stream().filter(s -> s.getId().equals(curOrderInfo.getOrder().getBuyDepartmentId())).collect(Collectors.toList());
        BusinessErrUtil.notEmpty(deptList, ExecptionMessageEnum.DEPARTMENT_CANNOT_VIEW_PURCHASER_INFO);
        Integer deptManagerUserId = deptList.get(0).getManagerId();
        UserBaseInfoDTO deptManageUserInfo = userClient.getUserDetailByID(deptManagerUserId);
        BusinessErrUtil.notNull(deptManageUserInfo, ExecptionMessageEnum.CANNOT_VERIFY_FUNDING_CARD_LEADER_INFO);
        if (!fundCardManageUserIdList.contains(deptManagerUserId)) {
            logger.error("经费卡负责人与课题组负责人不一致，请检查，param:"
                    + String.format("fundcardId：%s, deptManagerJobNumber:%s, deptId:%s, managerUserId:%s", fundCardIdList.get(0), deptManageUserInfo.getJobnumber(), curOrderInfo.getOrder().getBuyDepartmentId(), deptManagerUserId.toString()));
            Cat.logWarn(CAT_TYPE, curMethod, "经费卡负责人与课题组负责人不一致，请检查，param:"
                    + String.format("fundcardId：%s, deptManagerJobNumber:%s, deptId:%s, managerUserId:%s", fundCardIdList.get(0), deptManageUserInfo.getJobnumber(), curOrderInfo.getOrder().getBuyDepartmentId(), deptManagerUserId.toString()));
            return false;
        }

        // 验收权限判断1、负责人或操作人的权限（是否授权）
        boolean checkFundcardManager = fundCardManageUserIdList.contains(loginInfo.getUserId());
        boolean checkAuth = CollectionUtils.isNotEmpty(researchCustomClient.listAuthInfoByParam(deptManagerUserId, loginInfo.getUserId(), loginInfo.getOrgId()));
        boolean authPermission = checkFundcardManager || checkAuth;

        // 2、一二级审批权限
        List<AccessDTO> accessDTOList = userClient.getDepartmentAccess(New.list(curOrderInfo.getOrder().getBuyDepartmentId()), curOrderInfo.getOrder().getBuyUserId(), loginInfo.getOrgId());
        if (CollectionUtils.isEmpty(accessDTOList)) {
            return false;
        }
        List<String> accessCodeList = accessDTOList.stream().map(AccessDTO::getCode).collect(Collectors.toList());
        boolean hasTwoReceivePermission = accessCodeList.contains(ConfigConstant.ORDER_APPROVE_LEVEL_SECOND);
        if (OrderStatusEnum.OrderReceiveApproval.value.equals(curOrderInfo.getOrder().getStatus())) {
            return authPermission;
        } else if (OrderStatusEnum.OrderReceiveApprovalTwo.value.equals(curOrderInfo.getOrder().getStatus())) {
            return hasTwoReceivePermission;
        } else {
            return false;
        }
    }

    /**
     * @description: 控制中大的采购人中心，订单列表查看权限
     * @date: 2021/3/31 17:37
     * @author: zengyanru
     * @param searchRequest
     * @param request
     * @param rootDepartmentId
     * @param loginInfo
     * @return com.ruijing.store.order.api.search.dto.OrderSearchParamDTO
     */
    public void controllViewOrderAccessSYSU(OrderSearchParamDTO searchRequest, OrderListRequest request, Integer rootDepartmentId, LoginUserInfoBO loginInfo) {
        // 控制我的订单与课题组订单的 课题组显示逻辑
        List<DepartmentDTO> deptList = New.list();
        if (!request.getMyOrderCheck()) {
            deptList = loginInfo.getDeptList();
        }
        List<Integer> deptIdListForSearch = buyerOrderService.getDeptIdListForSearch(deptList, request, rootDepartmentId);
        request.setDeptIdList(deptIdListForSearch);
        buyerOrderService.constructSearchPageParam(searchRequest,request);

        // 构造完参数后，控制采购人能查看只有具有经费卡授权（或者本来就有权限）的订单(先照搬store逻辑）
        List<AccessDTO> accessDTOList = userClient.getDepartmentAccess(loginInfo.getDeptIdList(), loginInfo.getUserId(), loginInfo.getOrgId());
        List<String> accessCodeList = accessDTOList.stream().map(AccessDTO::getCode).collect(Collectors.toList());

        // 财务提交权限判断
        boolean financialSubmitCheck = accessCodeList.contains(ConfigConstant.FUNDCARD_AUTH)
                || accessCodeList.contains(ConfigConstant.FINANCIAL_SUBMISSION)
                || accessCodeList.contains(ConfigConstant.DANGEROUS_PURCHASE_ORDER_LEVEL_3)
                || accessCodeList.contains(ConfigConstant.ORDER_APPROVE_LEVEL_SECOND)
                || accessCodeList.contains(ConfigConstant.ORDER_APPROVE);
        if (!financialSubmitCheck) {
            List<FundCardAuthAndExtraDTO> fundcardAuthList = researchCustomClient.getByApproveAuthQueryDtoByOrgId(loginInfo.getUserId(), FundCardAuthStatusEnum.AUTHORIZED.getValue(), loginInfo.getOrgCode());
            if (CollectionUtils.isNotEmpty(fundcardAuthList)) {
                List<String> fundcardIdList = fundcardAuthList.stream().map(FundCardAuthAndExtraDTO::getFundcardId).distinct().collect(Collectors.toList());
                List<String> orderIdStringList = refFundcardOrderService.findOrderIdsByCardIds(fundcardIdList);
                if (CollectionUtils.isNotEmpty(orderIdStringList) && orderIdStringList.size() < 1000) {
                    List<Integer> fundcardRelatedOrderIdList = New.list();
                    for (String orderIdString : orderIdStringList) {
                        if (StringUtils.isNotBlank(orderIdString)) {
                            fundcardRelatedOrderIdList.add(Integer.valueOf(orderIdString));
                        }
                    }
                    // 设置额外包含的订单id值
                    searchRequest.setIncludeOrderIdList(fundcardRelatedOrderIdList);
                }
            }
            // 没有财务提交权限者只能看自己的订单+被授权的订单
            searchRequest.setBuyerIdList(New.list(loginInfo.getUserId()));
        }
    }

    /**
     * 中大特殊的验收图片url拼装逻辑
     * @param orgId
     * @param recPicUrl
     * @return
     */
    public static String joinRecPicUrl(Integer orgId, String recPicUrl) {
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId)) {
            if (!recPicUrl.startsWith("http")) {
                recPicUrl = SYSU_RECEIVE_PIC_URL_PREFIX + recPicUrl;
            }
        }
        return recPicUrl;
    }
}
