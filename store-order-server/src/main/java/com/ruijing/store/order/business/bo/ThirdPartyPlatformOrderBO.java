package com.ruijing.store.order.business.bo;

import java.util.List;

/**
 * 第三方平台订单信息
 * <AUTHOR>
 * @Date 2020/8/17 2:22 下午
 */
public class ThirdPartyPlatformOrderBO {

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 锐竞平台的订单号
     */
    private String reagentOrderNo;

    /**
     * 对接平台的订单号
     */
    private String extraOrderNo;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 课题组名
     */
    private String departmentName;

    /**
     * 订单金额
     */
    private String orderPrice;

    /**
     * 订单创建时间
     */
    private String orderCreationTime;

    /**
     * 供应商编码
     */
    private Integer suppId;

    /**
     * 更新原因
     */
    private String updatedReason;

    /**
     * 对方管理平台的订单状态（更新前的订单状态）
     */
    private Integer dockStatus;

    private List<ThirdPartyPlatformOrderDetailBO> orderDetailBOList;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getReagentOrderNo() {
        return reagentOrderNo;
    }

    public void setReagentOrderNo(String reagentOrderNo) {
        this.reagentOrderNo = reagentOrderNo;
    }

    public String getExtraOrderNo() {
        return extraOrderNo;
    }

    public void setExtraOrderNo(String extraOrderNo) {
        this.extraOrderNo = extraOrderNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(String orderPrice) {
        this.orderPrice = orderPrice;
    }

    public String getOrderCreationTime() {
        return orderCreationTime;
    }

    public void setOrderCreationTime(String orderCreationTime) {
        this.orderCreationTime = orderCreationTime;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public List<ThirdPartyPlatformOrderDetailBO> getOrderDetailBOList() {
        return orderDetailBOList;
    }

    public void setOrderDetailBOList(List<ThirdPartyPlatformOrderDetailBO> orderDetailBOList) {
        this.orderDetailBOList = orderDetailBOList;
    }

    public String getUpdatedReason() {
        return updatedReason;
    }

    public void setUpdatedReason(String updatedReason) {
        this.updatedReason = updatedReason;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getDockStatus() {
        return dockStatus;
    }

    public void setDockStatus(Integer dockStatus) {
        this.dockStatus = dockStatus;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartyPlatformOrderBO{");
        sb.append("reagentOrderNo='").append(reagentOrderNo).append('\'');
        sb.append(", extraOrderNo='").append(extraOrderNo).append('\'');
        sb.append(", status=").append(status);
        sb.append(", remark='").append(remark).append('\'');
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", orderPrice='").append(orderPrice).append('\'');
        sb.append(", orderCreationTime='").append(orderCreationTime).append('\'');
        sb.append(", orderDetailBOList='").append(orderDetailBOList).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
