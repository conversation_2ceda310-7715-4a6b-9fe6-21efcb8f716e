package com.ruijing.store.order.base.core.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;

import java.util.Date;
import java.util.List;

/**
 * @description: orderApprovalLogService
 * @author: zhuk
 * @create: 2019-07-05 16:58
 **/

public interface OrderApprovalLogService {

    /**
     * 新增订单 审批日志
     * @param orderApprovalLogDTO
     * @return
     */
    RemoteResponse insertOrderApprovalLog(OrderApprovalLogDTO orderApprovalLogDTO);

    default ListenableFuture<RemoteResponse> asyncInsertOrderApprovalLog(OrderApprovalLogDTO orderApprovalLogDTO) {
        return ListenableFutures.forValue(insertOrderApprovalLog(orderApprovalLogDTO));
    }

    /**
     * 批量新增 订单审批日志
     * @param logs
     */
    void insertOrderApprovalLogList(List<OrderApprovalLogDTO> logs);

    /**
     * 条件查询审批日志
     * @param request
     * @return
     */
    List<OrderApprovalLogDTO> findByOrderIdListAndStatus(OrderApprovalRequestDTO request);

    /**
     * 条件查询审批日志,获取最后一次验收审批通过的记录
     * @param request 请求数据
     * @return 日志
     */
    List<OrderApprovalLogDTO> getLastPassAcceptApproveLog(OrderApprovalRequestDTO request);

    /**
     * 装填电子签名url到日志中（暂时只支持验收审批，后续再支持更多的类型的电子签名）
     *
     * @param orderApprovalLogDTOList             电子签名日志
     * @param electronicSignatureOperationEnumSet 需要装填的电子签名类型
     */
    void fillLogWithElectronicSign(List<OrderApprovalLogDTO> orderApprovalLogDTOList, List<ElectronicSignatureOperationEnum> electronicSignatureOperationEnumSet);

    /**
     * 新增订单 审批日志
     * @param orderId       订单id
     * @param approveStatus 审批状态
     * @param operatorId    操作人id
     * @param reason        原因&备注
     */
    default void createOrderOperateLog(Integer orderId, Integer approveStatus, Integer operatorId, String operatorName, String reason) {
        OrderApprovalLogDTO orderApprovalLog = new OrderApprovalLogDTO();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(operatorId);
        orderApprovalLog.setOperatorName(operatorName);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setCreationTime(new Date());
        this.asyncInsertOrderApprovalLog(orderApprovalLog);
    }

    /**
     * 根据id更新操作日志时间
     * @param logs
     * @return
     */
    int updateByIds(List<OrderApprovalLogDTO> logs);

    void saveApprovalLog(Integer orderId, Integer status, Integer operatorId, String reason);
}
