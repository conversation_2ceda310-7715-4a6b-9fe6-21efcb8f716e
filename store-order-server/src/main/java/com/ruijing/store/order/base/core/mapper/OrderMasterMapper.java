package com.ruijing.store.order.base.core.mapper;

import com.ruijing.store.order.api.base.ordermaster.dto.OrderStatementRefDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderStatementRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.base.other.dto.SyncStatementStatusDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.core.model.GoodsReturnByDept;
import com.ruijing.store.order.base.core.model.OrderIdInvoiceTitleDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 *<AUTHOR>
 */
public interface OrderMasterMapper {

    /**
     * 插入新订单，（值为null 的属性不插入）
     *
     * @param record
     * @return
     */
    int insertSelective(OrderMasterDO record);

    /**
     * 根据id 查询订单
     *
     * @param id
     * @return
     */
    OrderMasterDO selectByPrimaryKey(Integer id);

    /**
     * 更新订单 ，值为null的参数 不更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderMasterDO record);

    /**
     * *根据 采购人id 和订单状态 得到订单列表
     *
     * @param fbuyerid
     * @param statusCollection
     * @return
     */
    List<OrderMasterDO> findByFbuyeridAndStatusIn(@Param("fbuyerid") Integer fbuyerid, @Param("statusCollection") Collection<Integer> statusCollection);

    /**
     * 根据id
     *
     * @param idCollection
     * @return
     */
    List<OrderMasterDO> findByIdIn(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 根据单位id和订单id得到订单列表
     *
     * @param idCollection
     * @return
     */
    List<OrderMasterDO> findByDeptIdAndIdIn(@Param("deptIdCollection") Collection<Integer> deptIdCollection, @Param("idCollection") Collection<Integer> idCollection);

    /**
     * 根据id和课题组id
     *
     * @param idCollection               id
     * @param fbuydepartmentidCollection 课题组id
     * @return
     */
    List<OrderMasterDO> findByIdInAndFbuydepartmentIdIn(@Param("idCollection") Collection<Integer> idCollection, @Param("fbuydepartmentidCollection") Collection<Integer> fbuydepartmentidCollection);

    /**
     * 根据id 更新订单状态
     *
     * @param updatedStatus
     * @param id
     * @return
     */
    int updateStatusById(@Param("updatedStatus") Integer updatedStatus, @Param("id") Integer id);

    /**
     * 根据 订单号 修改 经费状态
     *
     * @param fundStatus
     * @param forderno
     * @return
     */
    int updateFundStatusByforderno(@Param("fundStatus") Integer fundStatus, @Param("failedReason") String failedReason, @Param("forderno") String forderno);


    /**
     * 根据 状态 和 订单取消时间查询 订单列表
     *
     * @param status
     * @param deadFcanceldate
     * @return
     */
    List<OrderMasterDO> findByStatusAndFcanceldateLessThan(@Param("status") Integer status, @Param("deadFcanceldate") LocalDate deadFcanceldate,
                                                           @Param("equalFcanceldate") LocalDate equalFcanceldate, @Param("excludeOrgIds") List<Integer> excludeOrgIds);

    /**
     * 根据 状态 和 订单生成时间查询 订单列表
     *
     * @param status
     * @param deadForderdate
     * @return
     */
    List<OrderMasterDO> findByStatusAndForderdateLessThan(@Param("status") Integer status, @Param("deadForderdate") LocalDate deadForderdate,
                                                          @Param("equalForderdate") LocalDate equalForderdate, @Param("excludeOrgIds") List<Integer> excludeOrgIds);

    /**
     * 根据orderMaster的 orgid 和 订单状态 查询订单
     *
     * @param status
     * @param fuserid
     * @return
     */
    List<OrderMasterDO> findByStatusAndOrgId(@Param("status") Integer status, @Param("fuserid") Integer fuserid, @Param("startId") Integer startId, @Param("limit") Integer limit);

    /**
     * 根据orderMaster的 forderno   查询订单
     *
     * @param forderno
     * @return
     */
    OrderMasterDO findByForderno(@Param("forderno") String forderno);

    /**
     * 根据orderMaster的 orderNo 查询订单
     *
     * @param fordernoCollection
     * @return
     */
    List<OrderMasterDO> findByFordernoIn(@Param("fordernoCollection") Collection<String> fordernoCollection);

    /**
     * 查找超时结算的订单
     *
     * @param configs 用户超时配置
     * @param status  订单状态
     * @return
     */
    List<OrderTimeOutDTO> findTimeOutBalanceByConifgs(@Param("configs") List<OrganizationConfigDTO> configs, @Param("status") List<Integer> status);

    /**
     * 查找超时验收的订单
     *
     * @param configs 用户超时配置
     * @param status  订单状态
     * @return
     */
    List<OrderTimeOutDTO> findTimeOutExamineByConfigs(@Param("configs") List<OrganizationConfigDTO> configs, @Param("status") List<Integer> status);

    /**
     * 查询超时 验收订单
     *
     * @param fuserid
     * @param status
     * @return
     */
    List<OrderMasterDO> findReceiptTimeOut(@Param("fuserid") Integer fuserid, @Param("status") Integer status, @Param("limitDay") Integer limitDay);

    /**
     * 根据id更新订单的信息
     *
     * @param updateOrderParamDTO
     * @return
     */
    int updateOrderById(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 批量根据id更新订单信息
     * @param updateOrderParamDTOList 更新数据
     * @return 更新数量
     */
    int updateOrderByIdList(@Param("list") List<UpdateOrderParamDTO> updateOrderParamDTOList);

    /**
     * 通过一批id号将订单更新
     *
     * @param updated
     * @param idCollection
     * @return
     */
    int updateStatementByIdIn(@Param("updated") OrderMasterDO updated, @Param("idCollection") Collection<Integer> idCollection);

    /**
     * 批量更新订单的结算单id
     *
     * @param list
     * @return
     */
    int updateStatementIdByOrderIdIn(@Param("updated") OrderMasterDO updated, @Param("params") List<OrderStatementRequestDTO> list);

    /**
     * 根据订单号更新订单的信息
     *
     * @param updateOrderParamDTO
     * @return
     */
    int updateOrderByOrderNo(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 根据订单号批量更新订单状态
     *
     * @param updated
     * @param orderNoCollection
     * @return
     */
    int updateByOrderNoIn(@Param("updated") OrderMasterDO updated, @Param("orderNoCollection") Collection<String> orderNoCollection);

    /**
     * 批量更新订单的状态
     *
     * @param updated
     * @param idCollection
     * @return
     */
    int updateStatusByIdIn(@Param("updated") OrderMasterDO updated,
                           @Param("idCollection") Collection<Integer> idCollection);

    /**
     * 根据结算id查询订单基本信息
     *
     * @param statementIdCollection
     * @return
     */
    List<OrderStatementRefDTO> findOrderBaseByStatementIds(@Param("statementIdCollection") Collection<Integer> statementIdCollection);

    /**
     * 根据单位id、部门id、订单状态搜索订单信息
     * @param fuserid
     * @param fbuydepartmentidCollection
     * @param statusCollection
     * @return 符合条件的订单主表信息
     */
    List<OrderMasterDO> findByOrgIdDeptIdStatus(@Param("fuserid") Integer fuserid, @Param("fbuydepartmentidCollection") Collection<Integer> fbuydepartmentidCollection, @Param("statusCollection") Collection<Integer> statusCollection);

    /**
     * 根据采购单id查询订单信息
     *
     * @param ftbuyappidCollection
     * @return
     */
    List<OrderMasterDO> findByFtbuyappidIn(@Param("ftbuyappidCollection") Collection<Integer> ftbuyappidCollection);


    /**
     * 查询订单最大的id
     * @return
     */
    Integer selectMaxId();

    /**
     * 根据 minId maxId 查询订单数据
     * @param minId
     * @param maxId
     * @return
     */
    List<OrderMasterDO> findByAfterMinAndBeforeMax(@Param("minId")Integer minId, @Param("maxId")Integer maxId);

    /**
     * 根据updateTime 查询最近更新的订单数据
     * @param updateTime
     * @return
     */
    List<Integer> findIdByUpdateTimeAfter(@Param("updateTime")Date updateTime);

    /**
     * 根据订单号更新竞价单id
     * @param updatedBidOrderId
     * @param fordernoCollection
     * @return
     */
    int updateBidOrderIdByOrderNoList(@Param("updatedBidOrderId")String updatedBidOrderId,@Param("orderNoCollection")Collection<String> fordernoCollection);

    /**
     * 根据订单号 批量更新订单其他字段
     * @param updatedStatus
     * @return
     */
    int updateFieldByForderno(@Param("list")List<UpdateOrderParamDTO> updatedStatus);

    /**
     * @description: 通过订单id查找发票抬头
     * @date: 2021/3/26 17:41
     * @author: zengyanru
     * @param idCollection
     * @return java.util.List<com.ruijing.store.order.base.core.model.OrderIdInvoiceTitleDO>
     */
    List<OrderIdInvoiceTitleDO> findIdAndInvoiceTitleByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 根据id批量更新状态
     * @param updatedList
     * @return
     */
	int updateFieldByIdIn(@Param("updatedList")List<UpdateOrderParamDTO> updatedList);

    /**
     * 计算某种flowid的条目数
     * @param flowId
     * @return
     */
	Integer countByFlowId(@Param("flowId")Integer flowId, @Param("startTime")Date startTime, @Param("endTime")Date endTime);

    /**
     * 根据flowid和订单时间查询订单列表
     * @param flowId
     * @param startTime
     * @param endTime
     * @return
     */
	List<OrderMasterDO> selectByFlowIdAndForderdate(@Param("flowId")Integer flowId,
                                                    @Param("startTime")Date startTime,
                                                    @Param("endTime")Date endTime,
                                                    @Param("startIdx")Integer startIdx,
                                                    @Param("pageSize")Integer pageSize);

    /**
     * 根据id批量更新flow id
     * @param updatedList
     * @return
     */
	int batchUpdateFlowId(@Param("updatedList")List<OrderMasterDO> updatedList);

    /**
     * 订单号前缀匹配
     * @param sameOrderNo
     * @return
     */
	List<OrderMasterDO> findBySameOrderNoLike(@Param("sameOrderNo")String sameOrderNo);

     /**
     * 根据订单id和新的地址更新送货地址，写死 订单状态为8（待确认）
     * @param updatedFbiderdeliveryplace
     * @param id
     * @return
     */
	int updateDeliveryPlaceByIdAndControlStatus(@Param("updatedFbiderdeliveryplace")String updatedFbiderdeliveryplace,
                                                @Param("updatedBuyerContactMan")String updatedBuyerContactMan,
                                                @Param("updatedBuyerTelephone")String updatedBuyerTelephone,
                                                @Param("id")Integer id);

    /**
     * 批量更新订单收货地址
     * @param orderMasterDoList 订单主表DO列表
     * @return 更新条数
     */
    int batchUpdateDeliveryPlace(@Param("orderMasterDoList") List<OrderMasterDO> orderMasterDoList);

    /**
     * 通过id范围查找orderdate
     * @param minId
     * @param maxId
     * @return
     */
	List<OrderMasterDO> selectIdAndDateBetween(@Param("minId")Integer minId, @Param("maxId")Integer maxId);

    /**
     * 用于同步订单结算状态，状态不一致才修改，防止刷新表更新时间
     *  statementStatus为null则不更新
     * @param syncStatementStatusDTOS
     * @return 更新条数
     */
    int batchUpdateStatementStatus(@Param("syncStatementStatusDTOS") Collection<SyncStatementStatusDTO> syncStatementStatusDTOS);

    /**
     * 陆军医的特殊需求，筛选特定条件，按照课题组返回退货金额，用于后续计算课题组某月的总金额
     * @param orgId
     * @param minForderdate
     * @param maxForderdate
     * @param excludeStatusList
     * @param excludeSuppIdList
     * @return
     */
    List<GoodsReturnByDept> sumReturnAmountByDept(@Param("orgId")Integer orgId,
                                                  @Param("minForderdate")Date minForderdate,
                                                  @Param("maxForderdate")Date maxForderdate,
                                                  @Param("excludeStatusList")Collection<Integer> excludeStatusList,
                                                  @Param("excludeSuppIdList")Collection<Integer> excludeSuppIdList);

    int countBySuppIdAndStatus(@Param("suppIdCollection") Collection<Integer> suppIdCollection, @Param("statusCollection") Collection<Integer> statusCollection);

    void batchUpdateOrderTotalAmount(@Param("col") List<OrderMasterDO> collect);

    /**
     * 更新单位id,单位名称，单位code
     *
     * @param orderMasterDoList
     */
    void batchUpdateOrgInfo(@Param("orderMasterDoList") List<OrderMasterDO> orderMasterDoList);


    /**
     * 根据单位ID查询订单，排除指定状态
     *
     * @param excludeStatusList 需要排除的状态集合
     * @param orgId 单位ID
     * @return 订单列表
     */
    List<OrderMasterDO> findByExcludeStatusAndOrgId(@Param("excludeStatusList") Collection<Integer> excludeStatusList, 
                                                   @Param("orgId") Integer orgId);

    /**
     * 更新订单验收信息
     */
    int updateAcceptInfoById(UpdateOrderParamDTO updateOrderParamDTO);

    /**
     * 取消发货-清空发货信息
     */
    void cancelDeliveryById(@Param("orderId") Integer orderId, @Param("status") Integer status);

}
