package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.saturn.api.contract.dto.OrderContractQueryDTO;
import com.ruijing.order.saturn.api.contract.enums.ContractSignStatusEnum;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderCancelDeliveryDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderConfirmDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderDeliveryDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderUpdateStatusRpcService;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OrderOperationConstant;
import com.ruijing.store.order.other.service.ReimbursementService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

/**
 * @author: liwenyu
 * @createTime: 2023-10-27 16:55
 * @description:
 **/
@MSharpService
public class OrderUpdateStatusRpcServiceImpl implements OrderUpdateStatusRpcService {

    private static final String CAT_TYPE = OrderUpdateStatusRpcService.class.getName();

    private final Logger logger = LoggerFactory.getLogger(getClass());


    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private ReimbursementService reimbursementService;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderContractClient orderContractClient;


    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private CacheClient cacheClient;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "订单发货")
    public RemoteResponse<Boolean> orderDelivery(OrderDeliveryDTO orderDeliveryDTO) {
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderDeliveryDTO.getOrderId());
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForDelivery.getValue().equals(orderMasterDO.getStatus()), ExecptionMessageEnum.ORDER_NOT_PENDING_SHIPMENT);
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderNo(orderMasterDO.getForderno());
        updateOrderParamDTO.setOrderId(orderDeliveryDTO.getOrderId());
        updateOrderParamDTO.setDeliveryDate(orderDeliveryDTO.getDeliveryDate());
        updateOrderParamDTO.setDeliveryMan(orderDeliveryDTO.getDeliveryMan());
        updateOrderParamDTO.setDeliveryManId(orderDeliveryDTO.getDeliveryManId());
        updateOrderParamDTO.setDeliveryInfo(orderDeliveryDTO.getDeliveryInfo());
        updateOrderParamDTO.setDeliveryNo(orderDeliveryDTO.getDeliveryNo());
        updateOrderParamDTO.setStatus(OrderStatusEnum.WaitingForReceive.getValue());
        orderMasterMapper.updateOrderById(updateOrderParamDTO);
        orderMasterDO.setId(orderDeliveryDTO.getOrderId());
        orderMasterDO.setFdeliverydate(orderDeliveryDTO.getDeliveryDate());
        orderMasterDO.setFdeliveryman(orderDeliveryDTO.getDeliveryMan());
        orderMasterDO.setFdeliverymanid(orderDeliveryDTO.getDeliveryManId());
        orderMasterDO.setDeliveryInfo(orderDeliveryDTO.getDeliveryInfo());
        orderMasterDO.setDeliveryNo(orderDeliveryDTO.getDeliveryNo());
        orderMasterDO.setStatus(OrderStatusEnum.WaitingForReceive.getValue());
        reimbursementService.saveRecord(orderMasterDO);
        // 旧的推送管理平台
        this.pushOrderInfoToThirdPlatform(OrderEventTypeEnum.DELIVERY_ORDER, orderMasterDO, updateOrderParamDTO);
        return RemoteResponse.success(true);
    }

    /**
     * 旧推送管理平台，等待迁移后删除。现在对没有迁移完成的单位生效。
     * 参考文献：com.ruijing.store.order.business.service.impl.OrderMasterCommonServiceImpl#pushOrderInfoToThirdPlatform(com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO, com.ruijing.store.order.base.core.model.OrderMasterDO)
     */
    private void pushOrderInfoToThirdPlatform(OrderEventTypeEnum orderEventType, OrderMasterDO orderInfo, UpdateOrderParamDTO updateOrderParamDTO) {
        // 走order-thunder-service服务
        if (dockingConfigCommonService.getIfNeedOldPush(orderInfo, New.list(OuterBuyerDockingTypeEnum.ORDER_NO_PUSH, OuterBuyerDockingTypeEnum.ORDER_PUSH))) {
            Runnable task = () -> {
                ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderInfo);
                List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(orderInfo.getId());
                if (CollectionUtils.isNotEmpty(detailDOList)) {
                    thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
                }
                thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, orderEventType, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
            };
            AsyncExecutor
                    .listenableRunAsync(task)
                    .addFailureCallback(ex -> {
                        logger.error("订单号:" + orderInfo.getForderno() + "更新第三方订单状态失败：" + ex);
                        Cat.logError(CAT_TYPE, "pushSingleOrderInfo", "更新第三方订单状态失败：", ex);
                        dockingExtraService.saveOrUpdateDockingExtra(orderInfo.getForderno(), orderInfo.getForderno(), false, ex.getMessage());
                    });
        } else {
            // 推送订单更新到第三方平台(未迁移的单位走这个老接口)
            orderMasterForTPIService.updateThirdPlatformOrder(updateOrderParamDTO);
        }
    }
    /**
     * 原本供应商确认订单走的是 com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService#updateOrderById
     * 此接口用于单独替换供应商确认订单事件（须提醒对接方确认更换接口）
     * @param orderConfirmDTO 确认订单入参
     * @return
     */
    @Override
    public RemoteResponse<Boolean> suppConfirmOrder(OrderConfirmDTO orderConfirmDTO) {
        Assert.isTrue(orderConfirmDTO.getOrderId() != null, "orderId不能为空");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderConfirmDTO.getOrderId());
        Preconditions.notNull(orderMasterDO,"未找到订单信息！");
        // 校验是否需要签署合同
        boolean needContractAndSign = needContractAndSign(orderConfirmDTO, orderMasterDO.getFuserid());
        BusinessErrUtil.isTrue(needContractAndSign, ExecptionMessageEnum.SUPPLIER_CONTRACT_NOT_SIGNED);
        // 修改订单状态
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderConfirmDTO.getOrderId());
        updateOrderParamDTO.setConfirmDate(orderConfirmDTO.getConfirmDate());
        updateOrderParamDTO.setConfirmManId(orderConfirmDTO.getConfirmManId());
        updateOrderParamDTO.setConfirmMan(orderConfirmDTO.getConfirmMan());
        updateOrderParamDTO.setStatus(OrderStatusEnum.WaitingForDelivery.getValue());
        orderMasterMapper.updateOrderById(updateOrderParamDTO);
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "供应商取消发货")
    public RemoteResponse<Boolean> suppCancelDelivery(OrderCancelDeliveryDTO orderCancelDeliveryDTO) {
        Integer orderId = orderCancelDeliveryDTO.getOrderId();
        Integer cancelManId = orderCancelDeliveryDTO.getCancelManId();
        String cancelReason = orderCancelDeliveryDTO.getCancelReason();
        Preconditions.notNull(orderId, "订单id不能为空");
        Preconditions.notNull(cancelManId, "取消人id不能为空");
        Preconditions.notNull(cancelReason, "取消原因不能为空");

        // 订单验收锁
        final String lockKey = OrderOperationConstant.ACCEPT_REDIS_CACHE_KEY + orderId;
        boolean getLock = false;
        try {
            getLock = cacheClient.tryLock(lockKey, 10);
            BusinessErrUtil.isTrue(getLock, ExecptionMessageEnum.ORDER_RECEIVING_OR_CANCEL_DELIVERY_PROCESSING);
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderCancelDeliveryDTO.getOrderId());
            BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
            List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue()));
            // 仅支持 待收货 状态操作
            BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForReceive.getValue().equals(orderMasterDO.getStatus()), ExecptionMessageEnum.ORDER_NOT_PENDING_RECEIPT_CANNOT_CANCEL_DELIVERY);
            // 校验是否有退货单
            List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(orderId);
            BusinessErrUtil.isEmpty(goodsReturnList, ExecptionMessageEnum.ORDER_HAS_RETURN_RECORD_CANNOT_CANCEL_DELIVERY);

            // 校验并重置一物一码状态
            checkAndResetUniqueBarCode(orderMasterDO, orderExtraDTOList);

            // 更新订单状态和发货信息
            orderMasterMapper.cancelDeliveryById(orderId, OrderStatusEnum.WaitingForDelivery.getValue());

            // 记录操作日志
            createOrderOperateLog(orderId, OrderApprovalEnum.CANCEL_DELIVERY.getValue(), cancelManId, cancelReason);

            // 发邮件
            orderMessageHandler.sendCancelDeliveryToBuyer(orderMasterDO, cancelReason);

            return RemoteResponse.success();
        } finally {
            if (getLock) {
                cacheClient.unlock(lockKey);
            }
        }
    }

    /**
     * 校验并重置一物一码
     *
     * @param orderMasterDO     订单主信息
     * @param orderExtraDTOList 订单扩展信息
     */
    private void checkAndResetUniqueBarCode(OrderMasterDO orderMasterDO, List<OrderExtraDTO> orderExtraDTOList) {
        // 判断一物一码开关是否开启
        boolean isEachProductEachCodeEnabled = false;
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            for (OrderExtraDTO orderExtraDTO : orderExtraDTOList) {
                if (OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue().equals(orderExtraDTO.getExtraKey())) {
                    isEachProductEachCodeEnabled = CommonValueUtils.parseNumberStrToBoolean(orderExtraDTO.getExtraValue());
                    break;
                }
            }
        }

        // 如果没有开启一物一码，直接返回
        if (BooleanUtils.isNotTrue(isEachProductEachCodeEnabled)) {
            return;
        }

        // 查询一物一码信息
        List<OrderUniqueBarCodeDTO> uniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderMasterDO.getForderno(), New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
        if (CollectionUtils.isEmpty(uniqueBarCodeDTOList)) {
            return;
        }

        // 校验一物一码商品状态
        for (OrderUniqueBarCodeDTO dto : uniqueBarCodeDTOList) {
            // 检查是否存在非待入库状态
            if (!Objects.equals(dto.getInventoryStatus(), OrderProductInventoryStatusEnum.WAITING_FOR_INBOUND.getCode())) {
                BusinessErrUtil.isTrue(false, ExecptionMessageEnum.BAR_CODE_HAS_INBOUND_RECORD_CANNOT_CANCEL_DELIVERY, dto.getUniBarCode());
            }
            // 检查是否存在非待收货状态
            if (!Objects.equals(dto.getTransactionStatus(), OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode())) {
                BusinessErrUtil.isTrue(false, ExecptionMessageEnum.BAR_CODE_HAS_ACCEPT_CANNOT_CANCEL_DELIVERY, dto.getUniBarCode());
            }
        }

        // 重置二维码状态为未打印
        List<OrderUniqueBarCodeDTO> resetRequest = uniqueBarCodeDTOList.stream()
                .map(dto -> {
                    OrderUniqueBarCodeDTO resetDto = new OrderUniqueBarCodeDTO();
                    resetDto.setUniBarCode(dto.getUniBarCode())
                            .setOrderNo(dto.getOrderNo())
                            .setOrderDetailId(dto.getOrderDetailId())
                            .setPrinted(0);
                    return resetDto;
                })
                .collect(toList());

        // 调用修改接口重置打印状态
        if (CollectionUtils.isNotEmpty(resetRequest)) {
            orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(resetRequest);
        }
    }





    /**
     * 创建日志
     */
    private void createOrderOperateLog(Integer orderId, Integer approveStatus, Integer userId, String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
    }

    /**
     *
     * @param orderConfirmDTO
     * @param orgId
     * @return
     */
    private boolean needContractAndSign(OrderConfirmDTO orderConfirmDTO,
                                        Integer orgId) {
        // 读取配置（后续迭代实现，当前默认需要合同，走深圳市龙华区人民医院流程）
        boolean needContract = true;
        if (!needContract){
            return true;
        } else {
            boolean signed = true;
            // 深圳市龙华区人民医院试点
            if (OrgEnum.SHEN_ZHEN_REN_MIN_YI_YUAN.getValue() == orgId){
                // TODO 校验是否签署
                OrderContractQueryDTO queryDTO = new OrderContractQueryDTO();
                queryDTO.setOrderNoList(New.list(orderConfirmDTO.getOrderNo()));
                OrderContractInfoVO orderContractInfo = orderContractClient.getOrderContractInfo(queryDTO).get(0);
                // 双方都签署了合同才能确认订单
                if (!ContractSignStatusEnum.SIGNED.getCode().equals(orderContractInfo.getBuyerSignStatus()) ||
                        !ContractSignStatusEnum.SIGNED.getCode().equals(orderContractInfo.getSellerSignStatus())){
                    signed = false;
                }
            }
            return signed;
        }
    }

}
