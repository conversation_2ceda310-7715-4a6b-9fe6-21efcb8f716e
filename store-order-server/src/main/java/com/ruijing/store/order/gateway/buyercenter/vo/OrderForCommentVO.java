package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: zhukai
 * @date : 2020/12/29 下午2:48
 * @description: 订单评价基础信息
 */
@RpcModel(value = "订单列表-评价-订单信息")
public class OrderForCommentVO implements Serializable {

    private static final long serialVersionUID = 1728911140111333394L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("订单日期")
    private String orderDate;

    @RpcModelProperty("订单详情信息")
    private List<OrderDetailForCommentVO> orderDetailList;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<OrderDetailForCommentVO> getOrderDetailList() {
        return orderDetailList;
    }

    public void setOrderDetailList(List<OrderDetailForCommentVO> orderDetailList) {
        this.orderDetailList = orderDetailList;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public OrderForCommentVO setOrderDate(String orderDate) {
        this.orderDate = orderDate;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderForCommentVO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("orderId=" + orderId)
                .add("orderDate='" + orderDate + "'")
                .add("orderDetailList=" + orderDetailList)
                .toString();
    }
}
