package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderAllRelateLogDTO;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.orderapprovallog.service.OrderApprovalLogRPCService;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.OrderOperateLogService;
import com.ruijing.store.order.base.core.translator.OrderApprovalLogTranslator;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.PurchaseOrderAllRelateLogVO;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 日志操作
 * @author: zhuk
 * @create: 2019-10-18 09:24
 **/
@MSharpService
public class OrderApprovalLogRPCServiceImpl implements OrderApprovalLogRPCService {

    @Resource
    private OrderApprovalLogService orderApprovalLogService;
    
    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderOperateLogService orderOperateLogService;

    @Override
    public RemoteResponse<Boolean> insertOrderApproval(OrderApprovalLogDTO request) {
        if (request == null) {
            return RemoteResponse.<Boolean>custom().setSuccess().setFailure("插入失败！入参不能为空！");
        }
        RemoteResponse response = orderApprovalLogService.insertOrderApprovalLog(request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(response.isSuccess());
    }

    @Override
    public RemoteResponse<Integer> insertOrderApprovalLogList(List<OrderApprovalLogDTO> request) {
        if (CollectionUtils.isEmpty(request)) {
            return RemoteResponse.<Integer>custom().setSuccess().setFailure("插入失败！入参不能为空！");
        }
        orderApprovalLogService.insertOrderApprovalLogList(request);
        return RemoteResponse.<Integer>custom().setSuccess().setData(request.size());
    }

    @Override
    public RemoteResponse<Boolean> saveOrUpdateApprovalLog(OrderApprovalLogDTO request) {
        final Integer orderId = request.getOrderId();
        final Integer approveStatus = request.getApproveStatus();
        final String reason = request.getReason();
        Preconditions.notNull(orderId, "orderId must not be null");
        Preconditions.notNull(approveStatus, "approveStatus must not be null");

        OrderApprovalRequestDTO args = new OrderApprovalRequestDTO();
        args.setOrderIdList(New.list(orderId));
        args.setTypeList(New.list(approveStatus));
        final List<OrderApprovalLogDTO> logs = orderApprovalLogService.findByOrderIdListAndStatus(args);
        if (CollectionUtils.isEmpty(logs)) {
            insertOrderApproval(request);
        } else {
            logs.forEach(it -> {
                it.setCreationTime(new Date());
                it.setReason(reason);
            });
            orderApprovalLogService.updateByIds(logs);
        }
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 批量查询 订单审批日志 上限500
     *
     * @param request 查询数据
     */
    @Override
    public RemoteResponse<List<OrderApprovalLogDTO>> listOrderApprovalLog(OrderApprovalRequestDTO request) {
        List<Integer> queryOrderIdList = request.getOrderIdList();
        Preconditions.isTrue( queryOrderIdList.size() <= 500,"一次性查询订单数量不能大于500");
        Set<Integer> queryTypeSet = request.getTypeList() == null ? New.set() : New.set(request.getTypeList());
        List<OrderApprovalEnum> orderApproveStatusList = request.getOrderApproveStatusList();
        if(CollectionUtils.isNotEmpty(orderApproveStatusList)){
            // 兼容旧配置
            queryTypeSet.addAll(orderApproveStatusList.stream().map(OrderApprovalEnum::getValue).collect(Collectors.toSet()));
        }
        List<OrderApprovalLog> data = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(queryOrderIdList, queryTypeSet);
        List<OrderApprovalLogDTO> dtoList = data.stream().map(OrderApprovalLogTranslator::orderApprovalLogToDto).collect(Collectors.toList());
        return RemoteResponse.<List<OrderApprovalLogDTO>>custom().setData(dtoList).setSuccess();
    }

    /**
     * 批量查询采购人中心的日志
     */
    @Override
    public RemoteResponse<List<OrderAllRelateLogDTO>> listOrderLogInPurchaseCenter(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return RemoteResponse.success(New.emptyList());
        }
        Preconditions.isTrue(CollectionUtils.size(orderIdList) <= 200, "单次查询订单数量不能超过200个");
        List<PurchaseOrderAllRelateLogVO> orderAllRelateLogVOS = orderOperateLogService.listOrderAllRelateLog(orderIdList);
        if (CollectionUtils.isEmpty(orderAllRelateLogVOS)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderAllRelateLogDTO> orderAllRelateLogDTOList = OrderApprovalLogTranslator.purchaseOrderAllRelateLogVO2DTO(orderAllRelateLogVOS);
        return RemoteResponse.success(orderAllRelateLogDTOList);
    }
}
