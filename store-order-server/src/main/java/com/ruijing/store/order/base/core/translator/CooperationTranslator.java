package com.ruijing.store.order.base.core.translator;

import com.ruijing.cooperation.cbsd.api.msg.CbsdStoreHouseDTO;
import com.ruijing.store.order.api.base.other.dto.OrderStoreHouseDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderStoreHouseVO;

/**
 * 中爆相关dto的转换
 */
public class CooperationTranslator {

    /**
     * 中爆存储仓库 dto 转换为订单存储仓库的 dto
     * @param dto
     * @return
     */
    public static OrderStoreHouseDTO dtoToOrderStorageDto(CbsdStoreHouseDTO dto) {
        OrderStoreHouseDTO result = new OrderStoreHouseDTO();
        result.setStoreHouseId(dto.getStoreHouseId());
        result.setStoreHouseName(dto.getStoreHouseName());
        result.setStoreHouseNumber(dto.getStoreHouseNumber());

        return result;
    }

    /**
     * 中爆存储仓库 dto 转换为订单存储仓库的 dto
     * @param dto
     * @return
     */
    public static OrderStoreHouseVO dtoToOrderStorageVO(CbsdStoreHouseDTO dto) {
        OrderStoreHouseVO result = new OrderStoreHouseVO();
        result.setStoreHouseId(dto.getStoreHouseId());
        result.setStoreHouseName(dto.getStoreHouseName());
        result.setStoreHouseNumber(dto.getStoreHouseNumber());

        return result;
    }
}
