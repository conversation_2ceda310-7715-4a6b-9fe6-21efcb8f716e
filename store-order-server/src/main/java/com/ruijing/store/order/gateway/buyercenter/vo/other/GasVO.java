package com.ruijing.store.order.gateway.buyercenter.vo.other;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2024-04-24 17:49
 * @description:
 */
public class GasVO implements Serializable {

    private static final long serialVersionUID = 9027025421490692850L;

    @ModelProperty("充装气体名称")
    private String name;

    @ModelProperty("体色")
    private String bodyColor;

    @ModelProperty("字样")
    private String typeface;

    @ModelProperty("字色")
    private String letterColor;

    @ModelProperty("色环")
    private String colourRing;

    @ModelProperty("头色（上）")
    private String topHeadColor;

    @ModelProperty("头色（下）")
    private String bottomHeadColor;


    public String getName() {
        return name;
    }

    public GasVO setName(String name) {
        this.name = name;
        return this;
    }

    public String getBodyColor() {
        return bodyColor;
    }

    public GasVO setBodyColor(String bodyColor) {
        this.bodyColor = bodyColor;
        return this;
    }

    public String getTypeface() {
        return typeface;
    }

    public GasVO setTypeface(String typeface) {
        this.typeface = typeface;
        return this;
    }

    public String getLetterColor() {
        return letterColor;
    }

    public GasVO setLetterColor(String letterColor) {
        this.letterColor = letterColor;
        return this;
    }

    public String getColourRing() {
        return colourRing;
    }

    public GasVO setColourRing(String colourRing) {
        this.colourRing = colourRing;
        return this;
    }

    public String getTopHeadColor() {
        return topHeadColor;
    }

    public GasVO setTopHeadColor(String topHeadColor) {
        this.topHeadColor = topHeadColor;
        return this;
    }

    public String getBottomHeadColor() {
        return bottomHeadColor;
    }

    public GasVO setBottomHeadColor(String bottomHeadColor) {
        this.bottomHeadColor = bottomHeadColor;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GasVO.class.getSimpleName() + "[", "]")
                .add("name='" + name + "'")
                .add("bodyColor='" + bodyColor + "'")
                .add("typeface='" + typeface + "'")
                .add("letterColor='" + letterColor + "'")
                .add("colourRing='" + colourRing + "'")
                .add("topHeadColor='" + topHeadColor + "'")
                .add("bottomHeadColor='" + bottomHeadColor + "'")
                .toString();
    }
}
