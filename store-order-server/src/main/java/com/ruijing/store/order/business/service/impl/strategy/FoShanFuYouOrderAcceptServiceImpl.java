package com.ruijing.store.order.business.service.impl.strategy;

import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 佛山妇幼的验收策略
 * @author: zhong<PERSON><PERSON>i
 * @create: 2022-02-18 15:05
 */
@Service(OrderAcceptConstant.FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN_ACCEPT)
public class FoShanFuYouOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    /**
     * 计算入库模式
     *
     * @param orderMasterDO 订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList 订单商品详情
     * @return 入库模式
     */
    @Override
    public InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList) {
        // 服务类商品强制无需入库，其他的正常入库, detail里已经在业务上不会包含服务类商品和其他
        final boolean existServiceProduct = orderDetailList.stream().anyMatch(d -> InboundTypeEnum.SERVICE.getDesc().equals(d.getCategoryTag()));
        if (existServiceProduct) {
            return InWarehouseModeEnum.NO_NEED;
        }
        return super.calculateInWarehouseMode(orderMasterDO, receiptConfigMap, orderDetailList);
    }
}
