package com.ruijing.store.order.gateway.file;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.business.service.file.OrderUploadFileService;
import com.ruijing.store.order.gateway.file.request.OrderDetailAcceptFileRequest;
import com.ruijing.store.order.gateway.file.request.OrderFileInfoRequestDTO;
import com.ruijing.store.order.gateway.file.request.OrderFileUploadRequestDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;
import com.ruijing.store.order.rpc.client.UserClient;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/6/30 17:48
 * @description
 */
@MSharpService(isGateway = "true")
@RpcMapping("/uploadFile")
@RpcApi(value = "订单上传文件")
public class OrderUploadFileGWController {

    @Resource
    private OrderUploadFileService orderUploadFileService;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private UserClient userClient;


    
    @RpcMapping("/info")
    @RpcMethod("获取上传订单附件信息")
    public RemoteResponse<List<UploadFileInfoVO>> getUploadFileInfo(OrderFileInfoRequestDTO request) {
        List<UploadFileInfoVO> uploadFileInfoVOList = orderUploadFileService.getUploadFileInfoList(request);
        return RemoteResponse.<List<UploadFileInfoVO>>custom().setData(uploadFileInfoVOList).setSuccess();
    }

    @RpcMapping("/save")
    @RpcMethod("上传订单附件")
    public RemoteResponse<Boolean> saveUploadFileInfo(OrderUploadFileDataRequestDTO request) {
        Boolean uploadSuccess = orderUploadFileService.saveUploadFileInfo(request);
        if (Boolean.TRUE.equals(uploadSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("上传附件失败，请稍后重试或联系客服");
        }
    }

    @RpcMapping("/additionalAttachment")
    @RpcMethod("订单管理-追加验收附件")
    public RemoteResponse<Boolean> saveAdditionalAttachment(OrderFileUploadRequestDTO orderFileUploadRequestDTO) {
        Boolean uploadSuccess = orderUploadFileService.additionalAcceptanceAttachments(orderFileUploadRequestDTO);
        if (Boolean.TRUE.equals(uploadSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("上传附件失败，请稍后重试或联系客服");
        }
    }

    @RpcMapping("/additionalDetailAttachment")
    @RpcMethod("订单管理-追加验收详情关联附件")
    public RemoteResponse<Boolean> saveAdditionalDetailAttachment(RjSessionInfo rjSessionInfo, OrderDetailAcceptFileRequest request) {
        orderUploadFileService.appendSaveDetailAttachment(rjSessionInfo, request);
        return RemoteResponse.success();
    }

}
