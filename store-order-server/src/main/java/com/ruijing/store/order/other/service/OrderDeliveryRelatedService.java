package com.ruijing.store.order.other.service;

import com.ruijing.base.address.api.dto.LogisticsSubscribeDetailsDTO;
import com.ruijing.base.address.api.dto.LogisticsSubscribeInfoListDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.trade.api.dto.OrderDeliveryInfoDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderLogisticsInfoDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderLogisticsInfoVO;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.ServiceType;
import com.ruijing.store.order.rpc.client.AddressClient;
import com.ruijing.store.order.rpc.client.SuppClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2025-06-20 14:25
 * @description:
 */
@Service
public class OrderDeliveryRelatedService {

    @Resource
    private SuppClient suppClient;

    @Resource
    private AddressClient addressClient;

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取物流信息（物流100）")
    public OrderLogisticsInfoVO getOrderLogisticsInfo(Integer orderId){
        OrderLogisticsInfoVO orderLogisticsInfoVO = new OrderLogisticsInfoVO();
        orderLogisticsInfoVO.setOrderId(orderId);
        List<OrderDeliveryInfoDTO> deliveryInfoDTOList = suppClient.queryOrderDeliveryInfo(New.list(orderId));
        if(CollectionUtils.isEmpty(deliveryInfoDTOList) || deliveryInfoDTOList.get(0).getSubscribeId() == null){
            return orderLogisticsInfoVO;
        }
        Long subscribeId = deliveryInfoDTOList.get(0).getSubscribeId();
        orderLogisticsInfoVO.setSubscribeId(subscribeId);
        LogisticsSubscribeInfoListDTO logisticsSubscribeInfoListDTO = addressClient.getLogisticsSubscribeInfo(subscribeId);
        if(logisticsSubscribeInfoListDTO == null){
            return orderLogisticsInfoVO;
        }
        orderLogisticsInfoVO.setDeliveryNo(logisticsSubscribeInfoListDTO.getLogisticsNo());
        orderLogisticsInfoVO.setLogisticsCompany(logisticsSubscribeInfoListDTO.getLogisticsName());
        orderLogisticsInfoVO.setLogisticsCompanyLogoUrl(logisticsSubscribeInfoListDTO.getLogo());
        if(!Boolean.TRUE.equals(logisticsSubscribeInfoListDTO.getReceived())){
            // 已签收，则不返回预计送达时间
            orderLogisticsInfoVO.setArrivalTime(logisticsSubscribeInfoListDTO.getArrivalTime());
        }

        List<OrderLogisticsInfoDetailVO> logisticsDetails = New.list();
        if(CollectionUtils.isNotEmpty(logisticsSubscribeInfoListDTO.getDetailsList())){
            for(LogisticsSubscribeDetailsDTO detailItem : logisticsSubscribeInfoListDTO.getDetailsList()){
                OrderLogisticsInfoDetailVO voDetailItem = new OrderLogisticsInfoDetailVO()
                        .setLogisticsStatusName(detailItem.getLogisticsStatusName())
                        .setContext(detailItem.getContext())
                        .setLogisticsTime(detailItem.getLogisticsTime());
                logisticsDetails.add(voDetailItem);
            }
        }
        orderLogisticsInfoVO.setLogisticsDetails(logisticsDetails);

        return orderLogisticsInfoVO;
    }
}
