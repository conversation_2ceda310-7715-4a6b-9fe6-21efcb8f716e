package com.ruijing.store.order.other.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.other.service.ProductStockService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.SpotWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.SpotWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.enums.SpotWarehouseOptTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Description 商品库存处理service
 * @Date: 2024/06/20 15:20
 **/
@Service
public class ProductStockServiceImpl implements ProductStockService {

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ProductClient productClient;

    @Resource
    private BizShopProductServiceClient bizShopProductServiceClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private UserClient userClient;

    /**
     * 不返还库存的订单来源类型--临床采购单和竞价单
     */
    private final List<Integer> NOT_RETURN_STOCK_ORDER_TYPE = New.list(
            OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode(),
            OrderTypeEnum.BID_ORDER.getCode());


    /**
     * 返回库存
     * @param suppId
     * @param orderMasterDO
     */
    @Override
    public boolean addSku(Integer suppId, OrderMasterDO orderMasterDO) {
        if (NOT_RETURN_STOCK_ORDER_TYPE.contains(orderMasterDO.getOrderType())) {
            return true;
        }
        // 现货仓订单处理-取消订单更新库存
        if (isSpotWarehouseOrder(orderMasterDO.getId())) {
            return processSpotStockCancel(orderMasterDO);
        }
        // 查询订单详情
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        // 查询商品信息
        List<BaseProductDTO> returnProductList = orderDetailDOList.stream().map(orderDetailDO -> {
            BaseProductDTO baseProductDTO = new BaseProductDTO();
            baseProductDTO.setSupplierId(suppId);
            baseProductDTO.setId(orderDetailDO.getProductSn());
            baseProductDTO.setStock(orderDetailDO.getFquantity().intValue());
            return baseProductDTO;
        }).collect(toList());

        return addSkuCore(returnProductList, orderMasterDO);
    }

    @Override
    public boolean addSku(Integer suppId, List<GoodsReturnInfoDetailVO> completeReturnGoods, String returnNo, OrderMasterDO orderMasterDO) {
        if (NOT_RETURN_STOCK_ORDER_TYPE.contains(orderMasterDO.getOrderType())) {
            return true;
        }

        // 现货仓订单处理-退货生成入库单
        if (isSpotWarehouseOrder(orderMasterDO.getId())) {
            return processSpotStockReturn(completeReturnGoods, returnNo, orderMasterDO);
        }
        List<BaseProductDTO> returnProductList = completeReturnGoods.stream().map(item->{
            BaseProductDTO baseProductDTO = new BaseProductDTO();
            baseProductDTO.setSupplierId(suppId);
            baseProductDTO.setId(Long.decode(item.getProductId()));
            baseProductDTO.setStock(item.getQuantity().intValue());
            return baseProductDTO;
        }).collect(toList());
        return addSkuCore(returnProductList, orderMasterDO);
    }

    /**
     * 返还库存，
     * @param returnProductList 需要返还的商品
     * @return 是否成功
     */
    private boolean addSkuCore(List<BaseProductDTO> returnProductList, OrderMasterDO orderMasterDO) {
        // 已经删除的商品不返还，过滤一道
        List<Long> productIdList = returnProductList.stream().map(BaseProductDTO::getId).distinct().collect(toList());
        List<BaseProductDTO> productList = productClient.findByIdList(productIdList);
        Set<Long> productIdSet = productList.stream().map(BaseProductDTO::getId).collect(Collectors.toSet());
        returnProductList = returnProductList.stream().filter(item-> productIdSet.contains(item.getId())).collect(toList());
        //批量更新库存
        if (CollectionUtils.isNotEmpty(returnProductList)) {
            return bizShopProductServiceClient.batchUpdateStock(returnProductList);
        }
        return true;
    }


    /**
     * 现货仓取消订单处理-更新库存
     *
     * @param orderMasterDO 订单主表
     */
    private boolean processSpotStockCancel(OrderMasterDO orderMasterDO) {
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        return processSpotStock(orderDetailDOList, orderMasterDO, SpotWarehouseOptTypeEnum.STOCK, null);
    }


    /**
     * 现货仓退货订单处理-生成入库单
     *
     * @param completeReturnGoods 退货商品
     * @param orderMasterDO 订单主表
     */
    private boolean processSpotStockReturn(List<GoodsReturnInfoDetailVO> completeReturnGoods, String returnNo, OrderMasterDO orderMasterDO) {
        if (CollectionUtils.isEmpty(completeReturnGoods)) {
            return true;
        }
        List<Integer> orderDetailIdList = completeReturnGoods.stream().map(GoodsReturnInfoDetailVO::getDetailId).map(Integer::valueOf).collect(toList());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByIdIn(orderDetailIdList);
        // 根据详情ID分组退货信息
        Map<String, GoodsReturnInfoDetailVO> returnInfoByDetailIdMap = completeReturnGoods.stream().collect(Collectors.toMap(
                        GoodsReturnInfoDetailVO::getDetailId, vo -> vo, (existing, replacement) -> existing));
        // 填充退货数量、退货金额
        orderDetailDOList.forEach(orderDetail->{
            GoodsReturnInfoDetailVO returnInfoVO = returnInfoByDetailIdMap.get(String.valueOf(orderDetail.getId()));
            orderDetail.setFquantity(returnInfoVO.getQuantity());
            orderDetail.setReturnAmount(returnInfoVO.getAmount().doubleValue());
        });
        return processSpotStock(orderDetailDOList, orderMasterDO, SpotWarehouseOptTypeEnum.ENTRY, returnNo);
    }


    /**
     * 现货仓退货入库处理
     * 生成入库单或者更新库存
     * @param orderDetailDOList 订单商品详情
     * @param orderMasterDO 订单信息
     * @param optTypeEnum 操作类型 {@link SpotWarehouseOptTypeEnum}
     * @return 是否成功
     */
    private boolean processSpotStock(List<OrderDetailDO> orderDetailDOList, OrderMasterDO orderMasterDO, SpotWarehouseOptTypeEnum optTypeEnum, String returnNo) {
        Map<Integer, String> orderExtraMap = getOrderExtraMap(orderMasterDO.getId());
        // 根据详情id分组
        SpotWarehouseEntryDTO entryDTO = new SpotWarehouseEntryDTO();
        entryDTO.setOrgId(orderMasterDO.getFuserid());
        entryDTO.setOrderNo(orderMasterDO.getForderno());
        entryDTO.setRoomId(Integer.valueOf(orderExtraMap.get(OrderExtraEnum.STOCK_WAREHOUSE_ID.getValue())));
        entryDTO.setDeptId(orderMasterDO.getFbuydepartmentid());
        entryDTO.setDeptName(orderMasterDO.getFbuydepartment());
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        entryDTO.setUserName(StringUtils.defaultString(userInfo.getName()));
        entryDTO.setUserGuid(StringUtils.defaultString(userInfo.getGuid()));
        entryDTO.setReturnNo(returnNo);
        List<SpotWarehouseEntryDetailDTO> entryDetailDTOS = orderDetailDOList.stream().map(detailDO -> {
            SpotWarehouseEntryDetailDTO spotEntryDetail = new SpotWarehouseEntryDetailDTO();
            spotEntryDetail.setSuppId(orderMasterDO.getFsuppid());
            spotEntryDetail.setSuppName(orderMasterDO.getFsuppname());
            spotEntryDetail.setSpecifications(detailDO.getFspec());
            spotEntryDetail.setProductName(detailDO.getFgoodname());
            spotEntryDetail.setBrandName(detailDO.getFbrand());
            spotEntryDetail.setProductCode(detailDO.getFgoodcode());
            spotEntryDetail.setProductId(detailDO.getProductSn());
            spotEntryDetail.setDangerousType(detailDO.getDangerousTypeId());
            spotEntryDetail.setReceivedUnit(detailDO.getFunit());
            spotEntryDetail.setFpicpath(detailDO.getFpicpath());
            spotEntryDetail.setPrice(BigDecimal.valueOf(detailDO.getReturnAmount()));
            spotEntryDetail.setUnitPrice(detailDO.getFbidprice());
            spotEntryDetail.setOrderDetailId(detailDO.getId());
            spotEntryDetail.setOrderNo(orderMasterDO.getForderno());
            spotEntryDetail.setCategoryId(detailDO.getCategoryid());
            spotEntryDetail.setCasNo(detailDO.getCasno());
            spotEntryDetail.setRemainderPrice(detailDO.getRemainderPrice());
            spotEntryDetail.setReceivableNum(detailDO.getFquantity().intValue());
            spotEntryDetail.setReceivedNum(detailDO.getFquantity().intValue());
            spotEntryDetail.setControlFlag(detailDO.getRegulatoryTypeId());
            spotEntryDetail.setProductPlatformCode(detailDO.getProductCode());
            return spotEntryDetail;
        }).collect(toList());
        entryDTO.setDetailDTOList(entryDetailDTOS);
        entryDTO.setOptType(optTypeEnum.value);
        bizWareHouseClient.spotWarehouseEntry(entryDTO);
        return true;
    }


    /**
     * 判断是否是现货仓订单
     *
     * @param orderId 订单ID
     */
    private Boolean isSpotWarehouseOrder(Integer orderId) {
        Map<Integer, String> orderExtraMap = getOrderExtraMap(orderId);
        String spotWarehouseFlag = orderExtraMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
        return String.valueOf(StockTypeEnum.SPOT_WAREHOUSE_STOCK.getValue()).equals(spotWarehouseFlag);
    }


    /**
     * 获取订单额外信息
     *
     * @param orderId 退货单ID
     */
    private Map<Integer, String> getOrderExtraMap(Integer orderId) {
        List<Integer> extraKeyList = New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), OrderExtraEnum.STOCK_WAREHOUSE_ID.getValue());
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), extraKeyList);
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = com.ruijing.order.utils.DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(orderId);
        Map<Integer, String> extraKeyValueMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            extraKeyValueMap = com.ruijing.order.utils.DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        }
        return MapUtils.isNotEmpty(extraKeyValueMap) ? extraKeyValueMap : Collections.emptyMap();
    }

}
