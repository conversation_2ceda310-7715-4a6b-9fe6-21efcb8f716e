package com.ruijing.store.order.gateway.supplier.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderSplitUpRequestDTO;
import com.ruijing.store.order.gateway.supplier.service.SupplierOrderManageService;

import javax.annotation.Resource;

@MSharpService(isGateway = "true")
@RpcApi(value = "供应商端订单网关服务",description = "供应商端订单网关服务")
@RpcMapping("/supplierOrderManage")
public class SupplierOrderManageController {

    @Resource
    private SupplierOrderManageService supplierOrderManageService;

    @RpcMethod("订单拆分成子订单")
    @RpcMapping("/orderSplitUp")
    public RemoteResponse<Integer> orderSplitUp(RjSessionInfo rjSessionInfo, OrderSplitUpRequestDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.SPLITTING_FAILED_NO_PERMISSION);
        Preconditions.notNull(request, "拆分失败, 入参为空!");

        // 拆分订单逻辑
        int count = supplierOrderManageService.orderSplitUp(request, rjSessionInfo);
        return RemoteResponse.<Integer>custom().setSuccess().setData(count);
    }
}
