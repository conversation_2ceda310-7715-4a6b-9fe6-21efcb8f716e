package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.RoleDTO;
import com.ruijing.store.user.api.service.UserDepartmentRoleRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/4 10:02
 */
@ServiceClient
public class UserDepartmentRoleRpcServiceClient {

    private static final String CAT_TYPE = "UserDepartmentRoleRpcServiceClient";

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserDepartmentRoleRpcService userDepartmentRoleRpcService;

    @Resource
    private OrganizationClient organizationClient;

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户在课题组中是否有权限")
    public boolean findUserHasAccess(Integer orgId, Integer userId, Integer deptId, String accessCode) {
        Preconditions.isTrue(orgId != null && userId != null && deptId != null && StringUtils.isNotBlank(accessCode), "参数不完整，验证用户权限失败");
        RemoteResponse<Boolean> response = userDepartmentRoleRpcService.findUserHasAccess(orgId, userId, deptId, accessCode);
        if (response.isSuccess()) {
            return response.getData() == null ? false : response.getData();
        }
        Cat.logError(CAT_TYPE, "findUserHasAccess", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查找用户有权限的部门")
    public List<DepartmentDTO> findUserHasAccessDepartment(Integer orgId, Integer userId, String accessCode) {
        Preconditions.isTrue(orgId != null && userId != null  && StringUtils.isNotBlank(accessCode), "参数不完整，获取用户权限部门失败");
        RemoteResponse<List<DepartmentDTO>> response = userDepartmentRoleRpcService.findUserHasAccessDepartment(orgId, userId, accessCode);
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "findUserHasAccessDepartment", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查找用户有角色的所在部门(不包含其子部门)")
    public List<DepartmentDTO> findUserHasRoleDepartment(Integer orgId, Integer userId, Set<Integer> roleSet) {
        Preconditions.isTrue(orgId != null && userId != null  && CollectionUtils.isNotEmpty(roleSet), "参数不完整，获取用户权限部门失败");
        RemoteResponse<List<DepartmentDTO>> response = userDepartmentRoleRpcService.findUserHasRoleDepartment(orgId, userId, roleSet);
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "findUserHasAccessDepartment", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }


    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据医院id与用户id及部门id返回用户在部门中的角色集合(只在入参指定部门查找）")
    public List<RoleDTO> getRolesByUserIdAndDeptIdsInOrg(Integer orgId, Integer userId, List<Integer> departmentIds) {
        Preconditions.isTrue(orgId != null && userId != null  && CollectionUtils.isNotEmpty(departmentIds), "参数不完整，获取用户角色失败");
        RemoteResponse<List<RoleDTO>> response = userDepartmentRoleRpcService.getRolesByUserIdAndDeptIdsInOrg(orgId, userId, departmentIds);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取用户在根部门下的角色")
    public List<RoleDTO> getRootDeptRoles(Integer orgId, Integer userId) {
        List<OrganizationDTO> organizationDTOList = organizationClient.findByIdList(New.list(orgId));
        BusinessErrUtil.notEmpty(organizationDTOList, "organizationDTOList为空");
        OrganizationDTO organizationDTO = organizationDTOList.get(0);
        Integer rootDepartmentId = organizationDTO.getRootDepartmentId();
        Preconditions.isTrue(Objects.nonNull(orgId) && Objects.nonNull(userId) && Objects.nonNull(rootDepartmentId), "参数不完整，获取用户在根部门角色失败");
        return this.getRolesByUserIdAndDeptIdsInOrg(orgId, userId, New.list(rootDepartmentId));
    }
}
