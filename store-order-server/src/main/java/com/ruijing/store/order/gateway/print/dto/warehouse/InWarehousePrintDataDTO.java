package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;
import com.ruijing.store.warehouse.message.dto.WarehouseDockingDataDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28 17:50
 * @description
 */
public class InWarehousePrintDataDTO implements Serializable {

    private static final long serialVersionUID = -5084891042454309167L;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单流程种类 0:线上, 1:线下")
    private Integer orderSpecies;

    @RpcModelProperty(value = "入库单Id")
    private Integer warehouseApplicationId;

    @RpcModelProperty(value = "入库单号")
    private String warehouseApplicationNo;

    @RpcModelProperty(value = "入库单号对应条形码")
    private String entryNoBarcode;

    @RpcModelProperty(value = "申请时间")
    private Long warehouseApplicationTime;

    @RpcModelProperty(value = "申请人")
    private String warehouseApplicant;

    @RpcModelProperty(value = "库房Id")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "入库申请页面，用户填写的备注内容，即入库单的备注字段")
    private String remark;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称（待审批、审核通过、审核驳回）")
    private String approvalStatusName;

    @RpcModelProperty(value = "入库单状态（0：未入库 1：已入库）")
    private Integer status;

    @RpcModelProperty(value = "申请入库商品状态名称（未入库、已入库）")
    private String statusName;

    @RpcModelProperty(value = "入库单所有商品的总额的合计")
    private String totalPrice;

    @RpcModelProperty(value = "合计金额的大写")
    private String totalPriceInChinese;

    @RpcModelProperty(value = "入库日期，时间戳格式")
    private Long inWarehouseTime;

    @RpcModelProperty(value = "入库审核人姓名")
    private String approverName;

    @RpcModelProperty(value = "入库审核时间")
    private String approvalTimeString;

    @RpcModelProperty(value = "入库审核时间")
    private Long approvalTime;

    @RpcModelProperty(value = "入库申请图片")
    private List<String> inWarehousePictureUrlList;

    @RpcModelProperty(value = "入库申请单关联商品信息列表")
    private List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList;

    @RpcModelProperty(value = "入库方式")
    private Integer businessType;

    @RpcModelProperty("入库对接推送数据")
    private WarehouseDockingDataDTO dockingInfo;

    @RpcModelProperty("入库日志--时间最新的审批日志")
    private ApprovalLogFlatListDTO wareHouseApprovalFlatLog;

    @RpcModelProperty("采购/竞价审批日志")
    private List<OrderPrintApprovalLogDTO> wareHouseApprovalLog;

    @RpcModelProperty("试剂类商品总金额")
    private String reagentTotalPrice;

    @RpcModelProperty("耗材、动物类商品总金额")
    private String consumablesAndAnimalTotalPrice;

    @RpcModelProperty(value = "入库单商品数量总和")
    private Integer totalProductQuantity;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public String getEntryNoBarcode() {
        return entryNoBarcode;
    }

    public void setEntryNoBarcode(String entryNoBarcode) {
        this.entryNoBarcode = entryNoBarcode;
    }

    public Long getWarehouseApplicationTime() {
        return warehouseApplicationTime;
    }

    public void setWarehouseApplicationTime(Long warehouseApplicationTime) {
        this.warehouseApplicationTime = warehouseApplicationTime;
    }

    public String getWarehouseApplicant() {
        return warehouseApplicant;
    }

    public void setWarehouseApplicant(String warehouseApplicant) {
        this.warehouseApplicant = warehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }


    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public void setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
    }

    public Long getInWarehouseTime() {
        return inWarehouseTime;
    }

    public void setInWarehouseTime(Long inWarehouseTime) {
        this.inWarehouseTime = inWarehouseTime;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApprovalTimeString() {
        return approvalTimeString;
    }

    public void setApprovalTimeString(String approvalTimeString) {
        this.approvalTimeString = approvalTimeString;
    }

    public Long getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Long approvalTime) {
        this.approvalTime = approvalTime;
    }

    public List<String> getInWarehousePictureUrlList() {
        return inWarehousePictureUrlList;
    }

    public void setInWarehousePictureUrlList(List<String> inWarehousePictureUrlList) {
        this.inWarehousePictureUrlList = inWarehousePictureUrlList;
    }

    public List<WarehouseProductPrintDataDTO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public void setWarehouseProductInfoVOList(List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public WarehouseDockingDataDTO getDockingInfo() {
        return dockingInfo;
    }

    public void setDockingInfo(WarehouseDockingDataDTO dockingInfo) {
        this.dockingInfo = dockingInfo;
    }

    public ApprovalLogFlatListDTO getWareHouseApprovalFlatLog() {
        return wareHouseApprovalFlatLog;
    }

    public InWarehousePrintDataDTO setWareHouseApprovalFlatLog(ApprovalLogFlatListDTO wareHouseApprovalFlatLog) {
        this.wareHouseApprovalFlatLog = wareHouseApprovalFlatLog;
        return this;
    }

    public List<OrderPrintApprovalLogDTO> getWareHouseApprovalLog() {
        return wareHouseApprovalLog;
    }

    public InWarehousePrintDataDTO setWareHouseApprovalLog(List<OrderPrintApprovalLogDTO> wareHouseApprovalLog) {
        this.wareHouseApprovalLog = wareHouseApprovalLog;
        return this;
    }

    public String getReagentTotalPrice() {
        return reagentTotalPrice;
    }

    public InWarehousePrintDataDTO setReagentTotalPrice(String reagentTotalPrice) {
        this.reagentTotalPrice = reagentTotalPrice;
        return this;
    }

    public String getConsumablesAndAnimalTotalPrice() {
        return consumablesAndAnimalTotalPrice;
    }

    public InWarehousePrintDataDTO setConsumablesAndAnimalTotalPrice(String consumablesAndAnimalTotalPrice) {
        this.consumablesAndAnimalTotalPrice = consumablesAndAnimalTotalPrice;
        return this;
    }

    public Integer getTotalProductQuantity() {
        return totalProductQuantity;
    }

    public InWarehousePrintDataDTO setTotalProductQuantity(Integer totalProductQuantity) {
        this.totalProductQuantity = totalProductQuantity;
        return this;
    }

    @Override
    public String toString() {
        return "InWarehousePrintDataDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", orderSpecies=" + orderSpecies +
                ", warehouseApplicationId=" + warehouseApplicationId +
                ", warehouseApplicationNo='" + warehouseApplicationNo + '\'' +
                ", entryNoBarcode='" + entryNoBarcode + '\'' +
                ", warehouseApplicationTime=" + warehouseApplicationTime +
                ", warehouseApplicant='" + warehouseApplicant + '\'' +
                ", warehouseId=" + warehouseId +
                ", warehouseName='" + warehouseName + '\'' +
                ", remark='" + remark + '\'' +
                ", approvalStatus=" + approvalStatus +
                ", approvalStatusName='" + approvalStatusName + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", totalPrice='" + totalPrice + '\'' +
                ", totalPriceInChinese='" + totalPriceInChinese + '\'' +
                ", inWarehouseTime=" + inWarehouseTime +
                ", approverName='" + approverName + '\'' +
                ", approvalTimeString='" + approvalTimeString + '\'' +
                ", approvalTime=" + approvalTime +
                ", inWarehousePictureUrlList=" + inWarehousePictureUrlList +
                ", warehouseProductInfoVOList=" + warehouseProductInfoVOList +
                ", businessType=" + businessType +
                ", dockingInfo=" + dockingInfo +
                ", wareHouseApprovalFlatLog=" + wareHouseApprovalFlatLog +
                ", wareHouseApprovalLog=" + wareHouseApprovalLog +
                ", reagentTotalPrice='" + reagentTotalPrice + '\'' +
                ", consumablesAndAnimalTotalPrice='" + consumablesAndAnimalTotalPrice + '\'' +
                ", totalProductQuantity=" + totalProductQuantity +
                '}';
    }
}
