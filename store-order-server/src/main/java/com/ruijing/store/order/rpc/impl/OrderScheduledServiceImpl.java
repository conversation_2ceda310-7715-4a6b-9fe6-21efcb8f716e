package com.ruijing.store.order.rpc.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.reagent.auth.api.pojo.dto.UserDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.scheduled.service.OrderScheduledService;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnLogTranslator;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import com.ruijing.store.order.base.freezedeptlog.enums.IsDeletedEnum;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.order.util.TimeUtil;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 订单超时相关业务
 * @author: zhongyulei
 * @create: 2019/10/21 11:39'
 **/
@ServiceLog
@MSharpService
public class OrderScheduledServiceImpl implements OrderScheduledService {

    private final static String CAT_TYPE = "OrderScheduledServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass().getSimpleName());

    /**
     * 不需要自动取消 的单位列表  中大，华农，广西肿瘤
     */
    private final static List<Integer> excludeAutoCancelOrgs = New.list(3, 28, 15, 62, 74,42);
    static {
        excludeAutoCancelOrgs.addAll(ZhongShanDaXueOrgEnum.getAllOrgIds());
    }

    @PearlValue(key = "cancelorder.unconfirm.overday", defaultValue = "45")
    public Long unconfirmCancelOverday;

    @PearlValue(key = "sendemail.unconfirm.overday", defaultValue = "40")
    public Long unconfirmSendEmailOverday;

    @PearlValue(key = "sendemail.unconfirm.overday2", defaultValue = "43")
    public Long unconfirmSendEmailOverday2;

    @PearlValue(key = "sendemail.applycancel.overday", defaultValue = "12")
    public Long applySendEmailOverday;

    @PearlValue(key = "cancelorder.applycancel.overday", defaultValue = "14")
    public Long applyCancelOrderOverday;

    @Resource
    private OrderMasterForScheduledService orderMasterForScheduledService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private TimeoutStatisticsService timeoutStatisticsService;

    @Resource
    private FreezeDeptLogService freezeDeptLogService;

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private UserSyncClient userSyncClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Resource
    private GoodsReturnService goodsReturnService;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private CommonGoodsReturnService commonGoodsReturnService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private ResearchCustomClient researchCustomClient;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private OrderStatementService orderStatementService;

    /**
     * 订单自动验收
     * @return
     */
    @Override
    @ServiceLog(description = "订单自动验收", operationType = OperationType.WRITE)
    public RemoteResponse autoReceiptOrder() {
        try {
            orderAcceptService.batchAutoAcceptAllOrder();
            return RemoteResponse.custom().setSuccess().build();
        } catch (Exception e) {
            logger.error("自动验收异常",e);
            Cat.logError(CAT_TYPE,"autoReceiptOrder","自动验搜异常",e);
            return RemoteResponse.custom().setFailure("自动验搜异常").build();
        }
    }

    @Override
    @ServiceLog(description = "订单超时统计", operationType = OperationType.WRITE)
    public RemoteResponse orderTimeOutStatistics() {
        // 先清表,删除旧统计数据
        timeoutStatisticsService.deleteAll();
        // 查找所有的机构
        List<OrganizationDTO> organizationDTOList = null;
        try {
            organizationDTOList = userClient.getAllOrg();
        } catch (CallRpcException e) {
            Cat.logError(CAT_TYPE, "", "查询医院配置异常！", e);
            logger.error("查询医院配置异常！{}", e);
            return RemoteResponse.custom().setFailure(e.getMessage()).build();
        }
        // 获取id
        Set<Integer> ids = organizationDTOList.stream().map(OrganizationDTO::getId).collect(Collectors.toSet());
        Assert.notEmpty(ids);
        // 超时配置相关编码
        List<String> timeoutCode = Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(Collectors.toList());
        // 顺带查一下是否入库
        timeoutCode.add(ConfigConstant.USE_WAREHOUSE_SYSTEM);
        // 查询用户/机构的有效配置
        Map<String, Map<String, BaseConfigDTO>> timeOutCodeMap = sysConfigClient.findByCodes(timeoutCode);
        Map<Integer, Boolean> orgIdUseWarehouseMap = new HashMap<>(timeOutCodeMap.size());
        List<OrganizationConfigDTO> timeOutConfigs = new ArrayList<>(timeOutCodeMap.size());
        timeOutCodeMap.forEach((orgCode, orgCodeConfigMap) -> {
            if (StringUtils.isBlank(orgCode)) {
                return;
            }
            orgCodeConfigMap.forEach((configCode, baseConfigDTO) -> {
                if(ConfigConstant.USE_WAREHOUSE_SYSTEM.equals(baseConfigDTO.getConfigCode())){
                    // 是否使用库房系统
                    orgIdUseWarehouseMap.put(baseConfigDTO.getOrgId(), ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE.equals(baseConfigDTO.getConfigValue()));
                    return;
                }
                OrganizationConfigDTO organizationConfigDTO = new OrganizationConfigDTO();
                organizationConfigDTO.setOrgId(baseConfigDTO.getOrgId());
                organizationConfigDTO.setConfigCode(baseConfigDTO.getConfigCode());
                organizationConfigDTO.setConfigValue(baseConfigDTO.getConfigValue());
                // 用户填的值很大，超过long的最大值都是可能的，如果转换int失败，用int max值就好
                organizationConfigDTO.setConfigIntValue(NumberUtils.toInt(baseConfigDTO.getConfigValue(), Integer.MAX_VALUE));

                timeOutConfigs.add(organizationConfigDTO);
            });
        });

        // 获取用户订单超时配置项
        List<OrganizationConfigDTO> allBalanceDays = getConfigByType(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS, timeOutConfigs, ids);
        List<OrganizationConfigDTO> allBalanceAmount = getConfigByType(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT, timeOutConfigs, ids);
        List<OrganizationConfigDTO> allExamineDays = getConfigByType(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS, timeOutConfigs, ids);
        List<OrganizationConfigDTO> allExamineAmount = getConfigByType(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT, timeOutConfigs, ids);

        // 根据配置信息查询超时结算订单
        List<OrderTimeOutDTO> timeOutBalanceOrders = orderMasterForScheduledService.findTimeOutBalance(allBalanceDays,
                Arrays.asList(OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.OrderReceiveApproval.getValue())).stream().filter(orderTimeOutDTO -> {
            // 如果为南方医科大学 订单验收超时需求定制，订单结算方式为“课题组自结算”时，不计入超时设置统计
            if(OrgEnum.NAN_FANG_YI_KE.getValue() == orderTimeOutDTO.getFuserId() && OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderTimeOutDTO.getFundStatus())){
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        // 超时结算（超时验收审批部分或超时入库部分）
        List<OrderTimeOutDTO> timeOutApproveOrWarehouseOrders = timeOutBalanceOrders.stream()
                .filter(orderTimeOutDTO -> OrderCommonUtils.isNotAcceptApproveOrWareHouse(orderTimeOutDTO.getStatus(), orderTimeOutDTO.getInventoryStatus(), orgIdUseWarehouseMap.getOrDefault(orderTimeOutDTO.getFuserId(), Boolean.FALSE)))
                .collect(Collectors.toList());
        // 超时验收订单
        List<OrderTimeOutDTO> timeOutExamineOrders = orderMasterForScheduledService.findTimeOutExamine(allExamineDays,
                Arrays.asList(OrderStatusEnum.WaitingForReceive.getValue())).stream().filter(orderTimeOutDTO -> {
            // 如果为南方医科大学 订单验收超时需求定制，订单结算方式为“课题组自结算”时，不计入超时设置统计
            if(OrgEnum.NAN_FANG_YI_KE.getValue() == orderTimeOutDTO.getFuserId() && OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderTimeOutDTO.getFundStatus())){
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        // 超时结算订单
        List<Integer> timeOutBalanceOrderIdList = timeOutBalanceOrders.stream().map(OrderTimeOutDTO::getId).collect(Collectors.toList());
        List<Integer> timeOutBalanceOrderIds = new ArrayList<>(timeOutBalanceOrderIdList);
        // 超时验收
        List<Integer> timeOutExamineOrderIdList = timeOutExamineOrders.stream().map(OrderTimeOutDTO::getId).collect(Collectors.toList());
        timeOutBalanceOrderIdList.addAll(timeOutExamineOrderIdList);
        // 获取退货中的订单id(fmasterid)
        Set<Integer> returningOrderIds = orderDetailRelatedService.findFmasterIdByReturnStatus(timeOutBalanceOrderIdList,
                Arrays.asList(
                OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode(),
                OrderDetailReturnStatus.AGREETORETURN.getCode(),
                OrderDetailReturnStatus.REFUSEDTORETURN.getCode(),
                OrderDetailReturnStatus.RETURNEDGOODS.getCode()
                )
        );
        // 更新待结算单的超时标签
        this.asyncUpdateWaitStatementTimeoutTag(timeOutBalanceOrderIds, returningOrderIds);

        // 采购部门订单量统计结果 <fuserid, fbuydepartmentid, ReturningAmount>
        Table<Integer, Integer, Integer> balanceTable = getTotalByExcludeReturning(timeOutBalanceOrders, returningOrderIds);
        Table<Integer, Integer, Integer> approveOrWarehouseTable = getTotalByExcludeReturning(timeOutApproveOrWarehouseOrders, returningOrderIds);
        Table<Integer, Integer, Integer> examineTable = getTotalByExcludeReturning(timeOutExamineOrders, returningOrderIds);
        // 根据配置张数?计算最终超时统计结果
        List<TimeoutStatisticsDTO> balanceStatisticsList = calculateTimeoutStatistics(TimeOutBusinessType.BALANCE, allBalanceDays, allBalanceAmount, balanceTable);
        List<TimeoutStatisticsDTO> approveOrWarehouseStatisticsList = calculateTimeoutStatistics(TimeOutBusinessType.ACCEPT_APPROVE_OR_WAREHOUSE, allBalanceDays, allBalanceAmount, approveOrWarehouseTable);
        List<TimeoutStatisticsDTO> examineStatisticsList = calculateTimeoutStatistics(TimeOutBusinessType.ACCEPTANCE, allExamineDays, allExamineAmount, examineTable);

        // 将最终结果插入到 t_timeout_statistics
        int total = timeoutStatisticsService.insertBatch(balanceStatisticsList)
                + timeoutStatisticsService.insertBatch(approveOrWarehouseStatisticsList)
                + timeoutStatisticsService.insertBatch(examineStatisticsList);
        return RemoteResponse.custom().setData(total).setSuccess().build();
    }

    /**
     * 更新待结算中间表的超时标签为超时
     * @param timeOutBalanceOrderIds    超时订单id
     * @param returningOrderIds         退货中的订单id
     */
    private void asyncUpdateWaitStatementTimeoutTag(List<Integer> timeOutBalanceOrderIds, Set<Integer> returningOrderIds) {
        AsyncExecutor.listenableRunAsync(() -> {
            // 更新超时结算订单状态为1已超时(0未超时，1超时)  --> 排除掉退货中的订单
            List<Integer> timeOutBalanceInformIds = timeOutBalanceOrderIds.stream().filter(id -> !returningOrderIds.contains(id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(timeOutBalanceInformIds)) {
                // 调用结算服务更新结算状态为1超时，服务提供者没提供枚举
                int timeOutStatus = 1;
                statementPlatformClient.updateWaitingStatementTimeStatusOrders(timeOutBalanceInformIds, timeOutStatus);
            }
        }).addFailureCallback(throwable -> {
            logger.error(throwable.toString());
            Cat.logError(CAT_TYPE, "asyncUpdateWaitStatementTimeoutTag", "异常:", throwable);
        });

    }

    @Override
    @ServiceLog
    public RemoteResponse departmentTimeOutFreeze() {
        List<FreezeDeptLogDTO> listFreezeDeptLog = freezeDeptLogService.findAllByIsDelete(IsDeletedEnum.NOT_DELETED.getCode());
        List<TimeoutStatisticsDTO> listTimeOutStatistics = timeoutStatisticsService.findAll();

        // 旧的冻结数据
        Set<FreezeDeptLogDTO> freezeSet = listFreezeDeptLog.stream().collect(Collectors.toSet());
        // 新的统计表
        Set<TimeoutStatisticsDTO> timeOutSet = listTimeOutStatistics.stream().collect(Collectors.toSet());
        // 筛选新的冻结课题组, 筛选出已不在统计数据中的旧课题组解冻
        for (FreezeDeptLogDTO freeze : listFreezeDeptLog) {
            for (TimeoutStatisticsDTO timeOut : listTimeOutStatistics) {
                if (timeOut.getOrgId().equals(freeze.getOrgId()) &&
                        timeOut.getDepId().equals(freeze.getDepId()) &&
                        timeOut.getType().equals(freeze.getType())) {
                    freezeSet.remove(freeze);
                    timeOutSet.remove(timeOut);
                }
            }
        }

        // 新的超时冻结数据
        List<FreezeDeptLogDTO> newFreezeDepts = new ArrayList<>(timeOutSet.size());
        for (TimeoutStatisticsDTO timeOutDTO : timeOutSet) {
            newFreezeDepts.add(new FreezeDeptLogDTO(timeOutDTO.getOrgId(), timeOutDTO.getDepId(), timeOutDTO.getType(), IsDeletedEnum.NOT_DELETED.getCode(), new Date()));
        }
        // 解冻旧的数据
        List<Long> listDeletedIds = freezeSet.stream().map(FreezeDeptLogDTO::getId).collect(Collectors.toList());
        freezeDeptLogService.insertList(newFreezeDepts);
        freezeDeptLogService.updateDeleted(listDeletedIds);
        return RemoteResponse.custom().setSuccess().setSuccess().build();
    }

    /**
     * 采购部门订单量统计结果 <fuserid, fbuydepartmentid, ReturningAmount>
     * @param timeOutOrders      超时的订单
     * @param returningOrderIds  退货中的订单
     * @return
     */
    private Table<Integer, Integer, Integer> getTotalByExcludeReturning(List<OrderTimeOutDTO> timeOutOrders, Set<Integer> returningOrderIds) {
        Table<Integer, Integer, Integer> statisticsTable = HashBasedTable.create();
        for (OrderTimeOutDTO timeOutDTO : timeOutOrders) {
            // 统计不包含退货商品的订单, 如果订单中包含退货中的商品, 统计结果过滤此条记录
            if (!returningOrderIds.contains(timeOutDTO.getId())) {
                Integer amount = statisticsTable.get(timeOutDTO.getFuserId(), timeOutDTO.getFbuyDepartmentId());
                if (amount == null) {
                    amount = 0;
                }
                statisticsTable.put(timeOutDTO.getFuserId(), timeOutDTO.getFbuyDepartmentId(), ++amount);
            }
        }
        return statisticsTable;
    }

    /**
     * 通过超时策略获取用户配置, 给没配置信息的用户机构添加默认值
     * @param configType        配置策略
     * @param timeOutConfigs    已预设的配置
     * @param ids               机构id, 全集
     * @return
     */
    private List<OrganizationConfigDTO> getConfigByType(TimeOutConfigType configType, List<OrganizationConfigDTO> timeOutConfigs, Set<Integer> ids) {
        Set<Integer> missingBalanceDays = new HashSet<>(ids);
        List<OrganizationConfigDTO> allTimeOutConfig = new ArrayList<>(ids.size());
        // 查找缺失的配置差集
        for (OrganizationConfigDTO config : timeOutConfigs) {
            if (configType.getCode().equals(config.getConfigCode())) {
                allTimeOutConfig.add(config);
                missingBalanceDays.remove(config.getOrgId());
            }
        }

        // 为缺失的超时配置添加默认值
        for (Integer miss : missingBalanceDays) {
            allTimeOutConfig.add(new OrganizationConfigDTO(miss, configType.getDefaultSet()));
        }
        return allTimeOutConfig;
    }

    /**
     * 通过配置张数?计算统计结果
     * @param allTimeOutDaysConfigs   超时天数
     * @param alltimeOutAmountConfigs 超时张数
     * @param statisticsTable      超时订单统计表
     * @return
     */
    private List<TimeoutStatisticsDTO> calculateTimeoutStatistics(TimeOutBusinessType businessType,
                                                                 List<OrganizationConfigDTO> allTimeOutDaysConfigs,
                                                                 List<OrganizationConfigDTO> alltimeOutAmountConfigs,
                                                                 Table<Integer, Integer, Integer> statisticsTable) {
        // 用户超时天数的字典映射
        Map<Integer, Integer> daysMap = new HashMap<>(allTimeOutDaysConfigs.size());
        for (OrganizationConfigDTO daysConfig : allTimeOutDaysConfigs) {
            daysMap.put(daysConfig.getOrgId(), daysConfig.getConfigIntValue());
        }

        // 用户超时张数的字典映射
        Map<Integer, Integer> amountMap = new HashMap<>(alltimeOutAmountConfigs.size());
        for (OrganizationConfigDTO amountConfig : alltimeOutAmountConfigs) {
            amountMap.put(amountConfig.getOrgId(), amountConfig.getConfigIntValue());
        }

        // 筛选最终统计数据
        List<TimeoutStatisticsDTO> timeoutStatisticsList = new ArrayList<>();
        for (Integer fuserid : statisticsTable.rowKeySet()) {
            // 配置的天数
            Integer daysConfig = daysMap.get(fuserid);
            // 配置的张数?
            Integer amountConfig = amountMap.get(fuserid);
            if (amountConfig != null && daysConfig != null) {
                Map<Integer, Integer> fBuyDepartmentIdValueMap = statisticsTable.row(fuserid);
                for (Map.Entry<Integer, Integer> entry : fBuyDepartmentIdValueMap.entrySet()) {
                    // 如果统计结果大于等于配置张数, 添加到最终统计结果
                    if (entry.getValue() >= amountConfig) {
                        timeoutStatisticsList.add(new TimeoutStatisticsDTO(
                                fuserid,
                                entry.getKey(),
                                businessType.getValue(),
                                entry.getValue(),
                                new Date(),
                                daysConfig,
                                amountConfig

                        ));
                    }
                }
            }
        }
        return timeoutStatisticsList;
    }

    @Override
    public RemoteResponse departmentFreezeNotice() {
        // 1.查找已冻结的课题组(当天的)
        List<FreezeDeptLogDTO> freezeDeptLogDTOList = freezeDeptLogService.findByBetweenCreateDate(TimeUtil.getTodayMinTime(), TimeUtil.getTodayMaxTime(), IsDeletedEnum.NOT_DELETED.getCode());
        if (CollectionUtils.isEmpty(freezeDeptLogDTOList)) {
            logger.warn("无课题组需要冻结");
            return RemoteResponse.custom().setFailure("无课题组需要冻结").build();
        }

        // 2.获取课题组超时配置
        Map<String, Map<String, BaseConfigDTO>> timeOutConifgMap = null;
        try {
            timeOutConifgMap = sysConfigClient.findByCodes(Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(Collectors.toList()));
        } catch (CallRpcException e) {
            Cat.logError(CAT_TYPE, "departmentFreezeNotice", "获取配置信息失败！", e);
            logger.error("获取配置信息失败！{}", e.getMessage());
            return RemoteResponse.custom().setFailure("获取配置信息失败！").build();
        }

        Set<Integer> ids = freezeDeptLogDTOList.stream().map(FreezeDeptLogDTO::getOrgId).collect(Collectors.toSet());
        // 获取用户订单超时配置项
        List<OrganizationConfigDTO> allBalanceDays = getConfigByType(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS, Collections.emptyList(), ids);
        List<OrganizationConfigDTO> allExamineDays = getConfigByType(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS, Collections.emptyList(), ids);
        // 3.获取用户联系方式及相关订单,通过超时配置获取
        // 根据配置信息查询超时结算订单
        List<OrderTimeOutDTO> timeOutBalanceOrders = orderMasterForScheduledService.findTimeOutBalance(
                allBalanceDays,
                Arrays.asList(OrderStatusEnum.WaitingForStatement_1.getValue(),
                        OrderStatusEnum.OrderReceiveApproval.getValue()));
        // 超时验收订单
        List<OrderTimeOutDTO> timeOutExamineOrders = orderMasterForScheduledService.findTimeOutExamine(
                allExamineDays,
                Arrays.asList(OrderStatusEnum.WaitingForReceive.getValue()));

        // 还要剔除退货中的订单。。
        Map<TimeOutBusinessType, List<OrderTimeOutDTO>> overTimeOrderMap = removeReturningOrder(timeOutBalanceOrders, timeOutExamineOrders);

        // 4.异步调用消息发送服务
        // 订单根据课题组分组
        Map<Integer, List<OrderTimeOutDTO>> examineOrderMap = DictionaryUtils.groupBy(overTimeOrderMap.get(TimeOutBusinessType.ACCEPTANCE), OrderTimeOutDTO::getFbuyDepartmentId);
        Map<Integer, List<OrderTimeOutDTO>> balanceOrderMap = DictionaryUtils.groupBy(overTimeOrderMap.get(TimeOutBusinessType.BALANCE), OrderTimeOutDTO::getFbuyDepartmentId);
        List<Integer> departmentIdList = freezeDeptLogDTOList.stream().map(FreezeDeptLogDTO::getDepId).distinct().collect(Collectors.toList());

        // 冻结的课题组信息
        List<DepartmentDTO> freezeDepartmentList = Collections.emptyList();
        try {
            freezeDepartmentList = userClient.getDepartmentListByIds(departmentIdList);
        } catch (CallRpcException e) {
            Cat.logError(CAT_TYPE, "getDepartmentListByIds", "获取课题组信息失败！", e);
            logger.error("获取课题组信息失败！{}", e.getMessage());
            return RemoteResponse.custom().setFailure("获取课题组信息失败！").build();
        }

        // 获取所有超时的订单信息
        List<Integer> allOrderIds = ListUtils.toList(timeOutExamineOrders, OrderTimeOutDTO::getId);
        allOrderIds.addAll(ListUtils.toList(timeOutBalanceOrders, OrderTimeOutDTO::getId));
        List<OrderMasterDO> allTimeOutOrder = orderMasterMapper.findByIdInAndFbuydepartmentIdIn(allOrderIds, departmentIdList);

        // 依据课题组id分组weChat用户的map
        List<UserBaseInfoDTO> freezeDepartmentUserList = getFreezeDepartmentUser(allTimeOutOrder, freezeDepartmentList);
        Map<Integer, List<UserBaseInfoDTO>> departmentIdGuidUserBaseInfoMap = getUserInfoByDepartmentId(freezeDepartmentUserList, allTimeOutOrder);
        Map<Integer, List<UserDTO>> departmentIdGuidWeChatMap = getWeChatMapByDepartmentId(freezeDepartmentUserList, freezeDepartmentList);
        // 依据课题组id分组的课题组名map
        Map<Integer, DepartmentDTO> departmentNameMap = DictionaryUtils.toMap(freezeDepartmentList, DepartmentDTO::getId, Function.identity());

        freezeDeptLogDTOList.forEach(f -> {
            // 4.1 微信通知
            orderEmailHandler.sendWeChatToPurchaserCommon(departmentIdGuidWeChatMap.get(f.getDepId()), departmentNameMap.get(f.getDepId()));

            // 4.2 邮件通知
            List<UserBaseInfoDTO> noticeUserList = departmentIdGuidUserBaseInfoMap.get(f.getDepId());
            if (CollectionUtils.isEmpty(noticeUserList)) {
                return;
            }

            if (TimeOutBusinessType.ACCEPTANCE.getValue().equals(f.getType())) {
                orderEmailHandler.sendEmailDepartmentFreezeNoticeToPurchaser(TimeOutBusinessType.ACCEPTANCE, departmentNameMap.get(f.getDepId()), ListUtils.toList(noticeUserList, UserBaseInfoDTO::getEmail), examineOrderMap.get(f.getDepId()));
            } else {
                orderMessageHandler.sendEmailDepartmentFreezeNoticeToPurchaser(departmentNameMap.get(f.getDepId()), noticeUserList, balanceOrderMap.get(f.getDepId()));
            }
        });
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 自动取消订单
     *
     * @return
     */
    @Override
    @ServiceLog(description = "订单自动取消", operationType = OperationType.WRITE)
    public RemoteResponse autoCancelOrder() {

        //40天 、43天 未确认的订单 需要发送邮件 给供应商
        sendEmailForWaitingForConfirmScheduler();

        //订单生成45天，供应商未确认订单，则该订单自动取消，
        cancelOrderForWaitingForConfirmScheduler();

        //申请取消订单 12天后 对应的人未确认取消订单，则该订单自动取消邮件
        sendEmailForAgreeCancelOrderScheduler();

        //超时 未同意取消的订单， 自动取消
        agreeCancelOrderScheduler();
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 超时 未同意取消的订单， 自动取消
     */
    private void agreeCancelOrderScheduler() {
        final String CANCEL_MAN = "系统";
        final String CANCEL_MAN_ID = "-1";

        //供应商超时 未确认取消 采购人发起取消的订单
        List<OrderMasterDO> purchaseOrderList = getApplyCancelOrderList(applyCancelOrderOverday, OrderStatusEnum.PurchaseApplyToCancel.value);

        // 过滤出打开采购人邮件开关的订单
        List<OrderMasterDO> openBuyerEmailOrderList = getOpenEmailOrderList(purchaseOrderList);

        //供应商超时 未确认取消 采购人发起取消的订单给双方发送取消邮件、微信消息
        orderMessageHandler.sendPurchaseApplyCancelEmailToSupplier(purchaseOrderList, applyCancelOrderOverday);
        orderMessageHandler.sendPurchaseApplyCancelEmailToPurchase(openBuyerEmailOrderList, applyCancelOrderOverday);

        //采购人超时 未确认取消 供应商发起取消的订单
        List<OrderMasterDO> supplierOrderList = getApplyCancelOrderList(applyCancelOrderOverday, OrderStatusEnum.SupplierApplyToCancel.value);

        //采购人超时 未确认取消 供应商发起取消的订单给双方发送取消邮件、微信消息
        orderMessageHandler.sendSupplierApplyCancelEmailToSupplier(supplierOrderList, applyCancelOrderOverday);

        // TODO: 2022/5/20  采购人超时 未确认取消 供应商发起取消的订单给双方发送取消邮件
        // 过滤出打开采购人邮件开关的订单
        List<OrderMasterDO> openBuyerEmailOrderList2 = getOpenEmailOrderList(supplierOrderList);
        orderMessageHandler.sendSupplierApplyCancelEmailToPurchase(openBuyerEmailOrderList2, applyCancelOrderOverday);

        List<OrderMasterDO> resultList = New.listWithCapacity(purchaseOrderList.size() + supplierOrderList.size());
        resultList.addAll(purchaseOrderList);
        resultList.addAll(supplierOrderList);
        if(CollectionUtils.isEmpty(resultList)){
            return;
        }
        for (OrderMasterDO masterDO : resultList) {
            CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
            cancelOrderReqDTO.setOrderMasterId(masterDO.getId());
            cancelOrderReqDTO.setCancelMan(CANCEL_MAN);
            cancelOrderReqDTO.setCancelManId(CANCEL_MAN_ID);
            try {
                cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
            } catch (Exception e) {
                logger.error("{} 订单期自动取消异常",masterDO.getId(),e);
                Cat.logError(CAT_TYPE,"agreeCancelOrderScheduler",masterDO.getId() + "订单期自动取消异常",e);
            }
        }
        if(CollectionUtils.isNotEmpty(purchaseOrderList)) {
            // 供应商超时未确认取消订单，发送微信消息给供应商
            weChatMessageHandler.confirmCancelOverTimeToSupp(purchaseOrderList);
        }
    }

    /**
     * 获取申请取消状态的超时订单
     * @param overDay
     * @param orderStatus
     * @return
     */
    private List<OrderMasterDO> getApplyCancelOrderList(Long overDay, Integer orderStatus) {
        LocalDate nowDate = LocalDate.now();
        LocalDate cancelDate = nowDate.minusDays(overDay);
        String cancelDateStr = cancelDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String cancelDateUpper = cancelDateStr + " 23:59:59";
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setPageSize(10000);
        orderSearchParamDTO.setStatusList(New.list(orderStatus));
        FieldRangeDTO fieldRangeDTO = new FieldRangeDTO("fcanceldate",null,cancelDateUpper,false,true);
        orderSearchParamDTO.setFieldRangeList(New.list(fieldRangeDTO));
        orderSearchParamDTO.setExcludeOrgIdList(excludeAutoCancelOrgs);
        SearchPageResultDTO<OrderMasterSearchDTO> orderSearchList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> recordList = orderSearchList.getRecordList();
        List<Integer> orderIdList = recordList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        return CollectionUtils.isEmpty(orderMasterDOList) ? New.emptyList(): orderMasterDOList;
    }

    /**
     * 申请取消订单 12天后 对应的人未确认取消订单，则该订单自动取消邮件，
     */
    private void sendEmailForAgreeCancelOrderScheduler() {

        //采购人申请取消的订单 需要发邮件给供应商
        List<OrderMasterDO> purchaseCancelOrderList = getApplyCancelSendEmailOrderList(applySendEmailOverday, OrderStatusEnum.PurchaseApplyToCancel.value);
        orderMessageHandler.sendPurchaseApplyCancelWarnEmailToSupplier(purchaseCancelOrderList, applySendEmailOverday
                , applyCancelOrderOverday - applySendEmailOverday);

        //神奇邮件发送逻辑，供应商申请取消采购人未处理发邮件给供应商提醒供应商处理，暂时修正为发给采购人
        //采购人申请取消的订单 需要发邮件给供应商
        List<OrderMasterDO> supplierCancelOrderList = getApplyCancelSendEmailOrderList(applySendEmailOverday, OrderStatusEnum.SupplierApplyToCancel.value);

        // 过滤出打开采购人邮件开关的订单
        List<OrderMasterDO> openBuyerEmailOrderList = getOpenEmailOrderList(supplierCancelOrderList);
        orderMessageHandler.sendSupplierApplyCancelWarnEmailToBuyer(openBuyerEmailOrderList, applySendEmailOverday
                , applyCancelOrderOverday - applySendEmailOverday);

    }

    /**
     * 订单生成45天，供应商未确认订单，则该订单自动取消，
     */
    private void cancelOrderForWaitingForConfirmScheduler() {
        final String CANCEL_REASON = "供应商超时确认订单，系统自动取消订单";
        final String CANCEL_MAN = "系统";
        final String CANCEL_MAN_ID = "-1";
        List<OrderMasterDO> orderMasterDOS = getUnConfirmOrderList(unconfirmCancelOverday, OrderStatusEnum.WaitingForConfirm.value);
        if(CollectionUtils.isEmpty(orderMasterDOS)){
            return;
        }
        for (OrderMasterDO orderMasterDO : orderMasterDOS) {
            ApplyCancelOrderReqDTO applyCancelOrderReqDTO = new ApplyCancelOrderReqDTO();
            applyCancelOrderReqDTO.setFcancelreason(CANCEL_REASON);
            applyCancelOrderReqDTO.setFcancelmanid(CANCEL_MAN_ID);
            applyCancelOrderReqDTO.setFcancelman(CANCEL_MAN);
            applyCancelOrderReqDTO.setOrderId(orderMasterDO.getId());
            try {
                cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);
            } catch (Exception e) {
                logger.error("{}订单期自动取消异常",orderMasterDO.getId(),e);
                Cat.logError(CAT_TYPE,"cancelOrderForWaitingForConfirmScheduler", + applyCancelOrderReqDTO.getOrderId() + "订单期自动取消异常",e);
            }
        }
        // TODO: 2022/5/20  供应商未确认订单，则该订单自动取消
        orderMasterDOS = orderMasterMapper.findByIdIn(orderMasterDOS.stream().map(OrderMasterDO::getId).collect(Collectors.toSet()));
        // 过滤出打开采购人邮件开关的订单
        List<OrderMasterDO> openBuyerEmailOrderList = getOpenEmailOrderList(orderMasterDOS);
        orderMessageHandler.sendUnConfirmOrderCancelEmailToPurchase(openBuyerEmailOrderList, unconfirmCancelOverday);
        orderMessageHandler.sendUnConfirmOrderCancelEmailToSupplier(orderMasterDOS, unconfirmCancelOverday);
        weChatMessageHandler.confirmOverTimeToSupp(orderMasterDOS);
    }

    /**
     * 获取打开邮件开关的订单
     */
    private List<OrderMasterDO> getOpenEmailOrderList(List<OrderMasterDO> orderMasterDOList) {
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return New.emptyList();
        }
        List<Integer> userIdList = orderMasterDOList.stream()
                .map(OrderMasterDO::getFbuyerid)
                .distinct()
                .collect(Collectors.toList());
        List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByUserIds(userIdList);

        if (CollectionUtils.isEmpty(userBaseInfoDTOList)) {
            return New.emptyList();
        }

        // 用户id 对 guid的映射
        Map<Integer, String> userIdToGuidMap = New.mapWithCapacity(userBaseInfoDTOList.size());
        for (UserBaseInfoDTO user : userBaseInfoDTOList) {
            userIdToGuidMap.putIfAbsent(user.getId(), user.getGuid());
        }

        // 过滤出所有打开邮件开关的订单
        List<OrderMasterDO> openEmailOrders = orderMasterDOList.stream()
                .filter(order -> {
                    String guid = userIdToGuidMap.get(order.getFbuyerid());
                    return Objects.nonNull(guid) && orderMessageHandler.isBuyerEmailEnabled(guid, order.getFuserid(), SendingBusinessEnum.CANCEL_ORDER);
                })
                .collect(Collectors.toList());

        return openEmailOrders;
    }

    /**
     * 查询订单生成45天，供应商未确认订单
     */
    private List<OrderMasterDO> getUnConfirmOrderList(Long overDay, Integer orderStatus) {
        LocalDate nowDate = LocalDate.now();
        LocalDate orderDate = nowDate.minusDays(overDay);
        String orderDateStr = orderDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String orderDateUpper = orderDateStr + " 23:59:59";
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setPageSize(10000);
        orderSearchParamDTO.setStatusList(New.list(orderStatus));
        FieldRangeDTO fieldRangeDTO = new FieldRangeDTO("forderdate",null,orderDateUpper,false,true);
        orderSearchParamDTO.setFieldRangeList(New.list(fieldRangeDTO));
        orderSearchParamDTO.setExcludeOrgIdList(excludeAutoCancelOrgs);
        SearchPageResultDTO<OrderMasterSearchDTO> orderSearchList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> recordList = orderSearchList.getRecordList();
        List<Integer> orderIdList = recordList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        return CollectionUtils.isEmpty(orderMasterDOList) ? New.emptyList(): orderMasterDOList;
    }

    /**
     * 40天 、43天 未确认的订单 需要发送邮件 给供应商
     */
    private void sendEmailForWaitingForConfirmScheduler() {
        List<OrderMasterDO> sendEmailUnConfirmOrderList = getUnConfirmSendEmailOrderList(unconfirmSendEmailOverday, OrderStatusEnum.WaitingForConfirm.value);
        orderMessageHandler.sendUnConfirmOrderWarnEmailToSupplier(sendEmailUnConfirmOrderList, unconfirmSendEmailOverday
                , unconfirmCancelOverday - unconfirmSendEmailOverday);

        List<OrderMasterDO> sendEmailUnConfirmOrderList2 = getUnConfirmSendEmailOrderList(unconfirmSendEmailOverday2, OrderStatusEnum.WaitingForConfirm.value);
        logger.info("扫描供应商超{}未确认的订单，有{}条",unconfirmSendEmailOverday2,sendEmailUnConfirmOrderList2.size());
        orderMessageHandler.sendUnConfirmOrderWarnEmailToSupplier(sendEmailUnConfirmOrderList2, unconfirmSendEmailOverday2
                , unconfirmCancelOverday - unconfirmSendEmailOverday2);
    }

    /**
     * 需要 发邮件的 未确认定单
     *
     * @param overDay
     * @return
     */
    private List<OrderMasterDO> getUnConfirmSendEmailOrderList(Long overDay, Integer orderStatus) {
        LocalDate nowDate = LocalDate.now();
        LocalDate orderDate = nowDate.minusDays(overDay);
        String orderDateStr = orderDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String orderDateLower = orderDateStr + " 00:00:00";
        String orderDateUpper = orderDateStr + " 23:59:59";
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setPageSize(10000);
        orderSearchParamDTO.setStatusList(New.list(orderStatus));
        FieldRangeDTO fieldRangeDTO = new FieldRangeDTO("forderdate",orderDateLower,orderDateUpper,true,true);
        orderSearchParamDTO.setFieldRangeList(New.list(fieldRangeDTO));
        orderSearchParamDTO.setExcludeOrgIdList(excludeAutoCancelOrgs);
        SearchPageResultDTO<OrderMasterSearchDTO> orderSearchList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> recordList = orderSearchList.getRecordList();
        List<Integer> orderIdList = recordList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        return CollectionUtils.isEmpty(orderMasterDOList) ? New.emptyList(): orderMasterDOList;
    }

    /**
     * 需要 发邮件的 申请取消的订单
     *
     * @param overDay
     * @return
     */
    private List<OrderMasterDO> getApplyCancelSendEmailOrderList(Long overDay, Integer orderStatus) {
        LocalDate nowDate = LocalDate.now();
        LocalDate cancelDate = nowDate.minusDays(overDay);
        String cancelDateStr = cancelDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String cancelDateLower = cancelDateStr + " 00:00:00";
        String cancelDateUpper = cancelDateStr + " 23:59:59";
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setPageSize(10000);
        orderSearchParamDTO.setStatusList(New.list(orderStatus));
        FieldRangeDTO fieldRangeDTO = new FieldRangeDTO("fcanceldate",cancelDateLower,cancelDateUpper,true,true);
        orderSearchParamDTO.setFieldRangeList(New.list(fieldRangeDTO));
        orderSearchParamDTO.setExcludeOrgIdList(excludeAutoCancelOrgs);
        SearchPageResultDTO<OrderMasterSearchDTO> orderSearchList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> recordList = orderSearchList.getRecordList();
        List<Integer> orderIdList = recordList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        return CollectionUtils.isEmpty(orderMasterDOList) ? New.emptyList(): orderMasterDOList;
    }


    /**
     * 超时订单剔除退货中的订单
     * @param timeOutBalanceOrders
     * @param timeOutExamineOrders
     */
    private Map<TimeOutBusinessType, List<OrderTimeOutDTO>> removeReturningOrder(List<OrderTimeOutDTO> timeOutBalanceOrders, List<OrderTimeOutDTO> timeOutExamineOrders) {
        List<OrderTimeOutDTO> newBalanceOrders = null;
        List<OrderTimeOutDTO> newExamineOrders = null;

        List<Integer> timeOutBalanceOrderIdList = timeOutBalanceOrders.stream().map(OrderTimeOutDTO::getId).collect(Collectors.toList());
        List<Integer> timeOutExamineOrderIdList = timeOutExamineOrders.stream().map(OrderTimeOutDTO::getId).collect(Collectors.toList());
        timeOutBalanceOrderIdList.addAll(timeOutExamineOrderIdList);
        // 获取退货中的订单id(fmasterid)
        Set<Integer> returningOrderIds = orderDetailRelatedService.findFmasterIdByReturnStatus(timeOutBalanceOrderIdList,
                Arrays.asList(
                OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode(),
                OrderDetailReturnStatus.AGREETORETURN.getCode(),
                OrderDetailReturnStatus.REFUSEDTORETURN.getCode(),
                OrderDetailReturnStatus.RETURNEDGOODS.getCode()
                )
        );

        newBalanceOrders = timeOutBalanceOrders
                .stream()
                .filter(f -> !returningOrderIds.contains(f.getId()))
                .collect(Collectors.toList());

        newExamineOrders = timeOutExamineOrders
                .stream()
                .filter(f -> !returningOrderIds.contains(f.getId()))
                .collect(Collectors.toList());

        Map<TimeOutBusinessType, List<OrderTimeOutDTO>> overTimeOrderMap = new HashMap<>(2);
        overTimeOrderMap.put(TimeOutBusinessType.BALANCE, newBalanceOrders);
        overTimeOrderMap.put(TimeOutBusinessType.ACCEPTANCE, newExamineOrders);
        return overTimeOrderMap;
    }

    /**
     * 依据订单信息分组weChat用户
     * @param orderList
     * @param freezeDepartmentList
     * @return
     */
    private List<UserBaseInfoDTO> getFreezeDepartmentUser(List<OrderMasterDO> orderList, List<DepartmentDTO> freezeDepartmentList) {
        Map<Integer, Set<Integer>> orgIdUserIdMap = new HashMap<>(orderList.size());
        for (OrderMasterDO order : orderList) {
            if (orgIdUserIdMap.containsKey(order.getFuserid())) {
                orgIdUserIdMap.get(order.getFuserid()).add(order.getFbuyerid());
            } else {
                orgIdUserIdMap.put(order.getFuserid(), New.set(order.getFbuyerid()));
            }
        }

        // 课题组用户列表
        List<UserBaseInfoDTO> noticeUserList = userClient.getUserInfoByUserIdList(orgIdUserIdMap);
        if (CollectionUtils.isEmpty(noticeUserList)) {
            return Collections.emptyList();
        }
        return noticeUserList;
    }

    /**
     * 根据departmentId分组UserbaseInfo
     * @param noticeUserList
     * @param overTimeOrder
     * @return
     */
    private Map<Integer, List<UserBaseInfoDTO>> getUserInfoByDepartmentId(List<UserBaseInfoDTO> noticeUserList, List<OrderMasterDO> overTimeOrder) {
        if (CollectionUtils.isEmpty(noticeUserList)) {
            return MapUtils.EMPTY_MAP;
        }
        // 课题组所有联系人联系方式分组。根据 departmentId 对 User 分组
        Map<Integer, List<UserBaseInfoDTO>> departmentIdGuidWeChatMap = new HashMap<>(noticeUserList.size());
        // 一个用户可以属于多个课题组，分组 userId -> Set<DepartmentId>
        Map<Integer, Set<Integer>> userIdDepartmentIdMap = overTimeOrder.stream().collect(Collectors.groupingBy(OrderMasterDO::getFbuyerid, Collectors.mapping(OrderMasterDO::getFbuydepartmentid, Collectors.toSet())));

        noticeUserList.forEach(u -> {
            Set<Integer> departmentIdSet = userIdDepartmentIdMap.get(u.getId());
            if (departmentIdSet == null) {
                return;
            }

            departmentIdSet.forEach(departmentId -> {
                if (departmentIdGuidWeChatMap.containsKey(departmentId)) {
                    List<UserBaseInfoDTO> userInfoList = departmentIdGuidWeChatMap.get(departmentId);
                    userInfoList.add(u);
                    departmentIdGuidWeChatMap.put(departmentId, userInfoList);
                } else {
                    departmentIdGuidWeChatMap.put(departmentId, New.list(u));
                }
            });

        });

        return departmentIdGuidWeChatMap;
    }

    /**
     * 根据给departmentId微信用户分组
     * @param noticeUserList
     * @param freezeDepartmentList
     * @return
     */
    private Map<Integer, List<UserDTO>> getWeChatMapByDepartmentId(List<UserBaseInfoDTO> noticeUserList, List<DepartmentDTO> freezeDepartmentList) {
        if (CollectionUtils.isEmpty(noticeUserList)) {
            return MapUtils.EMPTY_MAP;
        }
        // 用户的guid集合
        List<String> userGuidList = ListUtils.toList(noticeUserList, UserBaseInfoDTO::getGuid);
        List<UserDTO> weChatUserList = Collections.emptyList();
        try {
            weChatUserList = userSyncClient.getUsersByGuid(userGuidList);
        } catch (CallRpcException e) {
            CatUtils.buildStackInfo("获取微信用户异常", e);
            logger.error("获取微信用户异常{}", e);
            throw new IllegalStateException("getUserInfoByDepartmentIds根据departmentId获取weChat用户信息异常");
        }

        if (CollectionUtils.isEmpty(weChatUserList)) {
            weChatUserList = new ArrayList<>();
        }
        // 课题组所有联系人联系方式分组。根据 departmentId 对 weChatUser 分组
        Map<Integer, List<UserDTO>> departmentIdGuidWeChatMap = new HashMap<>(freezeDepartmentList.size());
        Map<String, List<UserDTO>> guidGroup = weChatUserList
                .stream()
                .filter(f -> Objects.nonNull(f.getGuid()))
                .collect(Collectors.groupingBy(UserDTO::getGuid));

        freezeDepartmentList.forEach(department -> {
            if (guidGroup.containsKey(department.getGuid())) {
                departmentIdGuidWeChatMap.put(department.getId(), guidGroup.get(department.getGuid()));
            }
        });
        return departmentIdGuidWeChatMap;
    }

    @Override
    @ServiceLog(description = "退货单自动验收", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> returnAutoReceive() {
        // 1.查询 退货同意后，商品处于待验收状态时超过14天没有确认验收的退货单
        List<GoodsReturn> unFinishReturnSuccessList = goodsReturnService.getUnFinishReturnSuccess();

        // 2.查找对应机构，组装收货入参
        Map<Integer, OrganizationDTO> orgIdIdentityMap = this.getOrgIdIdentityMap(unFinishReturnSuccessList);

        // 3.查找对应的订单，组装收货入参
        Map<Integer, OrderMasterDO> orderIdIdentityMap = this.getOrderIdentityMap(unFinishReturnSuccessList);

        // 4.系统自动完成验收确认，并且标记 订单退货详情页面的退货说明操作内容应该提示：“订单超过14天未验收，系统自动验收”；
        for (GoodsReturn goodsReturn : unFinishReturnSuccessList) {
            OrganizationDTO organizationDTO = orgIdIdentityMap.get(goodsReturn.getOrgId());
            OrderMasterDO orderMasterDO = orderIdIdentityMap.get(goodsReturn.getOrderId());
            if (organizationDTO == null || orderMasterDO == null) {
                continue;
            }
            try {
                this.executeGoodsReturnSuccess(goodsReturn, organizationDTO, orderMasterDO);
            } catch (Exception e) {
                logger.error("定时退货收货失败，returnNo：{}", goodsReturn.getReturnNo());
            }
        }

        return RemoteResponse.<Integer>custom().setSuccess().setData(unFinishReturnSuccessList.size());
    }

    @Override
    public RemoteResponse<Boolean> autoCreateStatement() {
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.addSourceField("id");
        orderSearchParamDTO.setOrgIdList(New.list(OrgEnum.GUANG_ZHOU_SHI_YAN_SHI.getValue()));
        orderSearchParamDTO.setStatusList(New.list(OrderStatusEnum.WaitingForStatement_1.getValue()));
        orderSearchParamDTO.setStartHit(0);
        orderSearchParamDTO.setPageSize(500);
        SearchPageResultDTO<OrderMasterSearchDTO> pageResultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        if(CollectionUtils.isEmpty(pageResultDTO.getRecordList())){
            return RemoteResponse.success();
        }
        List<Integer> orderIdList = pageResultDTO.getRecordList().stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        for(Integer orderId : orderIdList){
            boolean inGoodReturnProcess = false;
            // 退货中的单据不自定发起结算
            List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderId(orderId);
            for(GoodsReturn goodsReturn : goodsReturns){
                boolean completeGoodReturn = GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus())
                        || GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(goodsReturn.getGoodsReturnStatus());
                if(!completeGoodReturn){
                    inGoodReturnProcess = true;
                    break;
                }
            }
            if(inGoodReturnProcess){
                continue;
            }
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
            try {
                orderStatementService.createStatement(orderMasterDO, DockingConstant.SYSTEM_OPERATOR_ID, DockingConstant.SYSTEM_OPERATOR_NAME, orderMasterDO.getInventoryStatus().intValue());
            } catch (Exception e){
                logger.error("订单id:{}发起结算失败", orderMasterDO.getId(), e);
            }
        }
        return RemoteResponse.success();
    }

    /**
     * 执行系统验收退货逻辑
     * @param goodsReturn
     * @param organizationDTO
     * @param orderMasterDO
     */
    private void executeGoodsReturnSuccess(GoodsReturn goodsReturn, OrganizationDTO organizationDTO, OrderMasterDO orderMasterDO) {
        // 5.回写退货信息，退货数量
        Supplier<Void> updatedFunction = () -> {
            GoodsReturn updateParam = new GoodsReturn();
            updateParam.setId(goodsReturn.getId());
            updateParam.setGoodsReturnStatus(GoodsReturnStatusEnum.SUCCESS.getCode());
            updateParam.setRemark("系统超时自动收货");
            goodsReturnMapper.updateByPrimaryKeySelective(updateParam);
            goodsReturn.setGoodsReturnStatus(GoodsReturnStatusEnum.SUCCESS.getCode());
            return null;
        };
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(organizationDTO.getCode())) {
            // 中大调中大解冻接口，他解冻完成后会回调我们的退货成功接口
            researchCustomClient.agree2ReturnOrderForSYSU(orderMasterDO.getId(), orderMasterDO.getFusercode());
            updatedFunction.get();
        } else {
            // 执行退货收货
            supplierGoodsReturnService.returnSuccess(goodsReturn, organizationDTO, orderMasterDO);
            updatedFunction.get();
            // 回写detail退货状态
            this.updateOrderDetailReturnStatus(goodsReturn);
        }

        // 6.插入操作日志
        commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(orderMasterDO.getFsuppid(), orderMasterDO.getFsuppname(), goodsReturn.getId(), GoodsReturnOperatorTypeEnum.SYSTEM_USER, GoodsReturnOperationTypeEnum.SYSTEM_RECEIVE_GOODS_RETURN, StringUtils.EMPTY, StringUtils.EMPTY)
        );
        // 推送到管理平台
        commonGoodsReturnService.pushReturnToThirdPlatform(goodsReturn, orderMasterDO);
    }

    private Map<Integer, OrganizationDTO> getOrgIdIdentityMap(List<GoodsReturn> unFinishReturnSuccessList) {
        List<Integer> orgIdList = ListUtils.toList(unFinishReturnSuccessList, GoodsReturn::getOrgId);
        List<OrganizationDTO> byOrgIdList = organizationClient.findByIdList(orgIdList);
        BusinessErrUtil.notEmpty(byOrgIdList, ExecptionMessageEnum.AUTO_RECEIPT_EXCEPTION_NO_INSTITUTION);
        return DictionaryUtils.toMap(byOrgIdList, OrganizationDTO::getId, Function.identity());
    }

    private Map<Integer, OrderMasterDO> getOrderIdentityMap(List<GoodsReturn> unFinishReturnSuccessList) {
        List<Integer> orderIdList = ListUtils.toList(unFinishReturnSuccessList, GoodsReturn::getOrderId);
        List<OrderMasterDO> orderByIdIn = orderMasterMapper.findByIdIn(orderIdList);
        BusinessErrUtil.notEmpty(orderByIdIn, ExecptionMessageEnum.AUTO_RECEIPT_EXCEPTION_NO_ORDER);
        return DictionaryUtils.toMap(orderByIdIn, OrderMasterDO::getId, Function.identity());
    }

    /**
     * 更新order_detail退货状态
     * @param goodsReturn
     */
    private void updateOrderDetailReturnStatus(GoodsReturn goodsReturn) {
        // 查询已存在的退货单信息
        List<GoodsReturn> existGoodsReturnList = goodsReturnMapper.findByOrderId(goodsReturn.getOrderId());
        List<OrderDetailDO> detailList = orderDetailMapper.findByFmasterid(goodsReturn.getOrderId());
        // 获取当前最优先展示的退货状态的map
        Map<Integer, Integer> detailIdReturnStatusMap = commonGoodsReturnService.getFirstReturnStatusMapByReturn(existGoodsReturnList);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailList)) {
            String goodsReturnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);

            Map<String, List<GoodsReturnInfoDetailVO>> detailIdGoodsReturnVOMap = goodsReturnInfoDetailVOList.stream().collect(Collectors.groupingBy(GoodsReturnInfoDetailVO::getDetailId));
            for (OrderDetailDO detailDO : detailList) {
                List<GoodsReturnInfoDetailVO> returnListItem = detailIdGoodsReturnVOMap.get(detailDO.getId().toString());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(returnListItem)) {
                    continue;
                }
                // 更新减去订单明细里的退货数量
                // 状态显示优先级 待确认0->同意退货1->拒绝退货2->采购人已退货4->已完成退货5
                detailDO.setReturnStatus(detailIdReturnStatusMap.get(detailDO.getId()) != null ? detailIdReturnStatusMap.get(detailDO.getId()) : GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode());
            }
            orderDetailMapper.loopUpdateByIdIn(detailList);
        }
    }
}
