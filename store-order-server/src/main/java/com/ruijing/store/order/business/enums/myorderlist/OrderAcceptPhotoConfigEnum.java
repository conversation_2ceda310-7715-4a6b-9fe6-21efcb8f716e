package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-08-26 15:25
 * @description: 验收图片配置枚举
 */
public enum OrderAcceptPhotoConfigEnum {

    /**
     * 无需上传
     */
    NO_NEED(0),

    /**
     * 可选择性上传
     */
    SELECTIVE(1),

    /**
     * 必传
     */
    MUST(2);

    private final Integer value;

    OrderAcceptPhotoConfigEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
