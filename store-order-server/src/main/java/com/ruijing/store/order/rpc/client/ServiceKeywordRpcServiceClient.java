package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.goods.api.dto.ProductMatchingDegreeDTO;
import com.ruijing.shop.goods.api.service.ServiceKeywordRpcService;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/11/24 07
 */
@ServiceClient
public class ServiceKeywordRpcServiceClient {

    @MSharpReference(remoteAppkey = "shop-goods-service")
    private ServiceKeywordRpcService serviceKeywordRpcService;

    @ServiceLog(description = "查询商品库信息", serviceType = ServiceType.RPC_CLIENT)
    public List<ProductMatchingDegreeDTO> queryMatchingDegree(List<String> goodsNameList){
        RemoteResponse<List<ProductMatchingDegreeDTO>> remoteResponse = serviceKeywordRpcService.queryMatchingDegree(goodsNameList);
        Preconditions.isTrue(remoteResponse.isSuccess(), "查询商品库信息失败：" + remoteResponse.getMsg());
        return remoteResponse.getData();
    }
}
