package com.ruijing.store.order.business.service.orderexport;

import com.ruijing.fundamental.cat.common.collections.New;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2022/5/23 18:23
 */
public class ExportHeaderName {
    public static List<String> orderNo = New.list("订单编号");
    public static List<String> purchaseGroup = New.list("采购组");
    public static List<String> goodsBrand = New.list("商品品牌");
    public static List<String> brand = New.list("品牌");
    public static List<String> goodsQuantity = New.list("商品数量");
    public static List<String> quantity = New.list("数量");
    public static List<String> goodsCode = New.list("商品货号");
    public static List<String> goodsPrice = New.list("商品单价");
    public static List<String> price = New.list("单价");
    public static List<String> amount = New.list("总价");
    public static List<String> goodsAmount = New.list("商品总价");
    public static List<String> soldUnit = New.list("销售单位");
    public static List<String> unit = New.list("单位");
    public static List<String> spec = New.list("规格");
    public static List<String> goodsType = New.list("商品类型");
    public static List<String> firstCategory = New.list("一级分类");
    public static List<String> secondCategory = New.list("二级分类");
    public static List<String> buyer = New.list("采购人");
    public static List<String> thirdCategory = New.list("三级分类");
    public static List<String> contactMan = New.list("收货人");
    public static List<String> supplier = New.list("供应商");
    public static List<String> toSupplierNote = New.list("给供应商备注");
    public static List<String> orderDate = New.list("订单日期");
    public static List<String> orderAmount = New.list("订单金额");
    public static List<String> status = New.list("状态");
    public static List<String> orderStatus = New.list("订单状态");
    public static List<String> goodsName = New.list("商品名称");
    public static List<String> productName = New.list("产品名称");
    public static List<String> receivePhone = New.list("收货电话");
    public static List<String> receiveAddr = New.list("收货地址");
    public static List<String> financialCode = New.list("财务编号");
    public static List<String> projectCode = New.list("项目代码");
    public static List<String> fundCardNo = New.list("经费卡卡号");
    public static List<String> projectName = New.list("项目名称");
    public static List<String> subject = New.list("科目");
    public static List<String> invoiceTitle = New.list("发票抬头");
    public static List<String> receiver1 = New.list("验收人1");
    public static List<String> receiver2 = New.list("验收人2");
    public static List<String> approveMan = New.list("审批人");
    public static List<String> firstCardName = New.list("一级经费名称");
    public static List<String> firstCardCode = New.list("一级经费编号");
    public static List<String> secondCardName = New.list("二级经费名称");
    public static List<String> secondCardCode = New.list("二级经费编号");
    public static List<String> thirdCardName = New.list("三级经费名称");
    public static List<String> thirdCardCode = New.list("三级经费编号");
    public static List<String> successFulReturnAmount = New.list("退货金额");
    public static List<String> successFulReturnQuantity = New.list("退货数量");
    public static List<String> successFulReturnProductAmount = New.list("退货总计");
    public static List<String> statementType = New.list("结算方式");
    public static List<String> orgName = New.list("采购单位");
    public static List<String> sendOutDate = New.list("发货日期");
    public static List<String> deliveryProxyType = New.list("代配送来源类型");
    public static List<String> deliveryStatus = New.list("代配送状态");
    public static List<String> sortedUser = New.list("分拣员");
    public static List<String> deliveryUser = New.list("配送员");
    public static List<String> deliveredTime = New.list("配送时间");
}
