package com.ruijing.store.order.gateway.print.dto.order;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.other.dto.OrderDetailPrintDTO;
import com.ruijing.store.order.api.base.other.dto.OrderFundCardPrintDTO;
import com.ruijing.store.order.api.gateway.dto.SupplierUserDTO;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;
import com.ruijing.store.order.gateway.print.dto.other.OrderInvoiceDTO;
import com.ruijing.store.order.gateway.print.vo.apply.ApplyInstructionsVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 10:55
 * @description
 */
public class OrderPrintDataItemDTO implements Serializable {

    private static final long serialVersionUID = 5704987457201412682L;
    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 采购单id
     */
    @RpcModelProperty("采购单id")
    private Integer applicationId;

    /**
     * 采购单单号
     */
    @RpcModelProperty("采购单单号")
    private String applicationNo;

    

    /**
     * 供应商名字
     */
    @RpcModelProperty("供应商名字")
    private String suppName;

    /**
     * 供应商编码
     */
    @RpcModelProperty("供应商编码")
    private String suppCode;

    @RpcModelProperty("供应商确认人用户信息")
    private SupplierUserDTO confirmManData;

    @RpcModelProperty("供应商业务员")
    private SupplierUserDTO suppTradeClerk;

    @RpcModelProperty("供应商法人")
    private String suppCorporation;

    @RpcModelProperty("供应商地址")
    private String suppAddress;


    /**
     * 采购人名称
     */
    @RpcModelProperty("采购人名称")
    private String buyerName;

    @RpcModelProperty("采购人工号")
    private String buyerJobNumber;

    /**
     * 采购审核人
     */
    @RpcModelProperty("采购审核人, 默认是一级审批人")
    private String orderApprovalName;

    /**
     * 二级采购审核人
     */
    @RpcModelProperty("二级采购审核人")
    private String orderSecondApprovalName;

    /**
     * 三级采购审核人
     */
    @RpcModelProperty("三级采购审核人")
    private String orderThirdApprovalName;

    @RpcModelProperty("采购单生成时间")
    private Long applicationCreateTime;

    @RpcModelProperty("采购提交审批时间")
    private Long applicationSubmitApprovalTime;

    /**
     * 订单发票抬头id
     */
    @RpcModelProperty("订单发票抬头id")
    private Integer invoiceTitleId;

    @RpcModelProperty("订单发票抬头名称")
    private String invoiceTitle;

    @RpcModelProperty("订单发票抬头税号")
    private String invoiceTaxNo;

    /**
     * 条形码
     */
    @RpcModelProperty("条形码")
    private String barCode;

    /**
     * 采购人联系方式
     */
    @RpcModelProperty("采购人联系方式")
    private String buyerTelephone;

    /**
     * 订单验收日期
     */
    @RpcModelProperty("订单验收日期")
    private Long orderReceiptDate;

    /**
     * 验收人名称
     */
    @RpcModelProperty("验收人名称")
    private String receiverName;

    /**
     * 收货人（注意与验收人的区分，此处与供应商命名一致）
     */
    @RpcModelProperty("收货人（注意与验收人的区分）")
    private String buyerContactMan;

    /**
     * 收货人手机号
     */
    @RpcModelProperty("收货人手机号")
    private String receiverPhone;

    /**
     * 收货地址
     */
    @RpcModelProperty("收货地址")
    private String receiverAddress;

    /**
     * 线下供应商统一信用编码
     */
    @RpcModelProperty("线下供应商统一信用编码")
    private String unifyCode;

    /**
     * 二维码
     */
    @RpcModelProperty("二维码")
    private String orderNoQrCode;

    @RpcModelProperty("微信订单详情二维码")
    private String wechatOrderDetailQrCode;

    /**
     * 验收审批人
     */
    @RpcModelProperty("验收审批人")
    private String acceptApprovalName;

    /**
     * 课题组名称
     */
    @RpcModelProperty("课题组名称")
    private String departmentName;

    /**
     * 当前课题组父级课题组名称
     */
    @RpcModelProperty("当前课题组父级课题组名称")
    private String departmentParentName;

    /**
     * 课题组负责人
     */
    @RpcModelProperty("课题组负责人")
    private String departmentManagerName;

    /**
     * 当前课题组向上两级的部门名称
     */
    @RpcModelProperty("当前课题组向上两级的管理部门名称")
    private String departmentGrandParentName;

    private Integer orgId;

    /**
     * 医院名称
     */
    @RpcModelProperty("单位/医院名称")
    private String orgName;

    /**
     * 订单验收日期
     */
    @RpcModelProperty("订单生成日期")
    private Long orderDate;

    /**
     * 采购申请说明
     */
    @RpcModelProperty("采购申请说明")
    private String purchaseNote;

    @RpcModelProperty("采购用途")
    private String purchasePurpose;

    /**
     * 线上-0，线下-1单
     */
    @RpcModelProperty("线上-0，线下-1单")
    private Integer orderSpecies;

    /**
     * 线下采购渠道
     */
    @RpcModelProperty("线下采购渠道")
    private String offlineProcurementChannel;

    @RpcModelProperty("自定义的订单关联分类")
    private String selfDefCategory;

    @RpcModelProperty("订单总额（退货前）")
    private BigDecimal totalPrice;

    @RpcModelProperty("减去成功退货后的订单总额")
    private BigDecimal totalPriceAfterReturn;

    @RpcModelProperty("减去成功退货后的合计数量")
    private BigDecimal totalQuantityAfterReturn;

    /**
     * 订单商品详情
     */
    @RpcModelProperty("订单商品详情")
    private List<OrderDetailPrintDTO> orderDetailPrintList;

    /**
     * 订单经费卡信息
     */
    @RpcModelProperty("订单经费卡信息")
    private List<OrderFundCardPrintDTO> orderFundCardPrintList;

    /**
     * 订单审批日志打印模型数组
     */
    @RpcModelProperty("采购/竞价及验收审批的审批日志")
    private List<OrderPrintApprovalLogDTO> orderPurchaseApprovalLogPrintList;

    @RpcModelProperty("采购/竞价审批日志")
    private List<OrderPrintApprovalLogDTO> purchaseOrBidApprovalLogList;

    @RpcModelProperty("提交采购单日志")
    private OrderPrintApprovalLogDTO submitPurchaseLog;

    @RpcModelProperty("采购单日志--打平的日志")
    private ApprovalLogFlatListDTO purchaseFlatLog;

    @RpcModelProperty("验收审批日志")
    private List<OrderPrintApprovalLogDTO> acceptApproveLogList;

    @RpcModelProperty("提交验收日志")
    private OrderPrintApprovalLogDTO submitAcceptLog;

    @RpcModelProperty("订单验收审批日志--最后一次审批驳回后的审批通过日志")
    private ApprovalLogFlatListDTO orderApproveSuccessLog;

    /**
     * 订单验收评价列表-字串
     */
    @RpcModelProperty("订单验收评价列表-评价id")
    private List<Integer> acceptCommentIdList;

    /**
     * 发票信息打印模组
     */
    @RpcModelProperty("发票信息")
    private List<OrderInvoiceDTO> invoiceList;

    @RpcModelProperty("所有发票数据的no和code的拼接")
    private String allInvoiceNo;

    /**
     * 验收时图片
     */
    @RpcModelProperty("验收时图片")
    private List<String> receivePicUrls;

    @RpcModelProperty("代配送标记，如果是代配送则为*")
    private String deliveryProxyMark;

    @RpcModelProperty("采购说明")
    private List<ApplyInstructionsVO> applyInfoList;

    @RpcModelProperty("验收人联系方式 确认收货")
    private String accpectorPhone;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public SupplierUserDTO getConfirmManData() {
        return confirmManData;
    }

    public void setConfirmManData(SupplierUserDTO confirmManData) {
        this.confirmManData = confirmManData;
    }

    public SupplierUserDTO getSuppTradeClerk() {
        return suppTradeClerk;
    }

    public OrderPrintDataItemDTO setSuppTradeClerk(SupplierUserDTO suppTradeClerk) {
        this.suppTradeClerk = suppTradeClerk;
        return this;
    }

    public String getSuppCorporation() {
        return suppCorporation;
    }

    public OrderPrintDataItemDTO setSuppCorporation(String suppCorporation) {
        this.suppCorporation = suppCorporation;
        return this;
    }

    public String getSuppAddress() {
        return suppAddress;
    }

    public OrderPrintDataItemDTO setSuppAddress(String suppAddress) {
        this.suppAddress = suppAddress;
        return this;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerJobNumber() {
        return buyerJobNumber;
    }

    public OrderPrintDataItemDTO setBuyerJobNumber(String buyerJobNumber) {
        this.buyerJobNumber = buyerJobNumber;
        return this;
    }

    public String getOrderApprovalName() {
        return orderApprovalName;
    }

    public void setOrderApprovalName(String orderApprovalName) {
        this.orderApprovalName = orderApprovalName;
    }

    public String getOrderSecondApprovalName() {
        return orderSecondApprovalName;
    }

    public void setOrderSecondApprovalName(String orderSecondApprovalName) {
        this.orderSecondApprovalName = orderSecondApprovalName;
    }

    public String getOrderThirdApprovalName() {
        return orderThirdApprovalName;
    }

    public void setOrderThirdApprovalName(String orderThirdApprovalName) {
        this.orderThirdApprovalName = orderThirdApprovalName;
    }

    public Long getApplicationCreateTime() {
        return applicationCreateTime;
    }

    public void setApplicationCreateTime(Long applicationCreateTime) {
        this.applicationCreateTime = applicationCreateTime;
    }

    public Long getApplicationSubmitApprovalTime() {
        return applicationSubmitApprovalTime;
    }

    public OrderPrintDataItemDTO setApplicationSubmitApprovalTime(Long applicationSubmitApprovalTime) {
        this.applicationSubmitApprovalTime = applicationSubmitApprovalTime;
        return this;
    }

    public Integer getInvoiceTitleId() {
        return invoiceTitleId;
    }

    public void setInvoiceTitleId(Integer invoiceTitleId) {
        this.invoiceTitleId = invoiceTitleId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone;
    }

    public Long getOrderReceiptDate() {
        return orderReceiptDate;
    }

    public void setOrderReceiptDate(Long orderReceiptDate) {
        this.orderReceiptDate = orderReceiptDate;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public void setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getUnifyCode() {
        return unifyCode;
    }

    public void setUnifyCode(String unifyCode) {
        this.unifyCode = unifyCode;
    }

    public String getOrderNoQrCode() {
        return orderNoQrCode;
    }

    public void setOrderNoQrCode(String orderNoQrCode) {
        this.orderNoQrCode = orderNoQrCode;
    }

    public String getWechatOrderDetailQrCode() {
        return wechatOrderDetailQrCode;
    }

    public void setWechatOrderDetailQrCode(String wechatOrderDetailQrCode) {
        this.wechatOrderDetailQrCode = wechatOrderDetailQrCode;
    }

    public String getAcceptApprovalName() {
        return acceptApprovalName;
    }

    public void setAcceptApprovalName(String acceptApprovalName) {
        this.acceptApprovalName = acceptApprovalName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public String getDepartmentManagerName() {
        return departmentManagerName;
    }

    public void setDepartmentManagerName(String departmentManagerName) {
        this.departmentManagerName = departmentManagerName;
    }

    public String getDepartmentGrandParentName() {
        return departmentGrandParentName;
    }

    public void setDepartmentGrandParentName(String departmentGrandParentName) {
        this.departmentGrandParentName = departmentGrandParentName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderPrintDataItemDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Long orderDate) {
        this.orderDate = orderDate;
    }

    public String getPurchaseNote() {
        return purchaseNote;
    }

    public void setPurchaseNote(String purchaseNote) {
        this.purchaseNote = purchaseNote;
    }

    public String getPurchasePurpose() {
        return purchasePurpose;
    }

    public void setPurchasePurpose(String purchasePurpose) {
        this.purchasePurpose = purchasePurpose;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public String getOfflineProcurementChannel() {
        return offlineProcurementChannel;
    }

    public void setOfflineProcurementChannel(String offlineProcurementChannel) {
        this.offlineProcurementChannel = offlineProcurementChannel;
    }

    public String getSelfDefCategory() {
        return selfDefCategory;
    }

    public void setSelfDefCategory(String selfDefCategory) {
        this.selfDefCategory = selfDefCategory;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public OrderPrintDataItemDTO setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
        return this;
    }

    public BigDecimal getTotalPriceAfterReturn() {
        return totalPriceAfterReturn;
    }

    public void setTotalPriceAfterReturn(BigDecimal totalPriceAfterReturn) {
        this.totalPriceAfterReturn = totalPriceAfterReturn;
    }

    public BigDecimal getTotalQuantityAfterReturn() {
        return totalQuantityAfterReturn;
    }

    public void setTotalQuantityAfterReturn(BigDecimal totalQuantityAfterReturn) {
        this.totalQuantityAfterReturn = totalQuantityAfterReturn;
    }

    public List<OrderDetailPrintDTO> getOrderDetailPrintList() {
        return orderDetailPrintList;
    }

    public void setOrderDetailPrintList(List<OrderDetailPrintDTO> orderDetailPrintList) {
        this.orderDetailPrintList = orderDetailPrintList;
    }

    public List<OrderFundCardPrintDTO> getOrderFundCardPrintList() {
        return orderFundCardPrintList;
    }

    public void setOrderFundCardPrintList(List<OrderFundCardPrintDTO> orderFundCardPrintList) {
        this.orderFundCardPrintList = orderFundCardPrintList;
    }

    public List<OrderPrintApprovalLogDTO> getOrderPurchaseApprovalLogPrintList() {
        return orderPurchaseApprovalLogPrintList;
    }

    public void setOrderPurchaseApprovalLogPrintList(List<OrderPrintApprovalLogDTO> orderPurchaseApprovalLogPrintList) {
        this.orderPurchaseApprovalLogPrintList = orderPurchaseApprovalLogPrintList;
    }

    public List<OrderPrintApprovalLogDTO> getPurchaseOrBidApprovalLogList() {
        return purchaseOrBidApprovalLogList;
    }

    public void setPurchaseOrBidApprovalLogList(List<OrderPrintApprovalLogDTO> purchaseOrBidApprovalLogList) {
        this.purchaseOrBidApprovalLogList = purchaseOrBidApprovalLogList;
    }

    public OrderPrintApprovalLogDTO getSubmitPurchaseLog() {
        return submitPurchaseLog;
    }

    public OrderPrintDataItemDTO setSubmitPurchaseLog(OrderPrintApprovalLogDTO submitPurchaseLog) {
        this.submitPurchaseLog = submitPurchaseLog;
        return this;
    }

    public ApprovalLogFlatListDTO getPurchaseFlatLog() {
        return purchaseFlatLog;
    }

    public OrderPrintDataItemDTO setPurchaseFlatLog(ApprovalLogFlatListDTO purchaseFlatLog) {
        this.purchaseFlatLog = purchaseFlatLog;
        return this;
    }

    public OrderPrintApprovalLogDTO getSubmitAcceptLog() {
        return submitAcceptLog;
    }

    public OrderPrintDataItemDTO setSubmitAcceptLog(OrderPrintApprovalLogDTO submitAcceptLog) {
        this.submitAcceptLog = submitAcceptLog;
        return this;
    }

    public List<OrderPrintApprovalLogDTO> getAcceptApproveLogList() {
        return acceptApproveLogList;
    }

    public void setAcceptApproveLogList(List<OrderPrintApprovalLogDTO> acceptApproveLogList) {
        this.acceptApproveLogList = acceptApproveLogList;
    }

    public ApprovalLogFlatListDTO getOrderApproveSuccessLog() {
        return orderApproveSuccessLog;
    }

    public OrderPrintDataItemDTO setOrderApproveSuccessLog(ApprovalLogFlatListDTO orderApproveSuccessLog) {
        this.orderApproveSuccessLog = orderApproveSuccessLog;
        return this;
    }

    public List<Integer> getAcceptCommentIdList() {
        return acceptCommentIdList;
    }

    public void setAcceptCommentIdList(List<Integer> acceptCommentIdList) {
        this.acceptCommentIdList = acceptCommentIdList;
    }

    public List<OrderInvoiceDTO> getInvoiceList() {
        return invoiceList;
    }

    public void setInvoiceList(List<OrderInvoiceDTO> invoiceList) {
        this.invoiceList = invoiceList;
    }

    public String getAllInvoiceNo() {
        return allInvoiceNo;
    }

    public void setAllInvoiceNo(String allInvoiceNo) {
        this.allInvoiceNo = allInvoiceNo;
    }

    public List<String> getReceivePicUrls() {
        return receivePicUrls;
    }

    public void setReceivePicUrls(List<String> receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public String getDeliveryProxyMark() {
        return deliveryProxyMark;
    }

    public void setDeliveryProxyMark(String deliveryProxyMark) {
        this.deliveryProxyMark = deliveryProxyMark;
    }

    public List<ApplyInstructionsVO> getApplyInfoList() {
        return applyInfoList;
    }

    public OrderPrintDataItemDTO setApplyInfoList(List<ApplyInstructionsVO> applyInfoList) {
        this.applyInfoList = applyInfoList;
        return this;
    }

    public String getAccpectorPhone() {
        return accpectorPhone;
    }

    public void setAccpectorPhone(String accpectorPhone) {
        this.accpectorPhone = accpectorPhone;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public OrderPrintDataItemDTO setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
        return this;
    }

    public String getInvoiceTaxNo() {
        return invoiceTaxNo;
    }

    public OrderPrintDataItemDTO setInvoiceTaxNo(String invoiceTaxNo) {
        this.invoiceTaxNo = invoiceTaxNo;
        return this;
    }

    @Override
    public String toString() {
        return "OrderPrintDataItemDTO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", applicationId=" + applicationId +
                ", applicationNo='" + applicationNo + '\'' +
                ", suppName='" + suppName + '\'' +
                ", suppCode='" + suppCode + '\'' +
                ", confirmManData=" + confirmManData +
                ", suppTradeClerk=" + suppTradeClerk +
                ", suppCorporation='" + suppCorporation + '\'' +
                ", suppAddress='" + suppAddress + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", buyerJobNumber='" + buyerJobNumber + '\'' +
                ", orderApprovalName='" + orderApprovalName + '\'' +
                ", orderSecondApprovalName='" + orderSecondApprovalName + '\'' +
                ", orderThirdApprovalName='" + orderThirdApprovalName + '\'' +
                ", applicationCreateTime=" + applicationCreateTime +
                ", applicationSubmitApprovalTime=" + applicationSubmitApprovalTime +
                ", invoiceTitleId=" + invoiceTitleId +
                ", invoiceTitle='" + invoiceTitle + '\'' +
                ", invoiceTaxNo='" + invoiceTaxNo + '\'' +
                ", barCode='" + barCode + '\'' +
                ", buyerTelephone='" + buyerTelephone + '\'' +
                ", orderReceiptDate=" + orderReceiptDate +
                ", receiverName='" + receiverName + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", receiverPhone='" + receiverPhone + '\'' +
                ", receiverAddress='" + receiverAddress + '\'' +
                ", unifyCode='" + unifyCode + '\'' +
                ", orderNoQrCode='" + orderNoQrCode + '\'' +
                ", wechatOrderDetailQrCode='" + wechatOrderDetailQrCode + '\'' +
                ", acceptApprovalName='" + acceptApprovalName + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", departmentParentName='" + departmentParentName + '\'' +
                ", departmentManagerName='" + departmentManagerName + '\'' +
                ", departmentGrandParentName='" + departmentGrandParentName + '\'' +
                ", orgId=" + orgId +
                ", orgName='" + orgName + '\'' +
                ", orderDate=" + orderDate +
                ", purchaseNote='" + purchaseNote + '\'' +
                ", purchasePurpose='" + purchasePurpose + '\'' +
                ", orderSpecies=" + orderSpecies +
                ", offlineProcurementChannel='" + offlineProcurementChannel + '\'' +
                ", selfDefCategory='" + selfDefCategory + '\'' +
                ", totalPrice=" + totalPrice +
                ", totalPriceAfterReturn=" + totalPriceAfterReturn +
                ", totalQuantityAfterReturn=" + totalQuantityAfterReturn +
                ", orderDetailPrintList=" + orderDetailPrintList +
                ", orderFundCardPrintList=" + orderFundCardPrintList +
                ", orderPurchaseApprovalLogPrintList=" + orderPurchaseApprovalLogPrintList +
                ", purchaseOrBidApprovalLogList=" + purchaseOrBidApprovalLogList +
                ", submitPurchaseLog=" + submitPurchaseLog +
                ", purchaseFlatLog=" + purchaseFlatLog +
                ", acceptApproveLogList=" + acceptApproveLogList +
                ", submitAcceptLog=" + submitAcceptLog +
                ", orderApproveSuccessLog=" + orderApproveSuccessLog +
                ", acceptCommentIdList=" + acceptCommentIdList +
                ", invoiceList=" + invoiceList +
                ", allInvoiceNo='" + allInvoiceNo + '\'' +
                ", receivePicUrls=" + receivePicUrls +
                ", deliveryProxyMark='" + deliveryProxyMark + '\'' +
                ", applyInfoList=" + applyInfoList +
                ", accpectorPhone='" + accpectorPhone + '\'' +
                '}';
    }
}
