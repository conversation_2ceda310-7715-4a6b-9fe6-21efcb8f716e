package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/17 17:53
 * @Description
 **/
public class OrderOfflineExtraVO implements Serializable {

    private static final long serialVersionUID = -2784208888974479367L;

    private Integer id;

    private Integer buyApplicationMasterId;

    private String context;

    private List<String> attachments;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBuyApplicationMasterId() {
        return buyApplicationMasterId;
    }

    public void setBuyApplicationMasterId(Integer buyApplicationMasterId) {
        this.buyApplicationMasterId = buyApplicationMasterId;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public List<String> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<String> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOfflineExtraVO{");
        sb.append("id=").append(id);
        sb.append(", buyApplicationMasterId=").append(buyApplicationMasterId);
        sb.append(", context='").append(context).append('\'');
        sb.append(", attachments=").append(attachments);
        sb.append('}');
        return sb.toString();
    }
}
