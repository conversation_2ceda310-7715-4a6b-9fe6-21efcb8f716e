package com.ruijing.store.order.business.service.orgondemand;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.order.utils.ListUtils;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.contract.constant.BusinessTypeEnum;
import com.ruijing.store.contract.constant.TransactionTypeEnum;
import com.ruijing.store.contract.dto.PriceContractProductDTO;
import com.ruijing.store.contract.dto.PriceContractTransactionDTO;
import com.ruijing.store.contract.dto.ProductBuyingAmountDTO;
import com.ruijing.store.contract.dto.ProductTransactionDTO;
import com.ruijing.store.contract.dto.budget.ClinicalBudgetTransactionDTO;
import com.ruijing.store.contract.dto.reagentProduct.ReagentProductDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.core.translator.ProductContractTranslator;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.OrderProductContractVO;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ProductContractVO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.PriceContractClient;
import com.ruijing.store.order.rpc.client.ReagentProductBaseClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @Date 2021/2/22 15:30
 * @Description 中山三院 单位个性化服务 the third affiliated hospital, SYSU.
 **/
@Service
public class ClinicalOrderService {

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private PriceContractClient priceContractClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper oderDetailMapper;

    @Resource
    private ReagentProductBaseClient reagentProductBaseClient;

    /**
     * @description: 获取采购单年份
     * @date: 2021/3/5 10:29
     * @author: zengyanru
     * @param applyId
     * @return java.lang.String
     */
    @ServiceLog(description = "获取采购单年份", serviceType = ServiceType.COMMON_SERVICE)
    private String getPurchaseYear(Integer applyId) {
        ApplicationQueryDTO query = new ApplicationQueryDTO();
        query.setApplyIds(New.list(Long.valueOf(applyId)));
        List<ApplicationMasterDTO> applyInfoList = applicationBaseClient.findByMasterId(query);
        ApplicationMasterDTO applyMasterInfo = applyInfoList.get(0);
        Preconditions.notNull(applyMasterInfo, "getPurchaseYear方法无法查询到采购单，入参\napplyId=" + applyId);
        return DateUtils.format("yyyy", applyMasterInfo.getCreateTime());
    }

    /**
     * 临床采购单，退货或取消订单释放价格合同金额
     * @param orderApprovalEnum
     * @param orderMaster
     * @param goodsReturn
     * @return
     */
    public void releaseReagentProductAnnualUsage(OrderApprovalEnum orderApprovalEnum ,OrderMasterDO orderMaster, GoodsReturn goodsReturn){
        if (!OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode().equals(orderMaster.getOrderType())) {
            return;
        }
        ClinicalBudgetTransactionDTO clinicalBudgetTransactionDTO = new ClinicalBudgetTransactionDTO();
        clinicalBudgetTransactionDTO.setYear(getPurchaseYear(orderMaster.getFtbuyappid()));
        clinicalBudgetTransactionDTO.setOrgId(orderMaster.getFuserid());
        if(orderApprovalEnum.equals(OrderApprovalEnum.GOODS_RETURN)){
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            Map<Long, ProductTransactionDTO> productMap = new HashMap<>(goodsReturnInfoDetailList.size());
            for(GoodsReturnInfoDetailVO goodsReturnInfoDetail : goodsReturnInfoDetailList){
                Long productId = Long.valueOf(goodsReturnInfoDetail.getProductId());
                BigDecimal price = goodsReturnInfoDetail.getPrice();
                BigDecimal quantity = goodsReturnInfoDetail.getQuantity();
                BigDecimal amount = price.multiply(quantity);
                ProductTransactionDTO transactionDTO = productMap.get(productId);
                if(transactionDTO == null){
                    transactionDTO = new ProductTransactionDTO();
                    transactionDTO.setProductId(productId);
                    transactionDTO.setPrice(amount);
                    transactionDTO.setQuantity(quantity.intValue());
                    productMap.put(productId , transactionDTO);
                }else {
                    transactionDTO.setQuantity(transactionDTO.getQuantity() + quantity.intValue());
                    transactionDTO.setPrice(transactionDTO.getPrice().add(amount));
                }
                clinicalBudgetTransactionDTO.setProductTransactionList(New.list(productMap.values()));
            }
        }else if(orderApprovalEnum.equals(OrderApprovalEnum.CANCEL)){
            List<ProductTransactionDTO> productTransactionList= New.list();
            // 查找订单详情（每个商品条目）
            List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(orderMaster.getId());
            for(OrderDetailDO orderDetail : orderDetailList){
                BigDecimal price = orderDetail.getFbidprice();
                BigDecimal quantity = orderDetail.getFquantity();
                Long productId = orderDetail.getProductSn();
                ProductTransactionDTO transactionDTO = new ProductTransactionDTO();
                transactionDTO.setPrice(price.multiply(quantity));
                transactionDTO.setProductId(productId);
                transactionDTO.setQuantity(quantity.intValue());
                productTransactionList.add(transactionDTO);
            }
            clinicalBudgetTransactionDTO.setProductTransactionList(productTransactionList);
        }
        Preconditions.isTrue(priceContractClient.releaseReagentProductAnnualUsage(clinicalBudgetTransactionDTO), "更新管控金额失败");

    }

    /**
     * 根据订单id获取对应的订单合同编号，商品合同信息等（方法学、品牌产地...）
     * @param orderIdList
     * @return
     */
    public List<OrderProductContractVO> listOrderProductByOrderIds(List<Integer> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)){
            return New.list();
        }
        // 所有订单 buyapp_id
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        if(CollectionUtils.isEmpty(orderMasterDOList)){
            return New.list();
        }
        List<OrderProductContractVO> orderProductContractVOS = New.listWithCapacity(orderIdList.size());

        // 匹配价格采购单和合同id的对应关系
        OrderProductContractVO orderProductContractVO;
        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            Integer ftbuyappid = orderMasterDO.getFtbuyappid();
            orderProductContractVO = new OrderProductContractVO();
            orderProductContractVO.setOrderId(orderMasterDO.getId());
            orderProductContractVO.setBuyAppId(ftbuyappid);
            orderProductContractVO.setFsuppid(orderMasterDO.getFsuppid());
            orderProductContractVO.setFsuppcode(orderMasterDO.getFsuppcode());
            orderProductContractVO.setFsuppname(orderMasterDO.getFsuppname());
            orderProductContractVOS.add(orderProductContractVO);
        }

        // 根据订单明细获取商品对应的合同信息(方法学、单位、品牌产地)
        List<OrderDetailDO> orderDetailDOS = oderDetailMapper.findAllByFmasteridIn(orderIdList);
        if(CollectionUtils.isEmpty(orderDetailDOS)){
            return orderProductContractVOS;
        }
        // 获取所有商品合同信息
        List<Long> productSnList = orderDetailDOS.stream().map(OrderDetailDO::getProductSn).distinct().collect(toList());

        // 反找锐竞合同商品信息, PriceContractProductDTO.remark不再使用了, modified by zyl 2022-01-11
        List<ReagentProductDTO> reagentProductDTOS = reagentProductBaseClient.listReagentProductByIds(productSnList);
        Map<Integer, List<OrderDetailDO>> orderToDetailMap = orderDetailDOS.stream().collect(groupingBy(OrderDetailDO::getFmasterid));
        Map<Long, ReagentProductDTO> productSnToPriceContractMap = reagentProductDTOS.stream().collect(toMap(ReagentProductDTO::getId, Function.identity()));
        List<OrderDetailDO> currOrderDetails;
        List<ProductContractVO> productContractVOS;
        // 设置不同订单下的商品合同信息
        for (OrderProductContractVO curr : orderProductContractVOS) {
            currOrderDetails = orderToDetailMap.get(curr.getOrderId());
            if(CollectionUtils.isEmpty(currOrderDetails)){
                continue;
            }
            // 设置订单明细及订单明细对应合同信息（方法学，品牌产地.....）
            productContractVOS = ProductContractTranslator.orderDetailToContractVo(currOrderDetails, productSnToPriceContractMap);
            curr.setProductContractList(productContractVOS);
        }
        return orderProductContractVOS;
    }

}
