package com.ruijing.store.order.base.minor.mapper;
import com.ruijing.store.order.base.minor.model.OrderContract;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderContractMapper {

    /**
     * 根据订单id 查询订单合同信息
     * @param orderId 订单id
     * @return OrderContract 订单合同信息
     */
    List<OrderContract> findByOrderId(@Param("orderId")Integer orderId);

    int deleteByPrimaryKey(Integer id);

    int insert(OrderContract record);

    int insertSelective(OrderContract record);

    OrderContract selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderContract record);

    int updateByPrimaryKey(OrderContract record);

    /**
     * 根据订单id列表 批量查询订单合同信息
     * @param orderIdCollection
     * @return OrderContract 订单合同信息
     */
    List<OrderContract> selectByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * @description: 根据订单id批量删除订单合同信息
     * @date: 2021/1/6 18:54
     * @author: zengyanru
     * @param orderIdCollection
     * @return int
     */
    int deleteByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * @description: 批量插入订单合同表，自动生成id
     * @date: 2021/1/6 18:57
     * @author: zengyanru
     * @param list
     * @return int
     */
    int insertList(@Param("list")List<OrderContract> list);


}