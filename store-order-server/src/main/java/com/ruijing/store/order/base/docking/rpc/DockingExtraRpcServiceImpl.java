package com.ruijing.store.order.base.docking.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingRequestDTO;
import com.ruijing.store.order.api.base.docking.service.DockingExtraRpcService;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.docking.translator.DockingExtraTranslator;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description: 第三方对接表 RPC服务
 * @author: zhuk
 * @create: 2019-09-25 14:25
 **/
@MSharpService
@ServiceLog
public class DockingExtraRpcServiceImpl implements DockingExtraRpcService {

    private String catType = "DockingExtraRpcService";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private DockingExtraService dockingExtraService;

    /**
     * 批量插入
     * @param dockingExtraDTOList
     * @return
     */
    @Override
    public RemoteResponse<Integer> insertList(List<DockingExtraDTO> dockingExtraDTOList){
        Integer result = dockingExtraService.insertList(dockingExtraDTOList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    /**
     * 新增dockingExtra
     * @param dockingExtraDTO
     * @return
     */
    @Override
    public RemoteResponse insertDockingExtra(DockingExtraDTO dockingExtraDTO){
        final String methodName = "insertDockingExtra";
        Transaction transaction = Cat.newTransaction(catType, methodName);
        RemoteResponse remoteResponse;
        try {
            dockingExtraService.insertDockingExtra(dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setSuccess().build();
            transaction.setSuccess();
        } catch (Exception e) {
            logger.error("insertDockingExtra 异常",e);
            transaction.setStatus(e);
            transaction.addData("入参",dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setFailure("插入dockingExtra失败！").build();
        }finally {
            transaction.complete();
        }
        return  remoteResponse;
    }

    /**
     * 查询 DockingExtra
     * @param dockingExtraDTO 入参
     * @return  List<DockingExtraDTO>
     */
    @Override
    public RemoteResponse<List<DockingExtraDTO>> findDockingExtra(DockingExtraDTO dockingExtraDTO){
        final String methodName = "findDockingExtra";
        Transaction transaction = Cat.newTransaction(catType, methodName);
        RemoteResponse remoteResponse;
        try {
            List<DockingExtraDTO> dockingExtraDTOList = dockingExtraService.findDockingExtra(dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setSuccess().setData(dockingExtraDTOList).build();
            transaction.setSuccess();
        } catch (Exception e) {
            logger.error(methodName+"异常",e);
            transaction.setStatus(e);
            transaction.addData("入参",dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setFailure("更新dockingExtra失败！").build();
        }finally {
            transaction.complete();
        }
        return  remoteResponse;
    }

    /**
     * 更新 DockingExtra 记录
     * @param dockingExtraDTO 入参
     * @return
     */
    @Override
    public RemoteResponse updateDockingExtra(DockingExtraDTO dockingExtraDTO) {
        final String methodName = "updateDockingExtra";
        Transaction transaction = Cat.newTransaction(catType, methodName);
        RemoteResponse remoteResponse;
        try {
            Boolean result = dockingExtraService.updateDockingExtra(dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setData(result).setSuccess().build();
        }catch (Exception e) {
            logger.error(methodName+"异常",e);
            transaction.setStatus(e);
            transaction.addData("入参",dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setFailure("插入dockingExtra失败！").build();
        }finally {
            transaction.complete();
        }
        return  remoteResponse;
    }

    /**
     * 根据info 修改 extraInfo
     * @param dockingExtraDTO
     * @return
     */
    @Override
    public RemoteResponse updateExtraInfoByInfo(DockingExtraDTO dockingExtraDTO){
        final String methodName = "updateExtraInfoByInfo";
        Transaction transaction = Cat.newTransaction(catType, methodName);
        RemoteResponse remoteResponse;
        try {
            String extraInfo = dockingExtraDTO.getExtraInfo();
            String info = dockingExtraDTO.getInfo();
            dockingExtraService.updateExtraInfoByInfo(extraInfo,info);
            remoteResponse = RemoteResponse.custom().setSuccess().build();
        }catch (Exception e) {
            logger.error(methodName+"异常",e);
            transaction.setStatus(e);
            transaction.addData("入参",dockingExtraDTO);
            remoteResponse = RemoteResponse.custom().setFailure("updateExtraInfoByInfo失败！").build();
        }finally {
            transaction.complete();
        }
        return  remoteResponse;
    }

    @Override
    public RemoteResponse<List<DockingExtraDTO>> findDockingExtraByInfoList(DockingRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getInfoList())) {
            return RemoteResponse.<List<DockingExtraDTO>>custom().setSuccess().setData(Collections.emptyList()).build();
        }
        Assert.isTrue(request.getInfoList().size() < 501, "查询订单数量 infoList 不可超过500！");
        List<DockingExtraDTO> result = dockingExtraService.findDockingExtraByInfo(request.getInfoList());
        return RemoteResponse.<List<DockingExtraDTO>>custom().setSuccess().setData(result).build();
    }

    @Override
    @ServiceLog(description = "通过对接单号信息和 type 查询对接单信息")
    public RemoteResponse<List<DockingExtraDTO>> findDockingByExtraInfoAndType(DockingRequestDTO request) {
        List<String> extraInfoList = request.getExtraInfoList();
        Integer type = request.getType();
        Preconditions.notEmpty(extraInfoList, "对接单号不可为空！");
        Preconditions.isTrue(extraInfoList.size() < 101, "单次查询订单对接信息数量不超过100！");
        Preconditions.notNull(type, "对接类型不可为空！");

        List<DockingExtraDTO> result = dockingExtraService.findDockingByExtraInfoAndType(extraInfoList, type);
        return RemoteResponse.<List<DockingExtraDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "保存&更新docking对接记录", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> saveOrUpdateByInfo(DockingExtraDTO request) {
        Preconditions.notNull(request, "request must not be null");
        Preconditions.notNull(request.getInfo(), "info must not be null");
        DockingExtra dockingExtra = DockingExtraTranslator.dtoToDockingExtra(request);
        Integer affect = dockingExtraService.saveOrUpdateDockingExtra(dockingExtra);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }
}
