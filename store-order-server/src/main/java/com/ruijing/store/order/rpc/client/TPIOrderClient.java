package com.ruijing.store.order.rpc.client;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.financial.docking.dto.order.*;
import com.reagent.tpi.tpiclient.api.order.v2.OrderReturnService;
import com.reagent.tpi.tpiclient.api.order.v2.OrderService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderDetailBO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/8/26 11:38 上午
 */
@ServiceClient
@CatAnnotation
public class TPIOrderClient {

    @MSharpReference(remoteAppkey = "tpi-client", timeout = "20000")
    private OrderService orderService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @MSharpReference(remoteAppkey = "tpi-client", timeout = "20000")
    private OrderReturnService orderReturnService;

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private final String CAT_TYPE = "TPIOrderClient";


    @ServiceLog(description = "查询第三方对接订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public CompletableFuture<RemoteResponse<OrderDTO>> getOrderInfo(OrgRequest<OrderQueryDTO> request) {
        CompletableFuture<RemoteResponse<OrderDTO>> dockingOrderInfoFuture = orderService.getOrder(request);
        return dockingOrderInfoFuture;
    }

    @ServiceLog(description = "更新第三方对接订单信息", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean updateOrderStatus(OrgRequest<OrderStatusUpdateDTO> request) {
        RemoteResponse<Boolean> response = orderService.orderStatusUpdate(request);
        Preconditions.isTrue(response.isSuccess(), "更新失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    /**
     * 异步 更新第三方平台订单状态
     * @param thirdPartyPlatformOrderBO
     * @return
     */
    public ListenableFuture<Boolean> updateOrderStatusAsync(ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO) {
        return AsyncExecutor.listenableCallAsync(() -> this.updateOrderStatus(thirdPartyPlatformOrderBO))
                .addFailureCallback(throwable -> {
                    LOGGER.error("更新第三方订单状态失败：" + throwable);
                    Cat.logError(CAT_TYPE, "updateOrderStatusAsync", "更新第三方订单状态失败：", throwable);
                });
    }

    /**
     * 更新第三方对接订单状态
     * @param thirdPartyPlatformOrderBO  第三方平台的订单信息对象
     * @return
     */
    @ServiceLog(description = "更新第三方对接订单状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean updateOrderStatus(ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO) {
        OrderStatusUpdateDTO orderStatusUpdate = new OrderStatusUpdateDTO();
        orderStatusUpdate.setAppKey(Environment.getAppKey());
        orderStatusUpdate.setOrderNo(thirdPartyPlatformOrderBO.getReagentOrderNo());
        orderStatusUpdate.setExtraOrderNo(thirdPartyPlatformOrderBO.getExtraOrderNo());
        orderStatusUpdate.setReason(thirdPartyPlatformOrderBO.getRemark());
        orderStatusUpdate.setStatus(thirdPartyPlatformOrderBO.getStatus());

        if (OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(thirdPartyPlatformOrderBO.getOrgCode())) {
            List<ExtraDTO> extraList = new ArrayList<>();
            ExtraDTO extraDTO = new ExtraDTO();

            String jobNumber = thirdPartyPlatformOrderBO.getJobNumber();
            if (jobNumber != null) {
                extraDTO.setField("jobNumber");
                extraDTO.setValue(jobNumber);
                extraList.add(extraDTO);
            }

            String departmentName = thirdPartyPlatformOrderBO.getDepartmentName();
            if (departmentName != null) {
                extraDTO = new ExtraDTO();
                extraDTO.setField("deptName");
                extraDTO.setValue(departmentName);
                extraList.add(extraDTO);
            }

            String orderCreationTime = thirdPartyPlatformOrderBO.getOrderCreationTime();
            if (orderCreationTime != null) {
                extraDTO = new ExtraDTO();
                extraDTO.setField("orderCreationTime");
                extraDTO.setValue(orderCreationTime);
                extraList.add(extraDTO);
            }

            String orderPrice = thirdPartyPlatformOrderBO.getOrderPrice();
            if (orderPrice != null) {
                extraDTO = new ExtraDTO();
                extraDTO.setField("orderPrice");
                extraDTO.setValue(orderPrice);
                extraList.add(extraDTO);
            }

            Integer suppId = thirdPartyPlatformOrderBO.getSuppId();
            if (suppId != null) {
                extraDTO = new ExtraDTO();
                extraDTO.setField("suppId");
                extraDTO.setValue(suppId.toString());
                extraList.add(extraDTO);
            }
            orderStatusUpdate.setExtraDTOs(extraList);
        }

        if (OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode().equals(thirdPartyPlatformOrderBO.getOrgCode())) {
            List<ThirdPartyPlatformOrderDetailBO> detailBOList = thirdPartyPlatformOrderBO.getOrderDetailBOList();
            List<OrderDetailDTO> collect = detailBOList.stream().map(bo -> {
                OrderDetailDTO detailDTO = new OrderDetailDTO();
                detailDTO.setId(bo.getDetailId().toString());
                detailDTO.setGoodsId(bo.getGoodsId());
                detailDTO.setQuantity(bo.getQuantity());
                return detailDTO;
            }).collect(Collectors.toList());
            orderStatusUpdate.setOrderDetailDTOs(collect);
        }

        OrgRequest<OrderStatusUpdateDTO> request = new OrgRequest<>();
        request.setData(orderStatusUpdate);
        request.setOrgCode(thirdPartyPlatformOrderBO.getOrgCode());
        return this.updateOrderStatus(request);
    }

    @ServiceLog(description = "重新推送订单", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean retryPushOrderInfo(OrgRequest<String> request) {
        RemoteResponse<Boolean> response = orderService.rePushOrder(request);
        Boolean successful = response.getData();
        return successful;
    }

    @ServiceLog(description = "第三方平台推送订单RPC接口", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean pushOrder(OrgRequest<OrderDTO> request) {
        RemoteResponse<Boolean> response = orderService.pushOrder(request);
        Preconditions.isTrue(response.isSuccess(), "推送订单失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "通知第三方平台订单退货接口", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean orderReturn(OrgRequest<OrderReturnDTO> request) {
        RemoteResponse<Boolean> response = orderReturnService.orderReturn(request);
        Preconditions.isTrue(response.isSuccess(), "通知第三方平台订单退货接口异常：" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

}
