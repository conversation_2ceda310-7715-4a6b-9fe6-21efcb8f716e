<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.orderlog.mapper.OrderLogDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.orderlog.model.OrderLogDO">
    <!--@mbg.generated-->
    <!--@Table t_order_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_detail_id" jdbcType="BIGINT" property="orderDetailId" />
    <result column="operation" jdbcType="INTEGER" property="operation" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="extra" jdbcType="INTEGER" property="extra" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, order_detail_id, `operation`, user_id, user_type, user_name, note, 
    extra, creation_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_order_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_log (order_id, order_detail_id, `operation`, 
      user_id, user_type, user_name, 
      note, extra, creation_time, 
      update_time)
    values (#{orderId,jdbcType=BIGINT}, #{orderDetailId,jdbcType=BIGINT}, #{operation,jdbcType=INTEGER}, 
      #{userId,jdbcType=BIGINT}, #{userType,jdbcType=INTEGER}, #{userName,jdbcType=VARCHAR}, 
      #{note,jdbcType=VARCHAR}, #{extra,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderDetailId != null">
        order_detail_id,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderDetailId != null">
        #{orderDetailId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogDO">
    <!--@mbg.generated-->
    update t_order_log
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderDetailId != null">
        order_detail_id = #{orderDetailId,jdbcType=BIGINT},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.orderlog.model.OrderLogDO">
    <!--@mbg.generated-->
    update t_order_log
    set order_id = #{orderId,jdbcType=BIGINT},
      order_detail_id = #{orderDetailId,jdbcType=BIGINT},
      `operation` = #{operation,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_type = #{userType,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>