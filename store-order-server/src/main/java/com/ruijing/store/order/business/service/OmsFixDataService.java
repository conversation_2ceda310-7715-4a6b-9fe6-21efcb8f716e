package com.ruijing.store.order.business.service;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.oms.request.FixDataCommonRequest;
import com.ruijing.store.order.gateway.oms.request.FixFundDataRequest;
import com.ruijing.store.order.gateway.oms.request.FixOrderDetailCommonRequest;
import com.ruijing.store.order.gateway.oms.request.OmsFixDataLogQueryRequest;
import com.ruijing.store.order.gateway.oms.vo.ErrorMsgVO;
import com.ruijing.store.order.gateway.oms.vo.OmsFixDataLogVO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/8 10:05
 * @description
 */
public interface OmsFixDataService {

    /**
     * 获取订单列表
     *
     * @param request 请求订单列表参数
     * @return 订单数据
     */
    PageableResponse<List<OrderInfoVO>> getOrderList(OrderListRequest request);

    /**
     * 获取数据修正日志
     *
     * @param omsFixDataLogQueryRequest 请求数据修正列表参数
     * @return 订单数据
     */
    PageableResponse<List<OmsFixDataLogVO>> getDataOperationLog(OmsFixDataLogQueryRequest omsFixDataLogQueryRequest);

    /**
     * 重新解冻
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单数据
     */
    List<ErrorMsgVO> reUnfreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);

    /**
     * 解冻
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单数据
     */
    List<ErrorMsgVO> unfreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);

    /**
     * 重新冻结
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单数据
     */
    List<ErrorMsgVO> reFreeze(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);

    /**
     * 修正订单状态
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单数据
     */
    List<ErrorMsgVO> fixOrderStatus(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);


    /**
     * 修正订单详情商品分类快照
     * @param loginUserInfoBO
     * @param fixOrderDetailCommonRequest
     * @return
     */
    List<ErrorMsgVO> fixOrderDetailProductCategory(LoginUserInfoBO loginUserInfoBO, FixOrderDetailCommonRequest fixOrderDetailCommonRequest);

    /**
     * 删除测试订单
     *
     * @param loginUserInfoBO
     * @param fixDataCommonRequest
     * @return
     */
    List<ErrorMsgVO> deleteTestOrders(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);

    /**
     * 修正经费状态
     *
     * @param loginUserInfoBO      用户信息
     * @param fixDataCommonRequest 修改订单数据
     */
    List<ErrorMsgVO> fixFundStatus(LoginUserInfoBO loginUserInfoBO, FixDataCommonRequest fixDataCommonRequest);

    /**
     * 数据修正-冻结经费
     *
     * @param omsLoginUserInfoBO OMS登录用户信息
     * @param request            冻结经费请求参数
     */
    List<ErrorMsgVO> freezeFund(LoginUserInfoBO omsLoginUserInfoBO, FixFundDataRequest request);

    /**
     * 数据修正-解冻经费
     *
     * @param omsLoginUserInfoBO OMS登录用户信息
     * @param request            冻结经费请求参数
     */
    List<ErrorMsgVO> unfreezeFund(LoginUserInfoBO omsLoginUserInfoBO, FixFundDataRequest request);
}
