package com.ruijing.store.order.gateway.oms.financial.service;

import com.ruijing.store.order.gateway.oms.financial.request.FinancialDockingOrderQueryRequestDTO;
import com.ruijing.store.order.gateway.oms.financial.vo.FinancialDockingOrderDataVO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-04-12 11:40
 * @description:
 **/
public interface OmsFinancialDockingService {

    /**
     * 查询订单数据
     * @param financialDockingOrderQueryRequestDTO 请求
     * @return 返回
     */
    List<FinancialDockingOrderDataVO> queryOrderData(FinancialDockingOrderQueryRequestDTO financialDockingOrderQueryRequestDTO);
}
