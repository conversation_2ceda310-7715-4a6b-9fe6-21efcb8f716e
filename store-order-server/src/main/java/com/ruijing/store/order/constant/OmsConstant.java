package com.ruijing.store.order.constant;

import com.ruijing.fundamental.common.collections.New;

import java.util.Set;

/**
 * @author: chen<PERSON><PERSON><PERSON>g
 * @createTime: 2024-12-05 16:19
 * @description:
 **/
public interface OmsConstant {

    /**
     * 测试环境测试供应商
     */
    Set<String> TEST_SUPPLIER_CODE_SET = New.set("S0655");

    /**
     * 现网测试供应商
     */
    Set<String> PRO_SUPPLIER_CODE_TEST_SET = New.set("S0182", "S0347", "S0398", "S0565","S0655", "S0739", "S02008",
            "S058094", "S0101679", "S0656", "S064440", "S0118709", "S060780", "S083529", "S01720", "S0525", "S02365", "S0132045");

    /**
     * 测试环境测试单位
     */
    Integer TEST_DEMO_ORG_ID = 1;

    /**
     * 现网 演示单位
     */
    Integer PRO_DEMO_ORG_ID = 999;


}
