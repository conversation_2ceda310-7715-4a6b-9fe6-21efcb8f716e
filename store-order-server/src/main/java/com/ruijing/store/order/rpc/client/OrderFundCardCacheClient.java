package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.service.OrderFundCardCacheRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 订单经费卡，换卡，保存卡相关客户端
 * <AUTHOR>
 */
@ServiceClient
public class OrderFundCardCacheClient {

    @MSharpReference(remoteAppkey="order-galaxy-service")
    private OrderFundCardCacheRpcService orderFundCardCacheRpcService;

    /**
     * 保存/更改经费卡
     */
    @ServiceLog(description = "保存/更改经费卡", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void saveFundCardCache(List<OrderFundCardDTO> orderFundCardList) {
        Preconditions.notEmpty(orderFundCardList, "保存经费卡失败，经费卡对象为空！");
        RemoteResponse response = orderFundCardCacheRpcService.saveOrderFundCard(orderFundCardList);
        Preconditions.isTrue(response.isSuccess(), "保存/更改经费卡失败: " + JsonUtils.toJsonIgnoreNull(response));
    }

    /**
     * 查询订单换卡缓存记录
     * @param orderIdList
     * @return
     */
    @ServiceLog(description = "查询订单换卡缓存记录", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderFundCardDTO> findOrderFundCardCache(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }

        RemoteResponse<List<OrderFundCardDTO>> response = orderFundCardCacheRpcService.findByOrderIdDesc(orderIdList);
        Preconditions.isTrue(response.isSuccess(), "查询订单换卡缓存记录异常: " + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }
}
