package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/28 9:34
 */
@RpcModel("订单列表-合同上传模板")
public class OrderContractTemplateVO implements Serializable {

    private static final long serialVersionUID = 6594404236561938135L;

    /**
     * 合同模板名字
     */
    @RpcModelProperty("合同模板名字")
    String templateName;

    /**
     * 合同模板url
     */
    @RpcModelProperty("合同模板url")
    String templateUrl;

    public String getTemplateName() {
        return templateName;
    }

    public OrderContractTemplateVO setTemplateName(String templateName) {
        this.templateName = templateName;
        return this;
    }

    public String getTemplateUrl() {
        return templateUrl;
    }

    public OrderContractTemplateVO setTemplateUrl(String templateUrl) {
        this.templateUrl = templateUrl;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderContractTemplateVO{");
        sb.append("templateName='").append(templateName).append('\'');
        sb.append(", templateUrl='").append(templateUrl).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
