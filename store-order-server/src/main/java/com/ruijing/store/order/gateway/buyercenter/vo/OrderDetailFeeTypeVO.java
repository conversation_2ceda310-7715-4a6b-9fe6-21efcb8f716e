package com.ruijing.store.order.gateway.buyercenter.vo;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * @description: 订单报销经费类型模型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/29 14:51
 **/
public class OrderDetailFeeTypeVO implements Serializable {

    private static final long serialVersionUID = -2194687435818597753L;

    /**
     * 所属订单id
     */
    @RpcModelProperty("所属订单id")
    private Integer orderId;

    /**
     * 订单详情id
     */
    @RpcModelProperty("订单明细id，和orderId一对多")
    private Integer orderDetailId;

    /**
     * 报账类型标签, 有"实验耗材费"和"测试分析费"两种
     */
    @RpcModelProperty("报账类型标签, 有 实验耗材费 和 测试分析费 两种")
    private String feeTypeTag;

    /**
     * 分类标签，有 动物，耗材，试剂，服务 四类
     */
    @RpcModelProperty("分类标签，有 动物，耗材，试剂，服务 四类, 备用字段")
    private String categoryTag;

    /**
     * 报账费用金额
     */
    @RpcModelProperty("报账费用金额单价，根据feeTypeTag分类")
    private BigDecimal feePrice;

    @RpcModelProperty("商品数量，根据feeTypeTag分类")
    private BigDecimal quantity;

    @RpcModelProperty("报账费用金额总价，根据feeTypeTag分类")
    private BigDecimal totalPrice;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public void setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public BigDecimal getFeePrice() {
        return feePrice;
    }

    public void setFeePrice(BigDecimal feePrice) {
        this.feePrice = feePrice;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailFeeTypeVO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderDetailId=" + orderDetailId)
                .add("feeTypeTag='" + feeTypeTag + "'")
                .add("categoryTag='" + categoryTag + "'")
                .add("feePrice=" + feePrice)
                .add("quantity=" + quantity)
                .add("totalPrice=" + totalPrice)
                .toString();
    }
}
