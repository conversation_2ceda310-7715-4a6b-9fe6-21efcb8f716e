package com.ruijing.store.order.gateway.supplier.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.inner.buyer.FetchDataResponseItemDTO;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.DockingDataTypeEnum;
import com.reagent.order.enums.DockingNumberTypeEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.fundcard.dto.FreezeDTO;
import com.reagent.research.fundcard.dto.FundCardSplitOrderDTO;
import com.reagent.research.fundcard.enums.BusinessTypeEnum;
import com.reagent.research.fundcard.enums.SourceTypeEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.srm.api.support.enums.ShopGasBottleUnbindEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.SupplierAuthorityDTO;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderChildRequestDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderDetailChildRequestDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderSplitUpRequestDTO;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.service.impl.RefFundcardOrderServiceImpl;
import com.ruijing.store.order.base.core.translator.OrderAddressTranslator;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.GenerateOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.supplier.service.SupplierOrderManageService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.ListUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@CatAnnotation
public class SupplierOrderManageServiceImpl implements SupplierOrderManageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierOrderManageServiceImpl.class);

    /**
     * 拆分订单后，需要清理的详情key
     */
    private Set<String> NEED_CLEAR_DETAIL_EXTRA_KEY_AFTER_SPLIT = New.set(OrderDetailExtraEnum.BIND_GAS_BOTTLE_BARCODE.getValue());

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private GenerateOrderService generateOrderService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private FetchOrderDockingDataServiceClient fetchOrderDockingDataServiceClient;

    @Resource
    private OrderDockingNumberRpcClient orderDockingNumberRpcClient;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "订单拆单", serviceType = ServiceType.COMMON_SERVICE)
    public int orderSplitUp(OrderSplitUpRequestDTO request, RjSessionInfo rjSessionInfo) {
        LOGGER.info("商家操作拆单, 入参{}", JsonUtils.toJsonIgnoreNull(request));
        final String orderNo = request.getOrderNo();
        final List<OrderChildRequestDTO> children = request.getChildren();
        Preconditions.notNull(orderNo, "操作失败，母订单号orderNo不可为空");
        Preconditions.notEmpty(children, "操作失败，子单不可为空");
        Preconditions.isTrue(children.size() > 1, "操作失败，拆单数量必须大于1");
        Preconditions.isTrue(children.size() < 11, "操作失败，拆单数量不可超过10");
        // lock
        final String lockKey = "orderSplitUp";
        cacheClient.controlRepeatOperation(lockKey + ":" + orderNo, 3);

        // 被拆母单信息
        final OrderMasterDO parentOrder = orderMasterMapper.findByForderno(orderNo);
        final List<OrderDetailDO> parentOrderDetailList = orderDetailMapper.findByFmasterid(parentOrder.getId());
        List<RefFundcardOrderDTO> refFundCardOrderDTOList = refFundcardOrderService.findByOrderIdList(Collections.singletonList(parentOrder.getId()));
        // 填充额外信息
        refFundcardOrderService.fillWithNewDTO(refFundCardOrderDTOList);

        // 商家账号拆单发货权限校验
        validationTotal(request, parentOrderDetailList);

        validationOrderMetadata(parentOrder, parentOrderDetailList, refFundCardOrderDTOList);

        validationPermission(parentOrder);

        // 绑定关系重置，需在一物一码数据重置或订单商品详情删除前，否则绑定关系丢失
        this.resetBind(parentOrder);

        // 重新冻结后再生成子单
        List<GenerateOrderDTO> childOrderList = generateChildOrder(rjSessionInfo, parentOrder, parentOrderDetailList, refFundCardOrderDTOList, children);

        resetQRCode(parentOrder, childOrderList);

        handlerAfterSplit(parentOrder, childOrderList);

        // unlock
        cacheClient.removeCache(lockKey + ":" + orderNo);

        return children.size();
    }

    private void handlerAfterSplit(OrderMasterDO parentOrder, List<GenerateOrderDTO> childOrderList) {
        if (OrderCommonUtils.isSplitOrder(parentOrder.getForderno())) {
            // 子单再拆, 给始祖母单添加拆分日志
            String ancestorOrderNo = childOrderList.stream().map(GenerateOrderDTO::getOrderMasterDTO).map(OrderMasterDTO::getForderno).findFirst().map(no -> no.substring(0, no.length() - 1)).orElse("");
            OrderMasterDO ancestor = orderMasterMapper.findByForderno(ancestorOrderNo);
            if (ancestor != null) {
                addSplitLog(ancestor, childOrderList);
            }
        } else {
            addSplitLog(parentOrder, childOrderList);
        }
        invoiceClient.deleteInvoiceByOrderIds(Collections.singletonList(parentOrder.getId()));
    }

    private void addSplitLog(OrderMasterDO parentOrder, List<GenerateOrderDTO> childOrderList) {
        String childOrderNo = childOrderList.stream().map(GenerateOrderDTO::getOrderMasterDTO).map(OrderMasterDTO::getForderno).collect(Collectors.joining("，"));
        orderApprovalLogService.createOrderOperateLog(parentOrder.getId(), OrderApprovalEnum.ORDER_SPLIT_UP.getValue(), parentOrder.getFsuppid(), parentOrder.getFsuppname(), "生成订单：" + childOrderNo);
    }

    /**
     * 临床 重新生成二维码
     * @param parentOrder
     * @param childOrderList
     */
    private void resetQRCode(OrderMasterDO parentOrder, List<GenerateOrderDTO> childOrderList) {
        List<OrderUniqueBarCodeDTO> existBarcodeListInParent = orderUniqueBarCodeRPCClient.findByOrderNo(parentOrder.getForderno(), New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
        // 母单有生成一物一码的才处理
        if (CollectionUtils.isEmpty(existBarcodeListInParent)) {
            return;
        }

        Runnable task = () -> {
            List<String> orderNoList = childOrderList.stream().map(GenerateOrderDTO::getOrderMasterDTO).map(OrderMasterDTO::getForderno).collect(Collectors.toList());
            List<OrderMasterDO> byOrderNO = orderMasterMapper.findByFordernoIn(orderNoList);
            if (CollectionUtils.isNotEmpty(byOrderNO)) {
                orderUniqueBarCodeRPCClient.deleteByOrderNo(parentOrder.getForderno());
                // 一物一码的单位，重新生成二维码
                byOrderNO.forEach(orderMasterDO -> orderUniqueBarCodeRPCClient.generateBarCode(orderMasterDO.getId()));
            }
        };
        AsyncExecutor.listenableRunAsync(task)
                .addFailureCallback(ex -> {
                    LOGGER.error("拆单重新生成二维码失败:{}", ex);
                    Cat.logError("拆单重新生成二维码失败:", ex);
                });
    }

    /**
     * 重置绑定关系
     * @param parentOrder 父订单
     */
    private void resetBind(OrderMasterDO parentOrder){
        AsyncExecutor.runAsync(()-> gasBottleClient.unbindGasBottle(parentOrder.getId(), parentOrder.getForderno(), parentOrder.getFuserid(), ShopGasBottleUnbindEnum.SPLIT_ORDER));
    }

    /**
     * 解冻母单, 冻结新的子单
     * @param rjSessionInfo     商家会话信息
     * @param parentOrder       母订单
     * @param childOrderList    子订单
     */
    private void reFreezeFundCard(RjSessionInfo rjSessionInfo, OrderMasterDO parentOrder, List<GenerateOrderDTO> childOrderList) {
        if(OrgEnum.GUANG_ZHOU_SHI_YAN_SHI.getValue() == parentOrder.getFuserid()){
            // 广州实验室为例，不走冻结解冻的单据拆单不调用重新冻结解冻接口
            return;
        }
        List<RefFundcardOrderDTO> parentFundCardDOList = new ArrayList<>(childOrderList.get(0).getRefFundcardOrderDTOS());
        if (CollectionUtils.isEmpty(parentFundCardDOList)) {
            return;
        }
        OrgRequest<FundCardSplitOrderDTO> request = new OrgRequest<>();
        request.setDepartmentIds(Collections.singletonList(parentOrder.getFbuydepartmentid()));
        request.setOrgCode(parentOrder.getFusercode());
        FundCardSplitOrderDTO param = new FundCardSplitOrderDTO();
        param.setAppKey(Environment.getAppKey());
        param.setSerialNumber(parentOrder.getForderno());
        param.setSourceType(SourceTypeEnum.ORDER.getValue());
        param.setBusinessType(BusinessTypeEnum.BUY.getValue());
        param.setUserId(Optional.ofNullable(rjSessionInfo.getUserId(RjUserTypeEnum.SUPPLIER_USER)).map(Long::intValue).orElse(0));

        SupplierAuthorityDTO supplierInfo = supplierGoodsReturnService.getSupplierAuthority();
        OrderOperatorDTO operator = new OrderOperatorDTO();
        operator.setUserId(supplierInfo.getSupplierId());
        operator.setUserName(supplierInfo.getUserName());
        Date nowDate = new Date();

        // 解冻的母单经费卡信息
        Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardDoMap = DictionaryUtils.groupBy(parentFundCardDOList, it -> Integer.parseInt(it.getOrderId()));
        List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(parentOrder.getId());
        param.setUnfreezeDTO(RefFundcardOrderServiceImpl.parsingRefToUnFreezeDto(operator, orderIdFundCardDoMap, nowDate, parentOrder, MapUtils.EMPTY_MAP, detailDOList));

        // 重新冻结的经费卡关联关系
        List<FreezeDTO> freezeList = childOrderList.stream()
                .map(it -> {
                    OrderMasterDTO childOrder = it.getOrderMasterDTO();
                    List<OrderDetailDO> childOrderDetails = it.getGenerateOrderDetailDTOList().stream().map(OrderDetailTranslator::generateDtoToOrderDetailDO).collect(Collectors.toList());
                    FreezeDTO freezeDTO = RefFundcardOrderServiceImpl.parsingRefToFreezeDto(operator, Collections.emptyMap(), nowDate, OrderMasterTranslator.dtoToOrderMasterDO(childOrder), childOrderDetails);
                    BigDecimal payTotalMoney = childOrder.getForderamounttotal().subtract(BigDecimal.valueOf(childOrder.getReturnAmount()));
                    freezeDTO.setSelectFundCardDTOS(it.getRefFundcardOrderDTOS().stream().map(card -> RefFundcardOrderTranslator.refToSelectFundCardDto(card, payTotalMoney)).collect(Collectors.toList()));
                    return freezeDTO;
                }).collect(Collectors.toList());
        param.setFreezeListDTO(freezeList);
        request.setData(param);

        researchFundCardServiceClient.fundCardSplitOrder(request);
    }

    /**
     * 生成子订单单号
     * @param parentOrderNo
     * @param count
     * @return
     */
    private String[] generateChildOrderNo(String parentOrderNo, int count) {
        String latestCharacter = parentOrderNo.substring(parentOrderNo.length() - 1);
        String[] orderNoChildren = null;
        if (OrderCommonUtils.isUpperCaseWithSingle(latestCharacter)) {
            List<OrderMasterDO> sameOrderNoLike = orderMasterMapper.findBySameOrderNoLike(parentOrderNo.substring(0, parentOrderNo.length() - 1));
            // 如果 (全部子单的数量) + (新拆的子单数量) > 10, 拦截
            if ((sameOrderNoLike.size() - 1) + (count - 1) > 10) {
                throw new IllegalStateException("超过订单：" + parentOrderNo.substring(0, parentOrderNo.length() - 1) + "最大拆单数10，请重试");
            }
            // 多次拆单
            orderNoChildren = OrderCommonUtils.generateChildOrderByMaximumCharacter(ListUtils.toList(sameOrderNoLike, OrderMasterDO::getForderno), parentOrderNo, count);
        } else {
            if (count > 10) {
                throw new IllegalStateException("超过订单：" + parentOrderNo + "最大拆单数10，请重试");
            }
            // 首次拆单
            orderNoChildren = OrderCommonUtils.generateChildOrder(parentOrderNo, count);
        }
        return orderNoChildren;
    }

    /**
     * 生成新的子订单
     * @param parentOrder           母订单
     * @param parentOrderDetailList 母订单详情
     * @param children              孩子订单
     */
    @ServiceLog
    private List<GenerateOrderDTO> generateChildOrder(RjSessionInfo rjSessionInfo, OrderMasterDO parentOrder, List<OrderDetailDO> parentOrderDetailList, List<RefFundcardOrderDTO> fundCardList, List<OrderChildRequestDTO> children) {
        List<String> parentOrderDetailIds = parentOrderDetailList.stream().map(d->d.getId().toString()).collect(Collectors.toList());
        List<FetchDataResponseItemDTO> multiSpecItems = fetchOrderDockingDataServiceClient.fetchOrderExtraBigData(parentOrderDetailIds, DockingDataTypeEnum.MULTIPLE_SPECIFICATIONS_PRODUCTS);
        Map<Integer, String> detailIdMultiSpecMap = DictionaryUtils.toMap(multiSpecItems, item ->Integer.parseInt(item.getOrderNo()), FetchDataResponseItemDTO::getData);
        // 订单额外信息
        CompletableFuture<List<BaseOrderExtraDTO>> orderExtraListFuture = AsyncExecutor.callAsync(
                () -> orderExtraClient.selectByOrderIdIn(New.list(parentOrder.getId())));
        // 地址
        CompletableFuture<OrderAddressDTO> addressFuture = AsyncExecutor.callAsync(
                () -> orderAddressRPCClient.findByOrderNo(parentOrder.getForderno()));
        // 子订单号
        CompletableFuture<String[]> childOrderNoListFuture = AsyncExecutor.callAsync(() -> {
            final String parentOrderNo = parentOrder.getForderno();
            return generateChildOrderNo(parentOrderNo, children.size());
        });
        CompletableFuture<List<OrderDetailExtraDTO>> orderDetailExtraFuture = AsyncExecutor.callAsync(() -> orderDetailExtraClient.listOrderDetailExtra(New.list(parentOrder.getId()), null));
        // 异步任务等待结束
        CompletableFuture.allOf(orderExtraListFuture, addressFuture, childOrderNoListFuture).join();
        List<BaseOrderExtraDTO> orderExtraList = New.list();
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        String[] childOrderNoList = new String[]{};
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = New.list();
        try {
            orderDetailExtraDTOList = orderDetailExtraFuture.get();
            orderExtraList = orderExtraListFuture.get();
            addressDTO = addressFuture.get();
            childOrderNoList = childOrderNoListFuture.get();
        } catch (Exception e) {
            Preconditions.isTrue(false, "拆单失败，获取原订单信息失败。" + e.getMessage());
        }
        // 组装订单详情额外信息与商品的关联map
        Map<Long, List<OrderDetailExtraDTO>> productId2OderDetailExtraMap = New.map();
        if(CollectionUtils.isNotEmpty(orderDetailExtraDTOList)) {
            Map<Integer, List<OrderDetailExtraDTO>> orderDetailId2OrderDetailExtraMap = orderDetailExtraDTOList.stream()
                    .filter(orderDetailExtraDTO -> !NEED_CLEAR_DETAIL_EXTRA_KEY_AFTER_SPLIT.contains(orderDetailExtraDTO.getExtraKey()))
                    .collect(Collectors.groupingBy(OrderDetailExtraDTO::getOrderDetailId));
            for(OrderDetailDO orderDetailDO : parentOrderDetailList){
                productId2OderDetailExtraMap.put(orderDetailDO.getProductSn(), orderDetailId2OrderDetailExtraMap.get(orderDetailDO.getId()));
            }
        }
        // 外部管理平台订单号，拆单后带出
        String outerBuyerOrderNo = this.getParentDockingNumber(parentOrder.getForderno(), DockingNumberTypeEnum.ORDER);
        // 外部管理平台送货单号，拆单后带出
        String outerBuyerDeliveryQrCode = this.getParentExtraBigData(parentOrder.getForderno(), DockingDataTypeEnum.OUTER_BUYER_DELIVERY_QR_CODE);

        // 组装生成订单信息
        List<GenerateOrderDTO> generateOrderDTOList = new ArrayList<>(children.size());
        for (int i = 0; i < childOrderNoList.length; i++) {
            GenerateOrderDTO item = new GenerateOrderDTO();
            OrderChildRequestDTO childRequest = children.get(i);
            item.setOrderMasterDTO(wrapperOrderMaster(parentOrder, childOrderNoList[i]));
            item.setGenerateOrderDetailDTOList(wrapperOrderDetail(item.getOrderMasterDTO(), parentOrderDetailList, childRequest, productId2OderDetailExtraMap, detailIdMultiSpecMap));
            item.setRefFundcardOrderDTOS(wrapperFundCard(fundCardList, item.getOrderMasterDTO()));
            GenerateOrderAddressDTO generateOrderAddressDTO = OrderAddressTranslator.dtoToGenerateDTO(addressDTO);
            if (StringUtils.isBlank(generateOrderAddressDTO.getAddress())) {
                generateOrderAddressDTO.setAddress(item.getOrderMasterDTO().getFbiderdeliveryplace());
            }
            item.setGenerateOrderAddressDTO(generateOrderAddressDTO);
            item.setGenerateOrderExtraDTOList(this.wrapperOrderExtraList(orderExtraList, item.getGenerateOrderDetailDTOList()));
            item.setDockingNumber(outerBuyerOrderNo);
            item.setOuterBuyerDeliveryQrCode(outerBuyerDeliveryQrCode);
            generateOrderDTOList.add(item);
        }
        // 重新冻结
        reFreezeFundCard(rjSessionInfo, parentOrder, generateOrderDTOList);
        // 更新并生成订单
        if (OrderCommonUtils.isSplitOrder(parentOrder.getForderno())) {
            // 更新子母单状态，子单再被拆时，在上面已经更新了对应的订单，不需要重复生成，但需要更新一些关联信息
            GenerateOrderDTO parentGenerateOrderDTO = generateOrderDTOList.remove(0);
            // 更新子母单主表信息
            OrderMasterDTO updateDTO = parentGenerateOrderDTO.getOrderMasterDTO();
            OrderMasterDO orderMasterDO = new OrderMasterDO();
            orderMasterDO.setId(parentOrder.getId());
            orderMasterDO.setForderno(updateDTO.getForderno());
            orderMasterDO.setOriginalAmount(updateDTO.getOriginalAmount());
            orderMasterDO.setForderamounttotal(updateDTO.getForderamounttotal());
            orderMasterDO.setCarryFee(updateDTO.getCarryFee());
            orderMasterMapper.updateByPrimaryKeySelective(orderMasterDO);
            // 更新t_ref_fundcard_order的数据
            RefFundcardOrderDTO refFundcardOrderDTO = new RefFundcardOrderDTO();
            refFundcardOrderDTO.setOrderId(orderMasterDO.getId().toString());
            refFundcardOrderDTO.setFreezeAmount(orderMasterDO.getForderamounttotal());
            refFundcardOrderDTO.setUsemoney(orderMasterDO.getForderamounttotal());
            refFundcardOrderService.updateRefFundcardOrderByOrderId(refFundcardOrderDTO);
            // 更新 galaxy的ref_fund_card_order表，经费支出申请单号不会变，无需更新
            RefOrderFundCardDTO refOrderFundCardDTO = new RefOrderFundCardDTO();
            refOrderFundCardDTO.setOrderId(orderMasterDO.getId());
            refOrderFundCardDTO.setFreezeAmount(orderMasterDO.getForderamounttotal());
            refOrderFundCardDTO.setUseAmount(orderMasterDO.getOriginalAmount());
            refFundCardOrderClient.updateByOrderId(refOrderFundCardDTO);
            // 删除再插入子母单orderExtra以移除商品的广告信息
            orderExtraClient.deleteInOrderId(New.list(parentOrder.getId()));
            List<GenerateOrderExtraDTO> generateOrderExtraDTOList = parentGenerateOrderDTO.getGenerateOrderExtraDTOList();
            if(CollectionUtils.isNotEmpty(generateOrderExtraDTOList)) {
                orderExtraClient.insertList(generateOrderExtraDTOList.stream().map(generateOrderExtraDTO -> {
                    BaseOrderExtraDTO baseOrderExtraDTO = new BaseOrderExtraDTO();
                    baseOrderExtraDTO.setOrderId(parentOrder.getId());
                    baseOrderExtraDTO.setOrderNo(parentOrder.getForderno());
                    baseOrderExtraDTO.setOrgId(parentOrder.getFuserid());
                    baseOrderExtraDTO.setExtraKey(generateOrderExtraDTO.getField().getValue());
                    baseOrderExtraDTO.setExtraValue(generateOrderExtraDTO.getValue());
                    baseOrderExtraDTO.setExtraKeyDesc(generateOrderExtraDTO.getField().getDesc());
                    return baseOrderExtraDTO;
                }).collect(Collectors.toList()));
            }
            // 处理子母单详情相关信息
            List<GenerateOrderDetailDTO> generateOrderDetailDTOList = parentGenerateOrderDTO.getGenerateOrderDetailDTOList();
            // 先删除orderDetailExtra信息
            orderDetailExtraClient.deleteOrderDetailExtra(New.list(parentOrder.getId()), null);
            // 再插入orderDetailExtra信息
            List<OrderDetailExtraDTO> parentOrderDetailExtraList = New.list();
            generateOrderDetailDTOList.forEach(generateOrderDetailDTO -> {
                List<OrderDetailExtraDTO> partitionOrderDetailExtraList = generateOrderDetailDTO.getOrderDetailExtraDTOList();
                if(CollectionUtils.isNotEmpty(partitionOrderDetailExtraList)){
                    partitionOrderDetailExtraList.forEach(orderDetailExtraDTO -> {
                        orderDetailExtraDTO.setOrderDetailId(generateOrderDetailDTO.getId());
                        orderDetailExtraDTO.setOrderId(parentOrder.getId());
                    });
                    parentOrderDetailExtraList.addAll(partitionOrderDetailExtraList);
                }
            });
            if(CollectionUtils.isNotEmpty(parentOrderDetailExtraList)){
                parentOrderDetailExtraList.forEach(orderDetailExtraDTO -> orderDetailExtraDTO.setOrderId(parentOrder.getId()));
                orderDetailExtraClient.batchInsertOrderDetailExtra(parentOrderDetailExtraList);
            }
            // 更新子母单详情
            for(GenerateOrderDetailDTO generateOrderDetailDTO : generateOrderDetailDTOList){
                OrderDetailDO orderDetailDO = new OrderDetailDO();
                orderDetailDO.setId(generateOrderDetailDTO.getId());
                orderDetailDO.setFdetailno(generateOrderDetailDTO.getFdetailno());
                orderDetailDO.setFquantity(generateOrderDetailDTO.getFquantity());
                orderDetailDO.setOriginalAmount(generateOrderDetailDTO.getOriginalAmount());
                orderDetailDO.setFbidamount(generateOrderDetailDTO.getFbidamount());
                orderDetailMapper.updateByPrimaryKeySelective(orderDetailDO);
            }
            // 删除数量为0的子母单详情
            List<Integer> orderDetailIdList = generateOrderDetailDTOList.stream().map(GenerateOrderDetailDTO::getId).collect(Collectors.toList());
            List<Integer> deleteList = orderDetailMapper.findByFmasterid(orderMasterDO.getId()).stream().map(OrderDetailDO::getId).collect(Collectors.toList())
                    .stream().filter(detailId -> !orderDetailIdList.contains(detailId)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(deleteList)) {
                orderDetailMapper.deleteInId(deleteList);
            }
            // 生成其它子单
            generateOrderService.generateNewOrder(generateOrderDTOList);
            // 子单生成后，重新将母单加入返回列表中
            generateOrderDTOList.add(parentGenerateOrderDTO);
        }else {
            // 更新母单状态
            OrderMasterDO updated = new OrderMasterDO();
            updated.setId(parentOrder.getId());
            updated.setStatus(OrderStatusEnum.ORDER_SPLIT_UP.getValue());
            updated.setShutDownDate(new Date());
            orderMasterMapper.updateByPrimaryKeySelective(updated);
            // 生成子单
            generateOrderService.generateNewOrder(generateOrderDTOList);
        }
        return generateOrderDTOList;
    }

    /**
     * order extra list转换成order extra do list，用于存储
     *
     * @param orderExtraList
     * @param orderDetailDTOList
     * @return
     */
    private List<GenerateOrderExtraDTO> wrapperOrderExtraList(List<BaseOrderExtraDTO> orderExtraList, List<GenerateOrderDetailDTO> orderDetailDTOList) {
        List<OrderDetailExtraDTO> orderDetailExtraList = orderDetailDTOList.stream().map(GenerateOrderDetailDTO::getOrderDetailExtraDTOList).reduce(new ArrayList<>(), (olist, nlist) -> {
            if(CollectionUtils.isNotEmpty(nlist)) {
                olist.addAll(nlist);
            }
            return olist;
        }).stream().filter(orderDetailDTO -> OrderDetailExtraEnum.ADVERTISEMENT_ID.getValue().equals(orderDetailDTO.getExtraKey())).collect(Collectors.toList());
        List<GenerateOrderExtraDTO> resList = New.listWithCapacity(orderExtraList.size());
        for (BaseOrderExtraDTO orderExtra : orderExtraList) {
            if(orderExtra.getExtraKey().equals(OrderExtraEnum.ADVERTISEMENT_ID.getValue())){
                // 若为广告额外信息且对应的商品已被拆分到其它订单，需要移除该orderExtra
                if(CollectionUtils.isEmpty(orderDetailExtraList.stream().filter(orderDetailDTO -> orderDetailDTO.getExtraValue().equals(orderExtra.getExtraValue())).collect(Collectors.toList()))){
                    continue;
                }
            }
            GenerateOrderExtraDTO orderExtraDTO = new GenerateOrderExtraDTO();
            orderExtraDTO.setField(OrderExtraEnum.getByValue(orderExtra.getExtraKey()));
            orderExtraDTO.setValue(orderExtra.getExtraValue());
            resList.add(orderExtraDTO);

        }
        return resList;
    }

    /**
     * 封装子订单的绑卡数组
     * @param fundCardList  旧的卡数据
     * @return              新的卡数据
     */
    private List<RefFundcardOrderDTO> wrapperFundCard(List<RefFundcardOrderDTO> fundCardList, OrderMasterDTO order) {
        List<RefFundcardOrderDTO> resultList = New.listWithCapacity(fundCardList.size());
        fundCardList.forEach(card ->{
            RefFundcardOrderDTO newCard = RefFundcardOrderTranslator.cloneDTO(card);
            newCard.setFreezeAmount(order.getForderamounttotal());
            newCard.setUsemoney(order.getForderamounttotal());
            resultList.add(newCard);
        });
        return resultList;
    }

    /**
     * 封装子订单的商品数组，以及子订单支付金额
     *
     * @param orderMasterDTO
     * @param parentOrderDetailList
     * @param childRequest
     * @param productId2OderDetailExtraMap
     * @return
     */
    private List<GenerateOrderDetailDTO> wrapperOrderDetail(OrderMasterDTO orderMasterDTO, List<OrderDetailDO> parentOrderDetailList, OrderChildRequestDTO childRequest, Map<Long, List<OrderDetailExtraDTO>> productId2OderDetailExtraMap, Map<Integer, String> detailIdMultiSpecMap) {
        Map<Integer, Integer> detailIdSplitTotalMap = DictionaryUtils.toMap(childRequest.getChildrenDetailList(), OrderDetailChildRequestDTO::getDetailId, OrderDetailChildRequestDTO::getTotal);
        return parentOrderDetailList.stream()
                // 取得当前的拆分的子订单拆分对应商品
                .filter(it -> detailIdSplitTotalMap.containsKey(it.getId()))
                .map(it -> {
                            BigDecimal quantity = BigDecimal.valueOf(Optional.ofNullable(detailIdSplitTotalMap.get(it.getId())).orElse(0));
                            it.setFquantity(quantity);
                            it.setOriginalPrice(it.getOriginalPrice());
                            it.setOriginalAmount(quantity.multiply(it.getOriginalPrice()));
                            it.setFbidprice(it.getFbidprice());
                            it.setFbidamount(quantity.multiply(it.getFbidprice()));
                            it.setFdetailno(orderMasterDTO.getForderno());
                            orderMasterDTO.setOriginalAmount(orderMasterDTO.getOriginalAmount().add(quantity.multiply(it.getOriginalPrice())));
                            orderMasterDTO.setForderamounttotal(orderMasterDTO.getForderamounttotal().add(quantity.multiply(it.getFbidprice())));
                            if (it.getCarryFee() != null && it.getCarryFee().compareTo(BigDecimal.ZERO) > 0) {
                                orderMasterDTO.setCarryFee(orderMasterDTO.getCarryFee().add(quantity.multiply(it.getCarryFee())));
                            }
                            GenerateOrderDetailDTO orderDetailDTO = OrderDetailTranslator.orderDetailDO2GenerateDTO(it);
                            orderDetailDTO.setAttribute(detailIdMultiSpecMap.get(it.getId()));
                            // 组装订单详情额外信息
                            List<OrderDetailExtraDTO> orderDetailExtraDTOList = productId2OderDetailExtraMap.get(it.getProductSn());
                            if(CollectionUtils.isNotEmpty(orderDetailExtraDTOList)){
                                orderDetailDTO.setOrderDetailExtraDTOList(orderDetailExtraDTOList.stream().map(orderDetailExtraDTO -> {
                                    OrderDetailExtraDTO detailExtra = new OrderDetailExtraDTO();
                                    detailExtra.setOrgId(orderMasterDTO.getFuserid());
                                    detailExtra.setExtraKey(orderDetailExtraDTO.getExtraKey());
                                    detailExtra.setExtraValue(orderDetailExtraDTO.getExtraValue());
                                    OrderDetailExtraEnum orderDetailExtraEnum = OrderDetailExtraEnum.getByValue(orderDetailExtraDTO.getExtraKey());
                                    if(orderDetailExtraEnum != null) {
                                        detailExtra.setExtraKeyType(orderDetailExtraEnum.getType());
                                    }
                                    return detailExtra;
                                }).collect(Collectors.toList()));
                            }
                            return orderDetailDTO;
                        }
                ).collect(Collectors.toList());
    }

    /**
     * 封装子订单
     * @param parentOrder
     * @param childOrderNo
     * @return
     */
    private OrderMasterDTO wrapperOrderMaster(OrderMasterDO parentOrder, String childOrderNo) {
        OrderMasterDTO result = OrderMasterTranslator.orderMasterDO2OrderMasterDTO(parentOrder);
        result.setId(null);
        result.setForderno(childOrderNo);
        result.setOriginalAmount(BigDecimal.ZERO);
        result.setForderamounttotal(BigDecimal.ZERO);
        result.setCarryFee(BigDecimal.ZERO);
        return result;
    }

    /**
     * 查询OMS拆单配置并校验是否开启
     * @param parentOrder
     */
    private void validationPermission(OrderMasterDO parentOrder ) {
        String splitPermission = sysConfigClient.getConfigByOrgCodeAndConfigCode(parentOrder.getFusercode(), ConfigConstant.SUPPLIER_SPLIT_ORDER_PERMISSION);
        Preconditions.isTrue(ConfigConstant.SUPPLIER_SPLIT_ORDER_PERMISSION_ENABLE.equals(splitPermission), "该单位未配置允许商家拆单，请联系客服");
    }

    /**
     * 危化品不能拆单
     * @param orderDetailList
     */
    private void validationOrderMetadata(OrderMasterDO parentOrder, List<OrderDetailDO> orderDetailList, List<RefFundcardOrderDTO> refFundCardOrderDTOList) {
        Preconditions.isTrue(OrderStatusEnum.WaitingForDelivery.getValue().equals(parentOrder.getStatus()), "操作失败，只有待发货状态可以拆单，当前订单状态为：" + Objects.requireNonNull(OrderStatusEnum.get(parentOrder.getStatus())).getName());
        // 允许拆单的危化品类型
        Set<Integer> allowDangerousTypeSet = New.set(DangerousTypeEnum.NORMAL_DANGEROUS.getValue(), DangerousTypeEnum.VIRUS.getValue(),
                DangerousTypeEnum.BACTERIAL_STRAIN.getValue(), DangerousTypeEnum.RADIOISOTOPE.getValue());
        // 除允许拆单的危化品类型外，其他危化品要限制拆单
        boolean hadDangerousProduct = orderDetailList.stream().anyMatch(it ->
                !allowDangerousTypeSet.contains(it.getDangerousTypeId()) &&
                        DangerousTypeEnum.isDangerousType(it.getDangerousTypeId()));
        BusinessErrUtil.isTrue(!hadDangerousProduct, ExecptionMessageEnum.HAZARDOUS_CHEMICALS_NO_SPLIT);
        Preconditions.isTrue(refFundCardOrderDTOList == null || refFundCardOrderDTOList.size() <= 1, "该订单绑定了多个经费，不支持拆单发货。");
        if (OrderCommonUtils.isSplitOrder(parentOrder.getForderno())){
            OrderEventStatusRequestDTO orderEventStatusRequestDTO = new OrderEventStatusRequestDTO();
            orderEventStatusRequestDTO.setOrderNoList(New.list(parentOrder.getForderno()));
            orderEventStatusRequestDTO.setOrderPushEventEnumList(New.list(OrderPushEventEnum.NOTICE_ORDER_CREATE_TO_OUTER_BUYER));
            List<OrderEventStatusResponseDTO> orderEventStatusDTOList = orderPushEventStatusClient.listEventPushStatus(orderEventStatusRequestDTO);
            BusinessErrUtil.isEmpty(orderEventStatusDTOList, "该订单仅允许拆分一次");
        }
    }

    /**
     * 校验拆分和采购数量是否吻合
     * @param request
     * @param orderDetailList
     */
    public void validationTotal(OrderSplitUpRequestDTO request, List<OrderDetailDO> orderDetailList) {
        Map<Integer, Integer> detailIdTotalMap = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetailDO::getId, Collectors.summingInt(it -> it.getFquantity().intValue())));
        Map<Integer, Integer> splitDetailTotalMap = request.getChildren().stream()
                .map(OrderChildRequestDTO::getChildrenDetailList)
                .peek(item-> BusinessErrUtil.notEmpty(item, "拆单失败，每个新的订单下必须选择需拆单的商品！"))
                .flatMap(List::stream)
                .peek(it -> Preconditions.isTrue(it.getTotal() > 0, "拆单失败，detailId：" + it.getDetailId() + " 拆分数量必须大于0"))
                .collect(Collectors.groupingBy(OrderDetailChildRequestDTO::getDetailId, Collectors.summingInt(OrderDetailChildRequestDTO::getTotal)));
        // 以原订单商品校验，如果有数量对不上的，抛出异常
        detailIdTotalMap.forEach((detailId, purchaseTotal) -> {
            Integer splitTotal = Optional.ofNullable(splitDetailTotalMap.get(detailId)).orElse(0);
            BusinessErrUtil.isTrue(purchaseTotal.equals(splitTotal), ExecptionMessageEnum.SPLIT_ITEMS_INCORRECT, detailId, purchaseTotal, splitTotal);
        });
    }

    private String getParentDockingNumber(String parentOrderNo, DockingNumberTypeEnum dockingNumberTypeEnum){
        List<OrderDockingNumberDTO> orderDockingNumberDTOList = orderDockingNumberRpcClient.listByParam(New.list(parentOrderNo), dockingNumberTypeEnum);
        if(CollectionUtils.isEmpty(orderDockingNumberDTOList)){
            return null;
        }
        return orderDockingNumberDTOList.get(0).getDockingNumber();
    }

    private String getParentExtraBigData(String parentOrderNo, DockingDataTypeEnum dockingDataTypeEnum){
        List<FetchDataResponseItemDTO> list = fetchOrderDockingDataServiceClient.fetchOrderExtraBigData(New.list(parentOrderNo), dockingDataTypeEnum);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0).getData();
    }
}
