package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

@Model("订单扫码退货入参")
public class OrderBarCodeReturnRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单号")
    private String orderNo;

    @ModelProperty("码商品信息, 退货时必填")
    private List<GoodsReturnDetailRequest> orderUniqueBarCodeList;

    @ModelProperty(value = "退货原因", description = "退货单粒度")
    private String returnReason;

    @ModelProperty(value = "退货说明", description = "退货单粒度")
    private String remark;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<GoodsReturnDetailRequest> getOrderUniqueBarCodeList() {
        return orderUniqueBarCodeList;
    }

    public void setOrderUniqueBarCodeList(List<GoodsReturnDetailRequest> orderUniqueBarCodeList) {
        this.orderUniqueBarCodeList = orderUniqueBarCodeList;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "OrderBarCodeReturnRequest{" +
                "orderNo='" + orderNo + '\'' +
                ", orderUniqueBarCodeList=" + orderUniqueBarCodeList +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}