<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.RefOrderDetailTagDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.RefOrderDetailTagDO">
    <!--@mbg.generated-->
    <!--@Table t_ref_order_detail_tag-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="ref_id" jdbcType="VARCHAR" property="refId" />
    <result column="tag_value" jdbcType="INTEGER" property="tagValue" />
    <result column="tag_name" jdbcType="VARCHAR" property="tagName" />
    <result column="tag_type" jdbcType="INTEGER" property="tagType" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="deletion_time" jdbcType="TIMESTAMP" property="deletionTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ref_id, tag_value, tag_name, tag_type, creation_time, update_time, is_deleted, 
    deletion_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_ref_order_detail_tag
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_ref_order_detail_tag
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.RefOrderDetailTagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_ref_order_detail_tag (ref_id, tag_value, tag_name, 
      tag_type, creation_time, update_time, 
      is_deleted, deletion_time)
    values (#{refId,jdbcType=VARCHAR}, #{tagValue,jdbcType=INTEGER}, #{tagName,jdbcType=VARCHAR}, 
      #{tagType,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=BIT}, #{deletionTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.RefOrderDetailTagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_ref_order_detail_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refId != null">
        ref_id,
      </if>
      <if test="tagValue != null">
        tag_value,
      </if>
      <if test="tagName != null">
        tag_name,
      </if>
      <if test="tagType != null">
        tag_type,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="deletionTime != null">
        deletion_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refId != null">
        #{refId,jdbcType=VARCHAR},
      </if>
      <if test="tagValue != null">
        #{tagValue,jdbcType=INTEGER},
      </if>
      <if test="tagName != null">
        #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="tagType != null">
        #{tagType,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deletionTime != null">
        #{deletionTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.RefOrderDetailTagDO">
    <!--@mbg.generated-->
    update t_ref_order_detail_tag
    <set>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=VARCHAR},
      </if>
      <if test="tagValue != null">
        tag_value = #{tagValue,jdbcType=INTEGER},
      </if>
      <if test="tagName != null">
        tag_name = #{tagName,jdbcType=VARCHAR},
      </if>
      <if test="tagType != null">
        tag_type = #{tagType,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="deletionTime != null">
        deletion_time = #{deletionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.core.model.RefOrderDetailTagDO">
    <!--@mbg.generated-->
    update t_ref_order_detail_tag
    set ref_id = #{refId,jdbcType=VARCHAR},
      tag_value = #{tagValue,jdbcType=INTEGER},
      tag_name = #{tagName,jdbcType=VARCHAR},
      tag_type = #{tagType,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      deletion_time = #{deletionTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-03-19-->
  <select id="findByRefIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ref_order_detail_tag
    where ref_id in
    <foreach item="item" index="index" collection="refIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2020-08-11-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_ref_order_detail_tag(
        ref_id,
        tag_value,
        tag_name,
        tag_type,
        creation_time,
        update_time,
        is_deleted,
        deletion_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.refId,jdbcType=VARCHAR},
            #{element.tagValue,jdbcType=INTEGER},
            #{element.tagName,jdbcType=VARCHAR},
            #{element.tagType,jdbcType=INTEGER},
            <choose>
                <when test="element.creationTime != null">
                    #{element.creationTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            <choose>
                <when test="element.updateTime != null">
                    #{element.updateTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            <choose>
                <when test="element.isDeleted != null">
                    #{element.isDeleted,jdbcType=BIT},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            #{element.deletionTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>