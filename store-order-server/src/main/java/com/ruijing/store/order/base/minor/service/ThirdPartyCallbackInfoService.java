package com.ruijing.store.order.base.minor.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.ThirdPartyCallbackInfoDTO;

/**
 * @description: 第三方回调信息表
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/9/30 14:11
 **/
public interface ThirdPartyCallbackInfoService {

    /**
     * 插入 第三方回调信息记录
     * @param infoDTO
     * @return
     */
    RemoteResponse insertCallbackInfo(ThirdPartyCallbackInfoDTO infoDTO);

    /**
     * 更新 第三方回调信息记录
     * @param infoDTO
     * @return
     */
    RemoteResponse updateCallbackInfoById(ThirdPartyCallbackInfoDTO infoDTO);

    /**
     * 更新 第三方回调信息记录
     * @param msg
     * @param orgCode 单位编号
     * @param num 单据编号
     * @return
     */
    RemoteResponse insertCallbackInfo(String msg,String orgCode,String num);
}
