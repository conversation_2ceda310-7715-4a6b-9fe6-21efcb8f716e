package com.ruijing.store.order.base.orderlog.model;

public class OrderLogExtraDO {
    /**
    * id
    */
    private Long id;

    /**
    * 订单日志id
    */
    private Long logId;

    /**
    * 修改字段
    */
    private String field;

    /**
    * 旧值
    */
    private String oldValue;

    /**
    * 新值
    */
    private String newValue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", logId=").append(logId);
        sb.append(", field=").append(field);
        sb.append(", oldValue=").append(oldValue);
        sb.append(", newValue=").append(newValue);
        sb.append("]");
        return sb.toString();
    }
}