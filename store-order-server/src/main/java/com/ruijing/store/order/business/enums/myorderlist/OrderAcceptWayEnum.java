package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/18 16:08
 * @Description
 **/
public enum OrderAcceptWayEnum {

    SELF_ACCEPTANCE(0,"自行验收"),
    CROSS_ACCEPTANCE(1,"交叉验收");

/*    SELF_PHOTO_ACCEPTANCE(2,"自行拍照验收入库"),
    CROSS_PHOTO_ACCEPTANCE(3,"交叉拍照验收入库");*/

    public final Integer value;

    public final String name;

    public static String CONFIG_CODE_PROCUREMENT_ACCEPTANCE_WAY = "PROCUREMENT_ACCEPTANCE_WAY";

    OrderAcceptWayEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return name;
    }

    public static final OrderAcceptWayEnum getByName(String name) {
        for (OrderAcceptWayEnum orderAcceptWayEnum : OrderAcceptWayEnum.values()) {
            if (orderAcceptWayEnum.name.equals(name)){
                return orderAcceptWayEnum;
            }
        }
        return null;
    }

    public static final OrderAcceptWayEnum getByValue(Integer value) {
        for (OrderAcceptWayEnum orderAcceptWayEnum : OrderAcceptWayEnum.values()) {
            if (orderAcceptWayEnum.value.equals(value)){
                return orderAcceptWayEnum;
            }
        }
        return null;
    }
}
