package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26 14:39
 * @Description
 **/
public class OrderAcceptCommentVO implements Serializable {

    private static final long serialVersionUID = 2129893930972561610L;

    /**
     * 订单验收评价id列表
     */
    @RpcModelProperty("订单验收评价id列表")
    private List<Integer> acceptCommentTagList;

    /**
     * 订单主表id
     */
    @RpcModelProperty("订单主表id")
    private Integer orderId;

    /**
     * 订单验收评价列表
     */
    @RpcModelProperty("订单验收评价列表")
    private List<String> acceptCommentList;

    /**
     * 单位id
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    public List<Integer> getAcceptCommentTagList() {
        return acceptCommentTagList;
    }

    public OrderAcceptCommentVO setAcceptCommentTagList(List<Integer> acceptCommentTagList) {
        this.acceptCommentTagList = acceptCommentTagList;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderAcceptCommentVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public List<String> getAcceptCommentList() {
        return acceptCommentList;
    }

    public OrderAcceptCommentVO setAcceptCommentList(List<String> acceptCommentList) {
        this.acceptCommentList = acceptCommentList;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderAcceptCommentVO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderAcceptCommentVO{");
        sb.append("acceptCommentTagList=").append(acceptCommentTagList);
        sb.append(", orderId=").append(orderId);
        sb.append(", acceptCommentList=").append(acceptCommentList);
        sb.append(", orgId=").append(orgId);
        sb.append('}');
        return sb.toString();
    }
}
