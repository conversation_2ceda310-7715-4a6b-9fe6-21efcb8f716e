package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.wms.api.dto.ApprovalProgressDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDetailDTO;
import com.ruijing.store.wms.api.dto.CountDTO;
import com.ruijing.store.wms.api.service.BizReceiceService;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date Created in 14:23 2020/5/14.
 * @Modified
 * @Description
 */
@ServiceClient
public class ClaimServiceClient {

    private static final String CAT_TYPE = "ClaimServiceClientClient";

    @MSharpReference(remoteAppkey = "store-wms-service")
    private BizReceiceService bizReceiceService;

    /**
     * 查询申领单详情
     *
     * @param claimId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public BizWarehouseReceiceDTO queryClaimDetailById(Integer claimId) {
        if (claimId == null) {
            return null;
        }
        ApiResult<BizWarehouseReceiceDTO> response = bizReceiceService.queryReceiceDetailById(claimId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryClaimDetailById", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + claimId + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return null;
    }

    /**
     * 查询申领单审批进度
     *
     * @param claimId 申领单Id
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<ApprovalProgressDTO> queryApprovalProgressById(Integer claimId) {
        if (claimId == null) {
            return Collections.emptyList();
        }
        ApiResult<List<ApprovalProgressDTO>> response = bizReceiceService.queryApprovalProgressById(claimId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryApprovalProgressById", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + claimId + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return Collections.emptyList();
    }

    /**
     * 分页查询申领单 必传page,size
     *
     * @param claimDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public PageApiResult<List<BizWarehouseReceiceDTO>> queryClaimPage(BizWarehouseReceiceDTO claimDTO) {
        if (claimDTO == null) {
            return null;
        }
        PageApiResult<List<BizWarehouseReceiceDTO>> response = bizReceiceService.queryReceicePage(claimDTO);
        if (response.successful()) {
            return response;
        }
        Cat.logError(CAT_TYPE, "queryClaimPage", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(claimDTO) + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return null;
    }

    /**
     * 提交申领(创建申领单)
     *
     * @param bizWarehouseReceiceDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public boolean createClaim(BizWarehouseReceiceDTO bizWarehouseReceiceDTO) {
        if (bizWarehouseReceiceDTO == null) {
            return false;
        }
        ApiResult<Boolean> response = bizReceiceService.createReceice(bizWarehouseReceiceDTO);
        if (response.successful()) {
            return response.getData() == null ? false : response.getData();
        }
        Cat.logError(CAT_TYPE, "createClaim", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(bizWarehouseReceiceDTO) + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return false;
    }

    /**
     * 新建申领单时根据库房id分页查询该库房的库存,必传roomId和receiceType,page,size
     *
     * @param claimDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public PageApiResult<List<BizWarehouseReceiceDetailDTO>> queryStockPage(BizWarehouseReceiceDTO claimDTO) {
        if (claimDTO == null) {
            return null;
        }
        PageApiResult<List<BizWarehouseReceiceDetailDTO>> response = bizReceiceService.queryStockPage(claimDTO);
        if (response.successful()) {
            return response;
        }
        Cat.logError(CAT_TYPE, "queryStockPage", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(claimDTO) + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return null;
    }

    /**
     * 根据申领单id取消该申领单
     *
     * @param id
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public boolean cancelClaim(Integer id, String name, String guid) {
        if (id == null || StringUtils.isBlank(name) || StringUtils.isBlank(guid)) {
            return false;
        }
        ApiResult<Boolean> response = bizReceiceService.cancelReceice(id, name, guid);
        if (response.successful()) {
            return response.getData() == null ? true : response.getData();
        }
        Cat.logError(CAT_TYPE, "cancelClaim", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + id + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return false;
    }

    /**
     * 显示各状态的申领单数量 传apply_user_guid 和orgId
     *
     * @param claimDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public CountDTO getCount(BizWarehouseReceiceDTO claimDTO) {
        if (claimDTO == null) {
            return null;
        }
        ApiResult<CountDTO> response = bizReceiceService.getCount(claimDTO);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "getCount", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(claimDTO) + "\n", new RuntimeException("RPC调用返回失败结果"));
        Preconditions.isTrue(false, response.getMessage());
        return null;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<BizWarehouseReceiceDTO> queryClaimByOrderNos(List<String> orderNos){
        RemoteResponse<List<BizWarehouseReceiceDTO>> response = bizReceiceService.queryReceiceByOrderNos(orderNos);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
