package com.ruijing.store.order.base.minor.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/4/2 0002 17:18
 * @Version 1.0
 * @Desc:描述
 */
public class RefCouponPurchaserDO {
    /**
    * PK
    */
    private String id;

    /**
    * 优惠券ID
    */
    private String couponId;

    /**
    * 用户ID
    */
    private Integer userId;

    /**
    * 领取状态，0:已领取,5:恢复使用,10:已使用,20:已过期
    */
    private Integer status;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 软删除标识
    */
    private Boolean isDeleted;

    /**
    * 软删除时间
    */
    private Date deletionTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }
}