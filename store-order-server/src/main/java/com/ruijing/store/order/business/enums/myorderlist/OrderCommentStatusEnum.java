package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/24 17:05
 * @Description
 **/
public enum OrderCommentStatusEnum {
    /**
     * 待评论
     */
    WAITING_TO_COMMENTS(0,"待评论"),
    /**
     * 已评论
     */
    HAVE_COMMENTS(1,"已评论"),

    /**
     * 无需评论
     */
    NO_COMMENTS(2,"无需评论");

    private Integer value;
    private String desc;

    OrderCommentStatusEnum(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderCommentStatusEnum getByDesc(String desc) {
        for (OrderCommentStatusEnum orderCommentStatusEnum : OrderCommentStatusEnum.values()) {
            if (orderCommentStatusEnum.desc.equals(desc)){
                return orderCommentStatusEnum;
            }
        }
        return null;
    }

    public static final OrderCommentStatusEnum getByValue(Integer value) {
        for (OrderCommentStatusEnum orderCommentStatusEnum : OrderCommentStatusEnum.values()) {
            if (orderCommentStatusEnum.value.equals(value)){
                return orderCommentStatusEnum;
            }
        }
        return null;
    }
}
