package com.ruijing.store.order.base.core.translator;


import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationDTO;
import com.ruijing.store.order.base.core.enums.ElectronicSignEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignConfigVO;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignInfoVO;

/**
 * 电子签名相关dto的转换
 */
public class ElectronicSignTranslator {


    /**
     * electronicSignOperationDTO转ElectronicSignInfoVO
     * @param electronicSignOperationDTO
     * @return
     */
    public static ElectronicSignInfoVO electronicSignOperationDTO2ElectronicSignInfoVO(ElectronicSignOperationDTO electronicSignOperationDTO) {
        if (electronicSignOperationDTO == null) {
            return null;
        }
        ElectronicSignInfoVO electronicSignInfoVO = new ElectronicSignInfoVO();
        Integer electronicSignEnable = electronicSignOperationDTO.getElectronicSignEnable();
        if (ElectronicSignEnum.CHOOSABLE.getType().equals(electronicSignEnable) || ElectronicSignEnum.MUSTUSE.getType().equals(electronicSignEnable)) {
            electronicSignInfoVO.setUseESign(true);
            electronicSignInfoVO.setCompleteInformation(electronicSignOperationDTO.getCompleteInformation());
            Boolean orgWithoutCode = electronicSignOperationDTO.getOrgWithoutCode();
            Boolean userWithoutCode = electronicSignOperationDTO.getUserWithoutCode();
            electronicSignInfoVO.setNeedPassWord(!(orgWithoutCode && userWithoutCode));
            electronicSignInfoVO.setNeedUploadESPhoto(electronicSignOperationDTO.getUploadESPhoto());
            electronicSignInfoVO.setRealNameAuthentication(electronicSignOperationDTO.getRealNameAuthentication());
            electronicSignInfoVO.setType(electronicSignOperationDTO.getType());
            electronicSignInfoVO.setEsPhotoUrl(electronicSignOperationDTO.getEsPhotoUrl());
        }else if(ElectronicSignEnum.FORBID.getType().equals(electronicSignEnable)){
            electronicSignInfoVO.setUseESign(false);
        }

        return electronicSignInfoVO;
    }

    /**
     * dto转vo
     * @param electronicSignOperationDTO 电子签名配置dto
     * @return 电子签名配置vo
     */
    public static ElectronicSignConfigVO dto2Vo(ElectronicSignOperationDTO electronicSignOperationDTO){
        ElectronicSignConfigVO electronicSignConfigVO = new ElectronicSignConfigVO();
        electronicSignConfigVO.setElectronicSignEnable(electronicSignOperationDTO.getElectronicSignEnable());
        electronicSignConfigVO.setCompleteInformation(electronicSignOperationDTO.getCompleteInformation());
        electronicSignConfigVO.setUploadESPhoto(electronicSignOperationDTO.getUploadESPhoto());
        electronicSignConfigVO.setRealNameAuthentication(electronicSignOperationDTO.getRealNameAuthentication());
        electronicSignConfigVO.setOrgWithoutCode(electronicSignOperationDTO.getOrgWithoutCode());
        electronicSignConfigVO.setUserWithoutCode(electronicSignOperationDTO.getUserWithoutCode());
        electronicSignConfigVO.setEsPhotoUrl(electronicSignOperationDTO.getEsPhotoUrl());
        electronicSignConfigVO.setType(electronicSignOperationDTO.getType());
        return electronicSignConfigVO;
    }
}
