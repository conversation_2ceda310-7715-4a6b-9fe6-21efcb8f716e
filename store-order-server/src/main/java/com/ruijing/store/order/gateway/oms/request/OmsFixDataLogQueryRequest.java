package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/7 11:16
 * @description
 */
@RpcModel("oms异常日志查询请求")
public class OmsFixDataLogQueryRequest implements Serializable {

    private static final long serialVersionUID = 3739733363493624089L;

    @RpcModelProperty(value = "当前页码", description = "不传默认值为1")
    private Integer pageNo;

    @RpcModelProperty(value = "每页记录数", description = "不传默认20条")
    private Integer pageSize;

    @RpcModelProperty("订单号")
    private String number;

    @RpcModelProperty("操作人姓名")
    private String operationName;

    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("操作开始时间")
    private Date startTime;

    @RpcModelProperty("操作结束时间")
    private Date endTime;

    @RpcModelProperty("审批单号")
    private String dingTalkApprovalNumber;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDingTalkApprovalNumber() {
        return dingTalkApprovalNumber;
    }

    public void setDingTalkApprovalNumber(String dingTalkApprovalNumber) {
        this.dingTalkApprovalNumber = dingTalkApprovalNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OmsFixDataLogQueryRequest.class.getSimpleName() + "[", "]")
                .add("pageNo=" + pageNo)
                .add("pageSize=" + pageSize)
                .add("number='" + number + "'")
                .add("operationName='" + operationName + "'")
                .add("orgId=" + orgId)
                .add("startTime=" + startTime)
                .add("endTime=" + endTime)
                .add("dingTalkApprovalNumber='" + dingTalkApprovalNumber + "'")
                .toString();
    }
}
