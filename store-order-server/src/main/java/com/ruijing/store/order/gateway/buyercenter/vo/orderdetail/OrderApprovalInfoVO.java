package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/1/14 18:32
 * @Description
 **/
@RpcModel(description = "订单审批信息返回体")
public class OrderApprovalInfoVO implements Serializable {

    private static final long serialVersionUID = 8904102685395115111L;

    /**
     * id
     */
    @RpcModelProperty("id")
    private Integer id;

    /**
     * 验收或拒绝图片
     */
    @RpcModelProperty("验收或拒绝图片")
    private String photo;

    /**
     * 备注
     */
    @RpcModelProperty("备注")
    private String reason;

    /**
     * 订单审批状态
     */
    @RpcModelProperty("订单审批状态")
    private OrderApprovalEnum orderApprovalStatus;
    
    @RpcModelProperty("审批等级")
    private Integer approveLevel;

    /**
     * 状态描述
     */
    @RpcModelProperty("状态描述")
    private String statusDesc;

    /**
     * 操作用户
     */
    @RpcModelProperty("操作用户")
    private String userName;

    /**
     * 创建时间
     */
    @RpcModelProperty("创建时间")
    private Long creationTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public OrderApprovalEnum getOrderApprovalStatus() {
        return orderApprovalStatus;
    }

    public void setOrderApprovalStatus(OrderApprovalEnum orderApprovalStatus) {
        this.orderApprovalStatus = orderApprovalStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Long creationTime) {
        this.creationTime = creationTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderApprovalInfoVO{");
        sb.append("id=").append(id);
        sb.append(", photo='").append(photo).append('\'');
        sb.append(", reason='").append(reason).append('\'');
        sb.append(", orderApprovalStatus=").append(orderApprovalStatus);
        sb.append(", approveLevel=").append(approveLevel);
        sb.append(", statusDesc='").append(statusDesc).append('\'');
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", creationTime=").append(creationTime);
        sb.append('}');
        return sb.toString();
    }
}
