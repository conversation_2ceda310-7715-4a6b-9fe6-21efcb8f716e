package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.whitehole.database.dto.file.OrderDeleteFileDTO;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.order.whitehole.database.service.OrderUploadFileDataService;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 11:37
 * @description
 */
@ServiceClient
public class OrderUploadFileRpcClient {

    @MSharpReference(remoteAppkey = "order-whitehole-service")
    private OrderUploadFileDataService orderUploadFileDataService;

    @ServiceLog(description = "获取订单基础服务上传文件信息", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderUploadFileDTO> getOrderUploadFileList(List<Integer> orderIdList, List<Integer> fileBusinessTypeList) {
        RemoteResponse<List<OrderUploadFileDTO>> response = orderUploadFileDataService.listOrderUploadFile(orderIdList, fileBusinessTypeList);
        Preconditions.isTrue(response.isSuccess(), "获取订单上传文件信息失败");
        return response.getData();
    }

    @ServiceLog(description = "覆盖上传文件信息", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Boolean overWriteList(List<OrderUploadFileDTO> orderUploadFileDTOList) {
        RemoteResponse<Boolean> response = orderUploadFileDataService.overWriteList(orderUploadFileDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "增量上传文件信息", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Boolean insertList(List<OrderUploadFileDTO> orderUploadFileDTOList) {
        if (CollectionUtils.isEmpty(orderUploadFileDTOList)) {
            return true;
        }
        RemoteResponse<Boolean> response = orderUploadFileDataService.insertList(orderUploadFileDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "删除订单文件", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Boolean deleteOrderUploadFile(OrderDeleteFileDTO orderDeleteFileDTO) {
        RemoteResponse<Boolean> response = orderUploadFileDataService.deleteOrderUploadFile(orderDeleteFileDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

}
