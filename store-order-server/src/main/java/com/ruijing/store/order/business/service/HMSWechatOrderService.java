package com.ruijing.store.order.business.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.other.dto.OrgConfigResponseDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.gateway.buyercenter.request.AppendRecieveImageReq;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderConfigsVO;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/9/16 14:35
 */
public interface HMSWechatOrderService {

    /**
     * 获取微信订单侧的订单数量(默认验收审批状态)
     * @param loginInfo
     * @param request
     * @return
     */
    Integer getOrderCountByStatus(LoginUserInfoBO loginInfo, OrderListRequest request);


    OrgConfigResponseDTO findSingleConfigByOrgCode(String orgCode,
                                                   String orgReceiptPicConfig);

    /**
     * 获取订单配置
     * @param orgCode
     * @return
     */
    RemoteResponse<OrderConfigsVO> getOrderConfig(String orgCode);
}
