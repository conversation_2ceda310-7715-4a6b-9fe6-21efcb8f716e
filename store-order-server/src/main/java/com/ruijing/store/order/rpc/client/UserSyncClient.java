package com.ruijing.store.order.rpc.client;

import com.reagent.auth.api.pojo.dto.UserDTO;
import com.reagent.auth.api.pojo.param.UserParam;
import com.reagent.auth.api.service.UserSyncService;
import com.reagent.auth.api.support.ResultBean;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zhukai
 * @date : 2019/12/16 3:11 下午
 * @description: 授权中心 用户信息接口
 */
@Service
@ServiceLog
public class UserSyncClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "UserClient";

    @MSharpReference(remoteAppkey = "auth-server")
    private UserSyncService userSyncService;

    public UserDTO getUser(UserParam userParam) throws CallRpcException {
        final String methodName = "getUserInfo";
        Transaction transaction = Cat.newTransaction(CAT_TYPE, methodName);
        RemoteResponse<List<UserBaseInfoDTO>> remoteResponse;
        ResultBean<UserDTO> responseResult;
        UserDTO userDTO= null;
        try {
            responseResult = userSyncService.getUser(userParam);
            userDTO = responseResult.getData();
            transaction.setSuccess();
            transaction.addData("返回结果：",responseResult);
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("getUser异常",e);
            throw new CallRpcException("获取用户信息异常！");
        }finally {
            transaction.complete();
        }
        return userDTO;
    }

    /**
     * 根据guid 查询用户信息 批量
     * @param guidList
     * @return
     * @throws CallRpcException
     */
    public List<UserDTO> getUsersByGuid(List<String> guidList) throws CallRpcException{
        final String methodName = "getUsersByGuid";

        ResultBean<List<UserDTO>> responseResult = null;
        Transaction transaction = Cat.newTransaction(CAT_TYPE, methodName);
        try {
            responseResult = userSyncService.getUsersByGuids(guidList);
            transaction.setSuccess();
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData(CatUtils.buildStackInfo("getUsersByGuid查询用户信息异常", e));
            logger.error("getUsersByGuid查询用户信息异常： {}", e);
            throw new CallRpcException("getUsersByGuid查询用户信息异常");
        } finally {
            transaction.complete();
        }

        return responseResult.getData();
    }
}
