package com.ruijing.store.order.business.service;

import com.reagent.bid.api.base.enums.CodeExceptionEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.risk.control.enums.LimitControlOperateTypeEnum;
import com.ruijing.fundamental.risk.control.enums.LimitControlStrategyTypeEnum;
import com.ruijing.fundamental.risk.control.riskmessage.dto.RiskMessageOrderDTO;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.LimitControlOrderParamDTO;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.RiskRuleLimitControlErrInfoDTO;
import com.ruijing.fundamental.risk.control.riskrule.dto.purchaseLimitControl.RiskRuleLimitControlResultDTO;
import com.ruijing.store.exception.CodeException;
import com.ruijing.store.goodsreturn.rpc.client.RiskControlClient;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.goodsreturn.vo.RiskRuleLimitCheckErrorInfoVO;
import com.ruijing.store.goodsreturn.vo.RiskRuleLimitErrorInfoVO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.RiskRuleControlTranslator;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Name: RiskRuleService
 * Description:     限额管控相关逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/8/11
 */
@Service
public class RiskRuleService {


    private static final Logger logger = LoggerFactory.getLogger(RiskRuleService.class);

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    RiskControlClient riskControlClient;


    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    /**
     * 取消订单成功，平均价/限额归还
     *
     * @param orderMasterDO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "取消订单成功，平均价/限额归还")
    public void amountLimitBackForOrderCancel(OrderMasterDO orderMasterDO) {
        // 查询经费卡信息
        List<RefFundcardOrderDTO> refFundCardByOrderIdList = refFundcardOrderService.findByOrderIdList(New.list(orderMasterDO.getId()));
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        LimitControlOrderParamDTO paramDTO = RiskRuleControlTranslator.order2LimitControlOrderParam(orderMasterDO, null, orderDetailDOList, refFundCardByOrderIdList);
        paramDTO.setLimitControlStrategyTypeEnum(LimitControlStrategyTypeEnum.PURCHASE_LIMIT_CONTROL_STRATEGY);
        paramDTO.setLimitControlOperateTypeEnum(LimitControlOperateTypeEnum.CANCEL_ORDER);
        // 先校验
        riskRuleValidateByOrder(paramDTO, orderDetailDOList);
        // 后执行扣减/返还
        riskRuleCostDeductByOrder(paramDTO, orderDetailDOList);
    }


    /**
     * 退货成功，平均价/限额归还
     *
     * @param goodsReturnInfoDetailVOList
     * @param orderDetailDOList
     * @param orderMasterDO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "退货成功，平均价/限额归还")
    public void amountLimitBackForGoodsReturn(List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList, List<OrderDetailDO> orderDetailDOList, OrderMasterDO orderMasterDO) {
        List<RefFundcardOrderDTO> refFundCardByOrderIdList = refFundcardOrderService.findByOrderIdList(New.list(orderMasterDO.getId()));
        LimitControlOrderParamDTO paramDTO = RiskRuleControlTranslator.order2LimitControlOrderParam(orderMasterDO, goodsReturnInfoDetailVOList, orderDetailDOList, refFundCardByOrderIdList);
        paramDTO.setLimitControlOperateTypeEnum(LimitControlOperateTypeEnum.RETURN_COMPLETED);
        paramDTO.setLimitControlStrategyTypeEnum(LimitControlStrategyTypeEnum.PURCHASE_LIMIT_CONTROL_STRATEGY);
        // 先校验
        riskRuleValidateByOrder(paramDTO, orderDetailDOList);
        // 后执行扣减/返还
        riskRuleCostDeductByOrder(paramDTO, orderDetailDOList);
    }

    /**
     * 扣减/返还前置校验
     *
     * @param paramDTO
     * @param orderDetailDOList
     */
    private void riskRuleValidateByOrder(LimitControlOrderParamDTO paramDTO, List<OrderDetailDO> orderDetailDOList) {
        // 进行限额校验
        RiskRuleLimitControlResultDTO resultDTO = riskControlClient.riskRuleValidateByOrder(paramDTO);
        // 校验失败返回异常
        if (!resultDTO.getPass()) {
            Map<String, List<OrderDetailDTO>> orderNoDetailMap = orderDetailDOList.stream()
                    .map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO)
                    .collect(Collectors.groupingBy(OrderDetailDTO::getFdetailno));
            // 将错误信息的商品信息按照拆单后的订单进行分组
            Map<String, List<RiskRuleLimitControlErrInfoDTO>> orderErrInfoListMap = partitionProductErrInfo2Order(resultDTO, orderNoDetailMap);
            // 构建校验返回给前端的数据
            RiskRuleLimitCheckErrorInfoVO errorInfoVO = RiskRuleControlTranslator.buildCheckErrorInfo(resultDTO, LimitControlOperateTypeEnum.BID_NEED_LAST_APPROVAL_SUBMIT, paramDTO.getOrgId(), orderErrInfoListMap);
            logger.error("提交竞价终审限额校验失败，异常信息如下: [{}]", JsonUtils.toJson(errorInfoVO));
            throw new CodeException(CodeExceptionEnum.CODE_4009.getValue(), "", errorInfoVO);
        }
    }

    /**
     * 执行扣减/返还
     *
     * @param paramDTO
     * @param orderDetailDOList
     */
    private void riskRuleCostDeductByOrder(LimitControlOrderParamDTO paramDTO, List<OrderDetailDO> orderDetailDOList) {
        // 进行限额校验
        RiskRuleLimitControlResultDTO resultDTO = riskControlClient.riskRuleCostDeductByOrder(paramDTO);
        // 校验失败返回异常
        if (!resultDTO.getPass()) {
            RiskRuleLimitErrorInfoVO errorInfoVO = RiskRuleControlTranslator.buildErrorInfo(resultDTO, orderDetailDOList);
            throw new CodeException(4009, "", errorInfoVO);
        }
    }

    /**
     * 将RiskRuleLimitControlResultDTO中的报错商品信息划归到拆分的订单中
     *
     * @param resultDTO
     * @param orderNoDetailMap
     * @return
     */
    private Map<String, List<RiskRuleLimitControlErrInfoDTO>> partitionProductErrInfo2Order(RiskRuleLimitControlResultDTO resultDTO, Map<String, List<OrderDetailDTO>> orderNoDetailMap) {
        Map<String, List<RiskRuleLimitControlErrInfoDTO>> map = new HashMap<>();
        List<RiskRuleLimitControlErrInfoDTO> errInfoList = resultDTO.getRiskRuleLimitControlErrInfoDTOS();
        orderNoDetailMap.forEach((orderNo, detailList) -> {
            List<RiskRuleLimitControlErrInfoDTO> errInfoDTOS = new ArrayList<>();
            // 使用商品详情中的供应商id，商品id作为标识
            List<String> list = detailList.stream()
                    .map(orderDetailDTO -> orderDetailDTO.getSuppId() + "-" + orderDetailDTO.getProductSn())
                    .collect(Collectors.toList());
            for (RiskRuleLimitControlErrInfoDTO errInfoDTO : errInfoList) {
                StringBuilder builder = new StringBuilder();
                builder.append(errInfoDTO.getSuppId()).append("-").append(errInfoDTO.getProductId());
                if (list.contains(builder.toString())) {
                    errInfoDTOS.add(errInfoDTO);
                }
            }
            map.put(orderNo, errInfoDTOS);
        });
        return map;
    }
}

