package com.ruijing.store.order.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: huangyouwang
 * @Description: 本地化JSon翻译工具类
 * @Date: 2024/11/05 18:58
 */

public class LocalI18nUtils {

    private static final Logger log = LoggerFactory.getLogger(LocalI18nUtils.class);

    private final static String RJ_WEBSITE_LANGUAGE_HEADER = "v-site-language";

    private static Map<String, Map<String, String>> i18nMap;

    static {
        try {
            loadI18nJson();
        } catch (IOException e) {
            log.error("加载i18n.json文件失败", e);
        }
    }

    /**
     * 从本地JSON文件翻译
     *
     * @param key  原值
     * @param lang 目标语言
     * @return 翻译后的文本 {@link com.ruijing.base.gateway.api.dto.LanguageEnum}
     */
    public static String translate(String key, String lang) {
        if (Objects.isNull(i18nMap)) {
            return key;
        }

        // 语言值转换
        String languageCode = convertLanguageCode(lang);

        Map<String, String> translations = i18nMap.get(key);
        // 如果没有找到翻译，则返回原始键
        if (Objects.nonNull(translations)) {
            String translation = translations.get(languageCode);
            return Objects.nonNull(translation) ? translation : key;
        }
        // 如果没有找到该键，则返回原始键
        return key;
    }

    public static String translate(String key) {
        return translate(key, getLangKey());
    }

    /**
     * 获取当前语言的语言值
     *
     * @return 语言值
     */
    public static String getLangKey() {
        return RpcContext.getProviderContext().getHeader(RJ_WEBSITE_LANGUAGE_HEADER, "cnzh");
    }

    private static String convertLanguageCode(String lang) {
        switch (lang) {
            case "cnzh":
                return "cn"; // 假设 JSON 中使用 "cn" 表示中文简体
            case "enus":
                return "en"; // 假设 JSON 中使用 "en" 表示英文
            default:
                return lang; // 如果没有匹配的语言，返回原始语言值
        }
    }

    @SuppressWarnings("unchecked")
    private static void loadI18nJson() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        try (InputStream inputStream = LocalI18nUtils.class.getResourceAsStream("/i18n.json")) {
            if (inputStream == null) {
                throw new FileNotFoundException("i18n.json file not found in classpath");
            }
            i18nMap = objectMapper.readValue(inputStream, Map.class);
        } catch (IOException e) {
            log.error("Failed to load i18n.json", e);
            throw e; // 重新抛出异常以便在静态初始化时捕获
        }
    }
}