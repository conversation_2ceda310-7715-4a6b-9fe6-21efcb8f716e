package com.ruijing.store.order.base.core.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.DefrayDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.BusinessTypeEnum;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.reagent.research.fundcard.enums.SourceTypeEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.FundCardFreezeCommonService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardProjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardSubjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderDetailFeeTypeVO;
import com.ruijing.store.order.rpc.client.OrganizationClient;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/9 9:27
 * @description
 */
@Service
public class FundCardFreezeCommonServiceImpl implements FundCardFreezeCommonService {

    /**
     * 实验耗材费中文
     */
    private final static String CONSUMABLES_FEE_CN = "实验耗材费";

    /**
     * 测试分析费中文
     */
    private final static String ANALYSIS_FEE_CN = "测试分析费";

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Override
    public ChangeFundCardRequestDTO getReFreezeRequestParam(OrderMasterDO orderMasterDO) {
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(orderMasterDO.getFuserid());
        // 获取单位经费卡层级
        String cardLevelString = sysConfigClient.getConfigByOrgCodeAndConfigCode(simpleOrgDTO.getCode(), ConfigConstant.RESEARCH_FUNDCARD_LEVEL);
        int cardLevel = NumberUtils.toInt(cardLevelString);
        List<RefFundcardOrderDO> refFundCardOrderDOList = refFundcardOrderMapper.findByOrderId(orderMasterDO.getId().toString());
        BusinessErrUtil.notEmpty(refFundCardOrderDOList, ExecptionMessageEnum.ORDER_NO_CARD_BINDING, orderMasterDO.getForderno());
        List<String> lastLevelCardIdList = refFundCardOrderDOList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(simpleOrgDTO.getCode(), lastLevelCardIdList);
        // 中肿定制 试验费分析费
        if (orderMasterDO.getFuserid() == OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue()) {
            return this.getZhongShanZhongLiuWrap(fundCardDTOList, cardLevel, orderMasterDO);
        }
        // 中大附一换卡（前端传参比较特殊），专门拉出来一个
        if (orderMasterDO.getFuserid() == OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue()) {
            return this.getZhongDaFuYiWrap(refFundCardOrderDOList, fundCardDTOList);
        }
        // 一级卡单位没有待结算换卡的
        BusinessErrUtil.isTrue(cardLevel != 1, ExecptionMessageEnum.NO_SUPPORT_UNFREEZE_FIRST_LEVEL_CARD);
        if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel) {
            // 二级卡
            return this.getSecondLevelCardWarp(refFundCardOrderDOList, fundCardDTOList);
        }
        // 三级卡
        return this.getThirdLevelFundCardWrap(refFundCardOrderDOList, fundCardDTOList);
    }

    @Override
    public void orderDefray(List<OrderMasterDO> unfinishedOrderList) {
        List<Integer> orderIdList = unfinishedOrderList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        String orgCode = unfinishedOrderList.get(0).getFusercode();
        boolean isNewBudget = researchFundCardServiceClient.isNewBudgetByOrgCode(orgCode);
        // 非中大系且为新结算的单位，才需要调用扣费
        boolean needCallDefray = !ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orgCode) && isNewBudget;
        // 如果属于新预算对接单位且非中大系，则推送支付金额到预算系统
        if (needCallDefray) {
            Date nowTime = new Date();
            List<String> orderIdString = orderIdList.stream().map(String::valueOf).collect(Collectors.toList());
            // 查找订单绑卡信息
            List<RefFundcardOrderDO> refFundCardList = refFundcardOrderMapper.findByOrderIdIn(orderIdString);
            // 绑卡信息转字典
            Map<Integer, List<RefFundcardOrderDO>> orderIdRefMap = refFundCardList.stream().collect(
                    Collectors.groupingBy(ref -> Integer.parseInt(ref.getOrderId())));
            List<DefrayDTO> defrayDTOList = New.list();
            for (OrderMasterDO masterDO : unfinishedOrderList) {
                List<RefFundcardOrderDO> refCardList = orderIdRefMap.get(masterDO.getId());
                if (CollectionUtils.isEmpty(refCardList)) {
                    continue;
                }
                DefrayDTO defrayDTO = new DefrayDTO();
                defrayDTO.setSerialNumber(masterDO.getForderno());
                defrayDTO.setAppKey(Environment.getAppKey());
                defrayDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
                defrayDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
                defrayDTO.setUserId(masterDO.getFbuyerid());
                defrayDTO.setBuyerUserId(masterDO.getFbuyerid());
                // 订单实际支付金额
                BigDecimal defrayMoney = masterDO.getForderamounttotal().subtract(new BigDecimal(masterDO.getReturnAmount().toString()));
                defrayDTO.setFreezeAmount(defrayMoney);
                defrayDTO.setOperateDate(nowTime);

                List<FundCardDTO> fundCardDtoList = refCardList.stream().map(ref -> RefFundcardOrderTranslator.refToFundCardDto(ref, defrayMoney)).collect(Collectors.toList());
                defrayDTO.setFundCardDTOs(fundCardDtoList);
                defrayDTOList.add(defrayDTO);
            }
            if (CollectionUtils.isEmpty(defrayDTOList)) {
                return;
            }
            // 推送订单支付信息到预算系统
            researchFundCardServiceClient.orderDefray(orgCode, defrayDTOList);
        }
    }

    private ChangeFundCardRequestDTO getSecondLevelCardWarp(List<RefFundcardOrderDO> refFundCardOrderDOList, List<FundCardDTO> fundCardDTOList) {
        Map<String, BigDecimal> cardIdFreezeAmountMap = DictionaryUtils.toMap(refFundCardOrderDOList, RefFundcardOrderTranslator::getLastLevelCardId, RefFundcardOrderTranslator::getFreezeAmount);
        ChangeFundCardRequestDTO changeFundCardRequestDTO = new ChangeFundCardRequestDTO();
        List<FundCardProjectRequestDTO> fundCardProjectRequestDTOList = New.list();
        changeFundCardRequestDTO.setSaveProjectList(fundCardProjectRequestDTOList);
        for (FundCardDTO firstLevelCard : fundCardDTOList) {
            String firstLevelCardId = firstLevelCard.getId();
            List<FundCardDTO> secondLevelFundCardList = firstLevelCard.getFundCardDTOs();
            // 装填一级经费卡数据
            FundCardProjectRequestDTO fundCardProjectRequestDTO = new FundCardProjectRequestDTO();
            fundCardProjectRequestDTO.setId(firstLevelCardId);
            fundCardProjectRequestDTO.setProjectCode(firstLevelCard.getCode());
            fundCardProjectRequestDTOList.add(fundCardProjectRequestDTO);
            List<OrderFundCardRequestDTO> orderFundCardRequestDTOList = New.list();
            fundCardProjectRequestDTO.setSaveFundCardList(orderFundCardRequestDTOList);
            for (FundCardDTO secondLevelCard : secondLevelFundCardList) {
                if (cardIdFreezeAmountMap.get(secondLevelCard.getId()) == null) {
                    continue;
                }
                fundCardProjectRequestDTO.setProjectName(secondLevelCard.getName());
                fundCardProjectRequestDTO.setFundType(secondLevelCard.getFundType());
                String secondLevelCardId = secondLevelCard.getId();
                // 装填二级经费卡数据
                OrderFundCardRequestDTO orderFundCardRequestDTO = new OrderFundCardRequestDTO();
                orderFundCardRequestDTO.setCardId(secondLevelCardId);
                orderFundCardRequestDTO.setCardNo(secondLevelCard.getCode());
                orderFundCardRequestDTO.setSaveFundCardSubjectList(New.emptyList());
                orderFundCardRequestDTOList.add(orderFundCardRequestDTO);
                // 如果是一级卡 就不继续封装了
            }
        }
        changeFundCardRequestDTO.setOrderIds(New.list(Integer.parseInt(refFundCardOrderDOList.get(0).getOrderId())));
        return changeFundCardRequestDTO;
    }

    private ChangeFundCardRequestDTO getThirdLevelFundCardWrap(List<RefFundcardOrderDO> refFundCardOrderDOList, List<FundCardDTO> fundCardDTOList) {
        Map<String, BigDecimal> cardIdFreezeAmountMap = DictionaryUtils.toMap(refFundCardOrderDOList, RefFundcardOrderTranslator::getLastLevelCardId, RefFundcardOrderTranslator::getFreezeAmount);
        ChangeFundCardRequestDTO changeFundCardRequestDTO = new ChangeFundCardRequestDTO();
        List<FundCardProjectRequestDTO> fundCardProjectRequestDTOList = New.list();
        changeFundCardRequestDTO.setSaveProjectList(fundCardProjectRequestDTOList);
        for (FundCardDTO firstLevelCard : fundCardDTOList) {
            String firstLevelCardId = firstLevelCard.getId();
            List<FundCardDTO> secondLevelFundCardList = firstLevelCard.getFundCardDTOs();
            for (FundCardDTO secondLevelCard : secondLevelFundCardList) {
                String secondLevelCardId = secondLevelCard.getId();
                List<FundCardDTO> thirdLevelFundCardList = secondLevelCard.getFundCardDTOs();
                for (FundCardDTO thirdLevelCard : thirdLevelFundCardList) {
                    if (cardIdFreezeAmountMap.get(thirdLevelCard.getId()) == null) {
                        continue;
                    }
                    // 装填一级经费卡数据
                    FundCardProjectRequestDTO fundCardProjectRequestDTO = new FundCardProjectRequestDTO();
                    fundCardProjectRequestDTO.setId(firstLevelCardId);
                    fundCardProjectRequestDTO.setFundType(firstLevelCard.getFundType());
                    fundCardProjectRequestDTO.setProjectName(thirdLevelCard.getName());
                    fundCardProjectRequestDTO.setProjectCode(firstLevelCard.getCode());
                    fundCardProjectRequestDTOList.add(fundCardProjectRequestDTO);

                    List<OrderFundCardRequestDTO> orderFundCardRequestDTOList = New.list();
                    fundCardProjectRequestDTO.setSaveFundCardList(orderFundCardRequestDTOList);

                    // 装填二级经费卡数据
                    OrderFundCardRequestDTO orderFundCardRequestDTO = new OrderFundCardRequestDTO();
                    orderFundCardRequestDTO.setCardId(secondLevelCardId);
                    orderFundCardRequestDTO.setCardNo(secondLevelCard.getCode());
                    orderFundCardRequestDTO.setSaveFundCardSubjectList(New.emptyList());
                    orderFundCardRequestDTOList.add(orderFundCardRequestDTO);

                    List<FundCardSubjectRequestDTO> fundCardSubjectRequestDTOList = New.list();
                    orderFundCardRequestDTO.setSaveFundCardSubjectList(fundCardSubjectRequestDTOList);

                    String thirdLevelCardId = thirdLevelCard.getId();
                    // 装填三级经费卡数据
                    FundCardSubjectRequestDTO fundCardSubjectRequestDTO = new FundCardSubjectRequestDTO();
                    fundCardSubjectRequestDTO.setId(thirdLevelCardId);
                    fundCardSubjectRequestDTO.setSubjectCode(thirdLevelCard.getCode());
                    fundCardSubjectRequestDTO.setUseAmount(cardIdFreezeAmountMap.get(thirdLevelCardId));
                    fundCardSubjectRequestDTOList.add(fundCardSubjectRequestDTO);
                }
            }
        }
        changeFundCardRequestDTO.setOrderIds(New.list(Integer.parseInt(refFundCardOrderDOList.get(0).getOrderId())));
        return changeFundCardRequestDTO;
    }

    /**
     * 中肿换卡数据获取。定制点：经费卡是二级卡，但是绑卡数据是一级卡。且经费卡下必定只有两张卡，实验分析费和
     *
     * @param fundCardDTOList 经费卡数据
     * @param cardLevel       经费卡等级
     * @param orderMasterDO   订单
     * @return 换卡数据
     */
    private ChangeFundCardRequestDTO getZhongShanZhongLiuWrap(List<FundCardDTO> fundCardDTOList, int cardLevel, OrderMasterDO orderMasterDO) {
        BusinessErrUtil.isTrue(FundCardLevelEnum.FUND_CARD.getValue() == cardLevel, ExecptionMessageEnum.SWELLING_CONFIG_ERROR);
        ChangeFundCardRequestDTO changeFundCardRequestDTO = new ChangeFundCardRequestDTO();
        List<FundCardProjectRequestDTO> fundCardProjectRequestDTOList = New.list();
        changeFundCardRequestDTO.setSaveProjectList(fundCardProjectRequestDTOList);
        for (FundCardDTO firstLevelCard : fundCardDTOList) {
            String firstLevelCardId = firstLevelCard.getId();
            List<FundCardDTO> secondLevelFundCardList = firstLevelCard.getFundCardDTOs();
            for (FundCardDTO secondLevelCard : secondLevelFundCardList) {
                // 装填一级经费卡数据
                FundCardProjectRequestDTO fundCardProjectRequestDTO = new FundCardProjectRequestDTO();
                fundCardProjectRequestDTO.setId(firstLevelCardId);
                fundCardProjectRequestDTO.setFundType(firstLevelCard.getFundType());
                // 用的二级卡名
                fundCardProjectRequestDTO.setProjectName(secondLevelCard.getName());
                fundCardProjectRequestDTO.setProjectCode(firstLevelCard.getCode());
                fundCardProjectRequestDTOList.add(fundCardProjectRequestDTO);

                List<OrderFundCardRequestDTO> orderFundCardRequestDTOList = New.list();
                fundCardProjectRequestDTO.setSaveFundCardList(orderFundCardRequestDTOList);
                String secondLevelCardId = secondLevelCard.getId();
                // 装填二级经费卡数据
                OrderFundCardRequestDTO orderFundCardRequestDTO = new OrderFundCardRequestDTO();
                orderFundCardRequestDTO.setCardId(secondLevelCardId);
                orderFundCardRequestDTO.setCardNo(secondLevelCard.getCode());
                orderFundCardRequestDTO.setSaveFundCardSubjectList(New.emptyList());
                orderFundCardRequestDTOList.add(orderFundCardRequestDTO);
            }
        }
        List<OrderDetailFeeTypeVO> orderDetailFeeTypeVOList = refFundcardOrderService.getOrderDetailFeeType(New.list(orderMasterDO.getId()));
        BusinessErrUtil.notEmpty(orderDetailFeeTypeVOList, ExecptionMessageEnum.UNABLE_OBTAIN_COST_CLASSIFICATION, orderMasterDO.getForderno());
        // 分析费
        BigDecimal analysisFee = orderDetailFeeTypeVOList.stream().filter(orderDetailFeeTypeVO -> ANALYSIS_FEE_CN.equals(orderDetailFeeTypeVO.getFeeTypeTag())).map(OrderDetailFeeTypeVO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        changeFundCardRequestDTO.setAnalysisFee(analysisFee);
        // 材料费
        BigDecimal consumablesFee = orderDetailFeeTypeVOList.stream().filter(orderDetailFeeTypeVO -> CONSUMABLES_FEE_CN.equals(orderDetailFeeTypeVO.getFeeTypeTag())).map(OrderDetailFeeTypeVO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        changeFundCardRequestDTO.setConsumablesFee(consumablesFee);
        changeFundCardRequestDTO.setOrderIds(New.list(orderMasterDO.getId()));
        return changeFundCardRequestDTO;
    }

    /**
     * 中大附一待结算选卡处理，如果是二级卡要变为三级卡来传参
     *
     * @param refFundCardOrderDOList 绑卡数据
     * @param fundCardDTOList        经费卡数据
     * @return 换卡数据
     */
    private ChangeFundCardRequestDTO getZhongDaFuYiWrap(List<RefFundcardOrderDO> refFundCardOrderDOList, List<FundCardDTO> fundCardDTOList) {
        Map<String, BigDecimal> cardIdFreezeAmountMap = DictionaryUtils.toMap(refFundCardOrderDOList, RefFundcardOrderTranslator::getLastLevelCardId, RefFundcardOrderTranslator::getFreezeAmount);
        ChangeFundCardRequestDTO changeFundCardRequestDTO = new ChangeFundCardRequestDTO();
        List<FundCardProjectRequestDTO> fundCardProjectRequestDTOList = New.list();
        changeFundCardRequestDTO.setSaveProjectList(fundCardProjectRequestDTOList);
        for (FundCardDTO firstLevelCard : fundCardDTOList) {
            String firstLevelCardId = firstLevelCard.getId();
            List<FundCardDTO> secondLevelFundCardList = firstLevelCard.getFundCardDTOs();
            for (FundCardDTO secondLevelCard : secondLevelFundCardList) {
                String secondLevelCardId = secondLevelCard.getId();
                List<FundCardDTO> thirdLevelFundCardList = secondLevelCard.getFundCardDTOs();
                BusinessErrUtil.notEmpty(thirdLevelFundCardList, ExecptionMessageEnum.NO_THIRD_LEVEL_CARD_DATA);
                for (FundCardDTO thirdLevelCard : thirdLevelFundCardList) {
                    if (cardIdFreezeAmountMap.get(thirdLevelCard.getId()) == null) {
                        continue;
                    }
                    // 装填一级经费卡数据
                    FundCardProjectRequestDTO fundCardProjectRequestDTO = new FundCardProjectRequestDTO();
                    fundCardProjectRequestDTO.setId(firstLevelCardId);
                    fundCardProjectRequestDTO.setFundType(firstLevelCard.getFundType());
                    fundCardProjectRequestDTO.setProjectName(thirdLevelCard.getName());
                    fundCardProjectRequestDTO.setProjectCode(firstLevelCard.getCode());
                    fundCardProjectRequestDTOList.add(fundCardProjectRequestDTO);

                    List<OrderFundCardRequestDTO> orderFundCardRequestDTOList = New.list();
                    fundCardProjectRequestDTO.setSaveFundCardList(orderFundCardRequestDTOList);

                    // 装填二级经费卡数据
                    OrderFundCardRequestDTO orderFundCardRequestDTO = new OrderFundCardRequestDTO();
                    orderFundCardRequestDTO.setCardId(secondLevelCardId);
                    orderFundCardRequestDTO.setCardNo(secondLevelCard.getCode());
                    orderFundCardRequestDTO.setSaveFundCardSubjectList(New.emptyList());
                    orderFundCardRequestDTOList.add(orderFundCardRequestDTO);

                    List<FundCardSubjectRequestDTO> fundCardSubjectRequestDTOList = New.list();
                    orderFundCardRequestDTO.setSaveFundCardSubjectList(fundCardSubjectRequestDTOList);

                    String thirdLevelCardId = thirdLevelCard.getId();
                    // 装填三级经费卡数据
                    FundCardSubjectRequestDTO fundCardSubjectRequestDTO = new FundCardSubjectRequestDTO();
                    fundCardSubjectRequestDTO.setId(thirdLevelCardId);
                    fundCardSubjectRequestDTO.setSubjectCode(thirdLevelCard.getCode());
                    fundCardSubjectRequestDTO.setUseAmount(cardIdFreezeAmountMap.get(thirdLevelCardId));
                    fundCardSubjectRequestDTOList.add(fundCardSubjectRequestDTO);

                }
            }
        }
        changeFundCardRequestDTO.setOrderIds(New.list(Integer.parseInt(refFundCardOrderDOList.get(0).getOrderId())));
        return changeFundCardRequestDTO;
    }
}
