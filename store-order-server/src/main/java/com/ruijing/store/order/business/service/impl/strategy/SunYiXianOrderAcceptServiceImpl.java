package com.ruijing.store.order.business.service.impl.strategy;

import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 孙逸仙的验收策略
 * @author: zhongyulei
 * @create: 2022-01-10 11:27
 */
@Service(OrderAcceptConstant.ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_ACCEPT)
public class SunYiXianOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        // 自结算的订单验收后变成待结算且手动提交入库, 入库完成后要自动发起结算
        // 配置了验收审批
        if (isAcceptApproval) {
            return 2;
        }

        return 3;
    }
}
