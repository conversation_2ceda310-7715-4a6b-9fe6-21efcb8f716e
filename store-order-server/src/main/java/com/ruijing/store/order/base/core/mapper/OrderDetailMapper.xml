<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.OrderDetailMapper">

    <resultMap id="DeficiencyDetailDO" type="com.ruijing.store.order.base.core.model.DeficiencyDetailDO">
        <result column="id" property="id"/>
        <result column="fmasterid" property="masterId"/>
        <result column="product_sn" property="productSn"/>
        <result column="first_category_Id" property="firstCategoryId"/>
        <result column="CategoryID" property="categoryId"/>
    </resultMap>
  <resultMap id="SimpleDetailInfoDO" type="com.ruijing.store.order.base.core.model.SimpleDetailInfoDO">
    <result column="id" property="id"/>
    <result column="fmasterid" property="fmasterid"/>
    <result column="fspec" property="fspec"/>
    <result column="fbidprice" property="fbidprice"/>
    <result column="fbrand" property="fbrand"/>
    <result column="fbrandid" property="fbrandid"/>
    <result column="fgoodname" property="fgoodname"/>
    <result column="fgoodcode" property="fgoodcode"/>
    <result column="product_code" property="productCode"/>
    <result column="fpicpath" property="fpicpath"/>
    <result column="return_status" property="returnStatus"/>
    <result column="return_amount" property="returnAmount"/>
    <result column="product_sn" property="productSn"/>
    <result column="dangerous_type_id" property="dangerousTypeId"/>
    <result column="dangerous_type_name" property="dangerousTypeName"/>
  </resultMap>
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.OrderDetailDO">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="fmasterid" jdbcType="INTEGER" property="fmasterid"/>
    <result column="fbiddate" jdbcType="TIMESTAMP" property="fbiddate"/>
    <result column="fdetailno" jdbcType="VARCHAR" property="fdetailno"/>
    <result column="CategoryID" jdbcType="INTEGER" property="categoryid"/>
    <result column="fclassification" jdbcType="VARCHAR" property="fclassification"/>
    <result column="fgoodcode" jdbcType="VARCHAR" property="fgoodcode"/>
    <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
    <result column="fgoodname" jdbcType="VARCHAR" property="fgoodname"/>
    <result column="fbrand" jdbcType="VARCHAR" property="fbrand"/>
    <result column="fspec" jdbcType="VARCHAR" property="fspec"/>
    <result column="funit" jdbcType="VARCHAR" property="funit"/>
    <result column="fquantity" jdbcType="DECIMAL" property="fquantity"/>
    <result column="fbidprice" jdbcType="DECIMAL" property="fbidprice"/>
    <result column="fbidamount" jdbcType="DECIMAL" property="fbidamount"/>
    <result column="fpicpath" jdbcType="VARCHAR" property="fpicpath"/>
    <result column="fremainquantity" jdbcType="DECIMAL" property="fremainquantity"/>
    <result column="fbrandid" jdbcType="INTEGER" property="fbrandid"/>
    <result column="tsuppmerpassid" jdbcType="INTEGER" property="tsuppmerpassid"/>
    <result column="fcancelquantity" jdbcType="DECIMAL" property="fcancelquantity"/>
    <result column="fcancelamount" jdbcType="DECIMAL" property="fcancelamount"/>
    <result column="product_sn" jdbcType="BIGINT" property="productSn"/>
    <result column="return_status" jdbcType="INTEGER" property="returnStatus"/>
    <result column="return_amount" jdbcType="DOUBLE" property="returnAmount"/>
    <result column="original_amount" jdbcType="DECIMAL" property="originalAmount"/>
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
    <result column="modify_price" jdbcType="BIT" property="modifyPrice"/>
    <result column="delivery_time" jdbcType="INTEGER" property="deliveryTime"/>
    <result column="remainder_price" jdbcType="DECIMAL" property="remainderPrice"/>
    <result column="negotiated_price" jdbcType="DECIMAL" property="negotiatedPrice"/>
    <result column="sysu_category_id" jdbcType="INTEGER" property="sysuCategoryId"/>
    <result column="category_directory_id" jdbcType="INTEGER" property="categoryDirectoryId"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="carry_fee" jdbcType="DECIMAL" property="carryFee"/>
    <result column="first_category_id" jdbcType="INTEGER" property="firstCategoryId"/>
    <result column="first_category_name" jdbcType="VARCHAR" property="firstCategoryName"/>
    <result column="second_category_id" jdbcType="INTEGER" property="secondCategoryId"/>
    <result column="second_category_name" jdbcType="VARCHAR" property="secondCategoryName"/>
    <result column="fee_type_tag" jdbcType="VARCHAR" property="feeTypeTag"/>
    <result column="category_tag" jdbcType="VARCHAR" property="categoryTag"/>
    <result column="dangerous_type_id" jdbcType="INTEGER" property="dangerousTypeId"/>
    <result column="dangerous_type_name" jdbcType="VARCHAR" property="dangerousTypeName"/>
    <result column="regulatory_type_id" jdbcType="INTEGER" property="regulatoryTypeId"/>
    <result column="regulatory_type_name" jdbcType="VARCHAR" property="regulatoryTypeName"/>
    <result column="casno" jdbcType="VARCHAR" property="casno"/>
    <result column="supp_id" jdbcType="INTEGER" property="suppId"/>
    <result column="supp_name" jdbcType="VARCHAR" property="suppName"/>
    <result column="supp_code" jdbcType="VARCHAR" property="suppCode"/>
    <result column="brand_ename" jdbcType="VARCHAR" property="brandEname"/>
  </resultMap>
  <resultMap id="EmptyCategoryMap" type="com.ruijing.store.order.base.core.model.OrderDetailDO">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fmasterid" jdbcType="INTEGER" property="fmasterid" />
    <result column="product_sn" jdbcType="BIGINT" property="productSn" />
    <result column="first_category_id" jdbcType="INTEGER" property="firstCategoryId" />
    <result column="first_category_name" jdbcType="VARCHAR" property="firstCategoryName" />
    <result column="supp_id" jdbcType="INTEGER" property="suppId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fmasterid, fbiddate,
    fdetailno, CategoryID, fclassification, fgoodcode, product_code, fgoodname, fbrand,
    fspec, funit, fquantity,
    fbidprice, fbidamount, fpicpath, fremainquantity, fbrandid,
    tsuppmerpassid, fcancelquantity,
    fcancelamount, product_sn, return_status,
    return_amount, original_amount, original_price, modify_price, delivery_time, remainder_price,
    negotiated_price, sysu_category_id, category_directory_id,update_time, carry_fee
      , first_category_id, first_category_name, second_category_id,
      second_category_name, fee_type_tag, category_tag, dangerous_type_id, dangerous_type_name, regulatory_type_id, regulatory_type_name,
      casno, supp_id, supp_name, supp_code, brand_ename
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from torder_detail
    where id = #{id,jdbcType=INTEGER}
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.OrderDetailDO">
    <!--@mbg.generated-->
    update torder_detail
    <set>
      <if test="fmasterid != null">
        fmasterid = #{fmasterid,jdbcType=INTEGER},
      </if>
      <if test="fbiddate != null">
        fbiddate = #{fbiddate,jdbcType=TIMESTAMP},
      </if>
      <if test="categoryid != null">
        CategoryID = #{categoryid,jdbcType=INTEGER},
      </if>
      <if test="fclassification != null">
        fclassification = #{fclassification,jdbcType=VARCHAR},
      </if>
      <if test="fgoodcode != null">
        fgoodcode = #{fgoodcode,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="fgoodname != null">
        fgoodname = #{fgoodname,jdbcType=VARCHAR},
      </if>
      <if test="fbrand != null">
        fbrand = #{fbrand,jdbcType=VARCHAR},
      </if>
      <if test="fspec != null">
        fspec = #{fspec,jdbcType=VARCHAR},
      </if>
      <if test="funit != null">
        funit = #{funit,jdbcType=VARCHAR},
      </if>
      <if test="fquantity != null">
        fquantity = #{fquantity,jdbcType=DECIMAL},
      </if>
      <if test="fbidprice != null">
        fbidprice = #{fbidprice,jdbcType=DECIMAL},
      </if>
      <if test="fbidamount != null">
        fbidamount = #{fbidamount,jdbcType=DECIMAL},
      </if>
      <if test="fpicpath != null">
        fpicpath = #{fpicpath,jdbcType=VARCHAR},
      </if>
      <if test="fremainquantity != null">
        fremainquantity = #{fremainquantity,jdbcType=DECIMAL},
      </if>
      <if test="fbrandid != null">
        fbrandid = #{fbrandid,jdbcType=INTEGER},
      </if>
      <if test="tsuppmerpassid != null">
        tsuppmerpassid = #{tsuppmerpassid,jdbcType=INTEGER},
      </if>
      <if test="fcancelquantity != null">
        fcancelquantity = #{fcancelquantity,jdbcType=DECIMAL},
      </if>
      <if test="fcancelamount != null">
        fcancelamount = #{fcancelamount,jdbcType=DECIMAL},
      </if>
      <if test="productSn != null">
        product_sn = #{productSn,jdbcType=BIGINT},
      </if>
      <if test="returnStatus != null">
        return_status = #{returnStatus,jdbcType=INTEGER},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DOUBLE},
      </if>
      <if test="originalAmount != null">
        original_amount = #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="modifyPrice != null">
        modify_price = #{modifyPrice,jdbcType=BIT},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=INTEGER},
      </if>
      <if test="remainderPrice != null">
        remainder_price = #{remainderPrice,jdbcType=DECIMAL},
      </if>
      <if test="negotiatedPrice != null">
        negotiated_price = #{negotiatedPrice,jdbcType=DECIMAL},
      </if>
      <if test="sysuCategoryId != null">
        sysu_category_id = #{sysuCategoryId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carryFee != null">
        carry_fee = #{carryFee,jdbcType=DECIMAL},
      </if>
        <if test="suppName != null">
            supp_name = #{suppName,jdbcType=VARCHAR},
        </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-06-27-->
  <select id="findByFmasterid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from torder_detail
    <where>
        fmasterid=#{fmasterid,jdbcType=INTEGER}
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2019-10-22-->
  <select id="findFmasteridByReturnStatusIn" resultType="java.lang.Integer">
    select distinct fmasterid
    from torder_detail
    where 1 = 1

    <if test="masterIdList != null and masterIdList.size() != 0">
      and fmasterid in
      <foreach item="item" index="index" collection="masterIdList"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>

    <if test="returnStatusCollection != null and returnStatusCollection.size() != 0">
      and return_status in
      <foreach item="item" index="index" collection="returnStatusCollection"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>

  </select>

<!--auto generated by MybatisCodeHelper on 2019-11-14-->
  <select id="findAllByFmasteridIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from torder_detail
        where fmasterid in
        <foreach item="item" index="index" collection="fmasteridCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <select id="findProductSnById" resultType="java.lang.Long">
        select product_sn
        from torder_detail
        where id=#{id,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <update id="updateFgoodnameByIdIn">
    update torder_detail
    set fgoodname=#{updatedFgoodname,jdbcType=VARCHAR}
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </update>


<!--auto generated by MybatisCodeHelper on 2020-07-20-->
  <update id="loopUpdateByIdIn">

      <foreach item="item" index="index" collection="list"
               open="" separator=";">
        update torder_detail
        <set>
          <if test="item.returnStatus != null">
              return_status = #{item.returnStatus,jdbcType=INTEGER},
          </if>

          <if test="item.fcancelquantity != null">
            fcancelquantity = #{item.fcancelquantity,jdbcType=DECIMAL},
          </if>

          <if test="item.returnAmount != null">
            return_amount = #{item.returnAmount,jdbcType=DOUBLE},
          </if>

          <if test="item.fbrand != null">
            fbrand = #{item.fbrand,jdbcType=VARCHAR},
          </if>

          <if test="item.fcancelamount != null">
            fcancelamount = #{item.fcancelamount,jdbcType=DECIMAL},
          </if>

          <if test="item.productSn != null">
            product_sn = #{item.productSn,jdbcType=BIGINT},
          </if>

        </set>
        where id = #{item.id,jdbcType=INTEGER}
      </foreach>

  </update>

<!--auto generated by MybatisCodeHelper on 2020-08-11-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO torder_detail(
        fmasterid,
        fbiddate,
        fdetailno,
        CategoryID,
        fclassification,
        fgoodcode,
        product_code,
        fgoodname,
        fbrand,
        fspec,
        funit,
        fquantity,
        fbidprice,
        fbidamount,
        fpicpath,
        fremainquantity,
        fbrandid,
        tsuppmerpassid,
        fcancelquantity,
        fcancelamount,
        product_sn,
        return_status,
        return_amount,
        original_amount,
        original_price,
        modify_price,
        delivery_time,
        remainder_price,
        negotiated_price,
        sysu_category_id,
        category_directory_id,
        update_time,
        carry_fee,
        first_category_id,
        first_category_name,
        second_category_id,
        second_category_name,
        fee_type_tag,
        category_tag,
        dangerous_type_id,
        dangerous_type_name,
        regulatory_type_id,
        regulatory_type_name,
        casno,
        supp_id,
        supp_name,
        supp_code,
        brand_ename
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.fmasterid,jdbcType=INTEGER},
            #{element.fbiddate,jdbcType=TIMESTAMP},
            #{element.fdetailno,jdbcType=VARCHAR},
            #{element.categoryid,jdbcType=INTEGER},
            #{element.fclassification,jdbcType=VARCHAR},
            #{element.fgoodcode,jdbcType=VARCHAR},
            #{element.productCode,jdbcType=VARCHAR},
            #{element.fgoodname,jdbcType=VARCHAR},
            #{element.fbrand,jdbcType=VARCHAR},
            #{element.fspec,jdbcType=VARCHAR},
            #{element.funit,jdbcType=VARCHAR},
            #{element.fquantity,jdbcType=DECIMAL},
            #{element.fbidprice,jdbcType=DECIMAL},
            #{element.fbidamount,jdbcType=DECIMAL},
            #{element.fpicpath,jdbcType=VARCHAR},
            #{element.fremainquantity,jdbcType=DECIMAL},
            #{element.fbrandid,jdbcType=INTEGER},
            #{element.tsuppmerpassid,jdbcType=INTEGER},
            <choose>
                <when test="element.fcancelquantity != null ">
                    #{element.fcancelquantity,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            <choose>
                <when test="element.fcancelamount != null ">
                    #{element.fcancelamount,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            #{element.productSn,jdbcType=BIGINT},
            #{element.returnStatus,jdbcType=INTEGER},

            <choose>
                <when test="element.returnAmount != null ">
                    #{element.returnAmount,jdbcType=DOUBLE},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>

            #{element.originalAmount,jdbcType=DECIMAL},
            #{element.originalPrice,jdbcType=DECIMAL},

            <choose>
                <when test="element.modifyPrice != null ">
                    #{element.modifyPrice,jdbcType=BIT},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.deliveryTime != null ">
                    #{element.deliveryTime,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.remainderPrice != null ">
                    #{element.remainderPrice,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>

            #{element.negotiatedPrice,jdbcType=DECIMAL},
            #{element.sysuCategoryId,jdbcType=INTEGER},

            <choose>
                <when test="element.categoryDirectoryId != null ">
                    #{element.categoryDirectoryId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.updateTime != null ">
                    #{element.updateTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>

            #{element.carryFee,jdbcType=DECIMAL},

            <choose>
                <when test="element.firstCategoryId != null ">
                    #{element.firstCategoryId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.firstCategoryName != null ">
                    #{element.firstCategoryName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.secondCategoryId != null ">
                    #{element.secondCategoryId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.secondCategoryName != null ">
                    #{element.secondCategoryName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.feeTypeTag != null ">
                    #{element.feeTypeTag,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.categoryTag != null ">
                    #{element.categoryTag,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.dangerousTypeId != null ">
                    #{element.dangerousTypeId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.dangerousTypeName != null ">
                    #{element.dangerousTypeName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.regulatoryTypeId != null ">
                    #{element.regulatoryTypeId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.regulatoryTypeName != null ">
                    #{element.regulatoryTypeName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.casno != null ">
                    #{element.casno,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.suppId != null ">
                    #{element.suppId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.suppName != null ">
                    #{element.suppName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
          <choose>
            <when test="element.suppCode != null ">
              #{element.suppCode,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>
            <choose>
                <when test="element.brandEname != null ">
                    #{element.brandEname,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    ''
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-11-04-->
  <select id="selectPartInfoByIdList"
          resultMap="SimpleDetailInfoDO">
    select id, fmasterid, fspec, fbidprice, fbrand, fbrandid, fgoodname, fgoodcode, product_code, fpicpath, return_status,
    return_amount, product_sn, dangerous_type_id, dangerous_type_name
    from torder_detail
    <where>
      <if test="idCollection != null and idCollection.size() > 0">
        and id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
    </where>
  </select>


<!--auto generated by MybatisCodeHelper on 2020-11-24-->
    <select id="selectLastId" resultType="java.lang.Integer">
        select
        id
        from torder_detail
        order by id desc limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-08-->
    <select id="findByIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from torder_detail
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-10-15-->
    <select id="selectEmptyCategoryTagItem" resultType="java.lang.Integer">
    select id
    from torder_detail
    where category_tag is null or category_tag=""
  </select>

  <update id="completeCategoryTagAndFeeTag" parameterType="list">
    <foreach collection="detailList" separator=";" item="updated">
      update torder_detail
      <set>
        <if test="updated.categoryTag != null">
          category_tag = #{updated.categoryTag,jdbcType=VARCHAR},
        </if>
        <if test="updated.feeTypeTag != null">
          fee_type_tag = #{updated.feeTypeTag,jdbcType=VARCHAR},
        </if>
        update_time = update_time
      </set>
      where id = #{updated.id,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2021-10-26-->
    <select id="selectIdByIdBetween" resultType="java.lang.Integer">
        select id
        from torder_detail
        where id <![CDATA[>=]]> #{minId,jdbcType=INTEGER} and id <![CDATA[<]]> #{maxId,jdbcType=INTEGER}
    </select>

  <select id="selectDateAndRelatedInfoByMasterIdBetween" resultMap="BaseResultMap">
    select id, fmasterid, fbiddate, update_time
    from torder_detail
    where fmasterid <![CDATA[>]]> #{minMasterId,jdbcType=INTEGER} and fmasterid <![CDATA[<=]]> #{maxMasterId,jdbcType=INTEGER}
  </select>

  <update id="batchUpdateBidDate">
    <foreach item="item" index="index" collection="updatedList"
             open="" separator=";">
      update torder_detail
      <set>
        <if test="item.fbiddate != null">
          fbiddate = #{item.fbiddate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="batchUpdatePrice" parameterType="com.ruijing.store.order.api.sysu.dto.OrderDetailPriceDTO">
    <foreach collection="col" item="element" index="index" separator=";">
      update torder_detail
      <set>
        <if test="element.bidPrice != null">
          fbidprice = #{element.bidPrice,jdbcType=DECIMAL},
        </if>
        <if test="element.bidAmount != null">
          fbidamount = #{element.bidAmount,jdbcType=DECIMAL},
        </if>
        <if test="element.remainderPrice != null">
          remainder_price = #{element.remainderPrice,jdbcType=DECIMAL},
        </if>
      </set>
      where id = #{element.orderDetailId,jdbcType=INTEGER}
    </foreach>
  </update>

    <update id="batchUpdateCategory" parameterType="com.ruijing.store.order.base.core.model.OrderDetailDO">
        <foreach collection="orderDetailDOList" item="element" index="index" separator=";">
            update torder_detail
            <set>
                <if test="element.firstCategoryId != null">
                    first_category_id = #{element.firstCategoryId,jdbcType=INTEGER},
                </if>
                <if test="element.firstCategoryName != null">
                    first_category_name = #{element.firstCategoryName,jdbcType=VARCHAR},
                </if>
                <if test="element.secondCategoryId != null">
                    second_category_id = #{element.secondCategoryId,jdbcType=INTEGER},
                </if>
                <if test="element.secondCategoryName != null">
                    second_category_name = #{element.secondCategoryName,jdbcType=VARCHAR},
                </if>
                <if test="element.categoryid != null">
                    CategoryID = #{element.categoryid,jdbcType=INTEGER},
                </if>
                <if test="element.fclassification != null">
                    fclassification = #{element.fclassification,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{element.id,jdbcType=INTEGER}
        </foreach>
    </update>

  <delete id="deleteInId">
    delete from torder_detail
    where id in
    <foreach item="item" index="index" collection="idCol"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>

  <!--auto generated by MybatisCodeHelper on 2022-08-02-->
    <select id="selectRemainderPriceByIdIn" resultMap="BaseResultMap">
        select id, remainder_price
        from torder_detail
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectOrderListDetailByOrderId" resultMap="BaseResultMap">
        select id, fmasterid, negotiated_price
        from torder_detail
        <where>
            fmasterid in
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId, jdbcType=INTEGER}
            </foreach>
        </where>
    </select>
</mapper>
