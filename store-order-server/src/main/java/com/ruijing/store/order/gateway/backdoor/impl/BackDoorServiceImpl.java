package com.ruijing.store.order.gateway.backdoor.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.order.whitehole.database.dto.eventlog.OpUserTypeEnum;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignDTO;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignGroupEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.FieldSortDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.constant.CategoryConstant;
import com.ruijing.store.order.gateway.backdoor.BackDoorService;
import com.ruijing.store.order.gateway.backdoor.request.BackDoorRequest;
import com.ruijing.store.order.gateway.backdoor.request.SaveElectronicSignRequest;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-03-07 15:59
 * @description:
 **/
@Service
public class BackDoorServiceImpl implements BackDoorService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private RateLimiter rateLimiter = RateLimiter.create(5);

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private WalletOrderRpcClient walletOrderRpcClient;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    public void refreezeOrder(List<String> orderNoList) {
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(orderNoList);
        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            try {
                refFundcardOrderService.reFreezeFundCard(orderMasterDO);
            } catch (Exception e) {
                logger.error(orderMasterDO.getForderno() + "重新冻结失败", e);
            }
        }
    }

    @Override
    public void compensateWalletCharge(OrderBasicParamDTO request) {
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(request.getOrderNoList());
        for(OrderMasterDO orderMasterDO : orderMasterDOList){
            walletOrderRpcClient.addOrderWalletQueue(orderMasterDO);
        }
    }

    @Override
    public void unfreezeZdfyServiceOrder(Integer startId){
        int pageSize = 200;
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setOrgIdList(New.list(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue()));
        orderSearchParamDTO.setStartHit(0);
        orderSearchParamDTO.setPageSize(200);
        orderSearchParamDTO.addSourceField("id");
        orderSearchParamDTO.addSourceField("forderno");
        orderSearchParamDTO.addSourceField("order_detail");
        orderSearchParamDTO.setStatusList(New.list(OrderStatusEnum.WaitingForConfirm.getValue(), OrderStatusEnum.WaitingForDelivery.getValue(),
                OrderStatusEnum.WaitingForReceive.getValue(), OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.SupplierApplyToCancel.getValue(),
                OrderStatusEnum.PurchaseApplyToCancel.getValue()));
        List<FieldSortDTO> fieldSortDTOList = New.list();
        fieldSortDTOList.add(new FieldSortDTO("id", SortOrderEnum.ASC));
        orderSearchParamDTO.setFieldSortList(fieldSortDTOList);
        SearchPageResultDTO<OrderMasterSearchDTO> resultDTO;
        FieldRangeDTO idRange = new FieldRangeDTO("id", startId == null ? "0" : startId.toString(), null);
        orderSearchParamDTO.setFieldRangeList(New.list(idRange));
        do {
            resultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
            if(CollectionUtils.isEmpty(resultDTO.getRecordList())){
                break;
            }
            for(OrderMasterSearchDTO searchDTO : resultDTO.getRecordList()){
                boolean allSpecialScience = searchDTO.getOrderDetail().stream().allMatch(d -> CategoryConstant.BIO_SCI_SERVICE_ID.equals(d.getSecondCategoryId())
                        || CategoryConstant.CHEMICAL_TECH_SERVICE_ID.equals(d.getSecondCategoryId())
                        || CategoryConstant.ANIMAL_EXPERIMENT_SERVICE_ID.equals(d.getSecondCategoryId()));
                if(allSpecialScience){
                    logger.info("旧数据处理，中大附一" + searchDTO.getForderno() + "解冻");
                    OrderBasicParamDTO orderBasicParamDTO = new OrderBasicParamDTO();
                    orderBasicParamDTO.setOrderId(searchDTO.getId());
                    orderFundCardService.unfreezeFundCard(orderBasicParamDTO);
                }
            }
            idRange.setLower(resultDTO.getRecordList().get(resultDTO.getRecordList().size() - 1).getId().toString());
        }while (resultDTO.getTotalHits() > pageSize);
    }


    @Override
    public void zhongShanLiuYuanRefreeze(Boolean updateFundStatusFlag, Date lastOrderDate, List<String> testOrderNos, List<String> excludeOrderNos) {
        Preconditions.notNull(lastOrderDate, "lastOrderDate不能为空");

        // 查询中山六院 不是（已关闭和完成)的订单
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByExcludeStatusAndOrgId(
                New.list(OrderStatusEnum.Close.getValue(), OrderStatusEnum.Finish.getValue()),
                OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getValue());

        // 如果指定了单号，则只处理指定单号
        if (CollectionUtils.isNotEmpty(testOrderNos)) {
            orderMasterDOList = orderMasterDOList.stream().filter(o -> testOrderNos.contains(o.getForderno())).collect(Collectors.toList());
        }

        // 如果指定了需要排除的单号，则过滤掉这些单号
        if (CollectionUtils.isNotEmpty(excludeOrderNos)) {
            orderMasterDOList = orderMasterDOList.stream().filter(o -> !excludeOrderNos.contains(o.getForderno())).collect(Collectors.toList());
        }

        //  只处理在这个时间前的订单
        orderMasterDOList = orderMasterDOList.stream().filter(order -> order.getForderdate().before(lastOrderDate)).collect(Collectors.toList());

        // 剔除 在2025年之前提交财务，且状态为"待付款"的结算单下的订单。
        Set<Integer> excludeOrderIdList1 = New.set(915995, 1752346, 1986444, 1827370, 2448281, 2463607, 2490831, 2505079, 2250897, 2548288, 2547323, 2621071, 2595386, 2545980, 2510244, 2740800, 2518987, 2765860, 2617080, 2847090, 2631065, 2745919, 2843124, 2533608, 2533616, 2533610, 2799391, 2864733, 2862580, 2839289, 2806364, 2785645, 2868072, 2806363, 2815957, 2737656, 2471019, 2461179, 2332556, 2395616, 2482470, 2460130, 2508538, 2602988, 2734669, 2756988, 2615957, 2932775, 2949358, 2903867, 3002552, 2762011, 2733485, 2681752, 2806362, 2846767, 2964603, 2670526, 2670521, 2670517, 2670493, 2844986, 2906553, 2868780, 2481923, 2721071, 2750925, 2776277, 2998148, 2851165, 2721073, 2909585, 3056505, 2840758, 2925634, 2822742, 2918391, 2775493, 2734774, 2531516, 2664994, 2999287, 3015340, 2405548, 3102645, 2839017, 3007670, 3078891, 3043390, 3043315, 1432881, 3225947, 3138649, 2853295, 2842413, 3056528, 3129496, 3146180, 3042524, 2901509, 2773348, 3058595, 3041559, 3029298, 3242696, 3196475, 3300920, 3092011, 3090650, 3090647, 3300921, 3079152, 3044491, 3501257, 3384769, 3468857, 3465284, 2969297, 3003398, 3154072, 3154888, 2723404, 2793739, 3274384, 3291364, 3324320, 3311467, 3252174, 3510936, 3418335, 3491497, 3448431, 3565061, 2753504, 2927393, 3158972, 3080276, 3534011, 3290482, 3551318, 3551316, 3551310, 3448430, 3527174, 3358394, 3393566, 3230332, 3426194, 3392977, 3392979, 3236193, 3461830, 3651431, 3264834, 3254590, 3131470, 3448429, 3235664, 2927839, 3157028, 2956430, 3650483, 3084324, 3068792, 2982434, 3067069, 1705919, 3424161, 1900583, 1761951);

        // 剔除 指定问题单
        Set<Integer> excludeOrderIdList2 = New.set(583385, 915995, 1828418, 1847460, 1901180, 1913079, 1919968, 1920001, 1920012, 1945528, 1955177, 1979248, 1989210, 1991614, 2206333, 2356368, 2750925, 2776277);

        // 剔除 已经调用过的单号
        Set<Integer> excludeOrderIdList3 = New.set();

        orderMasterDOList = orderMasterDOList.stream()
                .filter(orderMasterDO -> !excludeOrderIdList1.contains(orderMasterDO.getId())
                        && !excludeOrderIdList2.contains(orderMasterDO.getId())
                        && !excludeOrderIdList3.contains(orderMasterDO.getId()))
                .filter(orderMasterDO -> Objects.equals(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getValue(), orderMasterDO.getFuserid()))
                .collect(Collectors.toList());

        // 如果不更新经费状态，表示不是第一次调用，只执行未冻结的订单
        if (BooleanUtils.isNotTrue(updateFundStatusFlag)) {
            orderMasterDOList = orderMasterDOList.stream().filter(orderMasterDO -> OrderFundStatusEnum.UN_FREEZE.getValue().equals(orderMasterDO.getFundStatus())
                    || OrderFundStatusEnum.FreezedFail.getValue().equals(orderMasterDO.getFundStatus())).collect(Collectors.toList());
        }

        // 根据ID升序排序下
        orderMasterDOList.sort(Comparator.comparing(OrderMasterDO::getId));

        // 只在updateFundStatusFlag为true时更新订单冻结状态
        if (BooleanUtils.isTrue(updateFundStatusFlag)) {
            // 修改订单 冻结状态为未冻结,冻结失败原因置空
            List<UpdateOrderParamDTO> orderUpdatedList = orderMasterDOList.stream().map(orderMasterDO -> {
                UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
                updated.setOrderId(orderMasterDO.getId());
                updated.setFundStatus(OrderFundStatusEnum.UN_FREEZE.getValue());
                updated.setFailedReason(StringUtils.EMPTY);
                return updated;
            }).collect(Collectors.toList());

            List<List<UpdateOrderParamDTO>> partitionList = Lists.partition(orderUpdatedList, 100);
            int updateCount = 0;
            for (List<UpdateOrderParamDTO> part : partitionList) {
                orderMasterMapper.updateFieldByIdIn(part);
                updateCount += part.size();
                logger.info("批量更新中山六院订单冻结状态为未冻结,进度：{}/{}", updateCount, orderUpdatedList.size());
            }
            logger.info("批量更新中山六院订单冻结状态完成");
            for (OrderMasterDO masterDO : orderMasterDOList) {
                masterDO.setFundStatus(OrderFundStatusEnum.UN_FREEZE.getValue());
                masterDO.setFailedReason(StringUtils.EMPTY);
            }
        }

        // 调用重新冻结
        logger.info("开始重新冻结中山六院订单,总数：{}", CollectionUtils.size(orderMasterDOList));
        List<String> failedOrderNos = New.list();
        List<String> successOrderNos = New.list();

        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            try {
                rateLimiter.acquire();
                refFundcardOrderService.reFreezeFundCard(orderMasterDO);
                successOrderNos.add(orderMasterDO.getForderno());
            } catch (Exception e) {
                failedOrderNos.add(orderMasterDO.getForderno());
                logger.error(orderMasterDO.getForderno() + "中山六院重新冻结失败", e);
            }
        }

        logger.info("中山六院订单重新冻结完成，成功：{}，失败：{}", successOrderNos.size(), failedOrderNos.size());
        if (CollectionUtils.isNotEmpty(successOrderNos)) {
            logger.info("中山六院重新冻结成功订单号：{}", JSONUtil.toJsonStr(successOrderNos));
        }
        if (CollectionUtils.isNotEmpty(failedOrderNos)) {
            logger.error("中山六院重新冻结失败订单号：{}", JSONUtil.toJsonStr(failedOrderNos));
        }
    }


    @Override
    public void fixAbnormalAddress(OrderBasicParamDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "订单ID列表不能为空");
        // 添加日志前缀常量
        String LOG_PREFIX = "[cleanAddressLabel]";
        // 为每次操作创建唯一标识
        String operationId = "BATCH_" + System.currentTimeMillis();
        logger.info("{} {} 开始清洗地址标签数据", LOG_PREFIX, operationId);
        // 需要排除的中大单位机构ID
        List<Integer> excludeOrgIds = New.list(3, 62, 166);
        logger.info("{} {} 接收到需要处理的订单数：{}，将排除中山大学相关机构(ID: {})",
                LOG_PREFIX, operationId, orderIdList.size(), JSONUtil.toJsonStr(excludeOrgIds));
        // 创建一个专用的限流器，每秒5个请求
        RateLimiter addressCleanRateLimiter = RateLimiter.create(5);
        // 记录处理数据的统计信息
        int totalCount = orderIdList.size();
        int processedCount = 0;
        int successCount = 0;
        int failCount = 0;
        int excludedCount = 0;
        try {
            // 分批查询地址信息
            // 固定批次大小为50
            int batchSize = 50;
            List<List<Integer>> batchOrderIds = Lists.partition(orderIdList, 200);
            // 分批处理订单ID
            for (List<Integer> batchIds : batchOrderIds) {
                // 先获取订单主表信息，过滤掉需要排除的机构ID
                List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(New.list(batchIds));
                if (CollectionUtils.isEmpty(orderMasterList)) {
                    continue;
                }
                // 过滤掉需要排除的机构
                List<Integer> filteredOrderIds = orderMasterList.stream()
                        .filter(order -> !excludeOrgIds.contains(order.getFuserid()))
                        .map(OrderMasterDO::getId)
                        .collect(Collectors.toList());
                // 统计排除的数量
                excludedCount += (batchIds.size() - filteredOrderIds.size());
                if (CollectionUtils.isEmpty(filteredOrderIds)) {
                    logger.info("{} {} 批次中所有订单都属于排除机构，跳过处理", LOG_PREFIX, operationId);
                    continue;
                }
                // 根据过滤后的订单ID查询地址信息
                List<OrderAddressDTO> addressList = orderAddressRPCClient.findByOrderId(New.list(filteredOrderIds));
                if (CollectionUtils.isEmpty(addressList)) {
                    logger.info("{} {} 批次中未找到地址数据，跳过处理", LOG_PREFIX, operationId);
                    continue;
                }
                // 处理数据，先过滤出需要处理的记录
                List<OrderAddressDTO> addressToUpdate = addressList.stream()
                        .filter(addr -> StringUtils.isNotBlank(addr.getAddress()) && StringUtils.isNotBlank(addr.getLabel())
                                && addr.getAddress().endsWith("-" + addr.getLabel()))
                        .collect(Collectors.toList());
                logger.info("{} {} 批次中符合处理条件的记录有{}条", LOG_PREFIX, operationId, addressToUpdate.size());
                if (CollectionUtils.isNotEmpty(addressToUpdate)) {
                    // 清洗数据
                    addressToUpdate.forEach(address -> {
                        String originalAddress = address.getAddress();
                        String suffixToRemove = "-" + address.getLabel();
                        String cleanedAddress = StringUtils.substringBeforeLast(originalAddress, suffixToRemove);
                        if (StringUtils.isNotBlank(cleanedAddress)
                                && !cleanedAddress.equals(originalAddress)) {
                            address.setAddress(cleanedAddress);
                        } else {
                            logger.error("{} {} 地址清洗异常，原始地址未变化: orderId={}",
                                    LOG_PREFIX, operationId, address.getId());
                        }
                    });
                    // 分批更新
                    List<List<OrderAddressDTO>> updateBatches = Lists.partition(addressToUpdate, batchSize);
                    for (List<OrderAddressDTO> batch : updateBatches) {
                        try {
                            // 获取限流许可
                            addressCleanRateLimiter.acquire();

                            // 创建更新用的对象列表
                            List<OrderAddressDTO> updateList = batch.stream()
                                .map(address -> {
                                    OrderAddressDTO updateDTO = new OrderAddressDTO();
                                    updateDTO.setOrderNo(address.getOrderNo());
                                    updateDTO.setAddress(address.getAddress());
                                    return updateDTO;
                                })
                                .collect(Collectors.toList());

                            // 批量更新
                            orderAddressRPCClient.batchUpdateByOrderNo(updateList);

                            // 更新统计信息
                            successCount += batch.size();
                            processedCount += batch.size();
                            logger.info("{} {} 批量更新进度：{}/{}", LOG_PREFIX, operationId, processedCount, addressToUpdate.size());
                        } catch (Exception e) {
                            failCount += batch.size();
                            processedCount += batch.size();
                            logger.error("{} {} 批量更新地址失败", LOG_PREFIX, operationId, e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("{} {} 处理地址数据异常", LOG_PREFIX, operationId, e);
        } finally {
            logger.info("{} {} 地址标签数据清洗完成，总记录数：{}，排除记录数：{}，处理记录数：{}，成功：{}，失败：{}",
                    LOG_PREFIX, operationId, totalCount, excludedCount, processedCount, successCount, failCount);
        }
    }

    /**
     * 按指定速率写入数据
     * @param consumer 函数
     * @param list 数据
     * @param partitionSize 分组大小
     * @param <T> 类型
     */
    private <T> void batchWriteListAtLimitRate(Consumer<List<T>> consumer, List<T> list, Integer partitionSize){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        if(list.size() < partitionSize){
            rateLimiter.acquire();
            consumer.accept(list);
            return;
        }
        List<List<T>> partitionList = Lists.partition(list, partitionSize);
        for(List<T> part : partitionList){
            rateLimiter.acquire();
            consumer.accept(part);
        }
    }

    @Override
    public void updateApprovedUserOrderIds(BackDoorRequest backDoorRequest) {
        List<String> orderNoList = backDoorRequest.getOrderNoList();
        String jobNumber = backDoorRequest.getJobNumber();
        Integer orgId = backDoorRequest.getOrgId();
        if (CollectionUtils.isEmpty(orderNoList) || StringUtils.isBlank(jobNumber) || orgId == null) {
            logger.error("参数异常，orderNoList={}, jobNumber={}", orderNoList, jobNumber);
            return;
        }
        List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByJobNumAndOrgId(New.list(jobNumber), orgId);
        BusinessErrUtil.notEmpty(userBaseInfoDTOList, "用户不存在");

        UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOList.get(0);

        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(orderNoList);
        List<Integer> orderIdList = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        List<OrderApprovalLog> orderApprovalLogList = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(orderIdList, New.list(OrderApprovalEnum.PASS.getValue()));
        Map<Integer, List<OrderApprovalLog>> orderId2ApprovalListMap = DictionaryUtils.groupBy(orderApprovalLogList, OrderApprovalLog::getOrderId);
        List<OrderApprovalLog> updateLogList = New.list();
        for(Map.Entry<Integer, List<OrderApprovalLog>> entry : orderId2ApprovalListMap.entrySet()){
            Integer orderId = entry.getKey();
            List<OrderApprovalLog> orderApprovalLogs = entry.getValue();
            Optional<OrderApprovalLog> result = orderApprovalLogs.stream().max(Comparator.comparing(OrderApprovalLog::getCreationTime));
            if(!result.isPresent()){
                logger.info("订单审批通过记录不存在，orderId={}", orderId);
                continue;
            }

            OrderApprovalLog orderApprovalLog = result.get();
            logger.info("{}订单原审批记录：{}", orderId, JSONUtil.toJsonStr(orderApprovalLog));
            OrderApprovalLog updateLog = new OrderApprovalLog();
            updateLog.setId(orderApprovalLog.getId());
            updateLog.setOperatorId(userBaseInfoDTO.getId());
            updateLog.setOpUserType(OpUserTypeEnum.STORE.getValue());
            updateLogList.add(updateLog);

        }
        if(CollectionUtils.isNotEmpty(updateLogList)){
            orderApprovalLogMapper.updateByPrimaryKeyListSelective(updateLogList);
            logger.info("批量更新后审批记录：{}", JSONUtil.toJsonStr(updateLogList));
        }
    }

    @Override
    public void saveAcceptElectronicSign(SaveElectronicSignRequest request) {
        String orderNo = request.getOrderNo();
        Preconditions.hasLength(orderNo, "订单号不能为空");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        Preconditions.notNull(orderMasterDO, "订单不存在");
        Integer orderId = orderMasterDO.getId();
        String flastreceivemanid = orderMasterDO.getFlastreceivemanid();
        Preconditions.hasLength(flastreceivemanid, "订单验收人不存在");
        Integer orgId = orderMasterDO.getFuserid();
        UserBaseInfoDTO userInfo = userClient.getUserInfo(Integer.valueOf(flastreceivemanid), orgId);
        Preconditions.notNull(userInfo, "未找到验收人用户信息");

        // 保存验收电子签
        ElectronicSignDTO electronicSignDTO = new ElectronicSignDTO();
        electronicSignDTO.setUserGuid(userInfo.getGuid());
        electronicSignDTO.setUserName(userInfo.getName());
        electronicSignDTO.setOrgCode(orderMasterDO.getFusercode());
        electronicSignDTO.setOperation(ElectronicSignatureOperationEnum.ORDER_RECEIVE);
        electronicSignDTO.setBusinessId(String.valueOf(orderId));
        electronicSignDTO.setBusinessType(BusinessTypeEnum.ORDER);
        electronicSignDTO.setInteractionId(String.valueOf(orderId));
        electronicSignDTO.setEsSign(true);
        electronicSignDTO.setGroupCode(String.valueOf(orderMasterDO.getFbuydepartmentid()));
        electronicSignDTO.setGroupEnum(ElectronicSignGroupEnum.PURCHASE);
        electronicSignDTO.setUseSystemTypePhoto(true);
        // 跳过密码校验
        electronicSignDTO.setValidatePassword(false);
        electronicSignServiceClient.saveElectronicSign(electronicSignDTO);
    }

    @Override
    public void fillProductInfo() {
        List<Integer> orgIdList = New.list(OrgEnum.XIA_MEN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue(), OrgEnum.FU_JIAN_SHENG_ZHONG_LIU_YI_YUAN.getValue());
        List<OrderMasterDO> finalOrderMasterDOList = New.list();
        for (Integer orgId : orgIdList) {
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByOrgIdDeptIdStatus(orgId, null, New.list(OrderStatusEnum.WaitingForDelivery.getValue()));
            finalOrderMasterDOList.addAll(orderMasterDOList);
        }
        List<BaseOrderExtraDTO> alreadyFillProductInfoList = orderExtraClient.selectByOrderIdInAndExtraKey(finalOrderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList()), OrderExtraEnum.SUPP_NEED_FILL_PRODUCT_DATA.getValue());
        Map<Integer, BaseOrderExtraDTO> alreadyFillProductInfoMap = New.map();
        if(CollectionUtils.isNotEmpty(alreadyFillProductInfoList)){
            alreadyFillProductInfoMap  = DictionaryUtils.toMap(alreadyFillProductInfoList, BaseOrderExtraDTO::getOrderId, Function.identity());
        }
        List<BaseOrderExtraDTO> orderExtraDTOList = New.list();
        for (OrderMasterDO orderMasterDO : finalOrderMasterDOList) {
            boolean alreadyFill = alreadyFillProductInfoMap.containsKey(orderMasterDO.getId());
            if (alreadyFill) {
                continue;
            }
            BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
            orderExtraDTO.setOrderId(orderMasterDO.getId());
            orderExtraDTO.setOrderNo(orderMasterDO.getForderno());
            orderExtraDTO.setOrgId(orderMasterDO.getFuserid());
            orderExtraDTO.setExtraKey(OrderExtraEnum.SUPP_NEED_FILL_PRODUCT_DATA.getValue());
            orderExtraDTO.setExtraKeyDesc(OrderExtraEnum.SUPP_NEED_FILL_PRODUCT_DATA.getDesc());
            orderExtraDTO.setExtraValue("1");
            orderExtraDTOList.add(orderExtraDTO);
        }

        logger.info("批量保存订单拓展表数据：{}, 总数：{}", JSONUtil.toJsonStr(orderExtraDTOList), orderExtraDTOList.size());
        orderExtraClient.insertList(orderExtraDTOList);
    }
}
