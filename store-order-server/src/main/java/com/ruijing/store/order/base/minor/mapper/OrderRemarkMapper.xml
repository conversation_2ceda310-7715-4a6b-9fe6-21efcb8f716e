<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.OrderRemark">
    <!--@mbg.generated-->
    <!--@Table t_order_remark-->
    <id column="ftbuyappid" jdbcType="INTEGER" property="ftbuyappid" />
    <id column="fsuppid" jdbcType="INTEGER" property="fsuppid" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ftbuyappid, fsuppid, remark, creation_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_remark
    where ftbuyappid = #{ftbuyappid,jdbcType=INTEGER}
      and fsuppid = #{fsuppid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from t_order_remark
    where ftbuyappid = #{ftbuyappid,jdbcType=INTEGER}
      and fsuppid = #{fsuppid,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.ruijing.store.order.base.minor.model.OrderRemark">
    <!--@mbg.generated-->
    insert into t_order_remark (ftbuyappid, fsuppid, remark, 
      creation_time, update_time)
    values (#{ftbuyappid,jdbcType=INTEGER}, #{fsuppid,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ruijing.store.order.base.minor.model.OrderRemark">
    <!--@mbg.generated-->
    insert into t_order_remark
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ftbuyappid != null">
        ftbuyappid,
      </if>
      <if test="fsuppid != null">
        fsuppid,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ftbuyappid != null">
        #{ftbuyappid,jdbcType=INTEGER},
      </if>
      <if test="fsuppid != null">
        #{fsuppid,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.OrderRemark">
    <!--@mbg.generated-->
    update t_order_remark
    <set>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ftbuyappid = #{ftbuyappid,jdbcType=INTEGER}
      and fsuppid = #{fsuppid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.OrderRemark">
    <!--@mbg.generated-->
    update t_order_remark
    set remark = #{remark,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where ftbuyappid = #{ftbuyappid,jdbcType=INTEGER}
      and fsuppid = #{fsuppid,jdbcType=INTEGER}
  </update>

  <select id="findAllByPrimaryKey" parameterType="java.util.List" resultMap="BaseResultMap">
    SELECT ftbuyappid, fsuppid, remark from t_order_remark
    NATURAL JOIN
    (
    <foreach item="item" collection="list" separator="UNION ALL">
      SELECT
      #{item.ftbuyappid,jdbcType=INTEGER} as ftbuyappid,
      #{item.fsuppid,jdbcType=INTEGER} as fsuppid
    </foreach>
    ) t
  </select>

<!--auto generated by MybatisCodeHelper on 2020-11-12-->
  <select id="selectByFtbuyappidIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_order_remark
        where ftbuyappid in
        <foreach item="item" index="index" collection="ftbuyappidCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
<!--auto generated by MybatisCodeHelper on 2020-12-23-->
  <select id="selectByFtbuyappidAndFsuppid" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_order_remark
    <where>
        <if test="ftbuyappid != null">
         and ftbuyappid=#{ftbuyappid,jdbcType=INTEGER}</if>
        <if test="fsuppid != null">
         and fsuppid=#{fsuppid,jdbcType=INTEGER}</if>
    </where>
  </select>
</mapper>