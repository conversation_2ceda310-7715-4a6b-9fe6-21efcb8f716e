package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/12/21 16:26
 * @Description
 **/
@RpcModel("订单详情-发票信息")
public class OrderInvoiceInfoVO implements Serializable {

    private static final long serialVersionUID = 4481556126050263300L;

    /**
     * 发票id
     */
    @RpcModelProperty("发票id")
    private Integer invoiceId;

    /**
     *发票号
     */
    @RpcModelProperty("发票号")
    private String invoiceNo;

    /**
     * 发票code
     */
    @RpcModelProperty("发票code")
    private String invoiceCode;

    /**
     *发票金额
     */
    @RpcModelProperty("发票金额")
    private BigDecimal amount;

    /**
     *发票日期
     */
    @RpcModelProperty("发票日期")
    private String issueDate;

    @RpcModelProperty("发票时间戳")
    private Long issueDateTimeStamp;

    /**
     *发票备注
     */
    @RpcModelProperty("发票备注")
    private String remark;

    /**
     *开票人
     */
    @RpcModelProperty("开票人")
    private String drawer;

    /**
     *银行支行名字
     */
    @RpcModelProperty("银行支行名字")
    private String bankName;

    @RpcModelProperty(value = "联行号")
    private String bankCode;

    /**
     *银行总行名字
     */
    @RpcModelProperty("银行总行名字")
    private String bankNameCompany;

    /**
     *银行卡号
     */
    @RpcModelProperty("银行卡号")
    private String bankNo;

    /**
     *订单id
     */
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;

    /**
     *发票图片链接
     */
    @RpcModelProperty("发票图片链接")
    private List<String> picturePathList;

    @RpcModelProperty(value = "发票分类 1:全电发票，0：默认发票", required = true)
    private Integer invoiceItem;

    @RpcModelProperty(value = "纳税人识别号")
    private String taxpayerNo;

    /**
     * 类型
     * {@link com.reagent.research.statement.api.enums.InvoiceTypeEnum}
     */
    @RpcModelProperty("发票类型")
    private String type;

    @RpcModelProperty("发票附件")
    private List<OrderAttachmentVO> invoiceFileList;

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public OrderInvoiceInfoVO setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
        return this;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public OrderInvoiceInfoVO setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
        return this;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public OrderInvoiceInfoVO setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public OrderInvoiceInfoVO setIssueDate(String issueDate) {
        this.issueDate = issueDate;
        return this;
    }

    public Long getIssueDateTimeStamp() {
        return issueDateTimeStamp;
    }

    public OrderInvoiceInfoVO setIssueDateTimeStamp(Long issueDateTimeStamp) {
        this.issueDateTimeStamp = issueDateTimeStamp;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public OrderInvoiceInfoVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getDrawer() {
        return drawer;
    }

    public OrderInvoiceInfoVO setDrawer(String drawer) {
        this.drawer = drawer;
        return this;
    }

    public String getBankName() {
        return bankName;
    }

    public OrderInvoiceInfoVO setBankName(String bankName) {
        this.bankName = bankName;
        return this;
    }

    public String getBankNo() {
        return bankNo;
    }

    public OrderInvoiceInfoVO setBankNo(String bankNo) {
        this.bankNo = bankNo;
        return this;
    }

    public List<String> getPicturePathList() {
        return picturePathList;
    }

    public OrderInvoiceInfoVO setPicturePathList(List<String> picturePathList) {
        this.picturePathList = picturePathList;
        return this;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public OrderInvoiceInfoVO setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
        return this;
    }

    public String getBankNameCompany() {
        return bankNameCompany;
    }

    public OrderInvoiceInfoVO setBankNameCompany(String bankNameCompany) {
        this.bankNameCompany = bankNameCompany;
        return this;
    }

    public String getBankCode() {
        return bankCode;
    }

    public OrderInvoiceInfoVO setBankCode(String bankCode) {
        this.bankCode = bankCode;
        return this;
    }

    public Integer getInvoiceItem() {
        return invoiceItem;
    }

    public OrderInvoiceInfoVO setInvoiceItem(Integer invoiceItem) {
        this.invoiceItem = invoiceItem;
        return this;
    }

    public String getTaxpayerNo() {
        return taxpayerNo;
    }

    public OrderInvoiceInfoVO setTaxpayerNo(String taxpayerNo) {
        this.taxpayerNo = taxpayerNo;
        return this;
    }

    public String getType() {
        return type;
    }

    public OrderInvoiceInfoVO setType(String type) {
        this.type = type;
        return this;
    }

    public List<OrderAttachmentVO> getInvoiceFileList() {
        return invoiceFileList;
    }

    public void setInvoiceFileList(List<OrderAttachmentVO> invoiceFileList) {
        this.invoiceFileList = invoiceFileList;
    }


    @Override
    public String toString() {
        return new StringJoiner(", ", OrderInvoiceInfoVO.class.getSimpleName() + "[", "]")
                .add("invoiceId=" + invoiceId)
                .add("invoiceNo='" + invoiceNo + "'")
                .add("invoiceCode='" + invoiceCode + "'")
                .add("amount=" + amount)
                .add("issueDate='" + issueDate + "'")
                .add("issueDateTimeStamp=" + issueDateTimeStamp)
                .add("remark='" + remark + "'")
                .add("drawer='" + drawer + "'")
                .add("bankName='" + bankName + "'")
                .add("bankCode='" + bankCode + "'")
                .add("bankNameCompany='" + bankNameCompany + "'")
                .add("bankNo='" + bankNo + "'")
                .add("orderIdList=" + orderIdList)
                .add("picturePathList=" + picturePathList)
                .add("invoiceItem=" + invoiceItem)
                .add("taxpayerNo='" + taxpayerNo + "'")
                .toString();
    }
}
