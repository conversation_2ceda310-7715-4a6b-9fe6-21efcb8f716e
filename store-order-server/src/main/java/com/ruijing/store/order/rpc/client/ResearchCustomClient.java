package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.custom.api.apply.ApplyCustomBizService;
import com.reagent.research.custom.api.fundcard.FundcardAuthService;
import com.reagent.research.custom.dto.apply.ApplyPublicityDTO;
import com.reagent.research.custom.dto.apply.ApplyPublicityParamDTO;
import com.reagent.research.custom.dto.fundcard.FundCardAuthAndExtraDTO;
import com.reagent.research.custom.dto.fundcard.FundCardAuthParamDTO;
import com.reagent.research.sysu.order.api.dto.OrderAcceptanceAuthDTO;
import com.reagent.research.sysu.order.api.dto.OrderRequestDTO;
import com.reagent.research.sysu.order.api.service.OrderAcceptanceAuthBaseService;
import com.reagent.research.sysu.order.api.service.OrderRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Date: 2020/12/24 23:19
 */
@ServiceClient
public class ResearchCustomClient {

    @MSharpReference(remoteAppkey = "research-custom-business-service")
    private ApplyCustomBizService applyCustomBizService;

    @MSharpReference(remoteAppkey = "research-sysu-order-service")
    private OrderAcceptanceAuthBaseService orderAcceptanceAuthBaseService;

    @MSharpReference(remoteAppkey = "research-custom-business-service", timeout = "10000")
    private FundcardAuthService fundcardAuthService;

    @MSharpReference(remoteAppkey = "research-sysu-order-service", timeout = "10000")
    private OrderRpcService orderRpcService;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private OrganizationClient organizationClient;

    /**
     * 通过线下采购单id查询该采购单是否在公示中
     * @param applyId 采购单id
     * @param publicDays 公示日期
     * @param normalProduct 是否普通商品，否表示危险品
     * @return
     */
    @ServiceLog(description = "通过线下采购单id查询该采购单是否在公示中",serviceType = ServiceType.COMMON_SERVICE)
    public Boolean getApplyPublicity(Integer applyId, Integer publicDays, Boolean normalProduct) {
        OrgRequest<ApplyPublicityParamDTO> publicityRequest = new OrgRequest<>();
        ApplyPublicityParamDTO applyPublicityReq = new ApplyPublicityParamDTO();
        applyPublicityReq.setIds(Lists.newArrayList(applyId));
        applyPublicityReq.setPublicDay(publicDays);
        applyPublicityReq.setJudgeStyle(normalProduct);
        publicityRequest.setData(applyPublicityReq);
        RemoteResponse<List<ApplyPublicityDTO>> response = applyCustomBizService.getApplyPublicity(publicityRequest);
        Preconditions.notNull(response,"科研定制查询采购单公示接口异常");
        Preconditions.isTrue(response.isSuccess(),"科研定制查询采购单公示接口异常");
        List<ApplyPublicityDTO> applyPublicList = response.getData();

        if (CollectionUtils.isNotEmpty(applyPublicList)) {
            ApplyPublicityDTO applyPublicity = applyPublicList.get(0);
            if (applyPublicity != null) {
                return applyPublicity.isApplyPublicity();
            }
        }

        return false;
    }

    /**
     * @description: 查找中大订单验收审批授权信息
     * @date: 2021/2/5 11:15
     * @author: zengyanru
     * @param operatorId
     * @param authUserId
     * @param orgId
     * @return java.util.List<com.reagent.research.custom.dto.order.OrderAcceptanceAuthDTO>
     */
    @ServiceLog(description = "查找中大订单验收审批授权信息",serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderAcceptanceAuthDTO> listAuthInfoByParam(Integer operatorId, Integer authUserId, Integer orgId) {
        OrgRequest<OrderAcceptanceAuthDTO> orgRequest = new OrgRequest<>();
        OrderAcceptanceAuthDTO acceptanceAuthDTO = new OrderAcceptanceAuthDTO();
        acceptanceAuthDTO.setAuthId(authUserId);
        acceptanceAuthDTO.setOperatorId(operatorId);
        acceptanceAuthDTO.setOrgId(orgId);
        orgRequest.setData(acceptanceAuthDTO);
        OrganizationClient.SimpleOrgDTO simpleOrgInfo = organizationClient.findSimpleOrgDTOById(orgId);
        orgRequest.setOrgCode(simpleOrgInfo.getCode());
        RemoteResponse<List<OrderAcceptanceAuthDTO>> listRemoteResponse = orderAcceptanceAuthBaseService.listByParam(orgRequest);
        Preconditions.notNull(listRemoteResponse, "查找中大订单验收审批授权信息失败，请检查是否设置经费卡负责人，授权人。");
        Preconditions.isTrue(listRemoteResponse.isSuccess(), "查找中大订单验收审批授权信息失败，请检查是否设置经费卡负责人，授权人。");
        return listRemoteResponse.getData();
    }

    /**
     * @description: 获取经费卡授权人的权限信息
     * @date: 2021/2/19 17:14
     * @author: zengyanru
     * @param authUserId 被授权的人的userid
     * @param authStatus
     * @param orgCode
     * @return java.util.List<com.reagent.research.custom.dto.fundcard.FundCardAuthAndExtraDTO>
     */
    public List<FundCardAuthAndExtraDTO> getByApproveAuthQueryDtoByOrgId(Integer authUserId, Integer authStatus, String orgCode) {
        FundCardAuthParamDTO data = new FundCardAuthParamDTO();
        data.setAuthPersionId(authUserId);
        data.setStatus(authStatus);
        OrgRequest<FundCardAuthParamDTO> orgRequest = new OrgRequest<>();
        orgRequest.setData(data);
        orgRequest.setOrgCode(orgCode);
        RemoteResponse<List<FundCardAuthAndExtraDTO>> response = fundcardAuthService.listFundcardAuthAndExtra(orgRequest);
        Preconditions.notNull(response, "getByApproveAuthQueryDtoByOrgId方法获取授权信息出错");
        Preconditions.isTrue(response.isSuccess(), "getByApproveAuthQueryDtoByOrgId方法获取授权信息出错。" + response.getMsg());
        return response.getData() == null ? Collections.emptyList() : response.getData();
    }

    @ServiceLog(description = "中大退货解冻接口", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean agree2ReturnOrderForSYSU(Integer orderId, String orgCode) {
        OrgRequest<OrderRequestDTO> request = new OrgRequest<>();
        request.setOrgCode(orgCode);
        OrderRequestDTO requestBody = new OrderRequestDTO();
        requestBody.setOrderId(orderId);
        request.setData(requestBody);
        RemoteResponse<Boolean> response = orderRpcService.agree2ReturnOrder(request);
        Preconditions.isTrue(response.isSuccess(), "中大退货解冻异常" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }
}
