package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

/**
 * <AUTHOR>
 * @Date 2021/3/9 9:50
 * @Description
 **/
public class PriceContractBaseBO {

    /**
     * 价格合同id
     */
    private Long contractId;

    /**
     * 价格合同号
     */
    private String contractNo;

    public Long getContractId() {
        return contractId;
    }

    public PriceContractBaseBO setContractId(Long contractId) {
        this.contractId = contractId;
        return this;
    }

    public String getContractNo() {
        return contractNo;
    }

    public PriceContractBaseBO setContractNo(String contractNo) {
        this.contractNo = contractNo;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PriceContractBaseBO{");
        sb.append("contractId=").append(contractId);
        sb.append(", contractNo='").append(contractNo).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
