package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.other.dto.RefInvoiceOrderDTO;
import com.ruijing.store.order.api.base.refinvoiceorder.RefInvoiceOrderRPCService;
import com.ruijing.store.order.api.base.refinvoiceorder.dto.RefInvoiceOrderRequestDTO;
import com.ruijing.store.order.base.minor.mapper.RefInvoiceOrderMapper;
import com.ruijing.store.order.base.minor.model.RefInvoiceOrder;
import com.ruijing.store.order.base.minor.translator.OrderRelateTranslator;
import com.ruijing.order.annotation.ServiceLog;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyu<PERSON>i
 * @create: 2020/12/2 16:30
 **/
@MSharpService
public class RefInvoiceOrderRPCServiceImpl implements RefInvoiceOrderRPCService {

    @Resource
    private RefInvoiceOrderMapper refInvoiceOrderMapper;

    @Override
    @ServiceLog(description = "id查询订单关联发票信息")
    public RemoteResponse<List<RefInvoiceOrderDTO>> findByIdList(RefInvoiceOrderRequestDTO request) {
        List<String> idList = request.getIdList();
        Preconditions.notEmpty(idList, "查询失败，id为空！");
        Preconditions.isTrue(idList.size() < 101, "查询失败，单次查询数量不可超过100");

        List<RefInvoiceOrder> byIdListIn = refInvoiceOrderMapper.findByIdIn(idList);
        List<RefInvoiceOrderDTO> collect = byIdListIn.stream().map(OrderRelateTranslator::refInvoiceOrder2DTO).collect(Collectors.toList());
        return RemoteResponse.<List<RefInvoiceOrderDTO>>custom().setSuccess().setData(collect);
    }

    @Override
    @ServiceLog(description = "订单id查询订单关联发票信息")
    public RemoteResponse<List<RefInvoiceOrderDTO>> findByOrderIdList(RefInvoiceOrderRequestDTO request) {
        List<String> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "查询失败，订单id为空！");
        Preconditions.isTrue(orderIdList.size() < 101, "查询失败，单次查询数量不可超过100");

        List<RefInvoiceOrder> byIdListIn = refInvoiceOrderMapper.findByRefIdIn(orderIdList);
        List<RefInvoiceOrderDTO> collect = byIdListIn.stream().map(OrderRelateTranslator::refInvoiceOrder2DTO).collect(Collectors.toList());
        return RemoteResponse.<List<RefInvoiceOrderDTO>>custom().setSuccess().setData(collect);
    }

    @Override
    @ServiceLog(description = "批量插入订单关联发票信息")
    public RemoteResponse<Integer> insertList(List<RefInvoiceOrderDTO> request) {
        Preconditions.notEmpty(request, "更新失败！传参为空！");
        Preconditions.isTrue(request.size() < 101, "更新失败，单次插入数量不可超过100");

        List<RefInvoiceOrder> params = request.stream().map(OrderRelateTranslator::dtoToRefInvoiceOrder2).collect(Collectors.toList());

        int affect = refInvoiceOrderMapper.insertList(params);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    @ServiceLog(description = "id批量删除订单关联发票信息")
    public RemoteResponse<Integer> deleteByIdList(RefInvoiceOrderRequestDTO request) {
        List<String> idList = request.getIdList();
        Preconditions.notEmpty(idList, "更新失败！传参为空！");
        Preconditions.isTrue(idList.size() < 101, "更新失败，单次删除数量不可超过100");

        int affect = refInvoiceOrderMapper.deleteByIdIn(idList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    public RemoteResponse<Integer> deleteByInvoiceIdList(RefInvoiceOrderRequestDTO request) {
        List<Integer> invoiceIdList = request.getInvoiceIdList();
        Preconditions.notEmpty(invoiceIdList, "更新失败！传参为空！");
        Preconditions.isTrue(invoiceIdList.size() < 101, "更新失败，单次删除数量不可超过100");

        int affect = refInvoiceOrderMapper.deleteByInvoiceIdIn(invoiceIdList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }
}
