package com.ruijing.store.order.base.minor.model;

/**
 * <AUTHOR>
 * @Date 2020/11/3 0003 11:15
 * @Version 1.0
 * @Desc:描述
 */
/**
    * 商品描述快照
    */
public class ProductDescriptionSnapshotDO {
    private Long id;

    /**
    * 业务id
    */
    private String businessId;

    /**
    * 业务类型 1申请2竞价
    */
    private Integer businessType;

    /**
    * 商品id
    */
    private Long productId;

    /**
    * 商品描述快照
    */
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", businessType=").append(businessType);
        sb.append(", productId=").append(productId);
        sb.append(", description=").append(description);
        sb.append("]");
        return sb.toString();
    }
}