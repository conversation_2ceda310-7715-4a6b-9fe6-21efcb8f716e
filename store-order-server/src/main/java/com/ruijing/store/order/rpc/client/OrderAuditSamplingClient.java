package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.audit.sampling.dto.OrderAuditSamplingDTO;
import com.ruijing.order.saturn.api.audit.sampling.service.OrderAuditSamplingRpcService;

import java.util.List;

/**
 * @author: <PERSON>wenyu
 * @create: 2025-05-12 14:52
 * @description:
 */
@ServiceClient
public class OrderAuditSamplingClient {

    @MSharpReference(remoteAppkey = "order-saturn-service")
    private OrderAuditSamplingRpcService orderAuditSamplingRpcService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<OrderAuditSamplingDTO> listByOrderNos(List<String> orderNos){
        RemoteResponse<List<OrderAuditSamplingDTO>> response = orderAuditSamplingRpcService.listByOrderNos(orderNos);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
