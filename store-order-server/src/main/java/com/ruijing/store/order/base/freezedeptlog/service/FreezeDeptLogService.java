package com.ruijing.store.order.base.freezedeptlog.service;

import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: 冻结课题组相关业务
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/29 17:51
 **/
public interface FreezeDeptLogService {
    /**
     * 查找冻结/未冻结的课题组
     * @param isDeleted
     * @return
     */
    List<FreezeDeptLogDTO> findAllByIsDelete(int isDeleted);

    /**
     * 批量插入新的冻结课题组
     * @param list
     * @return
     */
    int insertList(@Param("list")List<FreezeDeptLogDTO> list);

    /**
     * 批量逻辑删除旧的冻结课题组
     * @param ids
     * @return
     */
    int updateDeleted(List<Long> ids);

    /**
     * 逻辑删除记录
     * @param orgId         部门/机构id
     * @param departmentId  课题组id
     * @return
     */
    int updateDeletedByOrgIdAndDepartmentIdAndType(int orgId, int departmentId);

    /**
     * 根据时间范围查询冻结课题组
     * @param isDeleted
     * @return
     */
    List<FreezeDeptLogDTO> findByBetweenCreateDate(Date minDate, Date maxDate, int isDeleted);
}
