package com.ruijing.store.order.gateway;

import com.google.common.collect.Lists;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.dto.request.ThirdPartOrderReturnDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.reagent.research.statement.api.statement.dto.StatementQueryDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.annotation.Method;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.mhttp.annotation.Mapping;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.gateway.dto.SyncOrderParamDTO;
import com.ruijing.store.order.api.gateway.dto.UnfreezeOrderRequestDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.business.service.orgondemand.AMUOrderService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.backdoor.BackDoorService;
import com.ruijing.store.order.gateway.backdoor.request.BackDoorRequest;
import com.ruijing.store.order.gateway.backdoor.request.SaveElectronicSignRequest;
import com.ruijing.store.order.gateway.backdoor.request.ZslyRequest;
import com.ruijing.store.order.gateway.buyercenter.request.*;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.TimeOutTipsVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.other.service.ReimbursementService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.enums.SyncServiceAppkeyEnum;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.statistic.api.search.service.StatisticsSearchRpcService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.usertag.client.UserTagRpcServiceClient;
import com.ruijing.store.warehouse.message.vo.inwarehouse.OrderRequestVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseApplicationPrepareSubmitVO;
import com.ruijing.store.warehouse.service.InWareHouseGWService;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import com.ruijing.user.tag.api.dto.AddUserTagRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2020/9/29 16:03
 **/
@MSharpService(isGateway = "true")
@RpcMapping("/orderPrivate")
public class OrderPrivateGWService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPrivateGWService.class);

    private static final String CAT_TYPE = "OrderPrivateGWService";

    private static final Map<Integer, String> CODE_APP_KEY_MAP = DictionaryUtils.enumToMap(SyncServiceAppkeyEnum.class, SyncServiceAppkeyEnum::getCode, SyncServiceAppkeyEnum::getAppkey);

    /**
     * 本地化部署，订单同步数量
     */
    @PearlValue(key = "order.local.deploy.sync.count", defaultValue = "2000")
    private Integer localDeploySyncCount;

    /**
     * 操作人名字，系统操作
     */
    private final String OPERATOR_NAME = "系统";

    /**
     * 操作人id，系统操作为-1
     */
    private final int OPERATOR_ID = -1;

    @Resource
    private LocalDeployOrderRPCClient localDeployOrderRPCClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Resource
    private SuppClient suppClient;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private SyncOrderServiceClient syncOrderServiceClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private WaitingStatementService waitingStatementService;

    @Resource
    private AMUOrderService amuOrderService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private StatisticsSearchRpcService statisticsSearchRpcService;

    @Resource
    private OrderManageRpcService orderManageRpcService;

    @Resource
    private InWareHouseGWService inWareHouseGWService;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private BackDoorService backDoorService;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private UserTagRpcServiceClient userTagRpcServiceClient;

    @Resource
    private ReimbursementService reimbursementService;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    /**
     * 千人千面用户标签初始化，根据提供的关键字，去匹配商品名包含关键字的订单的采购人，给对应的采购人打上用户关键字的标签
     * @param keyWords
     * @return
     */
    @RpcMapping("/initTagByOrderProduct")
    @ServiceLog(operationType = OperationType.WRITE,description = "关键字初始化用户标签接口",serviceType = ServiceType.RPC_CLIENT)
    public RemoteResponse<Integer> initTagByOrderProduct(List<String> keyWords) {
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        List<OrderMasterSearchDTO> recordList = null;
        Integer total = 0;
        int maxId = 0;
        Boolean flag = true;
        for (String keyWord : keyWords) {
            LOGGER.info("--初始化的关键字--【{}】",keyWord);
            maxId = 0;
            flag = true;
            while (flag) {
                FieldRangeDTO orderDateRangeDTO = new FieldRangeDTO("forderdate", "2023-03-01 00:00:00", null);
                FieldRangeDTO idRangeDTO = new FieldRangeDTO("id", maxId + "", null,false,false);
                orderSearchParamDTO.setFieldRangeList(New.list(orderDateRangeDTO, idRangeDTO));
                orderSearchParamDTO.setGoodsName(keyWord);
                orderSearchParamDTO.setPageSize(5000);
                FieldSortDTO fieldSortDTO = new FieldSortDTO("id",SortOrderEnum.ASC);
                List<FieldSortDTO> fieldSortDTOS = New.list(fieldSortDTO);
                orderSearchParamDTO.setFieldSortList(fieldSortDTOS);
                SearchPageResultDTO<OrderMasterSearchDTO> searchResultList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
                LOGGER.info("[{}]的条数为: {}",keyWord,searchResultList.getTotalHits());
                recordList = searchResultList.getRecordList();
                total += recordList.size();
                if (CollectionUtils.isNotEmpty(recordList)) {
                    maxId = recordList.get(recordList.size() - 1).getId();
                    List<AddUserTagRequest> requestList = recordList.stream().map(OrderMasterSearchDTO::getFbuyerid).distinct().map(buyerId -> {
                        AddUserTagRequest addUserTagRequest = new AddUserTagRequest();
                        addUserTagRequest.setUserId(buyerId);
                        addUserTagRequest.setTag(keyWord);
                        addUserTagRequest.setTagType(2);
                        addUserTagRequest.setFirstSourceId(1000);
                        addUserTagRequest.setSecondSourceName("商品标签");
                        return addUserTagRequest;
                    }).collect(Collectors.toList());
                    List<List<AddUserTagRequest>> lists = ListUtils.splitCollection(200, requestList);
                    for (List<AddUserTagRequest> list : lists) {
                        userTagRpcServiceClient.addUserTag(list);
                    }
                }
                if (searchResultList.getTotalHits()< 5000){
                    flag = false;
                }
            }
        }
        LOGGER.info("总共执行了：{}",total);
        return RemoteResponse.success(total);
    }
    /**
     * 根据订单号重新推送订单
     * @param request  订单号
     * @return
     */
    @RpcMapping("/rePushOrder")
    public RemoteResponse<Boolean> rePushOrder(OrderListRequest request){
        String orderNo = request.getOrderNo();
        Preconditions.notNull(orderNo, "订单号不能为空");
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderNo);
        Preconditions.notNull(orderMaster,orderNo + "查不到订单信息~！");
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMaster.getId());
        Preconditions.notEmpty(orderDetailDOList, orderNo + "无订单详情信息 ~！");
        orderManageService.pushOrderToThirdPlatformByThunder(New.list(orderMaster),orderDetailDOList);
        return RemoteResponse.<Boolean>custom().setData(Boolean.TRUE).setSuccess();
    }

    /**
     * 订单同步数据转发
     * @param request
     * @return
     */
    @RpcMapping(value = "/syncOrderData")
    public RemoteResponse<Boolean> syncOrderData(SyncOrderParamDTO request) {
        String token = request.getToken();
        // 暂时没想好token值，先用邮箱代替吧
        Preconditions.isTrue(token.equals("<EMAIL>"), "你没权限！");

        Boolean success = sendSyncOrderRequest(request.getDays(), request.getNumberList(), request.getKey());
        return RemoteResponse.<Boolean>custom().setSuccess().setData(success);
    }

    /**
     * 发送http请求到同步订单服务进行同步
     * @return
     */
    public Boolean sendSyncOrderRequest(Integer days, List<String> numberList, Integer key) {
        SyncDataRequestDTO request = new SyncDataRequestDTO();
        request.setLastFewDays(days);
        request.setNumberList(numberList);
        if (!SyncServiceAppkeyEnum.ALL.getCode().equals(key)) {
            syncOrderServiceClient.sync(request, CODE_APP_KEY_MAP.get(key));
        } else {
            CODE_APP_KEY_MAP.entrySet().stream().filter(m -> !SyncServiceAppkeyEnum.ALL.getCode().equals(m.getKey())).forEach(m -> syncOrderServiceClient.sync(request, m.getValue()));
        }
        return true;
    }

    /**
     * 解冻已关闭的订单
     * @param unfreezeOrderRequest
     * @return
     */
    @RpcMapping(value = "/unfreezeClosedOrder")
    public RemoteResponse<String> unfreezeClosedOrder(UnfreezeOrderRequestDTO unfreezeOrderRequest) {
        String verifyCode = unfreezeOrderRequest.getUnfreezeVerify();
        Integer orderId = unfreezeOrderRequest.getOrderId();

        String result = "";

        // 校验code是否正确，以防其他人乱调用接口
        if ("normt$9Al".equals(verifyCode)) {
            // 以后看看怎么设置好账号密码防止被取到
            Preconditions.notNull(orderId,"输入订单id不可为空");
            try {
                orderManageService.orderFundCardUnFreezeById(orderId);
                result = "成功";
            } catch (Exception e) {
                result = "解冻失败";
            }
        } else {
            result = "口令错误";
        }
        return RemoteResponse.<String>custom().setData(result).setSuccess();
    }

    /**
     * 订单手动关闭
     * @param request
     * @return
     */
    @RpcMapping(value = "/orderCloseByNoList")
    public RemoteResponse<Integer> orderCloseByNoList(OrderMasterCommonReqDTO request) {
        List<String> orderMasterNoList = request.getOrderMasterNoList();
        Preconditions.notEmpty(orderMasterNoList, "关闭订单失败！订单号为空");
        Date shutDownDate = new Date();

        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(orderMasterNoList);
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(0);
        }
        // 记录订单关闭日志
        List<OrderApprovalLogDTO> approvalLogDTOList = new ArrayList<>();
        for (OrderMasterDO orederItem : orderMasterDOList) {
            OrderApprovalLogDTO logDTO = new OrderApprovalLogDTO();
            logDTO.setOrderId(orederItem.getId());
            logDTO.setPhoto(StringUtils.EMPTY);
            logDTO.setReason("系统关闭订单");
            logDTO.setApproveStatus(OrderApprovalEnum.CANCEL.getValue());
            logDTO.setOperatorId(OPERATOR_ID);
            logDTO.setCreationTime(shutDownDate);
            logDTO.setApproveDescription("系统关闭订单");
            logDTO.setOperatorName(OPERATOR_NAME);
            approvalLogDTOList.add(logDTO);
        }



        orderApprovalLogService.insertOrderApprovalLogList(approvalLogDTOList);
        OrderMasterDO updated = new OrderMasterDO();
        updated.setStatus(OrderStatusEnum.Close.getValue());
        updated.setShutDownDate(shutDownDate);
        orderMasterMapper.updateByOrderNoIn(updated, orderMasterNoList);

        return RemoteResponse.<Integer>custom().setSuccess().setData(orderMasterNoList.size());
    }

    @RpcMapping(value = "/saveFundCards")
    public RemoteResponse<Boolean> saveFundCards(PrivateChangeFundCardRequestDTO request) {
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setOrgId(request.getOrgId());
        rjSessionInfo.setGuid(request.getGuid());
        rjSessionInfo.setUserType(RjUserTypeEnum.STORE_USER);
        boolean skipCheckCardEqual = request.getSkipCheckCardEqual() != null ? request.getSkipCheckCardEqual() : false;
        refFundcardOrderService.saveFundCardSkipVerify(request.getRequest(), rjSessionInfo, skipCheckCardEqual);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 手动发起结算的后门接口
     * @param request
     * @return
     */
    @RpcMapping(value = "/manualStatement")
    public RemoteResponse<List<UpdateOrderParamDTO>> manualStatement(OrderBasicParamDTO request) {
        List<String> orderNoList = request.getOrderNoList();
        Preconditions.notEmpty(orderNoList, "orderNoList not empty");

        List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(orderNoList);
        // 待结算或结算中但没有结算id的单（没结算id即未成功发起结算）
        List<OrderMasterDO> collect = orderList.stream().filter(o -> OrderStatusEnum.WaitingForStatement_1.getValue().equals(o.getStatus())
        || (OrderStatusEnum.Statementing_1.getValue().equals(o.getStatus()) && o.getStatementId() == null)).collect(Collectors.toList());
        Preconditions.notEmpty(collect, "输入的单据不满足条件！");

        List<UpdateOrderParamDTO> updatedStatus = new ArrayList<>(collect.size());
        for (OrderMasterDO item : collect) {
            orderStatementService.createStatement(item, item.getFbuyerid(), item.getFbuyername(), item.getInventoryStatus().intValue());
        }
        return RemoteResponse.<List<UpdateOrderParamDTO>>custom().setSuccess().setData(updatedStatus);
    }

    /**
     * 手动推送待结算单的后门接口
     * @param request
     * @return
     */
    @RpcMapping(value = "/manualWaitingStatement")
    public RemoteResponse<List<Integer>> manualWaitingStatement(OrderBasicParamDTO request) {
        List<String> orderNoList = request.getOrderNoList();
        Preconditions.notEmpty(orderNoList, "orderNoList not empty");

        List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(orderNoList);
        Map<String, List<OrderMasterDO>> collect = orderList.stream().filter(o -> OrderStatusEnum.WaitingForStatement_1.getValue().equals(o.getStatus())).collect(Collectors.groupingBy(OrderMasterDO::getFusercode, Collectors.toList()));
        Preconditions.notEmpty(collect, "waiting statement order not empty");

        collect.forEach((orgCode, orderMasterDOS) -> {
            waitingStatementService.pushWaitingStatement(orgCode, ListUtils.toList(orderMasterDOS, OrderMasterDO::getId));
        });

        return RemoteResponse.<List<Integer>>custom().setSuccess().setData(ListUtils.toList(orderList, OrderMasterDO::getId));
    }

    /**
     * 检查订单在数据库和es的差异情况
     * @param request
     * @return
     */
    @RpcMapping(value = "/checkOrderData")
    public RemoteResponse<Boolean> checkDBAndESWriteToExcel(SyncOrderParamDTO request) {
        String token = request.getToken();
        // 暂时没想好token值，先用邮箱代替吧
        Preconditions.isTrue(token.equals("<EMAIL>"), "你没权限！");

        SyncDataRequestDTO checkRequest = new SyncDataRequestDTO();
        checkRequest.setLastFewDays(request.getDays());
        checkRequest.setNumberList(request.getNumberList());
        checkRequest.setLastHours(request.getHours());
        checkRequest.setLastMinutes(request.getMinutes());

        Boolean checkSuccess = syncOrderServiceClient.checkDBAndESWriteToExcel(checkRequest);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(checkSuccess);
    }

    @RpcMapping(value = "/syncOrderDateBetweenMasterAndDetail")
    public RemoteResponse<Boolean> syncOrderDateBetweenMasterAndDetail(SyncOrderParamDTO syncOrderParamDTO) {
        Preconditions.isTrue("<EMAIL>".equals(syncOrderParamDTO.getToken()), "token 不正确");
        AsyncExecutor.listenableRunAsync(() -> {
            Integer maxOrderId = orderMasterMapper.selectMaxId();
            Integer lowerOrderId = 0;
            Integer batchSize = 500;
            while (lowerOrderId < maxOrderId) {
                List<OrderMasterDO> masterList = orderMasterMapper.selectIdAndDateBetween(lowerOrderId, lowerOrderId + batchSize);
                List<OrderDetailDO> detailList = orderDetailMapper.selectDateAndRelatedInfoByMasterIdBetween(lowerOrderId, lowerOrderId + batchSize);
                Map<Integer, String> orderIdDateMap = masterList.stream().collect(
                        Collectors.toMap(OrderMasterDO::getId,
                                s -> s.getForderdate() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, s.getForderdate()).split(" ")[0],
                                (ov, nv) -> nv));
                // 匹配和设置新的时间（以master为金标准）
                List<OrderDetailDO> correctedDetailList = New.list();
                String detailDateFormat = "yyyy-MM-dd";
                for (OrderDetailDO detail : detailList) {
                    String detailDate = detail.getFbiddate() == null ? "null" : DateUtils.format(detailDateFormat, detail.getFbiddate());
                    String masterDate = orderIdDateMap.get(detail.getFmasterid());
                    if (masterDate != null && !detailDate.equals(masterDate)) {
                        OrderDetailDO newDetail = new OrderDetailDO();
                        newDetail.setId(detail.getId());
                        newDetail.setFbiddate(DateUtils.parse(detailDateFormat, masterDate));
                        // 务必注意，updatetime须保持原样！
                        newDetail.setUpdateTime(detail.getUpdateTime());
                        correctedDetailList.add(newDetail);
                    }
                }
                try {
                    if (CollectionUtils.isNotEmpty(correctedDetailList)) {
                        orderDetailMapper.batchUpdateBidDate(correctedDetailList);
                    }
                } catch (Exception e) {
                    throw e;
                }
                lowerOrderId += batchSize;
            }
        }).addFailureCallback(throwable -> {
            Cat.logError("OrderPrivateGWService", "syncOrderDateBetweenMasterAndDetail", throwable.getMessage(), throwable);
        });
        return RemoteResponse.success();
    }

    @RpcMapping("/syncAllStatementStatus")
    public RemoteResponse<Boolean> syncAllStatementStatus(SyncOrderParamDTO syncOrderParamDTO) {
        Preconditions.isTrue("<EMAIL>".equals(syncOrderParamDTO.getToken()), "token 不正确");
        Integer lastFewDays = syncOrderParamDTO.getDays();
        Integer lastHours = syncOrderParamDTO.getHours();
        List<String> orderNoList = syncOrderParamDTO.getNumberList();
        Integer lastMinutes = syncOrderParamDTO.getMinutes();

        // 手动同步最近 lastHours 小时的数据
        if (lastHours != null && lastHours > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusHours(-(lastHours)).atZone(ZoneId.systemDefault());
            List<Integer> masterIdList = orderMasterMapper.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
            this.updateStatementStatusAsync(masterIdList, zonedDateTime);
        } else if (lastFewDays != null && lastFewDays > 0) {
            // 手动同步最近 lastFewDays 天的数据
            ZonedDateTime zonedDateTime = LocalDate.now().plusDays(-(lastFewDays)).atStartOfDay().atZone(ZoneId.systemDefault());
            List<Integer> masterIdList = orderMasterMapper.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
            this.updateStatementStatusAsync(masterIdList, zonedDateTime);
        } else if (lastFewDays != null && lastFewDays == -1L) {
            // get order total
            Integer maxId = orderMasterMapper.selectMaxId();
            int max = maxId.intValue();
            int count = 1;
            try {
                // 每次从数据库取500条数据
                while (count < max) {
                    List<OrderMasterDO> masterList = orderMasterMapper.findByAfterMinAndBeforeMax(count, count += 500);
                    this.updateStatementStatus(masterList);
                }
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "syncFullData", "全量同步订单数据失败", e);
                Preconditions.isTrue(false, "同步订单结算子状态出错" + e.getMessage());
            }
        } else if (CollectionUtils.isNotEmpty(orderNoList)) {
            // 手动同步单号为orderNoList的数据
            List<OrderMasterDO> updatedOrderList = orderMasterMapper.findByFordernoIn(orderNoList);
            this.updateStatementStatus(updatedOrderList);
        } else if (lastMinutes != null && lastMinutes > 0) {
            ZonedDateTime zonedDateTime = LocalDateTime.now().plusMinutes(-(lastMinutes)).atZone(ZoneId.systemDefault());
            List<Integer> masterIdList = orderMasterMapper.findIdByUpdateTimeAfter(Date.from(zonedDateTime.toInstant()));
            this.updateStatementStatusAsync(masterIdList, zonedDateTime);
        }
        return RemoteResponse.success();
    }

    /**
     * 异步更新订单结算子状态
     * @param masterIdList
     * @param zonedDateTime
     */
    private void updateStatementStatusAsync(List<Integer> masterIdList, ZonedDateTime zonedDateTime) {
        AsyncExecutor.listenableRunAsync(() -> {
            List<List<Integer>> lastUpdatedIdPartition = Lists.partition(masterIdList, 500);
            for (List<Integer> updatedIdList : lastUpdatedIdPartition) {
                List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(new HashSet<>(updatedIdList));
                this.updateStatementStatus(orderMasterList);
            }
        }).addFailureCallback(throwable -> {
            Cat.logError(CAT_TYPE, "syncAllStatementStatus", "手动同步" + zonedDateTime.toLocalDateTime() + "之后的订单结算子状态失败", throwable);
        });
    }

    /**
     * 传入订单master信息，更新结算子状态
     * @param orderMasterList
     */
    private void updateStatementStatus(List<OrderMasterDO> orderMasterList) {
        List<Integer> statementIdList = orderMasterList.stream().map(OrderMasterDO::getStatementId).collect(Collectors.toList());
        Map<Long, Integer> statementIdStatusMap = this.constructStateIdStatusMap(statementIdList);
        List<UpdateOrderParamDTO> updateMasterList = New.list();
        for (OrderMasterDO orderMaster : orderMasterList) {
            if (orderMaster.getStatementId() == null) {
                continue;
            }

            UpdateOrderParamDTO updateMaster = new UpdateOrderParamDTO();
            updateMaster.setStatementStatus(statementIdStatusMap.get(orderMaster.getStatementId().longValue()));
            updateMaster.setOrderId(orderMaster.getId());
            updateMaster.setUpdateTime(orderMaster.getUpdateTime());
            updateMasterList.add(updateMaster);
        }
        if (CollectionUtils.isNotEmpty(updateMasterList)) {
            orderMasterMapper.updateFieldByIdIn(updateMasterList);
        }
    }

    /**
     * 构造statement id 到 子状态的map
     * @param statementIdList
     * @return
     */
    private Map<Long, Integer> constructStateIdStatusMap(List<Integer> statementIdList) {
        StatementQueryDTO query = new StatementQueryDTO();
        List<Long> statementIdLongList = New.list();
        for (Integer statementId : statementIdList) {
            if (statementId != null) {
                statementIdLongList.add(Long.valueOf(statementId));
            }
        }
        if (CollectionUtils.isEmpty(statementIdLongList)) {
            return new HashMap<>();
        }

        Integer sepPartSize = 500;
        List<StatementResultDTO> statementList = New.list();
        List<List<Long>> partition = Lists.partition(statementIdLongList, sepPartSize);
        for (List<Long> part : partition) {
            query.setStatementIds(New.list(part));
            query.setPageSize(sepPartSize);
            List<StatementResultDTO> tempStatementList = statementPlatformClient.findStatementByPage(query);
            statementList.addAll(tempStatementList);
        }

        Map<Long, Integer> statemenIdStatusMap = new HashMap<>();
        for (StatementResultDTO statement : statementList) {
            statemenIdStatusMap.put(statement.getId(), statement.getStatus());
        }
        return statemenIdStatusMap;
    }

    @RpcMapping("/exportLujunyiDeptTree")
    public RemoteResponse<List<String>> exportLujunyiDeptTree(OrderListRequest request) {
        Preconditions.isTrue("<EMAIL>".equals(request.getSearch()), "token（search）不正确，请重新输入正确的token值");
        List<String> resUrlList = amuOrderService.exportDeptOrderAmount(request);
        return RemoteResponse.<List<String>>custom().setData(resUrlList).setSuccess();
    }

    /**
     * 后门查看www订单详情
     * @param request slang 用 guid_orgid的方式输入
     * @return
     * @throws ParseException
     */
    @RpcMapping("/orderDetail")
    public RemoteResponse<OrderInfoVO> getOrderDetail(OrderBasicParamDTO request) throws ParseException {
        Preconditions.notNull(request,"获取订单详情接口入参为空");
        String slang = request.getSlang();
        String[] s = slang.split("_");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(s[0], Integer.valueOf(s[1]), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        OrderInfoVO orderInfo = orderDetailRelatedService.getOrderDetail(loginInfo, request, false);
        return RemoteResponse.<OrderInfoVO>custom().setData(orderInfo).setSuccess();
    }

    /**
     * 后门 据条件返回 成功退货金额总和 的聚合结果
     * @param paramDTO
     * @return
     */
    @RpcMapping("/aggReturnAmountByEntities")
    public RemoteResponse<List<GoodsReturnAggResDTO>> getAggReturnAmountByEntities(StatisticsManagerParamDTO paramDTO) {
        return statisticsSearchRpcService.aggReturnAmountByEntities(paramDTO);
    }

    @RpcMapping("/rePushNotMigratedOrgOrder")
    @ServiceLog(description = "重推未迁移的单位订单", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> rePushNotMigratedOrgOrder(OrderBasicParamDTO orderBasicParamDTO) {
        String[] arr = orderBasicParamDTO.getSlang().split("&");
        Preconditions.isTrue("<EMAIL>".equals(arr[0]), "你没权限!");
        String event = arr.length == 1 ? null : arr[1];
        OrderEventTypeEnum eventType = event == null ? null : OrderEventTypeEnum.valueOf(event);
        List<String> orderNoList = orderBasicParamDTO.getOrderNoList();

        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(orderNoList);
        int successCount = 0;
        int totalCount = orderNoList.size();
        for(OrderMasterDO orderMasterDO : orderMasterDOList){
            try{
                if(eventType == null){
                    // 没指定，按正常走
                    this.setRjSession(orderMasterDO.getFbuyerid(), orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());
                    RemoteResponse<Boolean> response = orderManageRpcService.retryPushOrderByOrderNo(orderMasterDO.getForderno());
                    if(response.isSuccess() && Boolean.TRUE.equals(response.getData())){
                        successCount++;
                    }
                }else {
                    // 有指定按指定走
                    ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderMasterDO);
                    List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
                    if (CollectionUtils.isNotEmpty(detailDOList)) {
                        thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
                    }
                    thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, eventType, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
                    successCount++;
                }
            }catch (Exception e){
                LOGGER.error("重推订单");
            }
        }
        return RemoteResponse.success(true).setMsg("success:"+successCount+",total:"+totalCount);
    }

    @RpcMapping("/getPrepareSubmitInfoByOrderId")
    @ServiceLog(description = "获取手动入库可入库库房列表", operationType = OperationType.WRITE)
    public RemoteResponse<WarehouseApplicationPrepareSubmitVO> getPrepareSubmitInfoByOrderId(OrderBasicParamDTO orderBasicParamDTO) {
        Preconditions.notNull(orderBasicParamDTO, "获取订单详情接口入参为空");
        String slang = orderBasicParamDTO.getSlang();
        String[] s = slang.split("_");
        this.setRjSession(Integer.valueOf(s[0]), null, Integer.valueOf(s[1]));
        OrderRequestVO request = new OrderRequestVO();
        request.setOrderId(orderBasicParamDTO.getOrderId());
        return RemoteResponse.success(inWareHouseGWService.getPrepareSubmitInfoByOrderId(request));
    }

    @RpcMapping("/getFundCardDataToChangeCard")
    @ServiceLog(description = "获取换卡数据", operationType = OperationType.WRITE)
    public RemoteResponse<ChangeFundCardRequestDTO> getFundCardDataToChangeCard(PrivateChangeFundCardRequestDTO privateChangeFundCardRequestDTO) {
        // !!注意，使用前先和已发生的请求对比一下数据结构是否一致
        Preconditions.notNull(privateChangeFundCardRequestDTO, "获取换卡数据为空");
        // 获取orgId
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(privateChangeFundCardRequestDTO.getOrgId());
        // 获取单位经费卡层级
        String cardLevelString = sysConfigClient.getConfigByOrgCodeAndConfigCode(simpleOrgDTO.getCode(), ConfigConstant.RESEARCH_FUNDCARD_LEVEL);
        int cardLevel = NumberUtils.toInt(cardLevelString);
        List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(simpleOrgDTO.getCode(), New.list(privateChangeFundCardRequestDTO.getCardId()));
        ChangeFundCardRequestDTO changeFundCardRequestDTO = new ChangeFundCardRequestDTO();
        List<FundCardProjectRequestDTO> fundCardProjectRequestDTOList = New.list();
        changeFundCardRequestDTO.setSaveProjectList(fundCardProjectRequestDTOList);
        for(FundCardDTO firstLevelCard : fundCardDTOList){
            String firstLevelCardId =  firstLevelCard.getId();
            List<FundCardDTO> secondLevelFundCardList = firstLevelCard.getFundCardDTOs();
            // 装填一级经费卡数据
            FundCardProjectRequestDTO fundCardProjectRequestDTO = new FundCardProjectRequestDTO();
            fundCardProjectRequestDTO.setId(firstLevelCardId);
            fundCardProjectRequestDTO.setFundType(firstLevelCard.getFundType());
            fundCardProjectRequestDTO.setProjectCode(firstLevelCard.getCode());
            fundCardProjectRequestDTOList.add(fundCardProjectRequestDTO);
            // 如果是一级卡 就不继续封装了
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == cardLevel){
                continue;
            }
            List<OrderFundCardRequestDTO> orderFundCardRequestDTOList = New.list();
            fundCardProjectRequestDTO.setSaveFundCardList(orderFundCardRequestDTOList);
            for(FundCardDTO secondLevelCard : secondLevelFundCardList){
                fundCardProjectRequestDTO.setProjectName(secondLevelCard.getName());
                String secondLevelCardId = secondLevelCard.getId();
                List<FundCardDTO> thirdLevelFundCardList = secondLevelCard.getFundCardDTOs();
                OrderFundCardRequestDTO orderFundCardRequestDTO = new OrderFundCardRequestDTO();
                orderFundCardRequestDTO.setCardId(secondLevelCardId);
                orderFundCardRequestDTO.setCardNo(secondLevelCard.getCode());
                orderFundCardRequestDTO.setSaveFundCardSubjectList(New.emptyList());
                orderFundCardRequestDTOList.add(orderFundCardRequestDTO);
                if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel){
                    continue;
                }
                List<FundCardSubjectRequestDTO> fundCardSubjectRequestDTOList = New.list();
                orderFundCardRequestDTO.setSaveFundCardSubjectList(fundCardSubjectRequestDTOList);
                for(FundCardDTO thirdLevelCard : thirdLevelFundCardList){
                    FundCardSubjectRequestDTO fundCardSubjectRequestDTO = new FundCardSubjectRequestDTO();
                    fundCardSubjectRequestDTO.setId(thirdLevelCard.getId());
                    fundCardSubjectRequestDTO.setSubjectCode(thirdLevelCard.getCode());
                    fundCardSubjectRequestDTOList.add(fundCardSubjectRequestDTO);
                }
            }
        }
        return RemoteResponse.success(changeFundCardRequestDTO);
    }

    @RpcMapping("/rePushReturnData")
    @ServiceLog(description = "重推退货单数据，传returnNo", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> rePushReturnData(OrderBasicParamDTO orderBasicParamDTO){
        Preconditions.isTrue("<EMAIL>".equals(orderBasicParamDTO.getSlang()), "你没权限!");
        Preconditions.notNull(orderBasicParamDTO.getReturnNo());
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByReturnNoIn(New.list(orderBasicParamDTO.getReturnNo()));
        Preconditions.notEmpty(goodsReturnList, "没有这个退货单");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(goodsReturnList.get(0).getOrderNo());
        Preconditions.notNull(orderMasterDO, "退货单没订单");
        GoodsReturn goodsReturn = goodsReturnList.get(0);

        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        Integer returnStatus = goodsReturn.getGoodsReturnStatus();
        boolean needPushReturn = false;
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            boolean syncResult = OmsDockingConfigValueEnum.PUSH_BY_RUIJING.name().equals(config.getOrderDockingConfigDTO().getOrderSyncReplyReturnGoodsResult()) && (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(returnStatus) || GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(returnStatus));
            boolean syncAcceptReturnGoods = OmsDockingConfigValueEnum.PUSH_BY_RUIJING.name().equals(config.getOrderDockingConfigDTO().getOrderSyncSuppAcceptReturnGoods()) && GoodsReturnStatusEnum.SUCCESS.getCode().equals(returnStatus);
            needPushReturn = syncResult || syncAcceptReturnGoods;
        } else {
            needPushReturn = dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.RETURN_ALL_RETURN_AND_NOTICE, OrderDockingStrategyEnum.RETURN_PART_RETURN_AND_NOTICE, OrderDockingStrategyEnum.PUSH_RETURN_COMPLETE_STATUS));
        }
        // 新单位走order—thunder-service接口
        if (needPushReturn) {
            ThirdPartOrderReturnDTO thirdPartReturnInfo = GoodsReturnTranslator.doToThirdPartOrderDTO(goodsReturn);
            // 订单确认推送管理平台
            thirdPartOrderRPCClient.pushReturnInfo(orderMasterDO.getFusercode(), thirdPartReturnInfo, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
        }else {
            return RemoteResponse.<Boolean>custom().setData(false).setMsg("该单位没有开启退货推送");
        }
        return RemoteResponse.success();
    }

    @RpcMapping("/refreezeOrder")
    @ServiceLog(description = "重新冻结", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> refreezeOrder(OrderBasicParamDTO request){
        Preconditions.notNull(request);
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        backDoorService.refreezeOrder(request.getOrderNoList());
        return RemoteResponse.success();
    }

    @RpcMapping("/reimbursementConfigChange")
    @ServiceLog(description = "通知代结算配置变更", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> reimbursementConfigChange(OrderBasicParamDTO request){
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        Preconditions.isTrue(request.getOrgId() != null && request.getSuppId() != null);
        reimbursementService.configChange(request.getOrgId(), request.getSuppId());
        return RemoteResponse.success();
    }

    @RpcMethod("获取订单全部条形码批次信息, orderNo必填")
    @RpcMapping("/outputAllOrderBatches")
    public RemoteResponse<OrderBatchesVO> outputAllOrderBatches(OrderBarCodeQryReqDTO request) {
        OrderBatchesVO result = orderUniqueBarCodeRPCClient.getOrderBatches(request.getOrderNo(), request.getTypeList());
        return RemoteResponse.<OrderBatchesVO>custom().setSuccess().setData(result);
    }

    @RpcMapping("/checkDeptTimeoutStat")
    @RpcMethod("校验部门超时状况")
    public RemoteResponse<TimeOutTipsVO> checkDeptTimeoutStat(OrderBasicParamDTO request){
        String slang = request.getSlang();
        String[] s = slang.split("_");
        BusinessErrUtil.isTrue("<EMAIL>".equals(s[0]), "no permission");
        List<TimeOutTipsVO> timeOutTips = timeoutQueryService.checkDeptTimeoutStat(request.getOrgId(),Integer.valueOf(s[1]));
        return CollectionUtils.isNotEmpty(timeOutTips) ? RemoteResponse.success(timeOutTips.get(0)) : RemoteResponse.success(null);
    }

    @RpcMapping("/compensateWalletCharge")
    @RpcMethod("钱包扣费补偿")
    public RemoteResponse<Boolean> compensateWalletCharge(OrderBasicParamDTO request){
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        backDoorService.compensateWalletCharge(request);
        return RemoteResponse.success();
    }

    @RpcMapping("/unfreezeZdfyServiceOrder")
    @RpcMethod("中大附一特殊服务类订单解冻")
    public RemoteResponse<Boolean> unfreezeZdfyServiceOrder(OrderBasicParamDTO request){
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        backDoorService.unfreezeZdfyServiceOrder(request.getOrderId());
        return RemoteResponse.success();
    }


    @RpcMapping("/systemCancelGoodsReturn")
    @RpcMethod("系统取消退货申请")
    public RemoteResponse<Boolean> systemCancelGoodsReturn(OrderBasicParamDTO request){
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        BusinessErrUtil.notNull(request.getOrderId(), "orderId不能为空");
        String remark = StringUtils.isBlank(request.getRemark()) ? "" : request.getRemark();
        return RemoteResponse.success(buyerGoodsReturnService.systemCancelGoodsReturn(New.list(request.getOrderId()), GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN, remark));
    }

    @RpcMapping("/zhongShanLiuYuanRefreeze")
    @RpcMethod("旧数据处理-中山六院重新冻结经费")
    public RemoteResponse<Boolean> zhongShanLiuYuanRefreeze(ZslyRequest zslyRequest) {
        Preconditions.isTrue("<EMAIL>".equals(zslyRequest.getSlang()), "没权限");
        backDoorService.zhongShanLiuYuanRefreeze(zslyRequest.getUpdateFundStatusFlag(), zslyRequest.getLastOrderDate(), zslyRequest.getTestOrderNos(), zslyRequest.getExcludeOrderNos());
        return RemoteResponse.success();
    }

    @RpcMapping("/fixAbnormalAddress")
    @RpcMethod("修复异常地址数据")
    public RemoteResponse<Boolean> fixAbnormalAddress(
            @RpcMethodParam(includePropertyNames = {"slang", "orderIdList"}) OrderBasicParamDTO request) {
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        backDoorService.fixAbnormalAddress(request);
        return RemoteResponse.success();
    }

    /**
     * 设置rjsession
     *
     * @param userId 用户id
     * @param suppId 供应商id
     * @param orgId  单位id
     */
    private void setRjSession(Integer userId, Integer suppId, Integer orgId) {
        Map<String, Object> attachments = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        UserBaseInfoDTO userInfo = userClient.getUserDetailByID(userId);
        Preconditions.notNull(userInfo, "找不到userId" + userId + "的用户信息");
        sessionInfo.setGuid(userInfo.getGuid());
        sessionInfo.setSuppId(suppId);
        sessionInfo.setOrgId(orgId);
        sessionInfo.setUserId(userId.longValue());
        sessionInfo.setUserType(RjUserTypeEnum.STORE_USER);
        attachments.put(GatewayConstant.SESSION_INFO, sessionInfo);

        RpcContext.getProviderContext().setCallAttachments(attachments);
    }



    @RpcMapping("/checkPersonalTimeoutStat")
    @RpcMethod("校验个人超时状况")
    public RemoteResponse<TimeOutTipsVO> checkPersonalTimeoutStat(OrderListRequest orderListRequest){
        Integer fbuyerId = orderListRequest.getFbuyerId();
        Integer orgId = orderListRequest.getOrgId();
        List<UserBaseInfoDTO> userByIdsAndOrgId = userClient.getUserByIdsAndOrgId(New.list(fbuyerId), orgId);
        UserBaseInfoDTO userBaseInfoDTO = userByIdsAndOrgId.get(0);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(userBaseInfoDTO.getGuid(), orgId, false, null);
        List<TimeOutTipsVO> timeOutTips = timeoutQueryService.checkPersonalTimeoutStat(orgId, loginInfo.getUserId());
        return CollectionUtils.isNotEmpty(timeOutTips) ? RemoteResponse.success(timeOutTips.get(0)) : RemoteResponse.success(null);
    }

    @RpcMapping("/updateApprovedUserOrderIds")
    public RemoteResponse<Boolean> updateApprovedUserOrderIds(BackDoorRequest backdoorRequest){
        Preconditions.isTrue("<EMAIL>".equals(backdoorRequest.getSlang()));
        backDoorService.updateApprovedUserOrderIds(backdoorRequest);
        return RemoteResponse.success();
    }

    @Mapping("/saveAcceptElectronicSign")
    @Method("保存确认验收电子签")
    public RemoteResponse<Boolean> saveAcceptElectronicSign(SaveElectronicSignRequest request) {
        Preconditions.isTrue("<EMAIL>".equals(request.getSlang()), "没权限");
        backDoorService.saveAcceptElectronicSign(request);
        return RemoteResponse.success();
    }

    /**
     * 同步厦门妇幼，福建肿瘤 订单处于待发货下 填写商品信息字段
     *
     * @return
     */
    @Mapping("/fillProductInfo")
    @Method("补充 填写商品信息字段")
    public RemoteResponse<Boolean> fillProductInfo(BackDoorRequest backDoorRequest) {
        Preconditions.isTrue("<EMAIL>".equals(backDoorRequest.getSlang()), "没权限");
        backDoorService.fillProductInfo();
        return RemoteResponse.success();
    }
}
