package com.ruijing.store.order.base.core.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderOperatorDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderDetailFeeTypeVO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2019/10/9 15:11
 **/
public interface RefFundcardOrderService {

    /**
     * 添加一条经费卡关联信息记录
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse insertRefFundcardOrder(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 更新经费卡关联信息
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse updateRefFundcardOrderByOrderId(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 通过订单id物理删除经费卡关联信息
     * @param orderId
     * @return
     */
    RemoteResponse deleteRefFundcardOrderByOrderId(String orderId);

    /**
     * 通用换卡接口
     * 根据订单id 删除之前的关联，再新增关联
     * @param refFundCardOrderDTOS
     * @param callInterfaceFlag  是否调用外部接口
     * @return
     */
    RemoteResponse changeFundCardCommon(List<RefFundcardOrderDTO> refFundCardOrderDTOS, OrderOperatorDTO user, boolean isRefreeze, boolean callInterfaceFlag, String changeReason);

    /**
     * 通过订单id 查询经费卡关联信息
     * @param orderIdList
     * @return
     */
    List<RefFundcardOrderDTO> findByOrderIdList(List<Integer> orderIdList);

    /**
     * 通过订单id 查询ref经费卡关联信息
     * @param orderIdList
     * @return
     */
    List<RefFundcardOrderDTO> findByOrderId(List<String> orderIdList);

    /**
     * 采购单id 批量查询经费卡关联关系
     * @param applicationIdList
     * @return
     */
    List<RefFundcardOrderDTO> findByApplicationIdList(List<Integer> applicationIdList);

    /**
     * 根据经费开id集合查询订单id集合
     * @param cardIds
     * @return
     */
    List<String> findOrderIdsByCardIds(List<String> cardIds);

    /**
     * 根据采购申请id查询经费卡
     * @param applicationId
     * @return
     */
    List<RefFundcardOrderDO> findByApplicationId(String applicationId);

    /**
     * 根据订单id查询经费卡
     * @param orderId
     * @return
     */
    List<RefFundcardOrderDO> findRefundcardOrderByOrderId(String orderId);

    /**
     * 根据竞价单id查询经费卡
     * @param bidId
     * @return
     */
    List<RefFundcardOrderDO> findByBidId(String bidId);

    /**
     * 更新经费卡关联信息
     * @param refFundcardOrderDTO
     * @return
     */
    Integer updateRefFundCardOrderByApplicationId(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 更新经费卡关联信息
     * @param refFundcardOrderDTO
     * @return
     */
    Integer updateRefFundCardOrderByBId(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 批量删除绑卡记录
     * @param idList
     * @return
     */
    Integer deleteByIdList(List<String> idList);

    /**
     * 校验经费卡，迁移待结算换卡需要用。中肿需要特殊校验consumablesFee和analysisFee
     * @param refFundcardOrderDTO   换卡数组
     * @param user                  操作用户
     * @param consumablesFee        实验耗材费
     * @param analysisFee           测试分析费
     */
    void validateFundCard(List<RefFundcardOrderDTO> refFundcardOrderDTO,
                          OrderOperatorDTO user,
                          BigDecimal consumablesFee,
                          BigDecimal analysisFee);

    /**
     * 校验经费卡是否必填，非必填返回true，必填返回false
     * @param orderIdList   订单idList
     * @param user          用户信息
     * @return
     */
    Boolean fundCardNotRequired(List<Integer> orderIdList, OrderOperatorDTO user);

    /**
     * 待结算更换经费卡业务接口（含权限校验）
     * @param request 请求参数
     * @param rjSessionInfo 登录信息
     */
    void saveFundCardForWaitStatement(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo);



    /**
     * 更换逻辑核心接口
     * @param userInfo 用户信息
     * @param orgId    组织id
     * @param request  请求参数
     * @param isRefreeze 是否冻结
     * @param skipCheckCardEqual 是否跳过校验卡相同
     * @param callInterfaceFlag 是否调用对接单位接口
     */
    void changeFundCard(UserBaseInfoDTO userInfo, Integer orgId, ChangeFundCardRequestDTO request, boolean isRefreeze, boolean skipCheckCardEqual, boolean callInterfaceFlag);


        /**
         * 结算换卡业务接口（含权限校验）
         * @param request 请求参数
         * @param rjSessionInfo 登录信息
         */
    void saveFundCardForStatement(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo);

    /**
     * 待结算更换经费卡业务接口（含权限校验）
     *
     * @param request            请求参数
     * @param rjSessionInfo      登录信息
     * @param skipCheckCardEqual 是否跳过校验卡相同
     */
    void saveFundCardSkipVerify(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo, boolean skipCheckCardEqual);

    /**
     * 重新冻结经费卡（用于换卡失败的订单）
     * @param orderId 订单id
     */
    void reFreezeFundCard(Integer orderId);

    /**
     * 重新冻结经费卡（用于换卡失败的订单）
     * @param orderMasterDO 订单数据
     */
    void reFreezeFundCard(OrderMasterDO orderMasterDO);

    /**
     * 获取订单报账金额
     * @param orderIdList
     * @return
     */
    List<OrderDetailFeeTypeVO> getOrderDetailFeeType(List<Integer> orderIdList);

    /**
     * 订单冻结
     * @param order           订单信息
     * @param orderDetailList 订单明细
     * @param operator        订单操作人id
     * @return 成功的数量
     */
    Integer orderFundCardFreeze(OrderMasterDO order, List<OrderDetailDO> orderDetailList, LoginUserInfoBO operator);

    /**
     * 订单冻结经费
     * @param request
     * @return
     */
    Integer orderFundCardFreeze(OrderBasicParamDTO request);

    /**
     * delete reflect fund card by order id
     * @param orderIdList
     * @return count of deleted
     */
    int deleteByOrderIds(List<Integer> orderIdList);

    void fillWithNewDTO(List<RefFundcardOrderDTO> newRefFundCardOrderDTOS);
}
