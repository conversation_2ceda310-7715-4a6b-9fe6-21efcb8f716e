package com.ruijing.store.order.gateway.common.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-04-27 15:20
 * @description:
 **/
@RpcModel("通用选项的子对象")
public class CommonOptionItemVO<K,V> implements Serializable {

    private static final long serialVersionUID = -4509012506445391347L;

    @RpcModelProperty("键")
    private K key;

    @RpcModelProperty("值")
    private V value;

    public K getKey() {
        return key;
    }

    public void setKey(K key) {
        this.key = key;
    }

    public V getValue() {
        return value;
    }

    public void setValue(V value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CommonOptionItemVO.class.getSimpleName() + "[", "]")
                .add("key=" + key)
                .add("value=" + value)
                .toString();
    }
}
