package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 扫码退货结果响应对象
 */
@RpcModel("扫码退货结果")
public class BarCodeReturnResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("成功退货的订单信息")
    private List<BarCodeReturnInfoVO> successList;

    @RpcModelProperty("失败退货的订单信息")
    private List<BarCodeReturnInfoVO> failureList;

    @RpcModelProperty("是否全部成功")
    private Boolean allSuccess;
    
    @RpcModelProperty("是否全部失败")
    private Boolean allFailure;

    public List<BarCodeReturnInfoVO> getSuccessList() {
        return successList;
    }

    public BarCodeReturnResultVO setSuccessList(List<BarCodeReturnInfoVO> successList) {
        this.successList = successList;
        return this;
    }

    public List<BarCodeReturnInfoVO> getFailureList() {
        return failureList;
    }

    public BarCodeReturnResultVO setFailureList(List<BarCodeReturnInfoVO> failureList) {
        this.failureList = failureList;
        return this;
    }

    public Boolean getAllSuccess() {
        return allSuccess;
    }

    public BarCodeReturnResultVO setAllSuccess(Boolean allSuccess) {
        this.allSuccess = allSuccess;
        return this;
    }

    public Boolean getAllFailure() {
        return allFailure;
    }

    public BarCodeReturnResultVO setAllFailure(Boolean allFailure) {
        this.allFailure = allFailure;
        return this;
    }

    @Override
    public String toString() {
        return "BarCodeReturnResultVO{" +
                "successList=" + successList +
                ", failureList=" + failureList +
                ", allSuccess=" + allSuccess +
                ", allFailure=" + allFailure +
                '}';
    }
}