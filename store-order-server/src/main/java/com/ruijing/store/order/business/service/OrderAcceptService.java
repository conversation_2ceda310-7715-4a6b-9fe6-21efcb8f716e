package com.ruijing.store.order.business.service;

import com.ruijing.fundamental.cat.util.Pair;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderAcceptanceConfigVO;

import java.util.List;
import java.util.Map;

/**
 * 模块拆分，订单验收服务类
 */
public interface OrderAcceptService {

    /**
     * 获取订单验收配置
     * @param orderId 订单id
     * @return验收配置
     */
    OrderAcceptanceConfigVO getOrderAcceptanceConfig(Integer orderId);

    /**
     * 用户手动验收订单
     * @param orderReceiptParamDTO
     * @return
     */
    default ReceiptOrderResponseDO userAcceptOrder(OrderReceiptParamDTO orderReceiptParamDTO) {
        return null;
    }

    /**
     * 自动验收订单
     * @param params 订单验收参数
     * @param configMap 配置映射
     * @return 验收结果
     */
    default ReceiptOrderResponseDO autoAcceptOrder(OrderReceiptParamDTO params, Map<String, String> configMap){
        return null;
    }

    /**
     * 批量自动验收全部单位的订单
     */
    default void batchAutoAcceptAllOrder() {

    }

    /**
     * 计算订单验收分支
     * @param orderMasterDO     订单
     * @param orderDetailList   订单明细
     * @param isAcceptApproval  是否验收审批
     * @param platformWorkFunds 是否使用平台经费
     * @param unRelateOrderData 是否未对接经费的订单
     * @return
     */
    default Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval,
                                         boolean platformWorkFunds, boolean unRelateOrderData) {
        return 3;
    }

    /**
     * 检查验收条件
     * @param params
     * @param receiptConfigMap
     * @param isManual
     * @return
     */
    default Pair<OrderMasterDO, List<OrderDetailDO>> checkedConditionAndReturnOrderInfo(OrderReceiptParamDTO params, Map<String, String> receiptConfigMap, Boolean isManual) {
        return null;
    }

    /**
     * 计算入库模式
     * 1.无须入库
     * 2.待入库
     * 3.新库房自动入库
     * 4.新库房批量插入入库申请单
     * @param orderMasterDO 订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList 订单商品详情
     * @return 入库模式
     */
    InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList);

    /**
     * 根据入库模式执行入库
     * @param inWarehouseModeEnum 入库模式
     * @param orderMasterDO 订单主表
     * @param orderDetailList 订单详情
     * @param picUrl 验收图片
     * @return 入库状态
     */
    WarehouseResultDTO executeInWareHouse(InWarehouseModeEnum inWarehouseModeEnum, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, String picUrl);

    /**
     * 校验验收图片上传
     *
     * @param receiptConfigMap 验收配置
     * @param orderSnapshot    订单快照
     * @param orderDetailList  订单商品快照
     * @param picUrlList       订单上传图片
     */
    void validateAcceptPicUpload(Map<String, String> receiptConfigMap, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList, List<String> picUrlList);

    /**
     * 校验订单详情图片关联配置
     *
     * @param receiptConfigMap  配置
     * @param detailPictureList 订单详情图片关联信息
     */
    void validateDetailPicUpload(Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList, List<AcceptPictureDTO> detailPictureList);

    /**
     * 校验订单详情附件关联配置
     *
     * @param receiptConfigMap     配置
     * @param orderDetailList      订单详情列表
     * @param detailAttachmentList 订单详情附件关联信息
     * @return 验证结果
     */
    void validateDetailAttachmentUpload(Map<String, String> receiptConfigMap,
                                        List<OrderDetailDO> orderDetailList,
                                        List<AcceptAttachmentDTO> detailAttachmentList);

    /**
     * 校验附件上传规则
     *
     * @param receiptConfigMap         oms配置映射
     * @param attachmentList           验收附件列表（可为空）
     * @param orderVideoAttachmentList 订单视频附件列表
     * @param orderDetailList          订单详情列表（不可为空）
     */
    void validationAttachment(Map<String, String> receiptConfigMap,
                              List<AttachmentDTO> attachmentList,
                              List<AttachmentDTO> orderVideoAttachmentList,
                              List<OrderDetailDO> orderDetailList);

    /**
     * 拍照验收定制化处理,返回true时跳过后续校验
     *
     * @param picUrlList      验收图片
     * @param orderMasterDO   订单master
     * @param orderDetailList 订单detail
     * @return 拍照验收定制化处理, 返回true时跳过后续校验
     */
    boolean customValidationPicture(List<String> picUrlList, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList);
}
