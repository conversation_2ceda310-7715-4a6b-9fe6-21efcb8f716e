package com.ruijing.store.order.base.docking.service;

import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/2 18:00
 * @description
 */
public interface DockingConfigCommonService {

    /**
     * 获取配置
     * @param orgCode 机构代码
     * @return 配置
     */
    OrgDockingConfigDTO getConfig(String orgCode);

    /**
     * 是否需要旧推送（不走MQ)
     * @param orderData 订单数据
     * @param outerBuyerDockingTypeEnumList 对接枚举
     * @return 是否需要走旧推送
     */
    @Deprecated
    boolean getIfNeedOldPush(OrderMasterDO orderData, List<OuterBuyerDockingTypeEnum> outerBuyerDockingTypeEnumList);

    /**
     * 这个策略是否需要对接，数组传多个则有一个匹配则需要
     * @param orderData 订单数据
     * @param dockingStrategyEnumList 对应的对接策略
     * @return 是否需要走该策略的对接
     */
    @Deprecated
    boolean getIfNeedDocking(OrderMasterDO orderData, List<OrderDockingStrategyEnum> dockingStrategyEnumList);

    /**
     * 是否走OMS新对接配置
     * @param config 配置
     * @param orderData 订单数据
     * @param orderDetailDOList 订单详情数据
     * @return 是否走OMS新对接配置
     */
    boolean isNewDockingEnable(OrgDockingConfigDTO config, OrderMasterDO orderData, List<OrderDetailDO> orderDetailDOList);

    /**
     * 获取符合oms新对接配置的单
     * @param config 配置
     * @param searchDTOList 搜索结果
     * @return 符合配置的单
     */
    List<Integer> getEnableNewDockingOrderList(OrgDockingConfigDTO config, List<OrderMasterSearchDTO> searchDTOList);

    /**
     * 获取是否推送订单（生成订单才推送）
     *
     * @param orgCode 机构代码
     * @return 是否推送订单
     */
    boolean getIfOrgPushOrderAfterGenerate(String orgCode);
}
