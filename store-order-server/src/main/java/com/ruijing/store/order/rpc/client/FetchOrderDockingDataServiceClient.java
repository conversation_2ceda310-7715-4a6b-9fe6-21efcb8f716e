package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.inner.buyer.FetchOrderDockingDataService;
import com.reagent.order.dto.extra.OrderExtraBigDataDTO;
import com.reagent.order.dto.inner.buyer.FetchDataRequestDTO;
import com.reagent.order.dto.inner.buyer.FetchDataResponseItemDTO;
import com.reagent.order.enums.DockingDataTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;

import java.util.List;

import static com.ruijing.order.enums.OperationType.WRITE;

/**
 * @author: ch<PERSON><PERSON><PERSON><PERSON>g
 * @createTime: 2024-07-11 18:45
 * @description:
 **/
@ServiceClient
public class FetchOrderDockingDataServiceClient {

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private FetchOrderDockingDataService fetchOrderDockingDataService;

    @ServiceLog(description = "保存订单数据，此字段对应数据库中长度较大的列", operationType = WRITE)
    public void saveOrderExtraBigData(OrderExtraBigDataDTO data) {
        Preconditions.notNull(data, "入参不能为空");
        RemoteResponse<Boolean> response = fetchOrderDockingDataService.saveOrderExtraBigData(data);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "获取订单拓展数据（长度较大的）", operationType = WRITE)
    public List<FetchDataResponseItemDTO> fetchOrderExtraBigData(List<String> orderNoList, DockingDataTypeEnum fetchType){
        FetchDataRequestDTO fetchDataRequestDTO = new FetchDataRequestDTO();
        fetchDataRequestDTO.setOrderNoList(orderNoList);
        fetchDataRequestDTO.setFetchTypeValue(fetchType.getType());
        RemoteResponse<List<FetchDataResponseItemDTO>> response = fetchOrderDockingDataService.fetchOrderExtraBigData(fetchDataRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
