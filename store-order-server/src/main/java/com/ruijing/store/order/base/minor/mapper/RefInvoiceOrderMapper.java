package com.ruijing.store.order.base.minor.mapper;
import com.ruijing.store.order.base.minor.model.RefInvoiceOrder;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface RefInvoiceOrderMapper {
    int deleteByPrimaryKey(String id);

    int insert(RefInvoiceOrder record);

    int insertSelective(RefInvoiceOrder record);

    RefInvoiceOrder selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RefInvoiceOrder record);

    int updateByPrimaryKey(RefInvoiceOrder record);

    /**
     * 根据订单id批量查询发票信息
     * @param refIdCollection
     * @return
     */
    List<RefInvoiceOrder> findByRefIdIn(@Param("refIdCollection")List<String> refIdCollection);

    /**
     * 根据发票id批量查询发票信息
     * @param invoiceIdCollection
     * @return
     */
    List<RefInvoiceOrder> findByInvoiceIdIn(@Param("invoiceIdCollection")Collection<Integer> invoiceIdCollection);

    /**
     * 批量删除发票关联信息
     * @param invoiceIdCollection
     * @return
     */
    int deleteByInvoiceIdIn(@Param("invoiceIdCollection")Collection<Integer> invoiceIdCollection);

    /**
     * 批量插入发票关联信息
     * @param list
     * @return
     */
    int insertList(@Param("list")List<RefInvoiceOrder> list);

    /**
     * 通过id批量查询
     * @param idCollection
     * @return
     */
    List<RefInvoiceOrder> findByIdIn(@Param("idCollection")Collection<String> idCollection);

    /**
     * 通过id批量删除
     * @param idCollection
     * @return
     */
    int deleteByIdIn(@Param("idCollection")Collection<String> idCollection);

}