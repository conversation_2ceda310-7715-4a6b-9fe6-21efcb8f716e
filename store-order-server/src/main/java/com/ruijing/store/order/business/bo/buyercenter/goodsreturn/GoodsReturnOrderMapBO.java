package com.ruijing.store.order.business.bo.buyercenter.goodsreturn;

import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.SimpleDetailInfoDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2020/11/5 11:30
 * @Description
 **/
public class GoodsReturnOrderMapBO {

    private List<OrderMasterSearchDTO> torderMasters;

    private Map<Integer, Set<SimpleDetailInfoDO>> detailMap;

    private Map<Integer, List<GoodsReturn>> goodReturnMap;

    private Map<Integer, Integer> detailSuppIdMap;

    public List<OrderMasterSearchDTO> getTorderMasters() {
        return torderMasters;
    }

    public void setTorderMasters(List<OrderMasterSearchDTO> torderMasters) {
        this.torderMasters = torderMasters;
    }

    public Map<Integer, Set<SimpleDetailInfoDO>> getDetailMap() {
        return detailMap;
    }

    public void setDetailMap(Map<Integer, Set<SimpleDetailInfoDO>> detailMap) {
        this.detailMap = detailMap;
    }

    public Map<Integer, List<GoodsReturn>> getGoodReturnMap() {
        return goodReturnMap;
    }

    public void setGoodReturnMap(Map<Integer, List<GoodsReturn>> goodReturnMap) {
        this.goodReturnMap = goodReturnMap;
    }

    public Map<Integer, Integer> getDetailSuppIdMap() {
        return detailSuppIdMap;
    }

    public void setDetailSuppIdMap(Map<Integer, Integer> detailSuppIdMap) {
        this.detailSuppIdMap = detailSuppIdMap;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnOrderMapBO{");
        sb.append("torderMasters=").append(torderMasters);
        sb.append(", detailMap=").append(detailMap);
        sb.append(", goodReturnMap=").append(goodReturnMap);
        sb.append(", detailSuppIdMap=").append(detailSuppIdMap);
        sb.append('}');
        return sb.toString();
    }
}
