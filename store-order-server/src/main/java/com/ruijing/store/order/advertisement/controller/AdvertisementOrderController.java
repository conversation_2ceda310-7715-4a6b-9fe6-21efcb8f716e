package com.ruijing.store.order.advertisement.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderDTO;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderRequest;
import com.ruijing.store.order.api.advertisement.service.AdvertisementOrderRpcService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description oms广告管理页接口
 * @date 2023/9/4 15:49
 */
@MSharpService
@RpcMapping("/oms/advertisementOrder")
@RpcApi("oms广告投放订单相关接口")
public class AdvertisementOrderController {

    @Resource
    private AdvertisementOrderRpcService advertisementOrderRpcService;

    /**
     * 根据入参查询订单信息
     * @param request
     * @return
     */
    @RpcMapping("/list")
    @RpcMethod("分页查询广告投放相关订单的交易详情")
    public PageableResponse<List<AdvertisementOrderDTO>> listAdvertisementOrder(RjSessionInfo rjSessionInfo, AdvertisementOrderRequest request){
        Preconditions.notNull(rjSessionInfo, "用户信息不可为空");
        BusinessErrUtil.isTrue(RjUserTypeEnum.OMS_USER.equals(rjSessionInfo.getUserType()), ExecptionMessageEnum.NON_OMS_USER_CANNOT_VIEW);
        return advertisementOrderRpcService.listAdvertisementOrder(request);
    }
}
