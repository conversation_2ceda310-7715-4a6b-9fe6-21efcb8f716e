package com.ruijing.store.order.business.service.impl.strategy;

import cn.hutool.core.util.StrUtil;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.research.fundcard.enums.FundTypeEnum;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 中科院医学所 订单验收定制策略
 *
 * <AUTHOR>
 */
@Service(OrgConst.ZHONG_KE_YUAN_ZHONG_JI_SUO + OrderAcceptConstant.ACCEPT_SUFFIX)
public class ZhongKeYuanZhongJiSuoOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        List<RefFundcardOrderDO> fundcardOrderDOList = refFundcardOrderMapper.findByOrderId(String.valueOf(orderMasterDO.getId()));
        // 没有绑卡数据，走验收-完成模式
        if (CollectionUtils.isEmpty(fundcardOrderDOList)) {
            LOGGER.info("中科院医学所,未绑定经费卡，走非定制验收策略,orderId={}", orderMasterDO.getId());
            return super.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
        }
        Integer fundType = fundcardOrderDOList.get(0).getFundType();

        // 锐竞平台经费  走验收-完成模式，不走结算
        if (Objects.equals(FundTypeEnum.NOT_FINANCIAL.getValue(), fundType)) {
            return 1;
        }
        return super.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
    }
}