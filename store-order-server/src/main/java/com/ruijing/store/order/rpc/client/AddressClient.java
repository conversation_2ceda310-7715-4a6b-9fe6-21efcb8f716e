package com.ruijing.store.order.rpc.client;

import com.ruijing.base.address.api.dto.LogisticsSubscribeIdDTO;
import com.ruijing.base.address.api.dto.LogisticsSubscribeInfoListDTO;
import com.ruijing.base.address.api.dto.NewDeliveryAddressLabelDTO;
import com.ruijing.base.address.api.dto.NewDeliveryAddressLabelQuery;
import com.ruijing.base.address.api.service.LogisticsSubscribeRpcService;
import com.ruijing.base.address.api.service.NewDeliveryAddressLabelRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 基础服务 地址Client
 * @Date: 2024/12/23 11:47
 **/
@ServiceClient
public class AddressClient {

    @MSharpReference(remoteAppkey = "base-biz-address-service")
    private NewDeliveryAddressLabelRpcService newDeliveryAddressLabelRpcService;

    @MSharpReference(remoteAppkey = "base-biz-address-service")
    private LogisticsSubscribeRpcService logisticsSubscribeRpcService;

    @ServiceLog(description = "根据orgId和labelName获取地址标签信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<NewDeliveryAddressLabelDTO> queryAddressLabelByOrgIdAndLabelName(Integer orgId, String labelName) {
        NewDeliveryAddressLabelQuery newDeliveryAddressLabelQuery = new NewDeliveryAddressLabelQuery();
        newDeliveryAddressLabelQuery.setOrgId(orgId);
        newDeliveryAddressLabelQuery.setLabelName(labelName);
        RemoteResponse<List<NewDeliveryAddressLabelDTO>> response = newDeliveryAddressLabelRpcService.queryForList(newDeliveryAddressLabelQuery);
        BusinessErrUtil.isTrue(response.isSuccess(), "根据orgId和labelName获取地址标签信息失败, msg = " + response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "获取物流轨迹信息", serviceType = ServiceType.RPC_CLIENT)
    public LogisticsSubscribeInfoListDTO getLogisticsSubscribeInfo(Long subscribeId){
        LogisticsSubscribeIdDTO logisticsSubscribeIdDTO = new LogisticsSubscribeIdDTO();
        logisticsSubscribeIdDTO.setSubscribeId(subscribeId);
        RemoteResponse<LogisticsSubscribeInfoListDTO> response = logisticsSubscribeRpcService.getLogisticsSubscribeInfo(logisticsSubscribeIdDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
