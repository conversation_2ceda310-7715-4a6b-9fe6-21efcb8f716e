package com.ruijing.store.order.base.orderconfirm.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmForTheRecordRequest;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmRecordInfoRequest;
import com.ruijing.store.order.base.orderconfirm.service.OrderConfirmForTheRecordService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/8 58
 */
@MSharpService
@RpcMapping("/confirmForTheRecord")
public class OrderConfirmController {

    @Resource
    private OrderConfirmForTheRecordService orderConfirmForTheRecordService;

    @RpcMethod("确认备案")
    @RpcMapping("/confirm")
    public RemoteResponse<Boolean> confirmForTheRecord(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request) {
        orderConfirmForTheRecordService.confirmForTheRecord(rjSessionInfo, request);
        return RemoteResponse.success();
    }

    @RpcMethod("添加备案图片")
    @RpcMapping("/addConfirmPics")
    public RemoteResponse<Boolean> addConfirmPics(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request) {
        orderConfirmForTheRecordService.addConfirmPics(rjSessionInfo, request);
        return RemoteResponse.success();
    }

    @RpcMethod("查询备案信息")
    @RpcMapping("/confirmRecordInfo")
    public RemoteResponse<OrderConfirmForTheRecordDTO> confirmRecordInfo(RjSessionInfo rjSessionInfo, OrderConfirmRecordInfoRequest request) {
        return RemoteResponse.success(orderConfirmForTheRecordService.confirmRecordInfo(rjSessionInfo, request));
    }
}
