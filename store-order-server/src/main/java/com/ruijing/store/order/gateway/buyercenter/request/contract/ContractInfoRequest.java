package com.ruijing.store.order.gateway.buyercenter.request.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON>g <PERSON>
 * @Date: 2020/12/29 11:08
 */
@RpcMethod("合同-合同信息")
public class ContractInfoRequest implements Serializable {

    private static final long serialVersionUID = -1688767546381479707L;

    /**
     * 合同地址
     */
    @RpcModelProperty("合同地址")
    private String contractLocation;

    /**
     * 合同名字
     */
    @RpcModelProperty("合同名字")
    private String contractName;

    public String getContractLocation() {
        return contractLocation;
    }

    public ContractInfoRequest setContractLocation(String contractLocation) {
        this.contractLocation = contractLocation;
        return this;
    }

    public String getContractName() {
        return contractName;
    }

    public ContractInfoRequest setContractName(String contractName) {
        this.contractName = contractName;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ContractInfoRequest{");
        sb.append("contractLocation='").append(contractLocation).append('\'');
        sb.append(", contractName='").append(contractName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
