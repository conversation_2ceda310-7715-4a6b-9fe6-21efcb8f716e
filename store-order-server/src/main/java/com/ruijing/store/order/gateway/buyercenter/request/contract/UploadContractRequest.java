package com.ruijing.store.order.gateway.buyercenter.request.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Date: 2020/12/29 11:08
 */
@RpcModel("合同-上传合同请求体")
public class UploadContractRequest implements Serializable {

    private static final long serialVersionUID = 4711715712846708759L;

    /**
     * 合同信息列表
     */
    @RpcModelProperty("合同信息列表")
    private List<ContractInfoRequest> contractInfoList;

    /**
     * 合同编号
     */
    @RpcModelProperty("合同编号")
    private String uploadContractNo;

    /**
     * 订单id列表
     */
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;

    public List<ContractInfoRequest> getContractInfoList() {
        return contractInfoList;
    }

    public UploadContractRequest setContractInfoList(List<ContractInfoRequest> contractInfoList) {
        this.contractInfoList = contractInfoList;
        return this;
    }

    public String getUploadContractNo() {
        return uploadContractNo;
    }

    public UploadContractRequest setUploadContractNo(String uploadContractNo) {
        this.uploadContractNo = uploadContractNo;
        return this;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public UploadContractRequest setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UploadContractRequest{");
        sb.append("contractInfoList=").append(contractInfoList);
        sb.append(", uploadContractNo='").append(uploadContractNo).append('\'');
        sb.append(", orderIdList=").append(orderIdList);
        sb.append('}');
        return sb.toString();
    }
}
