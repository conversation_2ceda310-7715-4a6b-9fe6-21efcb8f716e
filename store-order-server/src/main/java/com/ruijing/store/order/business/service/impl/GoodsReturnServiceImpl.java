package com.ruijing.store.order.business.service.impl;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.other.dto.DangerousTagDTO;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.OrderPullParamDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.SimpleDetailInfoDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.business.enums.DangerousTagEnum;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnOrderRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnOrderBriefVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnOrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnRespVO;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.PageResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2019/10/16 15:42
 **/
@Service
@RpcApi
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class GoodsReturnServiceImpl implements GoodsReturnService {

    @PearlValue(key = "common.countLimit",defaultValue ="1000")
    private Integer countLimit = 1000;

    /**
     * 超时${returnTimeoutDays}天未验收的退货单
     */
    @PearlValue(key = "return.timeout.days",defaultValue ="14")
    private Integer returnTimeoutDays = 14;

    @Autowired
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    private static final Logger logger = LoggerFactory.getLogger(GoodsReturnServiceImpl.class);

    /**
     * 获取 创建时间 和 更新时间 在范围内的 退货单
     * @param orderPullParamDTO
     * @return
     */
    @Override
    public BasePageResponseDTO<GoodsReturn> getRangeDateReturnOrder(OrderPullParamDTO orderPullParamDTO){
        Integer pageSize = orderPullParamDTO.getPageSize();
        Assert.isTrue(pageSize <= countLimit,"pageSize不能大于"+countLimit+"数据");
        String startTime = orderPullParamDTO.getStartTime();
        Assert.notNull(startTime,"起始时间不能为空");
        String endTime = orderPullParamDTO.getEndTime();
        Assert.notNull(endTime,"结束不能为空");
        Date startDate = DateUtils.parse( DateUtils.SIMPLE_DATE_FORMAT,startTime);
        Date endDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT,endTime);
        BasePageResponseDTO<GoodsReturn> goodsReturnPage = PageResponseUtils.pageInvoke(() -> goodsReturnMapper.findRangeTimeReturnOrder(startDate, endDate)
                , orderPullParamDTO.getPageNo(), pageSize);
        return goodsReturnPage;
    }

    @Override
    @ServiceLog(description = "搜索与整理退货单信息", serviceType = ServiceType.COMMON_SERVICE)
    public GoodsReturnRespVO getReturnOrderForProcure(GoodsReturnOrderRequest request, String userGuid, Integer orgId) {
        // 获取当前登录用户的部门，单位，个人等信息
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(userGuid, orgId,true,null);

        request.setDeptIdList(loginUserInfo.getDeptIdList());
        request.setOrgId(loginUserInfo.getOrgId());
        request.setRootDepartmentId(loginUserInfo.getRootDepartmentId());

        // 搜索退货的订单，此处先不区分采购人，因为退货者不一定是下单的人
        OrderSearchParamDTO param = this.setQueryParam(request);

        // 检索与空值控制
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(param);
        if (response == null) {
            return new GoodsReturnRespVO(new ArrayList<>(), request.getPageNo(), request.getPageSize());
        }
        Long totalHit = response.getTotalHits();
        Integer totalPage = (int) Math.ceil((double) totalHit.intValue() / (double) request.getPageSize());
        if (totalHit <= 0) {
            return new GoodsReturnRespVO(new ArrayList<>(), request.getPageNo(), request.getPageSize());
        }

        // 获取master信息与detailid，并构造各表的mapping关系
        List<OrderMasterSearchDTO> torderMasters = response.getRecordList();
        List<Integer> detailIdList = torderMasters
                .stream()
                .map(OrderMasterSearchDTO::getOrderDetail)
                .flatMap(List::stream)
                .map(OrderDetailSearchDTO::getDetailId)
                .collect(Collectors.toList());

        Map<Integer, Set<SimpleDetailInfoDO>> detailMap = this.mapMasterIdToSimpleDetail(detailIdList);
        Map<Integer, List<GoodsReturn>> goodReturnMap = this.mapDetailIdToGoodsReturn(detailIdList);
        Map<Integer, Integer> detailSuppIdMap = this.mapDetailIdToSuppId(torderMasters);

        // 构造返回信息
        BigDecimal totalPrice;
        Set<SimpleDetailInfoDO> details;
        List<GoodsReturnOrderDetailVO> grModels;
        GoodsReturnOrderDetailVO grModel;
        GoodsReturn goodsReturn;
        GoodsReturnOrderBriefVO model;
        List<GoodsReturnOrderBriefVO> models = new ArrayList<>();
        for (OrderMasterSearchDTO order:torderMasters) {
            totalPrice = BigDecimal.ZERO;
            details = detailMap.get(order.getId());
            grModels = new ArrayList<>();
            for (SimpleDetailInfoDO detail : details) {
                if (detail.getReturnStatus() == null || detail.getReturnStatus().equals(OrderDetailReturnStatus.CANCELREQUEST_1.getCode()) || detail.getReturnStatus().equals(OrderDetailReturnStatus.CANCELREQUEST_2.getCode())) {
                    continue;
                }
                if (request.getStatus() != null && noSearchStatus(request.getStatus(), detail.getReturnStatus())) {
                    continue;
                }

                goodsReturn = goodReturnMap.get(detail.getId()).get(0);
                totalPrice = totalPrice.add(detail.getFbidprice().multiply(new BigDecimal(goodsReturn.getQuantity())));
                Integer suppId = detailSuppIdMap.get(detail.getId());
                grModel = this.constructGRModel(detail, goodsReturn,suppId);
                grModels.add(grModel);
            }

            model = new GoodsReturnOrderBriefVO(grModels, totalPrice.doubleValue(), order,order.getFsuppname());
            models.add(model);
        }

        // 整理危化品信息 TODO:后续可以删除此语句，改换前述detail中的信息dangeroustypename设置
        this.setDangerous(models);

        return new GoodsReturnRespVO(models, request.getPageNo(), request.getPageSize(), totalPage);
    }

    @Override
    public List<GoodsReturn> getUnFinishReturnSuccess() {
        return goodsReturnMapper.findByStatusAndReplyDate(Arrays.asList(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode(), GoodsReturnStatusEnum.RETURNED_GOODS.getCode()), new Date());
    }

    private Map<Integer, Set<SimpleDetailInfoDO>> mapMasterIdToSimpleDetail(List<Integer> detailIdList) {
        List<SimpleDetailInfoDO> detailList = orderDetailMapper.selectPartInfoByIdList(detailIdList);
        return detailList
                .stream()
                .collect(Collectors.groupingBy(SimpleDetailInfoDO::getFmasterid,Collectors.toSet()));
    }

    private Map<Integer, Integer> mapDetailIdToSuppId(List<OrderMasterSearchDTO> torderMasters) {
        // detail与master中的suppid的map, masterid与detail 的map
        Map<Integer, Integer> detailSuppIdMap = new HashMap<>();
        for (OrderMasterSearchDTO master:torderMasters) {
            List<OrderDetailSearchDTO> detailSearchDTOList = master.getOrderDetail();
            Integer suppId = master.getFsuppid();
            for (OrderDetailSearchDTO detail:detailSearchDTOList) {
                Integer detailId = detail.getDetailId();
                detailSuppIdMap.put(detailId,suppId);
            }
        }
        return detailSuppIdMap;
    }

    private Map<Integer, List<GoodsReturn>> mapDetailIdToGoodsReturn(List<Integer> detailIdList) {
        // 退货信息
        List<GoodsReturn> goodReturnList = goodsReturnMapper.findByDetailIdIn(detailIdList);
        return goodReturnList
                .stream()
                .sorted(Comparator.comparing(GoodsReturn::getCreationTime).reversed())
                .collect(Collectors.groupingBy(GoodsReturn::getDetailId));
    }

    private OrderSearchParamDTO setQueryParam(GoodsReturnOrderRequest searchInput) {
        List<Integer> deptIdList = searchInput.getDeptIdList();
        Integer rootDepartmentId = searchInput.getRootDepartmentId();
        Integer orgId = searchInput.getOrgId();

        OrderSearchParamDTO param = new OrderSearchParamDTO();
        // 设置检索条件 1、部门id。存在检索条件，则按照部门名称来检索，如果不存在，则判断是否含有根部门（无需设置默认全部部门)，如果不含有根部门则用检索到的所有部门；
        param.setOrgIdList(Arrays.asList(orgId));
        if (searchInput.getDepartment() != null) {
            param.setDepartmentIdList(Arrays.asList(searchInput.getDepartment()));
        } else {
            if (!deptIdList.contains(rootDepartmentId)) {
                param.setDepartmentIdList(deptIdList);
            }
        }

        // 设置检索条件 2、退货单状态（只能设置为in），有请求则设置
        List<Integer> requireReturnStatusList = new ArrayList<>(5);
        if (searchInput.getStatus() != null) {
            if (searchInput.getStatus().equals(OrderDetailReturnStatus.AGREETORETURN.getCode())) {
                // agree 特殊处理，这种情况的连锁情况
                requireReturnStatusList.add(OrderDetailReturnStatus.AGREETORETURN.getCode());
                requireReturnStatusList.add(OrderDetailReturnStatus.RETURNEDGOODS.getCode());
            } else {
                requireReturnStatusList.add(searchInput.getStatus());
            }
        } else {
            List<Integer> excludeStatusList = new ArrayList<>();
            excludeStatusList.add(OrderDetailReturnStatus.CANCELREQUEST_1.getCode());
            excludeStatusList.add(OrderDetailReturnStatus.CANCELREQUEST_2.getCode());
            for (OrderDetailReturnStatus goodsReturnStatusEnum:OrderDetailReturnStatus.values()) {
                Integer status = goodsReturnStatusEnum.getCode();
                if (status!=null && !excludeStatusList.contains(status)) {
                    requireReturnStatusList.add(status);
                }
            }
        }
        param.setDetailReturnStatusList(requireReturnStatusList);

        // 设置检索条件 3、普通搜索search字段(前端的返回已经控制无其他无关参数）
        String searchWord = searchInput.getSearch();
        if (StringUtils.isNotBlank(searchWord)) {
            Preconditions.isTrue(searchWord.length()<=100,"输入搜索字符不可超过100个字符");
            param.setSearchKey(searchWord);
            param.addFullTextMasterFields("fsuppname");
            param.addFullTextMasterFields("forderno");
            param.addFullTextMasterFields("fbuydepartment");
            param.addFullTextMasterFields("fbuyername");
        }

        // 设置检索条件 4、订单号
        String orderNo = searchInput.getOrderNo();
        if (StringUtils.isNotBlank(orderNo)) {
            BusinessErrUtil.isTrue(orderNo.length() <= 100, ExecptionMessageEnum.ORDER_NUMBER_MAX_100_CHARS);
            param.setOrderNo((searchInput.getOrderNo()));
        }

        // 设置检索条件 5、限制时间
        String startDate = "1970-01-01 00:00:00";
        String endDate = "2100-01-01 00:00:00";
        DateFormat dateInputFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateInputFormat.setLenient(false);
        ParsePosition pos = new ParsePosition(0);
        if (StringUtils.isNotBlank(searchInput.getStartDate())) {
            Date startDateCheck = dateInputFormat.parse(searchInput.getStartDate(),pos);
            BusinessErrUtil.isTrue(startDateCheck != null, ExecptionMessageEnum.START_TIME_FORMAT_ERROR);
            startDate = searchInput.getStartDate() + " 00:00:00";
            pos = new ParsePosition(0);
        }
        if (StringUtils.isNotBlank(searchInput.getEndDate())) {
            Date endDateCheck = dateInputFormat.parse(searchInput.getEndDate(),pos);
            BusinessErrUtil.isTrue(endDateCheck != null, ExecptionMessageEnum.END_TIME_FORMAT_ERROR);
            endDate = searchInput.getEndDate() + " 23:59:59";
            pos = new ParsePosition(0);
        }
        FieldRangeDTO orderDateRange = new FieldRangeDTO("forderdate",startDate,endDate);
        param.setFieldRangeList(Arrays.asList(orderDateRange));

        // 设置检索条件 6、时间排序，查询条目数
        param.setOrderDateSort(SortOrderEnum.DESC);
        Integer pageNo = searchInput.getPageNo();
        Integer pageSize = searchInput.getPageSize();
        Integer startHit = (pageNo-1) * pageSize;
        param.setStartHit(startHit);
        param.setPageSize(pageSize);

        return param;
    }

    private void setDangerous(List<GoodsReturnOrderBriefVO> models) {
        List<String> idList = models.stream().map(GoodsReturnOrderBriefVO::getGoodReturns).flatMap(g -> g.stream().map(GoodsReturnOrderDetailVO::getDetailId).map(Object::toString)).collect(toList());
        List<DangerousTagDTO> storeDangerousList = this.getDangerousTag(idList, DangerousTagEnum.ORDER_DETAIL);

        if (CollectionUtils.isNotEmpty(storeDangerousList)) {
            Map<String, String> tagMap = storeDangerousList.stream().collect(Collectors.toMap(DangerousTagDTO::getBusinessId, t -> DangerousTypeEnum.get(t.getDangerousType()).getName()));
            models.forEach(m -> m.getGoodReturns().forEach(
                    g -> {
                g.setDangerousTag(tagMap.get(g.getDetailId().toString()));
            })
            );
        }
    }

    // 通过businessid和业务类型获取危化品信息
    private List<DangerousTagDTO> getDangerousTag(List<String> detailIdStr, DangerousTagEnum businessType) {
        if (CollectionUtils.isEmpty(detailIdStr)) {
            return New.emptyList();
        }
        List<DangerousTagDO> dangerousTagDOList = dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(detailIdStr,businessType.getValue());
        List<DangerousTagDTO> dangerousTagDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dangerousTagDOList)) {
            dangerousTagDOList.forEach(
                dangerousTagDO -> {
                    DangerousTagDTO dangerousTagDTO = new DangerousTagDTO();
                    dangerousTagDTO.setBusinessId(dangerousTagDO.getBusinessId());
                    dangerousTagDTO.setDangerousType(dangerousTagDO.getDangerousType());
                    dangerousTagDTO.setCasNo(dangerousTagDO.getCasNo());
                    dangerousTagDTOList.add(dangerousTagDTO);
                }
            );
        }
        return dangerousTagDTOList;
    }

    // 构造退货单对应商品信息model
    private GoodsReturnOrderDetailVO constructGRModel(SimpleDetailInfoDO torderDetail, GoodsReturn goodsReturn, Integer suppId) {
        GoodsReturnOrderDetailVO grModel = new GoodsReturnOrderDetailVO();

        grModel.setSpecification(torderDetail.getFspec());
        grModel.setPrice(torderDetail.getFbidprice().doubleValue());
        grModel.setQuantity(goodsReturn.getQuantity());
        grModel.setId(goodsReturn.getId());
        grModel.setBrand(torderDetail.getFbrand());
        grModel.setDetailId(torderDetail.getId());
        grModel.setGoodsName(torderDetail.getFgoodname());
        grModel.setCode(torderDetail.getFgoodcode());
        if (torderDetail.getFpicpath() == null) {
            grModel.setPicturePath("");
        } else {
            grModel.setPicturePath(torderDetail.getFpicpath());
        }
        grModel.setStatus(torderDetail.getReturnStatus());
        grModel.setSn(torderDetail.getProductSn());
        grModel.setSupplierId(suppId);
        grModel.setAmount(goodsReturn.getPrice().toString());
        grModel.setDangerousTag(torderDetail.getDangerousTypeName());

        return grModel;
    }

    private boolean noSearchStatus(Integer status, Integer returnStatus) {
        logger.info("in noSearchStatus.status:{}, returnStatus:{}", status, returnStatus);
        boolean flag = false;
        if (status.equals(1)) {
            if (!(returnStatus.equals(1) || returnStatus.equals(4))) {
                flag = true;
            }
        } else {
            if (!returnStatus.equals(status)) {
                flag = true;
            }
        }
        logger.info("out noSearchStatus.flag:{}", flag);
        return flag;
    }

    /**
     * 根据订单id列表和需要的退货单状态，获取 订单id-订单明细id-退货列表
     * @param orderIdList
     * @param returnStatusList
     * @return
     */
    @Override
    public Map<Integer, Map<Integer, List<GoodsReturnInfoDetailVO>>> getOrderGoodsReturnMap(List<Integer> orderIdList, List<Integer> returnStatusList) {
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList, GoodsReturnInvalidEnum.NORMAL.getCode());
        Map<Integer, Map<Integer, List<GoodsReturnInfoDetailVO>>> orderIdDetailIdReturnMap = new HashMap<>();
        for (GoodsReturn goodsReturn : goodsReturnList) {
            if (!returnStatusList.contains(goodsReturn.getGoodsReturnStatus())) {
                continue;
            }
            List<GoodsReturnInfoDetailVO> returnInfoList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            Integer orderId = goodsReturn.getOrderId();
            if (!orderIdDetailIdReturnMap.containsKey(orderId)) {
                orderIdDetailIdReturnMap.put(orderId, new HashMap<>());
            }
            for (GoodsReturnInfoDetailVO returnInfo : returnInfoList) {
                Integer detailId = Integer.parseInt(returnInfo.getDetailId());
                if (!orderIdDetailIdReturnMap.get(orderId).containsKey(detailId)) {
                    orderIdDetailIdReturnMap.get(orderId).put(detailId, New.list());
                }
                orderIdDetailIdReturnMap.get(orderId).get(detailId).add(returnInfo);
            }
        }
        return orderIdDetailIdReturnMap;
    }

    @Override
    public void syncReplyTime() {
        // 防止脏数据导致死循环
        Set<Integer> duplicateSet = new HashSet<>();
        while (true) {
            List<GoodsReturn> goodsReturns = goodsReturnMapper.findSyncReplyList();
            goodsReturns.removeIf(goodsReturn -> duplicateSet.contains(goodsReturn.getId()));
            if(CollectionUtils.isEmpty(goodsReturns)){
                return;
            }
            List<Integer> idList = goodsReturns.stream().map(GoodsReturn::getId).collect(toList());
            duplicateSet.addAll(idList);
            List<GoodsReturnLogDO> goodsReturnLogDOList = goodsReturnLogDOMapper.findByReturnIdIn(idList);
            List<GoodsReturn> updateList = new ArrayList<>(goodsReturns.size());
            goodsReturnLogDOList.forEach((goodsReturnLogDO -> {
                if (goodsReturnLogDO.getOperationType().equals(GoodsReturnOperationTypeEnum.SUPPLIER_AGREE_GOODS_RETURN.getCode())) {
                    GoodsReturn goodsReturn = new GoodsReturn();
                    goodsReturn.setId(goodsReturnLogDO.getReturnId());
                    goodsReturn.setReplyTime(goodsReturnLogDO.getUpdateTime());
                    updateList.add(goodsReturn);
                    // 记录符合查询条件但没有更新的id
                    duplicateSet.remove(goodsReturnLogDO.getReturnId());
                }
            }));
            if(CollectionUtils.isNotEmpty(updateList)) {
                goodsReturnMapper.loopUpdateByIdIn(updateList);
            }
        }
    }
}
