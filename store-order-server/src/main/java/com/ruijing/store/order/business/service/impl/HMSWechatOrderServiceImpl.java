package com.ruijing.store.order.business.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.OrgConfigRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrgConfigResponseDTO;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.OrderStatisticsParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.business.service.HMSWechatOrderService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.constant.SysConfigConstants;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderConfigsVO;
import com.ruijing.store.order.rpc.client.RoleRPCClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.UserDepartmentRoleRpcServiceClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.AccessDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.RoleDTO;
import com.ruijing.store.user.api.service.AccessRpcService;
import com.ruijing.store.user.api.service.DepartmentRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/9/16 14:36
 */
@Service
public class HMSWechatOrderServiceImpl implements HMSWechatOrderService {

    private static final Logger logger = LoggerFactory.getLogger(HMSWechatOrderServiceImpl.class);


    @Resource
    private BuyerOrderServiceImpl buyerOrderService;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderMasterCommonService orderMasterCommonService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderRelatedRPCService orderRelatedRPCService;

    @Resource
    private RoleRPCClient roleRPCClient;

    @Resource
    private UserDepartmentRoleRpcServiceClient userDepartmentRoleRpcServiceClient;

    @MSharpReference(remoteAppkey = "store-user-service")
    private AccessRpcService accessRpcService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private DepartmentRpcService departmentRpcService;

    @Resource
    private SysConfigClient sysConfigClient;



    /**
     * 输入状态和实际状态的匹配 转换map
     */
    private static final Map<Integer, List<Integer>> statusCorrelMap = new HashMap<Integer, List<Integer>>() {{
        put(OrderStatusEnum.Statementing_1.getValue(), New.list(OrderStatusEnum.Statementing_1.getValue()));
        put(OrderStatusEnum.WaitingForStatement_1.getValue(), New.list(OrderStatusEnum.WaitingForStatement_1.getValue()));
        put(OrderStatusEnum.Finish.getValue(), New.list(OrderStatusEnum.Finish.getValue(), OrderStatusEnum.Assess.getValue()));
    }};

    /**
     * 获取微信订单侧的订单数量
     * @param request
     * @return
     */
    @Override
    public Integer getOrderCountByStatus(LoginUserInfoBO loginInfo, OrderListRequest request) {
        // 登录相关信息
        List<Integer> deptIdList = loginInfo.getDeptIdList();

        // 空部门则不展示
        if (CollectionUtils.isEmpty(deptIdList)) {
            return 0;
        }
        // 如果没有传入的状态 那就是默认读待我验收审批数量
        if (CollectionUtils.isEmpty(request.getStatusList()) && Objects.isNull(request.getStatus())) {
            OrderStatisticsParamDTO paramDTO = new OrderStatisticsParamDTO();
            paramDTO.setBuyerIds(Collections.singletonList(loginInfo.getUserId()));
            paramDTO.setOrgIds(Collections.singletonList(loginInfo.getOrgId()));
            Map<OrderStatusEnum, Long> countMap = orderSearchBoostService.countOrderStatusStatistics(paramDTO);
            Long count = countMap.get(OrderStatusEnum.OrderReceiveApproval);
            return count == null ? null : count.intValue();
        }
        
        // 微信订单计数不考虑仅具有查看权限和对应审批流的，也即前述的单也会统计在内，因此isHms是false
        OrderSearchParamDTO param = buyerOrderService.constructOrderSearchParam(request, loginInfo, false);
        param.setPageSize(0);
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(param);
        return response.getTotalHits() == null ? 0 : response.getTotalHits().intValue();
    }

    @Override
    public OrgConfigResponseDTO findSingleConfigByOrgCode(String orgCode,
                                                          String configCode) {
        OrgConfigRequestDTO param = new OrgConfigRequestDTO();
        param.setOrgCode(orgCode);
        param.setConfigCodesList(New.list(configCode));
        List<OrgConfigResponseDTO> result = findSysConfigListByOrgCode(param);
        if (CollectionUtils.isEmpty(result)) {
            // 返回null 兼容以前代码的非空判断
            return null;
        }
        return result.get(0);
    }

    @Override
    public RemoteResponse<OrderConfigsVO> getOrderConfig(String orgCode) {
        OrderConfigsVO orderConfigsVO = null;
        try {
            OrgConfigRequestDTO orgConfigRequestDTO = new OrgConfigRequestDTO();
            orgConfigRequestDTO.setOrgCode(orgCode);
            orgConfigRequestDTO.setConfigCodesList(New.list(SysConfigConstants.MUST_HAVE_FUNDCARD, SysConfigConstants.ORDER_CONTRACT_THRESHOLD
                    , SysConfigConstants.REQUIRE_SIGN_PROCUREMENT_CONTRACT, SysConfigConstants.ORG_ACCEPTANCE_APPROVAL_CONFIG));
            // oms配置dto数组对象
            RemoteResponse<List<OrgConfigResponseDTO>> sysConfigListByOrgCode = orderRelatedRPCService.findSysConfigListByOrgCode(orgConfigRequestDTO);

            List<OrgConfigResponseDTO> sysConfigListBYOrgCode = sysConfigListByOrgCode.getData();

            orderConfigsVO = new OrderConfigsVO();
            for (OrgConfigResponseDTO orgConfigResponseDTO : sysConfigListBYOrgCode) {
                if (orderConfigsVO.getConfigCodeSetMethodMap().containsKey(orgConfigResponseDTO.getConfigCode())) {
                    // 获取 set 函数
                    String setMethodName = orderConfigsVO.getConfigCodeSetMethodMap().get(orgConfigResponseDTO.getConfigCode());
                    // set 的方法
                    Method setMethod = orderConfigsVO.getClass().getDeclaredMethod(setMethodName, String.class);
                    // 反射 set value 值
                    setMethod.invoke(orderConfigsVO, orgConfigResponseDTO.getConfigValue());
                }
            }
        } catch (NoSuchMethodException e) {
            logger.error("配置VO对象无对应的set方法", e);
            return RemoteResponse.<OrderConfigsVO>custom().setFailure("configVO对象无对应的set方法").build();
        } catch (Exception e) {
            logger.error("获取订单配置信息异常", e);
            return RemoteResponse.<OrderConfigsVO>custom().setFailure("获取订单配置信息异常").build();
        }
        return RemoteResponse.<OrderConfigsVO>custom().setSuccess().setData(orderConfigsVO).build();
    }


    /**
     * 获取配置列表
     * @param param
     * @return
     */
    public List<OrgConfigResponseDTO> findSysConfigListByOrgCode(OrgConfigRequestDTO param){
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "findSysConfigByOrgCode");
        List<OrgConfigResponseDTO> result = null;
        try {
            RemoteResponse<List<OrgConfigResponseDTO>> sysConfigListByOrgCode = orderRelatedRPCService.findSysConfigListByOrgCode(param);
            result = sysConfigListByOrgCode.getData();
            transaction.setSuccess();
        } catch (Exception e) {
            logger.error("查询机构配置异常！{}", e.getMessage(), e);
            transaction.addData(CatUtils.buildStackInfo("查询机构配置异常！", e));
            throw new IllegalStateException("查询机构配置异常！" + e.getMessage());
        } finally {
            transaction.complete();
        }
        return result;
    }

    private Boolean checkUserHasAcceptPermission(Integer orgId, String code, Integer userId, Integer deptId){
        // 1. 获取用户所有角色
        List<RoleDTO> roleDTOList = roleRPCClient.findByOrgAndAccessCode(orgId, code);
        Set<Integer> roleIdSet = roleDTOList.stream().map(RoleDTO::getId).collect(Collectors.toSet());
        // 2. 获取部门
        List<DepartmentDTO> deptList = userDepartmentRoleRpcServiceClient.findUserHasRoleDepartment(orgId, userId, roleIdSet);
        AccessDTO access = accessRpcService.findByCode(code).getData();
        String accessName = access == null ? "" : access.getName();
        if (deptId == null) {
            if (deptList.isEmpty()) {
                logger.error(String.format("用户%d不具有%s权限", userId, accessName));
                return false;
            }
        } else {

            RemoteResponse<List<DepartmentDTO>> response = departmentRpcService.getOrgDepartment(orgId, New.list(deptId));
            BusinessErrUtil.isTrue(response.isSuccess() && CollectionUtils.isNotEmpty(response.getData()), ExecptionMessageEnum.SEARCH_DEPARTMENT_INFO_FAIL);
            DepartmentDTO dept = response.getData().get(0);
            boolean has = false;
            for (DepartmentDTO d : deptList) {
                if (d.getLft() <= dept.getLft() && d.getRgt() >= dept.getRgt()) {
                    has = true;
                }
            }
            if (!has) {
                logger.error(String.format("用户<id=%d>在部门<id=%d>中不具有权限<code=%s>", userId, deptId, code));
                return false;
            }
        }
        return true;
    }

    /**
     * 依据前端传入的状态转换
     * @param status
     * @return
     */
    private List<Integer> statusConvert(Integer status) {
        if (status == null) {
            return New.list();
        }
        List<Integer> correlStatusList = statusCorrelMap.get(status);
        return CollectionUtils.isEmpty(correlStatusList) ? New.list(status) : correlStatusList;
    }
}
