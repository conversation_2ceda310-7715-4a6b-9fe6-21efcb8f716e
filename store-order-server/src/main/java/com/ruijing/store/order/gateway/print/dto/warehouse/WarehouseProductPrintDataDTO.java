package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.gateway.print.dto.other.BatchesPrintDTO;

import java.io.Serializable;
import java.util.List;

/**
 * @author: li<PERSON>yu
 * @createTime: 2023-04-06 12:00
 * @description:
 **/
public class WarehouseProductPrintDataDTO implements Serializable {

    private Integer orderDetailId;

    @ModelProperty(value = "商品Id")
    private Long productId;

    @ModelProperty(value = "商品名称")
    private String productName;

    @ModelProperty(value = "商品规格")
    private String specifications;

    @ModelProperty(value = "商品品牌")
    private String brand;

    @ModelProperty(value = "商品货号")
    private String goodCode;

    @ModelProperty(value = "CAS号")
    private String casNo;

    @ModelProperty(value = "危化品标识(类型)")
    private Integer dangerousType;

    @ModelProperty(value = "危化品标识名称")
    private String dangerousTypeName;

    @ModelProperty(value = "是否是危化品, 0不是， 1是")
    private Integer dangerousFlag;

    @ModelProperty(value = "管制类型, 1,管制类;2,非管制,其他商品类型为null")
    private Integer regulatoryFlag;

    @ModelProperty(value = "单位")
    private String unit;

    @ModelProperty(value = "数量")
    private Integer quantity;

    @ModelProperty(value = "计量总量")
    private Double totalQuantity;

    @ModelProperty(value = "计量单位")
    private String quantityUnit;

    @ModelProperty(value = "供应商Id")
    private Integer supplierId;

    @ModelProperty(value = "供应商名称")
    private String supplierName;

    @ModelProperty(value = "商品图片")
    private String productPhoto;

    @ModelProperty(value = "商品单价")
    private String singlePrice;

    @ModelProperty(value = "商品总额")
    private String totalPrice;

    @ModelProperty(value = "商品分类id")
    private Integer categoryId;

    @ModelProperty(value = "一级商品分类名称")
    private String firstLevelCategoryName;

    @ModelProperty("一级分类ID")
    private Integer firstLevelCategoryId;

    /**
     * {@link com.ruijing.store.wms.api.enums.FormEnum}
     */
    @ModelProperty(value = "商品状态, 1 固体, 2 液体, 3 气体")
    private Integer form;

    @ModelProperty(value = "个性化商品品类名称，只用作存取显示")
    private String personalizedCategoryName;

    @ModelProperty(value = "分类标签")
    private String categoryTag;

    @ModelProperty(value = "货柜号")
    private String container;
    
    @ModelProperty("批次信息")
    private List<BatchesPrintDTO> batchDataList;

    @ModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    public WarehouseProductPrintDataDTO() {
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public WarehouseProductPrintDataDTO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getDangerousFlag() {
        return dangerousFlag;
    }

    public void setDangerousFlag(Integer dangerousFlag) {
        this.dangerousFlag = dangerousFlag;
    }

    public Integer getRegulatoryFlag() {
        return regulatoryFlag;
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductPhoto() {
        return productPhoto;
    }

    public void setProductPhoto(String productPhoto) {
        this.productPhoto = productPhoto;
    }

    public String getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(String singlePrice) {
        this.singlePrice = singlePrice;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getFirstLevelCategoryName() {
        return firstLevelCategoryName;
    }

    public void setFirstLevelCategoryName(String firstLevelCategoryName) {
        this.firstLevelCategoryName = firstLevelCategoryName;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    public String getPersonalizedCategoryName() {
        return personalizedCategoryName;
    }

    public void setPersonalizedCategoryName(String personalizedCategoryName) {
        this.personalizedCategoryName = personalizedCategoryName;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public String getContainer() {
        return container;
    }

    public void setContainer(String container) {
        this.container = container;
    }

    public List<BatchesPrintDTO> getBatchDataList() {
        return batchDataList;
    }

    public WarehouseProductPrintDataDTO setBatchDataList(List<BatchesPrintDTO> batchDataList) {
        this.batchDataList = batchDataList;
        return this;
    }

    public Integer getFirstLevelCategoryId() {
        return firstLevelCategoryId;
    }

    public WarehouseProductPrintDataDTO setFirstLevelCategoryId(Integer firstLevelCategoryId) {
        this.firstLevelCategoryId = firstLevelCategoryId;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public WarehouseProductPrintDataDTO setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    @Override
    public String toString() {
        return "WarehouseProductPrintDataDTO{" +
                "productId=" + productId +
                ", productName='" + productName + '\'' +
                ", specifications='" + specifications + '\'' +
                ", brand='" + brand + '\'' +
                ", goodCode='" + goodCode + '\'' +
                ", casNo='" + casNo + '\'' +
                ", dangerousType=" + dangerousType +
                ", dangerousTypeName='" + dangerousTypeName + '\'' +
                ", dangerousFlag=" + dangerousFlag +
                ", regulatoryFlag=" + regulatoryFlag +
                ", unit='" + unit + '\'' +
                ", quantity=" + quantity +
                ", totalQuantity=" + totalQuantity +
                ", quantityUnit='" + quantityUnit + '\'' +
                ", supplierId=" + supplierId +
                ", supplierName='" + supplierName + '\'' +
                ", productPhoto='" + productPhoto + '\'' +
                ", singlePrice='" + singlePrice + '\'' +
                ", totalPrice='" + totalPrice + '\'' +
                ", categoryId=" + categoryId +
                ", firstLevelCategoryName='" + firstLevelCategoryName + '\'' +
                ", firstLevelCategoryId=" + firstLevelCategoryId +
                ", form=" + form +
                ", personalizedCategoryName='" + personalizedCategoryName + '\'' +
                ", categoryTag='" + categoryTag + '\'' +
                ", container='" + container + '\'' +
                ", batchDataList=" + batchDataList +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                '}';
    }
}
