package com.ruijing.store.order.business.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterForCmsReq;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterForCmsService;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.order.annotation.ServiceLog;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhuk
 * @create: 2019-07-02 10:47
 **/
@ServiceLog
@MSharpService
public class OrderMasterForCmsServiceImpl implements OrderMasterForCmsService {

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Override
    public RemoteResponse<List<OrderMasterDTO>> findOrderMasterByBuyerAndStatus(OrderMasterForCmsReq orderMasterForCmsReq) {

        //采购人id
        Integer buyerId = orderMasterForCmsReq.getBuyerId();
        List<Integer> statusList = orderMasterForCmsReq.getStatusList();
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFbuyeridAndStatusIn(buyerId, statusList);
        List<OrderMasterDTO> orderMasterDTOList = orderMasterDOList.stream().map(orderMasterDO ->
                OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO)).collect(Collectors.toList());
        return RemoteResponse.<List<OrderMasterDTO>>custom().setSuccess().setData(orderMasterDTOList);
    }
}
