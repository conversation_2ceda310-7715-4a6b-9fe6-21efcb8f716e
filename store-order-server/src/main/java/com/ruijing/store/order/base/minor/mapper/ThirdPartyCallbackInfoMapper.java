package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO;

/**
 * @description: 第三方接口回调信息 MAapper
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/9/30 14:21
 */
public interface ThirdPartyCallbackInfoMapper {
    /**
     * 通过主键删除记录
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入一条记录
     * @param record
     * @return
     */
    int insert(ThirdPartyCallbackInfoDO record);


    /**
     * 有选择的插入一条记录
     * @param record
     * @return
     */
    int insertSelective(ThirdPartyCallbackInfoDO record);

    /**
     * 通过id查询记录
     * @param id
     * @return
     */
    ThirdPartyCallbackInfoDO selectByPrimaryKey(Integer id);

    /**
     * 更新记录 ，值为null的参数 不更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ThirdPartyCallbackInfoDO record);

    /**
     * 通过主键更新记录
     * @param record
     * @return
     */
    int updateByPrimaryKey(ThirdPartyCallbackInfoDO record);


}