package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/8 17:48
 * @Description
 **/
@RpcModel("退货概览信息列表请求体")
public class GoodsReturnBriefInfoRequest implements Serializable {

    private static final long serialVersionUID = -6077612736789232051L;

    /**
     * 订单id
     */
    @RpcModelProperty(value = "订单id")
    private Integer orderId;

    /**
     * 是否返回所有状态
     */
    @RpcModelProperty(value = "是否返回所有状态, true是，false和默认否")
    private Boolean returnAll;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Boolean getReturnAll() {
        return returnAll;
    }

    public void setReturnAll(Boolean returnAll) {
        this.returnAll = returnAll;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnBriefInfoRequest{");
        sb.append("orderId=").append(orderId);
        sb.append(", returnAll=").append(returnAll);
        sb.append('}');
        return sb.toString();
    }
}
