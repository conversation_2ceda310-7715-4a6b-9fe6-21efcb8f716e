package com.ruijing.store.order.base.core.translator;

import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.SelectFundCardDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @description: the translator of RefFundcardOrder
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/9 15:23
 **/
public class RefFundcardOrderTranslator {

    private static final int INIT_FUND_TYPE = 0;
    private static final int INIT_APPROVE_USER = 0;

    /**
     * 中肿经费卡类型，历史遗留字段
     */
    private static final int ZHONG_ZHONG_FUND_TYPE = 0;

    /**
     * 中大审批人id，历史遗留字段
     */
    private static final int ZHONG_SHAN_DA_XUE_APPROVE_USER_ID = 0;

    /**
     * 对象clone
     * @param dto
     * @return
     */
    public static RefFundcardOrderDTO cloneDTO(RefFundcardOrderDTO dto) {
        RefFundcardOrderDTO refFundcardOrder = new RefFundcardOrderDTO();
        refFundcardOrder.setId(dto.getId());
        refFundcardOrder.setApplicationId(dto.getApplicationId() == null ? "" : dto.getApplicationId());
        refFundcardOrder.setOrderId(dto.getOrderId() == null ? "": dto.getOrderId());
        refFundcardOrder.setBidId(dto.getBidId());
        refFundcardOrder.setCardId(dto.getCardId() == null ? "" : dto.getCardId());
        refFundcardOrder.setSubjectId(dto.getSubjectId());
        refFundcardOrder.setSerialNumber(dto.getSerialNumber());
        refFundcardOrder.setUsemoney(dto.getUsemoney());
        refFundcardOrder.setDeletionTime(dto.getDeletionTime());
        refFundcardOrder.setIsDeleted(dto.getIsDeleted() == null ? false : dto.getIsDeleted());
        Date nowDate = new Date();
        refFundcardOrder.setCreationTime(nowDate);
        refFundcardOrder.setUpdateTime(nowDate);
        refFundcardOrder.setCardNo(dto.getCardNo());
        refFundcardOrder.setFundType(dto.getFundType() == null ? INIT_FUND_TYPE : dto.getFundType());
        refFundcardOrder.setApproveUser(dto.getApproveUser() == null ? INIT_APPROVE_USER : dto.getApproveUser());
        refFundcardOrder.setFreezeAmount(dto.getFreezeAmount() == null ? new BigDecimal("0.00") : dto.getFreezeAmount());
        return refFundcardOrder;
    }

    public static RefFundcardOrderDO dto2DO(RefFundcardOrderDTO dto) {
        RefFundcardOrderDO refFundcardOrder = new RefFundcardOrderDO();
        refFundcardOrder.setId(dto.getId());
        refFundcardOrder.setApplicationId(dto.getApplicationId() == null ? "" : dto.getApplicationId());
        refFundcardOrder.setOrderId(dto.getOrderId() == null ? "": dto.getOrderId());
        refFundcardOrder.setBidId(dto.getBidId());
        refFundcardOrder.setCardId(dto.getCardId() == null ? "" : dto.getCardId());
        refFundcardOrder.setSubjectId(dto.getSubjectId());
        refFundcardOrder.setSerialNumber(dto.getSerialNumber());
        refFundcardOrder.setUsemoney(dto.getUsemoney());
        refFundcardOrder.setDeletionTime(dto.getDeletionTime());
        refFundcardOrder.setIsDeleted(dto.getIsDeleted() != null && dto.getIsDeleted());

        Date nowDate = new Date();
        refFundcardOrder.setCreationTime(nowDate);
        refFundcardOrder.setUpdateTime(nowDate);
        refFundcardOrder.setCardNo(dto.getCardNo());
        refFundcardOrder.setFundType(dto.getFundType() == null ? INIT_FUND_TYPE : dto.getFundType());
        refFundcardOrder.setApproveUser(dto.getApproveUser() == null ? INIT_APPROVE_USER : dto.getApproveUser());
        refFundcardOrder.setFreezeAmount(dto.getFreezeAmount() == null ? new BigDecimal("0.00") : dto.getFreezeAmount());
        refFundcardOrder.setCampusCode(dto.getCampusCode() == null ? "" : dto.getCampusCode());
        refFundcardOrder.setCampusName(dto.getCampusName() == null ? "" : dto.getCampusName());
        return refFundcardOrder;
    }

    /**
     * DO to DTO
     * @param refFundcardOrderDo
     * @return
     */
    public static RefFundcardOrderDTO do2Dto(RefFundcardOrderDO refFundcardOrderDo) {
        if (refFundcardOrderDo == null) {
            return null;
        }

        RefFundcardOrderDTO dto = new RefFundcardOrderDTO();
        dto.setId(refFundcardOrderDo.getId());
        dto.setApplicationId(refFundcardOrderDo.getApplicationId());
        dto.setOrderId(refFundcardOrderDo.getOrderId());
        dto.setBidId(refFundcardOrderDo.getBidId());
        dto.setCardId(refFundcardOrderDo.getCardId());
        dto.setSubjectId(refFundcardOrderDo.getSubjectId());
        dto.setSerialNumber(refFundcardOrderDo.getSerialNumber());
        dto.setUsemoney(refFundcardOrderDo.getUsemoney());
        dto.setDeletionTime(refFundcardOrderDo.getDeletionTime());
        dto.setIsDeleted(refFundcardOrderDo.getIsDeleted());
        dto.setCreationTime(refFundcardOrderDo.getCreationTime());
        dto.setUpdateTime(refFundcardOrderDo.getUpdateTime());
        dto.setCardNo(refFundcardOrderDo.getCardNo());
        dto.setFundType(refFundcardOrderDo.getFundType());
        dto.setApproveUser(refFundcardOrderDo.getApproveUser());
        dto.setFreezeAmount(refFundcardOrderDo.getFreezeAmount());
        dto.setCampusCode(refFundcardOrderDo.getCampusCode());
        dto.setCampusName(refFundcardOrderDo.getCampusName());
        return dto;
    }

    /**
     * DO to DTO with List
     * @param doList
     * @return
     */
    public static List<RefFundcardOrderDTO> do2Dto(List<RefFundcardOrderDO> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return New.list();
        }
        return doList.stream().map(r -> do2Dto(r)).collect(Collectors.toList());
    }

    /**
     * do to dto
     *
     * @param param
     * @return
     */
    public static RefFundcardOrderDTO doToDto(RefFundcardOrderDO param) {
        RefFundcardOrderDTO dto = new RefFundcardOrderDTO();
        dto.setId(param.getId());
        dto.setApplicationId(param.getApplicationId());
        dto.setOrderId(param.getOrderId());
        dto.setBidId(param.getBidId());
        dto.setCardId(param.getCardId());
        dto.setSubjectId(param.getSubjectId());
        dto.setSerialNumber(param.getSerialNumber());
        dto.setUsemoney(param.getUsemoney());
        dto.setDeletionTime(param.getDeletionTime());
        dto.setIsDeleted(param.getIsDeleted());
        dto.setCreationTime(param.getCreationTime());
        dto.setUpdateTime(param.getUpdateTime());
        dto.setCardNo(param.getCardNo());
        dto.setFundType(param.getFundType());
        dto.setApproveUser(param.getApproveUser());
        dto.setFreezeAmount(param.getFreezeAmount());
        dto.setCampusCode(param.getCampusCode());
        dto.setCampusName(param.getCampusName());
        return dto;
    }

    public static RefFundcardOrderDO fundCardDTO2RefDO(OrderFundCardDTO cache) {
        RefFundcardOrderDO ref = new RefFundcardOrderDO();
        ref.setId(UUID.randomUUID().toString());
        ref.setOrderId(cache.getOrderId().toString());
        ref.setCardId(cache.getFundCardId());
        ref.setIsDeleted(false);
        ref.setCreationTime(new Date());
        ref.setUpdateTime(new Date());
        ref.setCardNo(cache.getFundCardNo());
        ref.setFundType(ZHONG_ZHONG_FUND_TYPE);
        ref.setApproveUser(ZHONG_SHAN_DA_XUE_APPROVE_USER_ID);
        ref.setFreezeAmount(cache.getFreezeAmount());
        ref.setUsemoney(cache.getFreezeAmount());

        return ref;
    }


    /**
     * 绑卡记录转换预算系统的 FundCardDTO
     * @param item
     * @return
     */
    public static FundCardDTO refToFundCardDto(RefFundcardOrderDO item) {
        return refToFundCardDto(item, item.getFreezeAmount());
    }

    /**
     * 绑卡记录转换预算系统的 FundCardDTO
     * @param item
     * @param freezeAmount
     * @return
     */
    public static FundCardDTO refToFundCardDto(RefFundcardOrderDO item, final BigDecimal freezeAmount) {
        FundCardDTO result = new FundCardDTO();
        result.setId(item.getCardId());
        if (StringUtils.isNotBlank(item.getSubjectId())) {
            result.setId(item.getSubjectId());
        }
        result.setFreezeAmount(freezeAmount);

        return result;
    }

    /**
     * 绑卡记录转换预算系统的 FundCardDTO
     * @param item         RefFundcardOrderDO
     * @param freezeAmount 冻结金额
     * @return
     */
    public static SelectFundCardDTO refToSelectFundCardDto(RefFundcardOrderDO item, BigDecimal freezeAmount) {
        SelectFundCardDTO result = new SelectFundCardDTO();
        result.setId(item.getCardId());
        if (StringUtils.isNotBlank(item.getSubjectId())) {
            result.setId(item.getSubjectId());
        }
        result.setFreezeAmount(freezeAmount);
        return result;
    }

    /**
     * 绑卡记录转换预算系统的 FundCardDTO
     * @param item
     * @return
     */
    public static SelectFundCardDTO refToSelectFundCardDto(RefFundcardOrderDTO item, BigDecimal freezeAmount) {
        SelectFundCardDTO selectFundCardDTO = refToSelectFundCardDto(dto2DO(item), freezeAmount);
        selectFundCardDTO.setExpenseApplyNo(item.getExpenseApplyNo());
        return selectFundCardDTO;
    }

    public static RefOrderFundCardDTO fromDTO(RefFundcardOrderDTO source){
        RefOrderFundCardDTO target = new RefOrderFundCardDTO();
        if(source.getOrderId() != null) {
            target.setOrderId(Integer.parseInt(source.getOrderId()));
        }
        target.setFreezeAmount(source.getFreezeAmount());
        target.setExpenseApplyNo(source.getExpenseApplyNo());
        target.setFundCardId(RefFundcardOrderTranslator.getLastLevelCardId(source));
        target.setFundCardNo(source.getCardNo());
        target.setUseAmount(source.getUsemoney());
        target.setFreezeAmount(source.getFreezeAmount());
        return target;
    }

    /**
     * 获取最后一级经费卡id，有subjectId取subjectId,否则取cardId，兼容这个三级卡和非三级卡卡id字段不一致的坑
     * @param card 卡对象
     * @return 卡id
     */
    public static String getLastLevelCardId(RefFundcardOrderDO card){
        return StringUtils.isNotEmpty(card.getSubjectId()) ? card.getSubjectId() : card.getCardId();
    }

    /**
     * 同上面的方法
     */
    public static String getLastLevelCardId(RefFundcardOrderDTO card){
        return StringUtils.isNotBlank(card.getSubjectId()) ? card.getSubjectId() : card.getCardId();
    }

    /**
     * 获取使用金额，解决用了两个字段的坑
     * @param card 卡对象
     * @return 卡id
     */
    public static BigDecimal getFreezeAmount(RefFundcardOrderDO card){
        return card.getUsemoney() != null ? card.getUsemoney() : card.getFreezeAmount();
    }
}
