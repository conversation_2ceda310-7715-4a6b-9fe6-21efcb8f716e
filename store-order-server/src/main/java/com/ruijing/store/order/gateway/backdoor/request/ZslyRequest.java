package com.ruijing.store.order.gateway.backdoor.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2025-02-21 18:30
 * @description:
 */
public class ZslyRequest implements Serializable {

    private static final long serialVersionUID = 4506557270447805537L;

    private String slang;
    private Boolean updateFundStatusFlag;
    private Date lastOrderDate;
    private List<String> testOrderNos;
    private List<String> excludeOrderNos;

    public String getSlang() {
        return slang;
    }

    public ZslyRequest setSlang(String slang) {
        this.slang = slang;
        return this;
    }

    public Boolean getUpdateFundStatusFlag() {
        return updateFundStatusFlag;
    }

    public ZslyRequest setUpdateFundStatusFlag(Boolean updateFundStatusFlag) {
        this.updateFundStatusFlag = updateFundStatusFlag;
        return this;
    }

    public Date getLastOrderDate() {
        return lastOrderDate;
    }

    public ZslyRequest setLastOrderDate(Date lastOrderDate) {
        this.lastOrderDate = lastOrderDate;
        return this;
    }

    public List<String> getTestOrderNos() {
        return testOrderNos;
    }

    public ZslyRequest setTestOrderNos(List<String> testOrderNos) {
        this.testOrderNos = testOrderNos;
        return this;
    }

    public List<String> getExcludeOrderNos() {
        return excludeOrderNos;
    }

    public ZslyRequest setExcludeOrderNos(List<String> excludeOrderNos) {
        this.excludeOrderNos = excludeOrderNos;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ZslyRequest.class.getSimpleName() + "[", "]")
                .add("slang='" + slang + "'")
                .add("updateFundStatusFlag=" + updateFundStatusFlag)
                .add("lastOrderDate=" + lastOrderDate)
                .add("testOrderNos=" + testOrderNos)
                .add("excludeOrderNos=" + excludeOrderNos)
                .toString();
    }
}
