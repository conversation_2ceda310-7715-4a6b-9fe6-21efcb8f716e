<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.docking.mapper.DockingExtraMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.docking.model.DockingExtra">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="statusExtra" jdbcType="INTEGER" property="statusextra" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, creation_time, update_time, `type`, info, extra_info, statusExtra, memo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_docking_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_docking_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.docking.model.DockingExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_docking_extra (creation_time, update_time, `type`, 
      info, extra_info, statusExtra, 
      memo)
    values (#{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}, 
      #{info,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{statusextra,jdbcType=INTEGER}, 
      #{memo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.docking.model.DockingExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_docking_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="info != null">
        info,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="statusextra != null">
        statusExtra,
      </if>
      <if test="memo != null">
        memo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="info != null">
        #{info,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="statusextra != null">
        #{statusextra,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.docking.model.DockingExtra">
    <!--@mbg.generated-->
    update t_docking_extra
    <set>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="info != null">
        info = #{info,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="statusextra != null">
        statusExtra = #{statusextra,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.docking.model.DockingExtra">
    <!--@mbg.generated-->
    update t_docking_extra
    set creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=INTEGER},
      info = #{info,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      statusExtra = #{statusextra,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-09-25-->
  <select id="findDockingExtra" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_docking_extra
    where 1=1
    <if test="id!=null">
      and id=#{id,jdbcType=BIGINT}
    </if>
    <if test="creationTime!=null">
      and creation_time=#{creationTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateTime!=null">
      and update_time=#{updateTime,jdbcType=TIMESTAMP}
    </if>
    <if test="type!=null">
      and `type`=#{type,jdbcType=INTEGER}
    </if>
    <if test="info!=null">
      and info=#{info,jdbcType=VARCHAR}
    </if>
    <if test="extraInfo!=null">
      and extra_info=#{extraInfo,jdbcType=VARCHAR}
    </if>
    <if test="statusextra!=null">
      and statusExtra=#{statusextra,jdbcType=INTEGER}
    </if>
    <if test="memo!=null">
      and memo=#{memo,jdbcType=VARCHAR}
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2019-10-10-->
  <update id="updateExtraInfoByInfo">
    update t_docking_extra
    set extra_info=#{extraInfo,jdbcType=VARCHAR}
    where 1=1
    <if test="info!=null">
      and info=#{info,jdbcType=VARCHAR}
    </if>
  </update>

<!--auto generated by MybatisCodeHelper on 2020-02-24-->
  <insert id="insertList">
        INSERT INTO t_docking_extra(
        id,
        creation_time,
        update_time,
        type,
        info,
        extra_info,
        statusExtra,
        memo
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=BIGINT},
            #{element.creationTime,jdbcType=TIMESTAMP},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.type,jdbcType=INTEGER},
            #{element.info,jdbcType=VARCHAR},
            #{element.extraInfo,jdbcType=VARCHAR},
            #{element.statusextra,jdbcType=INTEGER},
            #{element.memo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-05-25-->
  <select id="findByInfoIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_docking_extra
        where info in
        <foreach item="item" index="index" collection="infoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-11-03-->
  <update id="updateByInfo">
    update t_docking_extra
    <set>
      <if test="updated.type != null">
        type = #{updated.type,jdbcType=INTEGER},
      </if>
      <if test="updated.info != null">
        info = #{updated.info,jdbcType=VARCHAR},
      </if>
      <if test="updated.extraInfo != null">
        extra_info = #{updated.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="updated.statusextra != null">
        statusExtra = #{updated.statusextra,jdbcType=INTEGER},
      </if>
      <if test="updated.memo != null">
        memo = #{updated.memo,jdbcType=VARCHAR},
      </if>
    </set>
    where info=#{updated.info,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-11-06-->
  <select id="countByInfo" resultType="java.lang.Long">
    select count(1)
    from t_docking_extra
    where info=#{info,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2020-11-23-->
  <select id="findByExtraInfoInAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_docking_extra
        where extra_info in
        <foreach item="item" index="index" collection="extraInfoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and `type`=#{type,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-01-->
  <select id="findByInfoInAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_docking_extra
    <where>
      <if test="infoCollection != null and infoCollection.size() > 0">
        and info in
        <foreach item="item" index="index" collection="infoCollection"
                 open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="type != null">
        and `type`=#{type,jdbcType=INTEGER}
      </if>
    </where>
  </select>

  <select id="findByInfoInAndTypeOrderByUpdateTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_docking_extra
    <where>
      <if test="infoCollection != null and infoCollection.size() > 0">
        and info in
        <foreach item="item" index="index" collection="infoCollection"
                 open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="type != null">
        and `type`=#{type,jdbcType=INTEGER}
      </if>
    </where>
    order by update_time desc
  </select>
</mapper>