package com.ruijing.store.order.util;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.common.BasePageParamDTO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 分页查询工具类
 * @author: zhongyu<PERSON>i
 * @create: 2019/11/7 15:49
 **/
public class PageResponseUtils {

    /**
     * 分页工具
     * @param supplier     需要分页的查询
     * @param pageNumber   pagenumber
     * @param pageSize
     * @return
     */
    public static <T> BasePageResponseDTO<T> pageInvoke(Supplier<List<T>> supplier, Integer pageNumber , Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        ArrayList<T> resultList = (ArrayList) supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);
        //page对象转List
        List<T> pageList = New.list(pageInfo.getList());
        BasePageResponseDTO<T> resultDTO = new BasePageResponseDTO();
        resultDTO.setData(pageList);
        resultDTO.setPageNo(pageInfo.getPageNum());
        resultDTO.setPageSize(pageInfo.getPageSize());
        resultDTO.setTotal(pageInfo.getTotal());
        return resultDTO;
    }

    /**
     * 分页异构转换函数
     * @param supplier      分页查询结果生产函数
     * @param function      异构转换函数
     * @param pageNumber    页数
     * @param pageSize      每页结果数
     * @param <T>           mybatis DO对象类型
     * @param <R>           DTO对象类型
     * @return              分页结果
     */
    public static <T, R> BasePageResponseDTO<R> pageInvoke(Supplier<List<T>> supplier,
                                                           Function<? super T, ? extends R> function,
                                                           Integer pageNumber,
                                                           Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        ArrayList<T> resultList = (ArrayList) supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);

        List<R> pageList = resultList.stream().map(function).collect(Collectors.toList());
        BasePageResponseDTO<R> resultDTO = new BasePageResponseDTO();
        resultDTO.setData(pageList);
        resultDTO.setPageNo(pageInfo.getPageNum());
        resultDTO.setPageSize(pageInfo.getPageSize());
        resultDTO.setTotal(pageInfo.getTotal());
        return resultDTO;
    }

    /**
     * 分页参数 ，得到搜索起始
     * @param basePageParamDTO
     * @return
     */
    public static Integer getRequestStart(BasePageParamDTO basePageParamDTO){
        Integer pageNo = basePageParamDTO.getPageNo();
        Integer pageSize = basePageParamDTO.getPageSize();
        return getRequestStart(pageNo, pageSize);
    }

    /**
     * 分页参数 ，得到搜索起始
     * @param pageNo
     * @param pageSize
     * @return start
     */
    public static Integer getRequestStart(Integer pageNo, Integer pageSize) {
        Integer start = (pageNo - 1) * pageSize;
        return start;
    }
}
