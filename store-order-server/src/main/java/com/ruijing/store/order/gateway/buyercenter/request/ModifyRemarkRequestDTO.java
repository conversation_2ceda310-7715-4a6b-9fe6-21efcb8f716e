package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2024-02-28 10:37
 * @description:
 **/
public class ModifyRemarkRequestDTO implements Serializable {

    private static final long serialVersionUID = 3751945717860390265L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("订单评论")
    private String remark;

    public Integer getOrderId() {
        return orderId;
    }

    public ModifyRemarkRequestDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public ModifyRemarkRequestDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ModifyRemarkRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("remark='" + remark + "'")
                .toString();
    }
}
