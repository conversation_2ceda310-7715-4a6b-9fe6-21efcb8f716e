<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
    <!--@mbg.generated-->
    <!--@Table t_ref_fundcard_order-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="application_id" jdbcType="VARCHAR" property="applicationId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="bid_id" jdbcType="VARCHAR" property="bidId" />
    <result column="card_id" jdbcType="VARCHAR" property="cardId" />
    <result column="subject_id" jdbcType="VARCHAR" property="subjectId" />
    <result column="serial_number" jdbcType="INTEGER" property="serialNumber" />
    <result column="useMoney" jdbcType="DECIMAL" property="usemoney" />
    <result column="deletion_time" jdbcType="TIMESTAMP" property="deletionTime" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="card_no" jdbcType="VARCHAR" property="cardNo" />
    <result column="fund_type" jdbcType="INTEGER" property="fundType" />
    <result column="approve_user" jdbcType="INTEGER" property="approveUser" />
    <result column="freeze_amount" jdbcType="DECIMAL" property="freezeAmount" />
    <result column="campus_code" jdbcType="VARCHAR" property="campusCode" />
    <result column="campus_name" jdbcType="VARCHAR" property="campusName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, application_id, order_id, bid_id, card_id, subject_id, serial_number, useMoney, 
    deletion_time, is_deleted, creation_time, update_time, card_no, fund_type, approve_user, 
    freeze_amount, campus_code, campus_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_ref_fundcard_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from t_ref_fundcard_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
    <!--@mbg.generated-->
    insert into t_ref_fundcard_order (id, application_id, order_id, 
      bid_id, card_id, subject_id, 
      serial_number, useMoney, deletion_time, 
      is_deleted, creation_time, update_time, 
      card_no, fund_type, approve_user, 
      freeze_amount, campus_code, campus_name)
    values (#{id,jdbcType=VARCHAR}, #{applicationId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{bidId,jdbcType=VARCHAR}, #{cardId,jdbcType=VARCHAR}, #{subjectId,jdbcType=VARCHAR}, 
      #{serialNumber,jdbcType=INTEGER}, #{usemoney,jdbcType=DECIMAL}, #{deletionTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{cardNo,jdbcType=VARCHAR}, #{fundType,jdbcType=INTEGER}, #{approveUser,jdbcType=INTEGER}, 
      #{freezeAmount,jdbcType=DECIMAL}, #{campusCode,jdbcType=VARCHAR}, #{campusName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
    <!--@mbg.generated-->
    insert into t_ref_fundcard_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applicationId != null">
        application_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="bidId != null">
        bid_id,
      </if>
      <if test="cardId != null">
        card_id,
      </if>
      <if test="subjectId != null">
        subject_id,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="usemoney != null">
        useMoney,
      </if>
      <if test="deletionTime != null">
        deletion_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="cardNo != null">
        card_no,
      </if>
      <if test="fundType != null">
        fund_type,
      </if>
      <if test="approveUser != null">
        approve_user,
      </if>
      <if test="freezeAmount != null">
        freeze_amount,
      </if>
        <if test="campusCode != null">
            campus_code,
        </if>
        <if test="campusName != null">
            campus_name
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applicationId != null">
        #{applicationId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="bidId != null">
        #{bidId,jdbcType=VARCHAR},
      </if>
      <if test="cardId != null">
        #{cardId,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        #{subjectId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="usemoney != null">
        #{usemoney,jdbcType=DECIMAL},
      </if>
      <if test="deletionTime != null">
        #{deletionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardNo != null">
        #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="fundType != null">
        #{fundType,jdbcType=INTEGER},
      </if>
      <if test="approveUser != null">
        #{approveUser,jdbcType=INTEGER},
      </if>
      <if test="freezeAmount != null">
        #{freezeAmount,jdbcType=DECIMAL},
      </if>
        <if test="campusCode != null">
            #{campusCode,jdbcType=VARCHAR},
        </if>
        <if test="campusName != null">
            #{campusName,jdbcType=VARCHAR}
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
    <!--@mbg.generated-->
    update t_ref_fundcard_order
    <set>
      <if test="applicationId != null">
        application_id = #{applicationId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="bidId != null">
        bid_id = #{bidId,jdbcType=VARCHAR},
      </if>
      <if test="cardId != null">
        card_id = #{cardId,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        subject_id = #{subjectId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=INTEGER},
      </if>
      <if test="usemoney != null">
        useMoney = #{usemoney,jdbcType=DECIMAL},
      </if>
      <if test="deletionTime != null">
        deletion_time = #{deletionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cardNo != null">
        card_no = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="fundType != null">
        fund_type = #{fundType,jdbcType=INTEGER},
      </if>
      <if test="approveUser != null">
        approve_user = #{approveUser,jdbcType=INTEGER},
      </if>
      <if test="freezeAmount != null">
        freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      </if>
        <if test="campusCode != null">
            campus_code = #{campusCode,jdbcType=VARCHAR},
        </if>
        <if test="campusName != null">
            campus_name = #{campusName,jdbcType=VARCHAR}
        </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
    <!--@mbg.generated-->
    update t_ref_fundcard_order
    set application_id = #{applicationId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      bid_id = #{bidId,jdbcType=VARCHAR},
      card_id = #{cardId,jdbcType=VARCHAR},
      subject_id = #{subjectId,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=INTEGER},
      useMoney = #{usemoney,jdbcType=DECIMAL},
      deletion_time = #{deletionTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      card_no = #{cardNo,jdbcType=VARCHAR},
      fund_type = #{fundType,jdbcType=INTEGER},
      approve_user = #{approveUser,jdbcType=INTEGER},
      freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      campus_code = #{campusCode,jdbcType=VARCHAR},
      campus_name = #{campusName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <update id="updateByOrderId" parameterType="com.ruijing.store.order.base.core.model.RefFundcardOrderDO">
        <!--@mbg.generated-->
        update t_ref_fundcard_order
        <set>
            <if test="applicationId != null">
                application_id = #{applicationId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null and orderId != ''">
                order_id = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="bidId != null and bidId != ''">
                bid_id = #{bidId,jdbcType=VARCHAR},
            </if>
            <if test="cardId != null and cardId != ''">
                card_id = #{cardId,jdbcType=VARCHAR},
            </if>
            <if test="subjectId != null and subjectId != ''">
                subject_id = #{subjectId,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null">
                serial_number = #{serialNumber,jdbcType=INTEGER},
            </if>
            <if test="usemoney != null">
                useMoney = #{usemoney,jdbcType=DECIMAL},
            </if>
            <if test="deletionTime != null">
                deletion_time = #{deletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="creationTime != null">
                creation_time = #{creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cardNo != null and cardNo != ''">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="fundType != null">
                fund_type = #{fundType,jdbcType=INTEGER},
            </if>
            <if test="approveUser != null">
                approve_user = #{approveUser,jdbcType=INTEGER},
            </if>
            <if test="freezeAmount != null">
                freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
            </if>
            <if test="campusCode != null">
                campus_code = #{campusCode,jdbcType=VARCHAR},
            </if>
            <if test="campusName != null">
                campus_name = #{campusName,jdbcType=VARCHAR}
            </if>
        </set>
        where order_id = #{orderId,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2019-10-09-->
  <update id="updateIsDeletedByOrderId">
    update t_ref_fundcard_order
    set is_deleted=#{updatedIsDeleted,jdbcType=BIT}
    where order_id=#{orderId,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-10-09-->
  <delete id="deleteByOrderId">
    delete from t_ref_fundcard_order
    where order_id=#{orderId,jdbcType=VARCHAR}
  </delete>

<!--auto generated by MybatisCodeHelper on 2019-12-26-->
  <select id="selectAllByOrderIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_fundcard_order
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2019-12-31-->
  <delete id="deleteByOrderIdIn">
        delete from t_ref_fundcard_order
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

<!--auto generated by MybatisCodeHelper on 2020-01-03-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_ref_fundcard_order(
        id,
        application_id,
        order_id,
        bid_id,
        card_id,
        subject_id,
        serial_number,
        useMoney,
        deletion_time,
        is_deleted,
        creation_time,
        update_time,
        card_no,
        fund_type,
        approve_user,
        freeze_amount,
        campus_code,
        campus_name
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=VARCHAR},
            #{element.applicationId,jdbcType=VARCHAR},
            #{element.orderId,jdbcType=VARCHAR},
            #{element.bidId,jdbcType=VARCHAR},
            #{element.cardId,jdbcType=VARCHAR},
            #{element.subjectId,jdbcType=VARCHAR},
            #{element.serialNumber,jdbcType=INTEGER},
            #{element.usemoney,jdbcType=DECIMAL},
            #{element.deletionTime,jdbcType=TIMESTAMP},
            #{element.isDeleted,jdbcType=BIT},
            #{element.creationTime,jdbcType=TIMESTAMP},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.cardNo,jdbcType=VARCHAR},
            #{element.fundType,jdbcType=INTEGER},
            #{element.approveUser,jdbcType=INTEGER},
            #{element.freezeAmount,jdbcType=DECIMAL},
            #{element.campusCode,jdbcType=VARCHAR},
            #{element.campusName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-01-07-->
  <select id="findByOrderIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_fundcard_order
        where
       order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-02-13-->
  <select id="selectAllByApplicationIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ref_fundcard_order
    where application_id in
    <foreach item="item" index="index" collection="applicationIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <select id="findOrderIdByCardIds" resultType="java.lang.String">
        select order_id
        from t_ref_fundcard_order
        where card_id in
        <foreach item="item" index="index" collection="cardIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and order_id is not null
    </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <select id="findByApplicationId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_fundcard_order
        where application_id=#{applicationId,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <select id="findByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_fundcard_order
        where order_id=#{orderId,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-05-13-->
  <select id="findByBidId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_ref_fundcard_order
        where bid_id=#{bidId,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-06-04-->
  <update id="updateByApplicationId">
        update t_ref_fundcard_order
        <set>
            <if test="updated.id != null">
                id = #{updated.id,jdbcType=VARCHAR},
            </if>
            <if test="updated.applicationId != null and updated.applicationId != ''">
                application_id = #{updated.applicationId,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderId != null and updated.orderId != ''">
                order_id = #{updated.orderId,jdbcType=VARCHAR},
            </if>
            <if test="updated.bidId != null and updated.bidId != ''">
                bid_id = #{updated.bidId,jdbcType=VARCHAR},
            </if>
            <if test="updated.cardId != null and updated.cardId != ''">
                card_id = #{updated.cardId,jdbcType=VARCHAR},
            </if>
            <if test="updated.subjectId != null  and updated.subjectId != ''">
                subject_id = #{updated.subjectId,jdbcType=VARCHAR},
            </if>
            <if test="updated.serialNumber != null">
                serial_number = #{updated.serialNumber,jdbcType=INTEGER},
            </if>
            <if test="updated.usemoney != null">
                useMoney = #{updated.usemoney,jdbcType=DECIMAL},
            </if>
            <if test="updated.deletionTime != null">
                deletion_time = #{updated.deletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.isDeleted != null">
                is_deleted = #{updated.isDeleted,jdbcType=BIT},
            </if>
            <if test="updated.creationTime != null">
                creation_time = #{updated.creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.cardNo != null and updated.cardNo != ''">
                card_no = #{updated.cardNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.fundType != null">
                fund_type = #{updated.fundType,jdbcType=INTEGER},
            </if>
            <if test="updated.approveUser != null">
                approve_user = #{updated.approveUser,jdbcType=INTEGER},
            </if>
            <if test="updated.freezeAmount != null">
                freeze_amount = #{updated.freezeAmount,jdbcType=DECIMAL},
            </if>
            <if test="updated.campusCode != null">
                campus_code = #{updated.campusCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.campusName != null">
                campus_name = #{updated.campusName,jdbcType=VARCHAR}
            </if>
        </set>
        where application_id=#{updated.applicationId,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2020-06-04-->
  <update id="updateByBidId">
        update t_ref_fundcard_order
        <set>
            <if test="updated.id != null">
                id = #{updated.id,jdbcType=VARCHAR},
            </if>
            <if test="updated.applicationId != null and updated.applicationId != ''">
                application_id = #{updated.applicationId,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderId != null and updated.orderId != ''">
                order_id = #{updated.orderId,jdbcType=VARCHAR},
            </if>
            <if test="updated.bidId != null and updated.bidId != ''">
                bid_id = #{updated.bidId,jdbcType=VARCHAR},
            </if>
            <if test="updated.cardId != null and updated.cardId != ''">
                card_id = #{updated.cardId,jdbcType=VARCHAR},
            </if>
            <if test="updated.subjectId != null and updated.subjectId != ''">
                subject_id = #{updated.subjectId,jdbcType=VARCHAR},
            </if>
            <if test="updated.serialNumber != null">
                serial_number = #{updated.serialNumber,jdbcType=INTEGER},
            </if>
            <if test="updated.usemoney != null">
                useMoney = #{updated.usemoney,jdbcType=DECIMAL},
            </if>
            <if test="updated.deletionTime != null">
                deletion_time = #{updated.deletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.isDeleted != null">
                is_deleted = #{updated.isDeleted,jdbcType=BIT},
            </if>
            <if test="updated.creationTime != null">
                creation_time = #{updated.creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.cardNo != null and updated.cardNo != ''">
                card_no = #{updated.cardNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.fundType != null">
                fund_type = #{updated.fundType,jdbcType=INTEGER},
            </if>
            <if test="updated.approveUser != null">
                approve_user = #{updated.approveUser,jdbcType=INTEGER},
            </if>
            <if test="updated.freezeAmount != null">
                freeze_amount = #{updated.freezeAmount,jdbcType=DECIMAL},
            </if>
            <if test="updated.campusCode != null">
                campus_code = #{updated.campusCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.campusName != null">
                campus_name = #{updated.campusName,jdbcType=VARCHAR}
            </if>
        </set>
        where bid_id=#{updated.bidId,jdbcType=VARCHAR}
    </update>

    <!--auto generated by MybatisCodeHelper on 2020-01-03-->
    <insert id="batchInsertSelective" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_ref_fundcard_order(
        id,
        application_id,
        order_id,
        bid_id,
        card_id,
        subject_id,
        serial_number,
        useMoney,
        deletion_time,
        is_deleted,
        creation_time,
        update_time,
        card_no,
        fund_type,
        approve_user,
        freeze_amount,
        campus_code,
        campus_name
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=VARCHAR},
            #{element.applicationId,jdbcType=VARCHAR},
            #{element.orderId,jdbcType=VARCHAR},
            #{element.bidId,jdbcType=VARCHAR},
            #{element.cardId,jdbcType=VARCHAR},
            #{element.subjectId,jdbcType=VARCHAR},
            #{element.serialNumber,jdbcType=INTEGER},
            #{element.usemoney,jdbcType=DECIMAL},
            #{element.deletionTime,jdbcType=TIMESTAMP},

            <choose>
                <when test="element.isDeleted != null">
                    #{element.isDeleted,jdbcType=BIT},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.creationTime != null">
                    #{element.creationTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            <choose>
                <when test="element.updateTime != null">
                    #{element.updateTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            #{element.cardNo,jdbcType=VARCHAR},

            <choose>
                <when test="element.fundType != null">
                    #{element.fundType,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.approveUser != null">
                    #{element.approveUser,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.freezeAmount != null">
                    #{element.freezeAmount,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.000,
                </otherwise>
            </choose>
            #{element.campusCode,jdbcType=VARCHAR},
            #{element.campusName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2021-02-05-->
  <delete id="deleteByIdIn">
        delete from t_ref_fundcard_order
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

</mapper>