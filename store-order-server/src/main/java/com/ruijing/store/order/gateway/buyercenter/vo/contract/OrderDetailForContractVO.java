package com.ruijing.store.order.gateway.buyercenter.vo.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Date: 2020/12/29 10:26
 */
@RpcModel("合同-订单详情")
public class OrderDetailForContractVO implements Serializable {

    private static final long serialVersionUID = 3979310254488595444L;

    /**
     * 货号
     */
    @RpcModelProperty("货号")
    private String fgoodcode;

    /**
     * 品牌
     */
    @RpcModelProperty("品牌")
    private String fbrand;

    /**
     * 商品名
     */
    @RpcModelProperty("商品名")
    private String fgoodname;

    /**
     * 数量
     */
    @RpcModelProperty("数量")
    private Double fquantity;

    /**
     * 计量单位
     */
    @RpcModelProperty("计量单位")
    private String fspec;

    public String getFgoodcode() {
        return fgoodcode;
    }

    public OrderDetailForContractVO setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
        return this;
    }

    public String getFbrand() {
        return fbrand;
    }

    public OrderDetailForContractVO setFbrand(String fbrand) {
        this.fbrand = fbrand;
        return this;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public OrderDetailForContractVO setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
        return this;
    }

    public Double getFquantity() {
        return fquantity;
    }

    public OrderDetailForContractVO setFquantity(Double fquantity) {
        this.fquantity = fquantity;
        return this;
    }

    public String getFspec() {
        return fspec;
    }

    public OrderDetailForContractVO setFspec(String fspec) {
        this.fspec = fspec;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderDetailForContractVO{");
        sb.append("fgoodcode='").append(fgoodcode).append('\'');
        sb.append(", fbrand='").append(fbrand).append('\'');
        sb.append(", fgoodname='").append(fgoodname).append('\'');
        sb.append(", fquantity=").append(fquantity);
        sb.append(", fspec='").append(fspec).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
