package com.ruijing.store.order.base.core.model;

public class GoodsReturnByDept {
    /**
     * 课题组id
     */
    private Integer deptId;

    /**
     * 退货金额总计
     */
    private Double sumReturnAmount;

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Double getSumReturnAmount() {
        return sumReturnAmount;
    }

    public void setSumReturnAmount(Double sumReturnAmount) {
        this.sumReturnAmount = sumReturnAmount;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnByDept{");
        sb.append("deptId=").append(deptId);
        sb.append(", sumReturnAmount=").append(sumReturnAmount);
        sb.append('}');
        return sb.toString();
    }
}
