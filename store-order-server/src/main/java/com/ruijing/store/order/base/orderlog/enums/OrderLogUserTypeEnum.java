package com.ruijing.store.order.base.orderlog.enums;

public enum OrderLogUserTypeEnum {
    PURCHASE(0, "采购"),
    SUPPLIER(1, "供应商"),
    ;
    private int code;
    private String description;

    OrderLogUserTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
