package com.ruijing.store.order.base.core.service.impl;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.ApplicationSaveDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.*;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDetailDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderAggregationResultDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.orderlog.enums.OrderLogUserTypeEnum;
import com.ruijing.store.order.base.orderlog.mapper.OrderLogDOMapper;
import com.ruijing.store.order.base.orderlog.mapper.OrderLogExtraDOMapper;
import com.ruijing.store.order.base.orderlog.model.OrderLogDO;
import com.ruijing.store.order.base.orderlog.model.OrderLogExtraDO;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.OrderDetailExtraClient;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.TranslateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单详情服务
 * @author: zhuk
 * @create: 2019-06-28 18:21
 **/
@ServiceLog
@MSharpService
public class OrderDetailServiceImpl implements OrderDetailService {

    private static final String CAT_TYPE = "OrderDetailService";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderLogDOMapper orderLogDOMapper;

    @Resource
    private OrderLogExtraDOMapper orderLogExtraDOMapper;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    /**
     * 订单日志操作类型，写死1，历史原因
     */
    private static final int ORDER_LOG_OPERATION = 1;

    /**
     *
     */
    private static final String ORDER_LOG_OPERATION_DESCRIPTION = "detail_goods_name";

    /**
     * 订单扩展表的值，写死1
     */
    private static final int ORDER_LOG_EXTRA = 1;

    @Override
    public RemoteResponse<List<OrderDetailDTO>> findOrderDetailsByMasterId(OrderDetailReq orderDetailReq) {
        Integer orderMasterId = orderDetailReq.getOrderMasterId();
        Preconditions.notNull(orderMasterId, "入参不可空");
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterId);
        List<OrderDetailDTO> torderDetailDTOList = orderDetailDOList.stream().map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderDetailDTO>>custom().setSuccess().setData(torderDetailDTOList).build();
    }

    @Override
    public RemoteResponse<List<OrderDetailDTO>> findOrderDetailByMasterIdList(OrderDetailReq orderDetailReq) {
        List<Integer> orderMasterIdList = orderDetailReq.getOrderMasterIdList();
        BusinessErrUtil.notEmpty(orderMasterIdList, "查询失败！订单id数组为空！");
        BusinessErrUtil.isTrue(orderMasterIdList.size() < 501, "查询失败！单次查询数量不可超过500");
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderMasterIdList);
        List<OrderDetailDTO> result = orderDetailDOList.stream().map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderDetailDTO>>custom().setSuccess().setData(result).build();
    }

    @Override
    @ServiceLog(description = "获取订单顶级分类结果")
    public RemoteResponse<Set<Integer>> findDetailTagValue(Integer orderId) {
        BusinessErrUtil.notNull(orderId, "查询失败！订单id为空！");

        List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(orderId);
        // 取得商品id，并筛选掉退货完成的商品(退货中的订单无法发起验收，故无需筛退货中的商品)
        Set<Integer> tagValueList = orderDetailList.stream()
                .filter(d -> { return !OrderDetailReturnStatus.SUCCESS.getCode().equals(d.getReturnStatus());})
                .map(d -> InboundTypeEnum.descOf(d.getCategoryTag()).getValue())
                .collect(Collectors.toSet());
        return RemoteResponse.<Set<Integer>>custom().setSuccess().setData(tagValueList).build();
    }

    @Override
    @ServiceLog(description = "根据detailId查找商品id")
    public RemoteResponse<Long> findProductSnByDetailId(Integer detailId) {
        BusinessErrUtil.notNull(detailId, "查询商品id失败！detailId为空！");
        Long productSn = orderDetailMapper.findProductSnById(detailId);
        return RemoteResponse.<Long>custom().setSuccess().setData(productSn).build();
    }

    @Override
    @ServiceLog(description = "待确认的订单更新订单详情商品名", operationType = OperationType.WRITE)
    public RemoteResponse updateGoodsNameById(OrderDetailReq request) {
        BusinessErrUtil.isTrue(request != null && request.getOrderDetailId() != null && request.getProductName() != null, "更新订单失败，缺少必要入参！");
        // 校验订单是否为待确认状态
        OrderDetailDO orderDetail = orderDetailMapper.selectByPrimaryKey(request.getOrderDetailId());
        BusinessErrUtil.notNull(orderDetail, ExecptionMessageEnum.UPDATE_PRODUCT_NAME_FAILED);

        // 商品id
        Long productSn = orderDetail.getProductSn();
        BusinessErrUtil.isTrue(productSn != null, "修改商品名失败，商品id为空！");
        // 订单信息
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderDetail.getFmasterid());
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForConfirm.getValue().equals(orderMaster.getStatus()), ExecptionMessageEnum.MODIFICATION_FAILED_NOT_PENDING);
        BusinessErrUtil.isTrue(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orderMaster.getFuserid()), ExecptionMessageEnum.MODIFICATION_FAILED_NON_LARGE_UNIT);

        // 走搜索吧， 一次捞出这个商品下所有 这个单位 的待确认单信息
        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setStatusList(Arrays.asList(OrderStatusEnum.WaitingForConfirm.getValue()));
        param.setOrgIdList(Arrays.asList(orderMaster.getFuserid()));
        param.setProductSn(productSn.toString());
        // 测试环境的数据很多，暂时设定300，线网一般只有十几条
        param.setPageSize(300);
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(param);
        if (CollectionUtils.isEmpty(response.getRecordList())) {
            Cat.logWarn(CAT_TYPE, "updateGoodsNameById", "无商品名称需要修改");
            return RemoteResponse.custom().setFailure("无商品名称需要修改").build();
        }

        Supplier<Stream<OrderMasterSearchDTO>> orderStream = () -> response.getRecordList().stream();
        // 订单详情id
        List<Integer> orderDetailRecordIdList = orderStream
                .get()
                .map(OrderMasterSearchDTO::getOrderDetail)
                .flatMap(List::stream)
                .filter(detail -> productSn.equals(detail.getProductId()))
                .map(OrderDetailSearchDTO::getDetailId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderDetailRecordIdList)) {
            Cat.logWarn(CAT_TYPE, "updateGoodsNameById", "无订单商品需要修改");
            return RemoteResponse.custom().setFailure("无订单商品需要修改").build();
        }

        // 更改detail的商品名
        orderDetailMapper.updateFgoodnameByIdIn(request.getProductName(), orderDetailRecordIdList);
        // 采购单id
        List<Long> buyAppIdList = orderStream.get().map(OrderMasterSearchDTO::getFtbuyappid).filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
        // 更改采购单detail的商品名
        AsyncExecutor.listenableRunAsync(() -> {
            saveApplicationDetail(request, buyAppIdList, productSn);
        }).addFailureCallback(throwable -> {
            logger.error("待确认的订单更新订单详情商品名失败{}", throwable);
            Cat.logError(CAT_TYPE, "updateGoodsNameById", "待确认的订单更新订单详情商品名失败", throwable);
        });

        // 记录操作日志
        OrderLogDO orderLog = new OrderLogDO();
        orderLog.setOrderId(orderMaster.getId().longValue());
        orderLog.setOrderDetailId(orderDetail.getId().longValue());
        orderLog.setOperation(ORDER_LOG_OPERATION);
        orderLog.setUserId(request.getUserId().longValue());
        orderLog.setUserType(OrderLogUserTypeEnum.SUPPLIER.getCode());
        orderLog.setUserName(request.getUserName());
        orderLog.setNote("");
        orderLog.setExtra(ORDER_LOG_EXTRA);
        orderLogDOMapper.insertSelective(orderLog);
        // 记录操作内容
        OrderLogExtraDO orderLogExtra = new OrderLogExtraDO();
        orderLogExtra.setLogId(orderLog.getId());
        orderLogExtra.setField(ORDER_LOG_OPERATION_DESCRIPTION);
        orderLogExtra.setOldValue(orderDetail.getFgoodname());
        orderLogExtra.setNewValue(request.getProductName());
        orderLogExtraDOMapper.insertSelective(orderLogExtra);

        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 保存/更新 采购申请单详情信息
     * @param request
     * @param buyAppIdList
     */
    private void saveApplicationDetail(OrderDetailReq request, List<Long> buyAppIdList, Long productSn) {
        ApplicationQueryDTO queryDTO = new ApplicationQueryDTO();
        queryDTO.setApplyIds(buyAppIdList);
        List<ApplicationDetailDTO> applicationDetailList = applicationBaseClient.findDetailByMasterId(queryDTO);

        if (CollectionUtils.isEmpty(applicationDetailList)) {
            Cat.logWarn(CAT_TYPE, "updateGoodsNameById", "无采购单商品需要修改");
            return;
        }
        // 需要更改的采购单详情列表
        List<ApplicationDetailDTO> saveDetailList = applicationDetailList.stream()
                .filter(detail -> productSn.equals(detail.getProductSn()))
                .map(detail -> {
                    ApplicationDetailDTO item = new ApplicationDetailDTO();
                    item.setId(detail.getId());
                    item.setGoodName(request.getProductName());
                    return item;
                })
                .collect(Collectors.toList());

        ApplicationSaveDTO applicationSaveDto = new ApplicationSaveDTO();
        applicationSaveDto.setDetails(saveDetailList);
        applicationSaveDto.setBusiness(ApplicationSaveDTO.BusinessEnum.APPLY_DETAIL);
        applicationBaseClient.saveApplicationDetail(applicationSaveDto);
    }

    @Override
    @ServiceLog(description = "批量更新订单明细", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> updateByIdList(List<OrderDetailDTO> params) {
        BusinessErrUtil.notEmpty(params, "更新失败，订单明细为空！");
        BusinessErrUtil.isTrue(params.size() <= 200, "更新数量上限200个");
        List<OrderDetailDO> orderDetailList = params.stream().map(OrderDetailTranslator::dtoToOrderDetailDO).collect(Collectors.toList());
        int affect = orderDetailMapper.loopUpdateByIdIn(orderDetailList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect).build();
    }

    @Override
    @ServiceLog(description = "订单商品销量统计")
    public RemoteResponse<List<OrderDetailStatisticsDTO>> orderDetailSaleStatistics(StatisticsManagerParamDTO request) {
        List<Integer> departmentIdList = request.getDepartmentIds();
        List<Long> productIdList = request.getProductIdList();
        departmentIdList = departmentIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        productIdList = productIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Preconditions.notEmpty(departmentIdList, "统计失败！departmentIdList不可为空");
        Preconditions.notEmpty(productIdList, "统计失败！productIdList不可为空");

        List<OrderAggregationResultDTO> aggregationProductCount = orderSearchBoostService.aggProductAmountAndCount(request);

        List<OrderDetailStatisticsDTO> result = aggregationProductCount.stream().map(it -> {
            OrderDetailStatisticsDTO item = new OrderDetailStatisticsDTO();
            item.setProductId(it.getAggFieldId());
            item.setSaleTotal(BigDecimal.valueOf(it.getQuantity()));
            return item;
        }).collect(Collectors.toList());

        return RemoteResponse.<List<OrderDetailStatisticsDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<OrderDetailDTO>> findOrderDetailByIdList(OrderDetailReq request) {
        List<Integer> orderDetailIdList = request.getOrderDetailIdList();
        Preconditions.notEmpty(orderDetailIdList, "订单明细id不可为空！");
        Preconditions.isTrue(orderDetailIdList.size() <= 500, "单次查询订单明细数量不可超过500");
        List<OrderDetailDTO> orderDetailDTOList = TranslateUtils.toModelList(
                () -> orderDetailMapper.findByIdIn(orderDetailIdList),
                OrderDetailTranslator::orderDetailDOToTorderDetailDTO
        );
        List<Integer> orderIdList = orderDetailDTOList.stream().map(OrderDetailDTO::getFmasterid).collect(Collectors.toList());
        Map<Integer, List<OrderDetailExtraDTO>> orderIdDetailExtraMap = buyerOrderService.getOrderIdDetailExtraMap(orderIdList);
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderIdDetailExtraMap.values()
                .stream()
                .flatMap(List::stream)
                .collect(toList());
        Map<Integer, List<OrderDetailExtraDTO>> detailId2ExtraMap = DictionaryUtils.groupBy(orderDetailExtraDTOList, OrderDetailExtraDTO::getOrderDetailId);
        orderDetailDTOList.forEach((detailDTO) ->{
            List<OrderDetailExtraDTO> detailExtraDTOList = detailId2ExtraMap.get(detailDTO.getId());
            buyerOrderService.setOrderDetailDTOExtraInfo(detailDTO, detailExtraDTOList);
        });

        return RemoteResponse.<List<OrderDetailDTO>>custom().setSuccess().setData(orderDetailDTOList);
    }

    @Override
    public RemoteResponse<List<DetailBatchesDTO>> findOrderDetailBatches(OrderDetailReq request) {
        List<Integer> orderDetailIdList = request.getOrderDetailIdList();
        Preconditions.notEmpty(orderDetailIdList, "orderDetailIdList can not empty");
        Preconditions.isTrue(orderDetailIdList.size() <= 200, "orderDetailIdList cannot exceed the maximum value of 200");

        List<OrderUniqueBarCodeDTO> queryResult = orderUniqueBarCodeRPCClient.findByDetailIdList(orderDetailIdList);
        List<DetailBatchesDTO> result = queryResult.stream().map(item -> {
            DetailBatchesDTO dto = new DetailBatchesDTO();
            dto.setDetailId(item.getOrderDetailId());
            dto.setBatch(item.getBatches());
            dto.setExpiration(item.getExpiration());
            dto.setManufacturer(item.getManufacturer());
            dto.setProductionDate(DateUtils.format("yyyy-MM-dd",item.getProductionDate()));
            dto.setExterior(item.getExterior());
            dto.setUniqueBarCode(item.getUniBarCode());
            return dto;
        }).collect(Collectors.toList());
        return RemoteResponse.<List<DetailBatchesDTO>>custom().setSuccess().setData(result);
    }

    /**
     * 根据订单详情id的范围查询订单详情id列表（主要用于比对统计数据与实际订单数据的差异）
     *
     * @param lowerDetailId
     * @param higherDetailId
     * @return
     */
    @Override
    public RemoteResponse<List<Integer>> findDetailIdListRange(Integer lowerDetailId, Integer higherDetailId) {
        Preconditions.isTrue((higherDetailId - lowerDetailId) <= 501, "查询detailId列表范围不能大于500");
        List<Integer> detailIdList = orderDetailMapper.selectIdByIdBetween(lowerDetailId, higherDetailId);
        return RemoteResponse.<List<Integer>>custom().setData(detailIdList).setSuccess();
    }

    @Override
    public RemoteResponse<List<OrderDetailDTO>> findDetailByOrderIdListExcludeReturnStatus(OrderDetailReq orderDetailReq) {
        List<Integer> orderIdList =orderDetailReq.getOrderMasterIdList();
        Preconditions.notEmpty(orderIdList, "订单id不可空");
        Preconditions.isTrue(orderIdList.size() <= 100, "一次查询数量不可大于100");
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        if(CollectionUtils.isEmpty(orderDetailDOList)){
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderDetailDTO> orderDetailDTOList = orderDetailDOList.stream().map(detailDO->{
            // 由于是找到未退货部分，这部分已退货信息给他设置为0
            OrderDetailDTO orderDetailDTO = OrderDetailTranslator.orderDetailDOToTorderDetailDTO(detailDO);
            orderDetailDTO.setFcancelquantity(BigDecimal.ZERO);
            orderDetailDTO.setFcancelamount(BigDecimal.ZERO);
            return orderDetailDTO;
        }).collect(Collectors.toList());
        Map<Integer, OrderDetailDTO> detailIdDetailMap = DictionaryUtils.toMap(orderDetailDTOList, OrderDetailDTO::getId, Function.identity());
        // 需要返回绑定气瓶信息，也一并查询并返回
        if(Boolean.TRUE.equals(orderDetailReq.getReturnBindGasBottle())) {
            this.bindGasBottleToOrderDetail(orderIdList, detailIdDetailMap);
        }
        // 排除指定退货状态的商品和气瓶
        List<Integer> excludeReturnStatus = orderDetailReq.getExcludeReturnStatusList();
        orderDetailDTOList = this.excludeGivenReturnStatusDetail(orderIdList, orderDetailDTOList, detailIdDetailMap, excludeReturnStatus);
        return RemoteResponse.success(orderDetailDTOList);
    }

    @Override
    public RemoteResponse<Boolean> modifyOrderDetailProductSnapshotById(OrderDetailModifyRequestDTO orderDetailModifyRequestDTO) {
        Preconditions.notNull(orderDetailModifyRequestDTO.getDetailId(), "需要修改的商品id不可空");
        OrderDetailDO updateParam = new OrderDetailDO();
        updateParam.setId(orderDetailModifyRequestDTO.getDetailId());
        updateParam.setFpicpath(orderDetailModifyRequestDTO.getFpicpath());
        updateParam.setFgoodname(orderDetailModifyRequestDTO.getFgoodname());
        orderDetailMapper.updateByPrimaryKeySelective(updateParam);
        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<Boolean> batchModifyOrderDetailSnapshotById(List<OrderDetailModifyRequestDTO> orderDetailModifyRequestDTOList) {
        if(CollectionUtils.isEmpty(orderDetailModifyRequestDTOList)){
            return RemoteResponse.success();
        }
        for(OrderDetailModifyRequestDTO requestDetail : orderDetailModifyRequestDTOList){
            OrderDetailDO updateParam = new OrderDetailDO();
            updateParam.setId(requestDetail.getDetailId());
            updateParam.setFpicpath(requestDetail.getFpicpath());
            updateParam.setFgoodname(requestDetail.getFgoodname());
            updateParam.setSuppName(requestDetail.getSuppName());
            orderDetailMapper.updateByPrimaryKeySelective(updateParam);
        }
        return RemoteResponse.success();
    }

    /**
     * 气瓶数据绑到商品详情上
     */
    private void bindGasBottleToOrderDetail(List<Integer> orderIdList, Map<Integer, OrderDetailDTO> detailIdDetailMap) {
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(orderIdList, null);
        if (CollectionUtils.isNotEmpty(orderDetailExtraDTOList)) {
            // 有一物一码绑瓶信息的，先绑定到dto内
            orderDetailExtraDTOList.forEach(orderDetailExtraDTO -> {
                if (OrderDetailExtraEnum.BIND_GAS_BOTTLE_BARCODE.getType().equals(orderDetailExtraDTO.getExtraKeyType())) {
                    OrderDetailDTO matchDetail = detailIdDetailMap.get(orderDetailExtraDTO.getOrderDetailId());
                    if (StringUtils.isNotEmpty(orderDetailExtraDTO.getExtraValue()) && matchDetail != null) {
                        matchDetail.setBindGasBottleBarcodes(JsonUtils.parseList(orderDetailExtraDTO.getExtraValue(), String.class));
                    }
                }
            });
        }
    }

    /**
     * 排除指定退货状态的商品
     */
    private List<OrderDetailDTO> excludeGivenReturnStatusDetail(List<Integer> orderIdList, List<OrderDetailDTO> orderDetailDTOList, Map<Integer, OrderDetailDTO> detailIdDetailMap, List<Integer> excludeReturnStatus){
        if(CollectionUtils.isNotEmpty(excludeReturnStatus)){
            List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList);
            for(GoodsReturn goodsReturn : goodsReturnList){
                // 如果是需要排除的退货状态，则需要重新计算数量
                boolean needCalculateQuantity = excludeReturnStatus.contains(goodsReturn.getGoodsReturnStatus());
                if(!needCalculateQuantity){
                    // 撤销退货的不用减掉
                    continue;
                }
                GoodsReturnDTO goodsReturnDTO = GoodsReturnTranslator.doToDto(goodsReturn);
                // 找到匹配的商品详情项，将其退货中的数量、价格减掉，
                for(GoodsReturnDetailDTO returnDetail : goodsReturnDTO.getGoodsReturnDetailDTOS()){
                    OrderDetailDTO matchDetail = detailIdDetailMap.get(returnDetail.getDetailId());
                    if(matchDetail != null){
                        BigDecimal returnQuantity = new BigDecimal(returnDetail.getQuantity());
                        BigDecimal leftQuantity = matchDetail.getFquantity().subtract(returnQuantity);
                        matchDetail.setFquantity(leftQuantity);
                        matchDetail.setFbidamount(leftQuantity.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : matchDetail.getFbidamount().subtract(leftQuantity.multiply(matchDetail.getFbidprice())));
                        if(CollectionUtils.isNotEmpty(returnDetail.getReturnGasBottleBarcodes())){
                            // 需要计算气瓶时进行计算
                            returnDetail.getReturnGasBottleBarcodes().forEach(returnGasBottleBarcode->matchDetail.getBindGasBottleBarcodes().remove(returnGasBottleBarcode));
                        }
                    }
                }
            }
            // 已经全部退完的，不返回
            orderDetailDTOList = orderDetailDTOList.stream().filter(orderDetailDTO -> orderDetailDTO.getFquantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        }
        return orderDetailDTOList;
    }
}
