package com.ruijing.store.order.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.log.enums.OrderDockingOperationEnum;
import com.reagent.order.base.log.enums.OrderDockingResultEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.enums.ExportFileTypeEnum;
import com.reagent.order.base.order.enums.OrderExportStatusEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.reagent.research.financial.docking.dto.order.OrderDetailDTO;
import com.reagent.research.fundcard.dto.*;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.SelectFundCardDTO;
import com.reagent.research.fundcard.enums.*;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.tpi.tpiclient.api.enums.MessageTypeEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.cooperation.cbsd.api.msg.CbsdStoreHouseDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.apply.enums.application.PushStatusEnum;
import com.ruijing.store.approval.api.enums.ApproveLevelEnum;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.component.BeanContainer;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.gateway.dto.DeliveryNoteDTO;
import com.ruijing.store.order.api.gateway.dto.DeliveryNoteProductDTO;
import com.ruijing.store.order.api.gateway.dto.InvoicePrintDTO;
import com.ruijing.store.order.api.gateway.dto.SupplierUserDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.api.search.enums.DateUnitEnum;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.*;
import com.ruijing.store.order.base.core.model.*;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.*;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.service.DangerousTagDOService;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.base.timeoutstatistics.service.impl.TimeoutStatisticsWithBalanceImpl;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.ElectronicSignBO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.buyercenter.request.OrderMaterialCodeRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.TransactionCountRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.*;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.impl.OrderManageRpcServiceImpl;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.search.service.impl.OrderSearchBoostServiceImpl;
import com.ruijing.store.order.service.BizBaseService;
import com.ruijing.store.order.service.OldDateService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.QrCodeUtil;
import com.ruijing.store.order.util.TimeUtil;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单超时结算业务相关实现
 * @author: zhongyulei
 * @create: 2019/11/5 15:48
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class OrderManageServiceImpl implements OrderManageService {

    /**
     * 系统用户id
     */
    private static final int SYSTEM_OPERATOR_ID = -1;

    private final static String CAT_TYPE = "OrderManageServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 旧订单地址的分隔符
     */
    private final String deliveryProxy = " 转送 ";

    /**
     * 在该日期之前的单，都是南方医未对接财务的旧单
     */
    @PearlValue(key = "old.order.date.for.nan.fang.yi.ke", defaultValue = "2020-01-25 23:59:59")
    private String oldOrderDateForNanFangYiKe;

    @PearlValue(key = "wechat.order.detail.url")
    private String wxOrderDetailUrl;

    @Autowired
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private DangerousTagDOService dangerousTagDOServiceImpl;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Autowired
    private CategoryServiceClient categoryServiceClient;

    @Autowired
    private OrderPicMapper orderPicMapper;

    @Autowired
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Autowired
    private UserClient userClient;

    @Autowired
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private WaitingStatementService waitingStatementService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Autowired
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @Autowired
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Resource
    private RefOrderDetailTagDOMapper refOrderDetailTagDOMapper;

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Autowired
    private CooperationClient cooperationClient;

    @Autowired
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private InvoiceClient invoiceClient;

    @Autowired
    private SuppClient suppClient;

    @Autowired
    private TPIOrderClient tpiOrderClient;

    @Resource
    private ProductDescriptionSnapshotMapper productDescriptionSnapshotMapper;

    @Resource
    private ProductClient productClient;

    @Resource
    private OrderExportClient orderExportClient;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderManageRpcServiceImpl orderManageRpcService;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private BizBaseService bizBaseService;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private TimeoutStatisticsWithBalanceImpl timeoutStatisticsWithBalance;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Resource
    private CategoryDirectoryClient categoryDirectoryClient;

    @Resource
    private OrderRiskVerifiedServiceClient orderRiskVerifiedServiceClient;

    @Resource
    private OrderBankSnapshotClient orderBankSnapshotClient;

    @Resource
    private OldDateService oldDateService;

    @Resource
    private OrderContractClient orderContractClient;

    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrderMaterialCodeRpcClient orderMaterialCodeRpcClient;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private OrderDockingNumberRpcClient orderDockingNumberRpcClient;

    @Resource
    private SysuClient sysuClient;

    @Resource
    private FetchOrderDockingDataServiceClient fetchOrderDockingDataServiceClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    /**
     * 获取订单状态数量 和待入库数量
     * @param userId
     * @param orgId
     * @return
     */
    @Override
    public OrderStatusCountVO getOrderCountByStatus(Integer userId, Integer orgId) {
        OrderStatusCountVO orderStatusCountVO = new OrderStatusCountVO();

        //统计 待发货、待收货、待结算的订单数量
        OrderStatisticsParamDTO orderStatisticsParamDTO = new OrderStatisticsParamDTO();
        orderStatisticsParamDTO.setBuyerIds(New.list(userId));
        orderStatisticsParamDTO.setOrgIds(New.list(orgId));
        Map<Integer, Integer> orderByStatus = orderSearchBoostService.countOrderByStatus(orderStatisticsParamDTO);
        Integer waitingDeliveryCount = orderByStatus.get(OrderStatusEnum.WaitingForDelivery.value) == null ? 0 : orderByStatus.get(OrderStatusEnum.WaitingForDelivery.value);
        orderStatusCountVO.setWaiteDeliverCount(waitingDeliveryCount);
        Integer waitingReceiveCount = orderByStatus.get(OrderStatusEnum.WaitingForReceive.value) == null ? 0 : orderByStatus.get(OrderStatusEnum.WaitingForReceive.value);
        orderStatusCountVO.setWaiteReceiveCount(waitingReceiveCount);
        Integer waitStatement1 = orderByStatus.get(OrderStatusEnum.WaitingForStatement_1.value) == null ? 0 : orderByStatus.get(OrderStatusEnum.WaitingForStatement_1.value);
        orderStatusCountVO.setWaiteStatementCount(waitStatement1);
        Integer waitingForConfirm = orderByStatus.get(OrderStatusEnum.WaitingForConfirm.value) == null ? 0 : orderByStatus.get(OrderStatusEnum.WaitingForConfirm.value);
        orderStatusCountVO.setWaitingForConfirmCount(waitingForConfirm);
        Integer statementingCount = orderByStatus.get(OrderStatusEnum.Statementing_1.value) == null ? 0 : orderByStatus.get(OrderStatusEnum.Statementing_1.value);
        orderStatusCountVO.setStatementingCount(statementingCount);

        //启用新库房的单位，统计待入库的单位
        OrganizationDTO organizationDTO = userClient.findSimpleOrgInfoByIdFromEnum(orgId);
        String useWareHouse = sysConfigClient.getConfigByOrgCodeAndConfigCode(organizationDTO.getCode(), "USE_WAREHOUSE_SYSTEM");
        //1表示启用库房
        String useWareHouseFlag = "1";
        if (useWareHouseFlag.equals(useWareHouse)) {
            OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
            orderSearchParamDTO.setBuyerIdList(New.list(userId));
            orderSearchParamDTO.setOrgIdList(New.list(orgId));
            orderSearchParamDTO.setInventoryStatusList(New.list(InventoryStatusEnum.WAITING_FOR_STORE.getCode()));
            orderSearchParamDTO.setStatusList(New.list(OrderStatusEnum.Statementing_1.value, OrderStatusEnum.WaitingForStatement_1.value));
            orderSearchParamDTO.setPageSize(0);
            SearchPageResultDTO<OrderMasterSearchDTO> result = orderSearchBoostService.commonSearch(orderSearchParamDTO);
            orderStatusCountVO.setWaiteInboundCount(result.getTotalHits().intValue());
        }
        return orderStatusCountVO;
    }

    /**
     *
     * @param userId  采购人id
     * @param orgId     机构id
     * @return workbenchOrderAmountVO.DayAmount  今日订单金额
     *          workbenchOrderAmountVO.mouthAmount  本月订单金额
     */
    @Override
    public WorkbenchOrderAmountVO getMyOrderAmount(Integer userId, Integer orgId){
        //今日订单金额
        String startTime = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, TimeUtil.getTodayMinTime());
        String endTime = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, TimeUtil.getTodayMaxTime());
        StatisticsManagerParamDTO statisticsManagerParamDTO = new StatisticsManagerParamDTO();
        statisticsManagerParamDTO.setStartTime(startTime);
        statisticsManagerParamDTO.setEndTime(endTime);
        statisticsManagerParamDTO.setBuyerId(userId);
        statisticsManagerParamDTO.setOrgId(orgId);
        statisticsManagerParamDTO.setNotStatusList(Arrays.asList(OrderStatusEnum.Close.getValue(), OrderStatusEnum.PurchaseApplyToCancel.getValue(), OrderStatusEnum.SupplierApplyToCancel.getValue(), OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        Double todayAmount  = orderSearchBoostService.countOrderTotalAmount(statisticsManagerParamDTO);
        //本月订单金额
        String firstDayMouth = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, TimeUtil.getFirstDayMinOfMouth());
        statisticsManagerParamDTO.setStartTime(firstDayMouth);
        Double mouthAmount = orderSearchBoostService.countOrderTotalAmount(statisticsManagerParamDTO);
        WorkbenchOrderAmountVO workbenchOrderAmountVO = new WorkbenchOrderAmountVO();
        workbenchOrderAmountVO.setDayAmount(todayAmount);
        workbenchOrderAmountVO.setMonthAmount(mouthAmount);
        return workbenchOrderAmountVO;
    }

    /**
     * 导出订单交易金额统计
     * @param departmentIds
     * @param userId
     * @param orgId
     * @param request
     * @return
     * @throws IOException
     */
    @Override
    public String exportOrderTransaction(List<Integer> departmentIds, Integer userId, Integer orgId,
                                         TransactionCountRequest request) {

        List<OrderTransactionCountVO> transactionList = this.getOrderTransaction(departmentIds, userId, orgId, request);
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(transactionList), ExecptionMessageEnum.NO_DATA_TO_EXPORT);
        String absolutePath;
        String fileName = "订单交易金额统计" + System.currentTimeMillis() +  ExcelTypeEnum.XLSX.getValue() ;
        File file = new File(fileName);
        try {
            //这里指定需要表头，因为model通常包含表信头息
            ExcelWriter writer= EasyExcel.write(file).build();
            WriteSheet sheet = EasyExcel.writerSheet("订单交易统计")
                    .head(OrderTransactionCountVO.class)
                    .build();
            writer.write(transactionList, sheet);
            writer.finish();
            FileUploadClient fileUploadClient = UploadFileClient.getFileUploadClientById(UploadFileClient.ORDER_STATIC_ID);
            FileUploadResp fileUploadResp = fileUploadClient.uploadFile(file);
            Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
            absolutePath = fileUploadResp.getAbsolutePath();
            if (Objects.nonNull(file)) {
                file.delete();
            }
        } catch (Exception e) {
            Cat.logError("OrderManageServiceImpl","exportOrderTransactionStatic","生成excelIO异常",e);
            logger.error("IO异常！", e);
            throw e;
        }
        LoginUserInfoBO user = (LoginUserInfoBO)RpcContext.getProviderContext().getCallAttachment("user");
        if (user != null) {
            OrderExportDTO orderExportDTO = new OrderExportDTO();
            orderExportDTO.setFileName(fileName);
            orderExportDTO.setExportDate(new Date());
            orderExportDTO.setOrgId(orgId);
            orderExportDTO.setOrgCode(user.getOrgCode());
            orderExportDTO.setUserId(user.getUserId());
            orderExportDTO.setUserName(user.getUserName());
            orderExportDTO.setStatus(OrderExportStatusEnum.EXPORT_SUCCESS.value);
            orderExportDTO.setFileType(ExportFileTypeEnum.BUYER_ORDER_STATISTICS.value);
            orderExportDTO.setFileUrl(absolutePath);
            orderExportClient.saveOrderExport(orderExportDTO);
        }
        return absolutePath;
    }

    /**
     *
     * @param departmentIds  部门id
     * @param userId  用户id
     * @param orgId  组织id
     * @param request 入参
     * @return
     */
    @Override
    public List<OrderTransactionCountVO> getOrderTransaction(List<Integer> departmentIds, Integer userId, Integer orgId,
                                                             TransactionCountRequest request){
        Date startTime = request.getStartTime();
        Preconditions.notNull(startTime,"起始时间不能为空~！");
        Date endTime = request.getEndTime();
        Preconditions.notNull(endTime,"结束时间不能为空~！");
        StatisticsManagerParamDTO statisticsManagerParamDTO = new StatisticsManagerParamDTO();
        statisticsManagerParamDTO.setStartTime(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,startTime));
        statisticsManagerParamDTO.setEndTime(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,endTime));
        statisticsManagerParamDTO.setDepartmentIds(departmentIds);
        statisticsManagerParamDTO.setOrgId(orgId);
        statisticsManagerParamDTO.setBuyerId(userId);
        statisticsManagerParamDTO.setNotStatusList(Arrays.asList(OrderStatusEnum.Close.getValue(), OrderStatusEnum.PurchaseApplyToCancel.getValue(), OrderStatusEnum.SupplierApplyToCancel.getValue(), OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        statisticsManagerParamDTO.setSortOrderEnum(SortOrderEnum.ASC);
        IntervalDTO dayIntervalDTO = new IntervalDTO();
        dayIntervalDTO.setDateUnit(DateUnitEnum.DAY);
        statisticsManagerParamDTO.setIntervalDate(dayIntervalDTO);
        List<OrderDateHistogramResultDTO> orderDateHistogramResultDTOS = orderSearchBoostService.aggOrderAmountDateHistogram(statisticsManagerParamDTO);
        List<OrderTransactionCountVO> transactionList = orderDateHistogramResultDTOS.stream().map(this::createOrderTransactionCountVO).collect(toList());
        return transactionList;
    }

    private OrderTransactionCountVO createOrderTransactionCountVO(OrderDateHistogramResultDTO orderDateHistogram){
        OrderTransactionCountVO orderTransactionCountVO = new OrderTransactionCountVO();
        orderTransactionCountVO.setTransactionDate(orderDateHistogram.getDateString());
        orderTransactionCountVO.setCount(orderDateHistogram.getCount().intValue());
        orderTransactionCountVO.setAmount(new BigDecimal(orderDateHistogram.getAmount()).setScale(2, RoundingMode.HALF_UP));
        return orderTransactionCountVO;
    }

    /**
     * 订单列表--评价--订单信息加载
     * @param orderNo
     * @param userId
     * @return
     */
    @Override
    public OrderForCommentVO getOrderForComment(String orderNo,Integer userId){
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setOrderNo(orderNo);
        SearchPageResultDTO<OrderMasterSearchDTO> orderEs = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> recordList = orderEs.getRecordList();
        Preconditions.isTrue(CollectionUtils.isNotEmpty(recordList),"查不到订单信息~！");
        List<Integer> orderIdList = orderEs.getRecordList().stream().map(OrderMasterSearchDTO::getId).collect(toList());

        final OrderMasterSearchDTO orderMasterSearchDTO = recordList.get(0);
        BusinessErrUtil.isTrue(userId.equals(orderMasterSearchDTO.getFbuyerid()), ExecptionMessageEnum.NO_PERMISSION_TO_EVALUATE_ORDER);
        OrderForCommentVO orderForCommentVO = new OrderForCommentVO();
        orderForCommentVO.setOrderNo(orderMasterSearchDTO.getForderno());
        orderForCommentVO.setOrderId(orderMasterSearchDTO.getId());
        orderForCommentVO.setOrderDate(orderMasterSearchDTO.getForderdate());
        List<OrderDetailSearchDTO> orderDetail = orderMasterSearchDTO.getOrderDetail();
        Map<Integer, List<OrderDetailExtraDTO>> orderIdDetailExtraMap = buyerOrderService.getOrderIdDetailExtraMap(orderIdList);
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderIdDetailExtraMap.values()
                .stream()
                .flatMap(List::stream)
                .collect(toList());
        Map<Integer, List<OrderDetailExtraDTO>> detailId2ExtraMap = DictionaryUtils.groupBy(orderDetailExtraDTOList, OrderDetailExtraDTO::getOrderDetailId);

        List<OrderDetailForCommentVO> detailList = orderDetail.stream().map(detail -> {
            OrderDetailForCommentVO orderDetailForCommentVO = new OrderDetailForCommentVO();
            OrderDetailVO orderDetailVO = new OrderDetailVO();
            orderDetailForCommentVO.setDetailId(detail.getDetailId());
            orderDetailForCommentVO.setProductName(detail.getFgoodname());
            orderDetailForCommentVO.setGoodsCode(detail.getFgoodcode());
            orderDetailForCommentVO.setSpecification(detail.getFspec());
            orderDetailForCommentVO.setProductPicUrl(detail.getPicPath());
            orderDetailForCommentVO.setSuppName(orderMasterSearchDTO.getFsuppname());
            orderDetailForCommentVO.setSuppId(orderMasterSearchDTO.getFsuppid());
            orderDetailForCommentVO.setProductId(detail.getProductId());
            orderDetailForCommentVO.setUnit(detail.getFunit());
            orderDetailForCommentVO.setFirstCategoryId(detail.getFirstCategoryId());
            orderDetailForCommentVO.setFirstCategoryName(detail.getFirstCategoryName());
            orderDetailForCommentVO.setCasNo(detail.getCasNo());
            orderDetailForCommentVO.setBrand(detail.getFbrand());
            orderDetailForCommentVO.setDangerousTag(DangerousTypeEnum.get(detail.getDangerousType()).getName());
            // 商品扩展信息
            List<OrderDetailExtraDTO> orderDetailExtraDTOS = detailId2ExtraMap.get(detail.getDetailId());
            com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO orderDetailDTO = new com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO();
            orderDetailDTO.setFirstCategoryId(detail.getFirstCategoryId());
            orderDetailDTO.setFspec(detail.getFspec());
            buyerOrderService.setOrderDetailDTOExtraInfo(orderDetailDTO, orderDetailExtraDTOS);
            orderDetailForCommentVO.setPackingSpec(orderDetailDTO.getPackingSpec());
            orderDetailForCommentVO.setModelNumber(orderDetailDTO.getModelNumber());
            orderDetailForCommentVO.setMedicalDeviceRegisCertNumber(orderDetailDTO.getMedicalDeviceRegisCertNumber());
            orderDetailForCommentVO.setCompletionCycle(orderDetailDTO.getCompletionCycle());
            orderDetailForCommentVO.setPress(orderDetailDTO.getPress());
            orderDetailForCommentVO.setPurity(orderDetailDTO.getPurity());
            orderDetailForCommentVO.setProductSpec(orderDetailDTO.getProductSpec());
            return orderDetailForCommentVO;
        }).collect(toList());
        orderForCommentVO.setOrderDetailList(detailList);
        return orderForCommentVO;
    }

    /**
     * 根据订单id 封装送货单信息
     *
     * @param orderId 订单id
     * @return DeliveryNoteDTO 送货单对象
     */
    @Override
    public DeliveryNoteDTO assembleDeliveryNote(Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        DeliveryNoteDTO deliveryNoteDTO = new DeliveryNoteDTO();
        deliveryNoteDTO.setOrderNo(orderMasterDO.getForderno());
        deliveryNoteDTO.setOrderDate(orderMasterDO.getForderdate());
        deliveryNoteDTO.setReceiverName(orderMasterDO.getFbuyercontactman());
        deliveryNoteDTO.setReceiverPhone(orderMasterDO.getFbuyertelephone());
        deliveryNoteDTO.setOldFlag(oldDateService.isOldDate(orderMasterDO.getFuserid(), orderMasterDO.getForderdate(), orderMasterDO.getFundStatus(), orderMasterDO.getSpecies().intValue()));
        // 坑，单据打印偏偏不需要显示待配送地址
        String receiverAddress = orderMasterDO.getFbiderdeliveryplace();
        if (receiverAddress.contains(deliveryProxy)) {
            OrderAddressDTO addressDTO = orderAddressRPCClient.findByOrderId(orderMasterDO.getId());
            if (addressDTO != null) {
                receiverAddress = StringUtils.defaultIfBlank(addressDTO.getProvince(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getCity(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getRegion(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getAddress(), StringUtils.EMPTY);
            }
        }
        deliveryNoteDTO.setReceiverAddress(receiverAddress);
        deliveryNoteDTO.setOrgName(orderMasterDO.getFusername());
        deliveryNoteDTO.setDepartmentName(orderMasterDO.getFbuydepartment());
        deliveryNoteDTO.setBuyerName(orderMasterDO.getFbuyername());
        deliveryNoteDTO.setSuppName(orderMasterDO.getFsuppname());
        deliveryNoteDTO.setFundStatus(orderMasterDO.getFundStatus());
        List<UserAccountDTO> userAccountDTOS = suppClient.querySuppAccountList(orderMasterDO.getFsuppid(), null);
        List<SupplierUserDTO> suppUserList = userAccountDTOS.stream()
                .filter(supp -> supp.getId().toString().equals(orderMasterDO.getFconfirmmanid()))
                .map(supp -> {
                    SupplierUserDTO suppUserDTO = new SupplierUserDTO();
                    suppUserDTO.setName(supp.getName());
                    suppUserDTO.setPhone(supp.getMobile());
                    return suppUserDTO;
                }).collect(toList());
        deliveryNoteDTO.setSuppUsers(suppUserList);
        List<OrderDetailDO> orderDetails = orderDetailMapper.findByFmasterid(orderId);
        BigDecimal sumQuantity = BigDecimal.ZERO;
        BigDecimal sumAmount= BigDecimal.ZERO;
        List<DeliveryNoteProductDTO> deliveryNoteProductList = New.listWithCapacity(orderDetails.size());
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(orderId);
        Map<Integer, BigDecimal> detailIdReturnCountMap = New.map();
        for(GoodsReturn goodsReturn : goodsReturnList){
            if(!GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus())){
                // 非完成退货的不计入退货
                continue;
            }
            GoodsReturnDTO goodsReturnDTO = GoodsReturnTranslator.doToDto(goodsReturn);
            for(GoodsReturnDetailDTO returnDetail : goodsReturnDTO.getGoodsReturnDetailDTOS()){
                BigDecimal returnCount = detailIdReturnCountMap.getOrDefault(returnDetail.getDetailId(), BigDecimal.ZERO);
                detailIdReturnCountMap.put(returnDetail.getDetailId(), returnCount.add(new BigDecimal(returnDetail.getQuantity())));
            }
        }
        for (OrderDetailDO orderDetail : orderDetails) {
            BigDecimal fquantity = orderDetail.getFquantity();
            BigDecimal fcancelquantity = detailIdReturnCountMap.getOrDefault(orderDetail.getId(), BigDecimal.ZERO);
            BigDecimal actualQuantity  = fquantity.subtract(fcancelquantity);
            // 如果全部退货不记录
            if (actualQuantity.compareTo(BigDecimal.ONE) < 0) {
                continue;
            }
            orderDetail.setFquantity(actualQuantity);
            sumQuantity = sumQuantity.add(actualQuantity);
            BigDecimal fbidprice = orderDetail.getFbidprice();
            BigDecimal actualAmount = actualQuantity.multiply(fbidprice)
                    .add(orderDetail.getRemainderPrice() == null ? BigDecimal.ZERO : orderDetail.getRemainderPrice());
            orderDetail.setFbidamount(actualAmount);
            sumAmount = sumAmount.add(actualAmount);
            deliveryNoteProductList.add(this.createDeliveryNoteProduct(orderDetail));
        }
        deliveryNoteDTO.setProductsInfoList(deliveryNoteProductList);
        deliveryNoteDTO.setSumAmount(String.format("%.2f", sumAmount));
        deliveryNoteDTO.setSumQuantity(String.format("%.2f", sumQuantity));
        String url = String.format(wxOrderDetailUrl, orderMasterDO.getId());
        deliveryNoteDTO.setOrderNoQrCode(QrCodeUtil.getBase64(url,105,105));
        // 线下单需要额外返回采购平台
        OrderOfflineInfoVO offlineInfo = orderDetailRelatedService.getOfflineInfo(orderMasterDO);
        if (offlineInfo != null) {
            deliveryNoteDTO.setProcurementChannel(offlineInfo.getProcurementChannel());
        }
        List<InvoiceDTO> invoiceList = invoiceClient.findInvoiceList(Arrays.asList(orderId), orderMasterDO.getFuserid());
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<InvoicePrintDTO> invoicePrintDTOList = invoiceList.stream().map(item -> {
                InvoicePrintDTO printDTO = new InvoicePrintDTO();
                printDTO.setInvoiceNo(item.getInvoiceNo());
                return printDTO;
            }).collect(toList());
            deliveryNoteDTO.setInvoicePrintList(invoicePrintDTOList);
        }
        // 增加n级审批人，采购或者竞价
        this.setApproveInfo(orderMasterDO, deliveryNoteDTO);
        // 增加买家留言
        deliveryNoteDTO.setOrderRemark(orderDetailRelatedService.getOrderRemark(orderMasterDO.getFtbuyappid(), orderMasterDO.getFsuppid()));
        return deliveryNoteDTO;
    }

    /**
     * 增加n级审批人，采购或者竞价
     * @param orderMasterDO
     * @param deliveryNoteDTO
     */
    private void setApproveInfo(OrderMasterDO orderMasterDO, DeliveryNoteDTO deliveryNoteDTO) {
        List<OrderPurchaseApprovalLogDTO> orderPurchaseApproveLogList = orderManageRpcService.getApproveLogInfo(orderMasterDO);
        if (CollectionUtils.isNotEmpty(orderPurchaseApproveLogList)) {
            List<OrderPurchaseApprovalLogPrintDTO> purchaseApprovalLogPrintList = New.listWithCapacity(orderPurchaseApproveLogList.size());
            for (OrderPurchaseApprovalLogDTO log : orderPurchaseApproveLogList) {
                OrderPurchaseApprovalLogPrintDTO printDTO = new OrderPurchaseApprovalLogPrintDTO();
                printDTO.setDate(log.getDate());
                printDTO.setApprover(log.getApprover());
                printDTO.setOperate(log.getOperate());
                printDTO.setOperateComment(log.getOperateComment());
                printDTO.setApproveLevel(log.getApproveLevel());
                printDTO.setResult(log.getResult());
                purchaseApprovalLogPrintList.add(printDTO);
            }
            deliveryNoteDTO.setOrderPurchaseApprovalLogPrintList(purchaseApprovalLogPrintList);
        }
    }

    /**
     * 订单详情 生成 送货单商品详情信息
     * @param orderDetail
     * @return
     */
    private DeliveryNoteProductDTO createDeliveryNoteProduct(OrderDetailDO orderDetail) {
        DeliveryNoteProductDTO deliveryNoteProductDTO = new DeliveryNoteProductDTO();
        deliveryNoteProductDTO.setGoodCode(orderDetail.getFgoodcode());
        deliveryNoteProductDTO.setGoodName(orderDetail.getFgoodname());
        deliveryNoteProductDTO.setBrandName(orderDetail.getFbrand());
        deliveryNoteProductDTO.setSpec(orderDetail.getFspec());
        deliveryNoteProductDTO.setUnit(orderDetail.getFunit());
        deliveryNoteProductDTO.setQuantity(String.format("%.2f", orderDetail.getFquantity()));
        deliveryNoteProductDTO.setPerPrice(String.format("%.2f", orderDetail.getFbidprice()));
        deliveryNoteProductDTO.setTotalAmount(String.format("%.2f", orderDetail.getFbidamount()));
        deliveryNoteProductDTO.setRemainderPrice(orderDetail.getRemainderPrice());
        return deliveryNoteProductDTO;
    }

    /**
     * 订单解冻经费卡
     * @param orderId
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "根据订单id解冻经费卡")
    public void orderFundCardUnFreezeById(Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        this.orderFundCardUnFreeze(orderMasterDO);
    }

    @Override
    public void orderFundCardUnFreeze(OrderUnFreezeRequestDTO orderUnFreezeRequestDTO) {
        logger.info("类{}方法{},订单{}调用经费解冻,参数{}",this.getClass().getName(),"orderFundCardUnFreeze",orderUnFreezeRequestDTO.getOrderId(),JsonUtils.toJsonIgnoreNull(orderUnFreezeRequestDTO));
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderUnFreezeRequestDTO.getOrderId());
        orderFundCardService.orderFundCardUnFreeze(orderMasterDO, orderUnFreezeRequestDTO);
    }

    /**
     * 订单解冻经费卡
     * @param orderMasterDO
     */
    @Override
    public void orderFundCardUnFreeze(OrderMasterDO orderMasterDO) {
        orderFundCardService.orderFundCardUnFreeze(orderMasterDO, null);
    }

    /**
     * 批量释放经费
     *    只保证调解冻接口成功，部分单位解冻结果依赖回调结果
     * @param userInfo 用户信息
     * @param idList 订单id
     */
    @Override
    public void batchUnfreezeFund(LoginUserInfoBO userInfo, List<Integer> idList) {
        Preconditions.notNull(userInfo, "用户信息为空！");
        BusinessErrUtil.isTrue(idList.size() > 0 && idList.size() <= 10, ExecptionMessageEnum.BATCH_RELEASE_FUNDS_1_TO_10);
        // 1.判断是否有权限
        Integer orgId = userInfo.getOrgId();
        Integer userId = userInfo.getUserId();
        List<Integer> deptIdList = userInfo.getDeptIdList();
        List<DepartmentDTO> hasAccessDepartment = userClient.getDeptForUserByAccess(userId, orgId, ConfigConstant.BATCH_UNFREEZING_FUNDS);
        Set<Integer> hasAccessId = hasAccessDepartment.stream().map(DepartmentDTO::getId).collect(Collectors.toSet());
        try {
            // 避免重复操作
            cacheClient.controlRepeatOperation(ConfigConstant.BATCH_UNFREEZING_FUNDS + userId,5, "您的操作过于频繁，请5s后再试");
            // 退货金额
            BigDecimal orderReturnAmount = null;
            // 订单总价格
            BigDecimal orderAmountTotal = null;
            // 获取所有订单(订单关闭且冻结失败)：在冻结前整单退货和取消订单都已更新订单状态为关闭
            List<OrderMasterDO> orderMasterDOS = Optional.ofNullable(orderMasterMapper.findByDeptIdAndIdIn(deptIdList, idList)).orElse(New.list()).stream()
                    .filter(orderMaster -> OrderStatusEnum.Close.getValue().equals(orderMaster.getStatus())
                            && OrderFundStatusEnum.ThrawFailed.getValue().equals(orderMaster.getFundStatus())).collect(toList());
            orderMasterDOS.forEach(o -> {
                BusinessErrUtil.isTrue(hasAccessId.contains(o.getFbuydepartmentid()), ExecptionMessageEnum.NO_UNFREEZE_PERMISSION_FOR_ORDER, o.getFbuydepartment(), o.getForderno());
            });
            // 目前只有取消订单或者是退货才会释放经费；部分退货不会更新经费状态，所以部分退货不存在解冻失败的情况，只考虑整单退货
            // 批量解冻接口只适用于同一采购单下或竞价单下的订单，不支持不同采购单或竞价单下的订单，所以依次进行解冻，一般来说解冻失败的情况并不多
            for (OrderMasterDO orderMasterDO : orderMasterDOS) {
                orderReturnAmount = Objects.nonNull(orderMasterDO.getReturnAmount()) ? BigDecimal.valueOf(orderMasterDO.getReturnAmount()) : BigDecimal.ZERO;
                orderAmountTotal = orderMasterDO.getForderamounttotal();
                // 整单退货导致的解冻失败: 退货金额等于订单总价格
                if(orderReturnAmount.compareTo(orderAmountTotal) == 0){
                    OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
                    orderUnFreezeRequestDTO.setOrderId(orderMasterDO.getId());
                    orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN);
                    orderUnFreezeRequestDTO.setFreezeAmount(orderAmountTotal);
                    this.orderFundCardUnFreeze(orderUnFreezeRequestDTO);
                }else if(StringUtils.isNotEmpty(orderMasterDO.getFcancelmanid())){
                    // 取消订单导致的解冻失败
                    this.orderFundCardUnFreeze(orderMasterDO);
                }else{
                    BusinessErrUtil.isTrue(false, ExecptionMessageEnum.ORDER_UNFREEZE_FAILED_NOT_FULL_RETURN, orderMasterDO.getForderno());
                }
            }
        } catch (Exception e) {
            throw e;
        } finally {
            // 操作完成，删除缓存
            cacheClient.removeCache(ConfigConstant.BATCH_UNFREEZING_FUNDS + userId);
        }
    }

    /**
     * 条件批量查询最新一个物资编码
     *
     * @param requestDTO
     * @return
     */
    @Override
    public List<OrderMaterialCodeDTO> latestMaterialCode(OrderMaterialCodeRequestDTO requestDTO) {
        return orderMaterialCodeRpcClient.queryByParam(requestDTO.getRequestList());
    }

    /**
     * 更新订单物资编码
     *
     * @param orderMaterialCodeDTO
     * @return
     */
    @Override
    public Integer updateOrderMaterialCode(OrderMaterialCodeDTO orderMaterialCodeDTO) {
        return orderMaterialCodeRpcClient.update(orderMaterialCodeDTO);
    }

    /**
     * 新增订单物资编码
     *
     * @param orderMaterialCodeDTOList
     * @return
     */
    @Override
    public Integer insertMaterialCode(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList) {
        return orderMaterialCodeRpcClient.insert(orderMaterialCodeDTOList);
    }

    @Override
    public RemoteResponse<Boolean> isServiceType(OrderBasicParamDTO param) {
        // 检测商品类型 rpc 接口
        OrderDetailReq req = new OrderDetailReq();
        req.setOrderMasterId(param.getOrderId());
        BusinessErrUtil.notNull(req.getOrderMasterId(), "参数不可为空！");
        RemoteResponse<List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO>> response = orderDetailService.findOrderDetailsByMasterId(
                req);
        if (response == null || !response.isSuccess()) {
            return RemoteResponse.<Boolean>custom().setFailure("查询订单详情异常！").build();
        }
        List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO> orderDetailDTOS = response.getData();
        Preconditions.notEmpty(orderDetailDTOS, "不存在订单数据！");
        // 匹配非服务类型
        com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO orderDetailDTO = orderDetailDTOS
                .stream()
                .filter(orderDetail -> !orderDetail.getCategoryTag().equals(InboundTypeEnum.SERVICE.getDesc()))
                .findFirst()
                .orElse(null);
        if (orderDetailDTO != null) {
            return RemoteResponse.<Boolean>custom().setSuccess().setData(Boolean.FALSE).build();
        }
        // 匹配服务类型
        orderDetailDTO = orderDetailDTOS
                .stream()
                .filter(orderDetail -> orderDetail.getCategoryTag().equals(InboundTypeEnum.SERVICE.getDesc() + ""))
                .findFirst()
                .orElse(null);

        if (orderDetailDTO != null) {
            return RemoteResponse.<Boolean>custom().setSuccess().setData(Boolean.TRUE).build();
        }

        return RemoteResponse.<Boolean>custom().setFailure("未知订单商品分类异常！").build();
    }

    /**
     * 江西肿瘤 验收发送信息
     * @param orderId 订单Id
     */
    @Override
    public void sendReceiptMessageForJiangZhong(Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(orderId);
        this.sendReceiptMessageForJiangZhong(orderMasterDO,orderDetailList);
    }

    private  void sendReceiptMessageForJiangZhong(OrderMasterDO orderMasterDO,List<OrderDetailDO> orderDetailList){
        List<String> goodNameList = orderDetailList.stream().map(OrderDetailDO::getFgoodname).collect(toList());
        String goodNames = StringUtils.join(goodNameList, ",");
        orderEmailHandler.sendEmailToPurchaserForJiangZhong(orderMasterDO);
        try {
            orderEmailHandler.sendWeChatToPurchaserForJiangZhong(orderMasterDO,goodNames);
        } catch (CallRpcException e) {
            logger.error("获取openId异常！",e);
            Cat.logError(CAT_TYPE,"sendWeCatToPurchaserForJiangZhong","获取openId异常！",e);
        }
    }

    /**
     * 提交订单验收审批
     * @param orderApprovalParamDTO
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void submitOrderApproval(OrderApprovalParamDTO orderApprovalParamDTO){
        Integer orderId = orderApprovalParamDTO.getOrderId();
        boolean isAutoApproval = ApprovalModeEnum.TIMEOUT_AUTO_APPROVAL.getCode().equals(orderApprovalParamDTO.getApprovalMode());

        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        Assert.notNull(orderMasterDO,"无对应订单信息！");
        Integer status = orderMasterDO.getStatus();
        BusinessErrUtil.isTrue(OrderStatusEnum.OrderReceiveApproval.value.equals(status), ExecptionMessageEnum.ORDER_NOT_PENDING_APPROVAL_NO_OPERATION);

        // 自动验收审批，但是此时用户已经手动审过了，则不做任何操作(陆军医在用，定时任务会判断3天内没有进行过审批则进行自动审批)
        if (isAutoApproval && (OrderStatusEnum.WaitingForStatement_1.value.equals(status) || OrderStatusEnum.Statementing_1.value.equals(status))) {
            return;
        }

        if (!isAutoApproval) {
            //校验用户是否有验收权限
            Boolean authentication = userClient.findUserHasAccess(orderMasterDO.getFuserid(),orderApprovalParamDTO.getUserId(),orderMasterDO.getFbuydepartmentid(),"ORDER_APPROVE");
            BusinessErrUtil.isTrue(authentication, ExecptionMessageEnum.NO_ACCEPTANCE_APPROVAL_AUTHORITY);
        }

        // 获取电子签名用的参数，校验并保存电子签名
        ElectronicSignBO electronicSignBO = new ElectronicSignBO();
        electronicSignBO.setOrgCode(orderApprovalParamDTO.getOrgCode());
        electronicSignBO.setUserGuid(orderApprovalParamDTO.getUserGuid());
        electronicSignBO.setOrderId(orderMasterDO.getId());
        electronicSignBO.setUserName(orderApprovalParamDTO.getUserName());
        electronicSignBO.setPassword(orderApprovalParamDTO.getPassword());
        electronicSignBO.setDepartmentId(orderMasterDO.getFbuydepartmentid());
        electronicSignBO.setOperation(ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL);
        bizBaseService.checkAndSaveElectronicSign(electronicSignBO);

        Integer operateType = orderApprovalParamDTO.getOperateType();
        //验收审批通过 || 超时验收审批通过
        if (OrderApprovalEnum.PASS.getValue().equals(operateType) || OrderApprovalEnum.TIMEOUT_AUTO_APPROVAL_ORDER.getValue().equals(operateType)) {
            //退货中的 退货状态 的商品名称
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
            String goodNames = this.getReturningGoodName(orderDetailDOList);
            boolean noneReturn = StringUtils.isEmpty(goodNames);
            if (!noneReturn && isAutoApproval) {
                orderApprovalLogService.createOrderOperateLog(orderId, OrderApprovalEnum.TIMEOUT_AUTO_APPROVAL_ORDER_FAILURE.getValue(), orderApprovalParamDTO.getUserId(), null, orderApprovalParamDTO.getReason());
            }
            BusinessErrUtil.isTrue(noneReturn, ExecptionMessageEnum.RETURN_PENDING_GOODS, goodNames);
            List<Integer> integers = New.list(orderId);
            // 经费卡id  根据订单id查出对应的经费卡
            List<OrderFundCardDTO> orderFundCardCacheList = orderFundCardCacheClient.findOrderFundCardCache(integers);
            // 有无换过卡
            boolean cardNoChange = true;
            OrderFundCardDTO fundCardCache = null;

            if (CollectionUtils.isNotEmpty(orderFundCardCacheList)) {
                //换卡需要调用，换卡接口，成功回调后再执行审批通过, 为什么是get(0)呢？因为现在的业务只支持选单张卡
                fundCardCache = orderFundCardCacheList.get(0);
                List<RefFundcardOrderDO> oldFundCard = refFundcardOrderMapper.findByOrderId(orderId.toString());

                BusinessErrUtil.notEmpty(oldFundCard, ExecptionMessageEnum.ACCEPTANCE_FAILED_NO_CARD_INFO);
                // 新卡和旧卡其实是不同的卡，有发生过换卡操作
                if (!fundCardCache.getFundCardId().equals(RefFundcardOrderTranslator.getLastLevelCardId(oldFundCard.get(0)))) {
                    cardNoChange = false;
                }
            }

            if (cardNoChange) {
                //没有换卡记录直接审批通过
                acceptanceApprovalSuccess(orderApprovalParamDTO,orderMasterDO);
            } else {
                BigDecimal freezeAmount = orderMasterDO.getForderamounttotal().subtract(new BigDecimal(orderMasterDO.getReturnAmount()));
                FundCardDTO fundCardByCardId = researchFundCardServiceClient.getFundCardByCardId(fundCardCache.getFundCardId(), orderApprovalParamDTO.getOrgCode());
                researchFundCardServiceClient.checkFundCardSufficient(fundCardByCardId, freezeAmount);
                this.changeFundCard(orderApprovalParamDTO,orderMasterDO,orderFundCardCacheList);
            }
        } else {
            //验收驳回
            this.acceptanceApprovalRejection(orderApprovalParamDTO);
        }
    }

    /**
     * 多级验收审批通过， 完成验收审批
     *
     * @param orderApprovalParamDTO 验收审批信息
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE,description = "多级验收审批完成，处理验收审批通过逻辑")
    public void acceptApproveFlowPass(OrderApprovalParamDTO orderApprovalParamDTO) {
        Integer orderId = orderApprovalParamDTO.getOrderId();
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.isTrue(OrderStatusEnum.OrderReceiveApproval.value.equals(orderMasterDO.getStatus()), ExecptionMessageEnum.ORDER_NOT_PENDING_APPROVAL_NO_OPERATION);
        // 经费卡id  根据订单id查出对应的经费卡
        List<OrderFundCardDTO> orderFundCardCacheList = orderFundCardCacheClient.findOrderFundCardCache(New.list(orderId));
        // 有无换过卡
        boolean cardNoChange = true;
        OrderFundCardDTO fundCardCache = null;

        if (CollectionUtils.isNotEmpty(orderFundCardCacheList)) {
            //换卡需要调用，换卡接口，成功回调后再执行审批通过, 为什么是get(0)呢？因为现在的业务只支持选单张卡
            fundCardCache = orderFundCardCacheList.get(0);
            List<RefFundcardOrderDO> oldFundCard = refFundcardOrderMapper.findByOrderId(orderId.toString());

            BusinessErrUtil.notEmpty(oldFundCard, ExecptionMessageEnum.PROCESS_APPROVAL_LOGIC_FAILED_NO_CARD, orderId);
            // 新卡和旧卡其实是不同的卡，有发生过换卡操作
            if (!fundCardCache.getFundCardId().equals(RefFundcardOrderTranslator.getLastLevelCardId(oldFundCard.get(0)))) {
                cardNoChange = false;
            }
        }
        if (cardNoChange) {
            //没有换卡记录直接审批通过
            acceptanceApprovalSuccess(orderApprovalParamDTO,orderMasterDO);
        } else {
            BigDecimal freezeAmount = orderMasterDO.getForderamounttotal().subtract(BigDecimal.valueOf(orderMasterDO.getReturnAmount()));
            FundCardDTO fundCardByCardId = researchFundCardServiceClient.getFundCardByCardId(fundCardCache.getFundCardId(), orderApprovalParamDTO.getOrgCode());
            researchFundCardServiceClient.checkFundCardSufficient(fundCardByCardId, freezeAmount);
            this.changeFundCard(orderApprovalParamDTO,orderMasterDO,orderFundCardCacheList);
        }
    }

    /**
     * 换卡成功 回调 方法实现
     * @param callbackRequest
     */
    @Override
    public void changeFundCardCallBack(CallbackRequest<ChangeFundCardCallbackResult> callbackRequest) {
        ChangeFundCardCallbackResult callbackResult = callbackRequest.getData();
        Assert.isTrue(callbackResult != null, "换卡回调失败，回调结果为空！");
        String orderNo = callbackResult.getSerialNumber();
        String orgCode = callbackRequest.getOrgCode();
        Integer status = callbackResult.getStatus();
        // 换卡的的订单流水号（订单号）
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        Assert.isTrue(orderMasterDO != null, "换卡回调失败，查询订单失败！");
        // 回传的拓展字段Map
        Map<String, String> extraDTOMap = New.map();
        if (CollectionUtils.isNotEmpty(callbackResult.getExtraDTOs() )) {
            extraDTOMap = callbackResult.getExtraDTOs().stream().
                    filter(extraDTO -> Objects.nonNull(extraDTO) && Objects.nonNull(extraDTO.getField())).
                    collect(Collectors.toMap(ExtraDTO::getField, ExtraDTO::getValue, (v1, v2) -> v1));
        }


        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderMasterDO.getId());
        boolean isReceiveApproval = OrderStatusEnum.OrderReceiveApproval.getValue().equals(orderMasterDO.getStatus());


        List<RefFundcardOrderDO> oldCardList = refFundcardOrderMapper.findByOrderId(orderMasterDO.getId().toString());
        // 新卡dto
        List<RefFundcardOrderDTO> newCardDTOList;
        // 新卡do
        List<RefFundcardOrderDO> newCardDOList;
        Integer userId = null;
        // 新卡卡号
        String newCardNos;
        // 旧卡卡号
        String oldCardNos = oldCardList.stream().map(RefFundcardOrderDO::getCardNo).collect(Collectors.joining("，"));
        // 换卡原因
        String changeReason = extraDTOMap.get("changeReason");
        // 日志枚举
        OrderApprovalEnum successLogEnum= isReceiveApproval? OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_SUCCESS : OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS;
        OrderApprovalEnum failureLogEnum= isReceiveApproval? OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_FAILURE : OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_FAILURE;

        // 1.获取新卡数据
        if (isReceiveApproval) {
            // 验收审批环节换卡，数据会先存到临时表。新卡即临时表的卡数据
            List<OrderFundCardDTO> fundCardCache = orderFundCardCacheClient.findOrderFundCardCache(New.list(orderMasterDO.getId()));
            // 没有换卡缓存，无需同步经费卡关联数据
            BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(fundCardCache), ExecptionMessageEnum.UNABLE_TO_RETRIEVE_CARD_REPLACEMENT);
            newCardDOList = fundCardCache.stream().map(RefFundcardOrderTranslator::fundCardDTO2RefDO).collect(toList());
            newCardDTOList = newCardDOList.stream().map(RefFundcardOrderTranslator::do2Dto).collect(toList());
            newCardNos = newCardDTOList.stream().map(RefFundcardOrderDTO::getCardNo).collect(Collectors.joining("，"));
        }else{
            // 非验收审批环节换卡，数据由我们传过去再由TPI返回来。新卡即我们传给TPI的EXTRADTO的数据
            Optional<ExtraDTO> newCardsOptional = callbackResult.getExtraDTOs().stream().filter(item->"newCards".equals(item.getField())).findFirst();
            Preconditions.isTrue(newCardsOptional.isPresent(), "新卡数据为空！");
            JSONArray jsonArray = JSON.parseArray(newCardsOptional.get().getValue());
            newCardDTOList = jsonArray.stream().map(json->JsonUtils.fromJson(json.toString(), RefFundcardOrderDTO.class)).collect(toList());
            newCardDOList = newCardDTOList.stream().map(RefFundcardOrderTranslator::dto2DO).collect(toList());
            newCardNos = newCardDTOList.stream().map(RefFundcardOrderDTO::getCardNo).collect(Collectors.joining("，"));
        }

        // 设置新卡uuid
        newCardDOList = newCardDOList.stream()
                .peek(refFundcardOrderDO -> {
                            if (StringUtils.isEmpty(refFundcardOrderDO.getId())) {
                                refFundcardOrderDO.setId(String.valueOf(UUID.randomUUID()));
                            }
                        }
                ).collect(toList());

        // 2.换卡后处理
        if (callbackRequest.isSuccess() && ChangeFundCardStatusEnum.SUCCESS.getCode().equals(status)) {
            // 成功
            logger.info("订单号{}，换卡成功回调！",orderNo);
            // 2.1 记录日志
            orderOtherLogClient.createOrderDockingLog(orderNo,orgCode,null, JsonUtils.toJsonIgnoreNull(callbackRequest),
                    OrderDockingOperationEnum.CHANG_FUND_CARD_CALLBACK.operation, OrderDockingResultEnum.SUCCESS.result);

            // 2.2 换卡成功，这时候才更新订单数据关联表
            if (CollectionUtils.isNotEmpty(newCardDOList)) {
                refFundcardOrderMapper.deleteByOrderId(orderMasterDO.getId().toString());
                refFundcardOrderMapper.insertList(newCardDOList);
                logger.info("更新经费卡信息成功，经费卡信息:{}",JsonUtils.toJson(newCardDOList));
            }

            if(isReceiveApproval){
                // 验收审批换卡，换卡成功才执行验收审批通过
                Optional<ExtraDTO> orderApprovalParamOptional = callbackResult.getExtraDTOs().stream().filter(item->"orderApprovalParamDTO".equals(item.getField())).findFirst();
                OrderApprovalParamDTO orderApprovalParamDTO = orderApprovalParamOptional.isPresent() ? JsonUtils.fromJson(orderApprovalParamOptional.get().getValue(),OrderApprovalParamDTO.class) : new OrderApprovalParamDTO();
                userId = orderApprovalParamDTO.getUserId();
                orderApprovalParamDTO.setFundStatus(OrderFundStatusEnum.ChangedCardSuccess.value);
                this.acceptanceApprovalSuccess(orderApprovalParamDTO, orderMasterDO);
                logger.info("验收审批通过,{}", JsonUtils.toJson(newCardDOList));
            } else {
                // userid有两个入口，待结算换卡传来的operatorUserId或验收审批参数的userid
                String operatorUserId = extraDTOMap.get("operatorUserId");
                if (StringUtils.isNotBlank(operatorUserId)) {
                    userId = Integer.valueOf(operatorUserId);
                }
            }

            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.Freezed.value);
            dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra(orderNo, callbackResult.getExtraSerialNumber(), DockingPushStatusEnum.SUCCESS.getCode(), DockingPushStatusEnum.SUCCESS.getDescription()));
            saveChangeCardLog(orderMasterDO.getId(), successLogEnum, userId, orderMasterDO.getFuserid(), changeReason, oldCardNos, newCardNos, StringUtils.EMPTY);
            // 3.更新待结算单的经费卡信息
            updateNewWaitingStatement(newCardDTOList);
        } else if (!callbackRequest.isSuccess() && status != null) {
            // change card failure
            // 财务系统有换卡接口, 原子操作换卡失败
            Integer fundStatus = OrderFundStatusEnum.ChangedCardFail.value;
            if (ChangeFundCardStatusEnum.CHANGE_FUND_CARD_FAILURE.getCode().equals(status)) {
                fundStatus = OrderFundStatusEnum.ChangedCardFail.value;
            } else if (ChangeFundCardStatusEnum.UNFREEZE_FAILURE.getCode().equals(status)) {
                // 非原子操作换卡，1解冻旧卡失败
                fundStatus = OrderFundStatusEnum.ThrawFailed.value;
            } else if (ChangeFundCardStatusEnum.FREEZE_FAILURE.getCode().equals(status)) {
                // 非原子操作换卡，2冻结新卡失败
                fundStatus = OrderFundStatusEnum.FreezedFail.value;
            }
            //记录日志
            orderOtherLogClient.createOrderDockingLog(orderNo, orgCode, null, JsonUtils.toJsonIgnoreNull(callbackRequest),
                    OrderDockingOperationEnum.CHANG_FUND_CARD_CALLBACK.operation, OrderDockingResultEnum.FAIL.result);
            logger.error("订单号{}，换卡失败回调！失败原因{}", orderNo, callbackRequest.getMsg());
            updateOrderParamDTO.setFundStatus(fundStatus);
            updateOrderParamDTO.setFailedReason(callbackRequest.getMsg());
            saveChangeCardLog(orderMasterDO.getId(), failureLogEnum, userId, orderMasterDO.getFuserid(), changeReason, oldCardNos, newCardNos, callbackRequest.getMsg());
        } else {
            // change card error
            logger.error("订单号{}，换卡失败回调！失败原因{}", orderNo, callbackRequest.getMsg());
            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ChangedCardFail.value);
            updateOrderParamDTO.setFailedReason("换卡失败：未知错误:" + callbackRequest.getMsg());
            saveChangeCardLog(orderMasterDO.getId(), failureLogEnum, userId, orderMasterDO.getFuserid(), changeReason, oldCardNos, newCardNos, callbackRequest.getMsg());
        }
        orderMasterMapper.updateOrderById(updateOrderParamDTO);


        // 调换卡的时候开启了政采目录管控的话，需要额外处理
        Optional<ExtraDTO> ctgDirOnOptional = callbackRequest.getData().getExtraDTOs().stream().filter(item->"isCategoryDirectoryOn".equals(item.getField())).findFirst();
        boolean isCategoryDirectoryOn = ctgDirOnOptional.filter(dto -> CommonValueUtils.parseNumberStrToBoolean(dto.getValue())).isPresent();
        if(!isCategoryDirectoryOn) {
            return;
        }
        if (!callbackRequest.isSuccess()) {
            // 换卡失败，需要回滚。无论是否原子性换卡，失败了的话最后还是要重新调用换卡接口。可以放心回滚
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
            categoryDirectoryClient.rollbackSaveStatisticsWhenFail(New.list(orderMasterDO), New.list(orderDetailDOList), oldCardList, newCardDTOList, userId);
        } else {
            // 换卡成功，释放新卡统计
            categoryDirectoryClient.cancelOrderStatistics(orderMasterDO.getFuserid(), New.list(orderMasterDO.getId()), oldCardList.stream().map(RefFundcardOrderDO::getCardId).collect(toList()));
        }
    }

    /**
     * 记录换卡日志
     * @param orderId 订单ID
     * @param approvalEnum 日志枚举
     * @param userId 操作用户ID
     * @param changeReason 换卡原因
     * @param newValue 新卡号
     * @param oldValue 旧卡号
     */
    private void saveChangeCardLog(Integer orderId, OrderApprovalEnum approvalEnum, Integer userId, Integer orgId,
                                   String changeReason, String newValue, String oldValue, String failReason) {

        // 查询操作人信息
        Integer operatorId = DockingConstant.SYSTEM_OPERATOR_ID;
        String operatorName = DockingConstant.SYSTEM_OPERATOR_NAME;
        if (Objects.nonNull(userId) && !Objects.equals(userId, DockingConstant.SYSTEM_OPERATOR_ID)) {
            try {
                UserBaseInfoDTO user = userClient.getUserInfo(userId, orgId);
                if (Objects.nonNull(user)) {
                    operatorId = user.getId();
                    operatorName = user.getName();
                }
            } catch (Exception e) {
                logger.error("未查询到操作人用户信息,orderId:{}, userId:{}", orderId, userId, e);
            }
        }

        // 构建备注信息
        String remark;
        if (OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_SUCCESS.equals(approvalEnum)
                || OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS.equals(approvalEnum)) {
            // 换卡成功的情况
            remark = StrUtil.format("原值为“{}”，新值为“{}”", oldValue, newValue);
            if (StringUtils.isNotBlank(changeReason)) {
                remark = StrUtil.format("原因：{}，{}", changeReason, remark);
            }
        } else {
            // 换卡失败的情况
            remark = StrUtil.format("{}，失败原因：{}", approvalEnum.getName(), failReason);
        }
        // 调用日志服务记录
        orderApprovalLogService.createOrderOperateLog(orderId, approvalEnum.getValue(), operatorId, operatorName, remark);
    }


    /**
     * 订单验收审核通过，进行换卡
     * @param orderApprovalParamDTO
     * @param order
     * @param fundCardCacheList
     */
    @Override
    public void changeFundCard(OrderApprovalParamDTO orderApprovalParamDTO, OrderMasterDO order, List<OrderFundCardDTO> fundCardCacheList) {
        if (CollectionUtils.isEmpty(fundCardCacheList)) {
            logger.warn("换卡不成功，无换卡信息!");
            return;
        }

        String userName = orderApprovalParamDTO.getUserName();
        String jobNumber = orderApprovalParamDTO.getJobNumber();
        String orgCode = orderApprovalParamDTO.getOrgCode();

        Preconditions.isTrue(order != null, "经费卡冻结失败，订单信息为空！");
        Integer orderId = order.getId();

        boolean isCategoryDirectoryOn = categoryDirectoryClient.isCategoryDirectoryOn(orgCode, true, true);
        List<OrderDetailDO> detailList = orderDetailMapper.findByFmasterid(orderId);
        List<OrderMasterDO> orderMasterDOList = New.list(order);
        List<RefFundcardOrderDTO> newRefFundCardOrderDTOS = fundCardCacheList.stream().map(RefFundcardOrderTranslator::fundCardDTO2RefDO).map(RefFundcardOrderTranslator::doToDto).collect(toList());
        if(isCategoryDirectoryOn){
            // 如果开启了政采目录限额
            // 校验新卡的政采目录余额是否充足
            categoryDirectoryClient.checkPurchaseLimitForDetails(orderMasterDOList, detailList, newRefFundCardOrderDTOS);
            // 不管是冻结还是换卡，都是先调用新卡的统计接口
            categoryDirectoryClient.saveOverStatistics(orderMasterDOList, detailList, newRefFundCardOrderDTOS, orderApprovalParamDTO.getUserId());
        }

        // 旧的经费卡列表
        List<RefFundcardOrderDO> oldFundCardList = refFundcardOrderMapper.findByOrderIdIn(Arrays.asList(orderId.toString()));

        Date nowDate = new Date();
        // 设置冻结经费卡对象
        FreezeDTO freezeDTO = new FreezeDTO();
        freezeDTO.setSerialNumber(order.getForderno());
        // 采购业务，需要维护枚举
        freezeDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        // 冻结金额 = 订单总金额 - 退货金额
        Double returnAmount = order.getReturnAmount() == null ? 0.0 : order.getReturnAmount();
        BigDecimal freezeAmount = order.getForderamounttotal().subtract(new BigDecimal(returnAmount));
        freezeDTO.setFreezeAmount(freezeAmount);
        freezeDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        freezeDTO.setUserId(orderApprovalParamDTO.getUserId());
        freezeDTO.setBuyerUserId(order.getFbuyerid());
        freezeDTO.setOperateDate(nowDate);
        freezeDTO.setAppKey(Environment.getAppKey());
        if(OrgEnum.JI_NAN_DA_XUE.getValue() == order.getFuserid()){
            List<UserBaseInfoDTO> userList = userClient.getUserByIdsAndOrgId(New.list(order.getFbuyerid()), order.getFuserid());
            if(CollectionUtils.isNotEmpty(userList)){
                // 暨大这里用采购人
                freezeDTO.setOperatorJobNumber(userList.get(0).getJobnumber());
                freezeDTO.setOperatorName(userList.get(0).getName());
            }
        }else {
            freezeDTO.setOperatorName(userName);
            freezeDTO.setOperatorJobNumber(jobNumber);
        }

        // 冻结的新卡信息
        List<SelectFundCardDTO> freezeFundCardList = new ArrayList<>(fundCardCacheList.size());
        List<String> cardIdList = new ArrayList<>(fundCardCacheList.size());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(order.getId());
        for (OrderFundCardDTO fundCardCache : fundCardCacheList) {
            orderDetailDOList.forEach(detail -> {
                SelectFundCardDTO freezeFundCard = new SelectFundCardDTO();
                freezeFundCard.setId(fundCardCache.getFundCardId());
                freezeFundCard.setFreezeAmount(detail.getFbidamount().subtract(BigDecimal.valueOf(detail.getReturnAmount())));
                freezeFundCard.setFirstGoodsCategoryId(detail.getFirstCategoryId());
                freezeFundCard.setSecondGoodsCategoryId(detail.getSecondCategoryId());
                freezeFundCardList.add(freezeFundCard);
            });
            cardIdList.add(fundCardCache.getFundCardId());
        }
        freezeDTO.setSelectFundCardDTOS(freezeFundCardList);
        // 设置冻结卡的经费负责人信息
        if (CollectionUtils.isNotEmpty(cardIdList)) {
            List<FundCardDTO> cardInfoList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
            Optional.ofNullable(cardInfoList)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(list -> list.get(0))
                    .map(FundCardDTO::getFundCardManagerDTOs)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(list -> list.get(0))
                    .ifPresent(fundCard -> {
                        freezeDTO.setManagerJobNumber(fundCard.getManagerCode());
                        freezeDTO.setManagerName(fundCard.getManagerName());
                    });
        }

        UnfreezeDTO unfreezeDTO = new UnfreezeDTO();
        unfreezeDTO.setSerialNumber(order.getForderno());
        unfreezeDTO.setAppKey(Environment.getAppKey());
        // 获取冻结编号
        DockingExtraDTO dockingRequest = new DockingExtraDTO();
        dockingRequest.setInfo(order.getForderno());
        List<DockingExtraDTO> dockingExtra = dockingExtraService.findDockingExtra(dockingRequest);
        String dockingInfo = null;
        if (CollectionUtils.isNotEmpty(dockingExtra)) {
            dockingInfo = dockingExtra.get(0).getExtraInfo();
        }
        // 设置冻结编号
        unfreezeDTO.setExtraSerialNumber(dockingInfo);
        unfreezeDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        unfreezeDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        unfreezeDTO.setUserId(orderApprovalParamDTO.getUserId());
        unfreezeDTO.setBuyerUserId(order.getFbuyerid());
        unfreezeDTO.setFreezeAmount(order.getForderamounttotal());
        unfreezeDTO.setOperateDate(nowDate);
        unfreezeDTO.setOperatorJobNumber(jobNumber);
        unfreezeDTO.setOperatorName(userName);

        // 解冻的旧卡信息
        List<FundCardDTO> unFreezeFundCardList = oldFundCardList.stream().map(ref -> RefFundcardOrderTranslator.refToFundCardDto(ref)).collect(toList());
        unfreezeDTO.setFundCardDTOs(unFreezeFundCardList);

        ChangeFundCardDTO changeFundCardDTO = new ChangeFundCardDTO();
        changeFundCardDTO.setFreezeDTO(freezeDTO);
        changeFundCardDTO.setUnfreezeDTO(unfreezeDTO);
        changeFundCardDTO.setAppKey(Environment.getAppKey());
        changeFundCardDTO.setSerialNumber(order.getForderno());
        changeFundCardDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        changeFundCardDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        changeFundCardDTO.setUserId(orderApprovalParamDTO.getUserId());

        List<ExtraDTO> extraDTOS = New.list(
                new ExtraDTO("isCategoryDirectoryOn", CommonValueUtils.parseBoolean2NumberStr(isCategoryDirectoryOn)),
                new ExtraDTO("orderApprovalParamDTO", JsonUtils.toJsonIgnoreNull(orderApprovalParamDTO))
        );
        changeFundCardDTO.setExtraDTOs(extraDTOS);
        // 是否回调才处理
        boolean successDependOnCallback = true;
        try {
            // todo 换卡2冻结新卡失败？是否需要去掉unfreezeDTO

            FundCardResultDTO fundCardResultDTO = researchFundCardServiceClient.changeFundCard(orgCode, changeFundCardDTO, New.list(order.getFbuydepartmentid()));
            Integer handleResult = fundCardResultDTO.getHandleResult();
            Integer fundStatus = OrderFundStatusEnum.ChangingCard.value;
            if (HandleResultEnum.UN_HANDLE.getCode().equals(handleResult)) {
                // 处理历史订单，未对接预算系统又调了换卡的订单叫无需解冻状态
                fundStatus = OrderFundStatusEnum.UN_FREEZE.value;
                successDependOnCallback = false;
            } else if (FundCardAsyCallBackEnum.NO_NEED.getValue().equals(fundCardResultDTO.getRelyAsyCallback())) {
                // 是否依赖回调结果，依赖回调则将经费状态改为换卡中，否则经费状态为冻结成功
                fundStatus = OrderFundStatusEnum.Freezed.value;
                successDependOnCallback = false;
            }
            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
            updateOrderParamDTO.setOrderId(orderId);
            updateOrderParamDTO.setFundStatus(fundStatus);
            orderMasterMapper.updateOrderById(updateOrderParamDTO);
            //记录日志
            orderOtherLogClient.createOrderDockingLog(order.getForderno(), orgCode, JsonUtils.toJsonIgnoreNull(changeFundCardDTO), null,
                    OrderDockingOperationEnum.CHANG_FUND_CARD.operation, OrderDockingResultEnum.SUCCESS.result);
        } catch (Exception e) {
            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();

            updateOrderParamDTO.setOrderId(orderId);
            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ChangedCardFail.value);
            String failReason = e.getMessage();
            failReason = StringUtils.isNotBlank(failReason) && failReason.length() > 500 ? failReason.substring(0,500) : failReason;
            updateOrderParamDTO.setFailedReason(failReason);
            orderMasterMapper.updateOrderById(updateOrderParamDTO);
            orderApprovalLogService.createOrderOperateLog(orderId, OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_FAILURE.getValue(), orderApprovalParamDTO.getUserId(), orderApprovalParamDTO.getUserName(), orderApprovalParamDTO.getReason());
            if(isCategoryDirectoryOn){
                categoryDirectoryClient.rollbackSaveStatisticsWhenFail(orderMasterDOList, detailList, oldFundCardList, newRefFundCardOrderDTOS, orderApprovalParamDTO.getUserId());
            }

            //记录日志
            orderOtherLogClient.createOrderDockingLog(order.getForderno(), orgCode, JsonUtils.toJsonIgnoreNull(changeFundCardDTO), e.getMessage(),
                    OrderDockingOperationEnum.CHANG_FUND_CARD.operation, OrderDockingResultEnum.FAIL.result);
            throw e;
        }
        // todo 目前有问题，如果不依赖回调的话，绑卡的数据不会更新。由于目前使用待验收审批换卡的单位都是对接了财务的，暂不处理。
        if(!successDependOnCallback && isCategoryDirectoryOn){
            // 不需要回调且为换卡，释放旧卡的政采管控
            categoryDirectoryClient.cancelOrderStatistics(order.getFuserid(), New.list(orderId), oldFundCardList.stream().map(RefFundcardOrderDO::getCardId).collect(Collectors.toList()));
        }
    }

    /**
     * 验收审批驳回
     * @param orderApprovalParamDTO
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void acceptanceApprovalRejection(OrderApprovalParamDTO orderApprovalParamDTO){
        Integer orderId = orderApprovalParamDTO.getOrderId();
        String reason = orderApprovalParamDTO.getReason();
        Assert.isTrue(StringUtils.isNotBlank(reason),"审批驳回，原因不能为空！");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        Assert.notNull(orderMasterDO,"无对应订单信息！");
        Integer status = orderMasterDO.getStatus();
        BusinessErrUtil.isTrue(OrderStatusEnum.OrderReceiveApproval.value.equals(status), ExecptionMessageEnum.ORDER_NOT_PENDING_APPROVAL_RETRY);
        // 验收审批驳回后 回到待收货状态
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderId);
        updateOrderParamDTO.setStatus(OrderStatusEnum.WaitingForReceive.value);
        orderMasterMapper.updateOrderById(updateOrderParamDTO);

        //记录操作日志
        this.createOrderOperateLog(orderId,OrderApprovalEnum.REJECT.getValue(),orderApprovalParamDTO.getUserId(),orderApprovalParamDTO.getReason());
    }


    /**
     * 验收审批通过
     * @param orderApprovalParamDTO
     * @param orderMasterDO
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void acceptanceApprovalSuccess(OrderApprovalParamDTO orderApprovalParamDTO, OrderMasterDO orderMasterDO) {
        logger.info("验收审批通过，入参{}",JsonUtils.toJson(orderApprovalParamDTO));
        String orgCode = orderApprovalParamDTO.getOrgCode();
        Integer userId = orderApprovalParamDTO.getUserId();
        String userName = orderApprovalParamDTO.getUserName();

        Integer orderId = orderMasterDO.getId();
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);

        //退货中的 退货状态 的商品名称
        String goodNames = this.getReturningGoodName(orderDetailDOList);
        BusinessErrUtil.isTrue(StringUtils.isEmpty(goodNames), ExecptionMessageEnum.RETURN_PENDING_GOODS, goodNames);

        //获取医院相关的 验收拍照配置、验收审批、验收入库配置、验收方式配置、合同配置、合同金额配置、线上单是否使用结算、线下单是否使用结算
        List<String> configCodeList = New.list(ConfigConstant.ORG_RECEIPT_PIC_CONFIG, ConfigConstant.ORG_RECEIPT_STORE_CONFIG,
                ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name(), ConfigConstant.AUTO_SUBMIT_STATMENT,
                ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM,
                ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE, ConfigConstant.USE_WAREHOUSE_SYSTEM);
        Map<String, String> receiptConfigMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orgCode, configCodeList);

        // 是否使用库房
        boolean isUsedStore = this.compareAndSetConfig(receiptConfigMap, ConfigConstant.USE_WAREHOUSE_SYSTEM, ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE);

        //判断是否是线上单
        int orderProcessSpecies = orderMasterDO.getSpecies().intValue();
        boolean isNormal = orderProcessSpecies == ProcessSpeciesEnum.NORMAL.getValue();

        //是否未对接过经费的历史订单
        boolean unRelateOrderData = isUnRelateOrderData(orderMasterDO);

        /* 当前单位是否使用结算系统 add by Kimmy 2021/03/29*/
        boolean usedStatement = orderStatementService.isUseStatement(receiptConfigMap, orderMasterDO);

        // 1.先更新完待结算状态，标识订单已经验收审批完成，这里处理验收审批主体逻辑
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setFundStatus(orderApprovalParamDTO.getFundStatus());
        updateOrderParamDTO.setStatus(OrderStatusEnum.WaitingForStatement_1.value);
        updateOrderParamDTO.setOrderId(orderId);
        orderMasterMapper.updateOrderById(updateOrderParamDTO);
        logger.info("验收审批，更新订单id为:{}的订单状态成功！入参:{}", orderId, JsonUtils.toJsonIgnoreNull(updateOrderParamDTO));
        // 通知完成了验收审批
        try{
            acceptApprovalClient.completeApproval(orderId);
        }catch (Exception e){
            logger.error("订单id{}通知验收审批服务完成审批流失败", orderId, e);
        }

        // 2.入库相关处理
        // 订单入库处理器策略, 现在是江西中医入库有定制业务
        Integer inventoryStatus = orderMasterDO.getInventoryStatus().intValue();
        InventoryStatusEnum inventoryStatusEnum = InventoryStatusEnum.getByCode(inventoryStatus);
        if(InventoryStatusEnum.WAITING_FOR_STORE.equals(inventoryStatusEnum)){
            OrderAcceptService customOrderAcceptService = BeanContainer.getBean(OrderAcceptService.class, OrderAcceptConstant.getAcceptStrategy().get(orgCode));
            if (customOrderAcceptService == null) {
                // 如果没有策略实现，就用默认实现
                customOrderAcceptService = orderAcceptService;
            }

            InWarehouseModeEnum warehouseMode;
            if (isUsedStore) {
                // 获取入库模式
                warehouseMode = customOrderAcceptService.calculateInWarehouseMode(orderMasterDO, receiptConfigMap, orderDetailDOList);
                if (SYSTEM_OPERATOR_ID != userId) {
                    // 操作人是系统时也无法获取到 rjSession，跳过
                    // 因为验收审批的入口在saturn, rjSession取不到, 先手动设置一下, 验收审批通过逻辑迁移到saturn再删掉
                    this.setRjSession(userId, orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());
                }
            }else {
                // 不用库房系统，无须入库
                warehouseMode = InWarehouseModeEnum.NO_NEED;
            }
            // 根据入库模式处理入库
            WarehouseResultDTO warehouseResultDTO = customOrderAcceptService.executeInWareHouse(warehouseMode, orderMasterDO, orderDetailDOList, orderMasterDO.getReceivePicUrls());
            logger.info("验收审批，处理订单id为:{}的订单入库！是否使用库房系统:{},入库模式:{}！入库结果:{}", orderId, isUsedStore, warehouseMode, warehouseResultDTO);
            // 入库失败，就不需要结算，所以在此处抛出异常
            Preconditions.isTrue(warehouseResultDTO.getWarehouseSuccess(), warehouseResultDTO.getWarehouseErrorMsg());
            inventoryStatusEnum = warehouseResultDTO.getInventoryStatus();
            inventoryStatus = inventoryStatusEnum.getCode();
        }

        // 3.调用结算部分逻辑
        boolean goAhead = !isUsedStore || InventoryStatusEnum.COMPLETE.equals(inventoryStatusEnum) || InventoryStatusEnum.NOT_INBOUND.equals(inventoryStatusEnum);
        if(goAhead){
            // 不使用库房系统或直接不调用库房接口，减少数量
            timeoutStatisticsWithBalance.executeAcceptApproveOrWareHouseStatisticChange(-1, orderMasterDO, orderDetailDOList);
        }
        if (goAhead && !unRelateOrderData) {
            orderStatementService.orderStatementCore(orderMasterDO, userId, userName, usedStatement, inventoryStatus);
        } else if (unRelateOrderData) {
            //特殊处理验收后直接走完流程的订单
            UpdateOrderParamDTO updateParam = new UpdateOrderParamDTO();
            updateParam.setStatus(OrderStatusEnum.Finish.getValue());
            updateParam.setFinishDate(new Date());
            updateParam.setOrderId(orderId);
            orderMasterMapper.updateOrderById(updateParam);
            logger.info("更新订单id为:{}的未对接经费历史单状态成功！入参:{}", orderId, JsonUtils.toJsonIgnoreNull(updateParam));
        }
    }

    /**
     * 异步写 我的待审的数缓存
     * @param orderMasterDO
     * @param userId
     */
    @Deprecated
    private void asyncUpdateApprovalCount(OrderMasterDO orderMasterDO, Integer userId) {
        List<DepartmentDTO> canApprovalDeptCollect = userClient.getDeptForUserByAccess(userId, orderMasterDO.getFuserid(), ConfigConstant.ORDER_APPROVE);
        // 待审列表只显示能审的单, 若canApprovalDeptCollect.isEmpty, 则deptId搜索条件传一个不存在的课题组id
        final List<Integer> canApprovalDeptIds = canApprovalDeptCollect.stream().map(DepartmentDTO::getId).collect(toList());
        if (CollectionUtils.isEmpty(canApprovalDeptIds)) {
            return;
        }
        if (canApprovalDeptIds.stream().anyMatch(f -> f.equals(orderMasterDO.getFbuydepartmentid()))) {
            final String uniKey = OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT + ":" + orderMasterDO.getFuserid() + ":" + userId;
            try {
                cacheClient.lockRetry(OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT_LOCK, 5);
                Map<OrderStatusEnum, Long> cache = (Map<OrderStatusEnum, Long>) cacheClient.getFromCache(uniKey);
                if (cache == null) {
                    return;
                }
                cache.computeIfPresent(OrderStatusEnum.OrderReceiveApproval, (orderStatusEnum, count) -> count - 1);
                cacheClient.setToCache(uniKey, cache, 3600);
            } catch (Exception e) {
                System.out.println("获取分布式锁{}失败:{}");
            } finally {
                cacheClient.removeCache(OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT_LOCK);
            }
        }
    }

    @Override
    public void asyncVerifyOrderRisk(List<Integer> orderIdList){
        Runnable task = () -> {
            if(CollectionUtils.isNotEmpty(orderIdList)) {
                OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderIdList.get(0));
                Integer orgId = orderMasterDO.getFuserid();
                String orgCode = orderMasterDO.getFusercode();
                if (ZhongShanDaXueOrgEnum.getAllOrgIds().contains(orgId)) {
                    orderRiskVerifiedServiceClient.validateOrderRisk(orgId, orgCode, orderIdList);
                }
            }
        };
        AsyncExecutor.listenableRunAsync(task).addFailureCallback(ex -> logger.error("非服务类商品库风险订单交易失败:{}", ex));
    }

    /**
     * 推送到管理平台，走thunder服务接口
     * @param orderMasterDOList
     * @param orderDetailDOList
     */
    @Override
    public void pushOrderToThirdPlatformByThunder(List<OrderMasterDO> orderMasterDOList, List<OrderDetailDO> orderDetailDOList) {
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return;
        }
        AsyncExecutor.listenableRunAsync(
                () -> {
                    Map<Integer, List<OrderDetailDO>> orderIdDetailMap = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFmasterid);
                    List<ThirdPartOrderMasterDTO> thirdPartOrderMasterList = orderMasterDOList.stream().map(o -> {
                        ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(o);
                        List<OrderDetailDO> detailDOList = orderIdDetailMap.get(o.getId());
                        if (CollectionUtils.isNotEmpty(detailDOList)) {
                            thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
                        }
                        return thirdPartOrder;
                    }).collect(toList());
                    thirdPartOrderRPCClient.pushOrderInfo(thirdPartOrderMasterList, OrderEventTypeEnum.GENERATE_ORDER, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
                }
        ).addFailureCallback(e -> {
            logger.error("推送订单异常:" + e);
            Cat.logError(CAT_TYPE, "pushTPIOrderInfo", "推送订单异常", e);
            if ((OrgEnum.HUA_NAN_SHI_FAN_DA_XUE.getCode().equals(orderMasterDOList.get(0).getFusercode()) || OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode().equals(orderMasterDOList.get(0).getFusercode()))) {
                // 还要把审批日志记录到采购单详情
                purchaseApprovalLogClient.addApprovalLog(orderMasterDOList.get(0).getFtbuyappid(), DockingConstant.SYSTEM_OPERATOR_NAME, ApproveLevelEnum.DEFAULT.getValue(), 0, "系统审批单推送失败", e.getMessage());
                applicationBaseClient.saveApplicationMaster(orderMasterDOList.get(0).getFtbuyappid(), null, null, PushStatusEnum.FAIL.getValue());
            }
            // 推送订单校验参数不通过的问题，这里更新下docking日志,前端根据docking日志决定是否显示重新推送按钮
            dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra(orderMasterDOList.get(0).getForderno(), null, DockingPushStatusEnum.ARGUMENT_EXCEPTION.getCode(), e.getMessage()));
            orderMasterMapper.updateStatusById(orderMasterDOList.get(0).getId(), OrderStatusEnum.DeckingFail.getValue());
        });
    }

    @Override
    public void pushOrderToThirdPlatform(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        // 广工&贵医大&华师的订单需要推送订单信息到对方平台,只能单笔的推
        String orgCode = orderMasterDO.getFusercode();
        if (DockingConstant.THIRD_PARTY_PLATFORM_ORGANIZATION.contains(orgCode)) {
            AsyncExecutor.listenableRunAsync(() -> {
                this.pushTPIOrderInfoStrategy(orderMasterDO, orderDetailDOList);
                orderOtherLogClient.createOrderDockingLog(orderMasterDO.getForderno(), orderMasterDO.getFusercode(), null, "status: " + orderMasterDO.getStatus(), "推送订单操作", null);
            }).addFailureCallback(throwable -> {
                logger.error("推送订单异常:" + throwable);
                Cat.logError(CAT_TYPE, "pushTPIOrderInfo", "推送订单异常", throwable);

                this.pushFailureCallbackToPurchase(orderMasterDO, orgCode, throwable);
                // 解决广工那种推送订单校验参数不通过的问题，这里更新下docking日志
                dockingExtraService.saveOrUpdateDockingExtra(orderMasterDO.getForderno(), orderMasterDO.getForderno(), false, throwable.getMessage());
                orderOtherLogClient.createOrderDockingLog(orderMasterDO.getForderno(), orderMasterDO.getFusercode(), null, throwable.getMessage(), "推送订单操作异常", null);
            });
        }
    }

    /**
     * 回写采购单推送状态
     * @param orderMasterDO
     * @param orgCode
     * @param throwable
     */
    private void pushFailureCallbackToPurchase(OrderMasterDO orderMasterDO, String orgCode, Throwable throwable) {
        if ((OrgEnum.HUA_NAN_SHI_FAN_DA_XUE.getCode().equals(orgCode) || OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode().equals(orgCode)) || OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(orgCode)) {
            // 还要把审批日志记录到采购单详情
            purchaseApprovalLogClient.addApprovalLog(orderMasterDO.getFtbuyappid(), DockingConstant.SYSTEM_OPERATOR_NAME, ApproveLevelEnum.DEFAULT.getValue(), 0, "系统审批单推送失败", throwable.getMessage());
            applicationBaseClient.saveApplicationMaster(orderMasterDO.getFtbuyappid(), null, null, PushStatusEnum.FAIL.getValue());
        }
    }



    /**
     * 推送订单信息到对接平台
     * @param orderMasterDO     orderMaster
     * @param orderDetailDOList orderDetail
     */
    private void pushTPIOrderInfoStrategy(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        String orgCode = orderMasterDO.getFusercode();
        // 类里有其他同名的OrderDTO类
        OrgRequest<com.reagent.research.financial.docking.dto.order.OrderDTO> request = new OrgRequest<>();
        com.reagent.research.financial.docking.dto.order.OrderDTO orderItem = new com.reagent.research.financial.docking.dto.order.OrderDTO();
        orderItem.setAppKey(Environment.getAppKey());
        orderItem.setType(MessageTypeEnum.ORDER.getValue());
        orderItem.setOrderNo(orderMasterDO.getForderno());
        orderItem.setStatus(orderMasterDO.getStatus());
        orderItem.setOrderDate(orderMasterDO.getForderdate());
        orderItem.setBuyerName(orderMasterDO.getFbuyername());

        orderItem.setBuyerTelephone(orderMasterDO.getFbuyertelephone());
        orderItem.setBuyerEmail(orderMasterDO.getFbuyeremail());
        orderItem.setOrderAmountTotal(orderMasterDO.getForderamounttotal());
        orderItem.setSuppId(orderMasterDO.getFsuppid());
        orderItem.setSuppName(orderMasterDO.getFsuppname());
        orderItem.setSuppCode(orderMasterDO.getFsuppcode());
        orderItem.setIfBid(OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDO.getOrderType()));
        orderItem.setBidBudget(orderMasterDO.getForderamounttotal());
        orderItem.setDeliveryPlace(orderMasterDO.getFbiderdeliveryplace());
        orderItem.setRemark("无");

        AtomicInteger detailSort = new AtomicInteger(1);
        List<OrderDetailDTO> orderDetailDTOList = orderDetailDOList.stream().map(detail -> {
            OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
            orderDetailDTO.setId(detail.getId().toString());
            orderDetailDTO.setGoodsId(detail.getProductSn().toString());
            orderDetailDTO.setGoodsName(detail.getFgoodname());
            orderDetailDTO.setQuantity(detail.getFquantity());
            orderDetailDTO.setPrice(detail.getFbidprice());
            orderDetailDTO.setGoodsCode(detail.getFgoodcode());
            // 有些商品的品牌信息没录入，推送订单写死"无"
            orderDetailDTO.setBrand(StringUtils.isNotBlank(detail.getFbrand()) ? detail.getFbrand() : "无");
            orderDetailDTO.setSpecification(detail.getFspec());
            orderDetailDTO.setUnit(detail.getFunit());
            orderDetailDTO.setCasNo(detail.getCasno());
            orderDetailDTO.setFirstGoodsCategoryId(detail.getFirstCategoryId() != null ? detail.getFirstCategoryId().toString() : null);
            orderDetailDTO.setFirstGoodsCategoryName(detail.getFirstCategoryName());
            orderDetailDTO.setSecondGoodsCategoryId(detail.getSecondCategoryId() != null ? detail.getSecondCategoryId().toString() : null);
            orderDetailDTO.setSecondGoodsCategoryName(detail.getSecondCategoryName());
            orderDetailDTO.setDangerousTypeName(detail.getDangerousTypeName());

            ExtraDTO extraDTO1 = new ExtraDTO();
            extraDTO1.setField("goodsType");
            extraDTO1.setValue(detail.getFirstCategoryName());

            ExtraDTO extraDTO2 = new ExtraDTO();
            extraDTO2.setField("sortNum");
            extraDTO2.setValue(String.valueOf(detailSort.getAndIncrement()));

            ExtraDTO extraDTO3 = new ExtraDTO();
            extraDTO3.setField("totalPrice");
            extraDTO3.setValue(detail.getFbidamount().setScale(2, RoundingMode.HALF_UP).toPlainString());

            orderDetailDTO.setExtraDTOs(Arrays.asList(extraDTO1, extraDTO2, extraDTO3));
            return orderDetailDTO;
        }).collect(Collectors.toList());
        orderItem.setOrderDetailDTOs(orderDetailDTOList);

        com.reagent.research.financial.docking.dto.order.DepartmentDTO departmentDTO = new com.reagent.research.financial.docking.dto.order.DepartmentDTO();
        departmentDTO.setId(orderMasterDO.getFbuydepartmentid().toString());
        departmentDTO.setName(orderMasterDO.getFbuydepartment());
        orderItem.setDepartmentDTO(departmentDTO);
        orderItem.setOrderDateString(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderMasterDO.getForderdate()));

        request.setOrgCode(orgCode);
        request.setData(orderItem);
        tpiOrderClient.pushOrder(request);
        // 同步采购单推送状态
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(orgCode)) {
            applicationBaseClient.saveApplicationMaster(orderMasterDO.getFtbuyappid(), null, null, PushStatusEnum.PUSHING.getValue());
        }
    }

    /**
     * 比较配置并返回配置
     *
     * @param receiptConfigMap
     * @param orgAcceptanceApprovalConfig
     * @param s
     * @return
     */
    private boolean compareAndSetConfig(Map<String, String> receiptConfigMap, String orgAcceptanceApprovalConfig, String s) {
        String acceptanceApprovalConfig = receiptConfigMap.get(orgAcceptanceApprovalConfig);
        return s.equals(acceptanceApprovalConfig);
    }

    /**
     * 是否为未对接经费的旧单
     * @param orderMasterDO
     * @return
     */
    public boolean isUnRelateOrderData(OrderMasterDO orderMasterDO) {
        String orgCode = orderMasterDO.getFusercode();
        if (
            // 广药科线下单， 自结算都是验收审批后已完成
                (OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode().equals(orgCode) && (OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus()) || OrderSpeciesEnum.OFFLINE.getValue().equals(orderMasterDO.getSpecies().intValue()))
                        || (OrgEnum.NAN_FANG_YI_KE.getCode().equals(orgCode) && orderMasterDO.getForderdate().before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, oldOrderDateForNanFangYiKe))))
        ) {
            return true;
        }
        return false;
    }

    /**
     * 记录订单验收日志
     * @param userId
     * @param orderId
     */
    private void createOrderOperateLog(Integer orderId , Integer approveStatus, Integer userId , String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setCreationTime(new Date());
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
        logger.info("记录订单日志成功！入参{}",JsonUtils.toJson(orderApprovalLog));
    }

    /**
     * 获取退货中物品的名称
     * @param orderDetailList
     * @return
     */
    private String getReturningGoodName(List<OrderDetailDO> orderDetailList) {
        List<Integer> returnStatusList = New.list(
                OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode(),
                OrderDetailReturnStatus.AGREETORETURN.getCode(),
                OrderDetailReturnStatus.REFUSEDTORETURN.getCode(),
                OrderDetailReturnStatus.RETURNEDGOODS.getCode());

        List<String> returnGoodNameList = orderDetailList.stream().filter(orderDetailDO -> returnStatusList.contains(orderDetailDO.getReturnStatus()))
                .map(OrderDetailDO::getFgoodname).collect(toList());
        return StringUtils.join(returnGoodNameList,"；");
    }

    /**
     * 成功入库 更新订单操作
     * @param inventoryStatus 入库状态
     * @param orgCode    组织code
     * @param orderMasterDO 订单
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void wareHouseSuccess(Byte inventoryStatus, String orgCode, OrderMasterDO orderMasterDO) {
        //获取医院相关的 验收拍照配置、验收入库配置、验收方式配置、合同配置、合同金额配置
        String autoStatementConfig = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.AUTO_SUBMIT_STATMENT);
        Integer statementIdForUpdate = null;
        Date statementDateForUpdate = null;
        Integer updateOrderStatus = null;
        // 如果 订单状态为待结算入库
        if (InventoryStatusEnum.COMPLETE.getCode().equals(inventoryStatus)
                || InventoryStatusEnum.NOT_INBOUND.getCode().equals(inventoryStatus)) {
            if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus())) {
                if (ConfigConstant.AUTO_STATEMENT_VALUE.toString().equals(autoStatementConfig)) {
                    int orderProcessSpecies = orderMasterDO.getSpecies().intValue();
                    boolean isNormal = orderProcessSpecies == ProcessSpeciesEnum.NORMAL.getValue();
                    boolean orgCanStatement = isNormal || OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode().equals(orgCode);
                    if (orgCanStatement) {
                        //自动提交结算配置， 自动提交结算，
                        StatementResultDTO statementDTO = researchStatementClient.createStatementSingle(NumberUtils.toInt(orderMasterDO.getFlastreceivemanid()), orderMasterDO.getFlastreceiveman(), orderMasterDO);
                        statementIdForUpdate = statementDTO.getId().intValue();
                        statementDateForUpdate = new Date();
                        updateOrderStatus = OrderStatusEnum.Statementing_1.value;
                    }
                } else {
                    //不是则 则推送到待结算业务表中，异步
                    waitingStatementService.pushWaitingStatement(orgCode, New.list(orderMasterDO.getId()));
                }
            }
        }
        // 修改订单出入库状态
        orderMasterDO.setInventoryStatus(inventoryStatus);
        orderMasterDO.setStatementId(statementIdForUpdate);
        orderMasterDO.setInStateTime(statementDateForUpdate);
        orderMasterDO.setStatus(updateOrderStatus);
        orderMasterMapper.updateByPrimaryKeySelective(orderMasterDO);
    }

    /**
     * 查询超时订单
     * @param params 入参
     * @return 结果
     */
    @Override
    public BasePageResponseDTO<OrderMasterTimeOutDTO> findTimeOutOrders(TimeOutOrderParamsDTO params) {
        // 4.返回结果
        return timeoutQueryService.findTimeOutOrders(params);
    }

    /**
     * 查询超时订单数量
     * @param departmentId     课题组id
     * @param timeOutConfigMap 超时配置
     * @param selectType        超时条件 0 -> 超时结算, 1 -> 超时验收
     * @return
     */
    @Override
    public SearchPageResultDTO<OrderMasterSearchDTO> getTimeOutOrdersBySelectType(int departmentId, Map<String, Integer> timeOutConfigMap, int selectType) {
        TimeOutOrderParamsDTO orderParamsDTO = new TimeOutOrderParamsDTO();
        orderParamsDTO.setDepartmentIds(Collections.singletonList(departmentId));
        orderParamsDTO.setOverTimeType(selectType);
        return timeoutQueryService.queryTimeOutOrder(orderParamsDTO, timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()), timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode()));
    }

    /**
     * 根据 orgCode 获取超时相关配置
     * @param orgCode 配置字典 (configCode -> configvalue)
     * @return
     */
    @Override
    public Map<String, Integer> getTimeOutConfigMap(String orgCode) {
        // 1.获取医院机构的配置
        List<BaseConfigDTO> configDTO = null;
        try {
            List<String> configCodeList = Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(toList());
            configCodeList.add(ConfigConstant.USE_WAREHOUSE_SYSTEM);
            configDTO = sysConfigClient.getValueByOrgCodeAndConfigCode(
                    orgCode,
                    configCodeList
            );
        } catch (CallRpcException e) {
            configDTO = New.emptyList();
        }

        Map<String, Integer> configMap = new HashMap<>(4);
        // 获取配置的结算超时天数
        int balanceDaysInt = TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getDefaultSet();
        // 验收超时天数
        int examineDaysInt = TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getDefaultSet();
        // 结算超时张数
        int balanceAmountInt = TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getDefaultSet();
        // 验收超时张数
        int examineAmountInt = TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getDefaultSet();
        for (BaseConfigDTO config : configDTO) {
            if (config.getConfigCode().equals(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode())) {
                balanceDaysInt = NumberUtils.toInt(config.getConfigValue(), TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getDefaultSet());
            }

            if (config.getConfigCode().equals(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode())) {
                examineDaysInt = NumberUtils.toInt(config.getConfigValue(), TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getDefaultSet());
            }

            if (config.getConfigCode().equals(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())) {
                balanceAmountInt = NumberUtils.toInt(config.getConfigValue(), TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getDefaultSet());
            }

            if (config.getConfigCode().equals(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode())) {
                examineAmountInt = NumberUtils.toInt(config.getConfigValue(), TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getDefaultSet());
            }

            if(config.getConfigCode().equals(ConfigConstant.USE_WAREHOUSE_SYSTEM)){
                configMap.put(ConfigConstant.USE_WAREHOUSE_SYSTEM, Integer.valueOf(config.getConfigValue()));
            }
        }

        configMap.put(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode(), balanceDaysInt);
        configMap.put(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode(), examineDaysInt);
        configMap.put(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode(), balanceAmountInt);
        configMap.put(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode(), examineAmountInt);

        return configMap;
    }

    @Override
    public List<OrderStoreHouseVO> findStorageByOrderInfo(OrderBasicParamDTO request) {
        Integer orderId = request.getOrderId();
        Preconditions.notNull(orderId, "查询存储仓库列表失败，订单id不可为空");
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.QUERY_STORAGE_LIST_FAILED_NO_ORDERS);

        Integer departmentId = orderMaster.getFbuydepartmentid();
        List<CbsdStoreHouseDTO> storeHouseList = cooperationClient.findStoreHouseByDepartmentId(departmentId);
        List<OrderStoreHouseVO> result = storeHouseList.stream().map(s -> CooperationTranslator.dtoToOrderStorageVO(s)).collect(Collectors.toList());
        return result;
    }

    /**
     * 设置rjsession
     * @param userId 用户id
     * @param suppId 供应商id
     * @param orgId 单位id
     */
    private void setRjSession(Integer userId, Integer suppId, Integer orgId){
        Map<String, Object> attachments = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        UserBaseInfoDTO userInfo = userClient.getUserDetailByID(userId);
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.ACCEPTANCE_APPROVAL_FAILED_NO_LOGIN_INFO);
        sessionInfo.setGuid(userInfo.getGuid());
        sessionInfo.setSuppId(suppId);
        sessionInfo.setOrgId(orgId);
        attachments.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setCallAttachments(attachments);
    }

    /**
     * 更新新待结算单的经费卡信息
     * @param newCardDTOList 经费卡订单DTO列表
     */
    private void updateNewWaitingStatement(List<RefFundcardOrderDTO> newCardDTOList) {
        if (CollectionUtils.isEmpty(newCardDTOList)) {
            return;
        }

        // 构建订单号与经费卡映射关系
        Map<String, String> orderRefFundCardMap = New.map();
        for (RefFundcardOrderDTO fundCardOrderDTO : newCardDTOList) {
            orderRefFundCardMap.compute(
                    fundCardOrderDTO.getOrderId(),
                    (k, fundCardNo) -> fundCardNo == null ?
                            fundCardOrderDTO.getCardNo() :
                            fundCardNo + ";" + fundCardOrderDTO.getCardNo()
            );
        }

        if (MapUtils.isEmpty(orderRefFundCardMap)) {
            return;
        }

        // 更新待结算单列表
        List<WaitingStatementOrderRequestDTO> statementOrderRequestDTOList = New.list();
        for (Map.Entry<String, String> entry : orderRefFundCardMap.entrySet()) {
            WaitingStatementOrderRequestDTO statementRequest = new WaitingStatementOrderRequestDTO();
            statementRequest.setOrderId(NumberUtils.toInt(entry.getKey(), 0));
            statementRequest.setFundCards(entry.getValue());
            statementOrderRequestDTOList.add(statementRequest);
        }

        if (CollectionUtils.isNotEmpty(statementOrderRequestDTOList)) {
            statementPlatformClient.updateWaitingStatement(statementOrderRequestDTOList);
        }
    }




}
