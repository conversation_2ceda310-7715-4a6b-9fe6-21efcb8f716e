package com.ruijing.store.order.base.minor.service;

import com.ruijing.store.order.api.base.other.dto.OrderRemarkDTO;

import java.util.List;

/**
 * @description: 订单备注表相关业务
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/18 10:12
 **/
public interface OrderRemarkService {
    /**
     * 通过采购单id 和 供应商id 批量查询记录
     * @param orderRemarkDTOS   入参
     * @return                  订单备注记录
     */
    List<OrderRemarkDTO> findOrderRemarkByPrimaryKeys(List<OrderRemarkDTO> orderRemarkDTOS);

    /**
     * 保存/更新订单备注
     * @param dto   订单备注实体
     * @return      更新条数
     */
    int saveOrUpdateOrderRemark(OrderRemarkDTO dto);

    /**
     * 保存订单
     * @param orderId 订单号
     * @param remark 评论内容
     */
    void saveBuyerRemark(Integer orderId, String remark);
}
