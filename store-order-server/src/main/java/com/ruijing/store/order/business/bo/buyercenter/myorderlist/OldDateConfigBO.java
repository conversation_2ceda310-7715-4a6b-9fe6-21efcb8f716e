package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @author: liwenyu
 * @createTime: 2023-10-11 09:56
 * @description:
 **/
public class OldDateConfigBO {

    /**
     * 财务对接配置，限制的旧单经费状态范围
     */
    private static final List<Integer> FINANCIAL_DOCKING_FUND_STATUS_SCOPE = New.list(OrderFundStatusEnum.UN_FREEZE.getValue());

    /**
     * 特殊标记，标识旧单可以不通过日期判断
     */
    private static final Date CAN_IGNORE_DATE = new Date(0);

    /**
     * 旧单日期--无此数据即非旧单
     */
    private Date oldDate;

    /**
     * 旧单范围--线上、线下单（null即都为旧单）
     */
    private List<Integer> speciesScopeList;

    /**
     * 旧单范围--经费状态（null即都为旧单）
     */
    private List<Integer> fundStatusScopeList;

    /**
     * 是否需要删除旧卡
     */
    private Boolean needDeleteOldCard;

    public Date getOldDate() {
        return oldDate;
    }

    public OldDateConfigBO setOldDate(Date oldDate) {
        this.oldDate = oldDate;
        return this;
    }

    public List<Integer> getSpeciesScopeList() {
        return speciesScopeList;
    }

    public OldDateConfigBO setSpeciesScopeList(List<Integer> speciesScopeList) {
        this.speciesScopeList = speciesScopeList;
        return this;
    }

    public List<Integer> getFundStatusScopeList() {
        return fundStatusScopeList;
    }

    public OldDateConfigBO setFundStatusScopeList(List<Integer> fundStatusScopeList) {
        this.fundStatusScopeList = fundStatusScopeList;
        return this;
    }

    public Boolean judgeOldCardNeedDel() {
        return needDeleteOldCard;
    }

    public OldDateConfigBO setNeedDeleteOldCard(Boolean needDeleteOldCard) {
        this.needDeleteOldCard = needDeleteOldCard;
        return this;
    }

    /**
     * 获取对接配置实例
     * @param orgCode 单位
     * @param baseConfigList 配置，一定要包含OLD_DATA_PROCESS_TIME与OLD_DATA_PROCESS_BUSINESS_SCOPE
     * @return 配置
     */
    public static OldDateConfigBO getInstance(String orgCode, List<BaseConfigDTO> baseConfigList){
        return OldDateConfigBO.getInstance(orgCode, baseConfigList, false);
    }

    /**
     * 获取对接配置实例
     * @param orgCode 单位
     * @param baseConfigList 配置，一定要包含OLD_DATA_PROCESS_TIME与OLD_DATA_PROCESS_BUSINESS_SCOPE
     * @return 配置
     */
    public static OldDateConfigBO getInstance(String orgCode, List<BaseConfigDTO> baseConfigList, boolean ignoreConstant){
        BusinessErrUtil.notNull(orgCode, ExecptionMessageEnum.ORG_CODE_REQUIRED_FOR_CONFIG);
        Date oldDate = null;
        if(!ignoreConstant){
            // 忽略写死的配置，则只读baseConfigList
            oldDate = OrderDateConstant.OLD_FLAG_OLD_ORDER_DATE_MAP.get(orgCode);
        }
        if(oldDate != null){
            return new OldDateConfigBO().setOldDate(oldDate);
        }
        if(CollectionUtils.isNotEmpty(baseConfigList)){
            Map<String, String> configMap = baseConfigList.stream()
                    .filter(baseConfigDTO -> orgCode.equals(baseConfigDTO.getOrgCode()))
                    .collect(Collectors.toMap(BaseConfigDTO::getConfigCode, BaseConfigDTO::getConfigValue, (o,n)->n));
            String enableBudgetDocking = configMap.get(ConfigCodeEnum.OLD_DATA_PROCESS_OPEN.name());
            if(!CommonValueUtils.TRUE_NUMBER_STR.equals(enableBudgetDocking)){
                return null;
            }
            String scopeConfig = configMap.get(ConfigCodeEnum.OLD_DATA_PROCESS_BUSINESS_SCOPE.name());
            try{
                List<Integer> speciesScopeList = StringUtils.isEmpty(scopeConfig) ? New.emptyList() :
                        Arrays.stream(scopeConfig.split(",")).filter(StringUtils::isNotBlank).map(value->Integer.valueOf(value.trim())).collect(toList()) ;
                // 不用判断旧单时间，只需要判断是否在业务范围且已冻结
                return new OldDateConfigBO()
                        .setOldDate(CAN_IGNORE_DATE)
                        .setFundStatusScopeList(FINANCIAL_DOCKING_FUND_STATUS_SCOPE)
                        .setSpeciesScopeList(speciesScopeList)
                        .setNeedDeleteOldCard(CommonValueUtils.parseNumberStrToBoolean(configMap.get(ConfigCodeEnum.OLD_DATA_PROCESS_CHANGE_CARD.name())));
            } catch (Exception e){
                throw new BusinessInterceptException(ExecptionMessageEnum.SYSTEM_CONFIG_READ_ERROR);
            }
        }
        return null;
    }

    /**
     * 判断是否旧单
     * @param orderDate 当前订单日期
     * @param fundStatus 当前订单状态
     * @param orderSpecies 当前订单类型
     * @return 是否旧单
     */
    public boolean getIsOldOrder(Date orderDate, Integer fundStatus, Integer orderSpecies){
        // 不需要判断旧单日期可以不判断日期
        if(CAN_IGNORE_DATE != this.oldDate){
            if(this.oldDate == null || orderDate.after(this.oldDate)){
                // 没有配置旧单日期或订单时间晚于旧单日期，非旧单
                return false;
            }
        }
        if(this.fundStatusScopeList != null && !fundStatusScopeList.contains(fundStatus)){
            // 有配置经费状态范围但当前单据经费状态范围不在里面，非旧单
            return false;
        }
        if(this.speciesScopeList != null && !this.speciesScopeList.contains(orderSpecies)){
            // 有配置线上线下单范围但当前单据订单类型范围不在里面，非旧单
            return false;
        }
        return true;
    }

    /**
     * 判断是否需要删除旧卡
     * @param orderDate 当前订单日期
     * @param fundStatus 当前订单状态
     * @param orderSpecies 当前订单类型
     * @return 是否旧单
     */
    public boolean judgeOldCardNeedDel(Date orderDate, Integer fundStatus, Integer orderSpecies){
        return this.getIsOldOrder(orderDate, fundStatus, orderSpecies) && this.needDeleteOldCard;
    }
}
