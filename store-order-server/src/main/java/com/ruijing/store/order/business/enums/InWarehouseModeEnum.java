package com.ruijing.store.order.business.enums;

/**
 * <AUTHOR>
 * @date 2022/8/4 18:54
 * @description
 */
public enum InWarehouseModeEnum {

    /**
     * 无需入库
     */
    NO_NEED(1, "无需入库"),

    /**
     * 待手动入库
     */
    WAITING_INBOUND(2, "待手动入库"),

    /**
     * 新库房系统自动入库
     */
    NEW_WAREHOUSE_SYSTEM_AUTO_INBOUND(3, "新库房系统自动入库"),

    /**
     * 新库房系统保存入库申请单
     */
    NEW_WAREHOUSE_SYSTEM_SAVE_APPLICATION_FORM(4, "新库房系统保存入库申请单");

    /**
     * 代码值
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    InWarehouseModeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
