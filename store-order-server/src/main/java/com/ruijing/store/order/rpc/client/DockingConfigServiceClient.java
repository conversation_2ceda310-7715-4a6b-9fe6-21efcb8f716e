package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.DockingConfigService;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.DockingConfigRequestDTO;
import com.reagent.order.dto.request.config.OrgDockingConfigQueryDTO;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/1 16:31
 * @description
 */
@ServiceClient
public class DockingConfigServiceClient {

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private DockingConfigService dockingConfigService;

    /**
     * 获取对接外部管理平台的单位列表
     * @param dockingTypeList 对接类型
     * @return 单位集合
     */
    @Deprecated
    public Set<String> getOuterBuyerSet(List<OuterBuyerDockingTypeEnum> dockingTypeList){
        DockingConfigRequestDTO dockingConfigRequestDTO = new DockingConfigRequestDTO();
        dockingConfigRequestDTO.setOuterBuyerDockingTypeList(dockingTypeList);
        return this.getOuterBuyerSet(dockingConfigRequestDTO);
    }

    /**
     * 获取对接外部管理平台的单位列表
     * @param dockingConfigRequestDTO 对接配置参数
     * @return 单位集合
     */
    @Deprecated
    public Set<String> getOuterBuyerSet(DockingConfigRequestDTO dockingConfigRequestDTO){
        RemoteResponse<Set<String>> response = dockingConfigService.getOuterBuyerSet(dockingConfigRequestDTO);
        Preconditions.isTrue(response.isSuccess(),"获取对接单位配置失败");
        return response.getData();
    }

    /**
     * 获取管理平台对接单位配置
     * @param orgCode 机构代码
     * @return 配置项
     */
    public OrgDockingConfigDTO getConfig(String orgCode){
        OrgDockingConfigQueryDTO orgDockingConfigQueryDTO = new OrgDockingConfigQueryDTO();
        orgDockingConfigQueryDTO.setOrgCode(orgCode);
        RemoteResponse<OrgDockingConfigDTO> orgDockingConfigDTO = dockingConfigService.getOuterBuyerDockingConfig(orgDockingConfigQueryDTO);
        Preconditions.isTrue(orgDockingConfigDTO.isSuccess(),"获取对接单位配置失败");
        return orgDockingConfigDTO.getData();
    }
}
