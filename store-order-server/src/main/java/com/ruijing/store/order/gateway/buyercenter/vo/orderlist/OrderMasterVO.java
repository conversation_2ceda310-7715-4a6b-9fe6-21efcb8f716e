package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignConfigVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/11/17 15:27
 * @Description
 **/
@RpcModel("订单管理-我的订单列表订单主表信息")
public class OrderMasterVO implements Serializable {

    private static final long serialVersionUID = 611902792253470617L;

    /**
     * 订单主键
     */
    @RpcModelProperty("订单主键")
    private Integer id;

    /**
     * 订单编号
     */
    @RpcModelProperty("订单编号")
    private String orderNo;

    /**
     * 采购申请单id
     */
    @RpcModelProperty("采购申请单id")
    private Integer buyAppId;

    /**
     * 采购申请单号
     */
    @RpcModelProperty("采购申请单号")
    private String buyAppNo;

    /**
     * 竞价单id
     */
    @RpcModelProperty("竞价单id")
    private String bidOrderId;

    /**
     * 竞价单号
     */
    @RpcModelProperty("竞价单号")
    private String bidOrderSn;

    /**
     * 订单日期
     */
    @RpcModelProperty("订单日期")
    private String orderDate;

    /**
     * 资产属性
     */
    @RpcModelProperty("资产属性")
    private Integer orderAttr;

    /**
     * 经费卡id 集合
     */
    @RpcModelProperty("经费卡id 集合")
    private List<String> fundCardIdList;

    /**
     * 经费卡信息，拼接的卡号卡名
     */
    @RpcModelProperty("经费卡信息，拼接的卡号卡名")
    private OrderFundcardVO fundCard;

    @RpcModelProperty("经费卡信息，不拼接，一张卡返回一条")
    private List<OrderFundcardVO> fundCardList;

    /**
     * 确认日期
     */
    @RpcModelProperty("确认日期")
    private String confirmDate;

    /**
     * 确认人姓名
     */
    @RpcModelProperty("确认人姓名")
    private String confirmMan;


    /**
     * 送货日期
     */
    @RpcModelProperty("送货日期")
    private String deliveryDate;


    /**
     * 收货日期
     */
    @RpcModelProperty("收货日期")
    private String lastReceiveDate;

    @RpcModelProperty("完成时间")
    private String finishDate;


    /**
     * 结算单创建日期
     */
    @RpcModelProperty("结算单创建日期")
    private String settleCreateDate;


    /**
     * 结算单完成日期
     */
    @RpcModelProperty("结算单完成日期")
    private String settleFinishDate;

    /**
     * 采购人Id
     */
    @RpcModelProperty("采购人Id")
    private Integer buyUserId;

    /**
     * 采购人
     */
    @RpcModelProperty("采购人")
    private String buyerName;


    /**
     * 采购部门
     */
    @RpcModelProperty("采购部门")
    private String departmentName;

    /**
     * 送货联系电话
     */
    @RpcModelProperty("送货联系电话")
    private String buyerTelephone;

    /**
     * 送货地址
     */
    @RpcModelProperty("送货地址")
    private String deliveryAddress;

    /**
     * 送货联系人
     */
    @RpcModelProperty("送货联系人")
    private String buyerContactMan;

    @RpcModelProperty("代配送员")
    private String deliveryProxyContactMan;

    @RpcModelProperty("代配送员电话")
    private String deliveryProxyContactPhone;

    /**
     * 订单状态
     */
    @RpcModelProperty("订单状态")
    private Integer status;

    /**
     * 外部供应商订单状态
     */
    @RpcModelProperty("外部供应商订单状态")
    private String outerSuppStatus;

    @RpcModelProperty("缺货")
    private Boolean lackOfGoods;

    /**
     * 经费状态
     */
    @RpcModelProperty("经费状态")
    private Integer fundStatus;

    /**
     * 经费释放失败原因
     */
    @RpcModelProperty("经费释放失败原因")
    private String  failedReason;

    /**
     * 总价
     */
    @RpcModelProperty("总价")
    private BigDecimal totalPrice;

    @RpcModelProperty("退货后成交额")
    private BigDecimal totalPriceAfterReturn;

    @RpcModelProperty("优惠总额")
    private BigDecimal discountAmount;

    /**
     * 供应商名称
     */
    @RpcModelProperty("供应商名称")
    private String supplierName;

    /**
     * 供应商代表法人
     */
    @RpcModelProperty("供应商代表法人")
    private String suppUser;

    /**
     * 供应商号码
     */
    @RpcModelProperty("供应商号码")
    private String suppTelephone;

    /**
     * 供应商qq
     */
    @RpcModelProperty("供应商qq")
    private String suppQQ;

    /**
     *项目号
     */
    @RpcModelProperty("项目号")
    private String[] projectNumbers;

    /**
     * 取消原因
     */
    @RpcModelProperty("取消原因")
    private String cancelReason;

    @RpcModelProperty("申请取消时间")
    private String cancelDate;

    /**
     * 总价格
     */
    @RpcModelProperty("总价格")
    private String wholePrice;

    /**
     * 验收金额
     */
    @RpcModelProperty("验收金额")
    private String lastPrice;

    /**
     * 能否验收  1 请别人验收  /0确认收货。
     */
    @RpcModelProperty("能否验收  1 请别人验收或者不显示验收按钮  /0确认收货或显示验收按钮")
    private Integer acceptance;

    @RpcModelProperty("验收按钮状态枚举值，0不显示/1显示正常/2显示置灰")
    private Integer acceptButtonStatus;

    @RpcModelProperty("验收按钮置灰提示")
    private String acceptButtonDisableHint;

    /**
     * 联系人手机号
     */
    @RpcModelProperty("联系人手机号")
    private String contactPhone;

    /**
     * 订单关闭日期
     */
    @RpcModelProperty("订单关闭日期")
    private String shutdownDate;

    /**
     * 供应商id
     */
    @RpcModelProperty("供应商id")
    private Integer supplierId;

    /**
     * 能否取消  1不能/0能
     */
    @RpcModelProperty("能否取消  1不能/0能")
    private Integer canCancel;

    /**
     * 孙逸仙医院图片
     */
    @RpcModelProperty("孙逸仙医院图片，暂未使用")
    private String urls;

    /**
     * 验收人
     */
    @RpcModelProperty("验收人")
    private String checkMan;

    /**
     * 改价前总价
     */
    @RpcModelProperty("改价前总价")
    private String beforeModifyPriceAll;

    /**
     * 订单类型
     */
    @RpcModelProperty("订单类型")
    private Integer orderType;

    /**
     * 发货人姓名
     */
    @RpcModelProperty("发货人姓名")
    private String deliveryName;

    /**
     * 发货人手机
     */
    @RpcModelProperty("发货人手机")
    private String deliveryMobile;

    /**
     * 入库状态
     */
    @RpcModelProperty("入库状态")
    private Integer inventoryStatus;

    /**
     * 入库驳回原因，目前只有广西肿瘤在用
     */
    @RpcModelProperty("入库驳回原因")
    private String warehouseRejectReason;

    /**
     * 能否入库
     */
    @RpcModelProperty("能否入库")
    private Boolean canInbound;

    /**
     * 结算单状态
     */
    @RpcModelProperty("结算单状态")
    private Integer statementStatus;

    /**
     * 订单验收描述
     */
    @RpcModelProperty("订单验收描述")
    private String orderReceiveDesc="";

    /**
     * 采购部门Id
     */
    @RpcModelProperty("采购部门Id")
    private Integer buyDepartmentId;

    /**
     * 部门guid
     */
    @RpcModelProperty("部门guid")
    private String deptGuid;

    /**
     * 线下单信息
     */
    @RpcModelProperty("线下单信息")
    private OrderOfflineInfoVO offlineInfo;

    /**
     * 单位名称
     */
    @RpcModelProperty("单位名称")
    private String orgName;

    /**
     * 单位id
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    /**
     * 采购给供应商的备注
     */
    @RpcModelProperty("采购给供应商的备注")
    private String orderRemark;

    /**
     * 中大关联关系
     */
    @RpcModelProperty("关联关系")
    private String relateInfo;

    /**
     * 订单确认图片
     */
    @RpcModelProperty("订单确认图片")
    private String confirmPics;


    /**
     * 订单确认备案追加图片
     */
    @RpcModelProperty("订单确认备案追加图片")
    private String confirmAddPics;

    /**
     * 评价状态 0待评价 1已评价 2无需评价
     */
    @RpcModelProperty("评价状态 0待评价 1已评价 2无需评价")
    private Integer commentStatus;

    /**
     * 是否超时 标志
     */
    @RpcModelProperty("是否超时 标志")
    private Boolean overTimeFlag = false;

    @RpcModelProperty("验收超时 标志")
    private Boolean overTimeAcceptance = false;

    @RpcModelProperty("结算超时 标志")
    private Boolean overTimeBalance = false;

    /**
     * 运费
     */
    @RpcModelProperty("运费")
    private BigDecimal totalCarryFee;

    /**
     * 对接状态
     */
    @RpcModelProperty("对接状态")
    private Integer dockingStatus;

    @RpcModelProperty("失败原因")
    private String dockingFailReason;

    /**
     * 采购课题组的学院名
     */
    @RpcModelProperty("采购课题组的学院名")
    private String collegeName;

    /**
     * 是否可以申请提交入库,0不可以，1可以
     */
    @RpcModelProperty("是否可以申请提交入库,0不可以，1可以")
    private Integer canSubmitWarehouse;

    /**
     * 是否可以打印出入库单,0不可以，1可以
     */
    @RpcModelProperty("是否可以打印出入库单,0不可以，1可以")
    private Integer canPrintInAndOutWarehouse;

    /**
     * 是否公示中（true | false）
     */
    @RpcModelProperty("是否公示中（true | false）")
    private boolean publicity;

    /**
     * 发票抬头id
     */
    @RpcModelProperty("发票抬头id")
    private Integer invoiceTitleId;

    /**
     * 线上单0还是线下单1
     */
    @RpcModelProperty("线上单0还是线下单1")
    private Integer species;

    /**
     * 进入待结算的时间
     */
    @RpcModelProperty("进入待结算的时间")
    private String waitForSettleTime;

    /**
     * 价格合同id
     */
    @RpcModelProperty("价格合同id")
    private Long contractId;

    /**
     * 价格合同号
     */
    @RpcModelProperty("价格合同号")
    private String contractNo;

    /**
     * 经费类型
     */
    @RpcModelProperty("经费类型")
    private String fundTypeName;

    @RpcModelProperty("是否开启代配送，true开启，false关闭")
    private Boolean deliveryProxyOn;

    @RpcModelProperty("配送类型")
    private Integer deliveryType;
    
    @RpcModelProperty(value = "代配送来源类型", enumLink = "com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum")
    private Integer deliveryProxySourceType;
    
    @RpcModelProperty(value = "取消代配送原因")
    private String cancelDeliveryProxyReason;

    @RpcModelProperty("代配送院区列表")
    private List<String> suppDeliveryProxyLabels;

    @RpcModelProperty("自定义的订单关联分类")
    private String selfDefCategory;
    
    @RpcModelProperty("订单推送状态列表")
    private List<OrderPushEventStatusVO> orderPushEventStatusList;

    @RpcModelProperty("优惠券码")
    private String couponCode;

    @RpcModelProperty("成功退货金额")
    private BigDecimal successfulReturnAmount;

    @RpcModelProperty("采购类型")
    private String purchaseType;
    
    @RpcModelProperty("当前验收审批等级")
    private Integer acceptApproveLevel;

    @RpcModelProperty("验收审批文案提示")
    private String acceptApprovalHint;

    @RpcModelProperty("是否订单完成后已经上传了文件")
    private Boolean uploadedAfterComplete = false;
    
    @RpcModelProperty("验收审批电子签名配置参数")
    private ElectronicSignConfigVO electronicSignConfigVO;

    @RpcModelProperty("是否试用订单")
    private Boolean trialOrder;

    /**
     * com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum#REMARK
     */
    @RpcModelProperty("用户自己填的备注")
    private String remark;

    @RpcModelProperty("预审批单号")
    private String procurementNo;

    @RpcModelProperty("结算单id")
    private Integer statementId;


    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getBuyAppId() {
        return buyAppId;
    }

    public void setBuyAppId(Integer buyAppId) {
        this.buyAppId = buyAppId;
    }

    public String getBuyAppNo() {
        return buyAppNo;
    }

    public void setBuyAppNo(String buyAppNo) {
        this.buyAppNo = buyAppNo;
    }

    public String getBidOrderId() {
        return bidOrderId;
    }

    public void setBidOrderId(String bidOrderId) {
        this.bidOrderId = bidOrderId;
    }

    public String getBidOrderSn() {
        return bidOrderSn;
    }

    public void setBidOrderSn(String bidOrderSn) {
        this.bidOrderSn = bidOrderSn;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public Integer getOrderAttr() {
        return orderAttr;
    }

    public void setOrderAttr(Integer orderAttr) {
        this.orderAttr = orderAttr;
    }

    public List<String> getFundCardIdList() {
        return fundCardIdList;
    }

    public void setFundCardIdList(List<String> fundCardIdList) {
        this.fundCardIdList = fundCardIdList;
    }

    public String getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(String confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getLastReceiveDate() {
        return lastReceiveDate;
    }

    public void setLastReceiveDate(String lastReceiveDate) {
        this.lastReceiveDate = lastReceiveDate;
    }

    public String getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(String finishDate) {
        this.finishDate = finishDate;
    }

    public String getSettleCreateDate() {
        return settleCreateDate;
    }

    public void setSettleCreateDate(String settleCreateDate) {
        this.settleCreateDate = settleCreateDate;
    }

    public String getSettleFinishDate() {
        return settleFinishDate;
    }

    public void setSettleFinishDate(String settleFinishDate) {
        this.settleFinishDate = settleFinishDate;
    }

    public Integer getBuyUserId() {
        return buyUserId;
    }

    public void setBuyUserId(Integer buyUserId) {
        this.buyUserId = buyUserId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone;
    }

    public String getFbiderdeliveryplace() {
        return deliveryAddress;
    }

    public void setFbiderdeliveryplace(String fbiderdeliveryplace) {
        this.deliveryAddress = fbiderdeliveryplace;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public void setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOuterSuppStatus() {
        return outerSuppStatus;
    }

    public OrderMasterVO setOuterSuppStatus(String outerSuppStatus) {
        this.outerSuppStatus = outerSuppStatus;
        return this;
    }

    public Boolean getLackOfGoods() {
        return lackOfGoods;
    }

    public OrderMasterVO setLackOfGoods(Boolean lackOfGoods) {
        this.lackOfGoods = lackOfGoods;
        return this;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSuppUser() {
        return suppUser;
    }

    public void setSuppUser(String suppUser) {
        this.suppUser = suppUser;
    }

    public String getSuppTelephone() {
        return suppTelephone;
    }

    public void setSuppTelephone(String suppTelephone) {
        this.suppTelephone = suppTelephone;
    }

    public String[] getProjectNumbers() {
        return projectNumbers;
    }

    public void setProjectNumbers(String[] projectNumbers) {
        this.projectNumbers = projectNumbers;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getWholePrice() {
        return wholePrice;
    }

    public void setWholePrice(String wholePrice) {
        this.wholePrice = wholePrice;
    }

    public String getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(String lastPrice) {
        this.lastPrice = lastPrice;
    }

    public Integer getAcceptance() {
        return acceptance;
    }

    public void setAcceptance(Integer acceptance) {
        this.acceptance = acceptance;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getShutdownDate() {
        return shutdownDate;
    }

    public void setShutdownDate(String shutdownDate) {
        this.shutdownDate = shutdownDate;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getCanCancel() {
        return canCancel;
    }

    public void setCanCancel(Integer canCancel) {
        this.canCancel = canCancel;
    }

    public String getUrls() {
        return urls;
    }

    public void setUrls(String urls) {
        this.urls = urls;
    }

    public String getCheckMan() {
        return checkMan;
    }

    public void setCheckMan(String checkMan) {
        this.checkMan = checkMan;
    }

    public String getBeforeModifyPriceAll() {
        return beforeModifyPriceAll;
    }

    public void setBeforeModifyPriceAll(String beforeModifyPriceAll) {
        this.beforeModifyPriceAll = beforeModifyPriceAll;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getDeliveryName() {
        return deliveryName;
    }

    public void setDeliveryName(String deliveryName) {
        this.deliveryName = deliveryName;
    }

    public String getDeliveryMobile() {
        return deliveryMobile;
    }

    public void setDeliveryMobile(String deliveryMobile) {
        this.deliveryMobile = deliveryMobile;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getWarehouseRejectReason() {
        return warehouseRejectReason;
    }

    public OrderMasterVO setWarehouseRejectReason(String warehouseRejectReason) {
        this.warehouseRejectReason = warehouseRejectReason;
        return this;
    }

    public Boolean getCanInbound() {
        return canInbound;
    }

    public void setCanInbound(Boolean canInbound) {
        this.canInbound = canInbound;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public String getOrderReceiveDesc() {
        return orderReceiveDesc;
    }

    public void setOrderReceiveDesc(String orderReceiveDesc) {
        this.orderReceiveDesc = orderReceiveDesc;
    }

    public Integer getBuyDepartmentId() {
        return buyDepartmentId;
    }

    public void setBuyDepartmentId(Integer buyDepartmentId) {
        this.buyDepartmentId = buyDepartmentId;
    }

    public String getDeptGuid() {
        return deptGuid;
    }

    public void setDeptGuid(String deptGuid) {
        this.deptGuid = deptGuid;
    }

    public OrderOfflineInfoVO getOfflineInfo() {
        return offlineInfo;
    }

    public void setOfflineInfo(OrderOfflineInfoVO offlineInfo) {
        this.offlineInfo = offlineInfo;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    public String getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(String relateInfo) {
        this.relateInfo = relateInfo;
    }

    public String getConfirmPics() {
        return confirmPics;
    }

    public void setConfirmPics(String confirmPics) {
        this.confirmPics = confirmPics;
    }

    public String getConfirmAddPics() {
        return confirmAddPics;
    }

    public void setConfirmAddPics(String confirmAddPics) {
        this.confirmAddPics = confirmAddPics;
    }

    public Integer getCommentStatus() {
        return commentStatus;
    }

    public void setCommentStatus(Integer commentStatus) {
        this.commentStatus = commentStatus;
    }

    public Boolean getOverTimeFlag() {
        return overTimeFlag;
    }

    public void setOverTimeFlag(Boolean overTimeFlag) {
        this.overTimeFlag = overTimeFlag;
    }

    public BigDecimal getTotalCarryFee() {
        return totalCarryFee;
    }

    public void setTotalCarryFee(BigDecimal totalCarryFee) {
        this.totalCarryFee = totalCarryFee;
    }

    public Integer getDockingStatus() {
        return dockingStatus;
    }

    public void setDockingStatus(Integer dockingStatus) {
        this.dockingStatus = dockingStatus;
    }

    public String getDockingFailReason() {
        return dockingFailReason;
    }

    public void setDockingFailReason(String dockingFailReason) {
        this.dockingFailReason = dockingFailReason;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public Integer getCanSubmitWarehouse() {
        return canSubmitWarehouse;
    }

    public void setCanSubmitWarehouse(Integer canSubmitWarehouse) {
        this.canSubmitWarehouse = canSubmitWarehouse;
    }

    public Integer getCanPrintInAndOutWarehouse() {
        return canPrintInAndOutWarehouse;
    }

    public void setCanPrintInAndOutWarehouse(Integer canPrintInAndOutWarehouse) {
        this.canPrintInAndOutWarehouse = canPrintInAndOutWarehouse;
    }

    public boolean isPublicity() {
        return publicity;
    }

    public void setPublicity(boolean publicity) {
        this.publicity = publicity;
    }

    public Integer getInvoiceTitleId() {
        return invoiceTitleId;
    }

    public void setInvoiceTitleId(Integer invoiceTitleId) {
        this.invoiceTitleId = invoiceTitleId;
    }

    public String getSuppQQ() {
        return suppQQ;
    }

    public void setSuppQQ(String suppQQ) {
        this.suppQQ = suppQQ;
    }

    public String getConfirmMan() {
        return confirmMan;
    }

    public OrderMasterVO setConfirmMan(String confirmMan) {
        this.confirmMan = confirmMan;
        return this;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public String getWaitForSettleTime() {
        return waitForSettleTime;
    }

    public void setWaitForSettleTime(String waitForSettleTime) {
        this.waitForSettleTime = waitForSettleTime;
    }

    public Long getContractId() {
        return contractId;
    }

    public OrderMasterVO setContractId(Long contractId) {
        this.contractId = contractId;
        return this;
    }

    public String getContractNo() {
        return contractNo;
    }

    public OrderMasterVO setContractNo(String contractNo) {
        this.contractNo = contractNo;
        return this;
    }

    public OrderFundcardVO getFundCard() {
        return fundCard;
    }

    public void setFundCard(OrderFundcardVO fundCard) {
        this.fundCard = fundCard;
    }

    public List<OrderFundcardVO> getFundCardList() {
        return fundCardList;
    }

    public OrderMasterVO setFundCardList(List<OrderFundcardVO> fundCardList) {
        this.fundCardList = fundCardList;
        return this;
    }

    public String getFundTypeName() {
        return fundTypeName;
    }

    public void setFundTypeName(String fundTypeName) {
        this.fundTypeName = fundTypeName;
    }

    public Integer getAcceptButtonStatus() {
        return acceptButtonStatus;
    }

    public OrderMasterVO setAcceptButtonStatus(Integer acceptButtonStatus) {
        this.acceptButtonStatus = acceptButtonStatus;
        return this;
    }

    public String getAcceptButtonDisableHint() {
        return acceptButtonDisableHint;
    }

    public OrderMasterVO setAcceptButtonDisableHint(String acceptButtonDisableHint) {
        this.acceptButtonDisableHint = acceptButtonDisableHint;
        return this;
    }

    public Boolean getOverTimeAcceptance() {
        return overTimeAcceptance;
    }

    public OrderMasterVO setOverTimeAcceptance(Boolean overTimeAcceptance) {
        this.overTimeAcceptance = overTimeAcceptance;
        return this;
    }

    public Boolean getOverTimeBalance() {
        return overTimeBalance;
    }

    public OrderMasterVO setOverTimeBalance(Boolean overTimeBalance) {
        this.overTimeBalance = overTimeBalance;
        return this;
    }

    public Boolean isDeliveryProxyOn() {
        return Boolean.TRUE.equals(deliveryProxyOn);
    }

    public void setDeliveryProxyOn(Boolean deliveryProxyOn) {
        this.deliveryProxyOn = deliveryProxyOn;
    }

    public Integer getDeliveryProxySourceType() {
        return deliveryProxySourceType;
    }

    public void setDeliveryProxySourceType(Integer deliveryProxySourceType) {
        this.deliveryProxySourceType = deliveryProxySourceType;
    }

    public String getCancelDeliveryProxyReason() {
        return cancelDeliveryProxyReason;
    }

    public void setCancelDeliveryProxyReason(String cancelDeliveryProxyReason) {
        this.cancelDeliveryProxyReason = cancelDeliveryProxyReason;
    }

    public List<String> getSuppDeliveryProxyLabels() {
        return suppDeliveryProxyLabels;
    }

    public void setSuppDeliveryProxyLabels(List<String> suppDeliveryProxyLabels) {
        this.suppDeliveryProxyLabels = suppDeliveryProxyLabels;
    }

    public String getSelfDefCategory() {
        return selfDefCategory;
    }

    public void setSelfDefCategory(String selfDefCategory) {
        this.selfDefCategory = selfDefCategory;
    }
    
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public BigDecimal getSuccessfulReturnAmount() {
        return successfulReturnAmount;
    }

    public void setSuccessfulReturnAmount(BigDecimal successfulReturnAmount) {
        this.successfulReturnAmount = successfulReturnAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getTotalPriceAfterReturn() {
        return totalPriceAfterReturn;
    }

    public void setTotalPriceAfterReturn(BigDecimal totalPriceAfterReturn) {
        this.totalPriceAfterReturn = totalPriceAfterReturn;
    }

    public String getDeliveryProxyContactMan() {
        return deliveryProxyContactMan;
    }

    public void setDeliveryProxyContactMan(String deliveryProxyContactMan) {
        this.deliveryProxyContactMan = deliveryProxyContactMan;
    }

    public String getDeliveryProxyContactPhone() {
        return deliveryProxyContactPhone;
    }

    public void setDeliveryProxyContactPhone(String deliveryProxyContactPhone) {
        this.deliveryProxyContactPhone = deliveryProxyContactPhone;
    }
    
    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Boolean getUploadedAfterComplete() {
        return uploadedAfterComplete;
    }

    public void setUploadedAfterComplete(Boolean uploadedAfterComplete) {
        this.uploadedAfterComplete = uploadedAfterComplete;
    }
    
    public List<OrderPushEventStatusVO> getOrderPushEventStatusList() {
        return orderPushEventStatusList;
    }

    public void setOrderPushEventStatusList(List<OrderPushEventStatusVO> orderPushEventStatusList) {
        this.orderPushEventStatusList = orderPushEventStatusList;
    }

    public Integer getAcceptApproveLevel() {
        return acceptApproveLevel;
    }

    public void setAcceptApproveLevel(Integer acceptApproveLevel) {
        this.acceptApproveLevel = acceptApproveLevel;
    }

    public String getAcceptApprovalHint() {
        return acceptApprovalHint;
    }

    public OrderMasterVO setAcceptApprovalHint(String acceptApprovalHint) {
        this.acceptApprovalHint = acceptApprovalHint;
        return this;
    }

    public ElectronicSignConfigVO getElectronicSignConfigVO() {
        return electronicSignConfigVO;
    }

    public void setElectronicSignConfigVO(ElectronicSignConfigVO electronicSignConfigVO) {
        this.electronicSignConfigVO = electronicSignConfigVO;
    }

    public Boolean getTrialOrder() {
        return trialOrder;
    }

    public void setTrialOrder(Boolean trialOrder) {
        this.trialOrder = trialOrder;
    }

    public String getRemark() {
        return remark;
    }

    public OrderMasterVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getProcurementNo() {
        return procurementNo;
    }

    public OrderMasterVO setProcurementNo(String procurementNo) {
        this.procurementNo = procurementNo;
        return this;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public Boolean getDeliveryProxyOn() {
        return deliveryProxyOn;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public OrderMasterVO setStatementId(Integer statementId) {
        this.statementId = statementId;
        return this;
    }


    @Override
    public String toString() {
        return "OrderMasterVO{" +
                "id=" + id +
                ", orderNo='" + orderNo + '\'' +
                ", buyAppId=" + buyAppId +
                ", buyAppNo='" + buyAppNo + '\'' +
                ", bidOrderId='" + bidOrderId + '\'' +
                ", bidOrderSn='" + bidOrderSn + '\'' +
                ", orderDate='" + orderDate + '\'' +
                ", orderAttr=" + orderAttr +
                ", fundCardIdList=" + fundCardIdList +
                ", fundCard=" + fundCard +
                ", fundCardList=" + fundCardList +
                ", confirmDate='" + confirmDate + '\'' +
                ", confirmMan='" + confirmMan + '\'' +
                ", deliveryDate='" + deliveryDate + '\'' +
                ", lastReceiveDate='" + lastReceiveDate + '\'' +
                ", finishDate='" + finishDate + '\'' +
                ", settleCreateDate='" + settleCreateDate + '\'' +
                ", settleFinishDate='" + settleFinishDate + '\'' +
                ", buyUserId=" + buyUserId +
                ", buyerName='" + buyerName + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", buyerTelephone='" + buyerTelephone + '\'' +
                ", deliveryAddress='" + deliveryAddress + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", deliveryProxyContactMan='" + deliveryProxyContactMan + '\'' +
                ", deliveryProxyContactPhone='" + deliveryProxyContactPhone + '\'' +
                ", status=" + status +
                ", outerSuppStatus='" + outerSuppStatus + '\'' +
                ", lackOfGoods=" + lackOfGoods +
                ", fundStatus=" + fundStatus +
                ", failedReason='" + failedReason + '\'' +
                ", totalPrice=" + totalPrice +
                ", totalPriceAfterReturn=" + totalPriceAfterReturn +
                ", discountAmount=" + discountAmount +
                ", supplierName='" + supplierName + '\'' +
                ", suppUser='" + suppUser + '\'' +
                ", suppTelephone='" + suppTelephone + '\'' +
                ", suppQQ='" + suppQQ + '\'' +
                ", projectNumbers=" + Arrays.toString(projectNumbers) +
                ", cancelReason='" + cancelReason + '\'' +
                ", cancelDate='" + cancelDate + '\'' +
                ", wholePrice='" + wholePrice + '\'' +
                ", lastPrice='" + lastPrice + '\'' +
                ", acceptance=" + acceptance +
                ", acceptButtonStatus=" + acceptButtonStatus +
                ", acceptButtonDisableHint='" + acceptButtonDisableHint + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", shutdownDate='" + shutdownDate + '\'' +
                ", supplierId=" + supplierId +
                ", canCancel=" + canCancel +
                ", urls='" + urls + '\'' +
                ", checkMan='" + checkMan + '\'' +
                ", beforeModifyPriceAll='" + beforeModifyPriceAll + '\'' +
                ", orderType=" + orderType +
                ", deliveryName='" + deliveryName + '\'' +
                ", deliveryMobile='" + deliveryMobile + '\'' +
                ", inventoryStatus=" + inventoryStatus +
                ", warehouseRejectReason='" + warehouseRejectReason + '\'' +
                ", canInbound=" + canInbound +
                ", statementStatus=" + statementStatus +
                ", orderReceiveDesc='" + orderReceiveDesc + '\'' +
                ", buyDepartmentId=" + buyDepartmentId +
                ", deptGuid='" + deptGuid + '\'' +
                ", offlineInfo=" + offlineInfo +
                ", orgName='" + orgName + '\'' +
                ", orgId=" + orgId +
                ", orderRemark='" + orderRemark + '\'' +
                ", relateInfo='" + relateInfo + '\'' +
                ", confirmPics='" + confirmPics + '\'' +
                ", confirmAddPics='" + confirmAddPics + '\'' +
                ", commentStatus=" + commentStatus +
                ", overTimeFlag=" + overTimeFlag +
                ", overTimeAcceptance=" + overTimeAcceptance +
                ", overTimeBalance=" + overTimeBalance +
                ", totalCarryFee=" + totalCarryFee +
                ", dockingStatus=" + dockingStatus +
                ", dockingFailReason='" + dockingFailReason + '\'' +
                ", collegeName='" + collegeName + '\'' +
                ", canSubmitWarehouse=" + canSubmitWarehouse +
                ", canPrintInAndOutWarehouse=" + canPrintInAndOutWarehouse +
                ", publicity=" + publicity +
                ", invoiceTitleId=" + invoiceTitleId +
                ", species=" + species +
                ", waitForSettleTime='" + waitForSettleTime + '\'' +
                ", contractId=" + contractId +
                ", contractNo='" + contractNo + '\'' +
                ", fundTypeName='" + fundTypeName + '\'' +
                ", deliveryProxyOn=" + deliveryProxyOn +
                ", deliveryType=" + deliveryType +
                ", deliveryProxySourceType=" + deliveryProxySourceType +
                ", cancelDeliveryProxyReason='" + cancelDeliveryProxyReason + '\'' +
                ", suppDeliveryProxyLabels=" + suppDeliveryProxyLabels +
                ", selfDefCategory='" + selfDefCategory + '\'' +
                ", orderPushEventStatusList=" + orderPushEventStatusList +
                ", couponCode='" + couponCode + '\'' +
                ", successfulReturnAmount=" + successfulReturnAmount +
                ", purchaseType='" + purchaseType + '\'' +
                ", acceptApproveLevel=" + acceptApproveLevel +
                ", acceptApprovalHint='" + acceptApprovalHint + '\'' +
                ", uploadedAfterComplete=" + uploadedAfterComplete +
                ", electronicSignConfigVO=" + electronicSignConfigVO +
                ", trialOrder=" + trialOrder +
                ", remark='" + remark + '\'' +
                ", procurementNo='" + procurementNo + '\'' +
                ", statementId='" + statementId + '\'' +
                '}';
    }
}
