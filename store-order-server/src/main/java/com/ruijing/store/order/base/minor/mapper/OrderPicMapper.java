package com.ruijing.store.order.base.minor.mapper;

import com.ruijing.store.order.base.minor.model.OrderPic;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: zhukai
 * CreateTime : 2019/12/5 10:33 上午
 * Description: ${description}
 */
public interface OrderPicMapper {

    /**
     * 根据订单号删除验收图片
     *
     * @param orderNo
     * @return
     */
    int deleteByOrderNo(@Param("orderNo") String orderNo);

    int deleteByPrimaryKey(Integer id);

    int insert(OrderPic record);

    int insertSelective(OrderPic record);

    OrderPic selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderPic record);

    int updateByPrimaryKey(OrderPic record);

    int batchInsert(@Param("list") List<OrderPic> list);

    List<OrderPic> selectByOrderNo(@Param("orderNo") String orderNo);

    List<OrderPic> batchSelectByOrderNo(@Param("orderNoList") List<String> orderNoList);

    int batchDeleteById(@Param("idList") List<Integer> idList);
}