package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.other.dto.DepartmentFreezeLogDTO;
import com.ruijing.store.order.api.base.other.service.DepartmentFreezeLogRPCService;
import com.ruijing.store.order.base.freezedeptlog.mapper.FreezeDeptLogDOMapper;
import com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO;
import com.ruijing.store.order.base.freezedeptlog.translator.FreezeDeptLogTranslator;
import com.ruijing.order.annotation.ServiceLog;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 冻结课题组RPC实现，最后会交给基础架构组收归
 * @author: zhong<PERSON><PERSON>i
 * @create: 2020/12/31 15:49
 **/
@MSharpService
public class DepartmentFreezeLogRPCServiceImpl implements DepartmentFreezeLogRPCService {

    @Resource
    private FreezeDeptLogDOMapper freezeDeptLogDOMapper;

    @Override
    @ServiceLog(description = "按医院id，获取由被冻结的部门")
    public RemoteResponse<List<DepartmentFreezeLogDTO>> findFreezeByOrgId(Integer orgId) {
        Preconditions.notNull(orgId, "查询失败！orgId不可为空！");
        List<FreezeDeptLogDO> collect = freezeDeptLogDOMapper.findByOrgId(orgId);
        List<DepartmentFreezeLogDTO> result = collect.stream().map(FreezeDeptLogTranslator::doToDepartmentFreezeLogDTO).collect(Collectors.toList());
        return RemoteResponse.<List<DepartmentFreezeLogDTO>>custom().setSuccess().setData(result);
    }
}
