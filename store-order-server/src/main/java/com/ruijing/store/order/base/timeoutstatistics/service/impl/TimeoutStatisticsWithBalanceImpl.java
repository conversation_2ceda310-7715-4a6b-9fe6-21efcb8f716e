package com.ruijing.store.order.base.timeoutstatistics.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.ordermaster.dto.GoodsReturnParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import com.ruijing.store.order.base.freezedeptlog.enums.IsDeletedEnum;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.timeoutstatistics.constant.TimeOutStatisticsServiceConstant;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: zhongyulei
 * @create: 2019/11/28 10:30
 * @description: 统计数据和取消退货相关业务类
 * 2023-02-21修改：
 * 新增验收审批/入库超时部分（归在结算里面）的修改，并保证验收审批/入库超时必须依附于结算超时数据。
 * 规则：
 * 1.有结算超时数据，验收审批/入库超时数据才有意义（才使用）
 * 2.结算超时数据减少时，触发小于阈值导致被删除后，验收审批/入库超时数据也一并删除
 * 3.结算超时数据增加时，如果已有结算超时数据，则增加验收审批/入库超时数据（有则update，无则insert）。
 * 否则判断是否超过阈值，超过则插入结算超时数据与验收审批/入库超时数据（有的话）
 * 4.除取消退货（这个功能也会影响结算超时数据），只会减少验收审批/入库超时数据。如果没有结算超时数据就不生效。
 **/
@ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
@Service(TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_BALANCE)
public class TimeoutStatisticsWithBalanceImpl extends AbstractTimeoutStatisticsServiceImpl {
    @Resource
    private FreezeDeptLogService freezeDeptLogService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean executeTimeOutStatisticsIncrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap) {
        if (count == null || count == 0) {
            return false;
        }
        // 统计过滤：如果为南方医科大学 订单验收超时需求定制，订单结算方式为“课题组自结算”时，不计入超时设置统计
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(params.getOrderMasterId());
        if (OrgEnum.NAN_FANG_YI_KE.getCode().equals(orderMasterDO.getFusercode()) && OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus())) {
            return false;
        }
        // 进行统计处理
        TimeoutStatisticsDTO balanceStatistics = findByDepartmentIdAndType(params.getOrganizationId(), params.getDepartmentId(), TimeOutBusinessType.BALANCE.getValue());
        Integer timeOutCount = balanceStatistics.getAmount();
        // 是否使用库房系统
        boolean useWarehouseSystem = Integer.valueOf(ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE).equals(timeOutConfigMap.get(ConfigConstant.USE_WAREHOUSE_SYSTEM));

        // 如果超时订单统计数 已存在且大于0
        if (timeOutCount != null && timeOutCount > 0) {
            logger.info("orgId:{},deptId:{},结算超时统计数量原数量{}，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), timeOutCount, count);
            this.updateTimeoutBalanceAfterReturn(count, orderMasterDO, timeOutConfigMap, balanceStatistics.getId());
            return false;
        } else {
            int balanceLimitDays = timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode());
            // 去ES查一下结算超时订单数据
            TimeOutOrderParamsDTO timeoutQryParam = new TimeOutOrderParamsDTO();
            timeoutQryParam.setUserId(params.getOrganizationId());
            timeoutQryParam.setDepartmentIds(New.list(params.getDepartmentId()));
            timeoutQryParam.setPageNo(0);
            timeoutQryParam.setPageSize(0);
            timeoutQryParam.setOverTimeType(TimeOutEnums.BALANCE.getType());
            Long balanceTimeOutCount = timeoutQueryService.queryTimeOutOrder(timeoutQryParam, balanceLimitDays, 0).getTotalHits();
            timeOutCount = balanceTimeOutCount.intValue();

            // 此时统计数大于配置超时单个数, 插入超时结算统计数据
            if (timeOutCount >= timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())) {
                logger.info("orgId:{},deptId:{},结算超时统计数量为空，查出数量0，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), timeOutCount);
                // 验收审批/入库超时数据
                long accOrWhTimeOutCount = 0;
                accOrWhTimeOutCount = timeoutQueryService.queryTimeOutAccOrWhOrder(timeoutQryParam, balanceLimitDays, useWarehouseSystem).getTotalHits();
                int finalAccOrWhTimeOutCount = (int) accOrWhTimeOutCount;

                // 开始事务
                Integer finalTimeOutCount = timeOutCount;
                transactionTemplate.execute(transactionStatus -> {
                    // 添加统计数据
                    Date nowDate = new Date();
                    // 验收超时部分
                    List<TimeoutStatisticsDTO> timeoutStatisticsDTOList = New.list(
                            new TimeoutStatisticsDTO(
                                    params.getOrganizationId(),
                                    params.getDepartmentId(),
                                    TimeOutBusinessType.BALANCE.getValue(),
                                    finalTimeOutCount,
                                    nowDate,
                                    timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()),
                                    timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())
                            )
                    );
                    if (finalAccOrWhTimeOutCount > 0) {
                        logger.info("orgId:{},deptId:{},验收审批/入库超时统计数量为空，查出数量0，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), finalAccOrWhTimeOutCount);
                        // 如有超时验收或入库超时部分 也插入
                        timeoutStatisticsDTOList.add(new TimeoutStatisticsDTO(
                                orderMasterDO.getFuserid(),
                                orderMasterDO.getFbuydepartmentid(),
                                TimeOutBusinessType.ACCEPT_APPROVE_OR_WAREHOUSE.getValue(),
                                finalAccOrWhTimeOutCount,
                                new Date(),
                                timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()),
                                timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())
                        ));
                    }

                    insertBatch(timeoutStatisticsDTOList);

                    // 冻结课题组
                    freezeDeptLogService.insertList(New.list(
                            new FreezeDeptLogDTO(
                                    params.getOrganizationId(),
                                    params.getDepartmentId(),
                                    TimeOutBusinessType.BALANCE.getValue(),
                                    IsDeletedEnum.NOT_DELETED.getCode(),
                                    nowDate
                            )
                    ));
                    return transactionStatus.isCompleted();
                });
            }
        }

        return true;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean executeTimeOutStatisticsDecrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap) {
        return this.executeTimeOutStatisticsDecrease(count, params, timeOutConfigMap, false);
    }

    /**
     * 执行减少结算超时统计
     *
     * @param count            减少数量
     * @param params           参数
     * @param timeOutConfigMap 配置数据
     * @param isApplyReturn    是否退货引起的
     * @return 减少后是否没有超过阈值
     */
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean executeTimeOutStatisticsDecrease(Integer count, GoodsReturnParamDTO params, Map<String, Integer> timeOutConfigMap, boolean isApplyReturn) {
        boolean isLessThanConfigBalanceAmount = false;
        List<TimeoutStatisticsDO> statisticsList = timeoutStatisticsMapper.queryByOrgIdAndDepIdIn(params.getOrganizationId(), New.list(params.getDepartmentId()));
        // 结算超时
        TimeoutStatisticsDO balanceStatistic = statisticsList.stream().filter(timeoutStatisticsDO -> TimeOutBusinessType.BALANCE.getValue().equals(timeoutStatisticsDO.getType())).findFirst().orElse(null);
        if (balanceStatistic == null) {
            return true;
        }
        // 验收审批/入库超时
        TimeoutStatisticsDO accOrWhStatic = statisticsList.stream().filter(timeoutStatisticsDO -> TimeOutBusinessType.ACCEPT_APPROVE_OR_WAREHOUSE.getValue().equals(timeoutStatisticsDO.getType())).findFirst().orElse(null);
        Integer balanceTimeOutCount = balanceStatistic.getAmount();
        if (balanceTimeOutCount != null && balanceTimeOutCount > 0) {
            if (count != null && count > 0) {
                // 超时结算单退货后, 结算超时统计数 - count
                int initAmount = balanceTimeOutCount;
                balanceTimeOutCount -= count;
                if (balanceTimeOutCount < timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())) {
                    logger.info("orgId:{},deptId:{},结算超时统计数量，原数量{},减少{}，小于阈值{},将清空", params.getOrganizationId(), params.getDepartmentId(), initAmount, count, timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode()));
                    // 如果此时统计数小于配置超时单张数
                    List<Long> idListToDelete = New.list(balanceStatistic.getId());
                    if (accOrWhStatic != null) {
                        // 删除依附于结算超时的验收审批/入库超时项，保证没有结算超时的时候也没有验收审批/入库超时
                        idListToDelete.add(accOrWhStatic.getId());
                    }
                    timeoutStatisticsMapper.batchDeleteById(idListToDelete);
                    isLessThanConfigBalanceAmount = true;
                } else {
                    int changeCount = -1 * count;
                    logger.info("orgId:{},deptId:{},结算超时统计数量，原数量{},减少{}", params.getOrganizationId(), params.getDepartmentId(), initAmount, count);
                    if (isApplyReturn) {
                        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(params.getOrderMasterId());
                        this.updateTimeoutBalanceAfterReturn(changeCount, orderMasterDO, timeOutConfigMap, balanceStatistic.getId());
                    } else {
                        timeoutStatisticsMapper.addAmountById(balanceStatistic.getId(), changeCount);
                    }
                }
            }
        } else {
            // 否则没有超过阈值
            isLessThanConfigBalanceAmount = true;
        }

        return isLessThanConfigBalanceAmount;
    }

    private void updateTimeoutBalanceAfterReturn(int changeCount, OrderMasterDO orderMasterDO, Map<String, Integer> timeOutConfigMap, long balanceStatisticId) {
        // 是否使用库房系统
        boolean useWarehouseSystem = Integer.valueOf(ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE).equals(timeOutConfigMap.get(ConfigConstant.USE_WAREHOUSE_SYSTEM));
        boolean isAccOrWhTimeOut = OrderCommonUtils.isNotAcceptApproveOrWareHouse(orderMasterDO.getStatus(), orderMasterDO.getInventoryStatus().intValue(), useWarehouseSystem);
        transactionTemplate.execute(transactionStatus -> {
            timeoutStatisticsMapper.addAmountById(balanceStatisticId, changeCount);
            if (isAccOrWhTimeOut) {
                // 是退货申请导致的超时数据减少且当前订单为验收审批/入库超时导致的结算超时，则也减少验收审批/入库超时计数
                this.executeAcceptApproveOrWareHouseStatisticChange(changeCount, orderMasterDO, timeOutConfigMap);
            }
            return transactionStatus.isCompleted();
        });
    }

    /**
     * 验收审批通过/入库完成，减少验收审批/入库统计数量。只能做粗略计算
     *
     * @param count         减少数量
     * @param orderMasterDO 订单数据
     */
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void executeAcceptApproveOrWareHouseStatisticChange(int count, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        Map<String, Integer> timeOutConfigMap = orderManageService.getTimeOutConfigMap(orderMasterDO.getFusercode());
        if (orderDetailDOList == null) {
            orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        }
        // 订单是否结算超时且无退货商品，是则处理
        boolean isBalanceTimeOut = OrderCommonUtils.isStatementTimeOut(orderMasterDO, orderDetailDOList, timeOutConfigMap);
        if (!isBalanceTimeOut) {
            return;
        }
        this.executeAcceptApproveOrWareHouseStatisticChange(count, orderMasterDO, timeOutConfigMap);
    }

    private void executeAcceptApproveOrWareHouseStatisticChange(int count, OrderMasterDO orderMasterDO, Map<String, Integer> timeOutConfigMap) {
        List<TimeoutStatisticsDO> statisticsList = timeoutStatisticsMapper.queryByOrgIdAndDepIdIn(orderMasterDO.getFuserid(), New.list(orderMasterDO.getFbuydepartmentid()));
        TimeoutStatisticsDO balanceStatic = statisticsList.stream().filter(timeoutStatisticsDO -> TimeOutBusinessType.BALANCE.getValue().equals(timeoutStatisticsDO.getType())).findFirst().orElse(null);
        TimeoutStatisticsDO accOrWhStatic = statisticsList.stream().filter(timeoutStatisticsDO -> TimeOutBusinessType.ACCEPT_APPROVE_OR_WAREHOUSE.getValue().equals(timeoutStatisticsDO.getType())).findFirst().orElse(null);
        // 没有更新数量 或没有该项 或该项条目为0，则不处理
        if (count == 0 || balanceStatic == null) {
            return;
        }
        if (count > 0) {
            // 此处是仅有取消退货且已有结算超时数据才会调用
            if (accOrWhStatic == null) {
                logger.info("orgId:{},deptId:{},验收审批/入库超时统计数量为空，查出数量0，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), count);
                // 没数据 且为新增 插入
                insertBatch(New.list(
                        new TimeoutStatisticsDTO(
                                orderMasterDO.getFuserid(),
                                orderMasterDO.getFbuydepartmentid(),
                                TimeOutBusinessType.ACCEPT_APPROVE_OR_WAREHOUSE.getValue(),
                                count,
                                new Date(),
                                timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()),
                                timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode())
                        )
                ));
            } else {
                logger.info("orgId:{},deptId:{},验收审批/入库超时统计数量原数量为{}，将增加{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), accOrWhStatic.getAmount(), count);
                // 有数据则更新
                timeoutStatisticsMapper.addAmountById(accOrWhStatic.getId(), count);
            }
            return;
        }
        // 其余的,做减法且数量不为0的，走更新
        if (accOrWhStatic != null && accOrWhStatic.getAmount() != 0) {
            logger.info("orgId:{},deptId:{},验收审批/入库超时统计数量原数量为{}，将减少{}", orderMasterDO.getFuserid(), orderMasterDO.getFbuydepartmentid(), accOrWhStatic.getAmount(), count);
            timeoutStatisticsMapper.addAmountById(accOrWhStatic.getId(), count);
        }
    }
}
