package com.ruijing.store.order.base.minor.translator;

import com.ruijing.store.order.api.base.other.dto.ThirdPartyCallbackInfoDTO;
import com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO;

/**
 * @description: 第三方回调信息表转换类
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/9/30 15:06
 **/
public class ThirdPartyCallbackInfoTranslator {

    public static ThirdPartyCallbackInfoDO dto2DO(ThirdPartyCallbackInfoDTO dto) {
        ThirdPartyCallbackInfoDO info = new ThirdPartyCallbackInfoDO();
        info.setId(dto.getId());
        info.setCreateTime(dto.getCreateTime());
        info.setUpdateTime(dto.getUpdateTime());
        info.setOrgcode(dto.getOrgcode());
        info.setNum(dto.getNum());
        info.setMessage(dto.getMessage());

        return info;
    }
}
