<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.GoodsReturnMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.GoodsReturn">
    <!--@mbg.generated-->
    <!--@Table t_goods_return-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="return_reason" jdbcType="VARCHAR" property="returnReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="quantity" jdbcType="DOUBLE" property="quantity" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="detail_id" jdbcType="INTEGER" property="detailId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="goods_return_status" jdbcType="INTEGER" property="goodsReturnStatus" />
    <result column="return_no" jdbcType="VARCHAR" property="returnNo" />
    <result column="reply_time" jdbcType="TIMESTAMP" property="replyTime" />
    <result column="return_time" jdbcType="TIMESTAMP" property="returnTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
    <result column="apply_name" jdbcType="VARCHAR" property="applyName" />
    <result column="picth" jdbcType="VARCHAR" property="picth" />
    <result column="agree_reason" jdbcType="VARCHAR" property="agreeReason" />

    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
      <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
      <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
      <result column="department_id" jdbcType="INTEGER" property="departmentId" />
      <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
      <result column="product_path" jdbcType="VARCHAR" property="productPath" />
      <result column="invalid" jdbcType="INTEGER" property="invalid" />
      <result column="goods_return_detail_json" jdbcType="VARCHAR" property="goodsReturnDetailJSON" />
      <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
      <result column="delay_accept_count" jdbcType="INTEGER" property="delayAcceptCount"/>
  </resultMap>

    <resultMap id="GoodsReturnCountDO" type="com.ruijing.store.order.base.core.model.GoodsReturnCountDO">
        <result column="goods_return_status" jdbcType="INTEGER" property="goodsReturnStatus" />
        <result column="count" jdbcType="INTEGER" property="count" />
    </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, creation_time, update_time, return_reason, remark, quantity, price, detail_id,
    user_id, supplier_id, supplier_name, goods_return_status, return_no, reply_time,
    return_time, receive_time, cancel_time, refuse_reason, apply_name, picth, agree_reason,
      order_id, org_id, org_name, order_no, order_status, department_id, department_name, product_path, invalid, goods_return_detail_json, buyer_name, delay_accept_count
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from t_goods_return
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_goods_return
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.GoodsReturn" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_goods_return (creation_time, update_time, return_reason,
      remark, quantity, price,
      detail_id, user_id, supplier_id,
      supplier_name, goods_return_status, return_no,
      reply_time, return_time, receive_time,
      cancel_time, refuse_reason, apply_name,
      picth, agree_reason, order_id, org_id, org_name, order_no, order_status,
      department_id, department_name, product_path, invalid, goods_return_detail_json, buyer_name
      )
    values (#{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{returnReason,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{quantity,jdbcType=DOUBLE}, #{price,jdbcType=DOUBLE},
      #{detailId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER},
      #{supplierName,jdbcType=VARCHAR}, #{goodsReturnStatus,jdbcType=INTEGER}, #{returnNo,jdbcType=VARCHAR},
      #{replyTime,jdbcType=TIMESTAMP}, #{returnTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP},
      #{cancelTime,jdbcType=TIMESTAMP}, #{refuseReason,jdbcType=VARCHAR}, #{applyName,jdbcType=VARCHAR},
      #{picth,jdbcType=VARCHAR}, #{agreeReason,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER},
      #{orgId,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, #{departmentId,jdbcType=INTEGER},
      #{departmentName,jdbcType=VARCHAR}, #{productPath,jdbcType=VARCHAR}, #{invalid,jdbcType=INTEGER}, #{goodsReturnDetailJSON,jdbcType=VARCHAR}, #{buyerName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.GoodsReturn" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_goods_return
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="returnReason != null">
        return_reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="goodsReturnStatus != null">
        goods_return_status,
      </if>
      <if test="returnNo != null">
        return_no,
      </if>
      <if test="replyTime != null">
        reply_time,
      </if>
      <if test="returnTime != null">
        return_time,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="applyName != null">
        apply_name,
      </if>
      <if test="picth != null">
        picth,
      </if>
      <if test="agreeReason != null">
        agree_reason,
      </if>
        <if test="orderId != null">
            order_id,
        </if>
        <if test="orgId != null">
            org_id,
        </if>
        <if test="orgName != null">
            org_name,
        </if>
        <if test="orderNo != null">
            order_no,
        </if>
        <if test="orderStatus != null">
            order_status,
        </if>
        <if test="departmentId != null">
            department_id,
        </if>
        <if test="departmentName != null">
            department_name,
        </if>
        <if test="productPath != null">
            product_path,
        </if>
        <if test="invalid != null">
            invalid,
        </if>
        <if test="goodsReturnDetailJSON != null and goodsReturnDetailJSON != ''">
            goods_return_detail_json,
        </if>
        <if test="buyerName != null">
            buyer_name,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnReason != null">
        #{returnReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="detailId != null">
        #{detailId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="goodsReturnStatus != null">
        #{goodsReturnStatus,jdbcType=INTEGER},
      </if>
      <if test="returnNo != null">
        #{returnNo,jdbcType=VARCHAR},
      </if>
      <if test="replyTime != null">
        #{replyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTime != null">
        #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="applyName != null">
        #{applyName,jdbcType=VARCHAR},
      </if>
      <if test="picth != null">
        #{picth,jdbcType=VARCHAR},
      </if>
      <if test="agreeReason != null">
        #{agreeReason,jdbcType=VARCHAR},
      </if>

        <if test="orderId != null">
            #{orderId,jdbcType=INTEGER},
        </if>
        <if test="orgId != null">
            #{orgId,jdbcType=INTEGER},
        </if>
        <if test="orgName != null">
            #{orgName,jdbcType=VARCHAR},
        </if>
        <if test="orderNo != null">
            #{orderNo,jdbcType=VARCHAR},
        </if>
        <if test="orderStatus != null">
            #{orderStatus,jdbcType=INTEGER},
        </if>
        <if test="departmentId != null">
            #{departmentId,jdbcType=INTEGER},
        </if>
        <if test="departmentName != null">
            #{departmentName,jdbcType=VARCHAR},
        </if>
        <if test="productPath != null">
            #{productPath,jdbcType=VARCHAR},
        </if>
        <if test="invalid != null">
            #{invalid,jdbcType=INTEGER},
        </if>
        <if test="goodsReturnDetailJSON != null and goodsReturnDetailJSON != ''">
            #{goodsReturnDetailJSON,jdbcType=VARCHAR},
        </if>
        <if test="buyerName != null">
            #{buyerName,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.GoodsReturn">
    <!--@mbg.generated-->
    update t_goods_return
    <set>
      <if test="returnReason != null">
        return_reason = #{returnReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="detailId != null">
        detail_id = #{detailId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="goodsReturnStatus != null">
        goods_return_status = #{goodsReturnStatus,jdbcType=INTEGER},
      </if>
      <if test="returnNo != null">
        return_no = #{returnNo,jdbcType=VARCHAR},
      </if>
      <if test="replyTime != null">
        reply_time = #{replyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTime != null">
        return_time = #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="applyName != null">
        apply_name = #{applyName,jdbcType=VARCHAR},
      </if>
      <if test="picth != null">
        picth = #{picth,jdbcType=VARCHAR},
      </if>
      <if test="agreeReason != null">
        agree_reason = #{agreeReason,jdbcType=VARCHAR},
      </if>

        <if test="orderId != null">
            order_id = #{orderId,jdbcType=INTEGER},
        </if>
        <if test="orgId != null">
            org_id = #{orgId,jdbcType=INTEGER},
        </if>
        <if test="orgName != null">
            org_name = #{orgName,jdbcType=VARCHAR},
        </if>
        <if test="orderNo != null">
            order_no = #{orderNo,jdbcType=VARCHAR},
        </if>
        <if test="orderStatus != null">
            order_status = #{orderStatus,jdbcType=INTEGER},
        </if>
        <if test="departmentId != null">
            department_id = #{departmentId,jdbcType=INTEGER},
        </if>
        <if test="productPath != null">
            product_path = #{productPath,jdbcType=VARCHAR},
        </if>
        <if test="invalid != null">
            invalid = #{invalid,jdbcType=INTEGER},
        </if>
        <if test="goodsReturnDetailJSON != null and goodsReturnDetailJSON != ''">
            goods_return_detail_json = #{goodsReturnDetailJSON,jdbcType=VARCHAR},
        </if>
        <if test="buyerName != null">
            buyer_name = #{buyerName,jdbcType=VARCHAR},
        </if>
        <if test="delayAcceptCount != null">
            delay_accept_count = #{delayAcceptCount,jdbcType=INTEGER},
        </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.core.model.GoodsReturn">
    <!--@mbg.generated-->
    update t_goods_return
    set return_reason = #{returnReason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DOUBLE},
      price = #{price,jdbcType=DOUBLE},
      detail_id = #{detailId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      goods_return_status = #{goodsReturnStatus,jdbcType=INTEGER},
      return_no = #{returnNo,jdbcType=VARCHAR},
      reply_time = #{replyTime,jdbcType=TIMESTAMP},
      return_time = #{returnTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      apply_name = #{applyName,jdbcType=VARCHAR},
      picth = #{picth,jdbcType=VARCHAR},
      agree_reason = #{agreeReason,jdbcType=VARCHAR},

      order_id = #{orderId,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=INTEGER},
      org_name = #{orgName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      department_id = #{departmentId,jdbcType=INTEGER},
      department_name = #{departmentName,jdbcType=VARCHAR},
      product_path = #{productPath,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=INTEGER},
      goods_return_detail_json = #{goodsReturnDetailJSON,jdbcType=VARCHAR},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      delay_accept_count = #{delayAcceptCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--auto generated by MybatisCodeHelper on 2019-11-14-->
  <select id="findRangeTimeReturnOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_goods_return
    where ((creation_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and creation_time <![CDATA[<=]]>
    #{endTime,jdbcType=TIMESTAMP})
    or
    (update_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} and update_time <![CDATA[<=]]>
    #{endTime,jdbcType=TIMESTAMP})
    )
  </select>

<!--auto generated by MybatisCodeHelper on 2019-12-10-->
  <select id="findByDetailId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where detail_id=#{detailId,jdbcType=INTEGER}
        order by id desc
    </select>

<!--auto generated by MybatisCodeHelper on 2020-07-20-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_goods_return(
        creation_time,
        update_time,
        return_reason,
        remark,
        quantity,
        price,
        detail_id,
        user_id,
        supplier_id,
        supplier_name,
        goods_return_status,
        return_no,
        reply_time,
        return_time,
        receive_time,
        cancel_time,
        refuse_reason,
        apply_name,
        picth,
        agree_reason,
        order_id,
        org_id,
        org_name,
        order_no,
        order_status,
        department_id,
        department_name,
        product_path,
        invalid,
        goods_return_detail_json,
        buyer_name

        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.creationTime,jdbcType=TIMESTAMP},
            #{element.updateTime,jdbcType=TIMESTAMP},
            <choose>
                <when test="element.returnReason != null ">
                    #{element.returnReason,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.remark != null ">
                    #{element.remark,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.quantity != null ">
                    #{element.quantity,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            <choose>
                <when test="element.price != null ">
                    #{element.price,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            #{element.detailId,jdbcType=INTEGER},
            #{element.userId,jdbcType=INTEGER},
            <choose>
                <when test="element.supplierId != null ">
                    #{element.supplierId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.supplierName != null ">
                    #{element.supplierName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            #{element.goodsReturnStatus,jdbcType=INTEGER},
            <choose>
                <when test="element.returnNo != null ">
                    #{element.returnNo,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            #{element.replyTime,jdbcType=TIMESTAMP},
            #{element.returnTime,jdbcType=TIMESTAMP},
            #{element.receiveTime,jdbcType=TIMESTAMP},
            #{element.cancelTime,jdbcType=TIMESTAMP},
            #{element.refuseReason,jdbcType=VARCHAR},
            #{element.applyName,jdbcType=VARCHAR},
            #{element.picth,jdbcType=VARCHAR},
            #{element.agreeReason,jdbcType=VARCHAR},

            <choose>
                <when test="element.orderId != null ">
                    #{element.orderId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    -1,
                </otherwise>
            </choose>
            <choose>
                <when test="element.orgId != null ">
                    #{element.orgId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    -1,
                </otherwise>
            </choose>
            #{element.orgName,jdbcType=VARCHAR},
            <choose>
                <when test="element.orderNo != null ">
                    #{element.orderNo,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.orderStatus != null ">
                    #{element.orderStatus,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.departmentId != null ">
                    #{element.departmentId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            #{element.departmentName,jdbcType=VARCHAR},
            <choose>
                <when test="element.productPath != null ">
                    #{element.productPath,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.invalid != null ">
                    #{element.invalid,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.goodsReturnDetailJSON != null ">
                    #{element.goodsReturnDetailJSON,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            #{element.buyerName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

  <!--auto generated by MybatisCodeHelper on 2019-11-26-->
  <select id="selectOneByReturnNoLike" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_goods_return
    where return_no like concat(#{likeReturnNo,jdbcType=VARCHAR},'%')
    order by creation_time desc limit 1
  </select>

  <!--auto generated by MybatisCodeHelper on 2020-03-04-->
  <select id="findByDetailIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_goods_return
    where detail_id in
    <foreach item="item" index="index" collection="detailIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2020-09-08-->
  <update id="updateByDetailIdIn">
        update t_goods_return
        <set>
            <if test="updated.creationTime != null">
                creation_time = #{updated.creationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.returnReason != null">
                return_reason = #{updated.returnReason,jdbcType=VARCHAR},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.quantity != null">
                quantity = #{updated.quantity,jdbcType=DOUBLE},
            </if>
            <if test="updated.price != null">
                price = #{updated.price,jdbcType=DOUBLE},
            </if>
            <if test="updated.userId != null">
                user_id = #{updated.userId,jdbcType=INTEGER},
            </if>
            <if test="updated.supplierId != null">
                supplier_id = #{updated.supplierId,jdbcType=INTEGER},
            </if>
            <if test="updated.supplierName != null">
                supplier_name = #{updated.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="updated.goodsReturnStatus != null">
                goods_return_status = #{updated.goodsReturnStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.returnNo != null">
                return_no = #{updated.returnNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.replyTime != null">
                reply_time = #{updated.replyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.returnTime != null">
                return_time = #{updated.returnTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.receiveTime != null">
                receive_time = #{updated.receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.cancelTime != null">
                cancel_time = #{updated.cancelTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.refuseReason != null">
                refuse_reason = #{updated.refuseReason,jdbcType=VARCHAR},
            </if>
            <if test="updated.applyName != null">
                apply_name = #{updated.applyName,jdbcType=VARCHAR},
            </if>
            <if test="updated.picth != null">
                picth = #{updated.picth,jdbcType=VARCHAR},
            </if>
            <if test="updated.agreeReason != null">
                agree_reason = #{updated.agreeReason,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderId != null">
                order_id = #{updated.orderId,jdbcType=INTEGER},
            </if>
            <if test="updated.orgId != null">
                org_id = #{updated.orgId,jdbcType=INTEGER},
            </if>
            <if test="updated.orgName != null">
                org_name = #{updated.orgName,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderNo != null">
                order_no = #{updated.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderStatus != null">
                order_status = #{updated.orderStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.departmentId != null">
                department_id = #{updated.departmentId,jdbcType=INTEGER},
            </if>
            <if test="updated.departmentName != null">
                department_name = #{updated.departmentName,jdbcType=VARCHAR},
            </if>
            <if test="updated.productPath != null">
                product_path = #{updated.productPath,jdbcType=VARCHAR},
            </if>
            <if test="updated.invalid != null">
                invalid = #{updated.invalid,jdbcType=INTEGER},
            </if>
            <if test="updated.goodsReturnDetailJSON != null">
                goods_return_detail_json = #{updated.goodsReturnDetailJSON,jdbcType=VARCHAR},
            </if>
            <if test="updated.buyerName != null">
                buyer_name = #{updated.buyerName ,jdbcType=VARCHAR},
            </if>
        </set>
        where detail_id in
        <foreach item="item" index="index" collection="detailIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2020-11-27-->
  <update id="loopUpdateByIdIn">
        update t_goods_return
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="reply_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.replyTime != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.replyTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>

            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.quantity != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.quantity,jdbcType=DOUBLE}
                    </if>
                </foreach>
            </trim>

            <trim prefix="price = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.price != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.price,jdbcType=DOUBLE}
                    </if>
                </foreach>
            </trim>

            <trim prefix="goods_return_status = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.goodsReturnStatus != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.goodsReturnStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="order_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderId != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderNo != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>

            <trim prefix="order_status = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orderStatus != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orgId != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orgId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="org_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orgName != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orgName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>

            <trim prefix="department_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.departmentId != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.departmentId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="department_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.departmentName != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.departmentName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>

            <trim prefix="buyer_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.buyerName != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.buyerName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>

            <trim prefix="invalid = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.invalid != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.invalid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>

            <trim prefix="goods_return_detail_json = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.goodsReturnDetailJSON != null ">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.goodsReturnDetailJSON,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2020-11-27-->
    <update id="batchCancelGoodsReturn">
        <foreach collection="list" item="updated" index="index"
                 separator=";">
            update t_goods_return
            <set>
                <if test="updated.goodsReturnStatus != null">
                    goods_return_status = #{updated.goodsReturnStatus,jdbcType=INTEGER},
                </if>
                <if test="updated.price != null">
                    price = #{updated.price,jdbcType=DOUBLE},
                </if>
            </set>
            where id = #{updated.id,jdbcType=INTEGER} and goods_return_status in (0, 1, 2, 4)
        </foreach>
    </update>


<!--auto generated by MybatisCodeHelper on 2020-12-04-->
  <select id="findByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
      and invalid = #{invalid,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-04-->
    <select id="findByPageParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        <where>
            <if test="params.orgId != null">
                and org_id = #{params.orgId,jdbcType=INTEGER}
            </if>

            <if test="params.departmentIdList != null and params.departmentIdList.size() != 0">
                and department_id in
                <foreach item="item" index="index" collection="params.departmentIdList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="params.startDate != null">
                and creation_time <![CDATA[>]]> #{params.startDate,jdbcType=TIMESTAMP}
            </if>

            <if test="params.endDate != null">
                and creation_time <![CDATA[<]]> #{params.endDate,jdbcType=TIMESTAMP}
            </if>

            <if test="params.returnNo != null and params.returnNo != ''">
                and return_no = #{params.returnNo,jdbcType=VARCHAR}
            </if>

            <if test="params.supplierId != null">
                and supplier_id = #{params.supplierId,jdbcType=INTEGER}
            </if>

            <if test="params.supplierName != null">
                and supplier_name like concat(#{params.supplierName,jdbcType=VARCHAR} ,'%')
            </if>

            <if test="params.returnStatusList != null and params.returnStatusList.size() != 0">
                and goods_return_status in
                <foreach item="item" index="index" collection="params.returnStatusList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="params.orgIds != null and params.orgIds.size() != 0">
                and org_id in
                <foreach item="item" index="index" collection="params.orgIds"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>

            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>

            <if test="params.orderNoLike != null and params.orderNoLike != ''">
                and order_no like concat('%', #{params.orderNoLike,jdbcType=VARCHAR} ,'%')
            </if>

            <if test="params.buyerName != null and params.buyerName != ''">
                and buyer_name like concat(#{params.buyerName,jdbcType=VARCHAR},'%')
            </if>

            and invalid = 0
            order by id desc
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-06-->
  <select id="countGroupByGoodsReturnStatus" resultMap="GoodsReturnCountDO">
    SELECT goods_return_status, count(goods_return_status) as count
    from t_goods_return
      <where>
          <if test="params.orgId != null ">
              and org_id = #{params.orgId,jdbcType=INTEGER}
          </if>

          <if test="params.departmentIdList != null and params.departmentIdList.size() != 0">
              and department_id in
              <foreach item="item" index="index" collection="params.departmentIdList"
                       open="(" separator="," close=")">
                  #{item,jdbcType=INTEGER}
              </foreach>
          </if>

          <if test="params.orgIds != null and params.orgIds.size() != 0">
              and org_id in
              <foreach item="item" index="index" collection="params.orgIds"
                       open="(" separator="," close=")">
                  #{item,jdbcType=INTEGER}
              </foreach>
          </if>

          <if test="params.supplierId != null">
              and supplier_id = #{params.supplierId,jdbcType=INTEGER}
          </if>

          and invalid = #{invalid,jdbcType=INTEGER}
      </where>
    GROUP BY goods_return_status
  </select>

<!--auto generated by MybatisCodeHelper on 2021-01-07-->
  <update id="updateGoodsDetailRemarkById" parameterType="com.ruijing.store.order.base.core.model.GoodsReturn">
        update t_goods_return
      <set>
          <if test="goodsReturnStatus != null">
              goods_return_status = #{goodsReturnStatus,jdbcType=INTEGER},
          </if>
          <if test="productPath != null">
              product_path = #{productPath,jdbcType=VARCHAR},
          </if>
          <if test="goodsReturnDetailJSON != null">
              goods_return_detail_json = #{goodsReturnDetailJSON,jdbcType=VARCHAR},
          </if>
          <if test="returnReason != null">
              return_reason = #{returnReason,jdbcType=VARCHAR},
          </if>
          <if test="remark != null">
              remark = #{remark,jdbcType=VARCHAR},
          </if>
      </set>
        where id=#{id,jdbcType=INTEGER} and goods_return_status in (0, 2)
    </update>

<!--auto generated by MybatisCodeHelper on 2021-01-08-->
  <select id="findByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where order_id=#{orderId,jdbcType=INTEGER}
      and invalid = #{invalid,jdbcType=INTEGER}
      order by id desc

    </select>

    <select id="findByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where order_id in
        <foreach item="item" index="index" collection="orderIds"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and invalid = #{invalid,jdbcType=INTEGER}
    </select>
<!--auto generated by MybatisCodeHelper on 2020-12-28-->
  <select id="findByOrderIdInAndGoodsReturnStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>

      <if test="goodsReturnStatusCollection != null and goodsReturnStatusCollection.size() != 0">
          and goods_return_status in
          <foreach item="item" index="index" collection="goodsReturnStatusCollection"
                   open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
          </foreach>
      </if>
      and invalid = #{invalid,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2020-12-28-->
  <update id="updateGoodsReturnStatusByOrderIdIn">
        update t_goods_return
        set goods_return_status=#{updatedGoodsReturnStatus,jdbcType=INTEGER},
        invalided = #{invalided,jdbcType=INTEGER}
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>


<!--auto generated by MybatisCodeHelper on 2021-01-21-->
  <select id="selectMaxId" resultType="java.lang.Integer">
    select max(id)
    from t_goods_return
  </select>

<!--auto generated by MybatisCodeHelper on 2021-01-21-->
  <select id="findByIdBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where id <![CDATA[>]]> #{minId,jdbcType=INTEGER} and id <![CDATA[<=]]> #{maxId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-25-->
  <select id="findGroupByReturnNoHavingDetailId" resultType="java.lang.String">
        select return_no
        from t_goods_return
        group by return_no
        having count(detail_id) > 1
    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-25-->
  <select id="findByReturnNoIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where return_no in
        <foreach item="item" index="index" collection="returnNoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
      order by creation_time desc
    </select>

    <select id="findGroupByDetailIdHavingReturnNo" resultType="java.lang.Integer">
        select detail_id
        from t_goods_return
        where goods_return_status in (0, 1, 2, 4, 5)
        group by detail_id
        having count(return_no) > 1
    </select>

<!--auto generated by MybatisCodeHelper on 2021-04-18-->
  <select id="findByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where order_no=#{orderNo,jdbcType=VARCHAR}
        and invalid = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-04-18-->
    <select id="findByOrderNoLatest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where order_no=#{orderNo,jdbcType=VARCHAR}
        and invalid = 0
        order by creation_time desc limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2021-04-19-->
  <select id="countIdByGoodsReturnStatus" resultType="java.lang.Integer">
        select id
        from t_goods_return
        where goods_return_status in
      <foreach item="item" index="index" collection="goodsReturnStatusList"
               open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
      </foreach>
        and invalid = #{invalid,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-07-06-->
  <select id="findByIdInAndOrgIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and org_id in
        <foreach item="item" index="index" collection="orgIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-10-20-->
  <select id="findIdByOrderIdIn" resultMap="BaseResultMap">
        select id, order_id
        from t_goods_return
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and invalid = 0
    </select>

    <select id="findByStatusAndReplyDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where
        goods_return_status in
        <foreach item="item" index="index" collection="statusCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and  #{date,jdbcType=TIMESTAMP} > reply_time
        and invalid = 0
    </select>
    <select id="findSyncReplyList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_goods_return
        where reply_time is null
        and goods_return_status in (1,4)
        order by id limit 0, 200
    </select>
</mapper>
