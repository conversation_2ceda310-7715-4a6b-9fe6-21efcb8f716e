package com.ruijing.store.order.base.docking.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.api.outer.buyer.OrderGenerateStatusNoticeService;
import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.notice.service.GenericCallService;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateOrderRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateResponseDTO;
import com.ruijing.store.order.api.base.docking.service.OrderForThirdPartRPCService;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.ResearchStatementClient;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/3/18 18:17
 **/
@MSharpService
public class OrderForThirdPartRPCServiceImpl implements OrderForThirdPartRPCService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "OrderForThirdPartRPCServiceImpl";

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Resource
    private GenericCallService genericCallService;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Override
    @ServiceLog(description = "第三方对接单位更新我方订单信息接口", operationType = OperationType.WRITE)
    public RemoteResponse<ThirdPartUpdateResponseDTO<String>> thirdPartyUpdateOrder(ThirdPartUpdateRequestDTO request) {
        String orgCode = request.getOrgCode();
        List<ThirdPartUpdateOrderRequestDTO> thirdPartUpdateOrderRequestDTOList = request.getThirdPartUpdateOrderRequestDTOList();
        BusinessErrUtil.notNull(orgCode, "更新订单失败！医院编码不可为空");
        BusinessErrUtil.isTrue(DockingConstant.THIRD_PARTY_PLATFORM_ORGANIZATION.contains(orgCode), "更新失败！非单位管理对接的单位无需推送更新");
        BusinessErrUtil.notEmpty(thirdPartUpdateOrderRequestDTOList, "更新订单失败！订单入参不可为空");
        BusinessErrUtil.isTrue(thirdPartUpdateOrderRequestDTOList.size() < 101, "更新订单失败！批量更新单次最多支持100单");

        List<UpdateOrderParamDTO> params = constructUpdateParams(thirdPartUpdateOrderRequestDTOList);
        List<String> thirdPartUpdateOrderResponse = params.stream().map(UpdateOrderParamDTO::getOrderNo).collect(Collectors.toList());

        // 更新我方商城的订单状态
        this.updateReagentOrder(orgCode, thirdPartUpdateOrderRequestDTOList);

        ThirdPartUpdateResponseDTO response = new ThirdPartUpdateResponseDTO();
        response.setOrgCode(orgCode);
        response.setThirdPartUpdateOrderResponseDTOList(thirdPartUpdateOrderResponse);
        return RemoteResponse.<ThirdPartUpdateResponseDTO<String>>custom().setSuccess().setData(response);
    }

    private void updateReagentOrder(String orgCode, List<ThirdPartUpdateOrderRequestDTO> params) {
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(orgCode)) {
            // 过滤
            List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(params.stream().map(ThirdPartUpdateOrderRequestDTO::getOrderNo).collect(Collectors.toList()));
            Map<String, OrderMasterDO> orderNoOrderMasterMap = DictionaryUtils.toMap(orderList, OrderMasterDO::getForderno, Function.identity());
            List<String> warehouseRejectList = params.stream().filter(o -> OrderStatusEnum.WaitingForStatement_1.getValue().equals(o.getStatus())).map(ThirdPartUpdateOrderRequestDTO::getOrderNo).distinct().collect(Collectors.toList());
            List<String> statementList = params.stream().filter(o -> OrderStatusEnum.Statementing_1.getValue().equals(o.getStatus())).map(ThirdPartUpdateOrderRequestDTO::getOrderNo).distinct().collect(Collectors.toList());
            List<String> approveSuccessOrderNoList = params.stream().filter(o -> OrderStatusEnum.WaitingForConfirm.getValue().equals(o.getStatus())).map(ThirdPartUpdateOrderRequestDTO::getOrderNo).distinct().collect(Collectors.toList());
            List<String> approveFailOrderNoList = params.stream().filter(o -> OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(o.getStatus())).map(ThirdPartUpdateOrderRequestDTO::getOrderNo).distinct().collect(Collectors.toList());
            Map<String, String> orderNoReasonMap = params.stream().filter(param -> param.getUpdateReason() != null).collect(Collectors.toMap(ThirdPartUpdateOrderRequestDTO::getOrderNo, ThirdPartUpdateOrderRequestDTO::getUpdateReason, (o, n) -> n));
            if (CollectionUtils.isNotEmpty(statementList)) {
                List<OrderMasterDO> statementOrderList = statementList.stream().map(orderNoOrderMasterMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                this.createStatement(statementOrderList, orderNoReasonMap);
            }
            if (CollectionUtils.isNotEmpty(warehouseRejectList)) {
                List<OrderMasterDO> warehouseRejectOrderList = warehouseRejectList.stream().map(orderNoOrderMasterMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                this.warehouseReject(warehouseRejectOrderList, orderNoReasonMap);
            }
            if (CollectionUtils.isNotEmpty(approveSuccessOrderNoList)) {
                List<OrderMasterDO> approveSuccessOrderList = approveSuccessOrderNoList.stream().map(orderNoOrderMasterMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                this.approveSuccess(approveSuccessOrderList, orderNoReasonMap);
            }
            if(CollectionUtils.isNotEmpty(approveFailOrderNoList)){
                List<OrderMasterDO> approveFailOrderList = approveFailOrderNoList.stream().map(orderNoOrderMasterMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                this.approveFail(approveFailOrderList, orderNoReasonMap);
            }
        }
    }

    private void createStatement(List<OrderMasterDO> orderList, Map<String, String> orderNoReasonMap){
        Date now = new Date();
        for(OrderMasterDO order : orderList){
            if (!OrderStatusEnum.WaitingForStatement_1.getValue().equals(order.getStatus())) {
                continue;
            }
            // 发起结算
            StatementResultDTO statementSingle = researchStatementClient.createStatementSingle(order.getFbuyerid(), order.getFbuyername(), order);
            // 回写结算数据
            UpdateOrderParamDTO update = new UpdateOrderParamDTO();
            update.setOrderId(order.getId());
            update.setStatus(OrderStatusEnum.Statementing_1.getValue());
            update.setInStateTime(now);
            update.setStatementId(statementSingle.getId().intValue());
            update.setStatementStatus(statementSingle.getStatus());
            update.setInventoryStatus(InventoryStatusEnum.COMPLETE.getCode());
            orderMasterMapper.updateOrderById(update);
            // 写入日志
            OrderApprovalLogDTO log = new OrderApprovalLogDTO();
            log.setOrderId(order.getId());
            log.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            log.setOperatorName(DockingConstant.SYSTEM_OPERATOR_NAME);
            log.setReason(orderNoReasonMap.getOrDefault(order.getForderno(), "订单发起结算"));
            log.setApproveStatus(OrderApprovalEnum.UPDATE_REMARK.getValue());
            orderApprovalLogService.asyncInsertOrderApprovalLog(log);
        }
    }

    private void warehouseReject(List<OrderMasterDO> orderList, Map<String, String> orderNoReasonMap) {
        orderList = orderList.stream()
                .filter(orderMasterDO -> OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()) &&
                        InventoryStatusEnum.WAITING_FOR_REVIEW.getCode().equals(orderMasterDO.getInventoryStatus().intValue())).collect(Collectors.toList());
        // 更新订单数据
        List<UpdateOrderParamDTO> updateParams = orderList.stream()
                .map(orderMasterDO -> {
                    UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                    updateOrderParamDTO.setOrderId(orderMasterDO.getId());
                    updateOrderParamDTO.setInventoryStatus(InventoryStatusEnum.FAILED.getCode());
                    updateOrderParamDTO.setOrderNo(orderMasterDO.getForderno());
                    return updateOrderParamDTO;
                }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(updateParams)){
            return;
        }
        orderMasterMapper.updateFieldByForderno(updateParams);
        // 回写入库驳回日志
        List<OrderApprovalLogDTO> logs = updateParams.stream().map(item -> {
            OrderApprovalLogDTO param = new OrderApprovalLogDTO();
            param.setOrderId(item.getOrderId());
            param.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            param.setOperatorName(DockingConstant.SYSTEM_OPERATOR_NAME);
            param.setReason(orderNoReasonMap.getOrDefault(item.getOrderNo(), "订单入库驳回"));
            param.setApproveStatus(OrderApprovalEnum.IN_BOUND_CALLBACK.getValue());
            return param;
        }).collect(Collectors.toList());
        orderApprovalLogService.insertOrderApprovalLogList(logs);
    }

    private void approveSuccess(List<OrderMasterDO> orderList, Map<String, String> orderNoReasonMap){
        orderList = orderList.stream()
                .filter(orderMasterDO -> OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(orderMasterDO.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderList)){
            return;
        }
        // 更新订单数据、
        List<UpdateOrderParamDTO> updateParams = orderList.stream()
                .map(orderMasterDO -> {
                    UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                    updateOrderParamDTO.setOrderId(orderMasterDO.getId());
                    updateOrderParamDTO.setStatus(OrderStatusEnum.WaitingForConfirm.getValue());
                    updateOrderParamDTO.setOrderNo(orderMasterDO.getForderno());
                    return updateOrderParamDTO;
                }).collect(Collectors.toList());
        orderMasterMapper.updateFieldByForderno(updateParams);
        // 吸入日志
        List<OrderApprovalLogDTO> logs = orderList.stream().map(item -> {
            OrderApprovalLogDTO param = new OrderApprovalLogDTO();
            param.setOrderId(item.getId());
            param.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
            param.setOperatorName(DockingConstant.SYSTEM_OPERATOR_NAME);
            param.setReason("订单审批通过");
            param.setApproveStatus(OrderApprovalEnum.UPDATE_REMARK.getValue());
            return param;
        }).collect(Collectors.toList());
        orderApprovalLogService.insertOrderApprovalLogList(logs);

        for (OrderMasterDO orderMasterDO : orderList) {
            OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO = new OuterBuyerCommonProcessDTO();
            outerBuyerCommonProcessDTO.setOrderNo(orderMasterDO.getForderno());
            outerBuyerCommonProcessDTO.setOrgCode(orderMasterDO.getFusercode());
            genericCallService.callMethod("store-apply-service", outerBuyerCommonProcessDTO, OrderGenerateStatusNoticeService.class.getName(), "approveSuccess", 10000, orderMasterDO.getForderno());
        }
        // 发邮件
        List<OrderMasterDO> finalOrderList = orderList;
        AsyncExecutor.listenableRunAsync(() -> {
            for (OrderMasterDO orderInfo : finalOrderList) {
                orderEmailHandler.sendOrderGenerateEmailToSupp(orderInfo);
                weChatMessageHandler.waitingConfirmToSupp(orderInfo);
            }
        }).addFailureCallback(throwable -> {
            LOGGER.error("发送生成订单邮件邮件给供应商失败：" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "发送生成订单邮件邮件给供应商失败：", throwable);
            throw new IllegalStateException(throwable);
        });
    }

    private void approveFail(List<OrderMasterDO> orderList, Map<String, String> orderNoReasonMap){
        orderList = orderList.stream()
                .filter(orderMasterDO -> OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(orderMasterDO.getStatus())).collect(Collectors.toList());
        for (OrderMasterDO orderMasterDO : orderList) {
            OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO = new OuterBuyerCommonProcessDTO();
            outerBuyerCommonProcessDTO.setOrderNo(orderMasterDO.getForderno());
            outerBuyerCommonProcessDTO.setOrgCode(orderMasterDO.getFusercode());
            outerBuyerCommonProcessDTO.setReason(orderNoReasonMap.get(orderMasterDO.getForderno()));
            genericCallService.callMethod("store-apply-service", outerBuyerCommonProcessDTO, OrderGenerateStatusNoticeService.class.getName(), "approveFail", 10000, orderMasterDO.getForderno());
        }
    }

    /**
     * 构造第三方订单更新入参
     * @param thirdPartUpdateOrderRequestDTOList
     * @return
     */
    private List<UpdateOrderParamDTO> constructUpdateParams(List<ThirdPartUpdateOrderRequestDTO> thirdPartUpdateOrderRequestDTOList) {
        return thirdPartUpdateOrderRequestDTOList.stream().map(it -> {
            UpdateOrderParamDTO param = new UpdateOrderParamDTO();
            param.setStatus(it.getStatus());
            param.setOrderNo(it.getOrderNo());
            return param;
        }).collect(Collectors.toList());
    }
}
