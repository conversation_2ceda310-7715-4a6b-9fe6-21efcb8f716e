package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/28 16:46
 */
public enum  OrderAcceptByPhotoEnum {

    NO_NEED(0,"无需拍照验收"),

    COMPULSORY(1,"强制拍照验收"),

    NOT_COMPULSORY(2,"非强制拍照验收"),

    COMPULSORY_EXCEPT_SERVICE(3,"除服务类强制拍照验收");

    private Integer value;

    private String desc;

    OrderAcceptByPhotoEnum(Integer value, String desc) {
        this.desc = desc;
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderAcceptByPhotoEnum getByName(String desc) {
        for (OrderAcceptByPhotoEnum orderAcceptByPhotoEnum : OrderAcceptByPhotoEnum.values()) {
            if (orderAcceptByPhotoEnum.desc.equals(desc)){
                return orderAcceptByPhotoEnum;
            }
        }
        return null;
    }

    public static final OrderAcceptByPhotoEnum getByValue(Integer value) {
        for (OrderAcceptByPhotoEnum orderAcceptByPhotoEnum : OrderAcceptByPhotoEnum.values()) {
            if (orderAcceptByPhotoEnum.value.equals(value)){
                return orderAcceptByPhotoEnum;
            }
        }
        return null;
    }

}
