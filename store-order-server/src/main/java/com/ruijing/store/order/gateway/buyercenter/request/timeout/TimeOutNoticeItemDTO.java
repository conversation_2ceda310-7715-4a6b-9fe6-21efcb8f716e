package com.ruijing.store.order.gateway.buyercenter.request.timeout;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2024-03-04 17:16
 * @description:
 **/
public class TimeOutNoticeItemDTO implements Serializable {

    private static final long serialVersionUID = 1522980246222457373L;

    private Integer orderId;

    private Integer deptId;

    public Integer getOrderId() {
        return orderId;
    }

    public TimeOutNoticeItemDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public TimeOutNoticeItemDTO setDeptId(Integer deptId) {
        this.deptId = deptId;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TimeOutNoticeItemDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("deptId=" + deptId)
                .toString();
    }
}
