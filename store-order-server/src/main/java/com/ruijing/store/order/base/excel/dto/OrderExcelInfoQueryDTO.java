package com.ruijing.store.order.base.excel.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

public class OrderExcelInfoQueryDTO implements Serializable {

    private static final long serialVersionUID = -5412568049865114222L;

    @RpcModelProperty("当前页")
    private Integer pageNo = 1;

    @RpcModelProperty("每页条数")
    private Integer pageSize = 10;

    @RpcModelProperty("导出时间开始")
    private Date startDate;

    @RpcModelProperty("导出时间结束")
    private Date endDate;

    @RpcModelProperty("导出状态")
    private Integer status;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "OrderExcelInfoQueryDTO{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", status=" + status +
                '}';
    }
}
