package com.ruijing.store.order.other.service.impl;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.research.reimbursement.dto.ReimbursementConfigDTO;
import com.reagent.research.reimbursement.enums.BusinessTypeEnum;
import com.reagent.research.reimbursement.enums.StatusEnum;
import com.reagent.research.reimbursement.exit.dto.ExitAccountRequestDTO;
import com.reagent.research.reimbursement.exit.dto.ExitAccountUpdateDTO;
import com.reagent.research.reimbursement.exit.enums.ExitAccountBusinessTypeEnum;
import com.reagent.research.reimbursement.exit.enums.ExitAccountEnabledEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.notice.cluster.ClusterNoticeClient;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.other.service.ReimbursementService;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.ReimbursementClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-10-27 17:58
 * @description:
 **/
@Service
public class ReimbursementServiceImpl implements ReimbursementService, InitializingBean {

    private static Map<Integer, Map<Integer, Pair<Date, Integer>>> orgIdSuppIdDatePeriodMap;

    /**
     * 还没代结算的状态（即发货前或已经关闭订单）
     */
    private static final List<Integer> AGENT_STATEMENT_EXCLUDE_STATUS_LIST = New.list(
            OrderStatusEnum.WaitingForDockingConfirm.getValue(),
            OrderStatusEnum.DeckingFail.getValue(),
            OrderStatusEnum.WaitingForConfirm.getValue(),
            OrderStatusEnum.PurchaseApplyToCancel.getValue(),
            OrderStatusEnum.SupplierApplyToCancel.getValue(),
            OrderStatusEnum.WaitingForDelivery.getValue(),
            OrderStatusEnum.ORDER_SPLIT_UP.getValue(),
            OrderStatusEnum.Close.getValue()

    );

    @Resource
    private ClusterNoticeClient clusterNoticeClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private ReimbursementClient reimbursementClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    public void afterPropertiesSet() {
        this.reloadAllConfig();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void configChange(Integer orgId, Integer suppId) {
        this.reloadAllConfig();
        clusterNoticeClient.noticeDataChange(ClusterNoticeClient.VEE_CARD_CACHE_RELOAD, "true");
        this.handleOldData(orgId, suppId);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void reloadAllConfig() {
        Map<Integer, Map<Integer, Pair<Date, Integer>>> configMap = New.map();
        List<ReimbursementConfigDTO> configList = reimbursementClient.getUsableReimbursementConfig();
        for (ReimbursementConfigDTO config : configList) {
            if (!(BusinessTypeEnum.ORDER.getValue().equals(config.getBusinessType()) && StatusEnum.ENABLE.getValue().equals(config.getBusinessType()))) {
                continue;
            }
            Map<Integer, Pair<Date, Integer>> suppIdDateMap = configMap.computeIfAbsent(config.getOrgId(), k -> New.map());
            suppIdDateMap.put(config.getSuppId(), Pair.of(config.getBeginTime(), config.getDays()));
        }
        // 直接替换，用于更新缓存
        orgIdSuppIdDatePeriodMap = configMap;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void saveRecord(OrderMasterDO orderMasterDO) {
        if (getIsAgentStatement(orderMasterDO.getFuserid(), orderMasterDO.getFsuppid(), orderMasterDO.getForderdate())) {
            List<BaseOrderExtraDTO> extraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderMasterDO.getId()), OrderExtraEnum.IS_TRIAL_ORDER.getValue());
            if(CollectionUtils.isNotEmpty(extraDTOList) && CommonValueUtils.TRUE_NUMBER_STR.equals(extraDTOList.get(0).getExtraValue())){
                // 试用订单，不推出账数据
                return;
            }
            ExitAccountRequestDTO exitAccountRequestDTO = new ExitAccountRequestDTO();
            exitAccountRequestDTO.setOrgId(orderMasterDO.getFuserid());
            exitAccountRequestDTO.setOrgName(orderMasterDO.getFusername());
            exitAccountRequestDTO.setSupplierId(orderMasterDO.getFsuppid());
            exitAccountRequestDTO.setSupplierName(orderMasterDO.getFsuppname());
            BigDecimal returnAmount = orderMasterDO.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderMasterDO.getReturnAmount());
            exitAccountRequestDTO.setExitAmount(orderMasterDO.getForderamounttotal().subtract(returnAmount));
            exitAccountRequestDTO.setBusinessNumber(orderMasterDO.getForderno());
            exitAccountRequestDTO.setBusinessType(ExitAccountBusinessTypeEnum.ORDER.getBusinessType());
            exitAccountRequestDTO.setDepartmentId(orderMasterDO.getFbuydepartmentid());
            exitAccountRequestDTO.setDepartmentName(orderMasterDO.getFbuydepartment());
            exitAccountRequestDTO.setStartTime(orderMasterDO.getFdeliverydate());
            exitAccountRequestDTO.setPeriodDay(orgIdSuppIdDatePeriodMap.get(orderMasterDO.getFuserid()).get(orderMasterDO.getFsuppid()).getRight());
            reimbursementClient.saveExitAccountList(New.list(exitAccountRequestDTO));
        }
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void invalidRecord(OrderMasterDO orderMasterDO) {
        ExitAccountUpdateDTO exitAccountUpdateDTO = new ExitAccountUpdateDTO();
        exitAccountUpdateDTO.setBusinessType(ExitAccountBusinessTypeEnum.ORDER.getBusinessType());
        exitAccountUpdateDTO.setBusinessNumber(orderMasterDO.getForderno());
        exitAccountUpdateDTO.setEnabled(ExitAccountEnabledEnum.INVALID.getEnabled());
        reimbursementClient.updateExitAccountEnabledList(New.list(exitAccountUpdateDTO));
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void validRecord(Integer orderId) {
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        if (getIsAgentStatement(orderMasterDO.getFuserid(), orderMasterDO.getFsuppid(), orderMasterDO.getForderdate())) {
            List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(orderId);
            if (goodsReturnList.stream().allMatch(goodsReturn ->
                    GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus())
                            || GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(goodsReturn.getGoodsReturnStatus()))) {
                ExitAccountUpdateDTO exitAccountUpdateDTO = new ExitAccountUpdateDTO();
                exitAccountUpdateDTO.setBusinessType(ExitAccountBusinessTypeEnum.ORDER.getBusinessType());
                exitAccountUpdateDTO.setBusinessNumber(orderMasterDO.getForderno());
                exitAccountUpdateDTO.setEnabled(ExitAccountEnabledEnum.EFFECTIVE.getEnabled());
                reimbursementClient.updateExitAccountEnabledList(New.list(exitAccountUpdateDTO));
            }
        }
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void deleteRecord(OrderMasterDO orderMasterDO) {
        if (!OrderStatusEnum.Close.getValue().equals(orderMasterDO.getStatus())) {
            return;
        }
        ExitAccountUpdateDTO exitAccountUpdateDTO = new ExitAccountUpdateDTO();
        exitAccountUpdateDTO.setBusinessType(ExitAccountBusinessTypeEnum.ORDER.getBusinessType());
        exitAccountUpdateDTO.setBusinessNumber(orderMasterDO.getForderno());
        exitAccountUpdateDTO.setEnabled(ExitAccountEnabledEnum.DELETE.getEnabled());
        reimbursementClient.updateExitAccountEnabledList(New.list(exitAccountUpdateDTO));
    }

    public static boolean getIsAgentStatement(Integer orgId, Integer suppId, Date orderDate) {
        Date configDate = getConfigDate(orgId, suppId);
        return configDate != null && orderDate.after(configDate);
    }

    private static Date getConfigDate(Integer orgId, Integer suppId) {
        Map<Integer, Pair<Date, Integer>> suppIdDatePeriodMap = orgIdSuppIdDatePeriodMap.get(orgId);
        Pair<Date, Integer> suppIdDatePair;
        if (suppIdDatePeriodMap == null || (suppIdDatePair = suppIdDatePeriodMap.get(suppId)) == null) {
            return null;
        }
        return suppIdDatePair.getLeft();
    }

    private void handleOldData(Integer orgId, Integer suppId) {
        int batchSize = 200;
        Date date = getConfigDate(orgId, suppId);
        if (date == null) {
            return;
        }
        Integer period = orgIdSuppIdDatePeriodMap.get(orgId).get(suppId).getRight();
        FieldRangeDTO idRange = new FieldRangeDTO("id", "-1", null, false, false);
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setExcludeStatusList(AGENT_STATEMENT_EXCLUDE_STATUS_LIST);
        orderSearchParamDTO.setFieldRangeList(New.list(
                new FieldRangeDTO("forderdate", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, date), null, true, false),
                idRange));
        orderSearchParamDTO.setStartHit(0);
        orderSearchParamDTO.setPageSize(batchSize);
        orderSearchParamDTO.setOrgIdList(New.list(orgId));
        orderSearchParamDTO.setSuppIdList(New.list(suppId));
        while (true) {
            SearchPageResultDTO<OrderMasterSearchDTO> resultDTO = orderSearchBoostService.commonSearch(orderSearchParamDTO);
            if (CollectionUtils.isNotEmpty(resultDTO.getRecordList())) {
                List<ExitAccountRequestDTO> exitAccountRequestDTOList = resultDTO.getRecordList().stream()
                        .filter(orderMasterSearchDTO -> orderMasterSearchDTO.getOrderExtra().stream().noneMatch(orderExtraDTO -> OrderExtraEnum.IS_TRIAL_ORDER.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.TRUE_NUMBER_STR.equals(orderExtraDTO.getExtraValue())))
                        .map(record->{
                    ExitAccountRequestDTO exitAccountRequestDTO = new ExitAccountRequestDTO();
                    exitAccountRequestDTO.setOrgId(record.getFuserid());
                    exitAccountRequestDTO.setOrgName(record.getFusername());
                    exitAccountRequestDTO.setSupplierId(record.getFsuppid());
                    exitAccountRequestDTO.setSupplierName(record.getFsuppname());
                    BigDecimal returnAmount = record.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(record.getReturnAmount());
                    exitAccountRequestDTO.setExitAmount(BigDecimal.valueOf(record.getForderamounttotal()).subtract(returnAmount));
                    exitAccountRequestDTO.setBusinessNumber(record.getForderno());
                    exitAccountRequestDTO.setBusinessType(ExitAccountBusinessTypeEnum.ORDER.getBusinessType());
                    exitAccountRequestDTO.setDepartmentId(record.getFbuydepartmentid());
                    exitAccountRequestDTO.setDepartmentName(record.getFbuydepartment());
                    exitAccountRequestDTO.setStartTime(record.getFdeliverydate());
                    exitAccountRequestDTO.setPeriodDay(period);
                    return exitAccountRequestDTO;
                }).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(exitAccountRequestDTOList)){
                    reimbursementClient.saveExitAccountList(exitAccountRequestDTOList);
                }
            }
            if (resultDTO.getTotalHits() <= batchSize) {
                break;
            }
            idRange.setLower(resultDTO.getRecordList().get(resultDTO.getRecordList().size() - 1).getId().toString());
        }
    }
}
