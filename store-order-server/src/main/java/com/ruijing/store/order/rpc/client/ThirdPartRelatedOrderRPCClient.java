package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.OrderDockingNumberRpcService;
import com.reagent.order.api.ThirdPartRelatedOrderRPCService;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.request.ThirdPartRelatedOrderDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

@ServiceClient
public class ThirdPartRelatedOrderRPCClient {

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private ThirdPartRelatedOrderRPCService thirdPartRelatedOrderRPCService;

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private OrderDockingNumberRpcService orderDockingNumberRpcService;

    @ServiceLog(description = "迁移对接记录接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer insertRelatedOrderList(List<ThirdPartRelatedOrderDTO> request) {
        RemoteResponse<Integer> response = thirdPartRelatedOrderRPCService.insertRelatedOrderList(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "保存对接记录接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer saveRelatedOrder(ThirdPartRelatedOrderDTO request) {
        RemoteResponse<Integer> response = thirdPartRelatedOrderRPCService.saveRelatedOrder(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void insertDockingNumber(List<OrderDockingNumberDTO> orderDockingNumberDTOList){
        RemoteResponse<Integer> response = orderDockingNumberRpcService.insertList(orderDockingNumberDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
