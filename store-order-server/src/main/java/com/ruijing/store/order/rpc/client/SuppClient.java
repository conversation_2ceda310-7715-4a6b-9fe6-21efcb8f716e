package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.api.outer.supplier.OuterSupplierRequestService;
import com.reagent.order.dto.outer.supplier.OuterSupplierDeliveryDTO;
import com.reagent.supp.api.order.service.SuppOrderService;
import com.reagent.supp.api.supplier.service.SuppInfoService;
import com.reagent.supp.api.user.service.SupplierUserService;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.carryfee.api.dto.BizSearchProductCarryFeeDTO;
import com.ruijing.shop.carryfee.api.dto.BizSearchProductCarryFeeResultDTO;
import com.ruijing.shop.carryfee.api.dto.BizSearchProductDetailCarryFeeDTO;
import com.ruijing.shop.carryfee.api.service.BizFareTemplateService;
import com.ruijing.shop.category.api.dto.BrandDTO;
import com.ruijing.shop.category.api.service.BrandService;
import com.ruijing.shop.crm.api.pojo.dto.*;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.shop.crm.api.pojo.dto.bank.BankAccountDTO;
import com.ruijing.shop.crm.api.pojo.dto.bank.SuppBankQueryDTO;
import com.ruijing.shop.crm.api.pojo.dto.query.DeliveryProxyMultiSuppQueryDTO;
import com.ruijing.shop.crm.api.pojo.param.OfflineSupplierParam;
import com.ruijing.shop.crm.api.pojo.param.QualParam;
import com.ruijing.shop.crm.api.pojo.param.UserParam;
import com.ruijing.shop.crm.api.pojo.query.SupplierContactQry;
import com.ruijing.shop.crm.api.pojo.query.SupplierMultiQry;
import com.ruijing.shop.crm.api.pojo.query.UserAccountPageableQry;
import com.ruijing.shop.crm.api.service.*;
import com.ruijing.shop.crm.api.support.PageResultBean;
import com.ruijing.shop.crm.api.support.ResultBean;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.shop.srm.api.dto.brand.SuppBrandDTO;
import com.ruijing.shop.srm.api.service.SuppBrandRPCService;
import com.ruijing.shop.trade.api.dto.OrderDeliveryInfoDTO;
import com.ruijing.shop.trade.api.enums.DeliveryTypeEnum;
import com.ruijing.shop.trade.api.query.OrderDeliveryInfoQuery;
import com.ruijing.shop.trade.api.service.OrderDeliveryInfoRpcService;
import com.ruijing.store.gaea.api.service.CategoryI18nRpcService;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.SuppShopInfoBO;
import com.ruijing.store.supp.include.api.dto.OrgIncludeSuppDTO;
import com.ruijing.store.supp.include.api.dto.OrgSuppSmallDTO;
import com.ruijing.store.supp.include.api.enums.OrgIncludeSuppStatusEnum;
import com.ruijing.store.supp.include.api.service.OrgIncludeSuppRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/7/7 0007 16:51
 * @Version 1.0
 * @Desc:描述
 */
@ServiceClient
public class SuppClient {

    @MSharpReference(remoteAppkey = "supp")
    private SupplierUserService supplierUserService;

    @MSharpReference(remoteAppkey = "supp")
    private SuppInfoService suppInfoService;

    @MSharpReference(remoteAppkey = "store-supp-include-service")
    private OrgIncludeSuppRpcService orgIncludeSuppRpcService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private CrmUserService crmUserService;

    @MSharpReference(remoteAppkey = "supp")
    private SuppOrderService suppOrderService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private ShopQualificationService shopQualificationService;

    @MSharpReference(remoteAppkey = "shop-srm-service")
    private SuppBrandRPCService suppBrandRpcService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private OfflineSupplierService offlineSupplierService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private ShopSuppContactService shopSuppContactService;

    @MSharpReference(remoteAppkey = "shop-carry-fee-service")
    private BizFareTemplateService bizFareTemplateService;

    @MSharpReference(remoteAppkey = "shop-trade-service")
    private OuterSupplierRequestService outerSupplierRequestService;

    private final static String CAT_TYPE = "SuppClient";

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private UserAccountManageRpcService userAccountManageRpcService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private DeliveryProxyRpcService deliveryProxyRpcService;

    @MSharpReference(remoteAppkey = "shop-category-service")
    private BrandService brandService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private SupplierRpcService supplierRpcService;

    @MSharpReference(remoteAppkey = "store-gaea-service")
    private CategoryI18nRpcService categoryI18nRpcService;

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private BankAccountRpcService bankAccountRpcService;

    @MSharpReference(remoteAppkey = "shop-srm-service")
    private com.ruijing.shop.srm.api.service.SupplierRpcService suppSrmRpcService;


    @MSharpReference(remoteAppkey = "shop-trade-service")
    private OrderDeliveryInfoRpcService orderDeliveryInfoRpcService;

    /**
     * 获取供应商用户信息(目前仅打印送货单用到)
     *
     * @param suppId
     * @param userTag {@link com.ruijing.shop.crm.api.support.enums.UserTagEnum}
     * @return
     */
    public List<UserAccountDTO> querySuppAccountList(Integer suppId, String userTag) {
        UserAccountPageableQry userAccountPageableQry = new UserAccountPageableQry(0, 1000);
        userAccountPageableQry.setSupplierId(suppId);
        userAccountPageableQry.setTag(userTag);
        PageableResponse<List<UserAccountDTO>> response = userAccountManageRpcService.queryPageableAccount(userAccountPageableQry);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        List<UserAccountDTO> userAccountDTOList = response.getData();
        if(CollectionUtils.isEmpty(userAccountDTOList)){
            return New.list();
        }
        return userAccountDTOList;
    }

    /**
     * 查询供应商是否已入驻
     * @param orgId
     * @param suppId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询供应商是否已入驻")
    public Boolean hasSettled(Integer orgId, Integer suppId) {
        RemoteResponse<List<OrgSuppSmallDTO>> remoteResponse = orgIncludeSuppRpcService.findIncludeByOrgId(orgId, OrgIncludeSuppStatusEnum.SETTLED_IN);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        List<OrgSuppSmallDTO> orgSuppDTOList = remoteResponse.getData();
        if (CollectionUtils.isNotEmpty(orgSuppDTOList)) {
            List<Integer> supplierIdList = orgSuppDTOList.stream().map(OrgSuppSmallDTO::getSuppId).collect(Collectors.toList());
            if (supplierIdList.contains(suppId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询供应商在单位的信息
     * @param orgId
     * @param suppId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询供应商在单位的信息")
    public List<OrgIncludeSuppDTO> selectUserIncludeSuppInfo(Integer orgId, Integer suppId) {
        RemoteResponse<List<OrgIncludeSuppDTO>> remoteResponse = orgIncludeSuppRpcService.selectByOrgIdAndSuppId(orgId, suppId);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 批量插入供应商入驻信息
     * @param OrgIncludeSuppDTOs
     * @return
     */
    public Boolean batchSaveUserIncludeSupp(List<OrgIncludeSuppDTO> OrgIncludeSuppDTOs) {
        RemoteResponse<Boolean> remoteResponse = orgIncludeSuppRpcService.batchSaveUserIncludeSupp(OrgIncludeSuppDTOs);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 批量更新入驻状态
     * @param orgId
     * @param suppIds
     * @param status
     * @return
     */
    public Boolean batchUpdateUserIncludeStatus(Integer orgId, List<Integer> suppIds, OrgIncludeSuppStatusEnum status) {
        RemoteResponse<Boolean> remoteResponse = orgIncludeSuppRpcService.batchUpdateStatus(orgId, suppIds, status);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }


    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据供应商id查询线上供应商集合")
    public List<SupplierDTO> getSupplierListByIds(List<Integer> suppIds) {
        if (CollectionUtils.isEmpty(suppIds)) {
            return New.list();
        }
        // 分批调用
        List<SupplierDTO> resultList = New.list();
        List<List<Integer>> partition = Lists.partition(suppIds, 200);
        for (List<Integer> part : partition) {
            SupplierMultiQry qry = new SupplierMultiQry();
            qry.setSupplierIds(New.list(part));
            List<SupplierDTO> supplierDTOList = queryMultiSupplier(qry);
            resultList.addAll(supplierDTOList);
        }
        return resultList;
    }


    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据供应商id查询线上供应商")
    public SupplierDTO getSuppById(Integer suppId) {
        List<SupplierDTO> supplierDTOList = this.getSupplierListByIds(New.list(suppId));
        if (CollectionUtils.isNotEmpty(supplierDTOList)) {
            return supplierDTOList.get(0);
        }
        return null;
    }


    /**
     * 依据供应商针对单位特异配置的联系方式
     * @param suppIds
     * @param orgId
     * @return
     */
    public Map<Integer, SuppShopInfoBO> getSupplierContactInfoMap(List<Integer> suppIds, Integer orgId) {
        return getSupplierContactInfoMap(suppIds, orgId, null);
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据供应商id集合, 机构id, 供应商用户类型查询供应商信息集合")
    public Map<Integer, SuppShopInfoBO> getSupplierContactInfoMap(List<Integer> suppIds, Integer orgId, Integer type) {
        if (CollectionUtils.isEmpty(suppIds)) {
            return new HashMap<>();
        }
        Map<Integer, SuppShopInfoBO> suppIdInfoMap = new HashMap<>();
        SupplierContactQry query = new SupplierContactQry();
        query.setOrgId(orgId.longValue());
        query.setSuppIds(suppIds);
        query.setType(type);
        RemoteResponse<List<SuppContactDTO>> response = shopSuppContactService.querySuppContact(query);
        Preconditions.isTrue(response != null && response.isSuccess(), "依据供应商针对单位特异配置的联系方式失败，" + response.getMsg());
        if (CollectionUtils.isEmpty(response.getData())) {
            return new HashMap<>();
        }
        for (SuppContactDTO suppContact : response.getData()) {
            suppIdInfoMap.putIfAbsent(suppContact.getSuppId(), this.suppContactToShopInfoTranslator(suppContact));
        }
        return suppIdInfoMap;
    }

    /**
     * SuppContactDTO 转换为 SuppShopInfoBO
     * @return
     */
    private SuppShopInfoBO suppContactToShopInfoTranslator(SuppContactDTO suppContact) {
        SuppShopInfoBO out = new SuppShopInfoBO();
        out.setId(suppContact.getSuppId().longValue());
        out.setMobile(suppContact.getTelephone());
        out.setTelephone(suppContact.getTelephone());
        out.setFcontactman(suppContact.getName());
        out.setQq(suppContact.getQq());
        return out;
    }

    /**
     * 线下单根据供应商id查询供应商信息
     * @param suppId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "线下单根据供应商id查询供应商信息")
    public OfflineSupplierDTO getOfflineSuppById(Integer suppId) {
        return this.getOfflineSuppByIdList(New.list(suppId)).get(0);
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "线下单根据供应商id查询供应商信息")
    public List<OfflineSupplierDTO> getOfflineSuppByIdList(List<Integer> suppIdList){
        OfflineSupplierParam request = new OfflineSupplierParam();
        request.setIds(suppIdList);
        request.setPageNum(1);
        request.setPageSize(1);
        PageResultBean<OfflineSupplierDTO> response = offlineSupplierService.getOfflineSupplierPage(request);
        Preconditions.isTrue(ResultBean.SUCCESS == response.getCode(), "线下单根据供应商id查询供应商信息异常：" + JsonUtils.toJsonIgnoreNull(response));
        Preconditions.isTrue(CollectionUtils.isNotEmpty(response.getRecords()), "线下单根据供应商id查询供应商信息异常：" + JsonUtils.toJsonIgnoreNull(response));
        return response.getRecords();
    }
    
    /**
     * @description: 通过供应商id获取供应商信息
     * @date: 2020/12/21 10:20
     * @author: zengyanru
     * @param suppIdList
     * @return com.ruijing.shop.crm.api.pojo.dto.QualificationDTO
     */ 
    @ServiceLog(description = "通过供应商id列表批量获取供应商信息",serviceType = ServiceType.COMMON_SERVICE)
    public Map<Integer, QualificationDTO> getSuppContactInfo(List<Integer> suppIdList) {
        if(CollectionUtils.isEmpty(suppIdList)){
            return New.emptyMap();
        }
        List<List<Integer>> suppIdPartList = Lists.partition(suppIdList, 200);
        List<QualificationDTO> resultList = New.listWithCapacity(suppIdList.size());
        for(List<Integer> part : suppIdPartList){
            QualParam qualParam = new QualParam();
            qualParam.setSuppIds(New.list(part));
            ResultBean<List<QualificationDTO>> suppInfo = shopQualificationService.getQualifications(qualParam);
            Preconditions.notNull(suppInfo,"调用供应商资质接口失败，请重试或联系客服");
            Preconditions.isTrue(suppInfo.getCode()==1,"获取供应商信息失败");
            Preconditions.notNull(suppInfo.getData(),"获取供应商信息失败");
            resultList.addAll(suppInfo.getData());
        }

        return resultList.stream()
                .collect(Collectors.toMap(QualificationDTO::getSuppId, Function.identity(),(oldValue,newValue) -> oldValue));
    }

    /**
     * 根据供应商id列表获取供应商认证信息列表
     *
     * @param suppIdList 供应商id列表
     * @return 认证信息列表
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取供应商认证信息")
    public List<QualificationDTO> getQualificationList(List<Integer> suppIdList) {
        List<List<Integer>> suppIdPartitionList = Lists.partition(suppIdList, 200);
        List<QualificationDTO> qualificationDTOList = new ArrayList<>(suppIdList.size());
        suppIdPartitionList.forEach(list -> {
            QualParam qualParam = new QualParam();
            qualParam.setSuppIds(New.list(list));
            ResultBean<List<QualificationDTO>> resultBean = shopQualificationService.getQualifications(qualParam);
            Preconditions.isTrue(ResultBean.SUCCESS == resultBean.getCode(), resultBean.getMsg());
            if (CollectionUtils.isNotEmpty(resultBean.getData())) {
                qualificationDTOList.addAll(resultBean.getData());
            }
        });
        return qualificationDTOList;
    }

    /**
     * @description: 通过供应商用户id获取用户信息
     * @date: 2020/12/22 17:23
     * @author: zengyanru
     * @param suppUserId
     * @return com.ruijing.shop.crm.api.pojo.dto.UserDTO
     */ 
    @ServiceLog(description = "通过供应商用户id获取用户信息",serviceType = ServiceType.COMMON_SERVICE)
    public UserDTO getSuppUserById(Integer suppUserId) {
        UserParam param = new UserParam();
        param.setId(suppUserId);
        ResultBean<UserDTO> suppUserResp = crmUserService.getUser(param);
        Preconditions.notNull(suppUserResp,"调用供应商获取用户接口失败，请重试或联系客服");
        Preconditions.isTrue(suppUserResp.getCode() == 1,"获取供应商用户信息失败");
        return suppUserResp.getData();
    }

    /**
     * @description: 查询供应商代理品牌信息, 无品牌返回null
     * @date: 2021/1/4 18:01
     * @author: zengyanru
     * @param suppId
     * @param brandId
     * @return com.ruijing.shop.crm.api.pojo.dto.SuppBrandDTO
     */ 
    @ServiceLog(description = "查询供应商代理品牌信息", serviceType = ServiceType.COMMON_SERVICE)
    public SuppBrandDTO getSuppBrand(Integer suppId, Integer brandId) {
        if (Objects.isNull(suppId) || Objects.isNull(brandId)) {
            return null;
        }
        SuppBrandDTO query = new SuppBrandDTO();
        query.setSuppId(suppId);
        query.setBrandId(brandId);
        RemoteResponse<List<SuppBrandDTO>> response = suppBrandRpcService.getSuppBrands(query);

        String methodName = "getSuppBrand";
        if (response == null || !Objects.equals(RemoteResponse.SUCCESS, response.getCode()) || CollectionUtils.isEmpty(response.getData())) {
            String extraErrorMsg = response == null ? "" : response.getMsg();
            Cat.logError(CAT_TYPE, methodName, "查询供应商代理品牌信息失败" + extraErrorMsg + "，入参\nsuppId: " + suppId + ", brandId: " + brandId, new RuntimeException("查询供应商代理品牌信息RPC调用失败"));
            return null;
        } else {
            return response.getData().get(0);
        }
    }

    /**
     * 获取采购商品运费信息，单个商品的运费
     * @param request
     * @return
     */
    public List<BizSearchProductDetailCarryFeeDTO> listProductCarryFee(BizSearchProductCarryFeeDTO request) {
        ApiResult<BizSearchProductCarryFeeResultDTO> response = bizFareTemplateService.findProductCarryFee(request);
        Preconditions.notNull(response, "查询商品运费回应为空");
        Preconditions.isTrue(response.successful(), "查询商品运费回应不成功，" + response.getMsg());
        if (Objects.isNull(response.getData())) {
            return New.emptyList();
        }
        return response.getData().getDetailCarryFeeDTOList();
    }

    /**
     * 获取商家和代配送院区的对应关系
     * @return
     */
    public Map<Integer, List<String>> getSuppIdDeliveryProxyLabel(Integer orgId, List<Integer> supplierIdList) {
        Map<Integer, List<String>> deliveryProxySuppIdCampusMap = new HashMap<>();
        List<List<Integer>> partition = Lists.partition(supplierIdList, 200);
        for (List<Integer> part : partition) {
            DeliveryProxyMultiSuppQueryDTO query = new DeliveryProxyMultiSuppQueryDTO();
            query.setSuppIds(New.list(part));
            query.setOrgId(orgId);
            RemoteResponse<List<DeliveryProxyDTO>> response = deliveryProxyRpcService.queryMultiSuppConfig(query);
            Preconditions.isTrue(response != null && response.isSuccess(), "获取代配送配置的供应商失败");
            List<DeliveryProxyDTO> deliveryProxyList = response.getData();
            for (DeliveryProxyDTO deliveryProxy : deliveryProxyList) {
                // 1表示开启代配送
                if (Objects.equals(deliveryProxy.getDeliveryProxy(), 1)) {
                    if (deliveryProxy.getLabels() != null) {
                        List<String> suppDeliveryProxyLabels = deliveryProxy.getLabels().stream().map(DeliveryAddressLabelDTO::getLabelName).collect(Collectors.toList());
                        deliveryProxySuppIdCampusMap.put(deliveryProxy.getSuppId(), suppDeliveryProxyLabels);
                    }
                }
            }
        }
        return deliveryProxySuppIdCampusMap;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据名字模糊查找供应商信息")
    public List<com.ruijing.shop.srm.api.dto.supp.TcardSupplyerInfoDTO> fuzzySearchSuppInfoByName(String name){
        RemoteResponse<List<com.ruijing.shop.srm.api.dto.supp.TcardSupplyerInfoDTO>> response = suppSrmRpcService.searchSuppByCodeOrName(name);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }


    /**
     * 根据品牌名称查询品牌id
     * @param chnName 中文名
     * @param engName 英文名
     * @return 查询匹配的品牌
     */
    public List<BrandDTO> queryBrandList(String chnName, String engName) {
        BrandDTO brandReq = new BrandDTO();
        brandReq.setCName(chnName);
        brandReq.setEName(engName);
        ApiResult<List<BrandDTO>> brandResult = brandService.queryBrandList(brandReq);
        Preconditions.isTrue(brandResult.successful(), brandResult.getMessage());
        return brandResult.getData();
    }


    /**
     * 供应商自动发货（单位不关心发货状态，且供应商发货了没有及时做发货操作时使用）
     * @param suppCode 商家代码
     * @param orderNo 订单号
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE, description = "供应商自动发货")
    public void autoDelivery(String suppCode, String orderNo){
        OuterSupplierDeliveryDTO outerSupplierDeliveryDTO = new OuterSupplierDeliveryDTO();
        outerSupplierDeliveryDTO.setOrderNo(orderNo);
        outerSupplierDeliveryDTO.setDeliveryType(DeliveryTypeEnum.SELF.code);
        outerSupplierDeliveryDTO.setSupplierCode(suppCode);
        outerSupplierDeliveryDTO.setDeliveryDate(new Date());
        outerSupplierDeliveryDTO.setDeliveryRemark("供应商未确认发货，单位收货导致系统自动发货。");
        RemoteResponse<Boolean> remoteResponse = outerSupplierRequestService.deliveryOrder(outerSupplierDeliveryDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), orderNo + "商家自动发货失败：" + remoteResponse.getMsg());
    }

    /**
     * 根据条件查询线上供应商列表
     *
     * @param query ID、suppCode、name等
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据条件查询线上供应商列表")
    public List<SupplierDTO> queryMultiSupplier(SupplierMultiQry query) {
        RemoteResponse<List<SupplierDTO>> response = supplierRpcService.queryMultiSupplier(query);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * 商品分类国际化
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public Map<String, String> batchTranslateCategoryName(List<String> categoryNames) {
        String language = RpcContext.getProviderContext().getHeader("v-site-language", "cnzh");
        return batchTranslateCategoryName(categoryNames, language);
    }

    /**
     * 商品分类国际化
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public Map<String, String> batchTranslateCategoryName(List<String> categoryNames, String language) {
        RemoteResponse<Map<String, String>> response = categoryI18nRpcService.batchTranslateCategoryName(New.set(categoryNames), language);
        if (!response.isSuccess()) {
            return New.emptyMap();
        }
        return response.getData();
    }


    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据供应商id批量查询供应商银行账户")
    public List<BankAccountDTO> listBankAccountBySuppIds(List<Integer> suppIds) {
        if (CollectionUtils.isEmpty(suppIds)) {
            return New.emptyList();
        }
        SuppBankQueryDTO query = new SuppBankQueryDTO();
        query.setSuppIdList(suppIds);
        query.setDefaultFlag(1);
        RemoteResponse<List<BankAccountDTO>> response = bankAccountRpcService.query(query);
        Preconditions.isTrue(response.isSuccess(), "根据供应商id批量查询供应商银行账户异常：" + response.getMsg());
        return response.getData();
    }

    /**
     * 获取供应商对应单位业务员信息
     * @param suppId 供应商id
     * @param orgId  采购单位id
     * @return       供应商集合
     */
    @ServiceLog(description = "获取供应商对应单位业务员信息", serviceType = ServiceType.RPC_CLIENT)
    public List<UserDTO> getSuppliersOrgBusinessUser(Integer suppId, Integer orgId) {
        // 获取供应商业务员
        RemoteResponse<List<UserDTO>> response = crmUserService.getOrgBusinessUser(orgId, suppId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if (CollectionUtils.isEmpty(response.getData())) {
            return New.emptyList();
        }
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "查询发货详情")
    public List<OrderDeliveryInfoDTO> queryOrderDeliveryInfo(List<Integer> orderIds){
        if(CollectionUtils.isEmpty(orderIds)){
            return New.emptyList();
        }
        // 分批调用，汇总结果，每批200
        List<OrderDeliveryInfoDTO> resultList = New.list();
        List<List<Integer>> partition = Lists.partition(orderIds, 200);
        for (List<Integer> part : partition) {
            OrderDeliveryInfoQuery orderDeliveryInfoQuery = new OrderDeliveryInfoQuery();
            orderDeliveryInfoQuery.setOrderIds(New.list(part));
            RemoteResponse<List<OrderDeliveryInfoDTO>> response = orderDeliveryInfoRpcService.queryOrderDeliveryInfo(orderDeliveryInfoQuery);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (CollectionUtils.isNotEmpty(response.getData())) {
                resultList.addAll(response.getData());
            }
        }
        return resultList;
    }
}
