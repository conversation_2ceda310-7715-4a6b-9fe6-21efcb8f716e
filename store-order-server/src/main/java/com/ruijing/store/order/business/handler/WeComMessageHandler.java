package com.ruijing.store.order.business.handler;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.message.api.dto.WeComTextCardDTO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.order.rpc.client.SendMsgClient;
import com.ruijing.user.rpc.enumation.BusinessIdEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-03-20 09:26
 * @description:
 **/
@Service
@ServiceLog
public class WeComMessageHandler {

    /**
     * 企业微信跳转到微信的通用前缀
     */
    private final static String URL_PREFIX = Environment.isProdEnv() ? "https://m.rjmart.cn/PI/login?loginForWX=1&backToWX=" :
            "https://m.test.rj-info.com/PI/login?loginForWX=1&backToWX=";

    /**
     * 跳转到微信链接的前缀
     */
    private final static String BACK_TO_WX_CONTENT = "acceptDetails?id=%d";

    /**
     * 转换用的字符集
     */
    private final static String URL_ENCODE_CHARSET = "UTF-8";

    @Resource
    private SendMsgClient sendMsgClient;


    /**
     * 发验收审批企信
     * @param userGuidList 用户guid
     * @param orderId 订单id
     * @param context 内容
     * @param orgId 单位id
     */
    public void sendWaitingAcceptApproveWeComMsg(List<String> userGuidList, Integer orderId, String context, Integer orgId){
        if(orgId != OrgEnum.SHEN_ZHEN_REN_MIN_YI_YUAN.getValue()){
            // 深圳龙华定制，不是就不发了
            return;
        }
        // 查配置，没开启配置的用户就不发了
        List<String> enableUserGuidList = sendMsgClient.getOrgEnableMsgPushGuidList(userGuidList, orgId, SendingBusinessEnum.YS_APPROVE_SUCCESS_TO_ORG, SendingWayEnum.ENT_WECHAT);
        if(CollectionUtils.isEmpty(enableUserGuidList)){
            return;
        }
        WeComTextCardDTO weComTextCardDTO = new WeComTextCardDTO();
        weComTextCardDTO.setBusinessId(BusinessIdEnum.STORE);
        WeComTextCardDTO.TextCard textCard = new WeComTextCardDTO.TextCard();
        String url = StringUtils.EMPTY;
        try{
            url = URL_PREFIX + URLEncoder.encode(String.format(BACK_TO_WX_CONTENT, orderId), URL_ENCODE_CHARSET);
        }catch (Exception e){
            Preconditions.isTrue(false, e.getMessage());
        }
        textCard.setUrl(url);
        textCard.setTitle("审核提醒");
        textCard.setContent(context);
        textCard.setButtonText("详情");
        weComTextCardDTO.setTextCard(textCard);
        weComTextCardDTO.setOrgId(orgId.toString());
        weComTextCardDTO.setGuidList(enableUserGuidList);
        sendMsgClient.sendWeComMsg(weComTextCardDTO);
    }
}
