package com.ruijing.store.order.gateway.buyercenter.request;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/22 16:22
 **/
public class FundCardProjectRequestDTO implements Serializable {

    private static final long serialVersionUID = 8640795136098727109L;

    /**
     * id
     */
    @RpcModelProperty("项目id，必填")
    private String id;

    /**
     * 项目code
     */
    @RpcModelProperty("项目code，一级经费卡的单位必填")
    private String projectCode;

    /**
     * 项目名称
     */
    @RpcModelProperty("项目名称")
    private String projectName;

    /**
     * 经费卡使用金额，只有眼科在使用
     */
    @RpcModelProperty("经费卡使用金额，非对接卡且是经费卡层级是一级才会用到")
    private BigDecimal useAmount;

    /**
     * 经费卡
     */
    @RpcModelProperty("经费卡列表")
    private List<OrderFundCardRequestDTO> saveFundCardList;

    /**
     * 经费类型，只有中肿会用
     */
    @RpcModelProperty("经费类型，只有中肿会用，中肿必填")
    private Integer fundType;

    @RpcModelProperty("经费支出申请单号")
    private String expenseApplyNo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public List<OrderFundCardRequestDTO> getSaveFundCardList() {
        return saveFundCardList;
    }

    public void setSaveFundCardList(List<OrderFundCardRequestDTO> saveFundCardList) {
        this.saveFundCardList = saveFundCardList;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }

    @Override
    public String toString() {
        return "FundCardProjectRequestDTO{" +
                "id='" + id + '\'' +
                ", projectCode='" + projectCode + '\'' +
                ", projectName='" + projectName + '\'' +
                ", useAmount=" + useAmount +
                ", saveFundCardList=" + saveFundCardList +
                ", fundType=" + fundType +
                ", expenseApplyNo='" + expenseApplyNo + '\'' +
                '}';
    }
}
