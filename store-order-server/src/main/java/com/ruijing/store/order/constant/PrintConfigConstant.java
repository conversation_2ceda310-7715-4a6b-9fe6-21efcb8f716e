package com.ruijing.store.order.constant;

/**
 * <AUTHOR>
 * @date 2022/9/15 10:13
 * @description
 */
public class PrintConfigConstant {

    /**
     * 送货单 打印按钮是否在对应订单状态展示配置
     */
    public static String SHOW_PRINT_DELIVERY_NOTE_BUTTON_STATUS = "DELIVERY_ORDER_PRINT";

    /**
     * 验收单 打印按钮是否在对应订单状态展示配置
     */
    public static String SHOW_PRINT_ACCEPTANCE_BUTTON_STATUS = "ACCEPTANCE_ORDER_PRINT";

    /**
     * 出入库单 打印按钮是否在对应订单状态展示配置
     */
    public static String SHOW_PRINT_WAREHOUSE_APPLICATION_BUTTON_STATUS = "IN_OUT_WARE_ORDER_PRINT";

    /**
     * 入库单 打印按钮是否在对应库房状态展示配置(出入库管理-我的入库单/课题组入库单)
     */
    public static String SHOW_PRINT_ENTRY_WAREHOUSE_APPLICATION_BUTTON_STATUS = "IN_WARE_ORDER_PRINT";

    /**
     * 申领单 打印按钮是否在对应申领单状态展示配置(出入库管理-我的申领单/课题组申领单)
     */
    public static String SHOW_PRINT_WAREHOUSE_CLAIM_APPLICATION_BUTTON_STATUS = "APPLY_WARE_ORDER_PRINT";
}
