package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.cms.api.dto.SendingPersonalAndDefaultDTO;
import com.ruijing.store.cms.api.dto.SendingPersonalSettingDTO;
import com.ruijing.store.cms.api.request.SendingPersonalAndDefaultParam;
import com.ruijing.store.cms.api.request.SendingSettingParam;
import com.ruijing.store.cms.api.service.SendingPersonalSettingService;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @author: zhukai
 * @date : 2020/3/5 1:32 下午
 * @description: Cms服务提供的接口
 */
@ServiceClient
public class CmsServerClient {

    @MSharpReference(remoteAppkey="store-cms-service")
    private SendingPersonalSettingService sendingPersonalSettingService;

    /**
     * 获取发送配置
     * @param sendingSettingParamList
     * @return
     */
    @ServiceLog(description = "获取发送配置",serviceType = ServiceType.RPC_CLIENT)
    public List<SendingPersonalAndDefaultDTO> getSendSetting(List<SendingSettingParam> sendingSettingParamList){
        List<SendingPersonalAndDefaultDTO> resultList = New.emptyList();
        //如果要查的设置列表不空，则调RPC查
        if (CollectionUtils.isNotEmpty(sendingSettingParamList)) {
            RemoteResponse<List<SendingPersonalAndDefaultDTO>> settingResponse = sendingPersonalSettingService.getSendingSettingByList(sendingSettingParamList);
            resultList = settingResponse.getData();
        }
        return resultList;
    }

    /**
     * 获取发送配置
     * @param sendingSettingParamList
     * @return
     */
    @ServiceLog(description = "获取发送配置",serviceType = ServiceType.RPC_CLIENT)
    public List<SendingPersonalAndDefaultDTO> getSettingListByPersonalAndDefault(SendingPersonalAndDefaultParam sendingSettingParamList) {
        List<SendingPersonalAndDefaultDTO> resultList = New.emptyList();
        if (sendingSettingParamList == null) {
            return Collections.emptyList();
        }
        //如果要查的设置列表不空，则调RPC查
        RemoteResponse<List<SendingPersonalAndDefaultDTO>> settingResponse = sendingPersonalSettingService.getSettingListByPersonalAndDefault(sendingSettingParamList);
        resultList = settingResponse.getData();
        return resultList;
    }

    @ServiceLog(description = "获取个人发送配置", serviceType = ServiceType.RPC_CLIENT)
    public List<SendingPersonalSettingDTO> listPersonSetting(List<SendingSettingParam> param) {
        RemoteResponse<List<SendingPersonalSettingDTO>> response = sendingPersonalSettingService.listPersonSetting(param);
        Preconditions.isTrue(response.isSuccess(), "获取个人发送配置失败");
        return response.getData();
    }
}
