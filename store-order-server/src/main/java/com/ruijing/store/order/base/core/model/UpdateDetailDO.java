package com.ruijing.store.order.base.core.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/11/17 0017 17:27
 * @Version 1.0
 * @Desc:描述
 */
public class UpdateDetailDO {

    /**
     * 订单详情id
     */
    private Integer id;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    private String firstCategoryName;

    /**
     * 二级分类id
     */
    private Integer secondCategoryId;

    /**
     * 二级分类名称
     */
    private String secondCategoryName;

    /**
     * 报账类型
     */
    private String feeTypeTag;

    /**
     * 分类标签（顶级分类）
     */
    private String categoryTag;

    /**
     * 危化品类型id
     */
    private Integer dangerousTypeId;

    /**
     * 危化品类型名称
     */
    private String dangerousTypeName;

    /**
     * 管制品类型id
     */
    private Integer regulatoryTypeId;

    /**
     * 管制品类型名称
     */
    private String regulatoryTypeName;

    /**
     * casNo
     */
    private String casNo;

    /**
     * 供应商id
     *
     */
    private Integer suppId;

    /**
     * 供应商名称
     */
    private String suppName;

    /**
     * 供应商code
     */
    private String suppCode;

    /**
     * 品牌英文名
     */
    private String brandEname;

    /**
     * 商品表记录的商品分类id
     */
    private Integer categoryId;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public void setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public Integer getDangerousTypeId() {
        return dangerousTypeId;
    }

    public void setDangerousTypeId(Integer dangerousTypeId) {
        this.dangerousTypeId = dangerousTypeId;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getRegulatoryTypeId() {
        return regulatoryTypeId;
    }

    public void setRegulatoryTypeId(Integer regulatoryTypeId) {
        this.regulatoryTypeId = regulatoryTypeId;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public void setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getBrandEname() {
        return brandEname;
    }

    public void setBrandEname(String brandEname) {
        this.brandEname = brandEname;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public UpdateDetailDO setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
