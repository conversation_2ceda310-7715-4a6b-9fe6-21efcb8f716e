package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.ThirdPartOrderRPCClient;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * @description: 入库完成回调订单默认实现
 * @author: zhongyulei
 * @create: 2022-01-14 15:11
 */
@Service
@Primary
public class InboundSucceedCallbackService {

    private final static String CAT_TYPE = "InboundSucceedCallbackService";
    
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;


    public OrderStatementService getOrderStatementService() {
        return orderStatementService;
    }

    public OrderMasterMapper getOrderMasterMapper() {
        return orderMasterMapper;
    }

    public void setOrderMasterMapper(OrderMasterMapper orderMasterMapper) {
        this.orderMasterMapper = orderMasterMapper;
    }
    
    /**
     * 推送待入库状态
     * @param orderId 订单id
     */
    public boolean pushWarehousingToThirdPlatForm(Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        if (dockingConfigCommonService.getIfNeedOldPush(orderMasterDO, New.list(OuterBuyerDockingTypeEnum.ORDER_NO_PUSH, OuterBuyerDockingTypeEnum.ORDER_PUSH))) {
            ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderMasterDO);
            List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
            if (CollectionUtils.isNotEmpty(detailDOList)) {
                thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
            }
            try{
                thirdPartOrderRPCClient.pushOrderInfo(thirdPartOrder, OrderEventTypeEnum.WAREHOUSING_ORDER, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
            }catch (Exception e){
                logger.error("推送入库状态异常:" + e);
                Cat.logError(CAT_TYPE, "pushTPIOrderInfo", "推送入库状态异常", e);
                // 解决广工那种推送订单校验参数不通过的问题，这里更新下docking日志
                dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra(orderMasterDO.getForderno(), null, DockingPushStatusEnum.ARGUMENT_EXCEPTION.getCode(), e.getMessage()));
                // 需要更新一下为推送失败状态，作为重新推送的标识
                if(OrgEnum.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orderMasterDO.getFusercode())){
                    orderMasterMapper.updateOrderById(new UpdateOrderParamDTO(){{
                        setOrderId(orderMasterDO.getId());
                        setInventoryStatus(InventoryStatusEnum.FAILED_TO_PUSH.getCode());
                    }});
                }
                return false;
            }
            return true;
        }
        return true;
    }

    /**
     * 订单入库回调完成事件
     * @param inventoryStatus 订单状态
     * @param orderInfo 订单信息
     * @return 是否处理
     */
    public boolean inBoundCallBack(Integer inventoryStatus, OrderMasterDO orderInfo) {
        // 订单状态为待结算，即已经完成了验收审批或者验收时才执行
        if (!OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderInfo.getStatus())) {
            return false;
        }
        // 系统id
        final Integer operatorId = -1;
        // 查询自动发起结算配置
        final String orgCode = orderInfo.getFusercode();
        // 记录订单日志
        orderApprovalLogService.createOrderOperateLog(orderInfo.getId(), OrderApprovalEnum.IN_BOUND_CALLBACK.getValue(), operatorId, "系统", null);

        //查询当前订单是否配置使用结算系统
        List<String> configCodeList = New.list(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
        Map<String, String> receiptConfigMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orgCode, configCodeList);

        /* 当前单位是否使用结算系统 */
        boolean usedStatement = orderStatementService.isUseStatement(receiptConfigMap, orderInfo);
        this.statementHandler(inventoryStatus, orderInfo, usedStatement);

        return true;
    }

    /**
     * 结算相关处理
     * @param inventoryStatus   入库状态
     * @param orderInfo             订单快照信息
     * @param usedStatement         是否使用结算
     */
    protected void statementHandler(Integer inventoryStatus, OrderMasterDO orderInfo, boolean usedStatement) {
        if (OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderInfo.getFundStatus())) {
            UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
            updated.setOrderId(orderInfo.getId());
            updated.setStatus(OrderStatusEnum.Finish.getValue());
            orderMasterMapper.updateOrderById(updated);
        } else {
            // 提交结算
            orderStatementService.orderStatementCore(orderInfo, orderInfo.getFbuyerid(), orderInfo.getFbuyername(), usedStatement, inventoryStatus);
        }
    }
}
