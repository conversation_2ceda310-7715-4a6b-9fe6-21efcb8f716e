package com.ruijing.store.order.service.impl;

import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageProductDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageRequestDTO;
import com.ruijing.store.apply.enums.application.ApplyManageBusinessTypeEnum;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.BidClient;
import com.ruijing.store.order.rpc.client.OrderFundCardCacheClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.service.ApplicationBaseService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 采购团队的业务方法集成类
 * @author: zhangzhifeng
 * @date: 2021-05-07 18:26
 **/
@Service
@CatAnnotation
public class ApplicationBaseServiceImpl implements ApplicationBaseService {

    private final static String CAT_TYPE = "ApplicationBaseServiceImpl";

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private BidClient bidClient;

    @Resource
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    /**
     *采购管控--金额统计
     *  同一课题组同一商品名称同一自然月采购金额累计不能超过X万
     */
    private Set<String> procurementOrgSet = New.set(OrgConst.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN,  OrgConst.JIANG_XI_ZHONG_YI_YAO_DA_XUE, OrgConst.JI_NAN_DA_XUE);

    /**
     * 采购管控--金额统计
     *   同一课题组同一商品名称同一自然月采购金额累计不能超过X万
     *
     * @param orderMasterDO 订单信息
     * @param cardId 经费卡id 如果不传则会去查表 t_ref_fundcard_order
     * @param operation 操作类型 从ApplyManageOperationEnum中获取 --> 需要区分 关闭订单、商品退货、换卡释放(暨大)两种业务区别
     * @param goodsReturnInfoDetailVOS 退货详情信息
     */
    @Override
    @ServiceLog(description = "同一商品采购金额统计(根据商品名)", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public Boolean updateApplyManageProductUsage(OrderMasterDO orderMasterDO, Integer operation, String cardId, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS) {
        Preconditions.notNull(orderMasterDO, "订单信息为null");
        Preconditions.notNull(operation, "操作类型为null");
        ApplyManageRequestDTO applyManageRequestDTO = new ApplyManageRequestDTO();
        // 商品列表
        applyManageRequestDTO.setProductList(getProductListByOperation(orderMasterDO, operation, goodsReturnInfoDetailVOS));
        // 采购单下单时间的自然年月
        applyManageRequestDTO.setYearMonth(Integer.parseInt(DateUtils.format("yyyyMM", this.getCreateTimeByOrderType(orderMasterDO))));
        // 经费卡id,目前对接的这几个单位只有一个经费卡，且采购接口也只能传一个经费卡id
        if (StringUtils.isEmpty(cardId)) {
            List<RefFundcardOrderDTO> refFundCardList = refFundcardOrderService.findByOrderIdList(New.list(orderMasterDO.getId()));
            // 没有绑定卡则不用传卡id
            if (CollectionUtils.isNotEmpty(refFundCardList)) {
                RefFundcardOrderDTO refFundcardOrderDTO = refFundCardList.get(0);
                cardId = RefFundcardOrderTranslator.getLastLevelCardId(refFundcardOrderDTO);
            }
        }
        applyManageRequestDTO.setCardId(cardId);
        // 订单id
        applyManageRequestDTO.setBusinessId(orderMasterDO.getId());
        // 业务类型
        applyManageRequestDTO.setBusinessType(ApplyManageBusinessTypeEnum.ORDER.getValue());
        // 操作类型
        applyManageRequestDTO.setOperation(operation);
        // 课题组id
        applyManageRequestDTO.setDepartmentId(orderMasterDO.getFbuydepartmentid());
        // 医院id
        applyManageRequestDTO.setOrgId(orderMasterDO.getFuserid());
        // 医院code
        applyManageRequestDTO.setOrgCode(orderMasterDO.getFusercode());
        // 暨大专用(允许自结算的采购单不选择经费卡, 没选经费卡不纳入管控)
        if (OrgConst.JI_NAN_DA_XUE.equals(orderMasterDO.getFusercode())) {
            // 经费类型是自结算 且 非修改为自结算操作， 跳过
            if (Objects.equals(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue(), orderMasterDO.getFundType()) &&
                    !Objects.equals(ApplyManageOperationEnum.UPDATE_STATEMENT_TYPE.getValue(), operation)) {
                return true;
            }

            ApplyMasterExtensionQueryDTO request = new ApplyMasterExtensionQueryDTO();
            request.setApplyIds(New.list(orderMasterDO.getFtbuyappid()));
            List<ApplyMasterExtensionDTO> permitCardByApplicationId = applicationBaseClient.findExtensionByApplicationId(request);
            if (CollectionUtils.isEmpty(permitCardByApplicationId)) {
                return true;
            }
            applyManageRequestDTO.setStatementType(permitCardByApplicationId.get(0).getStatementType());
        }
        return applicationBaseClient.updateApplyManageProductUsage(applyManageRequestDTO);
    }

    /**
     * 获取商品列表
     * @param orderMasterDO
     * @param operation
     * @param goodsReturnInfoDetailVOS
     * @return
     */
    private List<ApplyManageProductDTO> getProductListByOperation(OrderMasterDO orderMasterDO, Integer operation, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
        List<ApplyManageProductDTO> productList = New.list();
        // 退货、取消订单、换卡释放(目前只有暨大)
        List<OrderDetailDO> orderDetailDOList = null;
        if(operation.equals(ApplyManageOperationEnum.PRODUCT_RETURN.getValue())){
            Preconditions.notEmpty(goodsReturnInfoDetailVOS,"退货详情为空");
            List<Integer> detailIdList = goodsReturnInfoDetailVOS.stream().map(GoodsReturnInfoDetailVO::getDetailId).map(Integer::parseInt).collect(Collectors.toList());
            orderDetailDOList = orderDetailMapper.findByIdIn(detailIdList);
        }else {
            orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        }
        Optional.ofNullable(orderDetailDOList).orElse(New.list()).forEach(orderDetailDO -> {
            productList.add(new ApplyManageProductDTO(orderDetailDO.getFgoodname(), orderDetailDO.getFbidamount(), orderDetailDO.getFirstCategoryId()));
        });
        return productList;
    }

    /**
     * 根据单据类型获取采购单或竞价单的创建时间(用于解冻、采购金额统计)
     * @param orderMasterDO
     * @return Date
     */
    @Override
    public Date getCreateTimeByOrderType(OrderMasterDO orderMasterDO){
        Integer orderType = orderMasterDO.getOrderType();
        Date createTime = null;
        if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderType) || OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode().equals(orderType)){
            Integer ftbuyappid = orderMasterDO.getFtbuyappid();
            ApplicationMasterDTO applicationMasterByApplyId = applicationBaseClient.getApplicationMasterByApplyId(ftbuyappid,false);
            BusinessErrUtil.notNull(applicationMasterByApplyId, ExecptionMessageEnum.NO_PURCHASE_ORDER_WITH_ID, ftbuyappid);
            createTime = applicationMasterByApplyId.getCreateTime();
        }else if(OrderTypeEnum.BID_ORDER.getCode().equals(orderType)){
            // 竞价临床不产生订单
            List<BidMasterDTO> bidMasterByBidNoList = bidClient.findBidMasterByBidNoList(New.list(orderMasterDO.getBidOrderId()));
            BusinessErrUtil.notEmpty(bidMasterByBidNoList, ExecptionMessageEnum.CANNOT_FIND_BIDDING_TICKET, orderMasterDO.getBidOrderId());
            BidMasterDTO bidMasterDTO = bidMasterByBidNoList.get(0);
            createTime = bidMasterDTO.getCreatedTime();
        }
        BusinessErrUtil.notNull(createTime, ExecptionMessageEnum.FAILED_TO_OBTAIN_CREATION_TIME, orderMasterDO.getForderno());
        return createTime;
    }

}
