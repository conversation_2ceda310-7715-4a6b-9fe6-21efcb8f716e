package com.ruijing.store.order.base.excel.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

public class OrderExportRequestDTO implements Serializable {
    private static final long serialVersionUID = -1989888770034559376L;

    @RpcModelProperty("id")
    private Integer id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "OrderExportQueryDTO{" +
                "id=" + id +
                '}';
    }
}
