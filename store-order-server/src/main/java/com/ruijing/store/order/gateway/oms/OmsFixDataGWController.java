package com.ruijing.store.order.gateway.oms;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.annotation.MethodParam;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.oms.api.dto.OmsAccessDTO;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.business.service.OmsFixDataService;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.oms.request.FixDataCommonRequest;
import com.ruijing.store.order.gateway.oms.request.FixFundDataRequest;
import com.ruijing.store.order.gateway.oms.request.FixOrderDetailCommonRequest;
import com.ruijing.store.order.gateway.oms.request.OmsFixDataLogQueryRequest;
import com.ruijing.store.order.gateway.oms.response.OmsFixDataResponse;
import com.ruijing.store.order.gateway.oms.vo.OmsFixDataLogVO;
import com.ruijing.store.order.rpc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:45
 * @description
 */
@RpcApi(value = "oms数据修正网关服务", description = "oms数据修正网关服务")
@GateWayController(requestMapping = "/oms/fix")
public class OmsFixDataGWController {

    @Resource
    private OmsFixDataService omsFixDataService;

    @Resource
    private UserClient userClient;

    /**
     * 失败的经费状态
     */
    private final static List<Integer> FAILED_FUND_STATUS_LIST = New.list(OrderFundStatusEnum.ThrawFailed.getValue(), OrderFundStatusEnum.FreezedFail.getValue());

    @RpcMethod(value = "获取冻结解冻失败订单列表")
    @RpcMapping("/getFreezeRelatedFailOrderList")
    public PageableResponse<List<OrderInfoVO>> getFreezeRelatedFailOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("ABNORMAL_DATA"));
        if (CollectionUtils.isEmpty(request.getFundStatus())) {
            request.setFundStatus(FAILED_FUND_STATUS_LIST);
        } else {
            List<Integer> fundStatusList = request.getFundStatus().stream().filter(FAILED_FUND_STATUS_LIST::contains).collect(Collectors.toList());
            Preconditions.notEmpty(fundStatusList, "所查经费状态非失败的经费状态");
        }
        return omsFixDataService.getOrderList(request);
    }

    @RpcMethod(value = "获取需要修正的订单列表")
    @RpcMapping("/getNeedFixOrderList")
    public RemoteResponse<List<OrderInfoVO>> getNeedFixOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("DATA_CORRECT"));
        Preconditions.notEmpty(request.getOrderNoList(), "没有找到相关的数据，请检查单号是否正确");
        Preconditions.isTrue(request.getOrderNoList().size() <= 50, "查询数据量不能大于50");
        request.setPageSize(50);
        return RemoteResponse.success(omsFixDataService.getOrderList(request).getData());
    }

    @RpcMethod(value = "获取修正日志")
    @RpcMapping("/getFixDataLog")
    public PageableResponse<List<OmsFixDataLogVO>> getFixDataLog(RjSessionInfo rjSessionInfo, OmsFixDataLogQueryRequest omsFixDataLogQueryRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("DATA_CORRECT_RECORD"));
        return omsFixDataService.getDataOperationLog(omsFixDataLogQueryRequest);
    }

    @RpcMethod(value = "重新冻结")
    @RpcMapping("/reFreeze")
    public RemoteResponse<OmsFixDataResponse> reFreeze(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("ABNORMAL_DATA", "REFREEZE_FUNDS"));
        Preconditions.notEmpty(fixDataCommonRequest.getIds(), "请选择需要操作的单据。");
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        fixDataCommonRequest.setReason("开始调用冻结经费卡接口");
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.reFreeze(loginUserInfoBO, fixDataCommonRequest)));
    }

    @RpcMethod(value = "重新解冻")
    @RpcMapping("/reUnFreeze")
    public RemoteResponse<OmsFixDataResponse> reUnFreeze(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("ABNORMAL_DATA", "REUNFREEZE_FUNDS"));
        Preconditions.notEmpty(fixDataCommonRequest.getIds(), "请选择需要操作的单据。");
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        fixDataCommonRequest.setReason("开始调用解冻经费卡接口");
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.reUnfreeze(loginUserInfoBO, fixDataCommonRequest)));
    }

    @RpcMethod(value = "解冻")
    @RpcMapping("/unFreeze")
    public RemoteResponse<OmsFixDataResponse> unFreeze(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("DATA_CORRECT"));
        Preconditions.notEmpty(fixDataCommonRequest.getIds(), "请选择需要操作的单据。");
        Preconditions.hasLength(fixDataCommonRequest.getReason(), "请填写修正原因。");
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.unfreeze(loginUserInfoBO, fixDataCommonRequest)));
    }

    @RpcMethod(value = "修正订单状态")
    @RpcMapping("/fixOrderStatus")
    public RemoteResponse<OmsFixDataResponse> fixOrderStatus(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("DATA_CORRECT"));
        this.fixStatusCommonVerify(fixDataCommonRequest);
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.fixOrderStatus(loginUserInfoBO, fixDataCommonRequest)));
    }
    @RpcMethod(value = "修改订单快照商品分类")
    @RpcMapping("/fixOrderDetailProductCategory")
    public RemoteResponse<OmsFixDataResponse> fixOrderDetailProductCategory(RjSessionInfo rjSessionInfo, FixOrderDetailCommonRequest fixDataCommonRequest) {
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.fixOrderDetailProductCategory(loginUserInfoBO, fixDataCommonRequest)));
    }

    @RpcMethod(value = "修正经费状态")
    @RpcMapping("/fixFundStatus")
    public RemoteResponse<OmsFixDataResponse> fixFundStatus(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        this.verifyPermissions(rjSessionInfo.getGuid(), New.list("DATA_CORRECT"));
        this.fixStatusCommonVerify(fixDataCommonRequest);
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.fixFundStatus(loginUserInfoBO, fixDataCommonRequest)));
    }

    @RpcMethod(value = "数据修正-冻结经费")
    @RpcMapping("/freezeFund")
    public RemoteResponse<OmsFixDataResponse> freezeFund(RjSessionInfo rjSessionInfo, FixFundDataRequest fixFundDataRequest) {
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.freezeFund(loginUserInfoBO, fixFundDataRequest)));
    }

    @RpcMethod(value = "数据修正-解冻经费")
    @RpcMapping("/unFreezeFund")
    public RemoteResponse<OmsFixDataResponse> unFreezeFund(RjSessionInfo rjSessionInfo,
                                                           @MethodParam(excludePropertyNames = {"changeFundCardRequestDTO"}) FixFundDataRequest fixDataCommonRequest) {
        LoginUserInfoBO loginUserInfoBO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.unfreezeFund(loginUserInfoBO, fixDataCommonRequest)));
    }


    @RpcMethod(value = "删除测试订单")
    @RpcMapping("/deleteTestOrders")
    public RemoteResponse<OmsFixDataResponse> deleteTestOrders(RjSessionInfo rjSessionInfo, FixDataCommonRequest fixDataCommonRequest) {
        LoginUserInfoBO omsLoginUserInfo = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId().intValue());
        return RemoteResponse.success(new OmsFixDataResponse(omsFixDataService.deleteTestOrders(omsLoginUserInfo, fixDataCommonRequest)));
    }

    /**
     * 通用状态修改校验
     *
     * @param fixDataCommonRequest 修改状态入参
     */
    private void fixStatusCommonVerify(FixDataCommonRequest fixDataCommonRequest) {
        Preconditions.notNull(fixDataCommonRequest.getStatus(), "请选择需要修改状态。");
        Preconditions.notEmpty(fixDataCommonRequest.getIds(), "请选择需要操作的单据。");
        Preconditions.hasLength(fixDataCommonRequest.getReason(), "请填写修正原因。");
    }

    /**
     * 通用鉴权
     *
     * @param userGuid         当前登录用户guid
     * @param accessCodeNeeded 操作该模块所需要的权限
     */
    private void verifyPermissions(String userGuid, List<String> accessCodeNeeded) {
        List<OmsAccessDTO> accessInfoList = userClient.findAccessByGuid(userGuid);
        Set<String> accessCodeList = accessInfoList.stream().map(OmsAccessDTO::getCode).collect(Collectors.toSet());
        Preconditions.isTrue(accessCodeList.containsAll(accessCodeNeeded), "当前登录用户没有此模块权限");
    }

}
