package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: zhu<PERSON>
 * @date : 2020/12/28 上午11:43
 * @description: 采购人中心，订单统计查询入参
 */
@RpcModel("统计管理--订单统计入参")
public class TransactionCountRequest implements Serializable {

    /**
     * 当前页
     */
    @RpcModelProperty("当前页")
    private Integer pageNo = 1 ;

    /**
     * 页数大小
     */
    @RpcModelProperty("页数大小")
    private Integer pageSize = 10;

    /**
     * 开始时间
     */
    @RpcModelProperty("开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @RpcModelProperty("结束时间")
    private Date endTime;

    /**
     * 课题组Id
     *
     */
    @RpcModelProperty("课题组Id")
    private Integer departmentId;

    /**
     * 排序字段
     */
    @RpcModelProperty("排序字段：'amount'-金额，'count'-数量")
    private String orderByField;

    /**
     * 排序
     */
    @RpcModelProperty("1升序、2降序")
    private Integer sort;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getOrderByField() {
        return orderByField;
    }

    public void setOrderByField(String orderByField) {
        this.orderByField = orderByField;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
