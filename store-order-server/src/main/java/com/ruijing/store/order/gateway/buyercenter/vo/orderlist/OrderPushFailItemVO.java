package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Name: OrderPushFailDTO
 * Description: 订单推送失败记录
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/6/9
 */
@RpcModel("订单推送失败记录")
public class OrderPushFailItemVO {

    @RpcModelProperty(value = "订单id")
    private Integer id;

    @RpcModelProperty(value = "订单号")
    private String number;

    @RpcModelProperty(value = "采购人")
    private String buyerName;

    @RpcModelProperty(value = "订单金额")
    private BigDecimal budgetAmount;

    @RpcModelProperty(value = "创建时间")
    private Date pushTime;

    @RpcModelProperty(value = "失败原因")
    private String failReason;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    public String toString() {
        return "OrderPushFailDTO{" +
                ", orderNo='" + number + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", amount=" + budgetAmount +
                ", createTime=" + pushTime +
                ", failReason='" + failReason + '\'' +
                '}';
    }
}
