package com.ruijing.store.order.business.bo.buyercenter.operationlog;

/**
 * 给后端调用出入库信息接口的请求体，不需要序列化不需要交由其他人使用
 * @Author: <PERSON><PERSON>
 * @Date: 2021/1/12 21:51
 */
public class InboundRequest {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 入库单id
     */
    private String inoundId;

    public String getOrderNo() {
        return orderNo;
    }

    public InboundRequest setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getInoundId() {
        return inoundId;
    }

    public InboundRequest setInoundId(String inoundId) {
        this.inoundId = inoundId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InboundRequest{");
        sb.append("orderNo='").append(orderNo).append('\'');
        sb.append(", inoundId='").append(inoundId).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
