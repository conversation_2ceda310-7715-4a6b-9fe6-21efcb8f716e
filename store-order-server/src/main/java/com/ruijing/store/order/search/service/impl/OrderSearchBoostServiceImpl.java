package com.ruijing.store.order.search.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.MapBuilder;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.cache.RedisClient;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.search.client.aggregation.*;
import com.ruijing.search.client.aggregation.nested.NestedAggsItem;
import com.ruijing.search.client.aggregation.nested.NestedSumItem;
import com.ruijing.search.client.aggregation.nested.NestedTermsItem;
import com.ruijing.search.client.aggregation.nested.ReverseNestedAggsItem;
import com.ruijing.search.client.enums.QueryType;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.*;
import com.ruijing.search.client.query.*;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.AggsResultItem;
import com.ruijing.search.client.response.Record;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.score.ItemFunction;
import com.ruijing.search.client.score.ScoreRank;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.BasePageParamDTO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.DeliveryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderConfirmStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.general.enums.OrderNestedEnum;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.api.search.enums.DateUnitEnum;
import com.ruijing.store.order.api.search.enums.OrderAggregationSortFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderMetricFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.OrderApprovalService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.search.translator.OrderPojoTranslator;
import com.ruijing.store.order.service.OldDateService;
import com.ruijing.store.order.util.BigDecimalUtil;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.IllegalInputControlUtil;
import com.ruijing.store.order.util.PageResponseUtils;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggDTO;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggRequest;
import com.ruijing.store.statistic.api.search.dto.ValidPurchaseCountDTO;
import com.ruijing.store.statistic.api.search.enums.AdvertisementOrderAggTermTypeEnum;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @description: 搜索强化
 * @author: zhuk
 * @create: 2019-08-22 14:57
 **/

@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class OrderSearchBoostServiceImpl implements OrderSearchBoostService {

    private static final Integer orderMasterTable = 1;

    private static final Integer orderDetailTable = 2;

    private static final String AMOUNT_ITEM = "amountItem";

    private static final String QUANTITY_ITEM = "quantityIem";

    protected static final String NESTED_TABLE = "order_detail";

    protected static final String ORDER_SEARCH_INDEX = "order";

    private static final String NESTED_CARD = "card";

    private static final String NESTED_APPROVE_LOG = "log";

    private static final String NESTED_EXTRA = "order_extra";

    /**
     * 待验收审批的订单数量缓存前缀
     */
    public static final String ORDER_APPROVAL_COUNT = "ORDER_APPROVAL_COUNT";

    /**
     * 待验收审批的订单数量缓存前缀分布式锁key
     */
    public static final String ORDER_APPROVAL_COUNT_LOCK = "ORDER_APPROVAL_COUNT_LOCK";

    @PearlValue(key = "common.countLimit",defaultValue ="1000")
    private Integer countLimit = 1000;

    @PearlValue(key = "common.countWarn", defaultValue = "false")
    private Boolean countWarn;

    @Resource
    private OrderSearchRPCServiceClient orderSearchRPCServiceClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private UserClient userClient;

    @Resource
    private RedisClient redisClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderApprovalService orderApprovalService;

    @Resource
    private OldDateService oldDateService;

    private static final  String CAT_TYPE = "OrderSearchBoostService";

    private static final String wildCardQueryStr = "*%s*";

    /**
     * 订单搜索通用查询
     * @param orderSearchParamDTO
     * @return
     */
    @Override
    public SearchPageResultDTO<OrderMasterSearchDTO> commonSearch(OrderSearchParamDTO orderSearchParamDTO){
        final Request request = new Request();
        Integer startHit = orderSearchParamDTO.getStartHit();
        request.setStart((startHit == null || startHit < 0) ? 0 : startHit);
        Integer pageSize = orderSearchParamDTO.getPageSize();
        request.setPageSize((pageSize == null || pageSize < 0) ? 20 : pageSize);
        this.paramFillRequest(orderSearchParamDTO, request);
        return this.search(request);
    }

    @Override
    public SearchPageResultDTO<OrderMasterSearchDTO> search(Request request) {
        if (countWarn) {
            String clientAppKey = RpcContext.getProviderContext().getClientAppkey();
            String clientIp = RpcContext.getProviderContext().getClientIp();
            Transaction transaction = Cat.newTransaction("commonSearch", clientAppKey);
            transaction.addData( String.format("ip号为: 【%s】发生了调用", clientIp));
            transaction.complete();
        }
        request.setKey(ORDER_SEARCH_INDEX);
        Response response = orderSearchRPCServiceClient.search(request);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        orderMasterSearchDTOList.forEach(orderMasterSearchDTO ->
                orderMasterSearchDTO.setOldFlag(oldDateService.isOldDate(orderMasterSearchDTO.getFuserid(), DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getForderdate()), orderMasterSearchDTO.getFundStatus(), orderMasterSearchDTO.getSpecies())));
        SearchPageResultDTO<OrderMasterSearchDTO> result = new SearchPageResultDTO<>();
        result.setTotalHits(response.getTotalHits());
        result.setRecordList(orderMasterSearchDTOList);
        return result;
    }

    /**
     * 通用查询参数填充
     * @param orderSearchParamDTO
     * @param request
     */
    private void paramFillRequest(OrderSearchParamDTO orderSearchParamDTO, Request request) {
        // 防止有文档同步失败，要求一定要有订单号
        request.addFilter(new NotNullFilter("forderno"));
        //订单id查询
        Integer orderId = orderSearchParamDTO.getOrderId();
        if (orderId != null){
            request.addFilter(new TermFilter("id", orderId));
        }

        //订单id集合查询
        List<Integer> orderIdList = orderSearchParamDTO.getOrderIdList();
        if (CollectionUtils.isNotEmpty(orderIdList)){
            request.addFilter(new TermFilter("id", orderIdList));
        }
        // 通过bidId查询
        String bidOrderId = orderSearchParamDTO.getBidOrderId();
        if (StringUtils.isNotBlank(bidOrderId)){
            request.addFilter(new TermFilter("bid_order_id", bidOrderId));
        }

        List<String> bidOrderIdList = orderSearchParamDTO.getBidOrderIdList();
        if(CollectionUtils.isNotEmpty(bidOrderIdList)){
            request.addFilter(new TermFilter("bid_order_id", bidOrderIdList));
        }

        // 排除的申请单id集合
        List<String> excludeBidOrderIdList = orderSearchParamDTO.getExcludeBidOrderIdList();
        if (CollectionUtils.isNotEmpty(excludeBidOrderIdList)) {
            request.addNotFilter(new TermFilter("bid_order_id", excludeBidOrderIdList));
        }

        // （中科大附一特殊）经费卡院区搜索
        if (StringUtils.isNotBlank(orderSearchParamDTO.getCampusCode())) {
            TermFilter campusCodeFilter = new TermFilter(NESTED_CARD + ".campus_code", New.list(orderSearchParamDTO.getCampusCode()));
            NestedFilter nestedCardFilter = new NestedFilter(NESTED_CARD, campusCodeFilter);
            request.addFilter(nestedCardFilter);
        }
        if (StringUtils.isNotBlank(orderSearchParamDTO.getCampusName())) {
            TermFilter campusNameFilter = new TermFilter(NESTED_CARD + ".campus_name", New.list(orderSearchParamDTO.getCampusName()));
            NestedFilter nestedCardFilter = new NestedFilter(NESTED_CARD, campusNameFilter);
            request.addFilter(nestedCardFilter);
        }

        // 我的已审批列表，通过审批状态和审批人查询
        if (CollectionUtils.isNotEmpty(orderSearchParamDTO.getApproveStatusList()) && StringUtils.isNotBlank(orderSearchParamDTO.getOperatorId())) {
            TermQuery approveStatusQuery = new TermQuery(NESTED_APPROVE_LOG + ".approve_status", orderSearchParamDTO.getApproveStatusList());
            TermQuery operatorIdQuery = new TermQuery(NESTED_APPROVE_LOG + ".operator_id", orderSearchParamDTO.getOperatorId());
            request.addQuery(new NestedQuery(NESTED_APPROVE_LOG, new AndQuery(New.list(approveStatusQuery,operatorIdQuery))));
        }

        //结算单id
        List<Integer> statementIdList = orderSearchParamDTO.getStatementIdList();
        if (CollectionUtils.isNotEmpty(statementIdList)) {
            request.addFilter(new TermFilter("statement_id", statementIdList));
        }

        // 订单是否走自结算流程筛选
        Boolean useStatement = orderSearchParamDTO.getUseStatement();
        if (useStatement != null){
            request.addFilter(new TermFilter("status", New.list(OrderStatusEnum.Finish.getValue())));
            if (useStatement) {
                request.addFilter(new NotNullFilter("statement_id"));
            } else {
                request.addFilter(new IsNullFilter("statement_id"));
            }
        }

        //包含的订单状态
        List<Integer> statusList = orderSearchParamDTO.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            request.addFilter(new TermFilter("status", statusList));
        }

        //订单状态排除过滤
        List<Integer> excludeStatusList = orderSearchParamDTO.getExcludeStatusList();
        if (CollectionUtils.isNotEmpty(excludeStatusList)) {
            request.addNotFilter(new TermFilter("status", excludeStatusList));
        }

        //结算状态排除过滤
        List<Integer> statementStatusList = orderSearchParamDTO.getStatementStatusList();
        if (CollectionUtils.isNotEmpty(statementStatusList)) {
            request.addFilter(new TermFilter("statement_status", statementStatusList));
        }

        // 退货状态过滤
        List<Integer> excludeReturnStatusList = orderSearchParamDTO.getExcludeReturnStatusList();
        if(CollectionUtils.isNotEmpty(excludeReturnStatusList)){
            request.addNotFilter(new NestedFilter(NESTED_TABLE, new TermFilter(NESTED_TABLE + ".return_status", excludeReturnStatusList)));
        }

        // 经费状态过滤
        List<Integer> excludeFundStatusList = orderSearchParamDTO.getExcludeFundStatusList();
        if(CollectionUtils.isNotEmpty(excludeFundStatusList)){
            request.addNotFilter(new TermFilter("fund_status", excludeFundStatusList));
        }

        // 经费类型过滤
        if(orderSearchParamDTO.getFundType() != null){
            request.addFilter(new NestedFilter(NESTED_CARD, new TermFilter(NESTED_CARD + ".fund_type", orderSearchParamDTO.getFundType())));
        }

        //订单号精确查询
        String orderNo = orderSearchParamDTO.getOrderNo();
        if (StringUtils.isNotBlank(orderNo)) {
            IllegalInputControlUtil.checkSearchInput(orderNo);
            request.addQuery(new PhraseQuery("forderno", orderNo));
        }

        //订单对接推送状态精确查询
        List<Integer> dockingStatus = orderSearchParamDTO.getDockingStatus();
        if (!CollectionUtils.isEmpty(dockingStatus)) {
            request.addFilter(new TermFilter("docking_status", dockingStatus));
        }

        //订单号集合过滤
        List<String> orderNoList = orderSearchParamDTO.getOrderNoList();
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            request.addFilter(new TermFilter("forderno.keyword", orderNoList));
        }

        //流程种类
        ProcessSpeciesEnum species = orderSearchParamDTO.getSpecies();
        if (species != null) {
            request.addFilter(new TermFilter("species", species.getValue()));
        }

        //申请单id集合
        List<Integer> applicationIdList = orderSearchParamDTO.getApplicationIdList();
        if (CollectionUtils.isNotEmpty(applicationIdList)) {
            request.addFilter(new TermFilter("ftbuyappid", applicationIdList));
        }

        // 排除的申请单id集合
        List<Integer> excludeApplicationIds = orderSearchParamDTO.getExcludeApplicationIds();
        if (CollectionUtils.isNotEmpty(excludeApplicationIds)) {
            request.addNotFilter(new TermFilter("ftbuyappid", excludeApplicationIds));
        }

        //部门名称
        String departmentName = orderSearchParamDTO.getDepartmentName();
        if (StringUtils.isNotBlank(departmentName)) {
            IllegalInputControlUtil.checkSearchInput(departmentName);
            request.addQuery(new PhraseQuery("fbuydepartment", departmentName));
        }

        //部门集合
        List<Integer> departmentIdList = orderSearchParamDTO.getDepartmentIdList();
        if (CollectionUtils.isNotEmpty(departmentIdList)) {
            request.addFilter(new TermFilter("fbuydepartmentid", departmentIdList));
        }
        // 父一级部门id
        List<Integer> deptParentIdList = orderSearchParamDTO.getDeptParentIdList();
        if (CollectionUtils.isNotEmpty(deptParentIdList)) {
            request.addQuery(new TermQuery("dept_parent_id", deptParentIdList));
        }
        // 父一级部门名称
        List<String> deptParentNameList = orderSearchParamDTO.getDeptParentNameList();
        if (CollectionUtils.isNotEmpty(deptParentNameList)) {
            request.addQuery(new TermQuery("dept_parent_name", deptParentNameList));
        }

        //单位名称
        String orgName = orderSearchParamDTO.getOrgName();
        if (StringUtils.isNotBlank(orgName)) {
            IllegalInputControlUtil.checkSearchInput(orgName);
            if (Boolean.TRUE.equals(orderSearchParamDTO.getOrgNameFullMatch())) {
                try {
                    OrgEnum orgEnumByName = OrgEnum.getOrgEnumByName(orgName);
                    request.addQuery(new TermQuery("fuserid", New.list(orgEnumByName.getValue())));
                } catch (Exception e) {
                    request.addQuery(new PhraseQuery("fusername", orgName));
                }
            } else {
                request.addQuery(new PhraseQuery("fusername", orgName));
            }
        }

        //机构单位List
        List<Integer> orgIdList = orderSearchParamDTO.getOrgIdList();
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            request.addFilter(new TermFilter("fuserid", orgIdList));
        }

        //采购人姓名 模糊查询
        String buyerName = orderSearchParamDTO.getBuyerName();
        if (StringUtils.isNotBlank(buyerName)) {
            IllegalInputControlUtil.checkSearchInput(buyerName);
            request.addQuery(new PhraseQuery("fbuyername", buyerName));
        }

        // 采购下单时候的联系人姓名 模糊查询
        String buyerContactMan = orderSearchParamDTO.getBuyerContactMan();
        if (StringUtils.isNotBlank(buyerContactMan)) {
            IllegalInputControlUtil.checkSearchInput(buyerContactMan);
            request.addQuery(new PhraseQuery("fbuyercontactman", buyerContactMan));
        }

        // 额外逻辑需要控制展示的订单id列表(当前仅中大，如果设置了这个，会连带设置buyeridlist的值)
        List<Integer> includeOrderIdList = orderSearchParamDTO.getIncludeOrderIdList();
        if (CollectionUtils.isNotEmpty(includeOrderIdList)) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new TermQuery("id", includeOrderIdList.toArray()));
            orQuery.addQuery(new TermQuery("fbuyerid", orderSearchParamDTO.getBuyerIdList()));
            request.addQuery(orQuery);
            // 而后不额外设置采购人id的filter
            orderSearchParamDTO.setBuyerIdList(null);
        }

        //采购人id集合查询
        List<Integer> buyerIdList = orderSearchParamDTO.getBuyerIdList();
        if (CollectionUtils.isNotEmpty(buyerIdList)) {
            request.addFilter(new TermFilter("fbuyerid", buyerIdList));
        }

        //供应商名称
        String suppName = orderSearchParamDTO.getSuppName();
        if (StringUtils.isNotBlank(suppName)) {
            IllegalInputControlUtil.checkSearchInput(suppName);
            request.addQuery(new PhraseQuery("fsuppname", suppName));
        }

        //供应商id集合
        List<Integer> suppIdList = orderSearchParamDTO.getSuppIdList();
        if (CollectionUtils.isNotEmpty(suppIdList)) {
            request.addFilter(new TermFilter("fsuppid", suppIdList));
        }

        //机构id排除过滤
        List<Integer> excludeOrgIdList = orderSearchParamDTO.getExcludeOrgIdList();
        if (CollectionUtils.isNotEmpty(excludeOrgIdList)) {
            request.addNotFilter(new TermFilter("fuserid", excludeOrgIdList));
        }

        //订单号集合 排除过滤
        List<String> excludeOrderNoList = orderSearchParamDTO.getExcludeOrderNoList();
        if (CollectionUtils.isNotEmpty(excludeOrderNoList)) {
            request.addNotFilter(new TermFilter("forderno.keyword", excludeOrderNoList));
        }

        //采购单号集合
        List<String> applicationNoSet = orderSearchParamDTO.getApplicationNoList();
        if (CollectionUtils.isNotEmpty(applicationNoSet)) {
            request.addFilter(new TermFilter("fbuyapplicationno.keyword", applicationNoSet));
        }

        //经费状态集合
        List<Integer> fundStatusList = orderSearchParamDTO.getFundStatusList();
        if (CollectionUtils.isNotEmpty(fundStatusList)) {
            request.addFilter(new TermFilter("fund_status", fundStatusList));
        }

        //出入库状态集合
        List<Integer> inventoryStatusList = orderSearchParamDTO.getInventoryStatusList();
        if (CollectionUtils.isNotEmpty(inventoryStatusList)) {
            request.addFilter(new TermFilter("inventory_status", inventoryStatusList));
        }

        // 订单来源（类型）
        Integer orderType = orderSearchParamDTO.getOrderType();
        if (orderType != null) {
            request.addFilter(new TermFilter("order_type", orderType));
        }

        // 订单来源（类型）列表
        List<Integer> orderTypeList = orderSearchParamDTO.getOrderTypeList();
        if (CollectionUtils.isNotEmpty(orderTypeList)) {
            request.addFilter(new TermFilter("order_type", orderTypeList));
        }

        // 代配送标识筛选
        Boolean deliveryProxyOn = orderSearchParamDTO.getDeliveryProxyOn();
        if (Boolean.TRUE.equals(deliveryProxyOn)) {
            request.addFilter(new TermFilter("deliveryType",DeliveryTypeEnum.PROXY_DELIVERY_LIST));
        }

        // 配送类型筛选
        List<Integer> deliveryTypeList = orderSearchParamDTO.getDeliveryTypeList();
        if (CollectionUtils.isNotEmpty(deliveryTypeList)) {
            request.addFilter(new TermFilter("deliveryType", deliveryTypeList));
        }

        // 审批流id
        List<Integer> flowIdList = orderSearchParamDTO.getFlowIdList();
        if (CollectionUtils.isNotEmpty(flowIdList)) {
            request.addFilter(new TermFilter("flow_id", flowIdList));
        }

        // 管制品备案查询
        Integer confirmed = orderSearchParamDTO.getConfirmed();
        if (confirmed != null) {
            if (confirmed.equals(OrderConfirmStatusEnum.HAS_CONFIRM_RECORD.value)) {
                request.addQuery(new TermQuery("is_confirm",true));
            } else if (confirmed.equals(OrderConfirmStatusEnum.NO_CONFIRM_RECORD.value)) {
                request.addQuery(new TermQuery("is_confirm",false));
            } else {
                request.addQuery(new TermQuery("is_confirm",Arrays.asList(true,false)));
            }
        }

        if(orderSearchParamDTO.getRegularCustomerPurchase() != null){
            // 是否常客购买
            request.addFilter(new TermFilter("regular_customer_purchase", orderSearchParamDTO.getRegularCustomerPurchase()));
        }

        if(orderSearchParamDTO.getCustomerSubscribeSupp() != null){
            // 是否已关注店铺的用户下单
            request.addFilter(new TermFilter("customer_subscribe_supp", orderSearchParamDTO.getCustomerSubscribeSupp()));
        }

        //代配送状态
        Integer deliveryStatus = orderSearchParamDTO.getDeliveryStatus();
        if(deliveryStatus != null){
            request.addQuery(new TermQuery("delivery_status", deliveryStatus));
        }
        //配送员
        String deliveryUser = orderSearchParamDTO.getDeliveryUser();
        if(StringUtils.isNotBlank(deliveryUser)){
            request.addQuery(new MatchQuery("delivery_user", deliveryUser));
        }
        //分拣员
        String sortedUser = orderSearchParamDTO.getSortedUser();
        if(StringUtils.isNotBlank(sortedUser)){
            request.addQuery(new MatchQuery("sorted_user", sortedUser));
        }

        // 配送时间
        if(orderSearchParamDTO.getDeliveredTimeStart() != null && orderSearchParamDTO.getDeliveredTimeEnd() != null){
            request.addQuery(new RangeQuery("delivered_time",
                    DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,orderSearchParamDTO.getDeliveredTimeStart()),
                    DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderSearchParamDTO.getDeliveredTimeEnd())));
        }

        //代配送订单操作人
        String deliveryOperatorGuid = orderSearchParamDTO.getDeliveryOperatorGuid();
        if(StringUtils.isNotBlank(deliveryOperatorGuid)){
            request.addQuery(new TermQuery("delivery_operator_guids", deliveryOperatorGuid));
        }

        String receiveMan = orderSearchParamDTO.getReceiveMan();
        if (StringUtils.isNotEmpty(receiveMan)){
            request.addFilter(new TermFilter("flastreceiveman", receiveMan));
        }

        //移动端代配送查询
        String wxDeliveryProxyKeyword = orderSearchParamDTO.getWxDeliveryProxyKeyword();
        if(StringUtils.isNotBlank(wxDeliveryProxyKeyword)){
            List<Query> queryList = New.list();
            queryList.add(new NestedQuery(NESTED_TABLE, new PhraseQuery(NESTED_TABLE + ".fgoodname", wxDeliveryProxyKeyword)));
            queryList.add(new NestedQuery(NESTED_TABLE, new WildcardQuery(NESTED_TABLE + ".fgoodcode.keyword", String.format(wildCardQueryStr, wxDeliveryProxyKeyword))));
            queryList.add(new PhraseQuery("fsuppname", wxDeliveryProxyKeyword));
            queryList.add(new PhraseQuery("fbuyername", wxDeliveryProxyKeyword));
            queryList.add(new PhraseQuery("forderno", wxDeliveryProxyKeyword));
            request.addQuery(new OrQuery(queryList));
        }

        // 额外信息查询
        if (CollectionUtils.isNotEmpty(orderSearchParamDTO.getOrderExtraInfoList())) {
            for (OrderExtraInfoParamDTO extraInfo : orderSearchParamDTO.getOrderExtraInfoList()) {
                TermQuery keyExistQuery = new TermQuery(NESTED_EXTRA + ".extra_key", extraInfo.getOrderExtraEnum().getValue());
                if(extraInfo.getIncludeThisExtra() == null){
                    // 否则 根据key+value的组合对order_extra进行精确查找
                    TermQuery valueMatchQuery = new TermQuery(NESTED_EXTRA + ".extra_value", extraInfo.getOrderExtraValue());
                    request.addQuery(new NestedQuery(NESTED_EXTRA, new AndQuery(New.list(keyExistQuery,valueMatchQuery))));
                }else {
                    if (extraInfo.getIncludeThisExtra()) {
                        // 正选
                        request.addQuery(new NestedQuery(NESTED_EXTRA, keyExistQuery));
                    } else {
                        // 反选
                        request.addNotQuery(new NestedQuery(NESTED_EXTRA, keyExistQuery));
                    }
                }
            }
        }

        String goodsName = orderSearchParamDTO.getGoodsName();
        if (StringUtils.isNotBlank(goodsName)) {
            IllegalInputControlUtil.checkSearchInput(goodsName);
            PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fgoodname", goodsName);
            request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
        }

        /**
         * 品牌id
         */
        Integer brandId = orderSearchParamDTO.getBrandId();
        if (brandId != null) {
            TermQuery termQuery = new TermQuery(NESTED_TABLE + ".fbrandid", brandId);
            request.addQuery(new NestedQuery(NESTED_TABLE, termQuery));
        }

        String brandName = orderSearchParamDTO.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            IllegalInputControlUtil.checkSearchInput(brandName);
                PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fbrand", brandName);
            request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
        }

        String goodsCode = orderSearchParamDTO.getGoodsCode();
        if (StringUtils.isNotBlank(goodsCode)) {
            IllegalInputControlUtil.checkSearchInput(goodsCode);
            PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fgoodcode", goodsCode);
            request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
        }

        if (StringUtils.isNotBlank(orderSearchParamDTO.getCasNo())) {
            IllegalInputControlUtil.checkSearchInput(orderSearchParamDTO.getCasNo());
            TermQuery termQuery = new TermQuery(NESTED_TABLE + ".cas_no", orderSearchParamDTO.getCasNo());
            request.addQuery(new NestedQuery(NESTED_TABLE, termQuery));
        }

        if (orderSearchParamDTO.getCategoryTag() != null) {
            String categoryTag = InboundTypeEnum.valueOf(orderSearchParamDTO.getCategoryTag()).getDesc();
            TermQuery termQuery = new TermQuery(NESTED_TABLE + ".category_tag", categoryTag);
            request.addQuery(new NestedQuery(NESTED_TABLE, termQuery));
        }

        String productSn = orderSearchParamDTO.getProductSn();
        if (productSn != null) {
            IllegalInputControlUtil.checkSearchInput(productSn);
            PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "product_id", productSn);
            request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
        }

        List<Integer> detailReturnStatusList = orderSearchParamDTO.getDetailReturnStatusList();
        if (CollectionUtils.isNotEmpty(detailReturnStatusList)) {
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + "." + "return_status", detailReturnStatusList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }

        List<Integer> orderDetailIdList = orderSearchParamDTO.getOrderDetailIdList();
        if (CollectionUtils.isNotEmpty(orderDetailIdList)) {
            Preconditions.isTrue(orderDetailIdList.size() < 101, "单次批量查询商品必须少于100个");
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + "." + "detail_id", orderDetailIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }

        // 一级分类id列表
        List<Integer> firstCategoryIdList = orderSearchParamDTO.getFirstCategoryIdList();
        if (CollectionUtils.isNotEmpty(firstCategoryIdList)) {
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + ".first_category_id", firstCategoryIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }
        // 二级分类id列表
        List<Integer> secondCategoryIdList = orderSearchParamDTO.getSecondCategoryIdList();
        if (CollectionUtils.isNotEmpty(secondCategoryIdList)) {
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + ".second_category_id", secondCategoryIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }
        // 三级分类id列表
        List<Integer> thirdCategoryIdList = orderSearchParamDTO.getThirdCategoryIdList();
        if (CollectionUtils.isNotEmpty(thirdCategoryIdList)) {
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + ".categoryID", thirdCategoryIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }

        List<String> cardIdList = orderSearchParamDTO.getCardIdList();
        if (CollectionUtils.isNotEmpty(cardIdList)) {
            TermFilter nestFilter = new TermFilter(NESTED_CARD + "." + "card_id", cardIdList);
            request.addFilter(new NestedFilter(NESTED_CARD, nestFilter));
        }

        List<String> cardNoList = orderSearchParamDTO.getCardNoList();
        if (CollectionUtils.isNotEmpty(cardNoList)) {
            TermFilter nestFilter = new TermFilter(NESTED_CARD + "." + "card_no", cardNoList);
            request.addFilter(new NestedFilter(NESTED_CARD, nestFilter));
        }

        //范围查询
        List<FieldRangeDTO> fieldRangeList = orderSearchParamDTO.getFieldRangeList();
        if (CollectionUtils.isNotEmpty(fieldRangeList)) {
            fieldRangeList.forEach(range ->
                    request.addQuery(new RangeQuery(range.getField(), range.getLower(), range.getUpper()
                            ,range.getIncludeLower(),range.getIncludeUpper())));
        }
        //排序
        //按照订单 状态 score 排序
        if (orderSearchParamDTO.getScoreByStatus()) {
            ScoreRank scoreRank = createScoreRankByStatus();
            request.setScoreRank(scoreRank);
            request.addOrderSortItem(new FieldSortItem("_score", SortOrder.DESC));
        }

        //订单时间升序
        if(DeliveryStatusEnum.DELIVERED.getValue().equals(deliveryStatus)){
            request.addOrderSortItem(new FieldSortItem("delivered_time", SortOrder.DESC));
        }else {
            SortOrderEnum orderDateSort = orderSearchParamDTO.getOrderDateSort();
            if (orderDateSort == SortOrderEnum.ASC) {
                request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.ASC));
            } else if (orderDateSort == SortOrderEnum.DESC) {
                request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
            }
        }
        List<FieldSortDTO> fieldSortList = orderSearchParamDTO.getFieldSortList();
        if (CollectionUtils.isNotEmpty(fieldSortList)) {
            fieldSortList.forEach(fieldSort ->
                    request.addOrderSortItem(new FieldSortItem(fieldSort.getSortField()
                            , SortOrder.getSortOrder(fieldSort.getSortOrder().getCode()))));
        }

        //全文检索
        String searchKey = orderSearchParamDTO.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            IllegalInputControlUtil.checkSearchInput(searchKey);
            final OrQuery orQueryMaster = new OrQuery();
            Set<String> fullTextMasterFields = orderSearchParamDTO.getFullTextMasterFields();
            if (CollectionUtils.isNotEmpty(fullTextMasterFields)){
                List<String> fieldList = New.list(fullTextMasterFields);
                orQueryMaster.addQuery(new MultiPhraseQuery(fieldList , searchKey));
            }

            Set<String> fullTextDetailFields = orderSearchParamDTO.getFullTextDetailFields();
            if (CollectionUtils.isNotEmpty(fullTextDetailFields)){
                List<String> detailFieldList = New.list(fullTextDetailFields);
                MultiPhraseQuery multiPhraseQuery = new MultiPhraseQuery(detailFieldList,searchKey);
                NestedQuery nestedQuery = new NestedQuery(NESTED_TABLE, multiPhraseQuery);
                orQueryMaster.addQuery(nestedQuery);
            }
            request.addQuery(orQueryMaster);
        }

        //全文检索订单详情
        String detailSearchKey = orderSearchParamDTO.getDetailSearchKey();
        if (StringUtils.isNotBlank(detailSearchKey)) {
            IllegalInputControlUtil.checkSearchInput(detailSearchKey);
            final OrQuery orQueryDetail = new OrQuery();
            Set<String> fullTextDetailFields = orderSearchParamDTO.getFullTextDetailFields();
            if (CollectionUtils.isNotEmpty(fullTextDetailFields)) {
                for (String detailField : fullTextDetailFields) {
                    if (detailField != null && detailField.contains("cas_no")) {
                        TermQuery termQuery = new TermQuery(detailField, detailSearchKey);
                        orQueryDetail.addQuery(termQuery);
                    } else {
                        PhraseQuery phraseQuery = new PhraseQuery(detailField, detailSearchKey);
                        orQueryDetail.addQuery(phraseQuery);
                    }
                }
                NestedQuery nestedQuery = new NestedQuery(NESTED_TABLE, orQueryDetail);
                request.addQuery(nestedQuery);
            }
        }

        // 订单验收审批等级
        if(orderSearchParamDTO.getAcceptApproveLevel() != null){
            request.addFilter(new NestedFilter(NESTED_EXTRA, new AndFilter(New.list(new TermFilter(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ACCEPT_APPROVE_LEVEL.getValue()), new TermFilter(NESTED_EXTRA + ".extra_value", orderSearchParamDTO.getAcceptApproveLevel())))));
        }

        Set<String> sourceFieldSet = orderSearchParamDTO.getSourceFieldSet();
        if (CollectionUtils.isNotEmpty(sourceFieldSet)) {
            request.addOutputFieldSet(sourceFieldSet);
        }

        // 网关查询接口，商品名称、商品货号或商品cas号查询
        if(StringUtils.isNotBlank(orderSearchParamDTO.getProductSearchContent())){
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new NestedQuery(NESTED_TABLE, new PhraseQuery(NESTED_TABLE + "." + "fgoodname", orderSearchParamDTO.getProductSearchContent())));
            orQuery.addQuery(new NestedQuery(NESTED_TABLE, new PhraseQuery(NESTED_TABLE + "." + "fgoodcode", orderSearchParamDTO.getProductSearchContent())));
            orQuery.addQuery(new NestedQuery(NESTED_TABLE, new TermQuery(NESTED_TABLE + ".cas_no", orderSearchParamDTO.getProductSearchContent())));
            request.addQuery(orQuery);
        }
        // 商品平台唯一编码
        String productCode = orderSearchParamDTO.getProductCode();
        if (StringUtils.isNotBlank(productCode)) {
            IllegalInputControlUtil.checkSearchInput(productCode);
            TermFilter nestFilter = new TermFilter(NESTED_TABLE + ".product_code.keyword", productCode);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestFilter));
        }

        // 关联关系
        Integer relateInfo = orderSearchParamDTO.getRelateInfo();
        if (Objects.nonNull(relateInfo)) {
            boolean haveRelate = CommonValueUtils.parseNumberStrToBoolean(relateInfo.toString());
            if (haveRelate) {
                NotNullFilter notNullFilter = new NotNullFilter("relateInfo");
                TermFilter termFilter = new TermFilter("relateInfo", "");
                request.addFilter(new AndNotFilter(notNullFilter, termFilter));
            } else {
                OrFilter orFilter = new OrFilter();
                orFilter.addFilter(new IsNullFilter("relateInfo"));
                orFilter.addFilter(new TermFilter("relateInfo", ""));
                request.addFilter(orFilter);
            }
        }
    }

    /**
     * 根据结算单id 查询订单
     * @param statementIdList
     * @return 订单列表
     */
    @Override
    public List<OrderMasterSearchDTO> searchOrderByStatementIds(List<Integer> statementIdList,SortOrderEnum orderDateSort){
        final Request request = new Request();
        if (CollectionUtils.isEmpty(statementIdList)) {
            return New.emptyList();
        }
        BusinessErrUtil.isTrue(statementIdList.size() <= countLimit, ExecptionMessageEnum.QUERY_LIMIT_EXCEEDED, countLimit);
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(10000);
        request.addFilter(new TermFilter("statement_id", statementIdList));
        //订单时间升序
        if (orderDateSort==SortOrderEnum.ASC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.ASC));
        }else if (orderDateSort==SortOrderEnum.DESC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        }
        return baseSearch(request);
    }

    /**
     * 根据orderDetailId集合 查询订单 集合
     * @param detailIdList 订单详情id集合
     * @return List<OrderMasterSearchDTO>
     */
    @Override
    public List<OrderMasterSearchDTO> searchOrderByDetailIds(List<Integer> detailIdList){
        final Request request = new Request();
        if (CollectionUtils.isEmpty(detailIdList)) {
            return New.emptyList();
        }
        BusinessErrUtil.isTrue(detailIdList.size() <= countLimit, ExecptionMessageEnum.QUERY_LIMIT_EXCEEDED, countLimit);
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(detailIdList.size());
        String nested = OrderNestedEnum.NESTED_TABLE_DETAIL.getName();
        NestedFilter nestedFilter = new NestedFilter(nested, new TermFilter(nested + ".detail_id", detailIdList));
        request.addFilter(nestedFilter);
        request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        return baseSearch(request);
    }

    /**
     * 根据 更新时间，创建时间在 时间范围内的 推送的订单信息
     * @param orderPullParamDTO
     * @return
     */
    @Override
    public BasePageResponseDTO<OrderMasterSearchDTO> getBaseOrderByRangeDate(OrderPullParamDTO orderPullParamDTO){
        final Request request = new Request();
        Integer pageSize = orderPullParamDTO.getPageSize();
        Assert.isTrue(pageSize <= countLimit,"pageSize不能大于"+countLimit+"数据");
        String startTime = orderPullParamDTO.getStartTime();
        Assert.notNull(startTime,"起始时间不能为空");
        String endTime = orderPullParamDTO.getEndTime();
        Assert.notNull(endTime,"结束不能为空");
        request.setKey(ORDER_SEARCH_INDEX);
        OrQuery orQuery = new OrQuery();
        orQuery.addQuery(new RangeQuery("forderdate", startTime, endTime));
        orQuery.addQuery(new RangeQuery("update_time", startTime, endTime));
        request.addQuery(orQuery);
        request.setPageSize(pageSize);
        request.setStart(PageResponseUtils.getRequestStart(orderPullParamDTO));
        request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        request.addOrderSortItem(new FieldSortItem("update_time", SortOrder.DESC));
        BasePageResponseDTO<OrderMasterSearchDTO> result = basePageSearch(orderPullParamDTO, request);
        return result;
    }

    private BasePageResponseDTO<OrderMasterSearchDTO> basePageSearch(BasePageParamDTO basePageParamDTO, Request request) {
        Response response = orderSearchRPCServiceClient.search(request);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterDtos = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        BasePageResponseDTO<OrderMasterSearchDTO> result = new BasePageResponseDTO<>();
        result.setPageSize(basePageParamDTO.getPageSize());
        result.setPageNo(basePageParamDTO.getPageNo());
        result.setTotal(response.getTotalHits());
        result.setData(orderMasterDtos);
        return result;
    }

    /**
     * 根据订单id 搜索订单
     * @param orderId  订单id
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    public List<OrderMasterSearchDTO> searchOrderById(Integer orderId){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.addFilter(new TermFilter("id", orderId));
        return baseSearch(request);
    }

    /**
     * 根据订单idList 搜索订单
     * @param orderIdList  订单id
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    public List<OrderMasterSearchDTO> searchOrderByIdList(List<Integer> orderIdList, SortOrderEnum orderDateSort){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(500);
        request.addFilter(new TermFilter("id", orderIdList));
        //订单时间升序
        if (orderDateSort==SortOrderEnum.ASC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.ASC));
        }else if (orderDateSort==SortOrderEnum.DESC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        }
        return baseSearch(request);
    }

    /**
     * 根据订单idList 搜索订单.并计算返回 现在实际的金额
     * @param orderIdList  订单id
     * @return  List<OrderMasterSearchDTO>
     */
    @Override
    public List<OrderMasterSearchDTO> getActualOrderByIdList(List<Integer> orderIdList, SortOrderEnum orderDateSort){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(500);
        request.addFilter(new TermFilter("id", orderIdList));
        //订单时间升序
        if (orderDateSort==SortOrderEnum.ASC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.ASC));
        }else if (orderDateSort==SortOrderEnum.DESC){
            request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        }
        List<OrderMasterSearchDTO> orderMasterSearchDTOS = baseSearch(request);
        for (OrderMasterSearchDTO orderMasterSearchDTO : orderMasterSearchDTOS) {
            this.wipeOrderCancel(orderMasterSearchDTO);
        }
        return orderMasterSearchDTOS;
    }

    /**
     * 订单去除退货金额
     */
    private void wipeOrderCancel(OrderMasterSearchDTO orderMaster){
        List<OrderDetailSearchDTO> orderDetails = orderMaster.getOrderDetail();
        Double totalReturnAmount =0.0;
        for (OrderDetailSearchDTO orderDetail : orderDetails) {
            Integer quantity = orderDetail.getFquantity();
            Double cancelQuantity = orderDetail.getFcancelquantity() == null ? 0.0 : orderDetail.getFcancelquantity();
            Double returnAmount = BigDecimalUtil.multiply(orderDetail.getFbidprice(),cancelQuantity);
            orderDetail.setReturnAmount(returnAmount);
            orderDetail.setActualQuantity(BigDecimalUtil.subtract(quantity ,cancelQuantity));
            orderDetail.setActualAmount(BigDecimalUtil.subtract(orderDetail.getFbidamount() , returnAmount));
            totalReturnAmount = BigDecimalUtil.add(totalReturnAmount , returnAmount);
        }
        orderMaster.setActualAmount(BigDecimalUtil.subtract(orderMaster.getForderamounttotal() , totalReturnAmount));
    }

    /**
     * 订单通用底层
     * @param request
     * @return
     */
    private List<OrderMasterSearchDTO> baseSearch(Request request) {
        Response response = orderSearchRPCServiceClient.search(request);
        List<Record> recordList = response.getRecordList();
        List<OrderMasterSearchDTO> orderMasterDtos = OrderPojoTranslator.recordsToOrderMasterDTOS(recordList);
        return orderMasterDtos;
    }

    /**
     * 统计某段时间活跃供应商数量（排除演示供应商）
     * @param omsStatisticsParamDTO 时间范围，状态list， getNoSuppIdList
     * @return 供应商数量
     */
    @Override
    public Double countSuppliers(OmsStatisticsParamDTO omsStatisticsParamDTO){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        String startTime = omsStatisticsParamDTO.getStartTime();
        String endTime = omsStatisticsParamDTO.getEndTime();
        List<String> noSuppIdList = omsStatisticsParamDTO.getNoSuppIdList();
        List<Integer> statusList = omsStatisticsParamDTO.getStatusList();
        if (CollectionUtils.isNotEmpty(noSuppIdList)){
            request.addNotFilter(new TermFilter("fsuppid",noSuppIdList.toArray()));
        }
        if (CollectionUtils.isNotEmpty(statusList)){
            request.addFilter(new TermFilter("status",statusList.toArray()));
        }
        if (StringUtils.isNotBlank(startTime) || StringUtils.isNotBlank(endTime)){
            request.addQuery(new RangeQuery("forderdate",startTime,endTime));
        }
        String suppQuantity = "suppQuantity";
        CardinalityItem cardinalityItem = new CardinalityItem("fsuppid", suppQuantity);
        request.addAggsItem(cardinalityItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        double suppCount = aggsResultItem.getValue();
        return suppCount;
    }

    /**
     * 统计一段时间内总买家量（排除演示单位）
     * @param omsStatisticsParamDTO  fuserid列表（不包含）
     * @return 买家数量
     */
    @Override
    public Double countBuyers(OmsStatisticsParamDTO omsStatisticsParamDTO){

        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        String startTime = omsStatisticsParamDTO.getStartTime();
        String endTime = omsStatisticsParamDTO.getEndTime();
        List<String> noOrgIdList = omsStatisticsParamDTO.getNoOrgIdList();
        if (CollectionUtils.isNotEmpty(noOrgIdList)){
            request.addNotFilter(new TermFilter("fuserid",noOrgIdList.toArray()));
        }
        if (StringUtils.isNotBlank(startTime) || StringUtils.isNotBlank(endTime)){
            request.addQuery(new RangeQuery("forderdate",startTime,endTime));
        }
        String buyerQuantity = "buyerQuantity";
        CardinalityItem cardinalityItem = new CardinalityItem("fbuyerid", buyerQuantity);
        request.addAggsItem(cardinalityItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        double buyerCount = aggsResultItem.getValue();
        return buyerCount;
    }

    /**
     * 统计 一段时间 的 产品销量
     * @param  productSalesParamDTO 入参
     * @return saleQuantity 销量
     */
    @Override
    public double countProductSales(ProductSalesParamDTO productSalesParamDTO){

        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        String startDate = productSalesParamDTO.getStartDate();
        String endDate = productSalesParamDTO.getEndDate();
        Long productId = productSalesParamDTO.getProductId();

        request.addFilter(new NestedFilter(OrderNestedEnum.NESTED_TABLE_DETAIL.getName(),new TermFilter(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+".product_id",productId)));
        if (StringUtils.isNotBlank(startDate) || StringUtils.isNotBlank(endDate)){
            request.addQuery(new RangeQuery("forderdate",startDate,endDate));
        }
        List<Integer> departmentId = productSalesParamDTO.getDepartmentId();
        if (CollectionUtils.isNotEmpty(departmentId)){
            request.addFilter(new TermFilter("fbuydepartmentid", departmentId.toArray()));
        }
        String countQuantity = "countQuantity";
        String saleQuantity = "saleQuantity";
        NestedSumItem sumItem = new NestedSumItem(OrderNestedEnum.NESTED_TABLE_DETAIL.getName(), countQuantity
                ,new SumItem(OrderNestedEnum.NESTED_TABLE_DETAIL.getName()+".fquantity", saleQuantity));
        request.addAggsItem(sumItem);
        Response response = orderSearchRPCServiceClient.search(request);
        String termJson = response.getAggsResult().getAggsResultItems().get(0).getJson();
        Map<String, Map> termMap = JsonUtils.parseMap(termJson, Map.class);
        Map countQuantityMap = termMap.get("countQuantity");
        Map saleQuantityMap = (Map) countQuantityMap.get(saleQuantity);
        Object value = saleQuantityMap.get("value");
        String valueStr = value == null ? null : value.toString();
        double quantity = NumberUtils.toDouble(valueStr);
        return quantity;
    }

    /**
     * new NestedAggregationBuilder(Ne);
     * 统计 订单金额，课题组数量，供应商数量
     * @param paramDTO 入参
     * @return 订单金额，课题组数量，供应商数量
     */
    @Override
    public StatisticsManagerResultDTO countStatisticsOrder(StatisticsManagerParamDTO paramDTO){

        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        fillRequestStatistics(paramDTO, request);

        String supplierQuantity = "supplierQuantity";
        CardinalityItem cardinalityItem2 = new CardinalityItem("fsuppid", supplierQuantity);

        String totalAmount = "totalAmount";
        SumItem sumItem = new SumItem("forderamounttotal", totalAmount);

        String departmentQuantity = "departmentQuantity";
        CardinalityItem cardinalityItem = new CardinalityItem("fbuydepartmentid", departmentQuantity);

        request.addAggsItem(cardinalityItem);
        request.addAggsItem(cardinalityItem2);
        request.addAggsItem(sumItem);
        Response response = orderSearchRPCServiceClient.search(request);
        List<AggsResultItem> aggResultItems = response.getAggsResult().getAggsResultItems();

        StatisticsManagerResultDTO result  = new StatisticsManagerResultDTO();
        for (AggsResultItem aggResultItem : aggResultItems) {
            String json = aggResultItem.getJson();
            if (json.contains(supplierQuantity)){
                result.setSupplierQuantity(aggResultItem.getValue());
            }else if(json.contains(totalAmount)){
                result.setOriginalAmount(aggResultItem.getSum());
            }else if (json.contains(departmentQuantity)){
                result.setDepartmentQuantity(aggResultItem.getValue());
            }
        }
        result.setOrderQuantity(response.getTotalHits());
        return result;
    }

    /**
     * 统计供应商数量
     * @param paramDTO 入参
     * @return 供应商数量
     */
    @Override
    public StatisticsManagerResultDTO countSupplierQuantity(StatisticsManagerParamDTO paramDTO){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        fillRequestStatistics(paramDTO, request);

        CardinalityItem cardinalityItem = new CardinalityItem("fsuppid","supplierQuantity");
        request.addAggsItem(cardinalityItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        StatisticsManagerResultDTO result  = new StatisticsManagerResultDTO();
        result.setSupplierQuantity(aggsResultItem.getValue());
        return result;
    }

    /**
     * 统计课题组数量
     * @param paramDTO 入参
     * @return 课题组数量
     */
    @Override
    public StatisticsManagerResultDTO countDepartmentQuantity(StatisticsManagerParamDTO paramDTO){

        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        fillRequestStatistics(paramDTO, request);

        CardinalityItem cardinalityItem = new CardinalityItem("fbuydepartmentid","departmentQuantity");
        request.addAggsItem(cardinalityItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        StatisticsManagerResultDTO result  = new StatisticsManagerResultDTO();
        result.setDepartmentQuantity(aggsResultItem.getValue());
        return result;
    }

    /**
     * 订单管理 统计订单金额
     * @param paramDTO 入参
     * @return 订单金额  result.setOriginalAmount
     */
    @Override
    public Double countOrderTotalAmount(StatisticsManagerParamDTO paramDTO) {
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        fillRequestStatistics(paramDTO, request);
        SumItem sumItem = new SumItem("forderamounttotal","totalAmount");
        request.addAggsItem(sumItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        return new BigDecimal(aggsResultItem.getSum()).setScale(2, RoundingMode.HALF_UP).doubleValue();

    }

    /**
     *  用统计对象 田中搜索request
     * @param paramDTO 入参
     * @param request  搜索request
     */
    private void fillRequestStatistics(StatisticsManagerParamDTO paramDTO, Request request) {
        //采购部门姓名 模糊查询
        String departmentName = paramDTO.getDepartmentName();
        request.setPageSize(0);
        if (StringUtils.isNotBlank(departmentName)) {
            request.addQuery(new PhraseQuery("fbuydepartment", departmentName));
        }

        //订单状态过滤
        List<Integer> statusList = paramDTO.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            request.addFilter(new TermFilter("status", statusList.toArray()));
        }

        //流程种类
        Integer species = paramDTO.getSpecies();
        if (species != null) {
            request.addFilter(new TermFilter("species", species));
        }

        // 订单来源筛选
        List<Integer> orderTypeList = paramDTO.getOrderTypeList();
        if (CollectionUtils.isNotEmpty(orderTypeList)) {
            request.addFilter(new TermFilter("order_type", orderTypeList.toArray()));
        }

        List<Integer> departmentParentId = paramDTO.getDepartmentParentId();
        if ( CollectionUtils.isNotEmpty(departmentParentId)) {
            request.addFilter(new TermFilter("department_parent_id", departmentParentId.toArray()));
        }

        //采购部门id过滤
        List<Integer> departmentIds = paramDTO.getDepartmentIds();
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            request.addFilter(new TermFilter("fbuydepartmentid", departmentIds.toArray()));
        }

        // 兼容单个单位与单位列表
        Integer orgId = paramDTO.getOrgId();
        List<Integer> orgIdList = paramDTO.getOrgIdList() == null ? New.list() : paramDTO.getOrgIdList();
        if (orgId != null) {
            orgIdList.add(orgId);
        }
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            request.addFilter(new TermFilter("fuserid", orgIdList));
        }

        String brandName = paramDTO.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fbrand", brandName);
            request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
        }

        List<Long> productIdList = paramDTO.getProductIdList();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "product_id", productIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        Integer buyerId = paramDTO.getBuyerId();
        if ( buyerId != null) {
            request.addFilter(new TermFilter("fbuyerid", buyerId));
        }

        Integer firstCategoryId = paramDTO.getFirstCategoryId();
        if(firstCategoryId != null){
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "first_category_id", firstCategoryId);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        List<Integer> notStatusList = paramDTO.getNotStatusList();
        //订单状态过滤
        if (CollectionUtils.isNotEmpty(notStatusList)) {
            request.addNotFilter(new TermFilter("status", notStatusList));
        }
        //供应商id
        List<Integer> suppIdList = paramDTO.getSuppIdList();
        if (CollectionUtils.isNotEmpty(suppIdList)) {
            request.addFilter(new TermFilter("fsuppid", suppIdList));
        }
        //  日期范围搜索
        String startTime = paramDTO.getStartTime();
        String endTime = paramDTO.getEndTime();
        final String orderDateField = "forderdate";
        if (!(StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime))){
            request.addQuery(new RangeQuery(orderDateField, startTime, endTime));
        }

        // 去除的单位id列表
        List<Integer> excludeOrgIdList = paramDTO.getExcludeOrgIdList();
        if (CollectionUtils.isNotEmpty(excludeOrgIdList)) {
            request.addNotFilter(new TermFilter("fuserid", excludeOrgIdList));
        }

        // 去除的供应商id列表，明细和主表都要控制
        List<Integer> excludeSuppIdList = paramDTO.getExcludeSuppIdList();
        if (CollectionUtils.isNotEmpty(excludeSuppIdList)) {
            request.addNotFilter(new TermFilter("fsuppid", excludeSuppIdList));
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "supp_id", excludeSuppIdList);
            request.addNotFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        // 需要排除的用户id，一般用于排除测试用户的数据
        List<Integer> excludeUserIdList = paramDTO.getExcludeUserIdList();
        if(CollectionUtils.isNotEmpty(excludeUserIdList)){
            request.addNotFilter(new TermFilter("fbuyerid", excludeUserIdList));
        }

        //全文检索
        String searchKey = paramDTO.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            final OrQuery orQueryMaster = new OrQuery();
            Set<String> fullTextMasterFields = paramDTO.getFullTextMasterFields();
            if (CollectionUtils.isNotEmpty(fullTextMasterFields)){
                List<String> fieldList = New.list(fullTextMasterFields);
                orQueryMaster.addQuery(new MultiPhraseQuery(fieldList , searchKey));
            }

            Set<String> fullTextDetailFields = paramDTO.getFullTextDetailFields();
            if (CollectionUtils.isNotEmpty(fullTextDetailFields)){
                List<String> detailFieldList = New.list(fullTextDetailFields);
                MultiPhraseQuery multiPhraseQuery = new MultiPhraseQuery(detailFieldList,searchKey);
                NestedQuery nestedQuery = new NestedQuery(NESTED_TABLE, multiPhraseQuery);
                orQueryMaster.addQuery(nestedQuery);
            }
            AndQuery andQuery = new AndQuery();
            andQuery.addQuery(orQueryMaster);
            request.addQuery(andQuery);
        }
    }

    /**
     * 统计 各订单状态的个数
     *
     * @return
     */
    @Override
    public Map<Integer, Integer> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO) {
        Request request = this.constructCountStatusParam(orderStatisticsParamDTO);
        return this.countOrderBySearchRequest(request);
    }

    @Override
    public Map<Integer, Integer> countOrderByDeliveryStatus(OrderStatisticsParamDTO orderStatisticsParamDTO) {
        Request request = this.constructCountStatusParam(orderStatisticsParamDTO);
        request.addFilter(new TermFilter("deliveryType", DeliveryTypeEnum.PROXY.getCode()));
        request.addNotFilter(new TermFilter("status", New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue())));
        return this.aggFieldToCountOrderBySearchRequest(request, "delivery_status");
    }

    private Request constructCountStatusParam(OrderStatisticsParamDTO orderStatisticsParamDTO){
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        List<Integer> buyerIds = orderStatisticsParamDTO.getBuyerIds();
        List<Integer> orgIds = orderStatisticsParamDTO.getOrgIds();
        List<Integer> suppIds = orderStatisticsParamDTO.getSuppIds();
        List<Integer> deptIds = orderStatisticsParamDTO.getDeptIds();
        List<Integer> speciesList = orderStatisticsParamDTO.getSpeciesList();
        if (CollectionUtils.isNotEmpty(buyerIds)) {
            request.addFilter(new TermFilter("fbuyerid", buyerIds.toArray()));
        }
        if (CollectionUtils.isNotEmpty(orgIds)) {
            request.addFilter(new TermFilter("fuserid", orgIds.toArray()));
        }
        if (CollectionUtils.isNotEmpty(suppIds)) {
            request.addFilter(new TermFilter("fsuppid", suppIds));
        }
        if (CollectionUtils.isNotEmpty(deptIds)) {
            request.addFilter(new TermFilter("fbuydepartmentid", deptIds));
        }
        if (CollectionUtils.isNotEmpty(speciesList)) {
            request.addFilter(new TermFilter("species", speciesList));
        }
        // 审批流id
        List<Integer> flowIdList = orderStatisticsParamDTO.getFlowIdList();
        if (CollectionUtils.isNotEmpty(flowIdList)) {
            request.addFilter(new TermFilter("flow_id", flowIdList));
        }
        return request;
    }

    /**
     * @description: 外部的搜索入参与实际搜索入参的转换
     * @date: 2021/3/31 18:15
     * @author: zengyanru
     * @param orderSearchParamDTO
     * @return com.ruijing.search.client.request.Request
     */
    @Override
    public Request searchRequestTransform(OrderSearchParamDTO orderSearchParamDTO) {
        final Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        Integer startHit = orderSearchParamDTO.getStartHit();
        request.setStart((startHit == null || startHit < 0) ? 0 : startHit);
        Integer pageSize = orderSearchParamDTO.getPageSize();
        request.setPageSize((pageSize == null || pageSize < 0) ? 20 : pageSize);
        this.paramFillRequest(orderSearchParamDTO, request);
        return request;
    }

    /**
     * @description: 通过搜索请求获取状态与订单数量对应map
     * @date: 2021/3/31 17:46
     * @author: zengyanru
     * @param request 搜索请求
     * @return java.util.Map<java.lang.Integer,java.lang.Integer>
     */
    @Override
    public Map<Integer, Integer> countOrderBySearchRequest(Request request) {
        return this.aggFieldToCountOrderBySearchRequest(request, "status");
    }

    /**
     * 按字段聚合来查订单，获取数量
     * @param request 请求
     * @param aggField 聚合字段
     * @return 聚合结果
     */
    @Override
    public Map<Integer, Integer> aggFieldToCountOrderBySearchRequest(Request request, String aggField) {
        int statusSizeTop = 50;
        TermsItem aggsItem = new TermsItem(aggField, "statusCount");
        aggsItem.setTop(statusSizeTop);
        request.addAggsItem(aggsItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        String termJson = aggsResultItem.getJson();
        Map<String, Object> termMap = JsonUtils.parseMap(termJson, Object.class);
        Map statusCountMap = (Map) termMap.get("statusCount");
        List<Map<String, Integer>> buckets = (List<Map<String, Integer>>) statusCountMap.get("buckets");
        Map<Integer, Integer> resultMap = New.linkedHashMapWithCapacity(buckets.size());
        buckets.forEach(map -> {
            Integer key = NumberUtils.toInt(String.valueOf(map.get("key")));
            Integer value = map.get("doc_count");
            resultMap.put(key, value);
        });
        return resultMap;
    }



    @Override
    public List<OrderOrgStatResultDTO> countOrgTopProduct(OrderOrgStatParamDTO dto) {
        if (dto==null || dto.getOrgId() == null || dto.getTop() == null || dto.getTop() < 0) {
            return Collections.emptyList();
        }
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        request.addFilter(new TermFilter("fuserid", dto.getOrgId()));
        if (CollectionUtils.isNotEmpty(dto.getStatuses())) {
            request.addFilter(new TermFilter("status", dto.getStatuses().toArray()));
        }
        if(CollectionUtils.isNotEmpty(dto.getSuppIds())){
            request.addFilter(new TermFilter("fsuppid", dto.getSuppIds().toArray()));
        }
        if(StringUtils.isNotBlank(dto.getStartTime()) || StringUtils.isNotBlank(dto.getEndTime())){
            request.addFilter(new RangeFilter("forderdate", dto.getStartTime(), dto.getEndTime()));
        }
        String fquantitySum="fquantitySum";
        String groupByProduct="groupByProduct";
        String orderDetail="order_detail";
        TermsItem aggsItem = new TermsItem("order_detail.product_id", groupByProduct);
        aggsItem.addAggsItem(new SumItem("order_detail.fquantity", fquantitySum));
        aggsItem.setTop(dto.getTop());
        aggsItem.addSortItem(new FieldSortItem(fquantitySum, SortOrder.DESC));
        NestedTermsItem nestedTermsItem=new NestedTermsItem("order_detail", orderDetail, aggsItem);
        request.addAggsItem(nestedTermsItem);
        Response response=null;
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "countOrgTopProduct");
        List<OrderOrgStatResultDTO> list=new ArrayList<>();
        try {
            response = orderSearchRPCServiceClient.search(request);
            transaction.setSuccessStatus();
            JSONObject json = JSON.parseObject(response.getAggsResult().getAggsResultItems().get(0).getJson());
            JSONArray array=json.getJSONObject(orderDetail).getJSONObject(groupByProduct).getJSONArray("buckets");
            for(int i=0; i<array.size(); i++){
                JSONObject o=array.getJSONObject(i);
                OrderOrgStatResultDTO resultDTO=new OrderOrgStatResultDTO();
                resultDTO.setId(o.getLong("key"));
                resultDTO.setQuantity(o.getJSONObject(fquantitySum).getBigDecimal("value"));
                list.add(resultDTO);
            }
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData(dto.toString());
        } finally {
            transaction.complete();
        }
        return list;
    }


    @Override
    public BigDecimal sumSuppOrgSale(OrderOrgSuppStatParamDTO dto) {
        if (dto==null || dto.getOrgId() == null) {
            return BigDecimal.ZERO;
        }
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        request.addFilter(new TermFilter("fuserid", dto.getOrgId()));
        if (CollectionUtils.isNotEmpty(dto.getSuppIds())) {
            request.addFilter(new TermFilter("fsuppid", (List) dto.getSuppIds()));
        }

        if (dto.getSpecies() != null) {
            request.addFilter(new TermFilter("species", dto.getSpecies()));

        }

        if (CollectionUtils.isNotEmpty(dto.getExcludeStatus())) {
            // 过滤掉特定状态的单，不占用供应商线下单累计金额。modify by zhongyulei 2020/06/30
            request.addNotFilter(new TermFilter("status", dto.getExcludeStatus()));
        }

        String reSumAmount = "sumAmount";
        request.addAggsItem(new SumItem("forderamounttotal", reSumAmount));
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "sumSuppOrgSale");
        Response response = orderSearchRPCServiceClient.search(request);
        transaction.setSuccessStatus();
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        JSONObject json = JSON.parseObject(aggsResultItem.getJson());
        return json.getJSONObject(reSumAmount).getBigDecimal("value");
    }

    /**
     * 订单属性 聚合 订单金额和数量
     * @param paramDTO
     * @return
     */
    @Override
    public List<OrderAggregationResultDTO> aggOrderAmountAndCount(StatisticsManagerParamDTO paramDTO){

        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        Assert.isTrue(aggField != null,"aggField不能为空！");
        Integer table = aggField.getTable();
        Assert.isTrue(orderMasterTable.equals(table),aggField.getField() + "不是orderMaster的字段！");
        String aggItemName = "aggItem";
        Integer topSize = paramDTO.getTopSize();
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        this.fillRequestStatistics(paramDTO,request);
        TermsItem itemAgg = new TermsItem(aggField.getField(), aggItemName);
        itemAgg.setTop(topSize);
        FieldSortItem fieldSortItem = new FieldSortItem(AMOUNT_ITEM,SortOrder.DESC);
        OrderAggregationSortFieldEnum sortItem = paramDTO.getSortItem();
        SortOrder sortOrder = null;
        if (sortItem != null){
            sortOrder = SortOrder.getSortOrder(sortItem.getSort());
            fieldSortItem = new FieldSortItem(sortItem.getItemCode(), sortOrder);
        }

        itemAgg.addSortItem(fieldSortItem);
        request.addAggsItem(itemAgg);
        itemAgg.addAggsItem(new SumItem("forderamounttotal", AMOUNT_ITEM));
        CountItem countItem = new CountItem("id", QUANTITY_ITEM);
        itemAgg.addAggsItem(countItem);
        itemAgg.addAggsItem(new BucketSortItem("aggResPage", AMOUNT_ITEM)
                .setFrom(paramDTO.getAggResFrom())
                .setSize(paramDTO.getAggResSize())
                .setOrder(sortOrder == null ? null : sortOrder.getName()));
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);
        JSONObject termsJson = JSON.parseObject(aggsResultItem.getJson());
        JSONObject orgTermsJson = termsJson.getJSONObject(aggItemName);
        JSONArray bucketsArray = orgTermsJson.getJSONArray("buckets");

        //封装返回对象
        List<OrderAggregationResultDTO> resultList = New.listWithCapacity(bucketsArray.size());
        for (int i = 0; i < bucketsArray.size(); i++) {
            JSONObject jsonObject = bucketsArray.getJSONObject(i);
            OrderAggregationResultDTO aggregationResultDTO = new OrderAggregationResultDTO();
            if(aggField.getFieldNumberType()){
                Long aggFieldId =  jsonObject.getLong("key");
                aggregationResultDTO.setAggFieldId(aggFieldId);
            }
            aggregationResultDTO.setAggFieldStr(jsonObject.getString("key"));

            JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
            Double orderAmount = sumJson.getDouble("value");
            aggregationResultDTO.setAmount(orderAmount);

            JSONObject orderQuantityJson = jsonObject.getJSONObject(QUANTITY_ITEM);
            Double orderQuantity = orderQuantityJson.getDouble("value");
            aggregationResultDTO.setQuantity(orderQuantity);
            resultList.add(aggregationResultDTO);
        }
        return resultList;
    }

    /**
     * 订单商品属性 聚合  金额 数量
     * @param paramDTO
     * @return
     */
    @Override
    public List<OrderAggregationResultDTO> aggProductAmountAndCount(StatisticsManagerParamDTO paramDTO){
        // 聚合层级为：nested->filter->term->sum/count
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        Assert.isTrue(aggField != null,"aggField不能为空！");
        Integer table = aggField.getTable();
        Assert.isTrue(orderDetailTable.equals(table),aggField.getField() + "不是orderDetail的字段！");

        OrderAggregationSortFieldEnum sortItem = paramDTO.getSortItem();

        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        this.fillRequestStatistics(paramDTO,request);
        String productTerms = "productTerms";
        String nestedTerms = "nestedTerms";
        String filterItems = "filterAggItem";

        String amountField = paramDTO.getOrderAmountField() == null
                ? OrderMetricFieldEnum.DETAIL_ORIGINAL_AMOUNT.getField() : paramDTO.getOrderAmountField().getField();
        String quantityField = paramDTO.getOrderQuantityField() == null
                ? OrderMetricFieldEnum.DETAIL_QUANTITY.getField() : paramDTO.getOrderQuantityField().getField();

        TermsItem productAgg = new TermsItem(NESTED_TABLE + "."  + aggField.getField(), productTerms);

        productAgg.setTop(paramDTO.getTopSize());
        FieldSortItem fieldSortItem = new FieldSortItem(AMOUNT_ITEM,SortOrder.DESC);
        if (sortItem != null){
            fieldSortItem = new FieldSortItem(sortItem.getItemCode(),SortOrder.getSortOrder(sortItem.getSort()));
        }
        productAgg.addSortItem(fieldSortItem);

        SumItem sumProductAmountItem = new SumItem(NESTED_TABLE + "." +  amountField, AMOUNT_ITEM);
        productAgg.addAggsItem(sumProductAmountItem);
        SumItem sumProductQuantityItem = new SumItem(NESTED_TABLE + "." +  quantityField, QUANTITY_ITEM);
        productAgg.addAggsItem(sumProductQuantityItem);

        // 父子联表所需特殊的filter aggregation
        AndNotFilter andNotFilter = this.filterAggDetailConstruction(paramDTO);
        FilterItem filterAggItem = new FilterItem(filterItems, andNotFilter);
        filterAggItem.addAggItem(productAgg);

        NestedAggsItem nestedAggsItem = new NestedAggsItem(NESTED_TABLE, nestedTerms, filterAggItem);

        request.addAggsItem(nestedAggsItem);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem aggsResultItem = response.getAggsResult().getAggsResultItems().get(0);

        JSONObject termsJson = JSON.parseObject(aggsResultItem.getJson());
        JSONObject nestedTermsJson = termsJson.getJSONObject(nestedTerms);
        JSONObject filterTermsJson = nestedTermsJson.getJSONObject(filterItems);
        JSONObject productTermsJson = filterTermsJson.getJSONObject(productTerms);
        JSONArray bucketsArray = productTermsJson.getJSONArray("buckets");

        //封装返回对象
        List<OrderAggregationResultDTO> resultList = New.listWithCapacity(bucketsArray.size());
        for (int i = 0; i < bucketsArray.size(); i++) {
            JSONObject jsonObject = bucketsArray.getJSONObject(i);
            Long aggFieldId =  jsonObject.getLong("key");
            JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
            Double orderAmount = sumJson.getDouble("value");
            JSONObject orderQuantityJson = jsonObject.getJSONObject(QUANTITY_ITEM);
            Double orderQuantity = orderQuantityJson.getDouble("value");
            OrderAggregationResultDTO aggregationResultDTO = new OrderAggregationResultDTO();
            aggregationResultDTO.setAggFieldId(aggFieldId);
            aggregationResultDTO.setAmount(orderAmount);
            aggregationResultDTO.setQuantity(orderQuantity);
            resultList.add(aggregationResultDTO);
        }
        return resultList;
    }

    /**
     * 订单时间柱状图聚合订单金额
     * @param paramDTO 入参
     * @return
     */
    @Override
    public List<OrderDateHistogramResultDTO> aggOrderAmountDateHistogram(StatisticsManagerParamDTO paramDTO){
        String aggItemName = "aggItem";
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        this.fillRequestStatistics(paramDTO,request);
        IntervalDTO intervalDate = paramDTO.getIntervalDate();
        Assert.isTrue(intervalDate != null ,"intervalDate不能为空！");
        SortOrderEnum sortOrderEnum = paramDTO.getSortOrderEnum();
        String dateFormat = StringUtils.isNotBlank(intervalDate.getDateFormat()) ? intervalDate.getDateFormat() : "yyyy-MM-dd";
        Integer value = intervalDate.getValue() != null ? intervalDate.getValue() : 1;
        DateUnitEnum dateUnitEnum = intervalDate.getDateUnit() != null ? intervalDate.getDateUnit() : DateUnitEnum.MONTH;
        DateHistogramItem dateHistogramItem = new DateHistogramItem("forderdate",aggItemName,dateFormat, new DateHistogramItem.Interval(value,dateUnitEnum.getSymbol()));

        // 让桶数据完整返回所有时间分段信息，即便没有对应的单据数据
        dateHistogramItem.setMinDocCount(0);
        DateHistogramItem.ExtendedBounds extendedBounds = new DateHistogramItem.ExtendedBounds(
                DateUtils.format(dateFormat, DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, paramDTO.getStartTime()))
                , DateUtils.format(dateFormat, DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, paramDTO.getEndTime())));
        dateHistogramItem.setExtendedBounds(extendedBounds);

        FieldSortItem fieldSortItem = new FieldSortItem("_key",SortOrder.DESC);
        if (sortOrderEnum != null && SortOrderEnum.ASC == sortOrderEnum) {
            fieldSortItem = new FieldSortItem("_key",SortOrder.ASC);
        }
        dateHistogramItem.addSortItem(fieldSortItem);
        request.addAggsItem(dateHistogramItem);
        dateHistogramItem.addAggsItem(new SumItem("forderamounttotal", AMOUNT_ITEM));
        CountItem countItem = new CountItem("id", QUANTITY_ITEM);
        dateHistogramItem.addAggsItem(countItem);
        Response response = orderSearchRPCServiceClient.search(request);
        List<AggsResultItem> aggsResultItems = response.getAggsResult().getAggsResultItems();
        if (CollectionUtils.isEmpty(aggsResultItems)) {
            return New.list();
        }
        AggsResultItem aggsResultItem = aggsResultItems.get(0);
        JSONObject termsJson = JSON.parseObject(aggsResultItem.getJson());
        JSONObject orgTermsJson = termsJson.getJSONObject(aggItemName);
        JSONArray bucketsArray = orgTermsJson.getJSONArray("buckets");

        //封装返回对象
        List<OrderDateHistogramResultDTO> resultList = New.listWithCapacity(bucketsArray.size());
        for (int i = 0; i < bucketsArray.size(); i++) {
            JSONObject jsonObject = bucketsArray.getJSONObject(i);
            Long timestamp =  jsonObject.getLong("key");
            String dateString = jsonObject.getString("key_as_string");
            JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
            Double orderAmount = sumJson.getDouble("value");
            JSONObject countJson = jsonObject.getJSONObject(QUANTITY_ITEM);
            Double orderCount = countJson.getDouble("value");
            OrderDateHistogramResultDTO aggregationResultDTO = new OrderDateHistogramResultDTO();
            aggregationResultDTO.setDateString(dateString);
            aggregationResultDTO.setTimestamp(timestamp);
            aggregationResultDTO.setAmount(orderAmount);
            aggregationResultDTO.setCount(orderCount);
            resultList.add(aggregationResultDTO);
        }
        return resultList;
    }

    /**
     * 统计交易商品的金额 和数量
     * @param paramDTO
     */
    @Override
    public OrderAggregationResultDTO sumProductAmountAndQuantity(StatisticsManagerParamDTO paramDTO){

        String orderDetail="order_detail";
        String amountField = "original_amount";
        String quantityField = "fquantity";
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        this.fillRequestStatistics(paramDTO,request);
        SumItem sumProductAmountItem = new SumItem(orderDetail + "." +  amountField, AMOUNT_ITEM);
        SumItem sumProductQuantityItem = new SumItem(orderDetail + "." +  quantityField, QUANTITY_ITEM);
        String productAmount = "productAmount";
        NestedSumItem nestedTermsAmount =new NestedSumItem(orderDetail, productAmount, sumProductAmountItem);
        String productQuantity = "productQuantity";
        NestedSumItem nestedTermsQuantity =new NestedSumItem(orderDetail, productQuantity, sumProductQuantityItem);
        request.addAggsItem(nestedTermsAmount);
        request.addAggsItem(nestedTermsQuantity);
        Response response = orderSearchRPCServiceClient.search(request);
        AggsResultItem quantityResultItem = response.getAggsResult().getAggsResultItems().get(0);
        JSONObject quantityTermsJson = JSON.parseObject(quantityResultItem.getJson());
        JSONObject quantityJson = quantityTermsJson.getJSONObject(productQuantity);
        Double quantity = quantityJson.getJSONObject(QUANTITY_ITEM).getDouble("value");

        AggsResultItem amountResultItem = response.getAggsResult().getAggsResultItems().get(1);
        JSONObject amountTermsJson = JSON.parseObject(amountResultItem.getJson());
        JSONObject amountJson = amountTermsJson.getJSONObject(productAmount);
        Double amount = amountJson.getJSONObject(AMOUNT_ITEM).getDouble("value");;
        OrderAggregationResultDTO result = new OrderAggregationResultDTO();
        result.setAmount(amount);
        result.setQuantity(quantity);
        return result;
    }

    /**
     * @param request
     * @return java.lang.Integer
     * @description: 按条件获取符合条件的条目数
     * @date: 2021/4/6 14:44
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "按条件获取符合条件的条目数", serviceType = ServiceType.COMMON_SERVICE)
    public long searchCountByRequest(Request request) {
        // 设置默认参数，防止npe
        request.setKey(ORDER_SEARCH_INDEX);
        Integer startHit = request.getStart();
        request.setStart((startHit == null || startHit < 0) ? 0 : startHit);
        Integer pageSize = request.getPageSize();
        request.setPageSize((pageSize == null || pageSize < 0) ? 20 : pageSize);
        request.setQueryType(QueryType.COUNT);

        Response response = orderSearchRPCServiceClient.search(request);
        return response.getTotalHits();
    }


    /**
     * 8状态的第一位，
     * 4状态的第二位，
     * 9状态的第三位，
     * 13状态的第四位，
     * 其他状态的第五位，
     *
     * @return
     */
    private ScoreRank createScoreRankByStatus() {

        final String statusFiled = "status";

        //按照 订单状态 设置 rank评分
        ScoreRank scoreRank = new ScoreRank();
        scoreRank.setBoostMode("replace");
        //设置订单状态为8 的 权重为5
        TermFilter status8Filter = new TermFilter(statusFiled, OrderStatusEnum.WaitingForConfirm.getValue());
        ItemFunction itemFunction8 = new ItemFunction(status8Filter, 5f);
        scoreRank.addFunction(itemFunction8);

        //设置订单状态为4 的 权重为4
        TermFilter status4Filter = new TermFilter(statusFiled, OrderStatusEnum.WaitingForDelivery.getValue());
        ItemFunction itemFunction4 = new ItemFunction(status4Filter, 4f);
        scoreRank.addFunction(itemFunction4);

        //设置订单状态为9 的 权重为3
        TermFilter status9Filter = new TermFilter(statusFiled, OrderStatusEnum.PurchaseApplyToCancel.getValue());
        ItemFunction itemFunction9 = new ItemFunction(status9Filter, 3f);
        scoreRank.addFunction(itemFunction9);
        return scoreRank;
    }

    /**
     * filter aggregation 的订单详情参数的过滤，不支持全局参数，不支持（也不必要）主表字段
     * @param paramRequest
     * @return and not filter
     */
    private AndNotFilter filterAggDetailConstruction(StatisticsManagerParamDTO paramRequest) {
        List<Filter> andFilterList = New.list();

        String productCode = paramRequest.getProductCode();
        if (StringUtils.isNotBlank(productCode)) {
            PhraseFilter phraseFilter = new PhraseFilter(NESTED_TABLE + ".fgoodcode", productCode);
            andFilterList.add(phraseFilter);
        }

        String brandName = paramRequest.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            PhraseFilter phraseFilter = new PhraseFilter(NESTED_TABLE + ".fbrand.kw", brandName);
            andFilterList.add(phraseFilter);
        }

        List<Long> productIdList = paramRequest.getProductIdList();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            TermFilter termFilter = new TermFilter(NESTED_TABLE + "." + "product_id", productIdList);
            andFilterList.add(termFilter);
        }

        Integer firstCategoryId = paramRequest.getFirstCategoryId();
        if(firstCategoryId != null){
            andFilterList.add(new TermFilter(NESTED_TABLE+".first_category_id", firstCategoryId));
        }
        return new AndNotFilter(andFilterList, New.list());
    }

    @Override
    public Map<OrderStatusEnum, Long> countOrderStatusStatistics(OrderStatisticsParamDTO request) {
        final List<Integer> buyerIds = request.getBuyerIds();
        boolean inHms = Boolean.TRUE.equals(request.getInHms());
        boolean myOrderCheck = Boolean.TRUE.equals(request.getMyOrderCheck());
        Preconditions.notEmpty(buyerIds,"buyerIds must not be empty");
        Preconditions.notEmpty(request.getOrgIds(),"orgId不能传空值");

        final UserBaseInfoDTO userInfo = userClient.getUserDetailByID(buyerIds.get(0));
        
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(userInfo.getGuid(), request.getOrgIds().get(0), true, ConfigConstant.ORDER_VIEW);
        final OrderListRequest params = new OrderListRequest();
        params.setPageSize(0);
        params.setPageNo(0);
        final PageableResponse<OrderListRespVO> myPendingOrderList = orderApprovalService.getMyPendingOrderList(params, loginUserInfo, inHms);

        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setGuid(loginUserInfo.getUserGuid());
        rjSessionInfo.setOrgId(loginUserInfo.getOrgId());
        OrderListRequest orderListRequest = new OrderListRequest();
        orderListRequest.setMyOrderCheck(myOrderCheck);
        orderListRequest.setStatusList(New.list(OrderStatusEnum.WaitingForReceive.getValue(), OrderStatusEnum.WaitingForStatement_1.getValue()));
        OrderListCountVO orderListCountVO = buyerOrderService.countOrderList(rjSessionInfo, orderListRequest, inHms);

        return MapBuilder.<OrderStatusEnum, Long>custom()
                .put(OrderStatusEnum.OrderReceiveApproval, myPendingOrderList.getTotal())
                .put(OrderStatusEnum.WaitingForReceive, Long.valueOf(orderListCountVO.getWaitingForReceive()))
                .put(OrderStatusEnum.WaitingForStatement_1, Long.valueOf(orderListCountVO.getWaitingForStatement()))
                .build();
    }

    /**
     * 据条件返回 成功退货金额总和 的聚合结果
     * @param paramDTO
     * @return
     */
    @Override
    public List<GoodsReturnAggResDTO> aggReturnAmountByEntities(StatisticsManagerParamDTO paramDTO) {
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        this.fillRequestStatistics(paramDTO,request);

        // 组装入参
        String suppAggTerm = "suppAggTerm";
        String returnAmountSumAgg = "returnAmountSumAgg";
        // term
        TermsItem suppAggItem = new TermsItem("fsuppid", suppAggTerm);
        suppAggItem.setTop(10000);
        // sum
        SumItem returnAmountSumItem = new SumItem("return_amount", returnAmountSumAgg);
        suppAggItem.addAggsItem(returnAmountSumItem);

        request.addAggsItem(suppAggItem);
        Response searchRes = orderSearchRPCServiceClient.search(request);
        String errorMsg = searchRes == null ? "" : searchRes.getErrorMessage();
        BusinessErrUtil.isTrue(searchRes != null && searchRes.isSuccess(), ExecptionMessageEnum.AGGREGATE_SUPPLIER_RETURN_FAILED, errorMsg);

        // 组装结果
        AggsResultItem aggsResultItem = searchRes.getAggsResult().getAggsResultItems().get(0);
        JSONArray buckets = JSON.parseObject(aggsResultItem.getJson()).getJSONObject("suppAggTerm").getJSONArray("buckets");
        List<GoodsReturnAggResDTO> aggResList = New.list();
        for (int i = 0; i < buckets.size(); i++) {
            JSONObject bucket = buckets.getJSONObject(i);
            Long suppId = bucket.getLong("key");
            BigDecimal sumReturnAmount = bucket.getJSONObject(returnAmountSumAgg).getBigDecimal("value");
            GoodsReturnAggResDTO aggRes = new GoodsReturnAggResDTO();
            aggRes.setAggFieldId(suppId);
            aggRes.setReturnAmount(sumReturnAmount);
            aggResList.add(aggRes);
        }
        return aggResList;
    }

    /**
     * 按搜索条件，返回购买的商品数量信息
     * @param paramDTO
     * @return
     */
    public List<ValidPurchaseCountDTO> aggPurchaseQuantity(StatisticsManagerParamDTO paramDTO) {
        Preconditions.isTrue(paramDTO.getBuyerId() != null && CollectionUtils.isNotEmpty(paramDTO.getProductIdList()),
                "采购用户id与商品id列表的入参不可为空");

        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);
        // 业务特殊点，如果没有去除拆单，请配置去除拆单的状态
        if (CollectionUtils.isEmpty(paramDTO.getNotStatusList())) {
            paramDTO.setNotStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        } else if (!paramDTO.getNotStatusList().contains(OrderStatusEnum.ORDER_SPLIT_UP.getValue())) {
            paramDTO.getNotStatusList().add(OrderStatusEnum.ORDER_SPLIT_UP.getValue());
        }

        this.fillRequestStatistics(paramDTO,request);

        // 聚合构造
        final String orderNestedTerm = "orderNestedTerm";
        final String quantitySum = "quantitySum";
        final String productIdTerm = "productIdTerm";
        NestedAggsItem nestedItem = new NestedAggsItem(NESTED_TABLE, orderNestedTerm);
        TermsItem productIdItem = new TermsItem(NESTED_TABLE + ".product_id", productIdTerm);
        productIdItem.setTop(10000);
        SumItem sumItem = new SumItem(NESTED_TABLE + ".fquantity", quantitySum);

        productIdItem.addAggsItem(sumItem);
        nestedItem.addAggsItem(productIdItem);
        request.addAggsItem(nestedItem);

        // 关闭的订单请求（关闭，且是取消的）
        Request requestForCancelQuantity = SerializationUtils.clone(request);
        requestForCancelQuantity.addQuery(new TermQuery("status", OrderStatusEnum.Close.getValue()));
        requestForCancelQuantity.addFilter(new NotNullFilter("fcanceldate"));

        // 总数目
        CompletableFuture<JSONArray> allQuantityArraysFuture = AsyncExecutor.callAsync(() -> {
            Response allQuantityRes = orderSearchRPCServiceClient.search(request);
            JSONArray allQuantityBuckets = JSONObject.parseObject(allQuantityRes.getAggsResult().getAggsResultItems()
                    .get(0).getJson()).getJSONObject(orderNestedTerm).getJSONObject(productIdTerm).getJSONArray("buckets");
            return allQuantityBuckets;
        });

        // 取消的数目
        CompletableFuture<Map<Long, Integer>> productIdCancelQuantityMapFuture = AsyncExecutor.callAsync(() -> {
            Response cancelQuantityRes = orderSearchRPCServiceClient.search(requestForCancelQuantity);
            JSONArray cancelQuantityBuckets = JSONObject.parseObject(cancelQuantityRes.getAggsResult().getAggsResultItems()
                    .get(0).getJson()).getJSONObject(orderNestedTerm).getJSONObject(productIdTerm).getJSONArray("buckets");
            Map<Long, Integer> productIdCancelQuantityMap = new HashMap<>();
            for (int i = 0; i < cancelQuantityBuckets.size(); i++) {
                JSONObject bucket = cancelQuantityBuckets.getJSONObject(i);
                Long productId = bucket.getLong("key");
                Integer cancelQuantity = bucket.getJSONObject(quantitySum).getInteger("value");
                productIdCancelQuantityMap.put(productId, cancelQuantity);
            }
            return productIdCancelQuantityMap;
        });

        JSONArray allQuantityArrays = new JSONArray();
        Map<Long, Integer> productIdCancelQuantityMap = new HashMap<>();
        try {
            CompletableFuture.allOf(allQuantityArraysFuture, productIdCancelQuantityMapFuture).join();
            allQuantityArrays = allQuantityArraysFuture.get();
            productIdCancelQuantityMap = productIdCancelQuantityMapFuture.get();
        } catch (Exception e) {
            Preconditions.isTrue(false, "查询采购人购买数量出错，请检查代码。" + e.getMessage());
        }
        // 结果组合
        List<ValidPurchaseCountDTO> validPurchaseCountList = New.listWithCapacity(allQuantityArrays.size());
        for (int i = 0; i < allQuantityArrays.size(); i++) {
            Long productId = allQuantityArrays.getJSONObject(i).getLong("key");
            Integer quantity = allQuantityArrays.getJSONObject(i).getJSONObject(quantitySum).getInteger("value");
            int cancelQuantity = productIdCancelQuantityMap.get(productId) == null ? 0 : productIdCancelQuantityMap.get(productId);
            ValidPurchaseCountDTO validPurchaseCount = new ValidPurchaseCountDTO();
            validPurchaseCount.setBuyerId(paramDTO.getBuyerId());
            validPurchaseCount.setProductId(productId);
            validPurchaseCount.setAllQuantity(quantity);
            validPurchaseCount.setCancelQuantity(cancelQuantity);
            validPurchaseCount.setValidQuantity(quantity - cancelQuantity);
            validPurchaseCountList.add(validPurchaseCount);
        }
        return validPurchaseCountList;
    }

    /**
     * 广告投放订单聚合，按照条件聚合下单买家数，下单数，下单金额
     * @param request
     * @return
     */
    @Override
    public List<AdvertisementOrderAggDTO> aggAdvertisementOrderGroupByNestedField(AdvertisementOrderAggRequest request) {
        // 入参校验
        Preconditions.notNull(request.getStartTime(), "开始时间不可为空");
        Preconditions.notNull(request.getEndTime(), "结束时间不可为空");
        Preconditions.notEmpty(request.getAdvertisementIdList(), "广告投放id不可为空");
        // 组装查询条件
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setPageSize(0);
        if(CollectionUtils.isNotEmpty(request.getSuppIdList())) {
            searchRequest.addQuery(new TermQuery("fsuppid", request.getSuppIdList()));
        }
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList())));
        }
        // 构建过滤参数
        searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue())));
        searchRequest.addQuery(new RangeQuery("forderdate", DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getStartTime()), DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getEndTime())));
        searchRequest.addNotFilter(new TermFilter("status", OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        // 构建聚合参数
        ReverseNestedAggsItem reverseNestedAggsItem = new ReverseNestedAggsItem("orderExtraReverseNested");
        reverseNestedAggsItem.addAggsItem(new CountItem("id", "orderCount"));
        reverseNestedAggsItem.addAggsItem(new CardinalityItem("fbuyerid", "buyerCount"));
        reverseNestedAggsItem.addAggsItem(new SumItem("forderamounttotal", "amountCount"));
        TermsItem termsItem = new TermsItem("order_extra.extra_value","advertTerm");
        termsItem.addAggsItem(reverseNestedAggsItem);
        AndFilter andFilter = new AndFilter();
        andFilter.addFilter(new TermFilter(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue()));
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            andFilter.addFilter(new TermFilter(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList()));
        }
        FilterItem filterItem = new FilterItem("advertFilter", andFilter);
        filterItem.addAggItem(termsItem);
        NestedAggsItem nestedAggsItem = new NestedAggsItem("order_extra", "orderExtraNested");
        nestedAggsItem.addAggsItem(filterItem);
        searchRequest.addAggsItem(nestedAggsItem);
        Response response = orderSearchRPCServiceClient.search(searchRequest);
        BusinessErrUtil.isTrue(response.isSuccess(), ExecptionMessageEnum.AGGREGATE_AD_ORDER_INFO_FAILED);
        List<AggsResultItem>  aggsResultItems = response.getAggsResult().getAggsResultItems();
        Map<String, Object> resultMap = JsonUtils.parseMap(aggsResultItems.get(0).getJson(), Object.class);
        List<Object> buckets = (List<Object>) ((Map<String, Object>)((Map<String, Object>)((Map<String, Object>) resultMap.get("orderExtraNested")).get("advertFilter")).get("advertTerm")).get("buckets");
        List<AdvertisementOrderAggDTO> advertisementOrderAggDTOList = New.list();
        if(CollectionUtils.isNotEmpty(buckets)){
            buckets.forEach(bucket -> {
                Integer advertId = Integer.parseInt(((Map<String, Object>) bucket).get("key").toString());
                Map<String, Object> bucketMaps = (Map<String, Object>) ((Map<String, Object>) bucket).get("orderExtraReverseNested");
                AdvertisementOrderAggDTO advertisementOrderAggDTO = new AdvertisementOrderAggDTO();
                advertisementOrderAggDTO.setAdvertId(advertId);
                advertisementOrderAggDTO.setAmountCount(BigDecimal.valueOf((Double)((Map<String, Object>) bucketMaps.get("amountCount")).get("value")).setScale(2, RoundingMode.HALF_UP));
                advertisementOrderAggDTO.setOrderCount(((Integer)((Map<String, Object>) bucketMaps.get("orderCount")).get("value")));
                advertisementOrderAggDTO.setBuyerCount((Integer)((Map<String, Object>) bucketMaps.get("buyerCount")).get("value"));
                advertisementOrderAggDTOList.add(advertisementOrderAggDTO);
            });
        }
        return advertisementOrderAggDTOList;
    }

    @Override
    public List<AdvertisementOrderAggDTO> aggAdvertisementOrderGroupByDocField(AdvertisementOrderAggRequest request) {
        // 入参校验
        Preconditions.notNull(request.getStartTime(), "开始时间不可为空");
        Preconditions.notNull(request.getEndTime(), "结束时间不可为空");
        if(AdvertisementOrderAggTermTypeEnum.SUPP_ID.equals(request.getTermType())){
            Preconditions.notEmpty(request.getSuppIdList(), "供应商id不可为空");
        }
        // 组装查询条件
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setPageSize(0);
        if(CollectionUtils.isNotEmpty(request.getSuppIdList())) {
            searchRequest.addQuery(new TermQuery("fsuppid", request.getSuppIdList()));
        }
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList())));
        }
        // 构建过滤参数
        searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue())));
        searchRequest.addQuery(new RangeQuery("forderdate", DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getStartTime()), DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getEndTime())));
        searchRequest.addNotFilter(new TermFilter("status", OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        // 构建聚合参数
        ReverseNestedAggsItem reverseNestedAggsItem = new ReverseNestedAggsItem("orderExtraReverseNested");
        if(AdvertisementOrderAggTermTypeEnum.SUPP_ID.equals(request.getTermType())) {
            TermsItem termsItem = new TermsItem("fsuppid", "targetGroup");
            termsItem.addAggsItem(new CountItem("id", "orderCount"));
            termsItem.addAggsItem(new CardinalityItem("fbuyerid", "buyerCount"));
            termsItem.addAggsItem(new SumItem("forderamounttotal", "amountCount"));
            reverseNestedAggsItem.addAggsItem(termsItem);
        }else {
            DateHistogramItem dateHistogramItem = new DateHistogramItem("forderdate", "targetGroup", "yyyy-MM-dd", new DateHistogramItem.Interval(1, "day"));
            dateHistogramItem.addAggsItem(new CountItem("id", "orderCount"));
            dateHistogramItem.addAggsItem(new CardinalityItem("fbuyerid", "buyerCount"));
            dateHistogramItem.addAggsItem(new SumItem("forderamounttotal", "amountCount"));
            reverseNestedAggsItem.addAggsItem(dateHistogramItem);
        }
        AndFilter andFilter = new AndFilter();
        andFilter.addFilter(new TermFilter(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue()));
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            andFilter.addFilter(new TermFilter(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList()));
        }
        FilterItem filterItem = new FilterItem("advertFilter", andFilter);
        filterItem.addAggItem(reverseNestedAggsItem);
        NestedAggsItem nestedAggsItem = new NestedAggsItem("order_extra", "orderExtraNested");
        nestedAggsItem.addAggsItem(filterItem);
        searchRequest.addAggsItem(nestedAggsItem);
        Response response = orderSearchRPCServiceClient.search(searchRequest);
        BusinessErrUtil.isTrue(response.isSuccess(), ExecptionMessageEnum.AGGREGATE_AD_ORDER_INFO_FAILED);
        List<AggsResultItem>  aggsResultItems = response.getAggsResult().getAggsResultItems();
        Map<String, Object> resultMap = JsonUtils.parseMap(aggsResultItems.get(0).getJson(), Object.class);
        List<Object> buckets = (List<Object>) ((Map<String, Object>)((Map<String, Object>)((Map<String, Object>)((Map<String, Object>) resultMap.get("orderExtraNested")).get("advertFilter")).get("orderExtraReverseNested")).get("targetGroup")).get("buckets");
        List<AdvertisementOrderAggDTO> advertisementOrderAggDTOList = New.list();
        if(CollectionUtils.isNotEmpty(buckets)){
            buckets.forEach(bucket -> {
                AdvertisementOrderAggDTO advertisementOrderAggDTO = new AdvertisementOrderAggDTO();
                Map<String, Object> bucketMaps = (Map<String, Object>) bucket;
                if(request.getTermType().equals(AdvertisementOrderAggTermTypeEnum.SUPP_ID)) {
                    advertisementOrderAggDTO.setSuppId(Integer.parseInt(((Map<String, Object>) bucket).get("key").toString()));
                }else {
                    advertisementOrderAggDTO.setOrderDateStr(((Map<String, Object>) bucket).get("key_as_string").toString());
                }
                advertisementOrderAggDTO.setAmountCount(BigDecimal.valueOf((Double)((Map<String, Object>) bucketMaps.get("amountCount")).get("value")).setScale(2, RoundingMode.HALF_UP));
                advertisementOrderAggDTO.setOrderCount(((Integer)((Map<String, Object>) bucketMaps.get("orderCount")).get("value")));
                advertisementOrderAggDTO.setBuyerCount((Integer)((Map<String, Object>) bucketMaps.get("buyerCount")).get("value"));
                advertisementOrderAggDTOList.add(advertisementOrderAggDTO);
            });
        }
        return advertisementOrderAggDTOList;
    }

    @Override
    public List<AdvertisementOrderAggDTO> aggAdvertisementOrder(AdvertisementOrderAggRequest request) {
        // 入参校验
        Preconditions.notNull(request.getStartTime(), "开始时间不可为空");
        Preconditions.notNull(request.getEndTime(), "结束时间不可为空");
        // 组装查询条件
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setPageSize(0);
        if(CollectionUtils.isNotEmpty(request.getSuppIdList())) {
            searchRequest.addQuery(new TermQuery("fsuppid", request.getSuppIdList()));
        }
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList())));
        }
        searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue())));
        searchRequest.addQuery(new RangeQuery("forderdate", DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getStartTime()), DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getEndTime())));
        searchRequest.addNotFilter(new TermFilter("status", OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        // 组装聚合项
        searchRequest.addAggsItem(new CountItem("id", "orderCount"));
        searchRequest.addAggsItem(new CardinalityItem("fbuyerid", "buyerCount"));
        searchRequest.addAggsItem(new SumItem("forderamounttotal", "amountCount"));
        // 查询
        Response response = orderSearchRPCServiceClient.search(searchRequest);
        BusinessErrUtil.isTrue(response.isSuccess(), ExecptionMessageEnum.AGGREGATE_AD_ORDER_INFO_FAILED);
        List<AggsResultItem>  aggsResultItems = response.getAggsResult().getAggsResultItems();
        BusinessErrUtil.isTrue(aggsResultItems != null && aggsResultItems.size() >= 3, ExecptionMessageEnum.AGGREGATE_AD_ORDER_INFO_FAILED);
        // 组装查询结果
        AdvertisementOrderAggDTO advertisementOrderAggDTO = new AdvertisementOrderAggDTO();
        advertisementOrderAggDTO.setAmountCount(BigDecimal.valueOf(aggsResultItems.get(0).getSum()).setScale(2, RoundingMode.HALF_UP));
        advertisementOrderAggDTO.setBuyerCount((int) aggsResultItems.get(1).getValue());
        advertisementOrderAggDTO.setOrderCount((int) aggsResultItems.get(2).getValue());
        return New.list(advertisementOrderAggDTO);
    }
}
