package com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/3 16:15
 * @Description
 **/
@RpcModel("我的退货-我的退货概览信息")
public class GoodsReturnOrderBriefVO implements Serializable {

    private static final long serialVersionUID = -4083340941031955583L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单编号
     */
    @RpcModelProperty("订单编号")
    private String forderno;

    /**
     * 供应商名称
     */
    @RpcModelProperty("供应商名称")
    private String fsuppname;

    /**
     * 订单日期
     */
    @RpcModelProperty("订单日期")
    private String forderdate;

    /**
     * 采购部门
     */
    @RpcModelProperty("采购部门")
    private String fbuydepartment;

    /**
     * 退货信息
     */
    @RpcModelProperty("退货信息")
    private List<GoodsReturnOrderDetailVO> goodReturns;

    /**
     * 总价
     */
    @RpcModelProperty("总价")
    private Double totalPrice;

    /**
     * 采购人
     */
    @RpcModelProperty("采购人")
    private String buyerName;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getForderno() {
        return forderno;
    }

    public void setForderno(String forderno) {
        this.forderno = forderno;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    public String getForderdate() {
        return forderdate;
    }

    public void setForderdate(String forderdate) {
        this.forderdate = forderdate;
    }

    public String getFbuydepartment() {
        return fbuydepartment;
    }

    public void setFbuydepartment(String fbuydepartment) {
        this.fbuydepartment = fbuydepartment;
    }

    public List<GoodsReturnOrderDetailVO> getGoodReturns() {
        return goodReturns;
    }

    public void setGoodReturns(List<GoodsReturnOrderDetailVO> goodReturns) {
        this.goodReturns = goodReturns;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnOrderBriefVO{");
        sb.append("orderId=").append(orderId);
        sb.append(", forderno='").append(forderno).append('\'');
        sb.append(", fsuppname='").append(fsuppname).append('\'');
        sb.append(", forderdate='").append(forderdate).append('\'');
        sb.append(", fbuydepartment='").append(fbuydepartment).append('\'');
        sb.append(", goodReturns=").append(goodReturns);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", buyerName='").append(buyerName).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public GoodsReturnOrderBriefVO(List<GoodsReturnOrderDetailVO> grModels, double totalPrice, OrderMasterSearchDTO torderMaster, String suppname) {
        this.setGoodReturns(grModels);
        this.setBuyerName(torderMaster.getFbuyername());
        this.setTotalPrice(totalPrice);
        this.setOrderId(torderMaster.getId());
        this.setForderno(torderMaster.getForderno());
        this.setFbuydepartment(torderMaster.getFbuydepartment());
        this.setFsuppname(suppname);
        this.setForderdate(torderMaster.getForderdate());
    }
}
