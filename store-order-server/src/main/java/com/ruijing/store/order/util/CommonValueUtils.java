package com.ruijing.store.order.util;

/**
 * @author: liwenyu
 * @createTime: 2023-04-23 16:27
 * @description:
 **/
public class CommonValueUtils {

    public static final String TRUE_NUMBER_STR = "1";

    public static final String FALSE_NUMBER_STR = "0";

    public static final int TRUE_INT = 1;

    public static final int FALSE_INT = 0;

    /**
     * 将字符格式的0/1转为boolean
     * @param str 字符串
     * @return boolean
     */
    public static boolean parseNumberStrToBoolean(String str){
        return TRUE_NUMBER_STR.equals(str);
    }

    public static String parseBoolean2NumberStr(boolean bool){
        return bool ? TRUE_NUMBER_STR : FALSE_NUMBER_STR;
    }

    /**
     * 将boolean转0/1
     * @param bool 字符串
     * @return boolean
     */
    public static int parseBoolean2Number(boolean bool){
        return bool ? TRUE_INT : FALSE_INT;
    }
}
