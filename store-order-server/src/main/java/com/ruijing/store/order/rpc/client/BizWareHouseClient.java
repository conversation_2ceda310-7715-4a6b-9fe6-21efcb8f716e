package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.store.exception.CodeException;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.SubmitWarehouseVO;
import com.ruijing.store.warehouse.utils.OrderDetailsUtil;
import com.ruijing.store.wms.api.dto.*;
import com.ruijing.store.wms.api.dto.entry.BizWmsEntryDTO;
import com.ruijing.store.wms.api.dto.entry.BizWmsOrderEntryDTO;
import com.ruijing.store.wms.api.dto.requset.BizWarehouseEntryReq;
import com.ruijing.store.wms.api.enums.IncompatibilityTypeEnum;
import com.ruijing.store.wms.api.query.BizWarehouseEntryLogQuery;
import com.ruijing.store.wms.api.query.DangerousOccupyStockDetailQuery;
import com.ruijing.store.wms.api.query.WmsEntryApprovalQuery;
import com.ruijing.store.wms.api.service.BizEntryService;
import com.ruijing.store.wms.api.service.BizWarehouseDangerousOccupyStockRpcService;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 新出入库RPC客户端
 * @author: zhongyulei
 * @create: 2020/9/18 10:32
 **/
@ServiceClient
public class BizWareHouseClient {

    private static final String CAT_TYPE = "BizWareHouseClient";

    @MSharpReference(remoteAppkey = "store-wms-service")
    private BizEntryService bizEntryService;

    @MSharpReference
    private BizWarehouseDangerousOccupyStockRpcService bizWarehouseDangerousOccupyStockRpcService;

    @ServiceLog(description = "根据订单号集合查询入库单", serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseEntryDTO> findEntryByOrderNoList(List<String> orderNoList) {
        ApiResult<List<BizWarehouseEntryDTO>> response = bizEntryService.queryEntryByOrderNoList(orderNoList);
        Preconditions.isTrue(response.successful(), "根据订单号集合查询入库单错误：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据入库单id集合查询入库单明细", serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseEntryDetailDTO> findEntryDetailByIdList(List<Integer> entryIdList) {
        ApiResult<List<BizWarehouseEntryDetailDTO>> response = bizEntryService.queryEntryDetailByIds(entryIdList);
        Preconditions.isTrue(response.successful(), "根据入库单id集合查询入库单明细错误：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据入库单号集合查询入库单审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<WmsPersionDTO> findApprovalLogByOrderNo(List<String> orderNoList) {
        ApiResult<List<WmsPersionDTO>> response = bizEntryService.queryEntryPersionByEntryNoList(orderNoList);
        Preconditions.isTrue(response.successful(), "根据入库单号集合查询入库单审批日志错误：" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据入库单id获取日志", serviceType = ServiceType.RPC_CLIENT)
    public Map<Integer, List<BizWarehouseEntryLogDTO>> queryLogByEntryId(List<Integer> entryIdList){
        int partitionSize = 200;
        if(CollectionUtils.isEmpty(entryIdList)){
            return New.emptyMap();
        }
        List<List<Integer>> partitionList = Lists.partition(entryIdList, partitionSize);
        Map<Integer, List<BizWarehouseEntryLogDTO>> allResultMap = New.mapWithCapacity(entryIdList.size());
        for(List<Integer> part : partitionList){
            ApiResult<Map<Integer, List<BizWarehouseEntryLogDTO>>> result = bizEntryService.queryLogByEntryId(New.list(part));
            if(result == null){
                continue;
            }
            BusinessErrUtil.isTrue(result.successful(), result.getMsg());
            allResultMap.putAll(result.getData());
        }
        return allResultMap;

    }

    @ServiceLog(description = "根据入库单id获取日志", serviceType = ServiceType.RPC_CLIENT)
    public Map<Integer, List<BizWarehouseEntryLogDTO>> queryEntryApprovalSuccessLog(List<Integer> entryIdList){
        int partitionSize = 200;
        if(CollectionUtils.isEmpty(entryIdList)){
            return New.emptyMap();
        }
        List<List<Integer>> partitionList = Lists.partition(entryIdList, partitionSize);
        List<BizWarehouseEntryLogDTO> allResultList = New.listWithCapacity(entryIdList.size());
        for(List<Integer> part : partitionList){
            BizWarehouseEntryLogQuery bizWarehouseEntryLogQuery = new BizWarehouseEntryLogQuery();
            bizWarehouseEntryLogQuery.setEntryIds(part);
            RemoteResponse<List<BizWarehouseEntryLogDTO>> result = bizEntryService.queryEntryApprovalSuccessLog(bizWarehouseEntryLogQuery);
            if(result == null){
                continue;
            }
            BusinessErrUtil.isTrue(result.isSuccess(), result.getMsg());
            allResultList.addAll(result.getData());
        }
        if(CollectionUtils.isEmpty(allResultList)){
            return New.emptyMap();
        }
        return DictionaryUtils.groupBy(allResultList, BizWarehouseEntryLogDTO::getEntryId);

    }

    @ServiceLog(description = "是否启用化学品管理", serviceType = ServiceType.COMMON_SERVICE)
    public boolean chemicalManagement(Integer orgId) {
        Preconditions.notNull(orgId, "入参为空");
        ApiResult<Boolean> response = bizEntryService.chemicalManagement(orgId);
        if (response.successful()) {
            return response.getData() == null ? false : response.getData();
        }
        Cat.logError(CAT_TYPE, "chemicalManagement", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orgId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据单位id获取需要入库商品的所有分类,返回分类id的集合
     * @param orgId
     * @return
     */
    @ServiceLog(description = "根据单位id获取需要入库商品的所有分类", serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseEntryDetailDTO> queryKind(Integer orgId) {
        Preconditions.notNull(orgId, "入参为空");
        ApiResult<List<BizWarehouseEntryDetailDTO>> response = bizEntryService.queryKind(orgId);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "queryKind", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orgId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据订单号查询入库申请单列表
     * @param orderNo
     * @return
     * 查询结果为空时返回空列表
     */
    @ServiceLog(description = "根据订单号查询入库申请单列表", serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseEntryDTO> queryEntryByOrderNo(String orderNo){
        Preconditions.hasText(orderNo, "订单号为空");
        ApiResult<List<BizWarehouseEntryDTO>> response = bizEntryService.queryEntryByOrderNo(orderNo);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "queryEntryByOrderNo", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orderNo + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据入参分页查询入库申请单，分页参数必填
     * @param bizWarehouseEntryParam
     * @return
     */
    @ServiceLog(description = "根据入参分页查询入库申请单", serviceType = ServiceType.COMMON_SERVICE)
    public PageApiResult<List<BizWarehouseEntryDTO>> queryEntryPage(BizWarehouseEntryDTO bizWarehouseEntryParam){
        Preconditions.notNull(bizWarehouseEntryParam, "入参为空");
        PageApiResult<List<BizWarehouseEntryDTO>> warehouseListResult = bizEntryService.queryEntryPage(bizWarehouseEntryParam);
        if (warehouseListResult.successful()) {
            return warehouseListResult;
        }
        Cat.logError(CAT_TYPE, "queryEntryPage", "RPC调用返回失败结果：" + JsonUtils.toJson(warehouseListResult) + ";\n入参：" + JsonUtils.toJson(bizWarehouseEntryParam) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(warehouseListResult.getMessage());
    }

    /**
     * 批量插入入库申请单
     * @param bizWarehouseEntryDTOList
     */
    @ServiceLog(description = "批量插入入库申请单", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public boolean batchSaveWarehouseApplication(List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList) {
        Preconditions.notEmpty(bizWarehouseEntryDTOList, "入参为空");
        ApiResult<Boolean> response = bizEntryService.batchPutInStorage(bizWarehouseEntryDTOList);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "batchSaveWarehouseApplication", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(bizWarehouseEntryDTOList) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new CallRpcException(response.getMessage());
    }

    @ServiceLog(description = "重新提交入库", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public boolean reBatchPutInStorage(List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList){
        ApiResult<Boolean> response = bizEntryService.reBatchPutInStorage(bizWarehouseEntryDTOList);
        Preconditions.isTrue(response.successful(), response.getMessage());
        return response.getData();
    }

    /**
     * 获取入库申请单详情
     * @param warehouseApplicationId 入库申请单Id
     * @return
     */
    @ServiceLog(description = "获取入库申请单详情", serviceType = ServiceType.COMMON_SERVICE)
    public BizWarehouseEntryDTO queryEntryDetailById(Integer warehouseApplicationId){
        Preconditions.notNull(warehouseApplicationId, "入库申请单id为空");
        ApiResult<BizWarehouseEntryDTO> response = bizEntryService.queryEntryDetailById(warehouseApplicationId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryEntryDetailById", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + warehouseApplicationId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 入库单作废
     * @return true成功  false失败
     */
    @ServiceLog(description = "入库单作废", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public boolean invalidateEntryByEntryNo(String entryNo) {
        Preconditions.hasText(entryNo, "入库单号不能为空");
        String handlerName = "invalidateEntryByEntryNo";
        ApiResult<Boolean> result = bizEntryService.updateEntryInvalid(entryNo);
        if (result.successful()) {
            return result.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(result) + "\n入库单号：" + entryNo + "\n", new RuntimeException("rpc调用返回失败结果"));
        return false;
    }

    /**
     * 查询审批进度
     * @param entryNo 入库申请单号
     * @return
     */
    @ServiceLog(description = "查询审批进度", serviceType = ServiceType.COMMON_SERVICE)
    public List<ApprovalProgressDTO> queryApprovalProgressByEntryNo(String entryNo){
        String handlerName = "queryApprovalProgressByEntryNo";
        ApiResult<List<ApprovalProgressDTO>> response = bizEntryService.queryApprovalProgressByEntryNo(entryNo);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(response) + "\n入库单号：" + entryNo + "\n", new RuntimeException("rpc调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    @ServiceLog(description = "查询入库审核人", serviceType = ServiceType.COMMON_SERVICE)
    public List<WmsPersionDTO> queryEntryPersonByEntryNoList(List<String> entryNoList){
        String handlerName = "queryEntryPersonByEntryNoList";
        ApiResult<List<WmsPersionDTO>> response = bizEntryService.queryEntryPersionByEntryNoList(entryNoList);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(response) + "\n入库单号：" + JsonUtils.toJson(entryNoList) + "\n", new RuntimeException("rpc调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据参数获取各入库状态的入库申请单数量
     * @param bizWarehouseEntryDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public CountDTO getCount(BizWarehouseEntryDTO bizWarehouseEntryDTO){
        Preconditions.notNull(bizWarehouseEntryDTO, "入参不能为空");
        String handlerName = "getCount";
        ApiResult<CountDTO> response = bizEntryService.getCount(bizWarehouseEntryDTO);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(response) + "\n入参：" + JsonUtils.toJson(bizWarehouseEntryDTO) + "\n", new RuntimeException("rpc调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 判断该账号在本系统是否有权限,有任何一个权限返回true,否则返回false 传applyUserGuid,orgId
     * @param bizWarehouseEntryDTO
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public boolean haveAccess(BizWarehouseEntryDTO bizWarehouseEntryDTO){
        Preconditions.notNull(bizWarehouseEntryDTO, "入参为空");
        String handlerName = "haveAccess";
        ApiResult<Boolean> response = bizEntryService.haveAccess(bizWarehouseEntryDTO);
        if (response.successful()) {
            return response.getData() == null ? false : response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(response) + "\n入参：" + JsonUtils.toJson(bizWarehouseEntryDTO) + "\n", new RuntimeException("rpc调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 查询各审批状态的单的数量
     * @param wmsEntryApprovalQuery
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public ApprovalCountDTO selectApprovalCount(WmsEntryApprovalQuery wmsEntryApprovalQuery){
        Preconditions.notNull(wmsEntryApprovalQuery, "入参不能为空");
        String handlerName = "selectApprovalCount";
        ApiResult<ApprovalCountDTO> response = bizEntryService.selectApprovalCount(wmsEntryApprovalQuery);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, handlerName, "rpc调用返回失败结果:" + JsonUtils.toJson(response) + "\n入参：" + JsonUtils.toJson(wmsEntryApprovalQuery) + "\n", new RuntimeException("rpc调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 自动入库
     * @param orderMasterDO         订单快照信息
     * @param orderDetailDOList     订单明细
     * @param userInfo              登录用户信息
     * @param picURL                收货图片
     * @return                      是否成功
     */
    @ServiceLog(description = "自动入库",serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public Boolean autoInbound(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, UserBaseInfoDTO userInfo, String picURL) {
        List<InboundDetailReqDTO> inboundDetailReqDTOList = orderDetailDOList.stream().map(detail -> {
            // 减去退货商品后的数量
            int realQuantity = OrderDetailsUtil.getRealOrderProductQuantity(detail);
            // 减去退货商品后的价格
            BigDecimal realTotalPrice = OrderDetailsUtil.getRealOrderProductPrice(detail);
            // 总价（含差价）
            BigDecimal totalPriceWithRemainderPrice = realTotalPrice.add(detail.getRemainderPrice());
            
            InboundDetailReqDTO item = new InboundDetailReqDTO();
            item.setSuppId(orderMasterDO.getFsuppid())
                    .setSuppName(orderMasterDO.getFsuppname())
                    .setCategoryId(detail.getCategoryid())
                    .setFirstCategoryId(detail.getFirstCategoryId())
                    .setSecondCategoryId(detail.getSecondCategoryId())
                    .setSpecifications(detail.getFspec())
                    .setProductName(detail.getFgoodname())
                    .setBrandName(detail.getFbrand())
                    .setCasNo(detail.getCasno())
                    .setProductCode(detail.getFgoodcode())
                    .setDangerousType(detail.getDangerousTypeId())
                    .setControlFlag(detail.getRegulatoryTypeId())
                    .setContainer("")
                    .setPrice(totalPriceWithRemainderPrice)
                    .setUnitPrice(detail.getFbidprice())
                    .setReceivableNum(realQuantity)
                    .setReceivedUnit(detail.getFunit())
                    .setFpicpath(detail.getFpicpath())
                    .setOrderDetailId(detail.getId())
                    .setRemainderPrice(detail.getRemainderPrice());
            return item;
        }).collect(Collectors.toList());

        InboundReqDTO inboundReqDTO = new InboundReqDTO();
        inboundReqDTO.setOrgId(orderMasterDO.getFuserid())
                .setOrderNo(orderMasterDO.getForderno())
                .setApplyUserGuid(userInfo.getGuid())
                .setApplyUserName(userInfo.getName())
                .setPurchaseUserGuid(userInfo.getGuid())
                .setPurchaseUserName(orderMasterDO.getFbuyername())
                .setDeptId(orderMasterDO.getFbuydepartmentid())
                .setDeptName(orderMasterDO.getFbuydepartment())
                .setReceivePicUrls(picURL)
                .setDetails(inboundDetailReqDTOList)
                .setSpecies(orderMasterDO.getSpecies().intValue());

        RemoteResponse<Boolean> response = bizEntryService.autoInbound(inboundReqDTO);
        Preconditions.isTrue(response.isSuccess(), "自动入库失败: " + response.getMsg());
        return response.getData();
    }

    /**
     * 查询可入库的库房
     * @param orderDetailDTOList 商品详情
     * @param orderMasterDTO 订单数据
     * @return 可入库的库房
     */
    @ServiceLog(description = "查询可入库库房", serviceType = ServiceType.RPC_CLIENT)
    public List<EntryDetailRoomDTO> queryEntryDetailRoom(List<OrderDetailDTO> orderDetailDTOList, OrderMasterDTO orderMasterDTO){
        InboundReqDTO inboundReqDTO = new InboundReqDTO();
        List<InboundDetailReqDTO> inboundDetailReqDTOList = orderDetailDTOList.stream().map(detail->{
            InboundDetailReqDTO inboundDetailReqDTO = new InboundDetailReqDTO();
            inboundDetailReqDTO.setOrderDetailId(detail.getId());
            inboundDetailReqDTO.setFirstCategoryId(detail.getFirstCategoryId());
            inboundDetailReqDTO.setSecondCategoryId(detail.getSecondCategoryId());
            return inboundDetailReqDTO;
        }).collect(Collectors.toList());
        inboundReqDTO.setOrderNo(orderMasterDTO.getForderno());
        inboundReqDTO.setDetails(inboundDetailReqDTOList);
        inboundReqDTO.setOrgId(orderMasterDTO.getFuserid());
        inboundReqDTO.setDeptId(orderMasterDTO.getFbuydepartmentid());
        inboundReqDTO.setSpecies(orderMasterDTO.getSpecies().intValue());
        RemoteResponse<List<EntryDetailRoomDTO>> response = bizEntryService.queryEntryDetailRoom(inboundReqDTO);
        Preconditions.isTrue(response.isSuccess(), "查询可入库库房失败");
        return response.getData();
    }

    @ServiceLog(description = "查询准备入库商品的危化品标识", serviceType = ServiceType.RPC_CLIENT)
    public List<EntryDangerousDTO> queryEntryDangerous(String orderNo, Integer entryId, Integer orgId){
        InboundReqDTO inboundReqDTO = new InboundReqDTO().setOrgId(orgId).setOrderNo(orderNo).setEntryId(entryId);
        RemoteResponse<List<EntryDangerousDTO>> response = bizEntryService.queryEntryDangerous(inboundReqDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "释放预占库存", serviceType = ServiceType.RPC_CLIENT)
    public void releaseStockByOrder(BizWarehouseDangerousOccupyReturnGoodsDTO param){
        RemoteResponse<Boolean> response = bizWarehouseDangerousOccupyStockRpcService.releaseStockByOrder(param);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "获取配伍禁忌校验数据", serviceType = ServiceType.RPC_CLIENT)
    public void checkIncompatibility(List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList, Boolean incompatibilityVerify){
        RemoteResponse<List<BizWarehouseIncompatibilityDTO>> response = bizEntryService.checkIncompatibility(bizWarehouseEntryDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if(CollectionUtils.isNotEmpty(response.getData())){
            if(Boolean.TRUE.equals(incompatibilityVerify) && !IncompatibilityTypeEnum.RESTRICT.getValue().equals(response.getData().get(0).getType())){
                // 如果谨提醒，要校验是否商品谨提醒。（取第一个是库房那边说整批都一样）
                return;
            }
            SubmitWarehouseVO submitWarehouseVO = new SubmitWarehouseVO().setBizWarehouseIncompatibilityDTOList(response.getData());
            throw new CodeException(CodeException.WAREHOUSE_INCOMPATIBILITY_CODE, submitWarehouseVO);
        }
    }

    @ServiceLog(description = "提交入库时校验计量含量", serviceType = ServiceType.RPC_CLIENT)
    public void checkMeasurementNum(List<BizWarehouseEntryDTO> entryDTOList) {
        RemoteResponse<List<BizWarehouseIncompatibilityDTO>> response = bizEntryService.checkMeasurementNum(entryDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if (CollectionUtils.isNotEmpty(response.getData())) {
            SubmitWarehouseVO submitWarehouseVO = new SubmitWarehouseVO();
            submitWarehouseVO.setBizWarehouseIncompatibilityDTOList(response.getData());
            throw new CodeException(CodeException.CHECK_MEASUREMENT_NUMBER_CODE, submitWarehouseVO);
        }
    }

    @ServiceLog(description = "根据入库单号撤销入库", serviceType = ServiceType.RPC_CLIENT,operationType = OperationType.WRITE)
    public void entryWithDraw(EntryWithDrawDTO entryWithDrawDTO) {
        RemoteResponse<Boolean> response = bizEntryService.entryWithDraw(entryWithDrawDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "根据订单号查询入库单", serviceType = ServiceType.RPC_CLIENT)
    public List<BizWmsOrderEntryDTO> queryEntryByOrderNos(BizWarehouseEntryReq req) {
        RemoteResponse<List<BizWmsOrderEntryDTO>> response = bizEntryService.queryByOrderNos(req);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
    

    @ServiceLog(operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public List<BizWarehouseDangerousOccupyStockDetailDTO> getDangerousOccupyStockDetail(String appNo){
        DangerousOccupyStockDetailQuery dangerousOccupyStockDetailQuery = new DangerousOccupyStockDetailQuery();
        dangerousOccupyStockDetailQuery.setApplicationNo(appNo);
        RemoteResponse<List<BizWarehouseDangerousOccupyStockDetailDTO>> response = bizEntryService.getDangerousOccupyStockDetail(dangerousOccupyStockDetailQuery);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "查询最新的入库单")
    public List<BizWmsEntryDTO> queryLatestEntry(String orderNo){
        RemoteResponse<List<BizWmsEntryDTO>> response = bizEntryService.queryLatestEntry(orderNo);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "现货仓退货入库", operationType = OperationType.WRITE)
    public Boolean spotWarehouseEntry(SpotWarehouseEntryDTO spotWarehouseEntryDTO) {
        RemoteResponse<Boolean> response = bizEntryService.spotWarehouseEntry(spotWarehouseEntryDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    public void checkThresholds(List<BizWarehouseEntryDTO> warehouseEntryList){
        RemoteResponse<String> response = bizEntryService.checkThresholds(warehouseEntryList);
        BusinessErrUtil.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "查询出入库单信息")
    public List<BizWarehouseEntryExitDTO> queryEntryExitByOrderNo(String orderNo) {
        RemoteResponse<List<BizWarehouseEntryExitDTO>> response = bizEntryService.queryEntryExitByOrderNo(orderNo);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

}
