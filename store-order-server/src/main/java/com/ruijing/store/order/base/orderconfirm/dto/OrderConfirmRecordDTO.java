package com.ruijing.store.order.base.orderconfirm.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @<PERSON> liujiawei
 * @Date Created in 10:17 AM 2018/11/6.
 * @Description
 * @Modified
 */
public class OrderConfirmRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("图片")
    private List<String> pics;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<String> getPics() {
        return pics;
    }

    public void setPics(List<String> pics) {
        this.pics = pics;
    }
}
