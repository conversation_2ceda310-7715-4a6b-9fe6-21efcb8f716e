package com.ruijing.store.order.rpc.impl;

import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.RangeFilter;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.shop.wallet.api.dto.WaitingChargingOrderDTO;
import com.ruijing.shop.wallet.api.service.WalletBillCheckService;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.search.service.OrderSearchBoostService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-07-20 09:45
 * @description:
 **/
@MSharpService
public class WalletBillCheckServiceImpl implements WalletBillCheckService {

    /**
     * 需要进行扫描的状态--除退货
     */
    private final static List<Integer> STATUS_LIST_TO_SCAN = New.list(OrderStatusEnum.OrderReceiveApproval.getValue(),
            OrderStatusEnum.WaitingForStatement_1.getValue(),
            OrderStatusEnum.Statementing_1.getValue(),
            OrderStatusEnum.Finish.getValue());

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Override
    public PageableResponse<List<WaitingChargingOrderDTO>> queryNeedNoticeChargeOrder(WaitingChargingOrderDTO param) {
        Request request = new Request();
        int pageSize = param.getSize() == null || param.getSize() <= 0 ? 20 : param.getSize();
        int page = param.getPage() == null || param.getPage() <= 0 ? 1 : param.getPage();
        int startHit = (page - 1) * pageSize;
        request.setStart(startHit);
        request.setPageSize(pageSize);

        String lowerTime = param.getBegingTime() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, param.getBegingTime());
        String upperTime = param.getEndTime() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, param.getEndTime());
        if(lowerTime != null || upperTime != null){
            request.addFilter(new RangeFilter("create_time", lowerTime, upperTime, true, true));
        }
        if(param.getLastOrderId() != null){
            request.addFilter(new RangeFilter("id", param.getLastOrderId().toString(), null, false, true));
        }
        // 线上单
        request.addFilter(new TermFilter("species", ProcessSpeciesEnum.NORMAL.getValue()));
        // 排序
        request.addOrderSortItem(new FieldSortItem("id", SortOrder.ASC));
        if(param.getOrgId() != null){
            // 如果传了orgId，则支持按OrgId筛选
            request.addFilter(new TermFilter("fuserid", param.getOrgId().intValue()));
        }
        // 排除中大
        request.addNotFilter(new TermFilter("fuserid", ZhongShanDaXueOrgEnum.getAllOrgIds()));
        // 完成收货(由于不含关闭状态，故也不统计试用订单）
        request.addFilter(new TermFilter("status", STATUS_LIST_TO_SCAN));

        SearchPageResultDTO<OrderMasterSearchDTO> result = orderSearchBoostService.search(request);
        // 排除没有同步到的异常数据
        List<WaitingChargingOrderDTO> waitingChargingOrderDTOList = result.getRecordList().stream()
                .map(orderMasterSearchDTO -> {
            WaitingChargingOrderDTO waitingChargingOrderDTO = new WaitingChargingOrderDTO();
            waitingChargingOrderDTO.setLastOrderId(orderMasterSearchDTO.getId());
            waitingChargingOrderDTO.setOrderAmount(BigDecimal.valueOf(orderMasterSearchDTO.getForderamounttotal()));
            waitingChargingOrderDTO.setOrderDate(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getForderdate()));
            waitingChargingOrderDTO.setOrderNo(orderMasterSearchDTO.getForderno());
            waitingChargingOrderDTO.setOrgId(orderMasterSearchDTO.getFuserid().longValue());
            waitingChargingOrderDTO.setBuyerId(orderMasterSearchDTO.getFbuyerid());
            waitingChargingOrderDTO.setSuppId(orderMasterSearchDTO.getFsuppid());
            return waitingChargingOrderDTO;
        }).collect(Collectors.toList());

        return PageableResponse.<List<WaitingChargingOrderDTO>>custom()
                .setTotal(result.getTotalHits())
                .setPageNo(page)
                .setPageSize(pageSize)
                .setData(waitingChargingOrderDTOList)
                .setSuccess();
    }
}
