package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/6 19:03
 * @description
 */
@RpcModel("电子签名配置vo")
public class ElectronicSignConfigVO implements Serializable {

    private static final long serialVersionUID = 1272341492009157096L;
    
    @RpcModelProperty(
            value = "电子签名启用情况，如果是禁用，其他字段不会返回",
            required = true
    )
    private Integer electronicSignEnable;
    
    @RpcModelProperty("信息是否已完善，判断标准：已实名验证、当前机构配置的签名样式如果不是系统样式，需要用户上传有对应样式的电子签名图片")
    private Boolean completeInformation;
    
    @RpcModelProperty("是否需要上传图片，如果当前机构配置的签名样式不是系统样式，且用户还没上传对应样式的电子签名图片，则为true")
    private Boolean uploadESPhoto;
    
    @RpcModelProperty("是否实名认证  false未实名认证")
    private Boolean realNameAuthentication;
    
    @RpcModelProperty("单位配置能否免密  false禁止免密")
    private Boolean orgWithoutCode;
    
    @RpcModelProperty("用户配置是否免密 false 不免密")
    private Boolean userWithoutCode;
    
    @RpcModelProperty("电子签名图片（如果用户有设置就会返回，否则不返回）")
    private String esPhotoUrl;
    
    @RpcModelProperty("电子签名样式，ElectronicSignatureStyleEnum")
    private Integer type;

    public Integer getElectronicSignEnable() {
        return electronicSignEnable;
    }

    public void setElectronicSignEnable(Integer electronicSignEnable) {
        this.electronicSignEnable = electronicSignEnable;
    }

    public Boolean getCompleteInformation() {
        return completeInformation;
    }

    public void setCompleteInformation(Boolean completeInformation) {
        this.completeInformation = completeInformation;
    }

    public Boolean getUploadESPhoto() {
        return uploadESPhoto;
    }

    public void setUploadESPhoto(Boolean uploadESPhoto) {
        this.uploadESPhoto = uploadESPhoto;
    }

    public Boolean getRealNameAuthentication() {
        return realNameAuthentication;
    }

    public void setRealNameAuthentication(Boolean realNameAuthentication) {
        this.realNameAuthentication = realNameAuthentication;
    }

    public Boolean getOrgWithoutCode() {
        return orgWithoutCode;
    }

    public void setOrgWithoutCode(Boolean orgWithoutCode) {
        this.orgWithoutCode = orgWithoutCode;
    }

    public Boolean getUserWithoutCode() {
        return userWithoutCode;
    }

    public void setUserWithoutCode(Boolean userWithoutCode) {
        this.userWithoutCode = userWithoutCode;
    }

    public String getEsPhotoUrl() {
        return esPhotoUrl;
    }

    public void setEsPhotoUrl(String esPhotoUrl) {
        this.esPhotoUrl = esPhotoUrl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ElectronicSignConfigVO{" +
                "electronicSignEnable=" + electronicSignEnable +
                ", completeInformation=" + completeInformation +
                ", uploadESPhoto=" + uploadESPhoto +
                ", realNameAuthentication=" + realNameAuthentication +
                ", orgWithoutCode=" + orgWithoutCode +
                ", userWithoutCode=" + userWithoutCode +
                ", esPhotoUrl='" + esPhotoUrl + '\'' +
                ", type=" + type +
                '}';
    }
}
