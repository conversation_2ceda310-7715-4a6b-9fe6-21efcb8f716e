package com.ruijing.store.order.business.enums.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/30 15:31
 * @Description
 **/
public enum OrderContractUploadConfigEnum {

    /**
     * 没有合同上传功能
     */
    FALSE(0,"没有合同上传功能"),
    /**
     * 具备合同上传功能
     */
    TRUE(1,"具备合同上传功能");

    public final Integer value;

    public final String desc;

    OrderContractUploadConfigEnum(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static final OrderContractUploadConfigEnum getByName(String name) {
        for (OrderContractUploadConfigEnum orderContractUploadConfigEnum : OrderContractUploadConfigEnum.values()) {
            if (orderContractUploadConfigEnum.desc.equals(name)){
                return orderContractUploadConfigEnum;
            }
        }
        return null;
    }

    public static final OrderContractUploadConfigEnum getByValue(Integer value) {
        for (OrderContractUploadConfigEnum orderContractUploadConfigEnum : OrderContractUploadConfigEnum.values()) {
            if (orderContractUploadConfigEnum.value.equals(value)){
                return orderContractUploadConfigEnum;
            }
        }
        return null;
    }
}
