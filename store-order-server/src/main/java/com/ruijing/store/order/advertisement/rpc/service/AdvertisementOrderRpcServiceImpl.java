package com.ruijing.store.order.advertisement.rpc.service;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.query.NestedQuery;
import com.ruijing.search.client.query.RangeQuery;
import com.ruijing.search.client.query.TermQuery;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.Record;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderDTO;
import com.ruijing.store.order.api.advertisement.dto.AdvertisementOrderRequest;
import com.ruijing.store.order.api.advertisement.service.AdvertisementOrderRpcService;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 广告投放订单相关服务类
 * @date 2023/9/4 16:06
 */
@MSharpService
public class AdvertisementOrderRpcServiceImpl implements AdvertisementOrderRpcService {

    private static final String NESTED_EXTRA = "order_extra";

    private static final String ORDER_SEARCH_INDEX = "order";

    @Resource
    private UserClient userClient;

    @Resource
    private OrderSearchRPCServiceClient orderSearchRPCServiceClient;

    @Override
    public PageableResponse<List<AdvertisementOrderDTO>> listAdvertisementOrder(AdvertisementOrderRequest request) {
        // 入参校验
        Preconditions.notNull(request.getStartTime(), "开始时间不可为空");
        Preconditions.notNull(request.getEndTime(), "结束时间不可为空");
        // 组装查询条件
        Request searchRequest = new Request();
        searchRequest.setKey(ORDER_SEARCH_INDEX);
        searchRequest.setPageSize(request.getPageSize());
        searchRequest.setStart(request.getPageNo() > 0 ? (request.getPageNo() - 1) * request.getPageSize() : 0);
        if(CollectionUtils.isNotEmpty(request.getSuppIdList())) {
            searchRequest.addQuery(new TermQuery("fsuppid", request.getSuppIdList()));
        }
        if(CollectionUtils.isNotEmpty(request.getAdvertisementIdList())) {
            searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_value", request.getAdvertisementIdList())));
        }

        searchRequest.addQuery(new NestedQuery(NESTED_EXTRA, new TermQuery(NESTED_EXTRA + ".extra_key", OrderExtraEnum.ADVERTISEMENT_ID.getValue())));
        searchRequest.addQuery(new RangeQuery("forderdate", DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getStartTime()), DateUtils.format("yyyy-MM-dd HH:mm:ss", request.getEndTime())));
        searchRequest.addNotFilter(new TermFilter("status", OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        searchRequest.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));
        Response response = orderSearchRPCServiceClient.search(searchRequest);
        Preconditions.isTrue(response.isSuccess(), "查询订单信息失败");
        // 组装结果
        List<Record> recordList = response.getRecordList();
        List<AdvertisementOrderDTO> advertisementOrderDTOList = New.list();
        if(CollectionUtils.isNotEmpty(recordList)){
            for(Record record : recordList){
                AdvertisementOrderDTO advertisementOrderDTO = new AdvertisementOrderDTO();
                Map<String, String> valueMap = record.getValueMap();
                advertisementOrderDTO.setOrderNo(valueMap.get("forderno"));
                String amountStr = valueMap.get("forderamounttotal");
                advertisementOrderDTO.setOrderAmount(StringUtils.isBlank(amountStr) ? BigDecimal.ZERO : BigDecimal.valueOf(Double.parseDouble(amountStr)));
                String statusStr = valueMap.get("status");
                advertisementOrderDTO.setOrderStatus(StringUtils.isBlank(statusStr) ? null : Integer.parseInt(statusStr));
                String orderDateStr = valueMap.get("forderdate");
                advertisementOrderDTO.setOrderDate(StringUtils.isBlank(orderDateStr) ? null : DateUtils.parse("yyyy-MM-dd HH:mm:ss", orderDateStr));
                String buyerIdStr = valueMap.get("fbuyerid");
                advertisementOrderDTO.setBuyerId(StringUtils.isBlank(buyerIdStr) ? null : Integer.parseInt(buyerIdStr));
                advertisementOrderDTO.setBuyerName(valueMap.get("fbuyername"));
                String orgIdStr = valueMap.get("fuserid");
                Preconditions.isTrue(StringUtils.isNotBlank(orgIdStr), "数据有误单位id不可为空");
                advertisementOrderDTO.setOrgId(Integer.parseInt(orgIdStr));
                advertisementOrderDTO.setOrgName(valueMap.get("fusername"));
                String deptIdStr = valueMap.get("fbuydepartmentid");
                advertisementOrderDTO.setDepartmentId(StringUtils.isBlank(deptIdStr) ? null : Integer.parseInt(deptIdStr));
                advertisementOrderDTO.setDepartmentName(valueMap.get("fbuydepartment"));
                advertisementOrderDTOList.add(advertisementOrderDTO);
            }
            Map<Integer, List<AdvertisementOrderDTO>> orgOrderMap = advertisementOrderDTOList.stream().collect(Collectors.groupingBy(AdvertisementOrderDTO::getOrgId));
            Map<Integer, Map<Integer, UserBaseInfoDTO>> orgUserIdMap = New.map();
            orgOrderMap.forEach((orgId, list) -> {
                List<Integer> userIdList = list.stream().map(AdvertisementOrderDTO::getBuyerId).distinct().collect(Collectors.toList());
                List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(userIdList, orgId);
                Map<Integer, UserBaseInfoDTO> userBaseInfoDTOMap = userBaseInfoDTOList.stream().collect(Collectors.toMap(UserBaseInfoDTO::getId, Function.identity(), (oo,no) -> no));
                orgUserIdMap.put(orgId, userBaseInfoDTOMap);
            });
            advertisementOrderDTOList.forEach(advertisementOrderDTO -> {
                Map<Integer, UserBaseInfoDTO> userBaseInfoDTOMap = orgUserIdMap.get(advertisementOrderDTO.getOrgId());
                if(userBaseInfoDTOMap != null){
                    UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(advertisementOrderDTO.getBuyerId());
                    if(userBaseInfoDTO != null) {
                        advertisementOrderDTO.setBuyerEmail(userBaseInfoDTO.getEmail());
                        advertisementOrderDTO.setBuyerMobile(userBaseInfoDTO.getMobile());
                    }
                }
            });
        }
        return PageableResponse.<List<AdvertisementOrderDTO>>custom()
                .setCode(RemoteResponse.SUCCESS)
                .setData(advertisementOrderDTOList)
                .setPageSize(request.getPageSize())
                .setPageNo(request.getPageNo())
                .setTotal(response.getTotalHits())
                .setHasNext((long) request.getPageNo() * request.getPageSize() < response.getTotalHits())
                .build();
    }
}
