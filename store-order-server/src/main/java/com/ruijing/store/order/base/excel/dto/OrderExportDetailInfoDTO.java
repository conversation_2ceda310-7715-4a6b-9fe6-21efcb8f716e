package com.ruijing.store.order.base.excel.dto;

import java.math.BigDecimal;
import java.util.Date;

public class OrderExportDetailInfoDTO {
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 采购单id
     */
    private Integer applicationId;

    /**
     * 采购组
     */
    private String departmentName;

    /**
     * 采购人
     */
    private String purchaseName;

    /**
     * 收货人
     */
    private String  receiver;

    /**
     * 供应商
     */
    private String suppName;

    /**
     * 给供应商备注
     */
    private String remark;


    /**
     * 订单日期
     */
    private Date orderDate;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 状态
     */
    private Integer status;


    /**
     * 经费卡卡号
     */
    private String fundCardNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getPurchaseName() {
        return purchaseName;
    }

    public void setPurchaseName(String purchaseName) {
        this.purchaseName = purchaseName;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }
}
