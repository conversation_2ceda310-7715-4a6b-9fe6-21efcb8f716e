package com.ruijing.store.order.other.translator;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderAttachmentVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: chenzhanliang
 * @createTime: 2024-12-31 09:44
 * @description:
 **/
public class FileTranslator {

    public static List<OrderAttachmentVO> orderUploadFileDTOList2OrderAttachmentVOList(List<OrderUploadFileDTO> orderUploadFileDTOList){
        if(CollectionUtils.isEmpty(orderUploadFileDTOList)){
            return New.list();
        }
        List<OrderAttachmentVO> orderAttachmentVOList = new ArrayList<>(orderUploadFileDTOList.size());
        for(OrderUploadFileDTO orderUploadFileDTO : orderUploadFileDTOList){
            OrderAttachmentVO orderAttachmentVO = new OrderAttachmentVO();
            orderAttachmentVO.setUrl(orderUploadFileDTO.getUrl());
            orderAttachmentVO.setFileName(orderUploadFileDTO.getFileName());
            orderAttachmentVOList.add(orderAttachmentVO);
        }
        return orderAttachmentVOList;
    }
}
