package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;



@Model("修改订单拓展信息")
public class ModifyOrderExtInfoRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty(value = "订单id", required = true)
    private Integer orderId;

    @RpcModelProperty(value = "实验数据网盘链接", description = "江西中医附院定制")
    private String experimentDataUrl;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getExperimentDataUrl() {
        return experimentDataUrl;
    }

    public void setExperimentDataUrl(String experimentDataUrl) {
        this.experimentDataUrl = experimentDataUrl;
    }

    @Override
    public String toString() {
        return "ModifyOrderExtInfoRequestDTO{" +
                "orderId=" + orderId +
                ", experimentDataUrl='" + experimentDataUrl + '\'' +
                '}';
    }
}
