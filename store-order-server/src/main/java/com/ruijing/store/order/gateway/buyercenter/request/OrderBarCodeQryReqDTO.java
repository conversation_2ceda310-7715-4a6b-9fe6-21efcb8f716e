package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-08-08 16:18
 * @description:
 **/
public class OrderBarCodeQryReqDTO implements Serializable {

    private static final long serialVersionUID = 4824523157748551031L;

    /**
     * 订单详情id
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 订单详情id
     */
    @RpcModelProperty("订单详情id")
    private Integer orderDetailId;

    @RpcModelProperty(value = "一物一码类型",enumClass = UniqueBarCodeTypeEnum.class)
    private List<Integer> typeList;

    public String getOrderNo() {
        return orderNo;
    }

    public OrderBarCodeQryReqDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public OrderBarCodeQryReqDTO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public OrderBarCodeQryReqDTO setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderBarCodeQryReqDTO.class.getSimpleName() + "[", "]")
                .add("orderNo='" + orderNo + "'")
                .add("orderDetailId=" + orderDetailId)
                .add("typeList=" + typeList)
                .toString();
    }
}
