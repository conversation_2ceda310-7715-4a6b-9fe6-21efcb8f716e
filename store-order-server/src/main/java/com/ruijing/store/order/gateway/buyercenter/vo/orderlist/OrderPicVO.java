package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/11/17 17:09
 * @Description
 **/
@RpcModel("订单管理-我的订单列表订单图片信息")
public class OrderPicVO implements Serializable {

    private static final long serialVersionUID = 3899720078080861032L;

    /**
     * 图片路径
     */
    @RpcModelProperty("图片路径")
    private String url;

    /**
     * 图片上传时间
     */
    @RpcModelProperty("图片上传时间")
    private Long time;

    public String getUrl() {
        return url;
    }

    public OrderPicVO setUrl(String url) {
        this.url = url;
        return this;
    }

    public Long getTime() {
        return time;
    }

    public OrderPicVO setTime(Long time) {
        this.time = time;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderPicVO{");
        sb.append("url='").append(url).append('\'');
        sb.append(", time=").append(time);
        sb.append('}');
        return sb.toString();
    }
}
