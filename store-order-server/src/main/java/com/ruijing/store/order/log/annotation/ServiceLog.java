package com.ruijing.store.order.log.annotation;

import com.ruijing.store.order.log.enums.OperationType;
import com.ruijing.store.order.log.enums.ServiceType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * 日志注解, aop环绕记录出入参
 */
@Target({ElementType.METHOD, ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ServiceLog {

    String description() default "";

    /**
     * 日志的操作类型 ，查询操作 "read"  读取操作"writer"
     * @return
     */
    OperationType operationType() default  OperationType.READ;

    /**
     * 服务类型 rpc提供者服务 rpc-provider， rpc消费者服务 rpc-consumer，普通服务 common
     * @return
     */
    ServiceType serviceType() default  ServiceType.RPC_SERVICE;
}
