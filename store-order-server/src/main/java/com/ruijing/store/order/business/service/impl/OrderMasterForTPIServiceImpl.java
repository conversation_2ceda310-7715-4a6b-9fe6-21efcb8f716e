package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.financial.docking.dto.order.OrderDetailDTO;
import com.reagent.research.financial.docking.dto.order.OrderReturnDTO;
import com.reagent.research.financial.docking.dto.order.OrderStatusUpdateDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.rpc.client.BusinessDockingRPCClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.rpc.client.TPIOrderClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ruijing.store.order.api.base.enums.OrderApprovalEnum.CANCEL;
import static com.ruijing.store.order.api.base.enums.OrderApprovalEnum.NOTICE_JILI_DISPATCH;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/3/1 14:57
 **/
@Service
public class OrderMasterForTPIServiceImpl implements OrderMasterForTPIService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private final String CAT_TYPE = "OrderMasterForTPIServiceImpl";

    @Resource
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private TPIOrderClient tpiOrderClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Override
    public boolean updateThirdPlatformOrder(UpdateOrderParamDTO request) {
        // 不属于第三方对接单位的特殊订单状态不需要更新
        Integer orderStatus = request.getStatus();
        if (!DockingConstant.THIRD_PART_PLATFORM_STATUS_COLLECT.contains(orderStatus)) {
            return true;
        }
        String orderNo = request.getOrderNo();
        final OrderMasterDO orderInfo = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderInfo, ExecptionMessageEnum.PUSH_ORDER_STATUS_FAILED_NO_ORDER_INFO);
        // 非单号对接单位不需要推送订单状态
        if (!DockingConstant.THIRD_PARTY_PLATFORM_ORGANIZATION.contains(orderInfo.getFusercode())) {
            return true;
        }
        // 查询第三方平台的关联订单号，用galaxy的RPC接口. 目前广工不需要推送更新状态给管理平台, 华农二期代码没上线，也先加上。华农上线后删除
        if (needPushOrderStatusStrategy(orderInfo)) {
            BusinessDockingDTO dockingOrderInfo = businessDockingRPCClient.getBusinessDockingOrderInfo(orderNo);
            BusinessErrUtil.notNull(dockingOrderInfo, ExecptionMessageEnum.PUSH_ORDER_STATUS_FAILED_NO_THIRD_PARTY);
            this.updateThirdPartPlatformOrder(orderStatus, dockingOrderInfo, orderInfo);
        }
        return true;
    }

    /**
     * 是否需要推送订单状态到对方的管理平台
     * @param orderInfo
     * @return
     */
    private boolean needPushOrderStatusStrategy(OrderMasterDO orderInfo) {
        // 非广工&&非华农的单&&以及广西肿瘤的新单需要推送
        return !OrderDateConstant.isOldOrderForNormal(orderInfo.getFusercode(), orderInfo.getForderdate());
    }

    /**
     * 单号对接的单位推送退货信息给对方
     * @param request
     * @param orgCode
     * @return
     */
    public boolean orderReturn(GoodsReturn request, String orgCode) {
        if (!needPushReturnStrategy(request, orgCode)) {
            return true;
        }
        // 记录财务操作日志
        orderOtherLogClient.createOrderDockingLog(request.getOrderNo(), orgCode, null, "return_status: " + request.getGoodsReturnStatus(), GoodsReturnStatusEnum.getDescriptionByCode(request.getGoodsReturnStatus()), null);

        OrgRequest<OrderReturnDTO> params = new OrgRequest<>();
        params.setOrgCode(orgCode);
        OrderReturnDTO orderReturnDTO = new OrderReturnDTO();
        orderReturnDTO.setAppKey(Environment.getAppKey());
        orderReturnDTO.setOrderNo(request.getOrderNo());
        orderReturnDTO.setReturnNo(request.getReturnNo());

        // 设置OrderDetailDTO的策略
        this.setReturnDetailStrategy(request, orderReturnDTO);

        params.setData(orderReturnDTO);

        return tpiOrderClient.orderReturn(params);
    }

    private void setReturnDetailStrategy(GoodsReturn request, OrderReturnDTO orderReturnDTO) {
        String goodsReturnDetailJSON = request.getGoodsReturnDetailJSON();
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);
        List<OrderDetailDTO> orderDetailDTOs = new ArrayList<>(goodsReturnInfoDetailVOS.size());
        for (GoodsReturnInfoDetailVO vo : goodsReturnInfoDetailVOS) {
            OrderDetailDTO it = new OrderDetailDTO();
            it.setId(vo.getDetailId());
            it.setQuantity(vo.getQuantity());
            it.setGoodsId(vo.getProductId());
            it.setPrice(vo.getPrice());
            ExtraDTO extraDTO = new ExtraDTO();
            extraDTO.setField("returnAmount");
            extraDTO.setValue(vo.getAmount().setScale(2, RoundingMode.HALF_UP).toPlainString());
            it.setExtraDTOs(Arrays.asList(extraDTO));
            orderDetailDTOs.add(it);
        }
        orderReturnDTO.setOrderDetailDTOs(orderDetailDTOs);
    }

    /**
     * 需要同步退货状态策略
     * @param request   退货单
     * @param orgCode   机构编码
     * @return          是否需要同步退货状态到对方管理平台
     */
    private boolean needPushReturnStrategy(GoodsReturn request, String orgCode) {
        if (orgCode.equals(DockingConstant.GUANG_XI_ZHONG_LIU)) {// 广州医, 广西肿瘤卖家收货, 同意退货要推送状态
            if (!GoodsReturnStatusEnum.SUCCESS.getCode().equals(request.getGoodsReturnStatus())) {
                return false;
            } else {
                OrderMasterDO orderInfo = orderMasterMapper.findByForderno(request.getOrderNo());
                if (!OrderDateConstant.isOldOrderForNormal(orgCode, orderInfo.getForderdate())) {
                    // 新订单需要校验推送状态
                    dockingExtraService.customValidationDockingStatus(orderInfo.getForderno());
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    private void updateThirdPartPlatformOrder(Integer updatedStatus, BusinessDockingDTO dockingOrderInfo, OrderMasterDO orderInfo) {
        ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO = new ThirdPartyPlatformOrderBO();
        thirdPartyPlatformOrderBO.setReagentOrderNo(orderInfo.getForderno());
        thirdPartyPlatformOrderBO.setExtraOrderNo(dockingOrderInfo.getDockingNo());
        thirdPartyPlatformOrderBO.setDepartmentName(orderInfo.getFbuydepartment());
        thirdPartyPlatformOrderBO.setOrderCreationTime(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderInfo.getForderdate()));
        thirdPartyPlatformOrderBO.setOrderPrice(orderInfo.getForderamounttotal().toEngineeringString());
        thirdPartyPlatformOrderBO.setOrgCode(orderInfo.getFusercode());
        thirdPartyPlatformOrderBO.setOrgId(orderInfo.getFuserid());
        thirdPartyPlatformOrderBO.setSuppId(orderInfo.getFsuppid());
        thirdPartyPlatformOrderBO.setDockStatus(orderInfo.getStatus());

        final UserBaseInfoDTO userInfo = userClient.getUserInfo(orderInfo.getFbuyerid(), orderInfo.getFuserid());
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.PUSH_ORDER_STATUS_FAILED_NO_PURCHASER);
        thirdPartyPlatformOrderBO.setJobNumber(userInfo.getJobnumber());

        OrderApprovalEnum approvalType = this.setThirdPartyPlatformOrderStrategy(updatedStatus, thirdPartyPlatformOrderBO, orderInfo);
        // 记录日志策略
        this.logDockingStrategy(orderInfo, thirdPartyPlatformOrderBO);
        if (approvalType != null) {
            // 同步订单状态到第三方平台
            this.updateOrderStatusAsync(thirdPartyPlatformOrderBO);
        }
    }

    /**
     * 异步 更新第三方平台订单状态
     * @param thirdOrderBO
     * @return
     */
    public ListenableFuture<Boolean> updateOrderStatusAsync(ThirdPartyPlatformOrderBO thirdOrderBO) {
        return AsyncExecutor.listenableCallAsync(() -> this.updateOrderStatus(thirdOrderBO))
                .addFailureCallback(ex -> {
                    LOGGER.error("订单号:" + thirdOrderBO.getReagentOrderNo() + "更新第三方订单状态失败：" + ex);
                    Cat.logError(CAT_TYPE, "updateOrderStatusAsync", "更新第三方订单状态失败：", ex);
                    // 更新对接平台失败时，记录失败日志
                    orderOtherLogClient.createOrderDockingLog(thirdOrderBO.getReagentOrderNo(), thirdOrderBO.getOrgCode(), null, ex.getCause().toString(), thirdOrderBO.getRemark(), null);
                    // 广西肿瘤更新订单失败要记录更新失败日志, 然后支持用户重新推送
                    if (DockingConstant.GUANG_XI_ZHONG_LIU.equals(thirdOrderBO.getOrgCode())) {
                        dockingExtraService.saveOrUpdateDockingExtra(thirdOrderBO.getReagentOrderNo(), thirdOrderBO.getReagentOrderNo(), false, ex.getMessage());
                    }
                });
    }

    /**
     * 更新第三方对接订单状态
     * @param thirdPartyPlatformOrderBO  第三方平台的订单信息对象
     * @return
     */
    @ServiceLog(description = "更新第三方对接订单状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean updateOrderStatus(ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO) {
        OrderStatusUpdateDTO orderStatusUpdate = new OrderStatusUpdateDTO();
        orderStatusUpdate.setAppKey(Environment.getAppKey());
        orderStatusUpdate.setOrderNo(thirdPartyPlatformOrderBO.getReagentOrderNo());
        orderStatusUpdate.setExtraOrderNo(thirdPartyPlatformOrderBO.getExtraOrderNo());
        orderStatusUpdate.setReason(thirdPartyPlatformOrderBO.getRemark());
        orderStatusUpdate.setStatus(thirdPartyPlatformOrderBO.getStatus());

        OrgRequest<OrderStatusUpdateDTO> request = new OrgRequest<>();
        request.setData(orderStatusUpdate);
        request.setOrgCode(thirdPartyPlatformOrderBO.getOrgCode());
        return tpiOrderClient.updateOrderStatus(request);
    }

    private OrderApprovalEnum setThirdPartyPlatformOrderStrategy(Integer updatedStatus, ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO, OrderMasterDO orderInfo) {
        // 需要同步到第三方系统时，approvalType必须要赋值返回
        OrderApprovalEnum approvalType = null;
        Integer orderId = orderInfo.getId();
        String orgCode = orderInfo.getFusercode();
        Integer currentStatus = orderInfo.getStatus();

        if (OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(orgCode)) {
            // 旧单不需要做推送
            if (OrderDateConstant.isOldOrderForNormal(orgCode, orderInfo.getForderdate())) {
                return null;
            }
            switch (updatedStatus) {
                case DockingConstant.WAITING_FOR_RECEIVE:
                    // 供应商确认发货
                    thirdPartyPlatformOrderBO.setStatus(DockingConstant.WAITING_FOR_RECEIVE);
                    thirdPartyPlatformOrderBO.setRemark("供应商已发货");
                    approvalType = saveApproveLogForThirdPlatformSuccess(NOTICE_JILI_DISPATCH, orderId);
                    break;
                case DockingConstant.CLOSE:
                    // 广西肿瘤取消订单通知管理平台
                    thirdPartyPlatformOrderBO.setRemark("采购人取消订单");
                    thirdPartyPlatformOrderBO.setStatus(DockingConstant.CLOSE);
                    if (OrderStatusEnum.SupplierApplyToCancel.getValue().equals(currentStatus)) {
                        thirdPartyPlatformOrderBO.setRemark("采购人同意取消订单");
                    }
                    approvalType = saveApproveLogForThirdPlatformSuccess(CANCEL, orderId);
                    break;
            }
        }
        return approvalType;
    }

    /**
     * 通知基里推送成功，记录操作日志
     *
     * @param approvalEnum 推送状态
     */
    private OrderApprovalEnum saveApproveLogForThirdPlatformSuccess(OrderApprovalEnum approvalEnum, Integer orderId) {
        saveApproveLogForThirdPlatform(approvalEnum, orderId, "订单状态同步");
        return approvalEnum;
    }

    private void saveApproveLogForThirdPlatform(OrderApprovalEnum approvalEnum, Integer orderId, String reason) {
        OrderApprovalLogDTO param = new OrderApprovalLogDTO();
        param.setOrderId(orderId);
        param.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
        param.setOperatorName(DockingConstant.SYSTEM_OPERATOR_NAME);
        param.setReason(reason);
        param.setApproveStatus(approvalEnum.getValue());
        orderApprovalLogService.asyncInsertOrderApprovalLog(param);
    }

    /**
     * 记录第三方平台对接日志
     * @param orderInfo
     * @param thirdPartyPlatformOrderBO
     */
    private void logDockingStrategy(OrderMasterDO orderInfo, ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO) {
        Integer orderStatus = thirdPartyPlatformOrderBO.getStatus();
        // 记录财务操作日志
        orderOtherLogClient.createOrderDockingLog(orderInfo.getForderno(), orderInfo.getFusercode(), null, "status: " + orderStatus, thirdPartyPlatformOrderBO.getRemark(), null);
    }
}
