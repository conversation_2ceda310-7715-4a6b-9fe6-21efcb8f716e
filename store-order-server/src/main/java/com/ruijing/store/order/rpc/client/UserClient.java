package com.ruijing.store.order.rpc.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.oms.api.dto.OmsAccessDTO;
import com.ruijing.store.oms.api.dto.SysUserDTO;
import com.ruijing.store.oms.api.enums.UserOrgModuleEnum;
import com.ruijing.store.oms.api.service.SysUserRpcService;
import com.ruijing.store.oms.api.service.UserOrgModuleRpcService;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.supp.include.api.dto.OrgSuppSynStatusDataDTO;
import com.ruijing.store.supp.include.api.service.OrgIncludeSuppRpcService;
import com.ruijing.store.user.api.constant.access.HmsAccessConstant;
import com.ruijing.store.user.api.dto.*;
import com.ruijing.store.user.api.dto.department.DepartmentRoleAccessDTO;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.user.api.dto.role.UserInDepartmentRoleDTO;
import com.ruijing.store.user.api.dto.user.OrgUserDTO;
import com.ruijing.store.user.api.dto.user.UserQueryDTO;
import com.ruijing.store.user.api.enums.user.AccountTypeEnum;
import com.ruijing.store.user.api.response.PageRemoteResponse;
import com.ruijing.store.user.api.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: zhukai
 * @date : 2019/12/6 10:54 上午
 * @description: 用户中心RPC调用
 */
@ServiceClient
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class UserClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 用户是否有统计权限缓存
     */
    private Cache<String, Boolean> orgIdUserIdHasAccessStatCache = CacheBuilder.newBuilder().expireAfterWrite(15, TimeUnit.MINUTES).build();

    private static final String CAT_TYPE = "UserClient";

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserInfoService userInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserDepartmentInfoService userDepartmentInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserAccessInfoService userAccessInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserAccountBusinessService userAccountBusinessService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private OrganizationRpcService organizationRpcService;

    @MSharpReference(remoteAppkey = "store-supp-include-service")
    private OrgIncludeSuppRpcService orgIncludeSuppRpcService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserInfoRpcService userInfoRpcService;

    @MSharpReference(remoteAppkey="store-user-service")
    private DepartmentRpcService departmentRpcService;

    @MSharpReference(remoteAppkey="store-user-service")
    private UserDepartmentRoleRpcService userDepartmentRoleRpcService;

    @MSharpReference(remoteAppkey = "store-oms-web")
    private SysUserRpcService sysUserRpcService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserRoleInfoService userRoleInfoService;

    @MSharpReference(remoteAppkey = "store-oms-web")
    private UserOrgModuleRpcService userOrgModuleRpcService;

    @Resource
    private CacheClient cacheClient;

    @MSharpReference(remoteAppkey = "store-user-service")
    private BuyerBussinessRpcService buyerBussinessRpcService;

    /**
     * 获取注册到医院的供应商code
     * @param orgId   单位id
     * @param suppId  供应商id
     * @return
     * @throws CallRpcException
     */
    @ServiceLog(description = "根据Id获取组织", serviceType = ServiceType.COMMON_SERVICE)
    public OrgSuppSynStatusDataDTO findIncludeSuppCodeAndSynOrg(Integer orgId,Integer suppId) throws CallRpcException {
        RemoteResponse<OrgSuppSynStatusDataDTO> response = orgIncludeSuppRpcService.findIncludeSuppCodeAndSynOrg(orgId,suppId);
        Assert.isTrue(response.isSuccess(), "根据Id获取组织异常: " + response.getMsg());
        return response.getData();
    }

    /**
     * 根据Id获取组织
     *
     * @return List<OrganizationDTO>
     * @throws CallRpcException
     */
    @ServiceLog(description = "根据Id获取组织", serviceType = ServiceType.COMMON_SERVICE)
    public OrganizationDTO getOrgById(Integer orgId) throws CallRpcException {
        RemoteResponse<List<OrganizationDTO>> response = organizationRpcService.getByIds(Arrays.asList(orgId));
        Assert.isTrue(response.isSuccess(), "根据Id获取组织异常: " + response.getMsg());
        return CollectionUtils.isNotEmpty(response.getData()) ? response.getData().get(0) : new OrganizationDTO();
    }

    /**
     * 根据orgId获取orgCode
     *  先从枚举中获取，如果获取不到再去rpc获取
     * @param orgId
     * @return
     */
    @ServiceLog(description = "根据Id获取orgCode", serviceType = ServiceType.COMMON_SERVICE)
    public String getOrgCodeById(Integer orgId){
        String orgCode = null;
        try {
            orgCode = OrgEnum.getOrgEnumById(orgId).getCode();
        } catch (Exception e) {
            OrganizationDTO organizationDTO = this.getOrgById(orgId);
            Preconditions.notNull(organizationDTO, "根据orgId:" + orgId + "获取机构信息为空！");
            orgCode = organizationDTO.getCode();
        }
        return orgCode;
    }

    /**
     * 通过枚举来查找简单的单位信息（id、code、name），如果没找到的话会通过findById走RPC接口查询
     * @param id 单位id
     * @return 单位简单信息（id、code、name）
     */
    public OrganizationDTO findSimpleOrgInfoByIdFromEnum(Integer id) {
        OrganizationDTO organization;
        try {
            OrgEnum org = OrgEnum.getOrgEnumById(id);
            organization = new OrganizationDTO();
            organization.setName(org.getName());
            organization.setCode(org.getCode());
            organization.setId(org.getValue());
        } catch (Exception e) {
            logger.error("Org enum cannot find {}", id);
            organization = getOrgById(id);
        }
        return organization;
    }

    /**
     * 获取所有的组织
     *
     * @return List<OrganizationDTO>
     * @throws CallRpcException
     */
    @ServiceLog(description = "获取所有的组织", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrganizationDTO> getAllOrg() throws CallRpcException {
        RemoteResponse<List<OrganizationDTO>> response = organizationRpcService.getALLOrg();
        Assert.isTrue(response.isSuccess(), "获取所有的组织异常: " + response.getMsg());
        return response.getData();
    }

    /**
     * 判断用户是否具有传入的权限
     * @param orgId
     * @param userId
     * @param deptId
     * @param accessCode
     * @return
     */
    public Boolean findUserHasAccess(@NotNull Integer orgId, @NotNull Integer userId, Integer deptId, @NotNull String accessCode){
        RemoteResponse<Boolean> remoteResponse = userDepartmentRoleRpcService.findUserHasAccess(orgId, userId, deptId, accessCode);
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据 userid、departmentid、accessCode 校验用户是否有权限异常: " + JsonUtils.toJsonIgnoreNull(remoteResponse));
        return remoteResponse.getData();
    }

    /**
     * 根据用户id 和组织id 查询用户信息
     *
     * @param userId 用户ID
     * @param orgId  组织id
     * @throws CallRpcException rpc调用异常
     */
    public UserBaseInfoDTO getUserInfo(Integer userId, Integer orgId) throws CallRpcException {
        final String methodName = "getUserInfo";
        logger.info( "进入==》{}，用户id{}，组织Id{}",methodName, userId,orgId);
        List<UserBaseInfoDTO> userInfoList = getUserByIdsAndOrgId(New.list(userId), orgId);
        logger.info("结束==》{},结果{}",methodName,JsonUtils.toJson(userInfoList));
        return CollectionUtils.isNotEmpty(userInfoList) ? userInfoList.get(0) : new UserBaseInfoDTO();
    }

    /**
     * 根据用户id 和组织id 查询用户信息, 单次查询最多支持250个id
     * @param userIdList 用户ID
     * @param  orgId 组织id
     * @throws CallRpcException rpc调用异常
     */
    @ServiceLog(description = "根据用户id 和组织id 查询用户信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<UserBaseInfoDTO> getUserByIdsAndOrgId(List<Integer> userIdList, Integer orgId) throws CallRpcException {
        List<UserBaseInfoDTO> result = new ArrayList<>(userIdList.size());
        List<List<Integer>> partition = Lists.partition(userIdList, 250);
        for (List<Integer> userIdPartition : partition) {
            RemoteResponse<List<UserBaseInfoDTO>> remoteResponse = userInfoService.getUserByIdsAndOrgId(new ArrayList<>(userIdPartition), orgId);
            Preconditions.isTrue(remoteResponse.isSuccess(), "根据用户id 和组织id 查询用户信息异常:" + JsonUtils.toJsonIgnoreNull(remoteResponse));
            result.addAll(remoteResponse.getData());
        }

        return result;
    }

    /**
     * 根据部门id 获取部门信息
     * @param departmentId 组织id
     * @return DepartmentDTO
     * @throws CallRpcException
     */
    @ServiceLog(description = "根据部门id 获取部门信息", serviceType = ServiceType.COMMON_SERVICE)
    public DepartmentDTO getDepartmentInfo(Integer departmentId) throws CallRpcException {
        RemoteResponse<List<DepartmentDTO>> remoteResponse = departmentRpcService.getDepartmentsByIds(New.singletonList(departmentId.longValue()));
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据部门id 获取部门信息异常：" + JsonUtils.toJsonIgnoreNull(remoteResponse));
        DepartmentDTO departmentDTO  = CollectionUtils.isNotEmpty(remoteResponse.getData()) ? remoteResponse.getData().get(0) : new DepartmentDTO();

        return departmentDTO;
    }

    /**
     * 通过部门获取所有指定角色的用户
     *
     * @param departmentId
     * @param orgId
     * @return
     */
    public List<UserBaseInfoDTO> getUserByPiAndDepartmentIdAndAccessCode(Integer departmentId, Integer orgId, String accessCode) {
        // 获取部门角色的入参
        DepartmentRoleAccessDTO param = new DepartmentRoleAccessDTO();
        param.setAccessCode(accessCode);
        param.setDepartmentId(departmentId);
        param.setOrgId(orgId);
        RemoteResponse<List<UserBaseInfoDTO>> remoteResponse = userAccountBusinessService.getUserByRoleAndDepartmentIdAndAccessCode(param);
        return remoteResponse.getData();
    }

    /**
     * 根据课题组id 查询用户信息
     * @param userInfoMap orgId -> List<UserId>
     * @return
     */
    public List<UserBaseInfoDTO> getUserInfoByUserIdList(Map<Integer, Set<Integer>> userInfoMap) {
        if (MapUtils.isEmpty(userInfoMap)) {
            return Collections.emptyList();
        }
        List<UserBaseInfoDTO> result = new ArrayList<>();
        for (Map.Entry<Integer, Set<Integer>> entry : userInfoMap.entrySet()) {
            try {
                List<UserBaseInfoDTO> userInfoList = getUserByIdsAndOrgId(entry.getValue(), entry.getKey());
                result.addAll(userInfoList);
            } catch (CallRpcException e) {
                CatUtils.buildStackInfo("根据医院id批量用户信息异常，RPC服务异常", e);
                logger.error("根据医院id批量用户信息异常，RPC服务异常{}", e);
                throw new IllegalStateException("根据医院id批量用户信息异常，RPC服务异常");
            }
        }

        return result;
    }

    /**
     * 根据 userId 和 orgId 查询用户信息
     * @param userIdSet
     * @param orgId
     * @return
     * @throws CallRpcException
     */
    @ServiceLog(description = "根据 userId 和 orgId 查询用户信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<UserBaseInfoDTO> getUserByIdsAndOrgId(Set<Integer> userIdSet, Integer orgId) throws CallRpcException {
        if (CollectionUtils.isEmpty(userIdSet) || orgId == null) {
            return Collections.emptyList();
        }

        List<UserBaseInfoDTO> result = new ArrayList<>(userIdSet.size());
        List<Integer> userIdList = new ArrayList<>(userIdSet);
        List<List<Integer>> partition = Lists.partition(userIdList, 250);
        for (List<Integer> idItem : partition) {
            RemoteResponse<List<UserBaseInfoDTO>> response = userInfoService.getUserByIdsAndOrgId(new ArrayList<>(idItem), orgId);
            Preconditions.isTrue(response.isSuccess(), "根据 userId 和 orgId 查询用户信息异常: " + JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }

        return result;
    }

    /**
     * 根据部门id批量查询部门信息
     * @param ids
     * @return
     */
    @ServiceLog(description = "根据部门id批量查询部门信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<DepartmentDTO> getDepartmentListByIds(List<Integer> ids) throws CallRpcException {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<Long> collect = ids.stream().map(Integer::longValue).collect(Collectors.toList());
        RemoteResponse<List<DepartmentDTO>> response = departmentRpcService.getDepartmentsByIds(collect);
        Preconditions.isTrue(response.isSuccess(), "根据部门id批量查询部门信息异常：" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    /**
     * 根据用户guid及组织id获取基本信息
     * @param guid
     * @param orgId
     * @return
     */
    public UserBaseInfoDTO getUserInfoByGuidAndOrgid(String guid, Integer orgId) {
        RemoteResponse<UserBaseInfoDTO> response = userInfoService.getUserBaseInfoByGuidAndOrgid(guid, orgId);
        Preconditions.isTrue(response.isSuccess(), "根据用户guid及组织id获取基本信息失败！" + JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }


    @ServiceLog(description = "根据用户工号集合，医院code 查询用户信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<UserBaseInfoDTO> getUserByJobNumAndOrgId(List<String> jobNumberList, Integer orgId) {
        if(CollectionUtils.isEmpty(jobNumberList) || Objects.isNull(orgId)){
            return New.list();
        }
        RemoteResponse<List<UserBaseInfoDTO>> response = null;
        try {
            response = userInfoService.getUserByJobNumAndOrgId(jobNumberList,  orgId);
        } catch (Exception e) {
            throw new RuntimeException("没有匹配的单位id,orgId:"+orgId);
        }
        Preconditions.isTrue(response.isSuccess(), "根据 jobNumberList 和 orgId 查询用户信息异常: " + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }


    /**
     * 用户确认收货是否需要走报备页面
     * @param orgId
     * @param deptId
     * @param userId
     * @return
     */
    public Boolean getUserReportStatus(Integer orgId, Integer deptId, Integer userId) {
        RemoteResponse<Boolean> response = userInfoRpcService.getUserReportStatus(orgId, deptId, userId);
        Preconditions.isTrue(response.isSuccess(), "用户确认收货是否需要报备状态获取失败！" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData() != null ? response.getData() : false;
    }

    @ServiceLog(description = "通过guid获取用户基础信息", serviceType = ServiceType.COMMON_SERVICE)
    public UserBaseInfoDTO getUserBaseInfoByGuid(String userGuid) {
        RemoteResponse<UserBaseInfoDTO> response = userInfoService.getUserBaseInfoByGuidAndOrgid(userGuid,null);
        return response.getData();
    }

    @ServiceLog(description = "通过userid和orgid获取用户所在部门列表", serviceType = ServiceType.COMMON_SERVICE)
    public List<DepartmentDTO> getDepartmentsForUser(Integer userId, Integer orgId) {
        Long userIdLong = Long.valueOf(String.valueOf(userId));
        Long orgIdLong = Long.valueOf(String.valueOf(orgId));
        RemoteResponse<List<DepartmentDTO>> response = departmentRpcService.getDepartmentsForUser(userIdLong,orgIdLong);
        return response.getData();
    }

    @ServiceLog(description = "通过userid，orgid，权限code获取用户对应权限所在部门",serviceType = ServiceType.COMMON_SERVICE)
    public List<DepartmentDTO> getDeptForUserByAccess(Integer userId,Integer orgId,String accessCode) {
        RemoteResponse<List<DepartmentDTO>> response = userDepartmentRoleRpcService.findUserHasAccessDepartment(orgId,userId,accessCode);
        Preconditions.notNull(response, "获取用户对应权限所在部门调用rpc失败");
        Preconditions.isTrue(response.isSuccess(),"获取用户对应权限所在部门调用rpc失败。"+response.getMsg());
        return response.getData() == null ? New.list() : response.getData();
    }

    @ServiceLog(description = "通过userid,orgId,权限code批量获取用户权限所在部门", serviceType = ServiceType.RPC_CLIENT)
    public Map<String, List<DepartmentDTO>> findUserHasAccessDepartmentBatch(Integer userId, Integer orgId, List<String> accessCodeList){
        RemoteResponse<Map<String, List<DepartmentDTO>>> response = userDepartmentRoleRpcService.findUserHasAccessDepartmentBatch(orgId, userId, accessCodeList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * 获取当前登录用户个人及单位信息
     * @param currentUserGuid   当前登录用户的guid
     * @param orgId             当前登录用户的orgId
     * @return                  登录用户信息
     */
    public LoginUserInfoBO getLoginUserInfo(String currentUserGuid, Integer orgId) {
        return this.getLoginUserInfo(currentUserGuid, orgId, true, null);
    }

    /**
     * @description: 获取当前登录用户个人及单位信息(采购侧）
     * @date: 2021/1/20 18:45
     * @author: zengyanru
     * @param currentUserGuid
     * @param orgId
     * @param requireDeptList
     * @param accessCode
     * @return com.ruijing.store.order.base.core.bo.LoginUserInfoBO
     */
    @ServiceLog(description = "获取当前登录用户个人及单位信息", serviceType = ServiceType.COMMON_SERVICE)
    public LoginUserInfoBO getLoginUserInfo(String currentUserGuid, Integer orgId, Boolean requireDeptList, String accessCode) {
        String cacheUniqKey = String.join("_", New.list(currentUserGuid, orgId.toString(), requireDeptList == null ? "null" : requireDeptList.toString(), accessCode));
        // 先判断缓存是否存在与可用
        LoginUserInfoBO loginInfoFromCache = (LoginUserInfoBO) cacheClient.getFromCache(cacheUniqKey);
        if (loginInfoFromCache != null) {
            return loginInfoFromCache;
        }

        // 获取当前登录用户的部门，单位，个人等信息
        BusinessErrUtil.notNull(currentUserGuid, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        UserBaseInfoDTO userBaseInfo = this.getUserBaseInfoByGuid(currentUserGuid);
        Preconditions.notNull(userBaseInfo, "当前用户不存在采购中心，请重新登录或使用正确用户");
        Preconditions.isTrue(userBaseInfo.getActivate(), "此用户已被禁用，暂时无法进行相关操作。可联系相关负责人进行账户激活");
        Integer userId = userBaseInfo.getId();
        OrganizationDTO orgInfo = this.getOrgById(orgId);

        // 是否需要部门信息
        List<Integer> deptIdList = null;
        List<DepartmentDTO> deptList = New.list();
        if (Boolean.TRUE.equals(requireDeptList)) {
            if (accessCode != null) {
                // 获取对应权限的部门列表
                deptList = this.getDeptForUserByAccess(userId, orgId, accessCode);
                deptIdList = deptList.stream().map(DepartmentDTO::getId).collect(Collectors.toList());
            } else {
                // 无视权限获取部门列表
                deptList = this.getDepartmentsForUser(userId, orgId);
                deptIdList = deptList.stream().map(DepartmentDTO::getId).collect(Collectors.toList());
            }
        }

        LoginUserInfoBO loginUserInfo = new LoginUserInfoBO();
        loginUserInfo.setDeptIdList(deptIdList);
        loginUserInfo.setOrgId(orgId);
        loginUserInfo.setRootDepartmentId(orgInfo.getRootDepartmentId());
        loginUserInfo.setUserId(userId);
        loginUserInfo.setMobile(userBaseInfo.getMobile());
        loginUserInfo.setEmail(userBaseInfo.getEmail());
        loginUserInfo.setOrgCode(orgInfo.getCode());
        loginUserInfo.setDeptList(deptList);
        loginUserInfo.setJobNumber(userBaseInfo.getJobnumber());
        loginUserInfo.setUserName(userBaseInfo.getName());
        loginUserInfo.setOrgName(orgInfo.getName());
        loginUserInfo.setUserGuid(currentUserGuid);

        // 放入缓存
        cacheClient.putToCache(cacheUniqKey, loginUserInfo, 600);
        return loginUserInfo;
    }

    /**
     * 根据当前会话信息获取登录用户的完整信息
     * @param rjSessionInfo 会话信息
     * @return              登录用户信息
     */
    public LoginUserInfoBO getLoginUserInfoBySessionInfo(RjSessionInfo rjSessionInfo) {
        return this.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
    }

    /**
     * 根据用户id及课题组id息返回权限信息
     * @param deptIdList
     * @param userId
     * @param orgId
     * @return 用户在传入部门中具有的权限并集
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户权限信息")
    public List<AccessDTO> getDepartmentAccess(List<Integer> deptIdList, Integer userId, Integer orgId) {
        RemoteResponse<List<AccessDTO>> resultResponse = userDepartmentRoleRpcService.findUserAccessByUserIdAndDeptIds(orgId, deptIdList, userId);
        Assert.isTrue(resultResponse.isSuccess(), resultResponse.getMsg());
        return resultResponse.getData();
    }

    /**
     * 根据用户id及课题组id息返回权限信息, 支持获取父部门的权限
     * @param deptId
     * @param userId
     * @param orgId
     * @return 用户在传入部门中具有的权限并集
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户权限信息")
    public List<AccessDTO> getDepartmentAccess(Integer deptId, Integer userId, Integer orgId) {
        RemoteResponse<List<AccessDTO>> resultResponse = userDepartmentRoleRpcService.findUserAccessInOrgDepartment(orgId, deptId, userId);
        Assert.isTrue(resultResponse.isSuccess(), resultResponse.getMsg());
        return resultResponse.getData();
    }

    /**
     * 查询用户在课题组中是否有权限之一
     * @param userId
     * @param departmentIds
     * @param accessList 权限code列表
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户在课题组中是否有权限之一")
    public boolean checkUserInDepartmentAccess(Integer userId, List<Integer> departmentIds, List<String> accessList, Integer orgId) {
        List<AccessDTO> accessDTOList = this.getDepartmentAccess(departmentIds, userId, orgId);
        if (CollectionUtils.isNotEmpty(accessDTOList)) {
            List<String> accessCodeList = accessDTOList.stream().map(AccessDTO::getCode).collect(Collectors.toList());
            for (String access : accessList) {
                if (accessCodeList.contains(access)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 查询用户在课题组中是否有权限之一
     * @param userId
     * @param departmentId
     * @param accessList 权限code列表
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户在课题组中是否有权限之一")
    public boolean checkUserInDepartmentAccess(Integer userId, Integer departmentId, List<String> accessList, Integer orgId) {
        List<AccessDTO> accessDTOList = this.getDepartmentAccess(departmentId, userId, orgId);
        if (CollectionUtils.isNotEmpty(accessDTOList)) {
            List<String> accessCodeList = accessDTOList.stream().map(AccessDTO::getCode).collect(Collectors.toList());
            for (String access : accessList) {
                if (accessCodeList.contains(access)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 通过角色id列表和单位id查询所有具有该条件的用户信息列表
     * @param roleIdList
     * @param orgId
     * @return 具有该条件的用户信息列表
     */
    @ServiceLog(description = "通过角色id列表和单位id查询所有具有该条件的用户信息列表")
    public List<UserBaseInfoDTO> findUserByRoleIdAndOrgId(List<Integer> roleIdList, Integer orgId) {
        Preconditions.notEmpty(roleIdList,"查询角色的入参角色id列表不可为空");
        Preconditions.notNull(orgId,"查询角色的入参单位id不可为空");
        RemoteResponse<List<UserBaseInfoDTO>> userInfoListResp = userInfoRpcService.findUserInfoByRoleIdsAndOrgId(roleIdList,orgId);
        Preconditions.notNull(userInfoListResp,"查询单位特定角色用户信息接口异常");
        return userInfoListResp.getData();
    }
    
    public UserBaseInfoDTO getNotNullUserBaseInfoByGuid(String userGuid) {
        UserBaseInfoDTO result = this.getUserBaseInfoByGuid(userGuid);
        Preconditions.notNull(result, "根据guid找不到当前用户信息：" + userGuid);
        return result;
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户详情")
    public UserBaseInfoDTO getUserDetailByID(Integer userId) {
        Preconditions.isTrue(userId != null, "参数不完整，查询用户信息失败");
        RemoteResponse<UserBaseInfoDTO> response = userInfoService.getUserDetailByID(userId);
        if (response.isSuccess()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "getUserDetailByID", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + userId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    public UserBaseInfoDTO getNotNullUserDetailById(Integer userId) {
        UserBaseInfoDTO purchaser = this.getUserDetailByID(userId);
        Preconditions.notNull(purchaser, "找不到用户，Id：" + userId);
        return purchaser;
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询用户详情")
    public List<UserBaseInfoDTO> getUserByName(String userName) {
        Preconditions.isTrue(StringUtils.isNotBlank(userName), "用户名不能为空");
        RemoteResponse<List<UserBaseInfoDTO>> response = userInfoService.findByOr(userName, null, null);
        if (response.isSuccess()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "getUserByName", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + userName + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    public List<UserBaseInfoDTO> getUserByNameAndOrgId(String userName, Integer orgId) {
        Preconditions.notNull(orgId, "机构Id不能为空");
        List<UserBaseInfoDTO> userBaseInfoDTOList = this.getUserByName(userName);
        if (CollectionUtils.isEmpty(userBaseInfoDTOList)) {
            return null;
        }
        List<UserBaseInfoDTO> resultToReturn = new ArrayList<>();
        for (UserBaseInfoDTO toVisit : userBaseInfoDTOList) {
            if (orgId.equals(toVisit.getOrganizationId())) {
                resultToReturn.add(toVisit);
            }
        }
        return resultToReturn;
    }

    /**
     * 查询用户的所有部门
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<DepartmentDTO> getDepartmentsByUserId(Integer userId){
        Preconditions.notNull(userId, "用户id不能为空");
        String methodName = "getDepartmentsByUserId";
        RemoteResponse<List<DepartmentDTO>> response = userDepartmentInfoService.getDeparmentIdWithUserRole(userId.longValue());
        if (response.isSuccess()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, methodName, "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + userId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMsg());
    }

    @ServiceLog(description = "根据用户id查询用户信息", serviceType = ServiceType.RPC_CLIENT)
    public List<UserBaseInfoDTO> getUserByUserIds(List<Integer> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        RemoteResponse<List<UserBaseInfoDTO>> response = userInfoService.getUserByUserIds(userIdList);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    /**
     * 通过orgid， deptid， accesscode返回具有权限的人信息列表
     * @param orgId
     * @param deptId
     * @param accessCode
     * @return
     */
    public List<UserBaseInfoDTO> findHasAccessInDepartmentUser(Integer orgId, Integer deptId, String accessCode) {
        RemoteResponse<List<UserBaseInfoDTO>> response = userDepartmentRoleRpcService.findHasAccessInDepartmentUser(orgId, deptId, accessCode);
        Preconditions.isTrue(response != null && response.isSuccess(),
                "查询课题组有权限用户失败，单位id，部门id，权限码：" + orgId + ", " + deptId + ", " + accessCode);
        return response.getData();
    }

    @ServiceLog(description = "查找用户有角色的部门、不继承", serviceType = ServiceType.RPC_CLIENT)
    public List<DepartmentDTO> findUserHasRoleDepartment(Integer orgId, Integer userId, Set<Integer> roles) {
        final RemoteResponse<List<DepartmentDTO>> response = userDepartmentRoleRpcService.findUserHasRoleDepartment(orgId, userId, roles);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    /**
     * 获取所有子部门（以及子部门的子部门，层级的）
     * @param orgId
     * @param deptId
     * @return
     */
    public List<MiniDepartmentDTO> getAllChildTree(Integer orgId, Integer deptId) {
        RemoteResponse<List<MiniDepartmentDTO>> res = departmentRpcService.getAllChild(orgId, deptId);
        Preconditions.isTrue(res != null && res.isSuccess(), "获取所有子部门接口异常");
        return res.getData();
    }

    /**
     * 查询oms用户
     * @param userId
     * @return
     */
    @ServiceLog(description = "查询oms用户", serviceType = ServiceType.COMMON_SERVICE)
    public LoginUserInfoBO getOMSLoginUserInfo(Integer userId) {
        RemoteResponse<SysUserDTO> response = sysUserRpcService.findById(userId);
        String probErrMsg = response == null ? "" : response.getMsg();
        Preconditions.isTrue(response != null && response.isSuccess(), "查询oms用户失败, msg = " + probErrMsg);
        SysUserDTO omsUserInfo = response.getData();
        LoginUserInfoBO loginUserInfo = new LoginUserInfoBO();
        if (omsUserInfo == null) {
            return loginUserInfo;
        }
        loginUserInfo.setUserId(omsUserInfo.getId());
        loginUserInfo.setUserGuid(omsUserInfo.getGuid());
        loginUserInfo.setUserName(StringUtils.isBlank(omsUserInfo.getRealName()) ? omsUserInfo.getName() : omsUserInfo.getRealName());
        loginUserInfo.setOrgName("");
        loginUserInfo.setOrgId(-1);
        loginUserInfo.setOrgCode("");
        return loginUserInfo;
    }

    /**
     * 查询oms用户权限列表
     *
     * @param omsGuid
     */
    @ServiceLog(description = "查询oms用户权限列表", serviceType = ServiceType.COMMON_SERVICE)
    public List<OmsAccessDTO> findAccessByGuid(String omsGuid) {
        Preconditions.notNull(omsGuid, "查询oms用户权限入参omsGuid不可为空");
        // 查询主流程
        RemoteResponse<List<OmsAccessDTO>> response = sysUserRpcService.findAccessByGuid(omsGuid);
        Preconditions.isTrue(response != null && response.isSuccess(), "查询oms用户权限列表出错");
        return response.getData();
    }

    @ServiceLog(description = "递归查找部门及父级部门拥有该权限的用户", serviceType = ServiceType.RPC_CLIENT)
    public List<UserBaseInfoDTO> findUserHasRoleInThisAndAncestorDepartment(Integer orgId, Integer deptId, List<Integer> roleIds) {
        RemoteResponse<List<UserBaseInfoDTO>> response = userDepartmentRoleRpcService.findHasRoleInDeptUser(orgId,deptId,roleIds);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
        return response.getData();
    }

    /**
     * 根据部门id查询所有上级部门
     * @param deptId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询所有上级部门")
    public List<MiniDepartmentDTO> findParentsOrderToRoot(Integer deptId) {
        RemoteResponse<List<MiniDepartmentDTO>> remoteResponse = departmentRpcService.findParentsOrderToRoot(deptId);
        Assert.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 根据用户id及部门id返回角色信息
     * @param userId
     * @param departmentIds
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询角色信息")
    public  List<RoleDTO> getByUserIdAndDepartmentIds(Integer userId, List<Integer> departmentIds) {
        RemoteResponse<List<RoleDTO>> remoteResponse = userRoleInfoService.getByUserIdAndDepartmentIds(userId, departmentIds);
        Assert.isTrue(remoteResponse.isSuccess(), "查询角色信息失败");
        return remoteResponse.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<Integer> listOrgIdsByGuidAndModule(String guid, UserOrgModuleEnum userOrgModule){
        RemoteResponse<List<Integer>> response = userOrgModuleRpcService.listOrgIdsByGuidAndModule(guid, userOrgModule);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<SysUserDTO> findByIds(List<Integer> userIdList){
        RemoteResponse<List<SysUserDTO>> response = sysUserRpcService.findByIds(userIdList);
        Preconditions.isTrue(response.isSuccess(), "查询用户信息失败");
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据用户id+单位id查询用户所在部门角色集合")
    public List<UserInDepartmentRoleDTO> getDepartmentRoleByUserIdAndOrgId(Integer userId, Integer orgId){
        RemoteResponse<List<UserInDepartmentRoleDTO>> response = userDepartmentRoleRpcService.getDepartmentRoleByUserIdAndOrgId(userId, orgId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * 获取测试用户
     * @param orgId 单位id
     * @return 测试用户id
     */
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT)
    public List<Integer> queryTestUserByOrgId(Integer orgId){
        if(orgId == null){
            return New.emptyList();
        }
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setOrgId(orgId);
        userQueryDTO.setAccountType(AccountTypeEnum.TEST.getVal());
        userQueryDTO.setPageNo(1);
        // 一个单位一般不可能有200个测试账户，不做循环获取
        userQueryDTO.setPageSize(200);
        PageRemoteResponse<List<OrgUserDTO>> pageRemoteResponse = userInfoRpcService.getUserInfos(userQueryDTO);
        Preconditions.isTrue(pageRemoteResponse.isSuccess(), pageRemoteResponse.getMsg());
        if(CollectionUtils.isEmpty(pageRemoteResponse.getData())){
            return New.emptyList();
        }
        return pageRemoteResponse.getData().stream().map(OrgUserDTO::getId).collect(Collectors.toList());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "判断用户是否统计权限")
    public boolean getHaveAccessViewStat(Integer orgId, Integer userId){
        String cacheKey = orgId + "_" + userId;
        Boolean hasAccess = orgIdUserIdHasAccessStatCache.getIfPresent(cacheKey);
        if(hasAccess != null){
            return hasAccess;
        }
        Map<String, List<DepartmentDTO>> map = this.findUserHasAccessDepartmentBatch(userId, orgId, New.list(HmsAccessConstant.ORG_STAT_VIEW));
        hasAccess = map.values().stream().anyMatch(CollectionUtils::isNotEmpty);
        orgIdUserIdHasAccessStatCache.put(cacheKey, hasAccess);
        return hasAccess;
    }

    /**
     * 获取是否用户订阅了商家(批量）
     * @param userId 用户id
     * @param suppIds 商家
     * @return 是否订阅（如无数据则map里面没有对应的数据，请使用getOrDefault)
     */
    @ServiceLog(description = "获取是否用户订阅了商家(批量）", serviceType = ServiceType.RPC_CLIENT)
    public Map<Integer, Boolean> isUserSubscribeSupp(Integer userId, List<Integer> suppIds){
        RemoteResponse<List<UserInterestSupplierDTO>> response = buyerBussinessRpcService.findByUserAndSuppliers(userId, suppIds);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        if(CollectionUtils.isEmpty(response.getData())){
            return New.emptyMap();
        }
        return DictionaryUtils.toMap(response.getData(), UserInterestSupplierDTO::getSupplierId, item->Boolean.TRUE.equals(item.getInterest()));
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "获取课题组的负责人信息")
    public UserBaseInfoDTO getDepartmentManager(Integer orgId, Integer departmentId) {
        List<DepartmentDTO> departmentDTOList = listDepartmentInfo(orgId, New.list(departmentId));
        Preconditions.notEmpty(departmentDTOList, "部门信息不存在");
        UserBaseInfoDTO manager = null;
        Integer managerId = departmentDTOList.get(0).getManagerId();
        if (Objects.nonNull(managerId)) {
            List<UserBaseInfoDTO> managerList = getUserByIdsAndOrgId(New.list(managerId), orgId);
            if (CollectionUtils.isNotEmpty(managerList)) {
                manager = managerList.get(0);
            }
        }
        return manager;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "获取部门详细集合")
    public List<DepartmentDTO> listDepartmentInfo(Integer orgId, List<Integer> deptIds) {
        RemoteResponse<List<DepartmentDTO>> response = departmentRpcService.getOrgDepartment(orgId, deptIds);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }


}
