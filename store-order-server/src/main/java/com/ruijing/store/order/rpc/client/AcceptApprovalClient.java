package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.accept.approve.dto.AcceptApproveInfoDTO;
import com.ruijing.order.saturn.api.accept.approve.dto.RefAcceptApproveFlowLevelAuthUserDTO;
import com.ruijing.order.saturn.api.accept.approve.request.AcceptApproveInfoQueryDTO;
import com.ruijing.order.saturn.api.accept.approve.request.CheckApproveAuthParam;
import com.ruijing.order.saturn.api.accept.approve.request.SubmitAcceptApproveRequestDTO;
import com.ruijing.order.saturn.api.accept.approve.request.TimeOutAcceptApproveRequestDTO;
import com.ruijing.order.saturn.api.accept.approve.service.AcceptApproveRpcService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 10:35
 * @description
 */
@ServiceClient
public class AcceptApprovalClient {

    @MSharpReference(remoteAppkey = "order-saturn-service")
    private AcceptApproveRpcService acceptApproveRpcService;

    @ServiceLog(operationType = OperationType.READ,description = "获取验收审批信息",serviceType = ServiceType.RPC_CLIENT)
    public List<AcceptApproveInfoDTO> getAcceptApproveInfo(List<Integer> orderIdList){
        AcceptApproveInfoQueryDTO acceptApproveInfoQueryDTO = new AcceptApproveInfoQueryDTO();
        acceptApproveInfoQueryDTO.setOrderIdList(orderIdList);
        RemoteResponse<List<AcceptApproveInfoDTO>> response = acceptApproveRpcService.getAcceptApproveInfo(acceptApproveInfoQueryDTO);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
        return response.getData();
    }

    @ServiceLog(operationType = OperationType.WRITE,description = "验收审批，提交到验收审批处理",serviceType = ServiceType.RPC_CLIENT)
    public void submitToApproval(SubmitAcceptApproveRequestDTO submitAcceptApproveRequestDTO){
        RemoteResponse<Boolean> response = acceptApproveRpcService.submitToApproval(submitAcceptApproveRequestDTO);
        Preconditions.isTrue(response.isSuccess(),response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE, description = "提交超时自动验收审批",serviceType = ServiceType.RPC_CLIENT)
    public void submitDelayApproval(String orgCode, Integer orderId, Integer operatorId, String operatorName){
        TimeOutAcceptApproveRequestDTO timeOutAcceptApproveRequestDTO = new TimeOutAcceptApproveRequestDTO();
        timeOutAcceptApproveRequestDTO.setOrderId(orderId);
        timeOutAcceptApproveRequestDTO.setUserId(operatorId);
        timeOutAcceptApproveRequestDTO.setUserName(operatorName);
        timeOutAcceptApproveRequestDTO.setOrgCode(orgCode);
        RemoteResponse<Boolean> response = acceptApproveRpcService.submitDelayApproval(timeOutAcceptApproveRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "获取可以审批的订单",serviceType = ServiceType.RPC_CLIENT)
    public List<Integer> filterOrderUserHasAuthApprove(Integer orgId, Integer userId, List<Integer> orderIdList){
        CheckApproveAuthParam checkApproveAuthParam = new CheckApproveAuthParam()
                .setOrgId(orgId)
                .setUserId(userId)
                .setOrderIdList(orderIdList);
        RemoteResponse<List<Integer>> response = acceptApproveRpcService.filterOrderUserHasAuthApprove(checkApproveAuthParam);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "获取订单当前订单每一级可审批人",serviceType = ServiceType.RPC_CLIENT)
    public List<RefAcceptApproveFlowLevelAuthUserDTO> findOrderAllLevelApprovalAuthUser(Integer orgId, Integer orderId){
        CheckApproveAuthParam checkApproveAuthParam = new CheckApproveAuthParam()
                .setOrgId(orgId)
                .setOrderIdList(New.list(orderId));
        RemoteResponse<List<RefAcceptApproveFlowLevelAuthUserDTO>> response = acceptApproveRpcService.findOrderAllLevelApprovalAuthUser(checkApproveAuthParam);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(description = "通知完成了验收审批", serviceType = ServiceType.RPC_CLIENT)
    public void completeApproval(Integer orderId){
        RemoteResponse<Boolean> response = acceptApproveRpcService.completeApproval(orderId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
