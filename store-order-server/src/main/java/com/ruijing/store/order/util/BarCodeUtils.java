package com.ruijing.store.order.util;

import com.google.common.io.BaseEncoding;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 生成条形码工具类
 */
public class BarCodeUtils {
    private static final String FORMAT = "PNG";

    private BarCodeUtils() {
    }

    public static void encode(String contents, String dest, int width, int height, int offset) throws WriterException, IOException {
        contents = new String(contents.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        BitMatrix matrix = (new MultiFormatWriter()).encode(contents, BarcodeFormat.CODE_128, width - offset, height);
        MatrixToImageWriter.writeToStream(matrix, FORMAT, new FileOutputStream(new File(dest)));
    }

    public static byte[] encode(String contents, int width, int height, int offset) throws WriterException, IOException {
        contents = new String(contents.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        BitMatrix matrix = (new MultiFormatWriter()).encode(contents, BarcodeFormat.CODE_128, width - offset, height);
        MatrixToImageWriter.writeToStream(matrix, FORMAT, bos);
        bos.close();
        return bos.toByteArray();
    }

    public static String getBase64Img(String content) throws Exception {
        try {
            byte[] data = BarCodeUtils.encode(content, 180, 38, 10);
            return BaseEncoding.base64().encode(data);
        }catch (Throwable e){
            e.printStackTrace();
            throw new Exception(e.getMessage()+". This is an error but we throw exception to make it compatible with downstream codes.");
        }
    }
}
