package com.ruijing.store.order.business.bo.MyNotBalance;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/8 11:22
 * @Description
 **/
@RpcModel("请求体中经费卡信息")
public class FundCardInfoBO implements Serializable {

    private static final long serialVersionUID = -608986403970813935L;

    /**
     * 经费卡id（uuid）
     */
    @RpcModelProperty("经费卡id（uuid）")
    private String id;

    /**
     * 经费卡卡号
     */
    @RpcModelProperty("经费卡卡号")
    private String cardNo;

    /**
     * 经费卡名称
     */
    @RpcModelProperty("经费卡名称")
    private String cardName;

    /**
     * 经费卡类型的枚举值
     */
    @RpcModelProperty("经费卡类型的枚举值")
    private Integer fundType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("FundCardInfoBO{");
        sb.append("id='").append(id).append('\'');
        sb.append(", cardNo='").append(cardNo).append('\'');
        sb.append(", cardName='").append(cardName).append('\'');
        sb.append(", fundType=").append(fundType);
        sb.append('}');
        return sb.toString();
    }
}
