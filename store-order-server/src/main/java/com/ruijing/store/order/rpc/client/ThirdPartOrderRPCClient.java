package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.ThirdPartOrderRPCService;
import com.reagent.order.dto.request.*;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.event.OrderReturnEventEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;

import java.util.Collections;
import java.util.List;

/**
 * 第三方管理平台订单对接RPC接口
 */
@ServiceClient
public class ThirdPartOrderRPCClient {

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private ThirdPartOrderRPCService thirdPartOrderRPCService;

    public void pushOrderInfo(ThirdPartOrderMasterDTO orderMaster, OrderEventTypeEnum orderEventType, String operatorGuid, String operatorName) {
        pushOrderInfo(Collections.singletonList(orderMaster), orderEventType, operatorGuid, operatorName);
    }

    @ServiceLog(description = "推送订单信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean pushOrderInfo(List<ThirdPartOrderMasterDTO> orderMasterDTOList, OrderEventTypeEnum orderEventType, String operatorGuid, String operatorName) {
        Preconditions.notEmpty(orderMasterDTOList, "orderMaster list must not be null");

        ThirdPartBaseOrderRequestDTO requestDTO = new ThirdPartBaseOrderRequestDTO();
        requestDTO.setOrgCode(orderMasterDTOList.get(0).getOrgCode());
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setGuid(operatorGuid);
        operatorDTO.setName(operatorName);
        requestDTO.setOperator(operatorDTO);
        requestDTO.setOrderEventType(orderEventType);
        requestDTO.setOrderMasterList(orderMasterDTOList);
        if (orderMasterDTOList.size() == 1) {
            requestDTO.setOrderMaster(orderMasterDTOList.get(0));
        }

        RemoteResponse<Boolean> response = thirdPartOrderRPCService.pushOrderInfo(requestDTO);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "推送订单信息, 适用单个订单推送", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean pushSingleOrderInfo(ThirdPartOrderMasterDTO thirdPartOrder, OrderEventTypeEnum orderEventType, String operatorGuid, String operatorName) {
        Preconditions.notNull(thirdPartOrder, "orderMaster list must not be null");
        thirdPartOrder.setUpdatedStatus(orderEventType.getOrderStatus() != null ? orderEventType.getOrderStatus() : thirdPartOrder.getUpdatedStatus());

        ThirdPartBaseOrderRequestDTO requestDTO = new ThirdPartBaseOrderRequestDTO();
        requestDTO.setOrgCode(thirdPartOrder.getOrgCode());
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setGuid(operatorGuid);
        operatorDTO.setName(operatorName);
        requestDTO.setOperator(operatorDTO);
        requestDTO.setOrderEventType(orderEventType);
        requestDTO.setOrderMaster(thirdPartOrder);

        RemoteResponse<Boolean> response = thirdPartOrderRPCService.pushOrderInfo(requestDTO);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "推送退货信息, 适用单个订单推送", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void pushReturnInfo(String orgCode, ThirdPartOrderReturnDTO returnInfo, String operatorGuid, String operatorName) {
        Preconditions.notNull(returnInfo, "returnInfo list must not be null");

        ThirdPartBaseOrderRequestDTO requestDTO = new ThirdPartBaseOrderRequestDTO();
        requestDTO.setOrgCode(orgCode);
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setGuid(operatorGuid);
        operatorDTO.setName(operatorName);
        requestDTO.setOperator(operatorDTO);
        requestDTO.setOrderEventType(OrderEventTypeEnum.RETURN_ORDER);
        requestDTO.setOrderReturn(returnInfo);

        RemoteResponse<Boolean> response = thirdPartOrderRPCService.pushOrderInfo(requestDTO);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void noticeReturnEvent(String returnNo, OrderReturnEventEnum orderReturnEventEnum){
        ReturnEventNoticeRequest request = new ReturnEventNoticeRequest().setReturnNo(returnNo).setOrderReturnEventType(orderReturnEventEnum.getType());
        RemoteResponse<Boolean> response = thirdPartOrderRPCService.noticeOrderReturnEvent(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    /**
     * 旧方法为rePushOrderInfo，由于修改面积较大， 先保留原方法，测试完毕后再弃用旧方法改用新方法
     * @param orderNo 订单号
     * @return 是否成功
     */
    @ServiceLog(description = "重新推送订单信息(新)", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public boolean redoPushOrderInfo(String orderNo) {
        RemoteResponse<Boolean> response = thirdPartOrderRPCService.redoPushOrderInfo(orderNo);
        return response.getData() != null ? response.getData() : false;
    }
}
