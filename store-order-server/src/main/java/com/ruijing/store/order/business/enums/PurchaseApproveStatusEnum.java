package com.ruijing.store.order.business.enums;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/24 18:54
 */
public enum PurchaseApproveStatusEnum {

    REJECT("审批不通过", "", 3),
    FINAL("生成订单", "", 4),
    APPROVAL_1("一级审批", "PURCHASE_ORDER_LEVEL_1", 11),
    APPROVAL_2("二级审批", "PURCHASE_ORDER_LEVEL_2", 12),
    APPROVAL_3("三级审批", "PURCHASE_ORDER_LEVEL_3", 13),
    APPROVAL_4("四级审批", "PURCHASE_ORDER_LEVEL_4", 14),
    WATTINGTOSUBMIT("待提交审批", "", 20);

    public final String value;
    public final String code;
    public final Integer status;

    PurchaseApproveStatusEnum(String value, String code, Integer status) {
        this.value = value;
        this.code = code;
        this.status = status;
    }

    public String getValue() {
        return this.value;
    }
}
