package com.ruijing.store.statistic.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.search.dto.OrderAggregationResultDTO;
import com.ruijing.store.order.api.search.dto.OrderDateHistogramResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

public class StatisticsSearchRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private StatisticsSearchRpcServiceImpl statisticsSearchRpcService;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Test
    public void aggAmountAndQuantityByEntities() {
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();

        // 聚合master表字段
        paramDTO.setAggField(OrderSearchFieldEnum.SUPPLIER_ID);
        Mockito.when(orderSearchBoostService.aggOrderAmountAndCount(Mockito.any(StatisticsManagerParamDTO.class))).thenReturn(New.list());
        RemoteResponse<List<OrderAggregationResultDTO>> response = statisticsSearchRpcService.aggAmountAndQuantityByEntities(paramDTO);
        Assert.assertTrue(response.isSuccess());

        // 聚合detail表字段
        paramDTO.setAggField(OrderSearchFieldEnum.BRAND_ID);
        Mockito.when(orderSearchBoostService.aggProductAmountAndCount(Mockito.any(StatisticsManagerParamDTO.class))).thenReturn(New.list());
        response = statisticsSearchRpcService.aggAmountAndQuantityByEntities(paramDTO);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void aggAmountAndQuantityByTrendType() {
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setStartTime("2021-05-13 00:00:00");
        paramDTO.setEndTime("2021-05-14 00:00:00");

        Mockito.when(orderSearchBoostService.aggOrderAmountDateHistogram(Mockito.any(StatisticsManagerParamDTO.class))).thenReturn(New.list());
        RemoteResponse<Map<String, List<OrderDateHistogramResultDTO>>> response = statisticsSearchRpcService.aggAmountAndQuantityByTrendType(paramDTO);
        Assert.assertTrue(response.isSuccess());
    }
}