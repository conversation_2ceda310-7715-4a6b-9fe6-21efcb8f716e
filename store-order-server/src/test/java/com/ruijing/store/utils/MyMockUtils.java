package com.ruijing.store.utils;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: mock工具类
 * @author: zhongyu<PERSON>i
 * @create: 2021/3/11 11:07
 **/
public class MyMockUtils {

    /**
     * use reflection to remove the final modifier first and set default value，just try
     * 呃，mock常量只能自己写个反射去改成员变量了
     * @param clazz         类
     * @param fieldName     类的变量名
     * @param defaultValue  提前预设类的变量值
     */
    public static void setThreadLocalField(Object clazz, String fieldName, Object defaultValue) {
        try {
            Field field = clazz.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Field modifiersField = Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            int modifiers = modifiersField.getInt(field);
            // 抄的代码。。
            modifiers &= ~Modifier.FINAL;
            modifiersField.setInt(field, modifiers);
            field.set(clazz, defaultValue);
        } catch (Exception e) {
            // ignore
        }
    }

    /**
     * mock the RJSession property of RPC context
     * @param guid
     * @param orgId
     * @param supplierId
     */
    public static void setRPCContextRJSession(String guid, Integer orgId, Integer supplierId) {
        Map<String, Object> session = new HashMap<>();

        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid(guid);
        sessionInfo.setSuppId(supplierId);
        sessionInfo.setOrgId(orgId);
        sessionInfo.setUserType(RjUserTypeEnum.STORE_USER);
        sessionInfo.setUserId(2451L);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);
    }
}
