package com.ruijing.store;

import junit.framework.TestCase;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@RunWith(MockitoJUnitRunner.class)
public abstract class MockBaseTestCase extends TestCase {

    /**
     * 配置为自动运行Executor(及其子类)这种线程池里的任务
     * @param executor
     */
    public void configAutoRunInMultiThread(Executor executor) {
        // 配置多线程 mock returns of completable future
        Mockito.doAnswer(
                (InvocationOnMock invocation) -> {
                    ((Runnable) invocation.getArguments()[0]).run();
                    return null;
                }).when(executor).execute(Mockito.any(Runnable.class));
    }
}
