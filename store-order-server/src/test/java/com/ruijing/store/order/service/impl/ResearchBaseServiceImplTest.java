package com.ruijing.store.order.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ResearchBaseServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private ResearchBaseServiceImpl researchBaseServiceImpl;

    @Mock
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Test
    public void updateApplyManageProductUsage() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.anyString())).thenReturn(New.list(new RefFundcardOrderDO()));

        List<FundCardDTO> fundCardDTOS = new ArrayList<>();
        fundCardDTOS.add(new FundCardDTO());

        // 可以跑，但mock不出数据
//        Mockito.when(researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId("1", New.list("1"))).thenReturn(fundCardDTOS);

        // 下面这种方式就不能跑....,跟上面的没什么区别呀
//        Mockito.when(researchFundCardServiceClient.
//        findCurrentCardByOrgCodeAndCardId("1",list)).thenReturn(fundCardDTOS);

        // 该机构配置了平台经费 && 新单 测试 --> 期望返回 true
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFusercode(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode());
        // 新单
        orderMasterDO.setForderdate(DateUtils.parse("2021-04-08 18:00:01"));
        Mockito.when(researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(Mockito.anyString(),Mockito.any())).thenReturn(fundCardDTOS);
        Boolean platformFoundFlag = researchBaseServiceImpl.isPlatformFound(orderMasterDO);
        Assert.assertTrue(platformFoundFlag);

        //该机构配置了平台经费 && 旧单 测试  --> 期望返回false
        orderMasterDO.setForderdate(DateUtils.parse("2021-04-08 17:59:59"));
        platformFoundFlag = researchBaseServiceImpl.isPlatformFound(orderMasterDO);
        Assert.assertFalse(platformFoundFlag);
    }

    @Test
    public void needPlatformFoundCheck(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderdate(new Date());
        orderMasterDO.setFusercode(OrgConst.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN);
        boolean needPlatformFoundCheck = researchBaseServiceImpl.needPlatformFoundCheck(orderMasterDO);
        Assert.assertTrue(needPlatformFoundCheck);

        orderMasterDO.setFusercode("123");
        needPlatformFoundCheck = researchBaseServiceImpl.needPlatformFoundCheck(orderMasterDO);
        Assert.assertFalse(needPlatformFoundCheck);
    }

    @Test
    public void isNewOrder(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderdate(new Date());
        researchBaseServiceImpl.isNewOrder(orderMasterDO, "2021-05-21 18:00:00");
    }

}