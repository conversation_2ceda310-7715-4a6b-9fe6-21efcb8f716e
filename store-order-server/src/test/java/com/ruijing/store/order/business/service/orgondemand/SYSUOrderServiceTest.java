package com.ruijing.store.order.business.service.orgondemand;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.custom.dto.fundcard.FundCardAuthAndExtraDTO;
import com.reagent.research.custom.dto.order.OrderAcceptanceAuthDTO;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import com.ruijing.store.order.rpc.client.FundCardRelateRpcClient;
import com.ruijing.store.order.rpc.client.ResearchCustomClient;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.AccessDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.utils.MyMockUtils;
import com.sun.source.tree.AssertTree;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.List;

public class SYSUOrderServiceTest extends MockBaseTestCase {

    @InjectMocks
    private SYSUOrderService sysuOrderService;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private ResearchCustomClient researchCustomClient;

    @org.mockito.Mock
    private BuyerOrderService buyerOrderService;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private CacheClient cacheClient;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @org.mockito.Mock
    private DockingExtraMapper dockingExtraMapper;

    @org.mockito.Mock
    private FundCardRelateRpcClient fundCardRelateRpcClient;

    @org.mockito.Mock
    private OrderEmailHandler orderEmailHandler;

    public static class Mock {
        @MockMethod(targetClass = CacheClient.class)
        public void controlRepeatOperation(String uniqKey, Integer timeLimit) {
        }

        @MockMethod(targetClass = CacheClient.class)
        public void removeCache(String uniqKey) {
        }

        @MockMethod(targetClass = FundCardRelateRpcClient.class)
        public void fundCardUnFreeze(OrgRequest orgRequest) {
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        public void sendSupplierApplyCancelEmailToSupplier(List<OrderMasterDO> orderMasterDOS, long overDay) {
        }
    }

    @Test
    public void testCheckReceivePermission() {
        // order
        OrderMasterVO orderMasterVO = new OrderMasterVO();
        orderMasterVO.setFundCardIdList(New.list("fundcard1", "fundcard2"));
        orderMasterVO.setOrderNo("DC202112120001");
        orderMasterVO.setBuyDepartmentId(1234);
        orderMasterVO.setStatus(OrderStatusEnum.WaitingForStatement_1.getValue());

        // login info
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setManagerId(321);
        departmentDTO.setId(1234);
        LoginUserInfoBO loginInfo = new LoginUserInfoBO();
        loginInfo.setDeptList(New.list(departmentDTO));

        OrderInfoVO curOrderInfo = new OrderInfoVO();
        curOrderInfo.setOrder(orderMasterVO);

        // fundcard
        FundCardManagerDTO fundCardManagerDTO = new FundCardManagerDTO();
        fundCardManagerDTO.setUserId(321);
        FundCardDTO fundCardDTO = new FundCardDTO();
        fundCardDTO.setFundCardManagerDTOs(New.list(fundCardManagerDTO));

        AccessDTO accessDTO = new AccessDTO() {{
            setCode(ConfigConstant.ORDER_APPROVE_LEVEL_SECOND);
        }};
        List<AccessDTO> accessDTOList = New.list(accessDTO);
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.anyString(), Mockito.anyList())).thenReturn(New.list(fundCardDTO));
        Mockito.when(userClient.getDepartmentAccess(Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(accessDTOList);
        Mockito.when(userClient.getUserDetailByID(Mockito.anyInt())).thenReturn(new UserBaseInfoDTO() {{
            setJobnumber("sldkf");
        }});

        // test1
        Mockito.when(researchCustomClient.listAuthInfoByParam(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(New.list(new OrderAcceptanceAuthDTO()));
        boolean test1 = sysuOrderService.checkReceivePermission(curOrderInfo, loginInfo);
        Assert.assertTrue(!test1);

        // test2
        orderMasterVO.setStatus(OrderStatusEnum.OrderReceiveApproval.getValue());
        boolean test2 = sysuOrderService.checkReceivePermission(curOrderInfo, loginInfo);
        Assert.assertTrue(test2);

        // test3
        fundCardManagerDTO.setUserId(3214);
        fundCardDTO.setFundCardManagerDTOs(New.list(fundCardManagerDTO));
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.anyString(), Mockito.anyList())).thenReturn(New.list(fundCardDTO));
        boolean test3 = sysuOrderService.checkReceivePermission(curOrderInfo, loginInfo);
        Assert.assertTrue(!test3);

        // test4
        fundCardManagerDTO.setUserId(321);
        fundCardDTO.setFundCardManagerDTOs(New.list(fundCardManagerDTO));
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.anyString(), Mockito.anyList())).thenReturn(New.list(fundCardDTO));
        orderMasterVO.setStatus(OrderStatusEnum.OrderReceiveApprovalTwo.getValue());
        boolean test4 = sysuOrderService.checkReceivePermission(curOrderInfo, loginInfo);
        Assert.assertTrue(test4);
    }

    @Test
    public void testControllViewOrderAccessSYSU() {
        // input
        LoginUserInfoBO loginInfo = new LoginUserInfoBO();
        OrderSearchParamDTO searchRequest = new OrderSearchParamDTO();
        OrderListRequest request = new OrderListRequest();
        request.setMyOrderCheck(false);
        Integer rootDepartmentId = 123;

        DepartmentDTO departmentDTO = new DepartmentDTO();
        loginInfo.setDeptList(New.list(departmentDTO));

        Mockito.when(buyerOrderService.getDeptIdListForSearch(Mockito.anyList(), Mockito.any(OrderListRequest.class), Mockito.anyInt())).thenReturn(New.list(1, 2, 3));
        Mockito.doNothing().when(buyerOrderService).constructSearchPageParam(Mockito.any(OrderSearchParamDTO.class), Mockito.any(OrderListRequest.class));

        AccessDTO accessDTO = new AccessDTO();
        accessDTO.setCode(ConfigConstant.FINANCIAL_SUBMISSION);
        Mockito.when(userClient.getDepartmentAccess(Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(New.list(accessDTO));
        sysuOrderService.controllViewOrderAccessSYSU(searchRequest, request, rootDepartmentId, loginInfo);
        Assert.assertTrue(CollectionUtils.isEmpty(searchRequest.getIncludeOrderIdList()));

        // 无财务提交权限逻辑单测
        accessDTO.setCode("unknown access code");
        Mockito.when(userClient.getDepartmentAccess(Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(New.list(accessDTO));
        sysuOrderService.controllViewOrderAccessSYSU(searchRequest, request, rootDepartmentId, loginInfo);
        FundCardAuthAndExtraDTO fundCardAuthAndExtraDTO = new FundCardAuthAndExtraDTO();
        fundCardAuthAndExtraDTO.setFundcardId("456789");
        Mockito.when(researchCustomClient.getByApproveAuthQueryDtoByOrgId(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString())).thenReturn(New.list(fundCardAuthAndExtraDTO));
        sysuOrderService.controllViewOrderAccessSYSU(searchRequest, request, rootDepartmentId, loginInfo);
        Assert.assertTrue(CollectionUtils.isNotEmpty(searchRequest.getBuyerIdList()));

        // 授权订单id列表不为空
        List<String> orderIdStringList = New.list("13", "456787", "", "987");
        Mockito.when(refFundcardOrderService.findOrderIdsByCardIds(Mockito.anyList())).thenReturn(orderIdStringList);
        sysuOrderService.controllViewOrderAccessSYSU(searchRequest, request, rootDepartmentId, loginInfo);
        Assert.assertTrue(CollectionUtils.isNotEmpty(searchRequest.getBuyerIdList()));
    }

    @Test
    public void agreeCancelOrder() throws Exception {


        Mockito.when(orderMasterMapper.updateByPrimaryKeySelective(Mockito.any(OrderMasterDO.class))).thenReturn(1);
        Mockito.when(orderApprovalLogMapper.insert(Mockito.any(OrderApprovalLog.class))).thenReturn(1);
        Mockito.when(dockingExtraMapper.findByInfoIn(Mockito.anyList())).thenReturn(New.list());

        MyMockUtils.setThreadLocalField(sysuOrderService, "applyCancelOrderOverday", 14L);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(123);
        orderMasterDO.setForderno("forderno");
        LoginUserInfoBO loginInfo = new LoginUserInfoBO();
        loginInfo.setJobNumber("jobnumber");
        loginInfo.setUserId(123456);
        loginInfo.setOrgCode("orgCode");
        sysuOrderService.agreeCancelOrder(orderMasterDO, loginInfo);

        // exception
        Mockito.doThrow(new IllegalStateException("error test")).when(orderMasterMapper).updateByPrimaryKeySelective(Mockito.any(OrderMasterDO.class));
        try {
            sysuOrderService.agreeCancelOrder(orderMasterDO, loginInfo);
        } catch (Exception e) {
            // 能正常跑通表示通过测试
            Assert.assertTrue(true);
        }
    }

    @Test
    public void joinRecPicUrl() {
        Integer orgId = OrgEnum.ZHONG_SHAN_DA_XUE.getValue();
        MyMockUtils.setThreadLocalField(sysuOrderService, "SYSU_RECEIVE_PIC_URL_PREFIX", "http://sycl.sysu.edu.cn/hmsd");
        String picUrl = "/testRecPic";
        String s = sysuOrderService.joinRecPicUrl(orgId, picUrl);
        Assert.assertTrue(s.startsWith("http"));
    }
}