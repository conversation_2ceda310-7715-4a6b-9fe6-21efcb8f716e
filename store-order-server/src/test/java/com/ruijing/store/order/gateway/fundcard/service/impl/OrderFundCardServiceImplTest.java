package com.ruijing.store.order.gateway.fundcard.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.model.MockScope;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.alibaba.testable.processor.annotation.EnablePrivateAccess;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.dto.FundCardResultDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderUnFreezeTypeEnum;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.gateway.fundcard.request.FundCardSpecialRequestDTO;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardParam;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.order.service.impl.ResearchBaseServiceImpl;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;

@EnablePrivateAccess(srcClass = OrderFundCardServiceImpl.class)
public class OrderFundCardServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderFundCardServiceImpl orderFundCardService;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private ResearchBaseServiceImpl researchBaseService;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private OrderApprovalLogService orderApprovalLogService;

    @org.mockito.Mock
    private CacheClient cacheClient;

    @org.mockito.Mock
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @org.mockito.Mock
    private DockingExtraService dockingExtraService;

    @org.mockito.Mock
    private DepartmentRpcClient departmentRpcClient;

    public OrderFundCardServiceImplTest() {
    }

    public static class Mock {
        @MockMethod(targetClass = RpcContext.class)
        public static ProviderContext getProviderContext() {
            ProviderContext providerContext = ProviderContext.getProviderContext();
            HashMap<String, Object> callAttachments = new HashMap<>();
            RjSessionInfo rjSessionInfo = new RjSessionInfo();
            rjSessionInfo.setOrgId(2);
            callAttachments.put("RJ_SESSION_INFO", rjSessionInfo);
            providerContext.setCallAttachments(callAttachments);
            return providerContext;
        }

        @MockMethod(targetClass = OrderFundCardServiceImpl.class)
        public RemoteResponse saveFundCardCache(FundCardSpecialRequestDTO request) {
            return new RemoteResponse(RemoteResponse.FAILURE,"1",new Object());
        }

        @MockMethod(targetClass = ApplicationBaseService.class)
        Boolean updateApplyManageProductUsage(OrderMasterDO orderMasterDO, Integer operation, String cardId, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
            return true;
        }

        @MockMethod(targetClass = OrderMasterMapper.class, scope = MockScope.ASSOCIATED)
        OrderMasterDO selectByPrimaryKey(Integer id) {
            OrderMasterDO o = new OrderMasterDO();
            o.setFusercode(OrgEnum.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN.getCode());
            o.setId(1);
            o.setFundStatus(1);
            o.setForderamounttotal(BigDecimal.TEN);
            o.setFbuydepartmentid(1);
            return o;
        }

        @MockMethod(targetClass = RefFundcardOrderMapper.class, scope = MockScope.ASSOCIATED)
        List<RefFundcardOrderDO> findByOrderId(@Param("orderId") String orderId){
            RefFundcardOrderDO refFundcardOrderDO = new RefFundcardOrderDO();
            return New.list(refFundcardOrderDO);
        }

        @MockMethod(targetClass = ResearchFundCardServiceClient.class, scope = MockScope.ASSOCIATED)
        public boolean isNewBudgetByOrgCode(String orgCode){
            return true;
        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public OrganizationDTO getOrgById(Integer orgId){
            return new OrganizationDTO();
        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public UserBaseInfoDTO getUserInfo(Integer userId, Integer orgId){
            return new UserBaseInfoDTO();
        }

        @MockMethod(targetClass = DepartmentRpcClient.class, scope = MockScope.ASSOCIATED)
        public List<DepartmentDTO> getDepartmentsByIds(List<Long> departmentIds){
            return New.list(new DepartmentDTO());
        }

        @MockMethod(targetClass = ApplicationBaseService.class, scope = MockScope.ASSOCIATED)
        public Date getCreateTimeByOrderType(OrderMasterDO orderMasterDO){
            return new Date();
        }

        @MockMethod(targetClass = StatementPlatformClient.class, scope = MockScope.ASSOCIATED)
        public void deleteWaitingStatementByOrderId(List<Integer> orderIdList) {

        }
    }

    @Test
    public void orderFundCardUnFreezeCore() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setFundStatus(OrderFundStatusEnum.Freezed.getValue());
        orderMasterDO.setFbuydepartmentid(1);
        orderMasterDO.setForderamounttotal(new BigDecimal(1));
        orderMasterDO.setForderamounttotal(new BigDecimal(1));
        orderMasterDO.setReturnAmount(1.0);
        orderMasterDO.setStatus(6);
        orderMasterDO.setFusercode("JIANG_XI_ZHONG_YI_YAO_DA_XUE");
        OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
        orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN);
        orderUnFreezeRequestDTO.setFreezeAmount(BigDecimal.ONE);

        List<RefFundcardOrderDO> refFundCardList = New.list();
        PrivateAccessor.invoke(orderFundCardService, "orderFundCardUnFreezeCore", orderMasterDO, refFundCardList, orderUnFreezeRequestDTO, OrgConst.JIANG_XI_ZHONG_YI_YAO_DA_XUE);

        orderFundCardService.orderFundCardUnFreeze(orderMasterDO, orderUnFreezeRequestDTO);
    }

    @Test
    public void testSaveFundCardCache() {

        OrderMasterDO orderMasterDO1 = new OrderMasterDO();
        orderMasterDO1.setId(1);
        orderMasterDO1.setForderamounttotal(new BigDecimal(500));
        orderMasterDO1.setForderdate(new Date());
        orderMasterDO1.setFusercode(OrgEnum.NAN_FANG_YI_KE.getCode());
        OrderMasterDO orderMasterDO2 = new OrderMasterDO();
        orderMasterDO2.setId(2);
        orderMasterDO2.setForderamounttotal(new BigDecimal(600));
        orderMasterDO2.setForderdate(new Date());
        orderMasterDO2.setFusercode(OrgEnum.NAN_FANG_YI_KE.getCode());

        // mock 订单list
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.any())).thenReturn(New.list(orderMasterDO1, orderMasterDO2));
        Mockito.when(orderMasterMapper.updateFieldByIdIn(Mockito.any())).thenReturn(1);
        Mockito.when(cacheClient.lockRetry(Mockito.any(), Mockito.anyInt())).thenReturn(true);
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.any());

        // mock静态方法
        ProviderContext providerContext = ProviderContext.getProviderContext();
        HashMap<String, Object> callAttachments = new HashMap<>();
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setOrgId(2);
        callAttachments.put("RJ_SESSION_INFO", rjSessionInfo);
        providerContext.setCallAttachments(callAttachments);

        FundCardDTO fundCardDTO1 = new FundCardDTO();
        fundCardDTO1.setId("1");
        fundCardDTO1.setBalanceAmount(new BigDecimal(6000));
        FundCardDTO fundCardDTO2 = new FundCardDTO();
        fundCardDTO2.setId("2");
        fundCardDTO2.setBalanceAmount(new BigDecimal(12000));

        // mock 经费卡list
        Mockito.when(researchFundCardServiceClient.getFundCardListByCardIds(Mockito.any(), Mockito.any())).thenReturn(New.list(fundCardDTO1, fundCardDTO2));
        Mockito.when(researchBaseService.isNewOrder(Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.doNothing().when(orderApprovalLogService).createOrderOperateLog(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.any());

        OrderFundCardDTO orderFundCardDTO1 = new OrderFundCardDTO();
        orderFundCardDTO1.setOrderId(1);
        orderFundCardDTO1.setSequence(1);
        Mockito.when(orderFundCardCacheClient.findOrderFundCardCache(Mockito.any())).thenReturn(New.list(orderFundCardDTO1));

        RefFundcardOrderDTO refFundcardOrderDTO1 = new RefFundcardOrderDTO();
        refFundcardOrderDTO1.setOrderId("1");
        RefFundcardOrderDTO refFundcardOrderDTO2 = new RefFundcardOrderDTO();
        refFundcardOrderDTO2.setOrderId("2");

        Mockito.when(refFundcardOrderService.findByOrderId(Mockito.any())).thenReturn(New.list(refFundcardOrderDTO1, refFundcardOrderDTO2));
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.any(), Mockito.anyString())).thenReturn("1");
        LoginUserInfoBO loginUserInfoBO = new LoginUserInfoBO();
        loginUserInfoBO.setOrgCode(OrgEnum.NAN_FANG_YI_KE.getCode());
        Mockito.when(userClient.getLoginUserInfo(Mockito.any(), Mockito.anyInt())).thenReturn(loginUserInfoBO);
        Map<String, Object> session = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);

        FundCardSpecialRequestDTO request = new FundCardSpecialRequestDTO();
        OrderFundCardParam orderFundCardParam = new OrderFundCardParam();
        orderFundCardParam.setOrderId(1);
        orderFundCardParam.setProjectId("1");
        orderFundCardParam.setFundCardId("1");
        request.setOrderFundCardParams(New.list(orderFundCardParam));

        // 测试不是第一次换卡
        RemoteResponse<Boolean> responseNotFirst = orderFundCardService.saveFundCardCache(request);
        Assert.assertTrue(responseNotFirst.isSuccess());

        // 测试第一次换卡
        orderFundCardParam.setOrderId(2);
        orderFundCardParam.setProjectId("2");
        orderFundCardParam.setFundCardId("2");
        RemoteResponse<Boolean> responseFirst = orderFundCardService.saveFundCardCache(request);
        Assert.assertTrue(responseFirst.isSuccess());

        // 模拟获取orgCode失败
        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setCode("1111");
        Mockito.when(userClient.getOrgById(Mockito.anyInt())).thenReturn(organizationDTO);
        rjSessionInfo.setOrgId(1111);
        responseFirst = orderFundCardService.saveFundCardCache(request);
        Assert.assertTrue(responseFirst.isSuccess());

        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.any(), Mockito.anyString())).thenReturn("2");
        responseNotFirst = orderFundCardService.saveFundCardCache(request);
        Assert.assertTrue(responseNotFirst.isSuccess());
    }

    @Test
    public void unfreezeFundCard() {
        RefFundcardOrderDO ref1 = new RefFundcardOrderDO();
        ref1.setOrderId("1");
        ref1.setCardId("111");
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.any())).thenReturn(Collections.singletonList(ref1));
        Mockito.when(researchFundCardServiceClient.isNewBudgetByOrgCode(Mockito.any())).thenReturn(true);
        Mockito.when(userClient.getOrgById(Mockito.any())).thenReturn(new OrganizationDTO());
        DockingExtraDTO dockingExtraDTO1 = new DockingExtraDTO();
        dockingExtraDTO1.setExtraInfo("123");
        Mockito.when(dockingExtraService.findDockingExtra(Mockito.any())).thenReturn(Collections.singletonList(dockingExtraDTO1));
        DepartmentDTO d1 = new DepartmentDTO();
        d1.setManagerId(1);
        Mockito.when(departmentRpcClient.getDepartmentsByIds(Mockito.any())).thenReturn(Collections.singletonList(d1));
        UserBaseInfoDTO u1 = new UserBaseInfoDTO();
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anyList(), Mockito.any())).thenReturn(Collections.singletonList(u1));
        Mockito.when(userClient.getUserInfo(Mockito.any(), Mockito.any())).thenReturn(u1);
        Mockito.when(orderMasterMapper.updateOrderById(Mockito.any())).thenReturn(1);

        FundCardResultDTO fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setHandleResult(204);
        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any())).thenReturn(fundCardResultDTO);

        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderId(1);
        orderFundCardService.unfreezeFundCard(request);

        fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setHandleResult(200);
        fundCardResultDTO.setRelyAsyCallback("0");
        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any())).thenReturn(fundCardResultDTO);
        orderFundCardService.unfreezeFundCard(request);

        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any())).thenThrow(new IllegalStateException("error"));
        orderFundCardService.unfreezeFundCard(request);
    }
}