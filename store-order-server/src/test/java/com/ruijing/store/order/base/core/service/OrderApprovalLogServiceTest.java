package com.ruijing.store.order.base.core.service;

import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.impl.OrderApprovalLogServiceImpl;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.utils.MyMockUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;

public class OrderApprovalLogServiceTest extends MockBaseTestCase {

    @InjectMocks
    OrderApprovalLogServiceImpl orderApprovalLogService;

    @Mock
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private UserClient userClient;

    @Test
    public void findByOrderIdListAndStatus() {
        MyMockUtils.setThreadLocalField(orderApprovalLogService, "IoExecutor", Executors.newScheduledThreadPool(2));
        OrderApprovalLog log = new OrderApprovalLog();
        log.setOperatorId(-1);
        log.setApproveStatus(1);
        Mockito.when(orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(Mockito.anyList(), Mockito.anyCollection())).thenReturn(Collections.singletonList(log));

        OrderMasterDO o = new OrderMasterDO();
        o.setFuserid(118);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(Collections.singletonList(o));

        UserBaseInfoDTO u = new UserBaseInfoDTO();
        u.setId(1);
        u.setName("test");
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anyList(), Mockito.any())).thenReturn(Collections.singletonList(u));

        OrderApprovalRequestDTO requestDTO = new OrderApprovalRequestDTO();
        requestDTO.setOrderIdList(Arrays.asList(1));
        requestDTO.setTypeList(Arrays.asList(OrderApprovalEnum.PASS.getValue()));
        List<OrderApprovalLogDTO> response = orderApprovalLogService.findByOrderIdListAndStatus(requestDTO);
        Assert.assertTrue(response.size() > 0);
    }
}