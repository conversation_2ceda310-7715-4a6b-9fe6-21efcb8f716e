package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.supp.api.supplier.service.SuppInfoService;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.shop.crm.api.pojo.query.UserAccountPageableQry;
import com.ruijing.shop.crm.api.service.ShopSuppContactService;
import com.ruijing.shop.crm.api.service.UserAccountManageRpcService;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.List;

public class SuppClientTest extends MockBaseTestCase {

    @org.mockito.Mock
    private ShopSuppContactService shopSuppContactService;

    @org.mockito.Mock
    private SuppInfoService suppInfoService;

    @InjectMocks
    private SuppClient suppClient;

    public static class Mock {
        @MockMethod(targetClass = UserAccountManageRpcService.class)
        PageableResponse<List<UserAccountDTO>> queryPageableAccount(UserAccountPageableQry var1) {
            return PageableResponse.<List<UserAccountDTO>>custom().setSuccess().setData(New.list(new UserAccountDTO()));
        }
    }

    @Test
    public void querySuppAccountList() {
        List<UserAccountDTO> userAccountDTOList = suppClient.querySuppAccountList(1, null);
        System.out.println(userAccountDTOList);
    }

}