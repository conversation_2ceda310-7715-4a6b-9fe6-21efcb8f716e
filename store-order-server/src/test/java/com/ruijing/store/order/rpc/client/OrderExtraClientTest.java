package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.service.OrderExtraRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class OrderExtraClientTest extends MockBaseTestCase {

    @InjectMocks
    private OrderExtraClient orderExtraClient;

    @Mock
    private OrderExtraRpcService orderExtraRpcService;

    @Test
    public void insertList() {
        RemoteResponse<Integer> response = RemoteResponse.<Integer>custom().setSuccess().setData(1);
        Mockito.when(orderExtraRpcService.insertList(Mockito.anyList())).thenReturn(response);
        BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
        Integer result = orderExtraClient.insertList(New.list(orderExtraDTO));
        Assert.assertTrue(result > 0);
    }

    @Test
    public void selectByOrderIdAndExtraValue() {
        RemoteResponse<List<BaseOrderExtraDTO>> response = RemoteResponse.<List<BaseOrderExtraDTO>>custom().setSuccess().setData(New.list());
        Mockito.when(orderExtraRpcService.selectByOrderIdAndExtraKey(Mockito.any(BaseOrderExtraDTO.class))).thenReturn(response);
        BaseOrderExtraDTO query = new BaseOrderExtraDTO() {{
            setOrderId(13);
        }};
        List<BaseOrderExtraDTO> resultList = orderExtraClient.selectByOrderIdAndExtraKey(query);
        Assert.assertNotNull(resultList);
    }

    @Test
    public void selectByOrderIdInAndExtraValue() {
        RemoteResponse<List<BaseOrderExtraDTO>> response = RemoteResponse.<List<BaseOrderExtraDTO>>custom().setSuccess().setData(New.list());
        Mockito.when(orderExtraRpcService.selectByOrderIdInAndExtraKey(Mockito.anyCollection(), Mockito.anyInt())).thenReturn(response);
        List<BaseOrderExtraDTO> resultList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(123), 321);
        Assert.assertNotNull(resultList);
    }

    @Test
    public void selectByOrderIdIn() {
        RemoteResponse<List<BaseOrderExtraDTO>> response = RemoteResponse.<List<BaseOrderExtraDTO>>custom().setSuccess().setData(New.list());
        Mockito.when(orderExtraRpcService.selectByOrderIdList(Mockito.anyCollection())).thenReturn(response);
        List<BaseOrderExtraDTO> resultList = orderExtraClient.selectByOrderIdIn(New.list(123));
        Assert.assertNotNull(resultList);
    }

    @Test
    public void selectByOrderNoIn() {
        RemoteResponse<List<BaseOrderExtraDTO>> response = RemoteResponse.<List<BaseOrderExtraDTO>>custom().setSuccess().setData(New.list());
        Mockito.when(orderExtraRpcService.selectByOrderNoList(Mockito.anyCollection())).thenReturn(response);
        List<BaseOrderExtraDTO> resultList = orderExtraClient.selectByOrderNoIn(New.list("123"));
        Assert.assertNotNull(resultList);
    }
}