package com.ruijing.store.order.gateway;

import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.supp.api.order.dto.OrderLogisticsInfoDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.approval.api.dto.ApprovalTaskDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderApprovalParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.gateway.dto.SyncOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefOrderDetailTagDOMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefOrderDetailTagDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderRemarkMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.business.enums.TagTypeEnum;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.PrivateChangeFundCardRequestDTO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class OrderPrivateGWServiceTest extends MockBaseTestCase {

    @InjectMocks
    private OrderPrivateGWService orderPrivateGWService;

    @Mock
    private DockingExtraMapper dockingExtraMapper;

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Mock
    private TPIOrderClient tpiOrderClient;

    @Mock
    private RefFundcardOrderService refFundcardOrderService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private ResearchStatementClient researchStatementClient;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private UserClient userClient;

    @Mock
    private OrderRemarkMapper orderRemarkMapper;

    @Mock
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Mock
    private SuppClient suppClient;

    @Mock
    private ApplicationBaseClient applicationBaseClient;

    @Mock
    private BidClient bidClient;

    @Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    @Mock
    private OrderExtraClient orderExtraClient;

    @Mock
    private OrderApprovalLogService orderApprovalLogService;

    @Mock
    private RefOrderDetailTagDOMapper refOrderDetailTagDOMapper;

    @Test
    public void retryPushThirdPartOrder() {
        Mockito.when(dockingExtraMapper.updateByInfo(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());
        Mockito.when(tpiOrderClient.retryPushOrderInfo(Mockito.any())).thenReturn(true);

        OrderBasicParamDTO orderBasicParamDTO = new OrderBasicParamDTO();
        orderBasicParamDTO.setOrgCode(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode());
        orderBasicParamDTO.setOrderNo("DC202103291430401");
        orderPrivateGWService.retryPushThirdPartOrder(orderBasicParamDTO);
    }

    @Test
    public void saveFundCards() {
        String s = "{\"saveProjectList\":[{\"id\":\"f9e9a1d4-b89a-478e-8451-f7151eb51eb5\",\"projectCode\":\"12312\",\"projectName\":\"112312355\",\"fundType\":0,\"saveFundCardList\":[{\"cardId\":\"b011a3ed-283d-41dc-8d3e-b45f8c1f341c\",\"cardNo\":\"112312355\",\"saveFundCardSubjectList\":[]}]}],\"orderIds\":[180100,180098]}";
        ChangeFundCardRequestDTO requestDTO = JsonUtils.fromJson(s, ChangeFundCardRequestDTO.class);

        PrivateChangeFundCardRequestDTO request = new PrivateChangeFundCardRequestDTO();
        request.setRequest(requestDTO);
        request.setOrgId(5);
        request.setGuid("5tigzdqurfvhj7rydsyupezl");
        Mockito.doNothing().when(refFundcardOrderService).saveFundCardForWaitStatement(Mockito.any(), Mockito.any());

        RemoteResponse<Boolean> response = orderPrivateGWService.saveFundCards(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void manualStatement() {
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderNoList(Arrays.asList("test"));

        OrderMasterDO o = new OrderMasterDO();
        o.setStatus(6);
        o.setForderno("test");
        o.setFbuyerid(1);
        o.setFbuyername("111");
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.any())).thenReturn(Arrays.asList(o));
        StatementResultDTO r = new StatementResultDTO();
        r.setId(1L);
        Mockito.when(researchStatementClient.createStatementSingle(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(r);

        RemoteResponse<List<UpdateOrderParamDTO>> response = orderPrivateGWService.manualStatement(request);
        Assert.assertTrue(response.isSuccess());
    }


    @Test
    public void fetchSyncOrderMessage() throws Exception {
        Class<OrderPrivateGWService> orderPrivateGWServiceImpl = (Class<OrderPrivateGWService>) orderPrivateGWService.getClass();
        Method fetchSyncOrderMessage = orderPrivateGWServiceImpl.getDeclaredMethod("fetchSyncOrderMessage", List.class);
        fetchSyncOrderMessage.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.any())).thenReturn(New.list(orderMasterDO));
        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setId(2);
        orderDetailDO.setFmasterid(1);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.any())).thenReturn(New.list(orderDetailDO));
        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        userBaseInfoDTO.setMobile("1111");
        userBaseInfoDTO.setId(123);
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anySet(),Mockito.anyInt())).thenReturn(New.list(userBaseInfoDTO));
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setFtbuyappid(123);
        orderRemark.setRemark("13r43");
        Mockito.when(orderRemarkMapper.findAllByPrimaryKey(Mockito.any())).thenReturn(New.list(orderRemark));
        DangerousTagDO dangerousTagDO = new DangerousTagDO();
        dangerousTagDO.setBusinessId("123");
        dangerousTagDO.setDangerousType(1);
        Mockito.when(dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(Mockito.anyCollection(),Mockito.anyInt())).thenReturn(New.list(dangerousTagDO));
        Mockito.when(suppClient.getLogisticsInfo(Mockito.any())).thenReturn(New.list(new OrderLogisticsInfoDTO()));

        OrderMasterSearchDTO orderMasterSearchDTO = new OrderMasterSearchDTO();
        orderMasterSearchDTO.setId(1);
        fetchSyncOrderMessage.invoke(orderPrivateGWService, New.list(orderMasterSearchDTO));
    }

    @Test
    public void syncFlowId() {
        SyncOrderParamDTO request = new SyncOrderParamDTO();
        request.setToken("<EMAIL>");
        request.setDays(1);

        // 没有要更新的
        Mockito.when(orderMasterMapper.countByFlowId(Mockito.anyInt(), Mockito.any(), Mockito.any())).thenReturn(0);
        RemoteResponse<Boolean> response = orderPrivateGWService.syncFlowId(request);
        Assert.assertTrue(response.isSuccess());

        // 有1条要更新
        Mockito.when(orderMasterMapper.countByFlowId(Mockito.anyInt(), Mockito.any(), Mockito.any())).thenReturn(1);

        Integer buyAppId = 1;
        Integer flowId = 1;
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFtbuyappid(1);
        Mockito.when(orderMasterMapper.selectByFlowIdAndForderdate(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(New.list(orderMasterDO));

        ApprovalTaskDTO approvalTaskDTO = new ApprovalTaskDTO();
        approvalTaskDTO.setDockId(buyAppId.longValue());
        approvalTaskDTO.setFlowId(flowId.longValue());
        Mockito.when(applicationBaseClient.getAppInfoWithFlowId(Mockito.any())).thenReturn(New.list(approvalTaskDTO));

        Mockito.when(orderMasterMapper.batchUpdateFlowId(Mockito.any())).thenReturn(1);
        response = orderPrivateGWService.syncFlowId(request);
    }

    @Test
    public void migrateSecondApproval() {
        OrderMasterDO o = new OrderMasterDO();
        o.setId(1);
        Mockito.when(orderMasterMapper.findByOrgIdDeptIdStatus(Mockito.anyInt(), Mockito.anyCollection(), Mockito.anyCollection())).thenReturn(Arrays.asList(o));
        BaseOrderExtraDTO extraDTO = new BaseOrderExtraDTO();
        extraDTO.setExtraKeyDesc("第二验收人");
        extraDTO.setExtraKey(1);
        extraDTO.setExtraValue("hhhh");
        Mockito.when(orderExtraClient.selectByOrderIdIn(Mockito.anyCollection())).thenReturn(Arrays.asList(extraDTO));
        Mockito.doNothing().when(orderApprovalLogService).insertOrderApprovalLogList(Mockito.anyList());

        RemoteResponse<Integer> response = orderPrivateGWService.migrateSecondApproval();
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void batchUpdateCategoryTag() {
        SyncOrderParamDTO request = new SyncOrderParamDTO();
        request.setToken("<EMAIL>");

        Integer detailId = 123;

        Mockito.when(orderDetailMapper.selectEmptyCategoryTagItem()).thenReturn(New.list(detailId));

        // mock ref detail id
        RefOrderDetailTagDO refDetail1 = new RefOrderDetailTagDO();
        refDetail1.setRefId(detailId.toString());
        refDetail1.setTagType(TagTypeEnum.FEE_TYPE.getValue());
        refDetail1.setTagName("测试分析费");
        RefOrderDetailTagDO refDetail2 = new RefOrderDetailTagDO();
        refDetail2.setRefId(detailId.toString());
        refDetail2.setTagType(TagTypeEnum.FIRST_TIER_CATEGORY.getValue());
        refDetail2.setTagName("服务");
        Mockito.when(refOrderDetailTagDOMapper.findByRefIdIn(Mockito.any())).thenReturn(New.list(refDetail1, refDetail2));

        Mockito.when(orderDetailMapper.completeCategoryTagAndFeeTag(Mockito.any())).thenReturn(1);

        RemoteResponse<List<Integer>> response = orderPrivateGWService.batchUpdateCategoryTag(request);
        Assert.assertTrue(response.isSuccess());
    }
}
