package com.ruijing.store.order.base.core.translator;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDetailDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import org.junit.Assert;
import org.junit.Test;

public class GoodsReturnTranslatorTest {

    @Test
    public void doToDto() {
        GoodsReturnDetailDTO goodsReturnDetailDTO = new GoodsReturnDetailDTO();
        goodsReturnDetailDTO.setDetailId(123);
        String mockDetailListJson = JsonUtils.toJson(New.list(goodsReturnDetailDTO));

        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setGoodsReturnDetailJSON(mockDetailListJson);

        GoodsReturnDTO goodsReturnDTO = GoodsReturnTranslator.doToDto(goodsReturn);
        Assert.assertTrue(goodsReturnDTO.getId() == null);
    }
}