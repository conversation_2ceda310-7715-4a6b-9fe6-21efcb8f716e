package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.ThirdPartOrderRPCClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/3/9 16:23
 **/
public class OrderMasterCommonServiceTest extends MockBaseTestCase {

    @InjectMocks
    private OrderMasterCommonServiceImpl orderMasterCommonService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @Mock
    private SysConfigClient sysConfigClient;

    @Mock
    private OrderStatementService orderStatementService;

    @Mock
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Test
    public void batchUpdateByOrderNoTest() {
        Mockito.when(orderMasterMapper.updateFieldByForderno(Mockito.anyList())).thenReturn(1);
        Mockito.when(orderMasterForTPIService.updateThirdPlatformOrder(new UpdateOrderParamDTO())).thenReturn(true);

        List<UpdateOrderParamDTO> request = new ArrayList<>();
        for (int i = 0; i < 99; i++) {
            UpdateOrderParamDTO paramDTO1 = new UpdateOrderParamDTO();
            paramDTO1.setOrderNo("111");
            request.add(paramDTO1);
        }

        RemoteResponse<Integer> response = orderMasterCommonService.batchUpdateByOrderNo(request);
        Assert.assertTrue(response.getData() == 1);
    }

    @Test
    public void updateFieldByOrderNo() {
        Mockito.when(orderMasterMapper.updateFieldByForderno(Mockito.anyList())).thenReturn(1);
        List<UpdateOrderParamDTO> request = new ArrayList<>();
        for (int i = 0; i < 99; i++) {
            UpdateOrderParamDTO paramDTO1 = new UpdateOrderParamDTO();
            paramDTO1.setOrderNo("111");
            request.add(paramDTO1);
        }

        RemoteResponse<Integer> response = orderMasterCommonService.updateFieldByOrderNo(request);
        Assert.assertTrue(response.getData() == 1);
    }

}
