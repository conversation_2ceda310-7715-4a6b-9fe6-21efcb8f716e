package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.alibaba.testable.processor.annotation.EnablePrivateAccess;
import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.bid.api.rpc.service.BidCommonRpcService;
import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageProductDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageRequestDTO;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.service.impl.ApplicationBaseServiceImpl;
import org.apache.ibatis.annotations.Param;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@EnablePrivateAccess(srcClass = BidClient.class)
public class BidClientTest extends MockBaseTestCase {

    @InjectMocks
    private BidClient bidClient;

    public static class Mock {
        @MockMethod(targetClass = BidCommonRpcService.class)
        RemoteResponse<List<BidMasterDTO>> findBidMasterByBidNoList(List<String> bidNoList) {
            return RemoteResponse.<List<BidMasterDTO>>custom().setData(New.list(new BidMasterDTO())).setSuccess().build();
        }
    }

    @Test
    public void findBidMasterByBidNoList() {
        bidClient.findBidMasterByBidNoList(New.list("test"));
    }
}