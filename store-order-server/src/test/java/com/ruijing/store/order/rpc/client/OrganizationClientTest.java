package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.service.OrganizationRpcService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

public class OrganizationClientTest extends MockBaseTestCase {

    @InjectMocks
    private OrganizationClient organizationClient;

    @Mock
    private OrganizationRpcService organizationRpcService;

    @Test
    public void findByIdList() {
        OrganizationDTO organizationDTO = new OrganizationDTO();
        RemoteResponse<List<OrganizationDTO>> response = RemoteResponse.<List<OrganizationDTO>>custom().setSuccess().setData(Arrays.asList(organizationDTO));
        Mockito.when(organizationRpcService.getByIds(Mockito.anyList())).thenReturn(response);

        List<OrganizationDTO> byIdList = organizationClient.findByIdList(Arrays.asList(1));
        Assert.assertTrue("error", byIdList != null);

    }
}
