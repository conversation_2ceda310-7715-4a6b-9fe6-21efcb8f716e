package com.ruijing.store.order.rpc.callback;

import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.financial.docking.dto.order.OrderReturnResult;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class OrderReturnCallbackServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderReturnCallbackServiceImpl orderReturnCallbackService;

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Mock
    private DockingExtraService dockingExtraService;

    @Test
    public void handleOrderReturnResult() {
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        Mockito.when(dockingExtraService.saveOrUpdateDockingExtra(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        CallbackRequest<OrderReturnResult> request = new CallbackRequest<>();
        request.setOrgCode(DockingConstant.GUANG_XI_ZHONG_LIU);
        request.setCode(201);
        OrderReturnResult data = new OrderReturnResult();
        data.setOrderNo("test");
        request.setData(data);
        RemoteResponse<Boolean> response = orderReturnCallbackService.handleOrderReturnResult(request);
        Assert.assertTrue(response.isSuccess());
    }

}