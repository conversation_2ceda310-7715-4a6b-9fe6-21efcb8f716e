package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.log.service.OrderOtherLogRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class OrderOtherLogClientTest extends MockBaseTestCase {

    @Mock
    private OrderOtherLogRpcService orderOtherLogRpcService;

    @InjectMocks
    private OrderOtherLogClient orderOtherLogClient;

    @Test
    public void createOrderDockingLog() {
        RemoteResponse response = RemoteResponse.custom().setSuccess();
        Mockito.when(orderOtherLogRpcService.insertOrderDockingLog(Mockito.any())).thenReturn(response);

        String testStr = "test";
        for (int i = 0; i < 1000; i++) {
            testStr += "test";
        }
        orderOtherLogClient.createOrderDockingLog("test", "test", "test", testStr, "test", testStr);
    }
}
