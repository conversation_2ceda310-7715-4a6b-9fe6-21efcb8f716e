package com.ruijing.store.order.rpc.client;

import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.wms.api.service.WmsRuleRpcService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;


public class WmsRuleRpcServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    WmsRuleRpcServiceClient wmsRuleRpcServiceClient;

    @Mock
    private WmsRuleRpcService wmsRuleRpcService;

    @Test
    public void getNewWareHouseConfig() {
        ApiResult<Boolean> apiResult = ApiResult.newSuccess();
        apiResult.setData(true);
        Mockito.when(wmsRuleRpcService.autoInbound(Mockito.anyInt())).thenReturn(apiResult);
        Boolean newWareHouseConfig = wmsRuleRpcServiceClient.getNewWareHouseConfig(1);
        Assert.assertTrue(newWareHouseConfig);
    }
}