package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.baseconfig.api.rpc.SysConfigRpcService;
import com.ruijing.store.order.constant.ConfigConstant;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SysConfigClientTest extends MockBaseTestCase {

    @InjectMocks
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private WmsRuleRpcServiceClient wmsRuleRpcServiceClient;


    public static class Mock {
        @MockMethod(targetClass = SysConfigRpcService.class)
        RemoteResponse<Map<String, Map<String, BaseConfigDTO>>> findByCodes(List<String> codes){
            return RemoteResponse.<Map<String, Map<String, BaseConfigDTO>>>custom().setSuccess().setData(New.map("1", New.map("1", new BaseConfigDTO())));
        }

    }

    @Test
    public void isAutoStoreWareHouse() {
        Mockito.when(wmsRuleRpcServiceClient.getNewWareHouseConfig(Mockito.anyInt())).thenReturn(true);

        Map<String, String> configMap = new HashMap<>();
        configMap.put(ConfigConstant.ORG_RECEIPT_STORE_CONFIG, "2");
        boolean autoStoreWareHouse = sysConfigClient.isAutoStoreWareHouse(1, true, configMap);
        Assert.assertTrue(autoStoreWareHouse);
        autoStoreWareHouse = sysConfigClient.isAutoStoreWareHouse(1, false, configMap);
        Assert.assertTrue(autoStoreWareHouse);

        autoStoreWareHouse = sysConfigClient.isAutoStoreWareHouse(1, false, configMap);
        Assert.assertTrue(autoStoreWareHouse);
    }

    @Test
    public void findByCodes() {
        List<String> list = New.list(
                ConfigConstant.AUTO_ACCEPTANCE_CHCEK, ConfigConstant.AUTO_ACCEPTANCE_DAYS, ConfigConstant.ORG_RECEIPT_PIC_CONFIG,
                ConfigConstant.ORG_RECEIPT_STORE_CONFIG, ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, ConfigConstant.ORDER_CONTRACT_UPLOAD,
                ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, ConfigConstant.ORDER_CONTRACT_MONETARY_LIMITATION, ConfigConstant.AUTO_SUBMIT_STATMENT,
                ConfigConstant.STATEMENT_SUBMITTED_MANNER, ConfigConstant.USE_WAREHOUSE_SYSTEM, ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM,
                ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE
        );
        sysConfigClient.findByCodes(list);
    }
}