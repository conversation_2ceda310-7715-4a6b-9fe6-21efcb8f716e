package com.ruijing.store.order.rpc.client;

import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceRefResultDTO;
import com.reagent.research.statement.api.invoice.service.InvoiceApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.user.api.dto.invoicetitle.InvoiceTitleDTO;
import com.ruijing.store.user.api.service.InvoiceTitleRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

public class InvoiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private InvoiceClient invoiceClient;

    @Mock
    private InvoiceApi invoiceApi;

    @Mock
    private InvoiceTitleRpcService invoiceTitleRpcService;

    @Test
    public void testFindInvoiceVOList() {
        InvoiceDTO invoiceDTO = new InvoiceDTO();
        invoiceDTO.setAmount(BigDecimal.valueOf(406.00));
        invoiceDTO.setBankName("天河区天河北路888号");
        invoiceDTO.setBankNum("9882340000000000000000");
        invoiceDTO.setDrawer("西格玛奥德里123(上海)贸易有限公司");
        invoiceDTO.setId(1501419);
        invoiceDTO.setRemark("");
        invoiceDTO.setSourceId(175096L);
        invoiceDTO.setInvoiceNo("********-*********");
        invoiceDTO.setUrl("");
        invoiceDTO.setInvoiceRefDTOS(Arrays.asList(new InvoiceRefResultDTO()));

        RemoteResponse<List<InvoiceDTO>> response = RemoteResponse.<List<InvoiceDTO>>custom().setData(New.list(invoiceDTO)).setSuccess();
        Mockito.when(invoiceApi.findInvoiceList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(response);
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(new InvoiceQueryDTO());
        Assert.assertTrue(CollectionUtils.isEmpty(invoiceVOList.get(0).getPicturePathList()));

        invoiceDTO.setUrl("alskdjf;|,aklsd");
        invoiceDTO.setInvoicePhoto("alskdjf;|,aklsd");
        invoiceDTO.setInvoiceRefDTOS(Arrays.asList(new InvoiceRefResultDTO()));

        response = RemoteResponse.<List<InvoiceDTO>>custom().setData(New.list(invoiceDTO)).setSuccess();
        Mockito.when(invoiceApi.findInvoiceList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(response);
        invoiceVOList = invoiceClient.findInvoiceVOList(new InvoiceQueryDTO());
        Assert.assertTrue(CollectionUtils.isNotEmpty(invoiceVOList));
    }

    @Test
    public void findInvoiceTitleByIdList() {
        InvoiceTitleDTO invoiceTitleDTO = new InvoiceTitleDTO();
        invoiceTitleDTO.setId(16);
        invoiceTitleDTO.setTitle("江苏省中医院");
        List<InvoiceTitleDTO> resultList = New.list(invoiceTitleDTO);
        RemoteResponse<List<InvoiceTitleDTO>> response = RemoteResponse.<List<InvoiceTitleDTO>>custom().setSuccess().setData(resultList);
        Mockito.when(invoiceTitleRpcService.getByIds(Mockito.anyList())).thenReturn(response);
        List<InvoiceTitleDTO> invoiceTitleByIdList = invoiceClient.findInvoiceTitleByIdList(New.list(16));
        Assert.assertTrue(invoiceTitleByIdList.size() > 0);
    }
}