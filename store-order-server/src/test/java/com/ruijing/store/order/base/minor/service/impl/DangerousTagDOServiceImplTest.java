package com.ruijing.store.order.base.minor.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class DangerousTagDOServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private DangerousTagDOServiceImpl dangerousTagDOServiceImpl;

    @Mock
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Test
    public void selectByOrderDetailIdIn() {
        Mockito.when(dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(Mockito.anyCollection()
                ,Mockito.anyInt())).thenReturn(New.list());
        dangerousTagDOServiceImpl.selectByOrderDetailIdIn(New.list("1","2","3"));
    }
}