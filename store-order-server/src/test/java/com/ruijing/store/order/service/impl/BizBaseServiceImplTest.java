package com.ruijing.store.order.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationDTO;
import com.ruijing.store.electronicsign.api.dto.ValidatePasswordDTO;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.ElectronicSignServiceClient;
import org.junit.Test;
import org.mockito.InjectMocks;


public class BizBaseServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private BizBaseServiceImpl bizBaseServiceImpl;

    @org.mockito.Mock
    private ElectronicSignServiceClient electronicSignServiceClient;

    public static class Mock {
        @MockMethod(targetClass = BizBaseServiceImpl.class)
        public void checkESignPassWord(String passWord, String orgCode, String guid){}

        @MockMethod(targetClass = BizBaseServiceImpl.class)
        public void saveElectronicSign(String userGuid, String userName, String orgCode, ElectronicSignatureOperationEnum operation
                , String businessId ,String interactionId,String groupCode,String password){}

        @MockMethod(targetClass = ElectronicSignServiceClient.class)
        public void validatePassword(ValidatePasswordDTO validatePasswordDTO){}

        @MockMethod(targetClass = ElectronicSignServiceClient.class)
        public ElectronicSignOperationDTO searchOperationConfig(String userGuid, String orgCode, Integer departmentId, ElectronicSignatureOperationEnum electronicSignatureOperationEnum) {
            ElectronicSignOperationDTO electronicSignOperationDTO = new ElectronicSignOperationDTO();
            if(departmentId == null){
                electronicSignOperationDTO.setElectronicSignEnable(0);
                return electronicSignOperationDTO;
            }
            if(departmentId == 0){
                electronicSignOperationDTO.setOrgWithoutCode(true);
                electronicSignOperationDTO.setUserWithoutCode(true);
                return electronicSignOperationDTO;
            }
            electronicSignOperationDTO.setOrgWithoutCode(false);
            return electronicSignOperationDTO;
        }
    }

    @Test
    public void saveAcceptElectronicSign() {
        OrderReceiptParamDTO orderReceiptParamDTO = new OrderReceiptParamDTO();
        orderReceiptParamDTO.setUserId(1);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);

        // 不需要电子签名
        orderMasterDO.setFbuydepartmentid(null);
        bizBaseServiceImpl.saveAcceptElectronicSign(orderReceiptParamDTO,orderMasterDO);

        // 使用电子签名,需要输入密码
        orderMasterDO.setFbuydepartmentid(-1);
        orderReceiptParamDTO.setPassword("1");
        bizBaseServiceImpl.saveAcceptElectronicSign(orderReceiptParamDTO,orderMasterDO);

        // 使用电子签名,不需要输入密码
        orderMasterDO.setFbuydepartmentid(1);
        bizBaseServiceImpl.saveAcceptElectronicSign(orderReceiptParamDTO,orderMasterDO);
    }

    @Test
    public void checkESignPassWord() {
        bizBaseServiceImpl.checkESignPassWord("test","test","test");
    }
}