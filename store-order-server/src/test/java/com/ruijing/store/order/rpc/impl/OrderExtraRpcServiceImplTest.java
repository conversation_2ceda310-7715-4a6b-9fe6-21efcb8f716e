package com.ruijing.store.order.rpc.impl;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class OrderExtraRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderExtraRpcServiceImpl orderExtraRpcService;

    @Mock
    private OrderExtraClient orderExtraClient;

    @Test
    public void selectByOrderIdInAndExtraValue() {
        BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
        Mockito.when(orderExtraClient.selectByOrderIdInAndExtraKey(Mockito.anyCollection(), Mockito.anyInt())).thenReturn(New.list(orderExtraDTO));
        RemoteResponse<List<com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdInAndExtraValue(New.list(123), OrderExtraEnum.SECOND_RECEIVER_NAME.getValue());
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void selectByOrderIdIn() {
        BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
        Mockito.when(orderExtraClient.selectByOrderIdIn(Mockito.anyCollection())).thenReturn(New.list(orderExtraDTO));
        RemoteResponse<List<com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdIn(New.list(123));
        Assert.assertTrue(response.isSuccess());
    }
}