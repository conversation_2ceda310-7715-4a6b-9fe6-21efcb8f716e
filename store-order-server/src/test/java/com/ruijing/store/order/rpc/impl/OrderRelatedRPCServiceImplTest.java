package com.ruijing.store.order.rpc.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.search.client.request.Request;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.gaea.api.enums.CategoryTagTypeEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.other.dto.OrderCategoryAmountDTO;
import com.ruijing.store.order.api.base.other.dto.OrderFundCardCacheRequestDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.base.other.dto.SyncStatementStatusDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.rpc.client.OrderFundCardCacheClient;
import com.ruijing.store.order.rpc.client.ResearchFundCardServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderRelatedRPCServiceImplTest extends MockBaseTestCase {

    @org.mockito.Mock
    private OrderSearchBoostService orderSearchBoostService;

    @InjectMocks
    private OrderRelatedRPCServiceImpl orderRelatedRPCService;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private ResearchBaseService researchBaseService;

    public static class Mock {
        @MockMethod(targetClass = RpcContext.class)
        public static ProviderContext getProviderContext() {
            ProviderContext providerContext = ProviderContext.getProviderContext();
            HashMap<String, Object> callAttachments = new HashMap<>();
            RjSessionInfo rjSessionInfo = new RjSessionInfo();
            rjSessionInfo.setOrgId(2);
            callAttachments.put("RJ_SESSION_INFO", rjSessionInfo);
            providerContext.setCallAttachments(callAttachments);
            return providerContext;
        }

        // @MockMethod(targetClass = OrderRelatedRPCServiceImpl.class)
        @MockMethod
        private Map<Integer, Map<String, BigDecimal>> getCategoryAmount(OrderRelatedRPCServiceImpl self, List<Integer> orderIdList, CategoryTagTypeEnum categoryTagTypeEnum) {
            if (orderIdList.get(0) == -1) {
                return new HashMap<>();
            }
            return PrivateAccessor.invoke(self, "getCategoryAmount", orderIdList, CategoryTagTypeEnum.FEE_TAG_TYPE);
        }

        @MockMethod(targetClass = OrderStatementService.class)
        Integer batchUpdateStatementStatus(List<SyncStatementStatusDTO> syncStatementStatusDTOS) {
            return 1;
        }
    }

    @Test
    public void batchUpdateStatementStatus() {
        orderRelatedRPCService.batchUpdateStatementStatus(New.list(new SyncStatementStatusDTO()));
    }

    @Test
    public void checkDeptHasOrder() {
        List<Integer> deptIdList = New.list(123);
        Mockito.when(orderSearchBoostService.searchCountByRequest(Mockito.any(Request.class))).thenReturn(0L);
        RemoteResponse<Boolean> response = orderRelatedRPCService.checkDeptHasOrder(deptIdList);
        Assert.assertTrue(response.isSuccess());
        Assert.assertFalse(response.getData());
    }

    @Test
    public void getFeeCategoryAmount() throws Exception {
        OrderMasterCommonReqDTO orderMasterCommonReqDTO = new OrderMasterCommonReqDTO();
        orderMasterCommonReqDTO.setOrderMasterIds(New.list(123, 456));

        OrderDetailDO orderDetailDO1 = new OrderDetailDO();
        orderDetailDO1.setFmasterid(123);
        orderDetailDO1.setFbidamount(new BigDecimal("100"));
        orderDetailDO1.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO1.setFbidprice(new BigDecimal("50"));
        orderDetailDO1.setFeeTypeTag("测试分析费");
        orderDetailDO1.setCategoryTag("服务");

        OrderDetailDO orderDetailDO11 = new OrderDetailDO();
        orderDetailDO11.setFmasterid(123);
        orderDetailDO11.setFbidamount(new BigDecimal("50"));
        orderDetailDO11.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO11.setFbidprice(new BigDecimal("25"));
        orderDetailDO11.setFeeTypeTag("测试分析费");
        orderDetailDO11.setCategoryTag("服务");

        OrderDetailDO orderDetailDO12 = new OrderDetailDO();
        orderDetailDO12.setFmasterid(123);
        orderDetailDO12.setFbidamount(new BigDecimal("50"));
        orderDetailDO12.setFcancelquantity(new BigDecimal("0"));
        orderDetailDO12.setFbidprice(new BigDecimal("25"));
        orderDetailDO12.setFeeTypeTag("实验耗材费");
        orderDetailDO12.setCategoryTag("耗材");

        OrderDetailDO orderDetailDO2 = new OrderDetailDO();
        orderDetailDO2.setFmasterid(456);
        orderDetailDO2.setFbidamount(new BigDecimal("1000"));
        orderDetailDO2.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO2.setFbidprice(new BigDecimal("500"));
        orderDetailDO2.setFeeTypeTag("实验耗材费");
        orderDetailDO2.setCategoryTag("耗材");

        OrderDetailDO orderDetailDO21 = new OrderDetailDO();
        orderDetailDO21.setFmasterid(456);
        orderDetailDO21.setFbidamount(new BigDecimal("1000"));
        orderDetailDO21.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO21.setFbidprice(new BigDecimal("500"));
        orderDetailDO21.setFeeTypeTag("测试分析费");
        orderDetailDO21.setCategoryTag("服务");

        List<OrderDetailDO> orderDetailList = New.list(orderDetailDO1, orderDetailDO11, orderDetailDO12, orderDetailDO2, orderDetailDO21);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.anyList())).thenReturn(orderDetailList);
        RemoteResponse<List<OrderCategoryAmountDTO>> response = orderRelatedRPCService.getFeeCategoryAmount(orderMasterCommonReqDTO);
        Assert.assertTrue(response.getData().size() >= 1);

        orderMasterCommonReqDTO.setOrderMasterIds(New.list(-1));
        RemoteResponse<List<OrderCategoryAmountDTO>> responseEmpty = orderRelatedRPCService.getFeeCategoryAmount(orderMasterCommonReqDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(responseEmpty.getData()));
    }

    @Test
    public void getCategoryTypeAmount() throws Exception {
        OrderMasterCommonReqDTO orderMasterCommonReqDTO = new OrderMasterCommonReqDTO();
        orderMasterCommonReqDTO.setOrderMasterIds(New.list(123, 456));

        OrderDetailDO orderDetailDO1 = new OrderDetailDO();
        orderDetailDO1.setFmasterid(123);
        orderDetailDO1.setFbidamount(new BigDecimal("100.00"));
        orderDetailDO1.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO1.setFbidprice(new BigDecimal("50.12"));
        orderDetailDO1.setFeeTypeTag("测试分析费");
        orderDetailDO1.setCategoryTag("服务");

        OrderDetailDO orderDetailDO11 = new OrderDetailDO();
        orderDetailDO11.setFmasterid(123);
        orderDetailDO11.setFbidamount(new BigDecimal("50.12"));
        orderDetailDO11.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO11.setFbidprice(new BigDecimal("25.52"));
        orderDetailDO11.setFeeTypeTag("测试分析费");
        orderDetailDO11.setCategoryTag("服务");

        OrderDetailDO orderDetailDO12 = new OrderDetailDO();
        orderDetailDO12.setFmasterid(123);
        orderDetailDO12.setFbidamount(new BigDecimal("50"));
        orderDetailDO12.setFcancelquantity(new BigDecimal("0"));
        orderDetailDO12.setFbidprice(new BigDecimal("25"));
        orderDetailDO12.setFeeTypeTag("实验耗材费");
        orderDetailDO12.setCategoryTag("耗材");

        OrderDetailDO orderDetailDO2 = new OrderDetailDO();
        orderDetailDO2.setFmasterid(456);
        orderDetailDO2.setFbidamount(new BigDecimal("1000.123456"));
        orderDetailDO2.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO2.setFbidprice(new BigDecimal("500"));
        orderDetailDO2.setFeeTypeTag("实验耗材费");
        orderDetailDO2.setCategoryTag("耗材");

        OrderDetailDO orderDetailDO21 = new OrderDetailDO();
        orderDetailDO21.setFmasterid(456);
        orderDetailDO21.setFbidamount(new BigDecimal("1000"));
        orderDetailDO21.setFcancelquantity(new BigDecimal("1"));
        orderDetailDO21.setFbidprice(new BigDecimal("500"));
        orderDetailDO21.setFeeTypeTag("测试分析费");
        orderDetailDO21.setCategoryTag("服务");

        List<OrderDetailDO> orderDetailList = New.list(orderDetailDO1, orderDetailDO11, orderDetailDO12, orderDetailDO2, orderDetailDO21);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.anyList())).thenReturn(orderDetailList);
        RemoteResponse<List<OrderCategoryAmountDTO>> response = orderRelatedRPCService.getCategoryTypeAmount(orderMasterCommonReqDTO);
        Assert.assertTrue(response.getData().size() >= 1);

        // empty result
        orderMasterCommonReqDTO.setOrderMasterIds(New.list(-1));
        RemoteResponse<List<OrderCategoryAmountDTO>> responseEmpty = orderRelatedRPCService.getCategoryTypeAmount(orderMasterCommonReqDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(responseEmpty.getData()));
    }

    @Test
    public void saveFundCardCache(){

        OrderMasterDO orderMasterDO1 = new OrderMasterDO();
        orderMasterDO1.setId(1);
        orderMasterDO1.setForderamounttotal(new BigDecimal(500));
        OrderMasterDO orderMasterDO2 = new OrderMasterDO();
        orderMasterDO2.setId(2);
        orderMasterDO2.setForderamounttotal(new BigDecimal(600));
        // mock 订单list
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.any())).thenReturn(New.list(orderMasterDO1,orderMasterDO2));

        FundCardDTO fundCardDTO1 = new FundCardDTO();
        fundCardDTO1.setId("1");
        fundCardDTO1.setBalanceAmount(new BigDecimal(6000));
        FundCardDTO fundCardDTO2 = new FundCardDTO();
        fundCardDTO2.setId("2");
        fundCardDTO2.setBalanceAmount(new BigDecimal(12000));

        // mock 经费卡list
        Mockito.when(researchFundCardServiceClient.getFundCardListByCardIds(Mockito.any(),Mockito.any())).thenReturn( New.list(fundCardDTO1,fundCardDTO2));

        OrderFundCardDTO orderFundCardDTO1 = new OrderFundCardDTO();
        orderFundCardDTO1.setOrderId(1);
        orderFundCardDTO1.setSequence(1);
        Mockito.when(orderFundCardCacheClient.findOrderFundCardCache(Mockito.any())).thenReturn(New.list(orderFundCardDTO1));


        RefFundcardOrderDTO refFundcardOrderDTO1 = new RefFundcardOrderDTO();
        refFundcardOrderDTO1.setOrderId("1");
        RefFundcardOrderDTO refFundcardOrderDTO2 = new RefFundcardOrderDTO();
        refFundcardOrderDTO2.setOrderId("2");

        Mockito.when(refFundcardOrderService.findByOrderId(Mockito.any())).thenReturn(New.list(refFundcardOrderDTO1,refFundcardOrderDTO2));

        OrderFundCardCacheRequestDTO orderRequestDTO1 = new OrderFundCardCacheRequestDTO();
        orderRequestDTO1.setFundCardId("1");
        orderRequestDTO1.setOrderId(1);

        Mockito.when(researchBaseService.isNewOrder(Mockito.any(), Mockito.any())).thenReturn(true);

        // 测试不是第一次换卡
        RemoteResponse<Boolean> responseNotFirst = orderRelatedRPCService.saveFundCardCaches(New.list(orderRequestDTO1),"orgCode");
        Assert.assertTrue(responseNotFirst.isSuccess());

        // 测试第一次换卡
        orderRequestDTO1.setOrderId(2);
        orderRequestDTO1.setFundCardId("2");
        RemoteResponse<Boolean> responseFirst = orderRelatedRPCService.saveFundCardCaches(New.list(orderRequestDTO1),"orgCode");
        Assert.assertTrue(responseFirst.isSuccess());

        // 模拟获取orgCode失败
        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setCode("1111");
        Mockito.when(userClient.getOrgById(Mockito.anyInt())).thenReturn(organizationDTO);

        responseFirst = orderRelatedRPCService.saveFundCardCaches(New.list(orderRequestDTO1),"orgCode");
        Assert.assertTrue(responseFirst.isSuccess());
    }
}