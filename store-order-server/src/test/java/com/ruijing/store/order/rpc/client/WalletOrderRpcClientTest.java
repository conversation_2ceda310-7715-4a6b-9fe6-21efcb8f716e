package com.ruijing.store.order.rpc.client;

import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.shop.wallet.api.dto.WalletOrderReturnDTO;
import com.ruijing.shop.wallet.api.service.WalletOrderService;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

public class WalletOrderRpcClientTest extends MockBaseTestCase {

    @InjectMocks
    private WalletOrderRpcClient walletOrderRpcClient;

    @Mock
    private WalletOrderService walletOrderService;

    @Test
    public void findRefundByReturnNo() {
        PageApiResult<List<WalletOrderReturnDTO>> response = new PageApiResult<>();
        response.setData(Arrays.asList(new WalletOrderReturnDTO()));
        Mockito.when(walletOrderService.queryOrderReturnPage(Mockito.any())).thenReturn(response);
        List<WalletOrderReturnDTO> result = walletOrderRpcClient.findRefundByReturnNo("test");
        Assert.assertTrue("error", result != null);
    }
}
