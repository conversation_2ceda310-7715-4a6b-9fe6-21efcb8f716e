package com.ruijing.store.order.sysu;

import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnLogDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class SYSUGoodsReturnRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private SYSUGoodsReturnRPCServiceImpl sysuGoodsReturnRPCService;

    @Mock
    private CommonGoodsReturnService commonGoodsReturnService;

    @Mock
    private GoodsReturnMapper goodsReturnMapper;

    @Mock
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;


    @Test
    public void generateReturnNo() {
        Mockito.when(commonGoodsReturnService.createReturnNo(Mockito.anyString())).thenReturn("test1");
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderNo("test");
        RemoteResponse<GoodsReturnDTO> response = sysuGoodsReturnRPCService.generateReturnNo(request);
        Assert.assertTrue("error", response.isSuccess());
    }

    @Test
    public void findByOrderId() {
        GoodsReturn g = new GoodsReturn();
        g.setOrderId(1);
        g.setId(1);
        Mockito.when(goodsReturnMapper.findIdByOrderIdIn(Mockito.anyList())).thenReturn(Collections.singletonList(g));
        GoodsReturnLogDO log = new GoodsReturnLogDO();
        log.setReturnId(1);
        Mockito.when(goodsReturnLogDOMapper.findByReturnIdIn(Mockito.anyList())).thenReturn(Collections.singletonList(log));

        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderIdList(Collections.singletonList(1));
        RemoteResponse<List<GoodsReturnLogDTO>> response = sysuGoodsReturnRPCService.findByOrderId(request);
        Assert.assertTrue(response.isSuccess());
    }
}
