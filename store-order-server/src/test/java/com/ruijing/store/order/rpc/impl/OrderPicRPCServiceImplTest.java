package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.other.dto.OrderPicDTO;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.model.OrderPic;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class OrderPicRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderPicRPCServiceImpl orderPicRPCService;

    @Mock
    private OrderPicMapper orderPicMapper;

    @Test
    public void testFindPicByOrderId() {
        List<String> orderIdList = New.list("DC201805104379401", "DC202105254039901");
        List<OrderPic> orderPicList = New.list();
        OrderPic orderPic = new OrderPic();
        orderPic.setOrderNo("DC201805104379401");
        orderPic.setPic("https://www.gray.rjmart.cn/store/api/upload/image/normal/201805/28e8278b-8131-49cf-888a-a467c02ad608.jpg");
        orderPicList.add(orderPic);
        orderPic = new OrderPic();
        orderPic.setOrderNo("DC202105254039901");
        orderPic.setPic("https://www.gray.rjmart.cn/store/api/upload/image/normal/201805/28e8278b-8131-49cf-888a-a467c02ad608.jpg");
        orderPicList.add(orderPic);

        Mockito.when(orderPicMapper.batchSelectByOrderNo(Mockito.anyList())).thenReturn(orderPicList);
        RemoteResponse<List<OrderPicDTO>> response = orderPicRPCService.findPicByOrderNo(orderIdList);
        Preconditions.isTrue(response.isSuccess());
    }

    @Test
    public void testDeletePicByOrderId() {
        Integer orderId = 1341;
        List<Integer> orderNoList = New.list(orderId);
        List<OrderPic> orderPicList = New.list();
        OrderPic orderPic = new OrderPic();
        orderPic.setOrderNo("DC201805104379401");
        orderPic.setPic("https://www.gray.rjmart.cn/store/api/upload/image/normal/201805/28e8278b-8131-49cf-888a-a467c02ad608.jpg");
        orderPicList.add(orderPic);

        Mockito.when(orderPicMapper.batchDeleteById(Mockito.anyList())).thenReturn(1);
        RemoteResponse response = orderPicRPCService.deletePicById(orderNoList);
        Preconditions.isTrue(response.isSuccess());
    }

    @Test
    public void testBatchInsertOrderPic() {
        List<OrderPicDTO> orderPicList = New.list();
        OrderPicDTO orderPic = new OrderPicDTO();
        orderPic.setOrderNo("DCetstetsd");
        orderPic.setPicUrl("https://www.gray.rjmart.cn/setsetsdjlfajtest.jpg");
        orderPicList.add(orderPic);
        OrderPicDTO orderPic1 = new OrderPicDTO();
        orderPic1.setOrderNo("DCzxzxzxzxzx");
        orderPic1.setPicUrl("https://www.gray.rjmart.cn/zxzxzxzxzzzxtest.jpg");
        orderPicList.add(orderPic1);

        Mockito.when(orderPicMapper.batchInsert(Mockito.anyList())).thenReturn(2);
        RemoteResponse response = orderPicRPCService.batchInsertOrderPic(orderPicList);
        Preconditions.isTrue(response.isSuccess());
    }
}
