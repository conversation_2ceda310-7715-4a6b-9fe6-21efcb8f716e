package com.ruijing.store.order.base.core.translator;

import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationDTO;
import com.ruijing.store.order.base.core.enums.ElectronicSignEnum;
import org.junit.Test;

public class ElectronicSignTranslatorTest {

    @Test
    public void electronicSignOperationDTO2ElectronicSignInfoVO() {
        ElectronicSignOperationDTO electronicSignOperationDTO = new ElectronicSignOperationDTO();
        electronicSignOperationDTO.setElectronicSignEnable(ElectronicSignEnum.CHOOSABLE.getType());
        electronicSignOperationDTO.setOrgWithoutCode(true);
        electronicSignOperationDTO.setUserWithoutCode(true);
        ElectronicSignTranslator.electronicSignOperationDTO2ElectronicSignInfoVO(electronicSignOperationDTO);


        electronicSignOperationDTO.setElectronicSignEnable(ElectronicSignEnum.FORBID.getType());
        ElectronicSignTranslator.electronicSignOperationDTO2ElectronicSignInfoVO(electronicSignOperationDTO);
        ElectronicSignTranslator.electronicSignOperationDTO2ElectronicSignInfoVO(null);
    }
}