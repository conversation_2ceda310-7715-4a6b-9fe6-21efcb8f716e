package com.ruijing.store.order.rpc.client;


import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderResultDTO;
import com.reagent.research.statement.api.order.service.StatementOrderApi;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.research.statement.api.statement.service.StatementSearchApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class StatementPlatformClientTest extends MockBaseTestCase {

    @InjectMocks
    private StatementPlatformClient statementPlatformClient;

    @Mock
    private StatementOrderApi statementOrderApi;

    @Mock
    private StatementSearchApi statementSearchApi;

    @Test
    public void updateWaitingStatement() {
        WaitingStatementOrderResultDTO resultDTO = new WaitingStatementOrderResultDTO();
        RemoteResponse<List<WaitingStatementOrderResultDTO>> response = RemoteResponse.<List<WaitingStatementOrderResultDTO>>custom().setSuccess().setData(Arrays.asList(resultDTO));
        Mockito.when(statementOrderApi.updateWaitingStatementOrders(Mockito.any())).thenReturn(response);

        WaitingStatementOrderRequestDTO requestDTO = new WaitingStatementOrderRequestDTO();
        List<WaitingStatementOrderResultDTO> result = statementPlatformClient.updateWaitingStatement(Arrays.asList(requestDTO));
        Assert.assertTrue(result.size() != 0);
    }

    @Test
    public void updateWaitingStatementTimeStatusOrders() {
        WaitingStatementOrderResultDTO resultDTO = new WaitingStatementOrderResultDTO();
        RemoteResponse<List<WaitingStatementOrderResultDTO>> response = RemoteResponse.<List<WaitingStatementOrderResultDTO>>custom().setSuccess().setData(Arrays.asList(resultDTO));
        Mockito.when(statementOrderApi.updateWaitingStatementTimeStatusOrders(Mockito.any())).thenReturn(response);

        List<WaitingStatementOrderResultDTO> result = statementPlatformClient.updateWaitingStatementTimeStatusOrders(Arrays.asList(1), 1);
        Assert.assertTrue(result.size() != 0);
    }

    @Test
    public void constructStatementIdMap() {

        Integer statementId = 123;

        OrderMasterSearchDTO masterSearch = new OrderMasterSearchDTO();
        masterSearch.setStatementId(123);
        String orgCode = "orgCode";

        RemoteResponse<List<StatementResultDTO>> response = RemoteResponse.<List<StatementResultDTO>>custom()
                .setData(New.list(new StatementResultDTO(){{setId(statementId.longValue());}})).setSuccess();
        Mockito.when(statementSearchApi.findStatementByIds(Mockito.any())).thenReturn(response);

        // 空列表
        Map<Long, StatementResultDTO> resMap = statementPlatformClient.constructStatementIdMap(New.list(), orgCode);
        Assert.assertEquals(0L, resMap.size());

        // 有值列表
        resMap = statementPlatformClient.constructStatementIdMap(New.list(masterSearch), orgCode);
        Assert.assertNotEquals(0L, resMap.size());
    }
}
