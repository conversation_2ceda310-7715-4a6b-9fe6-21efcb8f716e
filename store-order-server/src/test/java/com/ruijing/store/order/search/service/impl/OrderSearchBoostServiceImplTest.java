package com.ruijing.store.order.search.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.AggregationResult;
import com.ruijing.search.client.response.AggsResultItem;
import com.ruijing.search.client.response.Response;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.api.search.enums.OrderAggregationSortFieldEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.utils.MyMockUtils;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class OrderSearchBoostServiceImplTest extends MockBaseTestCase {


    @InjectMocks
    private OrderSearchBoostServiceImpl orderSearchBoostServiceImpl;

    @org.mockito.Mock
    private OrderSearchRPCServiceClient orderSearchRPCServiceClient;

    @Test
    public void searchOrderByStatementIdsTest() {

        Response response = new Response();
        response.setRecordList(New.list());

        List<Integer> statementIdList = New.list(123);
        SortOrderEnum orderDateSort = SortOrderEnum.ASC;

        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);
        List<OrderMasterSearchDTO> orderMasterSearchDTOS
                = orderSearchBoostServiceImpl.searchOrderByStatementIds(statementIdList, orderDateSort);
        Assert.assertTrue(orderMasterSearchDTOS.size() == 0);
        statementIdList = New.list();
        orderDateSort = SortOrderEnum.DESC;
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);
        orderMasterSearchDTOS
                = orderSearchBoostServiceImpl.searchOrderByStatementIds(statementIdList, orderDateSort);
        Assert.assertTrue(orderMasterSearchDTOS.size() == 0);

    }

    @Test
    public void testCommonSearch() throws NoSuchFieldException, IllegalAccessException {
        // mock input
        OrderSearchParamDTO searchParamDTO = new OrderSearchParamDTO();
        MyMockUtils.setThreadLocalField(orderSearchBoostServiceImpl, "countWarn", false);

        Response response = new Response();
        response.setRecordList(New.list());
        response.setTotalHits(0);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);
        searchParamDTO.setBuyerContactMan("bilibili");
        searchParamDTO.setIncludeOrderIdList(New.list(19980));
        searchParamDTO.setBuyerIdList(New.list(2454));
        searchParamDTO.setOrderType(0);
        searchParamDTO.setFirstCategoryIdList(New.list(123));
        SearchPageResultDTO<OrderMasterSearchDTO> resultDTO = orderSearchBoostServiceImpl.commonSearch(searchParamDTO);
        Assert.assertTrue(resultDTO.getRecordList().size() == 0);
    }

    @Test
    public void searchRequestTransform() {
        OrderSearchParamDTO searchParamDTO = new OrderSearchParamDTO();
        Request request = orderSearchBoostServiceImpl.searchRequestTransform(searchParamDTO);
        Assert.assertTrue(request.getPageSize() > 0);
    }

    @Test
    public void countOrderBySearchRequest() {
        String jsonResponse = "{\"tookInMillis\":2,\"errorMessage\":null,\"totalHits\":249,\"queryId\":null,\"recordList\":[],\"aggsResult\":{\"aggsResultItems\":[{\"avg\":0.0,\"sum\":0.0,\"min\":0.0,\"max\":0.0,\"value\":0.0,\"statResult\":null,\"json\":\"{\\\"statusCount\\\":{\\\"doc_count_error_upper_bound\\\":0,\\\"sum_other_doc_count\\\":0,\\\"buckets\\\":[{\\\"key\\\":\\\"3\\\",\\\"doc_count\\\":102},{\\\"key\\\":\\\"10\\\",\\\"doc_count\\\":55},{\\\"key\\\":\\\"5\\\",\\\"doc_count\\\":42},{\\\"key\\\":\\\"6\\\",\\\"doc_count\\\":19},{\\\"key\\\":\\\"11\\\",\\\"doc_count\\\":12},{\\\"key\\\":\\\"20\\\",\\\"doc_count\\\":11},{\\\"key\\\":\\\"8\\\",\\\"doc_count\\\":6},{\\\"key\\\":\\\"15\\\",\\\"doc_count\\\":1},{\\\"key\\\":\\\"4\\\",\\\"doc_count\\\":1}]}}\"}]},\"analysisResult\":{\"total\":3,\"successFull\":3,\"failed\":0,\"dsl\":null},\"success\":true}";
        Response response = JsonUtils.fromJson(jsonResponse, Response.class);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);

        Request request = new Request();
        Map<Integer, Integer> resultMap = orderSearchBoostServiceImpl.countOrderBySearchRequest(request);
        Assert.assertTrue(resultMap.get(OrderStatusEnum.WaitingForConfirm.getValue()) > 0);
    }

    @Test
    public void searchCountByRequest() {
        List<Integer> deptIdList = New.list(123);
        Request request = new Request();
        request.addFilter(new TermFilter("fbuydepartmentid", deptIdList));

        Response response = new Response();
        response.setTotalHits(321L);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);

        long countResult = orderSearchBoostServiceImpl.searchCountByRequest(request);
        Assert.assertTrue(Objects.equals(321L, countResult));
    }

    @Test
    public void aggOrderAmountAndCount() throws Exception {
        // mock input
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setAggField(OrderSearchFieldEnum.SUPPLIER_ID);
        paramDTO.setSuppIdList(New.list(655,1216));
        paramDTO.setSortItem(OrderAggregationSortFieldEnum.AMOUNT_DESC);
        paramDTO.setOrgId(3);
        paramDTO.setOrgIdList(New.list(62));
        paramDTO.setExcludeOrgIdList(New.list(1,999));
        paramDTO.setExcludeSuppIdList(New.list(679,680));
        paramDTO.setOrderTypeList(New.list(0,2));

        // reflect some global data
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "orderMasterTable", 1);
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "ORDER_SEARCH_INDEX", "order");
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "AMOUNT_ITEM", "amountItem");
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "QUANTITY_ITEM", "quantityItem");

        // mock result
        String responseJson = "{\"tookInMillis\":26,\"errorMessage\":null,\"totalHits\":126156,\"queryId\":null,\"recordList\":[],\"aggsResult\":{\"aggsResultItems\":[{\"avg\":0.0,\"sum\":0.0,\"min\":0.0,\"max\":0.0,\"value\":0.0,\"statResult\":null,\"json\":\"{\\\"aggItem\\\":{\\\"doc_count_error_upper_bound\\\":0,\\\"sum_other_doc_count\\\":0,\\\"buckets\\\":[{\\\"key\\\":\\\"655\\\",\\\"doc_count\\\":123681,\\\"amountItem\\\":{\\\"value\\\":5.4856865711E8},\\\"quantityIem\\\":{\\\"value\\\":123681}},{\\\"key\\\":\\\"1216\\\",\\\"doc_count\\\":2475,\\\"amountItem\\\":{\\\"value\\\":1.138297876E7},\\\"quantityIem\\\":{\\\"value\\\":2475}}]}}\"}]},\"analysisResult\":{\"total\":3,\"successFull\":3,\"failed\":0,\"dsl\":null},\"success\":true}";
        Response response = JsonUtils.fromJson(responseJson, Response.class);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);

        List<OrderAggregationResultDTO> resultList = orderSearchBoostServiceImpl.aggOrderAmountAndCount(paramDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
    }


    @Test
    public void aggOrderAmountDateHistogram() throws Exception {
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setIntervalDate(new IntervalDTO());
        paramDTO.setSortOrderEnum(SortOrderEnum.ASC);

        Response response = new Response();
        AggregationResult aggResult = new AggregationResult();
        AggsResultItem aggItem = new AggsResultItem();
        String exampleJson = "{\n" +
                "    \"aggItem\" : {\n" +
                "      \"buckets\" : [\n" +
                "        {\n" +
                "          \"key_as_string\" : \"2020-01-01\",\n" +
                "          \"key\" : 1577836800000,\n" +
                "          \"doc_count\" : 16,\n" +
                "          \"amountItem\" : {\n" +
                "            \"value\" : 14145.06\n" +
                "          },\n" +
                "          \"quantityIem\" : {\n" +
                "            \"value\" : 16\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  }";
        aggItem.setJson(exampleJson);
        aggResult.addAggsResultItem(aggItem);
        response.setAggsResult(aggResult);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);

        List<OrderDateHistogramResultDTO> resultList = orderSearchBoostServiceImpl.aggOrderAmountDateHistogram(paramDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        aggResult = new AggregationResult();
        response.setAggsResult(aggResult);
        resultList = orderSearchBoostServiceImpl.aggOrderAmountDateHistogram(paramDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(resultList));
    }

    @Test
    public void aggProductAmountAndCount() {
        // mock input
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setAggField(OrderSearchFieldEnum.BRAND_ID);
        paramDTO.setSortItem(OrderAggregationSortFieldEnum.AMOUNT_DESC);
        paramDTO.setStartTime("2021-05-31 00:00:00");
        paramDTO.setEndTime("2021-05-31 23:59:59");

        // for filter item
        paramDTO.setProductCode("productCode");
        paramDTO.setBrandName("brandName");
        paramDTO.setProductIdList(New.list(1L));

        // reflect some global data
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "orderMasterTable",1);
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "ORDER_SEARCH_INDEX","order");
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "AMOUNT_ITEM","amountItem");
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "QUANTITY_ITEM","quantityItem");
        MyMockUtils.setThreadLocalField(OrderSearchBoostServiceImpl.class, "NESTED_TABLE","order_detail");

        // mock result
//        String responseJson = "{\"nestedTerms\":{\"doc_count\":16176,\"productTerms\":{\"doc_count_error_upper_bound\":-1,\"sum_other_doc_count\":7922,\"buckets\":[{\"key\":\"1\",\"doc_count\":231,\"amountItem\":{\"value\":1.0622378913E8},\"quantityIem\":{\"value\":58786.0}},{\"key\":\"2\",\"doc_count\":4701,\"amountItem\":{\"value\":6354495.6},\"quantityIem\":{\"value\":15040.0}}]}}}";
        String responseJson = "{\"nestedTerms\":{\"meta\":{},\"doc_count\":32,\"filterAggItem\":{\"meta\":{},\"doc_count\":32,\"productTerms\":{\"doc_count_error_upper_bound\":0,\"sum_other_doc_count\":0,\"buckets\":[{\"key\":\"2\",\"doc_count\":6,\"amountItem\":{\"value\":15793.12},\"quantityIem\":{\"value\":9.0}},{\"key\":\"387\",\"doc_count\":1,\"amountItem\":{\"value\":510.0},\"quantityIem\":{\"value\":10.0}},{\"key\":\"1152\",\"doc_count\":1,\"amountItem\":{\"value\":422.73},\"quantityIem\":{\"value\":3.0}},{\"key\":\"1107\",\"doc_count\":1,\"amountItem\":{\"value\":347.6},\"quantityIem\":{\"value\":2.0}},{\"key\":\"1701\",\"doc_count\":1,\"amountItem\":{\"value\":237.0},\"quantityIem\":{\"value\":2.0}},{\"key\":\"909\",\"doc_count\":1,\"amountItem\":{\"value\":200.0},\"quantityIem\":{\"value\":1.0}},{\"key\":\"1423\",\"doc_count\":9,\"amountItem\":{\"value\":162.0},\"quantityIem\":{\"value\":9.0}},{\"key\":\"5\",\"doc_count\":1,\"amountItem\":{\"value\":61.88},\"quantityIem\":{\"value\":1.0}},{\"key\":\"910\",\"doc_count\":1,\"amountItem\":{\"value\":50.0},\"quantityIem\":{\"value\":1.0}}]}}}}";
        Response response = new Response();
        AggregationResult aggResult = new AggregationResult();
        AggsResultItem aggsResultItem = new AggsResultItem();
        aggsResultItem.setJson(responseJson);
        aggResult.addAggsResultItem(aggsResultItem);
        response.setAggsResult(aggResult);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any(Request.class))).thenReturn(response);

        List<OrderAggregationResultDTO> resultList = orderSearchBoostServiceImpl.aggProductAmountAndCount(paramDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
    }

    @Test
    public void countOrderByStatus() {
        OrderStatisticsParamDTO request = new OrderStatisticsParamDTO();
        request.setBuyerIds(New.list(3917));
        request.setOrgIds(New.list(4));
        request.setSuppIds(New.list(163));
        request.setDeptIds(New.list(903));
        request.setSpeciesList(New.list(ProcessSpeciesEnum.NORMAL.getValue()));

        String responseJson = "{\"tookInMillis\":2,\"errorMessage\":null,\"totalHits\":1,\"queryId\":null,\"recordList\":[],\"aggsResult\":{\"aggsResultItems\":[{\"avg\":0.0,\"sum\":0.0,\"min\":0.0,\"max\":0.0,\"value\":0.0,\"statResult\":null,\"json\":\"{\\\"statusCount\\\":{\\\"doc_count_error_upper_bound\\\":0,\\\"sum_other_doc_count\\\":0,\\\"buckets\\\":[{\\\"key\\\":\\\"8\\\",\\\"doc_count\\\":1}]}}\"}]},\"analysisResult\":{\"total\":3,\"successFull\":3,\"failed\":0,\"dsl\":null},\"success\":true}\n";
        Response response = JsonUtils.fromJson(responseJson, Response.class);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any())).thenReturn(response);

        Map<Integer, Integer> resultMap = orderSearchBoostServiceImpl.countOrderByStatus(request);
        Assert.assertTrue(resultMap.size() > 0);
    }

    @Test
    public void aggReturnAmountByEntities() {
        String resString = "{\"tookInMillis\":3,\"errorMessage\":null,\"totalHits\":121,\"queryId\":null,\"recordList\":[],\"aggsResult\":{\"aggsResultItems\":[{\"avg\":0.0,\"sum\":0.0,\"min\":0.0,\"max\":0.0,\"value\":0.0,\"statResult\":null,\"json\":\"{\\\"suppAggTerm\\\":{\\\"doc_count_error_upper_bound\\\":0,\\\"sum_other_doc_count\\\":0,\\\"buckets\\\":[{\\\"key\\\":\\\"1216\\\",\\\"doc_count\\\":38,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"3831\\\",\\\"doc_count\\\":38,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"655\\\",\\\"doc_count\\\":27,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"570\\\",\\\"doc_count\\\":5,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"50000186\\\",\\\"doc_count\\\":4,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"165\\\",\\\"doc_count\\\":3,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"50000181\\\",\\\"doc_count\\\":3,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"50000145\\\",\\\"doc_count\\\":2,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}},{\\\"key\\\":\\\"110\\\",\\\"doc_count\\\":1,\\\"returnAmountSumAgg\\\":{\\\"value\\\":0.0}}]}}\"}]},\"analysisResult\":{\"total\":3,\"successFull\":3,\"failed\":0,\"dsl\":\"{\\\"from\\\":0,\\\"size\\\":0,\\\"timeout\\\":\\\"5000ms\\\",\\\"query\\\":{\\\"bool\\\":{\\\"must\\\":[{\\\"range\\\":{\\\"forderdate\\\":{\\\"from\\\":\\\"2022-01-01 00:00:00\\\",\\\"to\\\":\\\"2022-05-31 00:00:00\\\",\\\"include_lower\\\":true,\\\"include_upper\\\":false,\\\"boost\\\":1.0}}}],\\\"filter\\\":[{\\\"terms\\\":{\\\"fuserid\\\":[118],\\\"boost\\\":1.0}}],\\\"adjust_pure_negative\\\":true,\\\"boost\\\":1.0}},\\\"track_total_hits\\\":2147483647,\\\"aggregations\\\":{\\\"suppAggTerm\\\":{\\\"terms\\\":{\\\"field\\\":\\\"fsuppid\\\",\\\"size\\\":10000,\\\"min_doc_count\\\":1,\\\"shard_min_doc_count\\\":0,\\\"show_term_doc_count_error\\\":false,\\\"order\\\":[{\\\"_count\\\":\\\"desc\\\"},{\\\"_key\\\":\\\"asc\\\"}]},\\\"aggregations\\\":{\\\"returnAmountSumAgg\\\":{\\\"sum\\\":{\\\"field\\\":\\\"return_amount\\\"}}}}}}\"},\"success\":true}";
        Response searchRes = JsonUtils.fromJson(resString, Response.class);
        Mockito.when(orderSearchRPCServiceClient.search(Mockito.any())).thenReturn(searchRes);

        // input
        StatisticsManagerParamDTO paramDTO = new StatisticsManagerParamDTO();
        paramDTO.setStartTime("2022-01-01 00:00:00");
        paramDTO.setEndTime("2022-01-01 00:00:01");
        paramDTO.setOrgIdList(New.list(118));

        // mock and assert
        List<GoodsReturnAggResDTO> aggResDTOList = orderSearchBoostServiceImpl.aggReturnAmountByEntities(paramDTO);
        Assert.assertNotNull(aggResDTOList);
    }
}
