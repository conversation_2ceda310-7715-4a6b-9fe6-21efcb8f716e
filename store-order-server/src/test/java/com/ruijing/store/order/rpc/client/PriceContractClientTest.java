package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.apply.service.application.ApplyMasterExtensionService;
import com.ruijing.store.contract.PriceContractBaseService;
import com.ruijing.store.contract.dto.ListPriceContractRequestDTO;
import com.ruijing.store.contract.dto.PriceContractDTO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;
import java.util.Objects;
public class PriceContractClientTest extends MockBaseTestCase {

   @InjectMocks
   private PriceContractClient priceContractClient;

   @Mock
   private PriceContractBaseService priceContractBaseService;

   @Mock
   private ApplyMasterExtensionService applyMasterExtensionService;

   @Test
   public void testGetPriceContractBaseList() {
       List<Long> contractList = New.list();
       contractList.add(32L);
       // construct mock data
       List<PriceContractDTO> priceContractDTOList = New.list();
       PriceContractDTO priceContractDTO = new PriceContractDTO();
       priceContractDTO.setId(32L);
       priceContractDTO.setContractNo("CT202102250007");
       priceContractDTOList.add(priceContractDTO);
       RemoteResponse<List<PriceContractDTO>> response = RemoteResponse.<List<PriceContractDTO>>custom().setData(priceContractDTOList).setSuccess();
       Mockito.when(priceContractBaseService.listPriceContractByIdList(Mockito.any(ListPriceContractRequestDTO.class))).thenReturn(response);
       List<PriceContractBaseBO> result = priceContractClient.getPriceContractBaseList(contractList);
       Assert.assertEquals(result.get(0).getContractNo(), "CT202102250007");
   }

   @Test
   public void testGetPriceContractId() {
       // mock input data
       Integer ftbuyappid = 123;
       List<ApplyMasterExtensionDTO> applyExtraInfoList = New.list();
       ApplyMasterExtensionDTO applyMasterExtensionDTO = new ApplyMasterExtensionDTO();
       applyMasterExtensionDTO.setApplyId(123L);
       applyMasterExtensionDTO.setContractId(999L);
       applyExtraInfoList.add(applyMasterExtensionDTO);
       RemoteResponse<List<ApplyMasterExtensionDTO>> response = RemoteResponse.<List<ApplyMasterExtensionDTO>>custom().setData(applyExtraInfoList).setSuccess();

       // normal not empty data
       Mockito.when(applyMasterExtensionService.listByApplyIds(Mockito.any(ApplyMasterExtensionQueryDTO.class))).thenReturn(response);
       Long priceContractId = priceContractClient.getPriceContractId(ftbuyappid);
       Assert.assertTrue(Objects.equals(999L, priceContractId));

       // empty response
       response = RemoteResponse.<List<ApplyMasterExtensionDTO>>custom().setData(New.list()).setSuccess();
       Mockito.when(applyMasterExtensionService.listByApplyIds(Mockito.any(ApplyMasterExtensionQueryDTO.class))).thenReturn(response);
       priceContractId = priceContractClient.getPriceContractId(ftbuyappid);
       Assert.assertEquals(priceContractId, null);

   }
}