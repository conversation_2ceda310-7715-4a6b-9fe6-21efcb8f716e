package com.ruijing.store.order.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.alibaba.testable.processor.annotation.EnablePrivateAccess;
import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageProductDTO;
import com.ruijing.store.apply.dto.application.manage.ApplyManageRequestDTO;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.MoneyControlOperatingEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.BidClient;
import com.ruijing.store.order.rpc.client.OrderAddressRPCClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import org.apache.ibatis.annotations.Param;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.*;

@EnablePrivateAccess(srcClass = ApplicationBaseServiceImpl.class)
public class ApplicationBaseServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private ApplicationBaseServiceImpl applicationBaseServiceImpl;

    @org.mockito.Mock
    private ApplicationBaseClient applicationBaseClient;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    public static class Mock {
        @MockMethod(targetClass = RefFundcardOrderService.class)
        List<RefFundcardOrderDTO> findByOrderIdList(List<Integer> orderIdList) {
            return New.list(new RefFundcardOrderDTO());
        }

        @MockMethod(targetClass = ApplicationBaseClient.class)
        public Boolean updateApplyManageProductUsage(ApplyManageRequestDTO applyManageRequestDTO){
            return true;
        }

        @MockMethod(targetClass = ApplicationBaseServiceImpl.class)
        private List<ApplyManageProductDTO> getProductListByOperation(OrderMasterDO orderMasterDO, Integer operation, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
            return New.list(new ApplyManageProductDTO());
        }

        @MockMethod(targetClass = ApplicationBaseServiceImpl.class)
        public Date getCreateTimeByOrderType(OrderMasterDO orderMasterDO){
            return new Date();
        }

        @MockMethod(targetClass = OrderDetailMapper.class)
        List<OrderDetailDO> findByIdIn(@Param("idCollection") Collection<Integer> idCollection){
            return New.list(new OrderDetailDO());
        }

        @MockMethod(targetClass = OrderDetailMapper.class)
        List<OrderDetailDO> findByFmasterid(@Param("fmasterid")Integer fmasterid){
            return New.list(new OrderDetailDO());
        }

        @MockMethod(targetClass = BidClient.class)
        public List<BidMasterDTO> findBidMasterByBidNoList(List<String> bidNoList){
            BidMasterDTO bidMasterDTO = new BidMasterDTO();
            bidMasterDTO.setCreatedTime(new Date());
            return New.list(bidMasterDTO);
        }

        @MockMethod(targetClass = ApplicationBaseClient.class)
        public ApplicationMasterDTO getApplicationMasterByApplyId(Integer applyId, Boolean withDetail) {
            ApplicationMasterDTO applicationMasterDTO = new ApplicationMasterDTO();
            applicationMasterDTO.setCreateTime(new Date());
            return applicationMasterDTO;
        }

        @MockMethod(targetClass = ApplicationBaseServiceImpl.class)
        public Boolean updateApplyManageProductUsage(OrderMasterDO orderMasterDO, Integer operation, String cardId, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
            if(customMockFileValueMap.get("ApplicationBaseServiceImpl.updateApplyManageProductUsage.exception") != null){
                throw new NullPointerException();
            }
            return true;
        }

        @MockMethod(targetClass = OrderOtherLogClient.class)
        public void createOrderDockingLog(String orderNo,String orgCode,String paramInfo,String resultInfo,String operation,String result){}

        @MockMethod(targetClass = ApplicationBaseServiceImpl.class)
        private Integer getStatementType(OrderMasterDO orderMasterDO){
            return 1;
        }

        @MockMethod(targetClass = ApplicationBaseClient.class)
        public List<ApplyMasterExtensionDTO> findPermitCardByApplicationId(ApplyMasterExtensionQueryDTO request){
            return New.list(new ApplyMasterExtensionDTO());
        }
    }

    private static Map<Object, Object> customMockFileValueMap = new HashMap();

    @Test
    public void updateApplyManageProductUsage() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFusercode(OrgConst.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN);
        orderMasterDO.setId(1);

        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = New.list(new GoodsReturnInfoDetailVO());
        Boolean returnFlag = applicationBaseServiceImpl.updateApplyManageProductUsage(orderMasterDO,
                ApplyManageOperationEnum.PRODUCT_RETURN.getValue(), null, goodsReturnInfoDetailVOS);
        Assert.assertTrue(returnFlag);

        orderMasterDO.setFusercode(OrgConst.JI_NAN_DA_XUE);
        applicationBaseServiceImpl.updateApplyManageProductUsage(orderMasterDO,
                ApplyManageOperationEnum.PRODUCT_RETURN.getValue(), null, goodsReturnInfoDetailVOS);
    }

    @Test
    public void getProductListByOperation() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFusercode(OrgConst.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN);
        orderMasterDO.setId(1);
        GoodsReturnInfoDetailVO goodsReturnInfoDetailVO = new GoodsReturnInfoDetailVO();
        goodsReturnInfoDetailVO.setDetailId("1");
        PrivateAccessor.invoke(applicationBaseServiceImpl, "getProductListByOperation", orderMasterDO, ApplyManageOperationEnum.PRODUCT_RETURN.getValue(), New.list(goodsReturnInfoDetailVO));
    }

    @Test
    public void getCreateTimeByOrderType() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        // 采购单
        orderMasterDO.setOrderType(OrderTypeEnum.PURCHASE_ORDER.getCode());
        PrivateAccessor.invoke(applicationBaseServiceImpl, "getCreateTimeByOrderType", orderMasterDO);
        // 采购单
        orderMasterDO.setOrderType(OrderTypeEnum.BID_ORDER.getCode());
        PrivateAccessor.invoke(applicationBaseServiceImpl, "getCreateTimeByOrderType", orderMasterDO);
    }
}