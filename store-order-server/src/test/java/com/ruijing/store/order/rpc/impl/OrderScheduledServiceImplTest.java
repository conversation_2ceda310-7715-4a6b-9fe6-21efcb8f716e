package com.ruijing.store.order.rpc.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.reagent.research.financial.docking.dto.order.OrderStatusQueryResult;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderResultDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.business.service.OrderMasterForScheduledService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

public class OrderScheduledServiceImplTest extends MockBaseTestCase {
    @InjectMocks
    private OrderScheduledServiceImpl orderScheduledService;

    @Mock
    private GoodsReturnService goodsReturnService;

    @Mock
    private OrganizationClient organizationClient;

    @Mock
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Mock
    private TPIOrderClient tpiOrderClient;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Mock
    private GoodsReturnMapper goodsReturnMapper;

    @Mock
    private CommonGoodsReturnService commonGoodsReturnService;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @Mock
    private ResearchStatementClient researchStatementClient;

    @Mock
    private TimeoutStatisticsService timeoutStatisticsService;

    @Mock
    private UserClient userClient;

    @Mock
    private SysConfigClient sysConfigClient;

    @Mock
    private OrderMasterForScheduledService orderMasterForScheduledService;

    @Mock
    private StatementPlatformClient statementPlatformClient;

    @Mock
    private OrderDetailRelatedService orderDetailRelatedService;

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Mock
    private OrderMasterCommonService orderMasterCommonService;

    @Mock
    private OrderEmailHandler orderEmailHandler;

    @Mock
    private ResearchCustomClient researchCustomClient;

    @Test
    public void returnAutoReceive() {
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setOrderId(3);
        goodsReturn.setOrgId(3);
        goodsReturn.setGoodsReturnDetailJSON("[]");
        Mockito.when(goodsReturnService.getUnFinishReturnSuccess()).thenReturn(Arrays.asList(goodsReturn));

        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setId(3);
        Mockito.when(organizationClient.findByIdList(Mockito.anyList())).thenReturn(Arrays.asList(organizationDTO));

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(3);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.doNothing().when(supplierGoodsReturnService).returnSuccess(Mockito.any(), Mockito.any(), Mockito.any());

        Mockito.when(goodsReturnMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        Mockito.when(goodsReturnMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        Mockito.when(goodsReturnMapper.findByOrderId(Mockito.anyInt())).thenReturn(Arrays.asList(goodsReturn));
        Mockito.when(researchCustomClient.agree2ReturnOrderForSYSU(Mockito.anyInt(), Mockito.any())).thenReturn(true);

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setId(1);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(Arrays.asList(orderDetailDO));
        Mockito.when(orderDetailMapper.loopUpdateByIdIn(Mockito.anyList())).thenReturn(1);
        Mockito.doNothing().when(commonGoodsReturnService).saveReturnOperationLog(Mockito.any(GoodsReturnLogDO.class));
        Mockito.when(orderMasterForTPIService.asyncOrderReturn(Mockito.any(), Mockito.anyString())).thenReturn(ListenableFutures.forValue(true));

        orderScheduledService.returnAutoReceive();

        organizationDTO = new OrganizationDTO();
        organizationDTO.setId(1);
        organizationDTO.setCode("ZHONG_SHAN_XIAO_XUE");
        Mockito.when(organizationClient.findByIdList(Mockito.anyList())).thenReturn(Arrays.asList(organizationDTO));
        orderScheduledService.returnAutoReceive();
    }



    @Test
    public void orderTimeOutStatistics() throws Exception {
        OrganizationDTO organizationDTO = new OrganizationDTO();
        organizationDTO.setId(1);
        Mockito.when(timeoutStatisticsService.deleteAll()).thenReturn(1);
        Mockito.when(userClient.getAllOrg()).thenReturn(Arrays.asList(organizationDTO));
        Map<String, Map<String, BaseConfigDTO>> baseConfigDTOKeyMap = new HashMap<>();
        HashMap<String, BaseConfigDTO>  baseConfigDTOMap = new HashMap<>();
        baseConfigDTOMap.put("test",new BaseConfigDTO());
        baseConfigDTOKeyMap.put("test",baseConfigDTOMap);
        Mockito.when(sysConfigClient.findByCodes(Mockito.any())).thenReturn(baseConfigDTOKeyMap);

        // 私有方法测试
        ArrayList<OrganizationConfigDTO> organizationConfigDTOS = new ArrayList<>();
        OrganizationConfigDTO organizationConfigDTO = new OrganizationConfigDTO();
        organizationConfigDTO.setConfigCode("BALANCE_CYCLE_LIMIT_DAYS");
        organizationConfigDTOS.add(organizationConfigDTO);

        OrderTimeOutDTO orderTimeOutDTO = new OrderTimeOutDTO();
        orderTimeOutDTO.setId(5);
        orderTimeOutDTO.setFuserId(1);
        orderTimeOutDTO.setFbuyDepartmentId(1);
        Mockito.when(orderMasterForScheduledService.findTimeOutBalance(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(orderTimeOutDTO));
        Mockito.when(orderMasterForScheduledService.findTimeOutExamine(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(orderTimeOutDTO));
        Mockito.when(orderDetailRelatedService.findFmasterIdByReturnStatus(Mockito.any(),Mockito.any())).thenReturn(New.set(1,2,3));
        Mockito.when(statementPlatformClient.updateWaitingStatement(Mockito.any())).thenReturn(New.list(new WaitingStatementOrderResultDTO()));

        Table<Integer, Integer, Integer> statisticsTable = HashBasedTable.create();
        statisticsTable.put(1, 1, 1);

//        List<TimeoutStatisticsDTO> list = New.list(new TimeoutStatisticsDTO());
//        PowerMockito.when(orderScheduledService2, "calculateTimeoutStatistics",
//                TimeOutBusinessType.BALANCE ,
//                New.list(new OrganizationConfigDTO()),
//                New.list(new OrganizationConfigDTO()),
//                statisticsTable)
//                .thenReturn(list);

        Mockito.when(timeoutStatisticsService.insertBatch(Mockito.any())).thenReturn(1);

        RemoteResponse response = orderScheduledService.orderTimeOutStatistics();
        Assert.assertTrue(response.isSuccess());

        Mockito.when(statementPlatformClient.updateWaitingStatement(Mockito.any())).thenThrow(new IllegalStateException("error"));
        response = orderScheduledService.orderTimeOutStatistics();
        Assert.assertTrue(response.isSuccess());
    }

}
