package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.HashMap;

public class OrderAcceptRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderAcceptRpcServiceImpl orderAcceptRpcServiceImpl;

    @Mock
    private OrderAcceptService orderAcceptService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private SysConfigClient sysConfigClient;

    @Test
    public void testGetCommonPrintData() {
        Mockito.when(orderAcceptService.userAcceptOrder(new OrderReceiptParamDTO())).thenReturn(new ReceiptOrderResponseDO());
        orderAcceptRpcServiceImpl.userAcceptOrder(new OrderReceiptParamDTO());
    }

}