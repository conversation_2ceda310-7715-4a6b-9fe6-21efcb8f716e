package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.electronicsign.api.dto.*;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.electronicsign.api.service.ElectronicSignUserConfigService;
import com.ruijing.store.electronicsign.api.service.ElectronicSignatureOperationService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

public class ElectronicSignServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private ElectronicSignServiceClient electronicSignServiceClient;

    @org.mockito.Mock
    private ElectronicSignUserConfigService electronicSignUserConfigClient;

    @org.mockito.Mock
    private ElectronicSignatureOperationService electronicSignOperationService;

    public static class Mock {
        @MockMethod(targetClass = ElectronicSignatureOperationService.class)
        public RemoteResponse<ElectronicSignResultDTO> electronicSign(ElectronicSignDTO dto) {
            ElectronicSignResultDTO signResultDTO = new ElectronicSignResultDTO();
            signResultDTO.setFlag(true);
            RemoteResponse<ElectronicSignResultDTO> remoteResponse = RemoteResponse.<ElectronicSignResultDTO>custom().setSuccess().setData(signResultDTO).build();
            return remoteResponse;
        }

        @MockMethod(targetClass = ElectronicSignatureOperationService.class)
        public RemoteResponse<ElectronicSignOperationDTO> searchOperationConfig(SearchOperationConfigDTO dto){
            RemoteResponse<ElectronicSignOperationDTO> remoteResponse = RemoteResponse.<ElectronicSignOperationDTO>custom().setSuccess().setData(new ElectronicSignOperationDTO()).build();
            return remoteResponse;
        }

        @MockMethod(targetClass = ElectronicSignUserConfigService.class)
        public RemoteResponse validatePassword(ValidatePasswordDTO var1){
            RemoteResponse<ElectronicSignOperationDTO> remoteResponse = RemoteResponse.<ElectronicSignOperationDTO>custom().setSuccess().setData(new ElectronicSignOperationDTO()).build();
            return remoteResponse;
        }

    }

    @Test
    public void findUserConfigByGuid() {
        RemoteResponse<UserConfigDTO> remoteResponse = RemoteResponse.<UserConfigDTO>custom().setSuccess().setData(new UserConfigDTO()).build();
        Mockito.when(electronicSignUserConfigClient.findByGuid(Mockito.anyString())).thenReturn(remoteResponse);
        electronicSignServiceClient.findUserConfigByGuid("test");
    }

    @Test
    public void saveElectronicSign() {
        electronicSignServiceClient.saveElectronicSign(new ElectronicSignDTO());
    }

    @Test
    public void searchOperationConfig() {
        electronicSignServiceClient.searchOperationConfig("test", "test", 1, ElectronicSignatureOperationEnum.BID_FINAL_AUDIT);
    }

    @Test
    public void validatePassword() {
        electronicSignServiceClient.validatePassword(new ValidatePasswordDTO());
    }

}