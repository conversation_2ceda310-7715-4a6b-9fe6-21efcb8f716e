package com.ruijing.store.order.gateway.buyercenter.controller;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderStatusLimitDaysRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.text.ParseException;
import java.util.Collections;
import java.util.List;

public class BuyerOrderGWControllerTest extends MockBaseTestCase {

    @InjectMocks
    private BuyerOrderGWController buyerOrderGWController;

    @org.mockito.Mock
    private OrderDetailRelatedService orderDetailRelatedService;

    @org.mockito.Mock
    private BuyerOrderService buyerOrderService;

    public static class Mock {
        @MockMethod(targetClass = CancelOrderManageService.class)
        public void cancelOfflineOrder(CancelOrderReqDTO cancelOrderReqDTO){
        }
    }

    @Test
    public void testListInvoiceInfoByOrder() throws Exception {
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setGuid("aldkjfklasdjksj");
        rjSessionInfo.setOrgId(45);
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderId(45679);
        Mockito.when(orderDetailRelatedService.listInvoiceByOrder(Mockito.anyLong(), Mockito.anyString(), Mockito.anyInt())).thenReturn(Collections.emptyList());
        RemoteResponse<List<OrderInvoiceInfoVO>> response = buyerOrderGWController.listInvoiceInfoByOrder(rjSessionInfo, request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void limitDaysAfterFinish() {
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        OrderStatusLimitDaysRequest limitDaysRequest = new OrderStatusLimitDaysRequest();
        Mockito.when(buyerOrderService.limitDaysAfterFinish(Mockito.any(RjSessionInfo.class), Mockito.any(OrderStatusLimitDaysRequest.class))).thenReturn(true);
        RemoteResponse<Boolean> response = buyerOrderGWController.limitDaysAfterFinish(rjSessionInfo, limitDaysRequest);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void getOrderDetail() throws ParseException {
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderId(123456);
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        Mockito.when(orderDetailRelatedService.getOrderDetail(Mockito.any(LoginUserInfoBO.class), Mockito.any(OrderBasicParamDTO.class), Mockito.anyBoolean())).thenReturn(orderInfoVO);
        RemoteResponse<OrderInfoVO> orderDetail = buyerOrderGWController.getOrderDetail(new RjSessionInfo(), request);
        Assert.assertTrue(orderDetail.isSuccess());
    }

    @Test
    public void cancelOrderOffline() {
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setUserId(123L);
        ApplyCancelOrderReqDTO applyCancelOrderReq = new ApplyCancelOrderReqDTO();
        applyCancelOrderReq.setOrderId(123);
        applyCancelOrderReq.setFcancelreason("123");
        buyerOrderGWController.cancelOrderOffline(rjSessionInfo,applyCancelOrderReq);
    }
}