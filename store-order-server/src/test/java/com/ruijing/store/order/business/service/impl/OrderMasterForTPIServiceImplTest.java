package com.ruijing.store.order.business.service.impl;

import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.TimeUtil;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;


public class OrderMasterForTPIServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderMasterForTPIServiceImpl orderMasterForTPIService;

    @Mock
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private UserClient userClient;

    @Mock
    private TPIOrderClient tpiOrderClient;

    @Mock
    private OrderApprovalLogService orderApprovalLogService;

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Mock
    private OrderEmailHandler orderEmailHandler;

    @Mock
    private DockingExtraService dockingExtraService;

    @Test
    public void updateThirdPlatformOrder() throws InterruptedException {
        OrderMasterDO o = new OrderMasterDO();
        o.setForderno("rtest");
        o.setFusercode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        o.setForderamounttotal(BigDecimal.valueOf(100.00));
        o.setForderdate(new Date());
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);

        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(null, null, null, null, null, null);

        Mockito.when(businessDockingRPCClient.getBusinessDockingOrderInfo(Mockito.any())).thenReturn(new BusinessDockingDTO());
        Mockito.when(userClient.getUserInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(new UserBaseInfoDTO());
        Mockito.when(orderApprovalLogService.asyncInsertOrderApprovalLog(Mockito.any())).thenReturn(ListenableFutures.forValue(null));
//        Mockito.when(orderMasterForTPIService.updateOrderStatusAsync(Mockito.any())).thenReturn(AsyncResults.forValue(true));
        Mockito.when(purchaseApprovalLogClient.addApprovalLog(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(dockingExtraService.saveOrUpdateDockingExtra(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);

        Mockito.doNothing().when(orderEmailHandler).sendOrderGenerateEmailToSupp(Mockito.any());

        OrderDetailDO detailDO = new OrderDetailDO();
        detailDO.setId(1119);
        detailDO.setFquantity(BigDecimal.ONE);
        detailDO.setProductSn(1L);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.any())).thenReturn(Arrays.asList(detailDO));
        UpdateOrderParamDTO param = new UpdateOrderParamDTO();
        param.setStatus(4);
        boolean success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        o.setStatus(-1);
        param.setStatus(8);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setFusercode(DockingConstant.HUA_NAN_NONG_YE_DA_XUE);
        o.setStatus(6);
        param.setStatus(10);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setStatus(4);
        param.setStatus(3);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setStatus(9);
        param.setStatus(3);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setStatus(19);
        param.setStatus(3);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setStatus(-1);
        param.setStatus(8);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setFusercode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        o.setStatus(-1);
        param.setStatus(8);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        o.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        o.setStatus(4);
        o.setForderdate(TimeUtil.getSettingDate(-100L));
        param.setStatus(5);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(o);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

        Mockito.doThrow(new IllegalStateException("error")).when(orderOtherLogClient).createOrderDockingLog(null, null, null, null, null, null);
        o.setStatus(4);
        param.setStatus(5);
        success = orderMasterForTPIService.updateThirdPlatformOrder(param);
        Assert.assertTrue(success);

    }

    @Test
    public void orderReturn() {
        Mockito.doThrow(new IllegalStateException("error")).when(orderOtherLogClient).createOrderDockingLog(null, null, null, null, null, null);
        Mockito.when(tpiOrderClient.orderReturn(Mockito.any(OrgRequest.class))).thenReturn(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        orderMasterDO.setForderdate(new Date());
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(orderMasterDO);


        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setGoodsReturnStatus(1);
        goodsReturn.setGoodsReturnDetailJSON("[{\"detailId\":184404,\"goodsName\":\"红细胞溶解液\",\"goodsCode\":\"200001045514\",\"specification\":\"100ml\",\"brand\":\"凯基生物(KeyGEN)\",\"goodsPicturePath\":\"https://images-test.rjmart.cn/image/6m11988i/02b10273-470c-4471-a1ae-27c52ac0959b.jpg\",\"price\":77.8,\"quantity\":2,\"amount\":155.6,\"returnReason\":\"尺寸/容量与商品描述不符\",\"remark\":\"454545\",\"dangerousTag\":\"其他\",\"productId\":\"200001045514\",\"unit\":\"瓶\"},{\"detailId\":184405,\"goodsName\":\"EZ DNA Methylation-Startup™ Kit(甲基化）\",\"goodsCode\":\"200001047123\",\"specification\":\"50rxns\",\"brand\":\"深圳(NUNCn)\",\"goodsPicturePath\":\"https://images-test.rjmart.cn/image/0e9410b6/a9b0f559-f95f-48c8-bbd6-e4518eea7da9.png\",\"price\":5.89,\"quantity\":1,\"amount\":5.89,\"returnReason\":\"收到商品少件/破损/污渍等\",\"remark\":\"4575\",\"dangerousTag\":\"其他\",\"productId\":\"200001047123\",\"unit\":\"套\"}]");
        orderMasterForTPIService.orderReturn(goodsReturn, DockingConstant.HUA_NAN_SHI_FAN_DA_XUE);

        goodsReturn.setGoodsReturnStatus(5);
        orderMasterForTPIService.orderReturn(goodsReturn, DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
    }
}
