package com.ruijing.store.order.business.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.tpi.tpiclient.message.req.order.OrderReq;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceDTO;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceQueryDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.api.search.dto.OrderStatisticsParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.minor.mapper.RefCouponPurchaserDOMapper;
import com.ruijing.store.order.base.minor.mapper.RefInvoiceOrderMapper;
import com.ruijing.store.order.base.minor.model.RefCouponPurchaserDO;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.orgondemand.ClinicalOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

public class CancelOrderManageServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private CancelOrderManageServiceImpl cancelOrderManageService;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private ClinicalOrderService clinicalOrderService;

    @org.mockito.Mock
    private CacheClient cacheClient;

    @org.mockito.Mock
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @org.mockito.Mock
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @org.mockito.Mock
    private StatementPlatformClient statementPlatformClient;

    @org.mockito.Mock
    private BizShopProductServiceClient bizShopProductServiceClient;

    @org.mockito.Mock
    private InvoiceClient invoiceClient;

    @org.mockito.Mock
    private OrderEmailHandler orderEmailHandler;

    @org.mockito.Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @org.mockito.Mock
    private RefCouponPurchaserDOMapper refCouponPurchaserDOMapper;

    @org.mockito.Mock
    private ProductClient productClient;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private RefInvoiceOrderMapper refInvoiceOrderMapper;

    @org.mockito.Mock
    private DepartmentRpcClient departmentRpcClient;

    @org.mockito.Mock
    private DockingExtraService dockingExtraService;

    @org.mockito.Mock
    private HuaNongServiceClient huaNongServiceClient;

    @org.mockito.Mock
    private ApplicationBaseService applicationBaseService;

    public static class Mock {
        @MockMethod(targetClass = OrderManageService.class)
        public void orderFundCardUnFreeze(OrderMasterDO orderMasterDO) {
        }

        @MockMethod(targetClass = OrderManageService.class)
        public boolean addSku(Integer suppId, Integer orderMasterId, Integer orderType) {
            return true;
        }

        @MockMethod(targetClass = ApplicationBaseService.class)
        public Boolean updateApplyManageProductUsage(OrderMasterDO orderMasterDO, Integer operation,List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
            return true;
        }

        @MockMethod(targetClass = OrderMasterMapper.class)
        public int updateByPrimaryKeySelective(OrderMasterDO record) {
            return 1;
        }

        @MockMethod(targetClass = CancelOrderManageServiceImpl.class)
        public void deleteInvoiceByOrderId(String orgCode, List<Integer> orderIdList) {
        }

        @MockMethod(targetClass = CacheClient.class)
        public void controlRepeatOperation(String uniqKey, Integer timeLimit){
        }

        @MockMethod(targetClass = CacheClient.class)
        public void removeCache(String uniqKey){
        }

        @MockMethod(targetClass = RefCouponPurchaserDOMapper.class)
        public int updateByPrimaryKeySelective(RefCouponPurchaserDO record){
            return 1;
        }

        @MockMethod(targetClass = PurchaseApprovalLogClient.class)
        public List<ApplyRefBusinessPriceDTO> listApplyRefBusinessPrice(ApplyRefBusinessPriceQueryDTO applyRefBusinessPriceQueryDTO){
            return New.list(new ApplyRefBusinessPriceDTO()) ;
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        public void sendPurchaserApplyCancelEmailToSupplier(OrderMasterDO orderMaster) {
        }


        @MockMethod(targetClass = ApplicationBaseClient.class)
        public ApplicationMasterDTO getApplicationMasterByApplyId(Integer applyId, Boolean withDetail) {
            ApplicationMasterDTO applicationMasterDTO = new ApplicationMasterDTO();
            applicationMasterDTO.setApplyNumber("1");
            applicationMasterDTO.setCreateTime(new Date());
            ApplicationDetailDTO applicationDetailDTO = new ApplicationDetailDTO();
            applicationDetailDTO.setQuantity(new BigDecimal(1));
            applicationDetailDTO.setBidPrice(new BigDecimal(2));
            applicationMasterDTO.setDetails(New.list(applicationDetailDTO));
            return applicationMasterDTO;
        }

        @MockMethod(targetClass = OrderSearchBoostService.class)
        public Map<Integer, Integer> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO) {
            return new HashMap<>();
        }

        @MockMethod(targetClass = SysConfigClient.class)
        public String getConfigByOrgCodeAndConfigCode(String orgCode, String configCode) throws CallRpcException {
            return "2";
        }

        @MockMethod(targetClass = UserClient.class)
        public OrganizationDTO findSimpleOrgInfoByIdFromEnum(Integer id) {
            return new OrganizationDTO();
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void saveOrderPic(List<String> picUrlList, String orderNo) {
        }


        @MockMethod(targetClass = OrderOtherLogClient.class)
        public void createOrderDockingLog(String orderNo,String orgCode,String paramInfo
                ,String resultInfo,String operation,String result){
        }

        @MockMethod(targetClass = CancelOrderManageServiceImpl.class)
        private void huaNongCancelOrder(OrderMasterDO orderMasterDO, String cancelReason){ }

        @MockMethod(targetClass = UserClient.class)
        public UserBaseInfoDTO getUserInfo(Integer userId, Integer orgId) throws CallRpcException{
            UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
            userBaseInfoDTO.setJobnumber("123");
            return userBaseInfoDTO;
        }

        @MockMethod(targetClass = CancelOrderManageServiceImpl.class)
        private void addCancelOrderLog(OrderMasterDO orderMasterDO, Integer orderApprovalEnumValue, String reason){}

        @MockMethod(targetClass = CancelOrderManageServiceImpl.class)
        private void thirdPartyCancelOrder(OrderMasterDO orderMasterDO, String cancelReason){}

        @MockMethod(targetClass = CancelOrderManageServiceImpl.class)
        private void generalCancleOrder(OrderMasterDO orderMasterDO){}
    }

    @Test
    public void huaNongCancelOrder() throws Exception {
        Class<CancelOrderManageServiceImpl> cancelOrderManageServiceImplClass
                = (Class<CancelOrderManageServiceImpl>) cancelOrderManageService.getClass();

        Method huaNongCancelOrder = cancelOrderManageServiceImplClass
                .getDeclaredMethod("huaNongCancelOrder", OrderMasterDO.class ,String.class);
        huaNongCancelOrder.setAccessible(true);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFuserid(1);
        orderMasterDO.setFbuydepartmentid(1);

        // 获取不到华农用户信息
        Mockito.when(departmentRpcClient.findByUserIdAndOrgIdAndDepName(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        try{
            huaNongCancelOrder.invoke(cancelOrderManageService, orderMasterDO,"取消原因");
        }catch (Throwable ex){
            ex.printStackTrace();
            Assert.assertTrue(ex.getCause().getMessage().equals("在锐竞系统中找不到对应的华农用户"));
        }

        // 获取得到华农用户信息
        DepartmentThirdPartyDTO departmentThirdPartyDTO = new DepartmentThirdPartyDTO();
        departmentThirdPartyDTO.setJobNumber("1");
        departmentThirdPartyDTO.setDepartmentId(1);
        Mockito.when(departmentRpcClient.findByUserIdAndOrgIdAndDepName(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(New.list(departmentThirdPartyDTO));

        // dockingExtraList信息为空
        Mockito.when(dockingExtraService.findDockingExtra(Mockito.any())).thenReturn(New.list());
        try{
            huaNongCancelOrder.invoke(cancelOrderManageService, orderMasterDO,"取消原因");
        }catch (Throwable ex){
            Assert.assertTrue(ex.getCause().getMessage().equals("在锐竞系统中找不到对应订单信息"));
        }

        // dockingExtraList信息不为空，取消订单失败
        DockingExtraDTO dockingExtra = new DockingExtraDTO();
        Mockito.when(dockingExtraService.findDockingExtra(Mockito.any())).thenReturn(New.list(dockingExtra));
        // 抛异常
        Mockito.doThrow(new IllegalStateException("抛异常")).when(huaNongServiceClient).cancelOrder(Mockito.any(OrderReq.class),Mockito.anyString(),Mockito.anyString());
        try{
            huaNongCancelOrder.invoke(cancelOrderManageService, orderMasterDO,"取消原因");
        }catch (Throwable ex){
            Assert.assertTrue(ex.getCause().getMessage().equals("华农调用取消订单异常抛异常"));
        }
    }


    @Test
    public void agreeCancelOrder() {
        // mock input
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        Integer cancelManId = 123456;
        cancelOrderReqDTO.setCancelManId(cancelManId.toString());
        cancelOrderReqDTO.setOrderMasterId(123654);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFbuyerid(cancelManId);
        orderMasterDO.setStatus(OrderStatusEnum.SupplierApplyToCancel.value);
        orderMasterDO.setId(123333);

//        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMasterDO);
//        Mockito.doNothing().when(clinicalOrderService).releaseContractAmount(Mockito.anyInt(), Mockito.any(OrderMasterDO.class), Mockito.any(GoodsReturn.class));

        // 正常流程
        cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
        // 能正常跑通表示通过测试
        Assert.assertTrue(true);

        // 采购人同意取消
        cancelOrderReqDTO.setCancelManId("-1");
        cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
        // 能正常跑通表示通过测试
        Assert.assertTrue(true);

        // 华农取消订单
        orderMasterDO.setFusercode(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode());
        cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);

        // 报错
        Mockito.doThrow(new IllegalStateException("error test")).when(orderMasterMapper).selectByPrimaryKey(Mockito.anyInt());
        try {
            cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
        } catch (Exception e) {
            // 能正常跑通表示通过测试
            Assert.assertTrue(true);
        }
    }


    @Test
    public void generalCancleOrder() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<? extends CancelOrderManageServiceImpl> aClass = cancelOrderManageService.getClass();
        Method applyCancelOrder = aClass.getDeclaredMethod("generalCancleOrder",OrderMasterDO.class);
        applyCancelOrder.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFtbuyappid(123);
        orderMasterDO.setFbuyerid(123);
        applyCancelOrder.invoke(cancelOrderManageService,orderMasterDO);

        // 华农取消订单
        orderMasterDO.setFusercode(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode());
        applyCancelOrder.invoke(cancelOrderManageService,orderMasterDO);
    }


    @Test
    public void cancelOfflineOrder() {
        OrderMasterDO orderMaster = new OrderMasterDO();
        orderMaster.setFbuyerid(10);
        orderMaster.setId(100);
        orderMaster.setFundStatus(1);
        orderMaster.setStatus(OrderStatusEnum.WaitingForReceive.getValue());
        orderMaster.setFusercode(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode());
        orderMaster.setFundStatus(1);
        orderMaster.setForderdate(new Date());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMaster);
        Mockito.doNothing().when(cacheClient).controlRepeatOperation(Mockito.anyString(), Mockito.anyInt());
        Mockito.when(orderMasterMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(orderApprovalLogMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.anyString())).thenReturn(new ArrayList<>());
        Mockito.doNothing().when(statementPlatformClient).deleteWaitingStatementByOrderId(Mockito.anyList());
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.anyString());

        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setOrderMasterId(10);
        cancelOrderReqDTO.setCancelManId("10");
        cancelOrderReqDTO.setCancelReason("test");
        cancelOrderManageService.cancelOfflineOrder(cancelOrderReqDTO);
        Assert.assertTrue(true);
    }


    @Test
    public void cancelOrder() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(177219);
        orderMasterDO.setStatus(8);
        orderMasterDO.setFusercode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        orderMasterDO.setForderno("DC202103221306401");
        orderMasterDO.setFbuyerid(123);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        Mockito.when(orderMasterMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(bizShopProductServiceClient.batchUpdateStock(Mockito.any())).thenReturn(true);
        Mockito.doNothing().when(invoiceClient).deleteInvoiceByOrderIds(Mockito.anyList());
        Mockito.when(orderMasterForTPIService.updateThirdPlatformOrder(Mockito.anyInt(), Mockito.anyString())).thenReturn(true);
//        Mockito.doNothing().when(clinicalOrderService).releaseContractAmount(Mockito.anyInt(), Mockito.any(), Mockito.any());
        Mockito.when(refCouponPurchaserDOMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.anyString());
        Mockito.when(productClient.findByIdList(Mockito.anyList())).thenReturn(new ArrayList());
        Mockito.when(orderApprovalLogMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyString())).thenReturn("test");
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.anyString());

        // 待确认取消订单
        ApplyCancelOrderReqDTO applyCancelOrderReqDTO = new ApplyCancelOrderReqDTO();
        applyCancelOrderReqDTO.setFcancelmanid("123");
        applyCancelOrderReqDTO.setStatus(3);
        cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);

        orderMasterDO.setStatus(0);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);

        // 待发货取消订单
        applyCancelOrderReqDTO = new ApplyCancelOrderReqDTO();
        applyCancelOrderReqDTO.setFcancelmanid("123");
        applyCancelOrderReqDTO.setStatus(4);
        applyCancelOrderReqDTO.setFcancelreason("取消");
        orderMasterDO.setStatus(OrderStatusEnum.WaitingForDelivery.getValue());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);

        // 供应商申请取消
        orderMasterDO.setStatus(OrderStatusEnum.SupplierApplyToCancel.getValue());
        applyCancelOrderReqDTO.setOrderId(123);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        try{
            cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);
        }catch (IllegalStateException ex){
            Assert.assertTrue(ex.getMessage().endsWith("供应商已经申请取消订单。请刷新后重试"));
        }

        // 最后分支
        orderMasterDO.setStatus(OrderStatusEnum.OrderReceiveApprovalTwo.getValue());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        try{
            cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);
        }catch (IllegalStateException ex){
            Assert.assertTrue(ex.getMessage().endsWith("只有未确认或者未发货的订单才能取消"));
        }
    }

    @Test
    public void refuseCancelOrder() {

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFbuyerid(111);
        orderMasterDO.setStatus(19);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        Mockito.when(orderMasterMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setOrderMasterId(111);
        cancelOrderReqDTO.setRefuseReason("1111");
        cancelOrderReqDTO.setCancelManId("111");
        cancelOrderManageService.refuseCancelOrder(cancelOrderReqDTO);

    }

    /**
     * 恢复优惠券
     */
    @Test
    public void recoveryCoupon() throws Exception {
        Class<? extends CancelOrderManageServiceImpl> aClass = cancelOrderManageService.getClass();
        Method recoveryCoupon = aClass.getDeclaredMethod("recoveryCoupon",OrderMasterDO.class);
        recoveryCoupon.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFtbuyappid(123);
        recoveryCoupon.invoke(cancelOrderManageService,orderMasterDO);
        Assert.assertTrue(true);
    }

    @Test
    public void applyCancelOrder() throws Exception {
        Class<? extends CancelOrderManageServiceImpl> aClass = cancelOrderManageService.getClass();
        Method applyCancelOrder = aClass.getDeclaredMethod("applyCancelOrder",OrderMasterDO.class
                ,String.class,String.class,String.class,Integer.class);
        applyCancelOrder.setAccessible(true);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFtbuyappid(123);
        orderMasterDO.setFbuyerid(123);
        applyCancelOrder.invoke(cancelOrderManageService,orderMasterDO,"123","123","123",1);
        Assert.assertTrue(true);
    }

    @Test
    public void cancelOrderForWaitingConfirm() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<CancelOrderManageServiceImpl> cancelOrderManageServiceImplClass
                = (Class<CancelOrderManageServiceImpl>) cancelOrderManageService.getClass();

        Method waitingConfirm = cancelOrderManageServiceImplClass
                .getDeclaredMethod("cancelOrderForWaitingConfirm", OrderMasterDO.class ,String.class ,String.class ,String.class);
        waitingConfirm.setAccessible(true);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFuserid(1);
        orderMasterDO.setFbuydepartmentid(1);

//        Mockito.doNothing().when(clinicalOrderService).releaseContractAmount(Mockito.anyInt(), Mockito.any(OrderMasterDO.class), Mockito.any(GoodsReturn.class));
        Mockito.when(applicationBaseService.updateApplyManageProductUsage(Mockito.any(OrderMasterDO.class), Mockito.anyInt(), Mockito.any(), Mockito.anyList())).thenReturn(true);
        Mockito.when(orderMasterForTPIService.updateThirdPlatformOrder(Mockito.anyInt(), Mockito.anyString())).thenReturn(true);
        orderMasterDO.setFbuyerid(1);
        orderMasterDO.setFusercode(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode());
        waitingConfirm.invoke(cancelOrderManageService, orderMasterDO,"cancelMan", "1", "cancelReason");
    }


    @Test
    public void updateCancelOrder() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<CancelOrderManageServiceImpl> cancelOrderManageServiceImplClass
                = (Class<CancelOrderManageServiceImpl>) cancelOrderManageService.getClass();

        Method updateCancelOrder = cancelOrderManageServiceImplClass
                .getDeclaredMethod("updateCancelOrder", Integer.class, Integer.class, String.class, String.class, Date.class, Date.class, String.class);
        updateCancelOrder.setAccessible(true);

        updateCancelOrder.invoke(cancelOrderManageService, 1, 2, "3", "4", new Date(), new Date(), "5");
    }

}