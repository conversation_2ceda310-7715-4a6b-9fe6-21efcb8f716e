package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnRequestDTO;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnStatRequestDTO;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnStatResultDTO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.platform.commons.util.Preconditions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.dao.DataAccessException;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


public class GoodsReturnRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private GoodsReturnRpcServiceImpl goodsReturnRpcServiceImp;

    @Mock
    private GoodsReturnMapper goodsReturnMapper;

    @Mock
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private UserClient userClient;

    @Mock
    private GoodsReturnLogDOMapper goodsReturnLogMapper;

    @Test
    public void goodsReturnSuccessCallBack() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.when(userClient.getOrgById(Mockito.anyInt())).thenReturn(new OrganizationDTO());
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setOrderId(1);
        Mockito.when(goodsReturnMapper.findByOrderIds(Mockito.anyList())).thenReturn(Arrays.asList(goodsReturn));
        Mockito.doNothing().when(supplierGoodsReturnService).returnSuccess(Mockito.any(), Mockito.any(), Mockito.any());
        GoodsReturnRequestDTO request = new GoodsReturnRequestDTO();
        request.setOrderIdList(Arrays.asList(1));
        RemoteResponse<Integer> response = goodsReturnRpcServiceImp.goodsReturnSuccessCallBack(request);
        Assert.assertTrue("失败", response.isSuccess());
    }

    @Test
    public void goodsReturnStats() {
        Integer orderId = 1;
        Integer returnId = 1;
        String testJson = "[{\"detailId\":188506,\"goodsName\":\"Fermentas预染蛋白Marker 10-250KD，MBI预染彩虹蛋白marker（10-250KD）\",\"goodsCode\":\"YA1071-150\",\"specification\":\"500ml\",\"brand\":\"集萃药康(GemPharmatech)\",\"goodsPicturePath\":\"https://images-test.rjmart.cn/image/6m11988i/02b10273-470c-4471-a1ae-27c52ac0959b.jpg\",\"price\":19.2,\"quantity\":1,\"amount\":19.2,\"returnReason\":\"商品为假货\",\"remark\":\"123212\",\"dangerousTag\":\"其他\",\"productId\":\"200001045096\",\"unit\":\"卷\"},{\"detailId\":188507,\"goodsName\":\"MBI预染蛋白marker（170KD），Fermentas预染彩虹蛋白marker(10-170KDa)\",\"goodsCode\":\"200001045093\",\"specification\":\"500ml\",\"brand\":\"耐思(NEST)\",\"goodsPicturePath\":\"https://images-test.rjmart.cn/image/6m11988i/02b10273-470c-4471-a1ae-27c52ac0959b.jpg\",\"price\":35,\"quantity\":1,\"amount\":35,\"returnReason\":\"收到商品少件/破损/污渍等\",\"remark\":\"123123\",\"dangerousTag\":\"其他\",\"productId\":\"200001045093\",\"unit\":\"瓶\"}]";
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setGoodsReturnDetailJSON(testJson);
        goodsReturn.setOrderId(orderId);
        goodsReturn.setId(returnId);

        // 日志
        Mockito.when(goodsReturnLogMapper.findReturnIdByOperationAndUpdateTime(Mockito.anyCollection(), Mockito.any(Date.class), Mockito.any(Date.class))).thenReturn(New.list(returnId));
        // 退货表
        Mockito.when(goodsReturnMapper.findByIdInAndOrgIdIn(Mockito.anyCollection(), Mockito.anyCollection())).thenReturn(New.list(goodsReturn));

        // 其他参数的查询
        OrderMasterDO orderMaster = new OrderMasterDO();
        orderMaster.setId(orderId);
        orderMaster.setInventoryStatus(InventoryStatusEnum.COMPLETE.getCode().byteValue());
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(New.list(orderMaster));

        GoodsReturnStatRequestDTO request = new GoodsReturnStatRequestDTO();
        request.setStartTime(System.currentTimeMillis() - 100000);
        request.setEndTime(System.currentTimeMillis());
        request.setOrgIdList(New.list(123));
        request.setInventoryStatus(InventoryStatusEnum.COMPLETE.getCode());

        RemoteResponse<GoodsReturnStatResultDTO> response = goodsReturnRpcServiceImp.goodsReturnStats(request);
        Assert.assertTrue(response.isSuccess());
        BigDecimal returnAmount = response.getData().getReturnAmount();
        Assert.assertTrue(returnAmount.compareTo(BigDecimal.ZERO) > 0);

        // inventory status为空
        request.setInventoryStatus(null);
        response = goodsReturnRpcServiceImp.goodsReturnStats(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getData().getReturnAmount().compareTo(BigDecimal.ZERO) > 0);
    }
}
