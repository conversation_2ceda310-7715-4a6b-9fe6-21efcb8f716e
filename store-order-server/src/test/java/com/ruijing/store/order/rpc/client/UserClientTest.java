package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.user.api.dto.AccessDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.service.UserDepartmentRoleRpcService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

public class UserClientTest extends MockBaseTestCase {

    @InjectMocks
    private UserClient userClient;

    @org.mockito.Mock
    private UserDepartmentRoleRpcService userDepartmentRoleRpcService;

    @org.mockito.Mock
    private InvoiceClient invoiceClient;

    public static class Mock {
        @MockMethod(targetClass = UserClient.class)
        public OrganizationDTO getOrgById(Integer orgId){
            return new OrganizationDTO();
        }
    }

    @Test
    public void testGetDepartmentAccess() {
        List<Integer> deptIdList = New.list();
        int userId = 123;
        int orgId = 321;

        // mock result
        AccessDTO accessDTO = new AccessDTO();
        RemoteResponse<List<AccessDTO>> response = RemoteResponse.<List<AccessDTO>>custom().setSuccess().setData(New.list(accessDTO)).setMsg("success");
        Mockito.when(userDepartmentRoleRpcService.findUserAccessByUserIdAndDeptIds(orgId, deptIdList, userId)).thenReturn(response);
        List<AccessDTO> result = userClient.getDepartmentAccess(deptIdList, userId, orgId);
        Assert.assertTrue(result != null);
    }

    @Test
    public void testCheckUserInDepartmentAccess() {
        List<Integer> deptIdList = New.list();
        int userId = 123;
        int orgId = 321;
        List<String> accessList = Arrays.asList("access1", "AccessTwo");

        AccessDTO accessDTO = new AccessDTO();
        accessDTO.setCode("access1");
        RemoteResponse<List<AccessDTO>> response = RemoteResponse.<List<AccessDTO>>custom().setSuccess().setData(New.list(accessDTO)).setMsg("success");
        Mockito.when(userDepartmentRoleRpcService.findUserAccessByUserIdAndDeptIds(Mockito.anyInt(), Mockito.anyList(),Mockito.anyInt())).thenReturn(response);
        boolean result1 = userClient.checkUserInDepartmentAccess(userId, deptIdList, accessList, orgId);
        Assert.assertTrue(result1);

        accessDTO.setCode("not in access");
        response = RemoteResponse.<List<AccessDTO>>custom().setSuccess().setData(New.list(accessDTO)).setMsg("success");
        Mockito.when(userDepartmentRoleRpcService.findUserAccessByUserIdAndDeptIds(Mockito.anyInt(), Mockito.anyList(),Mockito.anyInt())).thenReturn(response);
        boolean result2 = userClient.checkUserInDepartmentAccess(userId, deptIdList, accessList, orgId);
        Assert.assertTrue(!result2);
    }

    @Test
    public void getOrgCodeById() {
        userClient.getOrgCodeById(1);
    }
}