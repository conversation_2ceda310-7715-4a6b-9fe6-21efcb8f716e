package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class OrderDetailServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderDetailServiceImpl orderDetailService;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Test
    public void findOrderDetailByIdList() {
        Mockito.when(orderDetailMapper.findByIdIn(Mockito.anyList())).thenReturn(new ArrayList<>());
        OrderDetailReq request = new OrderDetailReq();
        request.setOrderDetailIdList(Arrays.asList(1, 2, 3));
        RemoteResponse<List<OrderDetailDTO>> response = orderDetailService.findOrderDetailByIdList(request);
        Assert.assertTrue("查询失败！", response.isSuccess());
    }

    @Test
    public void findDetailTagValue() {
        Integer orderId = 123;

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setCategoryTag(InboundTypeEnum.REAGENT.getDesc());
        orderDetailDO.setReturnStatus(OrderDetailReturnStatus.SUCCESS.getCode());

        OrderDetailDO orderDetailDO1 = new OrderDetailDO();
        orderDetailDO1.setCategoryTag(InboundTypeEnum.ANIMAL.getDesc());
        orderDetailDO1.setReturnStatus(OrderDetailReturnStatus.RETURNEDGOODS.getCode());

        List<OrderDetailDO> orderDetailList = New.list(orderDetailDO, orderDetailDO1);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(orderDetailList);
        RemoteResponse<Set<Integer>> response = orderDetailService.findDetailTagValue(orderId);
        Assert.assertTrue(response.getData().size() == 1);
    }
}
