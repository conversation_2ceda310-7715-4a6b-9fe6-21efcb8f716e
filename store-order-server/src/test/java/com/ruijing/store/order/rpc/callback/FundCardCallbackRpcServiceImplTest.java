package com.ruijing.store.order.rpc.callback;

import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.fundcard.dto.FreezeCallbackResult;
import com.reagent.research.fundcard.dto.FundCardCompleteCallbackResult;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;

public class FundCardCallbackRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private FundCardCallbackRpcServiceImpl fundCardCallbackRpcService;

    @Mock
    private OrderMasterCommonService orderMasterCommonService;

    @Mock
    private DockingExtraService dockingExtraService;

    @Test
    public void handleFundCardCompleteCallbackResult() {
        RemoteResponse<Integer> response = RemoteResponse.<Integer>custom().setSuccess().setData(1);
        Mockito.when(orderMasterCommonService.updateFundStatusByOrderNo(Mockito.any())).thenReturn(response);

        CallbackRequest<FundCardCompleteCallbackResult> request = new CallbackRequest<>();
        request.setCode(200);
        FundCardCompleteCallbackResult result = new FundCardCompleteCallbackResult();
        result.setSerialNumbers(Arrays.asList("test"));
        request.setData(result);
        RemoteResponse<Boolean> response1 = fundCardCallbackRpcService.handleFundCardCompleteCallbackResult(request);
        Assert.assertTrue(response1.isSuccess());

        request.setCode(500);
        response1 = fundCardCallbackRpcService.handleFundCardCompleteCallbackResult(request);
        Assert.assertTrue(response1.isSuccess());
    }

    @Test
    public void handleFrozenResult() {
        RemoteResponse<Integer> response = RemoteResponse.<Integer>custom().setSuccess().setData(1);
        Mockito.when(orderMasterCommonService.updateFundStatusByOrderNo(Mockito.any())).thenReturn(response);
        Mockito.when(dockingExtraService.saveOrUpdateDockingExtra(Mockito.any())).thenReturn(1);

        CallbackRequest<FreezeCallbackResult> request = new CallbackRequest<>();
        request.setCode(200);
        FreezeCallbackResult result = new FreezeCallbackResult();
        result.setSerialNumber("test");
        request.setData(result);
        RemoteResponse<Boolean> response1 = fundCardCallbackRpcService.handleFrozenReuslt(request);
        Assert.assertTrue(response1.isSuccess());

        request.setCode(500);
        response1 = fundCardCallbackRpcService.handleFrozenReuslt(request);
        Assert.assertTrue(response1.isSuccess());
    }
}