package com.ruijing.store.order.gateway;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.OrderCommonPrintDataDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

/**
 * @auther: Kimmy Tse
 * @Date: 2021/3/17 11:33
 * @Description:
 */
public class OrderManageGWServiceImplTest extends MockBaseTestCase {

    @Mock
    private OrderManageRpcService orderManageRpcService;

    @InjectMocks
    private OrderManageGWServiceImpl orderManageGWService;

    @Test
    public void getCommonPrintData() {
        OrderCommonPrintParamDTO dto = new OrderCommonPrintParamDTO();
        RemoteResponse<OrderCommonPrintDataDTO> response = RemoteResponse.<OrderCommonPrintDataDTO>custom().setSuccess().build();
        //模拟返回自定的单返回值
        Mockito.when(orderManageRpcService.getCommonPrintData(Mockito.any(OrderCommonPrintParamDTO.class))).thenReturn(response);
        RemoteResponse<OrderCommonPrintDataDTO> brandTopList = orderManageGWService.getCommonPrintData(dto);
        Assert.assertTrue(brandTopList.isSuccess());

        //模拟调用抛异常
        Mockito.when(orderManageRpcService.getCommonPrintData(Mockito.any(OrderCommonPrintParamDTO.class))).thenThrow(Exception.class);
        try {
            orderManageGWService.getCommonPrintData(dto);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof Exception);
        }
    }



}