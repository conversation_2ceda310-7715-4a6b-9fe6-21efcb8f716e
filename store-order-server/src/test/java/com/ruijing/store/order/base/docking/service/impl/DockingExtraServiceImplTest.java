package com.ruijing.store.order.base.docking.service.impl;

import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class DockingExtraServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private DockingExtraServiceImpl dockingExtraService;

    @Mock
    private DockingExtraMapper dockingExtraMapper;

    @Test
    public void saveOrUpdateDockingExtra() {
        Mockito.when(dockingExtraMapper.countByInfo(Mockito.anyString())).thenReturn(0L);
        Mockito.when(dockingExtraMapper.insertSelective(Mockito.any())).thenReturn(1);

        Integer affect = dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra("test1", "test2", 0, "message"));
        Assert.assertTrue("error", affect > 0);

        Mockito.when(dockingExtraMapper.countByInfo(Mockito.anyString())).thenReturn(1L);
        Mockito.when(dockingExtraMapper.updateByInfo(Mockito.any())).thenReturn(1);
        Integer affect2 = dockingExtraService.saveOrUpdateDockingExtra(new DockingExtra("test1", "test2", -2, "message"));
        Assert.assertTrue("error", affect2 > 0);
    }
}