package com.ruijing.store.warehouse.service.impl;
import com.alibaba.testable.core.model.MockScope;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.rpc.client.BizExitServiceClient;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.utils.MyMockUtils;
import com.ruijing.store.warehouse.message.bean.OrderBean;
import com.ruijing.store.warehouse.message.bean.WarehouseApplicationBean;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.service.InWareHouseGWService;
import com.ruijing.store.warehouse.utils.GateWayMessageUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.enums.InboundStatus;

import java.util.Collections;
import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/8/26 15:15
 */
public class InWareHouseGWServiceImplMock {

    @MockMethod(targetClass = GateWayMessageUtil.class, scope = MockScope.ASSOCIATED)
    public static RjSessionInfo validateAndGetStoreUser() {
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        sessionInfo.setOrgId(8);
        sessionInfo.setUserType(RjUserTypeEnum.STORE_USER);
        sessionInfo.setUserId(2451L);
        return sessionInfo;
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private List<BizWarehouseEntryDTO> getBizWarehouseEntryDTOListByOrderNo(String orderNo) {
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setStatus(InboundStatus.WAREHOUSING.getValue());
        return New.list(bizWarehouseEntryDTO);
    }

    @MockMethod(targetClass = BizExitServiceClient.class)
    public List<BizWarehouseExitDTO> queryExitByOrderNo(String orderNo) {
        BizWarehouseExitDTO bizWarehouseExitDTO = new BizWarehouseExitDTO();
        bizWarehouseExitDTO.setStatus(InboundStatus.WAREHOUSING.getValue());
        return New.list(bizWarehouseExitDTO);
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private OrderMasterDTO getNotNullOrderMasterByOrderNo(String orderNo) {
        OrderMasterDTO orderMasterDTO = new OrderMasterDTO();
        orderMasterDTO.setId(0);
        orderMasterDTO.setFmasterguid("");
        orderMasterDTO.setFtbuyappid(0);
        orderMasterDTO.setForderno("");
        orderMasterDTO.setForderdate(new Date());
        orderMasterDTO.setFbuyerid(0);
        orderMasterDTO.setFbuyercode("");
        orderMasterDTO.setFbuyername("");
        orderMasterDTO.setFbuyeremail("");
        orderMasterDTO.setFbuyercontactman("");
        orderMasterDTO.setFbuyertelephone("");
        orderMasterDTO.setFbuydepartmentid(0);
        orderMasterDTO.setFbuydepartment("");
        orderMasterDTO.setFsuppid(0);
        orderMasterDTO.setFsuppcode("");
        orderMasterDTO.setFsuppname("");
        orderMasterDTO.setFbiderdeliveryplace("");
        orderMasterDTO.setForderamounttotal(new BigDecimal("0"));
        orderMasterDTO.setFundStatus(0);
        orderMasterDTO.setFailedReason("");
        orderMasterDTO.setStatus(0);
        orderMasterDTO.setFconfirmdate(new Date());
        orderMasterDTO.setFconfirmmanid("");
        orderMasterDTO.setFconfirmman("");
        orderMasterDTO.setFcanceldate(new Date());
        orderMasterDTO.setFcancelmanid("");
        orderMasterDTO.setFcancelman("");
        orderMasterDTO.setFdeliverydate(new Date());
        orderMasterDTO.setFdeliverymanid("");
        orderMasterDTO.setFdeliveryman("");
        orderMasterDTO.setFlastreceivedate(new Date());
        orderMasterDTO.setFlastreceivemanid("");
        orderMasterDTO.setFlastreceiveman("");
        orderMasterDTO.setFassessdate(new Date());
        orderMasterDTO.setFassessmanid("");
        orderMasterDTO.setFassessman("");
        orderMasterDTO.setPiemail("");
        orderMasterDTO.setProjectid("");
        orderMasterDTO.setProjectnumber("");
        orderMasterDTO.setProjecttitle("");
        orderMasterDTO.setFuserid(0);
        orderMasterDTO.setFusercode("");
        orderMasterDTO.setFusername("");
        orderMasterDTO.setStatementId(0);
        orderMasterDTO.setFcancelreason("");
        orderMasterDTO.setFrefuseCancelReason("");
        orderMasterDTO.setShutDownDate(new Date());
        orderMasterDTO.setDeliveryInfo("");
        orderMasterDTO.setDeliveryNo("");
        orderMasterDTO.setReturnAmount(0.0D);
        orderMasterDTO.setFrefuseCancelDate(new Date());
        orderMasterDTO.setFdeliveryid(0);
        orderMasterDTO.setBidOrderId("");
        orderMasterDTO.setOrderType(0);
        orderMasterDTO.setReceivePicUrls("");
        orderMasterDTO.setTpiProjectId("");
        orderMasterDTO.setOriginalAmount(new BigDecimal("0"));
        orderMasterDTO.setInventoryStatus((byte)0);
        orderMasterDTO.setSpecies((byte)0);
        orderMasterDTO.setUpdateTime(new Date());
        orderMasterDTO.setInStateTime(new Date());
        orderMasterDTO.setPurchaseRootinType(0);
        orderMasterDTO.setCarryFee(new BigDecimal("0"));
        orderMasterDTO.setInvoiceTitleId(0);
        orderMasterDTO.setInvoiceTitle("");
        orderMasterDTO.setInvoiceTitleNumber("");
        orderMasterDTO.setFundType(0);
        orderMasterDTO.setFundTypeName("");
        orderMasterDTO.setCreateTime(new Date());
        return orderMasterDTO;
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private void validateUserViewAccessInOrganization(Integer userId, Integer orgId) {
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private OrderMasterSearchDTO getNotNullSearchOrder(Integer orderId) {
        OrderMasterSearchDTO orderMasterSearchDTO = new OrderMasterSearchDTO();
        orderMasterSearchDTO.setId(0);
        orderMasterSearchDTO.setForderno("");
        orderMasterSearchDTO.setFbuydepartmentid(0);
        orderMasterSearchDTO.setFbuydepartment("");
        orderMasterSearchDTO.setFsuppname("");
        orderMasterSearchDTO.setFsuppid(0);
        orderMasterSearchDTO.setForderamounttotal(0.0D);
        orderMasterSearchDTO.setOrderType(0);
        orderMasterSearchDTO.setFuserid(0);
        orderMasterSearchDTO.setSpecies(0);
        orderMasterSearchDTO.setFtbuyappid(0);
        orderMasterSearchDTO.setFbuyapplicationno("");
        orderMasterSearchDTO.setProjectNumber("");
        orderMasterSearchDTO.setExtraInfo("");
        orderMasterSearchDTO.setFbuyercontactman("");
        orderMasterSearchDTO.setFusername("");
        orderMasterSearchDTO.setForderdate("");
        orderMasterSearchDTO.setFbiderdeliveryplace("");
        orderMasterSearchDTO.setDepartmentParentId(0);
        orderMasterSearchDTO.setDepartmentParentName("");
        orderMasterSearchDTO.setFbuyername("");
        orderMasterSearchDTO.setStatus(0);
        orderMasterSearchDTO.setConfirmType(0);
        orderMasterSearchDTO.setRelateInfo("");
        orderMasterSearchDTO.setStatementId(0);
        orderMasterSearchDTO.setFbuyerid(0);
        orderMasterSearchDTO.setConfirm(0);
        orderMasterSearchDTO.setOrderDetail(Lists.newArrayList());
        orderMasterSearchDTO.setFconfirmdate(new Date());
        orderMasterSearchDTO.setFdeliverydate(new Date());
        orderMasterSearchDTO.setUpdateTime(new Date());
        orderMasterSearchDTO.setLog(Lists.newArrayList());
        orderMasterSearchDTO.setCard(Lists.newArrayList());
        orderMasterSearchDTO.setFbuyertelephone("");
        orderMasterSearchDTO.setFlastreceivedate(new Date());
        orderMasterSearchDTO.setFlastreceiveman("");
        orderMasterSearchDTO.setBidOrderId("");
        orderMasterSearchDTO.setActualAmount(0.0D);
        orderMasterSearchDTO.setFcanceldate(new Date());
        orderMasterSearchDTO.setFundStatus(0);
        orderMasterSearchDTO.setInventoryStatus(0);
        orderMasterSearchDTO.setCarryFee(0.0D);
        orderMasterSearchDTO.setFlastreceivemanid(0);
        orderMasterSearchDTO.setReturnAmount(0.0D);
        return orderMasterSearchDTO;
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private WarehouseApplicationBean getWarehouseApplicationBean(BizWarehouseEntryDTO warehouseApplicationInfo, List<OrderDetailSearchDTO> orderDetailSearchDTOList, String orgCode, Integer orgId) {
        WarehouseApplicationBean applicationBean = new WarehouseApplicationBean();
        applicationBean.setProductBeans(New.list());
        return applicationBean;
    }

    @MockMethod(targetClass = InWareHouseGWServiceImpl.class)
    private void populateInWarehouseApprover(List<OutWarehouseApplicationDetailVO> receiver, List<WarehouseApplicationDetailVO> provider) {
    }
}
