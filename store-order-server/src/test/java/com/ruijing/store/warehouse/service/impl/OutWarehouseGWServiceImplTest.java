package com.ruijing.store.warehouse.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.List;

public class OutWarehouseGWServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OutWarehouseGWServiceImpl outWarehouseGWService;

    @Test
    public void testGetImmediatelyOutWarehouseApplicationBean() {
        // mock input
        String exitInfoListJson = "[{\"id\":3847,\"exitNo\":\"CK402052740315877378\",\"orgId\":60,\"userGuid\":\"1235449308187197440\",\"orderNo\":\"DC202101146336701\",\"userName\":\"all\",\"roomId\":196,\"roomName\":\"普通试剂耗材库房(总库)\",\"status\":1,\"createTime\":1610592843000,\"deptId\":17039,\"deptName\":\"test课题组\",\"exitTime\":\"2021-01-14 10:54:03\",\"businessType\":1,\"exitDetailDTOList\":[{\"id\":4989,\"exitId\":3847,\"suppId\":5,\"suppName\":\"西格玛奥德里123(上海)贸易有限公司\",\"specifications\":\"5kg\",\"productName\":\"小白鼠\",\"brandName\":\"SUPELCO\",\"casNo\":\"\",\"productCode\":\"xbph-005\",\"dangerousType\":13,\"controlFlag\":2,\"shouldoutNum\":7.000,\"exitedNum\":7,\"exitedUnit\":\"件\",\"measurementUnit\":\"\",\"measurementNum\":0.000,\"form\":0,\"sort\":\"\",\"price\":406.00,\"unitPrice\":58.00}]}]\n";
        List<BizWarehouseExitDTO> exitInfoList = JsonUtils.parseList(exitInfoListJson, BizWarehouseExitDTO.class);
        OutWarehouseApplicationBean outWarehouseApplicationBean = outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(exitInfoList.get(0), OrgEnum.WU_YI_DA_XUE.getCode());
        Assert.assertTrue(outWarehouseApplicationBean.getOutWarehouseApplicationId()!=null);
    }
}